package com.howbuy.crm.hb.web.controller.conference;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.hb.domain.conference.CmConferenceConscust;
import com.howbuy.crm.hb.domain.conference.CmConferenceCustKinsfolk;
import com.howbuy.crm.hb.service.conference.CmConferenceConscustService;
import com.howbuy.crm.hb.service.conference.CmConferenceKinsfolkService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.page.framework.utils.Util;

import crm.howbuy.base.utils.DateUtil;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.*;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping(value = "/conferencecustkinsfolk")
public class CmConferenceCustKinsfolkController {

	private static Logger LOG = LoggerFactory.getLogger(CmConferenceCustKinsfolkController.class);
	
	@Autowired
	private CmConferenceConscustService cmConferenceConscustService;

	@Autowired
	private CmConferenceKinsfolkService cmConferenceKinsfolkService;
	
	@RequestMapping(value="/conferencecustkinsfolkList.do")
	public String conferenceList(){
		return "/conference/conferenceCustkinsfolkList";
	}
	
	@ResponseBody
	@RequestMapping("/queryConferenceCustkinsfolkList.do")
	public Map<String, Object> queryConferenceCustkinsfolkList(HttpServletRequest request) throws Exception{
		// 设置查询参数
		Map<String, String> param = null;
		// 获取分页参数
		param = new ParamUtil(request).getParamMap();
		
		String consCode = request.getParameter("consCode");
		String orgCode = request.getParameter("orgCode");
		
		if(StringUtils.isNotEmpty(consCode) && !"null".equals(consCode) && !"ALL".equals(consCode)) {
			param.put("consCode", consCode);
		}else {
			param.put("consCode", null);
			param.put("teamCode", Util.getSubQueryByOrgCode(orgCode));
		}

		List<CmConferenceCustKinsfolk> listtemp = cmConferenceKinsfolkService.listCmConferenceKinsfolk(param);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		for(CmConferenceCustKinsfolk v:listtemp){

			ConsOrgCache orgcache = ConsOrgCache.getInstance();
			String or = orgcache.getCons2OutletMap().get(v.getConscode());
			String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(v.getConscode()));
			if("0".equals(uporgcode)){
				v.setUporgname(orgcache.getOrgMap().get(v.getOrgcode()));
			}else{
				v.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
			}

			v.setOrgcode(orgcache.getOrgMap().get(v.getOrgcode()));

			v.setAppointmentstypename(ConstantCache.getInstance().getConstantKeyVal("appointmentsType").get(v.getAppointmentstype()));
			v.setIdtypename(ConstantCache.getInstance().getConstantKeyVal("idtype").get(v.getIdtype()));
			if(StringUtils.isNotEmpty(v.getCutoffdt())) {
				
				long now = Long.parseLong(DateUtil.getDateFormat(new Date(),"yyyyMMddHHmmss"));
				long cutoffdt = Long.parseLong(v.getCutoffdt());
				if (cutoffdt < now) {
					v.setCutoffdt("0");
				}
			}
		}
		
		resultMap.put("rows", listtemp);
		return resultMap;
	}

	@ResponseBody
	@RequestMapping("/queryConferenceCustkinsfolkCount.do")
	public String queryConferenceCustkinsfolkCount(HttpServletRequest request) throws Exception{
		// 设置查询参数
		String result = "0";
		Map<String, String> param = null;
		// 获取分页参数
		param = new ParamUtil(request).getParamMap();

		int listnum = cmConferenceKinsfolkService.countCmConferenceKinsfolk(param);

		if(listnum > 0){
			result = String.valueOf(listnum);
		}

		return result;
	}

	@ResponseBody
	@RequestMapping("/saveConferenceCustKinsfolk.do")
	public String saveConferenceCustKinsfolk(HttpServletRequest request,
										 HttpServletResponse response) throws Exception {
		String result = "success";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		User user = (User)request.getSession().getAttribute("loginUser");
		String update = request.getParameter("update");

		JSONArray jsonUpdateArray = JSONArray.parseArray(update);

		if(jsonUpdateArray != null){
			for(Object obj : jsonUpdateArray){
				JSONObject entity = (JSONObject) obj;
				CmConferenceCustKinsfolk bean = JSON.parseObject(entity.toJSONString(), CmConferenceCustKinsfolk.class);
				bean.setModifier(user.getUserId());
				bean.setModifydt(sdf.parse(sdf.format(System.currentTimeMillis())));
				cmConferenceKinsfolkService.updateCmConferenceKinsfolk(bean);
			}
		}

		return result;
	}

	@ResponseBody
	@RequestMapping("/delConferenceCustKinsfolk.do")
	public String delConferenceCustKinsfolk(HttpServletRequest request,
											 HttpServletResponse response) throws Exception {
		String result = "success";

		User user = (User)request.getSession().getAttribute("loginUser");
		String kinsfolkid = request.getParameter("kinsfolkid");
		String conferenceid = request.getParameter("conferenceid");
		String conscustno = request.getParameter("conscustno");

		if(StringUtils.isNotEmpty(kinsfolkid)){
			cmConferenceKinsfolkService.deleteCmConferenceKinsfolk(kinsfolkid);
		}

		if(StringUtils.isNotEmpty(conferenceid) && StringUtils.isNotEmpty(conscustno)){

			Map<String,String> param = new HashMap<String, String>();
			param.put("conferenceid",conferenceid);
			param.put("conscustno",conscustno);

			CmConferenceConscust cmConferenceConscust = cmConferenceConscustService.getConferenceConscustinfo(param);

			Integer num = cmConferenceConscust.getAppointmentsnub() - 1;

			cmConferenceConscust.setAppointmentsnub(num);
			cmConferenceConscustService.updateCmConferenceConscust(cmConferenceConscust);

		}

		return result;
	}


	@RequestMapping("/exportConferenceCustKinsfolk.do")
	public void exportConferenceCustKinsfolk(HttpServletRequest request, @RequestParam Map<String, String> params,
											HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, String> param = new HashMap<String, String>();
		SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");
		SimpleDateFormat sdfTime = new SimpleDateFormat("HH:mm:ss");
		
		// 获取分页参数
		String orgCode = request.getParameter("orgCode");
		String conferenceName = request.getParameter("conferenceName");
		//String appointmentsType = request.getParameter("appointmentsType");
		String custName = request.getParameter("custName");

		conferenceName = URLDecoder.decode(conferenceName.trim(), "UTF-8");
		custName = URLDecoder.decode(custName.trim(), "UTF-8");

		param.put("conferenceName", conferenceName);
		if(StringUtils.isNotEmpty(custName)) {
			param.put("custName", custName);
		}
		/*if(StringUtils.isNotEmpty(appointmentsType)) {
			param.put("appointmentsType", appointmentsType);
		}*/
		String consCode = request.getParameter("consCode");

		if(StringUtils.isNotEmpty(consCode) && !"null".equals(consCode) && !"ALL".equals(consCode)) {
			param.put("consCode", consCode);
		}else {
			param.put("consCode", null);
			param.put("teamCode", Util.getSubQueryByOrgCode(orgCode));
		}

		List<CmConferenceCustKinsfolk> cmConferenceCustKinsfolklist = cmConferenceKinsfolkService.listCmConferenceKinsfolk(param);
		if(cmConferenceCustKinsfolklist != null && cmConferenceCustKinsfolklist.size() > 0){
			for(CmConferenceCustKinsfolk v:cmConferenceCustKinsfolklist){
				Date gotoDate = v.getGotoflightdt();
				Date returnDate = v.getReturnflightdt();

				ConsOrgCache orgcache = ConsOrgCache.getInstance();
				String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(v.getConscode()));
				if("0".equals(uporgcode)){
					v.setUporgname(orgcache.getOrgMap().get(v.getOrgcode()));
				}else{
					v.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
				}

				v.setOrgcode(orgcache.getOrgMap().get(v.getOrgcode()));
				v.setAppointmentstypename(ConstantCache.getInstance().getConstantKeyVal("appointmentsType").get(v.getAppointmentstype()));
				v.setIdtypename(ConstantCache.getInstance().getConstantKeyVal("idtype").get(v.getIdtype()));
				if(gotoDate != null){
					v.setGotoflightdtstr(DateUtil.getDateFormat(gotoDate,""));
					v.setGotoflightDate(sdfDate.format(gotoDate));
					v.setGotoflightTime(sdfTime.format(gotoDate));
				}
				if(returnDate != null){
					v.setReturnflightdtstr(DateUtil.getDateFormat(returnDate,""));
					v.setReturnflightDate(sdfDate.format(returnDate));
					v.setReturnflightTime(sdfTime.format(returnDate));
				}
			}
			resultMap.put("rows", cmConferenceCustKinsfolklist);
		}else{
			LOG.error("查询对应会议信息失败！");
		}

		try {
			// 清空输出流
			response.reset();
			// 设置文件格式和名字
			response.setContentType("multipart/form-data");
			response.setHeader("Content-Disposition",
					"attachment;fileName=" + new String("至臻年会参会成员记录.xls".getBytes("gb2312"), "ISO8859-1"));
			ServletOutputStream os = response.getOutputStream();

			String[] columnName = {
					"会议ID","参会区域","参会部门","所属投顾","会议名称","客户号","客户姓名","出行客户姓名","参会人证件类型","出行方式",
					"参会人证件号码","去程班次","去程班次抵达日期","去程班次抵达时间","返程班次","返程班次出发日期","返程班次出发时间"
			};

			String[] beanProperty = {
					"conferenceid","uporgname","orgcode","consname","conferencename","conscustno","custname",
					"kinsfolkname","idtypename","tripway","idno","gotoflight","gotoflightDate","gotoflightTime","returnflight","returnflightDate","returnflightTime"
			};
			ExcelWriter.writeExcel(os, "至臻年会参会成员记录", 0, (List<?>)resultMap.get("rows"), columnName, beanProperty);
			os.close(); // 关闭流
		} catch (Exception e) {
			LOG.error("文件导出异常", e);
		}

	}
	
	/**
	 * 查询路演管理会议信息
	 * 
	 * author: wu.long
	 * date: 2019年9月2日 下午3:49:33 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/selectRoadShowConferenceState")
	public String selectRoadShowConferenceState(HttpServletRequest request){
		String result = "";
		try {
			String conferenceId = request.getParameter("conferenceId");
			result = cmConferenceKinsfolkService.selectRoadShowConferenceState(conferenceId);
		} catch (Exception e) {
			LOG.error("查询路演管理会议信息异常：", e);
		}
		return result;
	} 
	
	
	
	
	
	
	
	
	
	
	
	
	
}
