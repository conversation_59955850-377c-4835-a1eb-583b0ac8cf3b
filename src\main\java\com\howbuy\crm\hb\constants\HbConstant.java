/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.constants;

import java.util.Arrays;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/9/25 11:16
 * @since JDK 1.8
 */
public class HbConstant {


    /**
     * 地址加密后展示的文案
     * 备注：如果当前登录用户的数据深度，无法查看指定客户的地址信息，则对地址进行加密
     */
    public static final String ADDR_MASK = "**********";


    /**
     * 财富管理中心、高端业务中心、产品中心、HKPWM
     */
    public static final List<String> CENTER_ORG_LIST = Arrays.asList("财富管理中心", "高端业务中心", "产品中心", "HKPWM");

    /**
     * 下划线
     */
    public static final String UN_LINE_CHAR = "-";

}