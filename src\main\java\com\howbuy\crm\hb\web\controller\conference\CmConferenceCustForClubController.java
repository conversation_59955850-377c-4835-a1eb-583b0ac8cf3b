package com.howbuy.crm.hb.web.controller.conference;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.domain.conference.*;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.system.HbOrganization;
import com.howbuy.crm.hb.service.conference.CmConferenceConscustService;
import com.howbuy.crm.hb.service.conference.CmConferenceCustInfomationService;
import com.howbuy.crm.hb.service.conference.CmConferenceInfomationService;
import com.howbuy.crm.hb.service.conference.CmConferenceService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.system.HbOrganizationService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.controller.cache.CacheCode;
import com.howbuy.crm.hb.web.controller.cache.CacheUtil;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ExcelUtils;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.page.framework.utils.Util;
import com.howbuy.crm.page.framework.utils.ValidateUtil;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;
import crm.howbuy.base.validate.VarChar2Validator;
import jxl.Sheet;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.util.*;

@Controller
@RequestMapping(value = "/conferencecustforclub")
public class CmConferenceCustForClubController extends BaseController {

    private static Logger LOG = LoggerFactory.getLogger(CmConferenceCustForClubController.class);

    private final String DOWNLOAD_FILE_NAME = "至臻年会名单导入模板.xls";

    private final String MODEL_FILE_NAME = "ConferenceConscustForClub.xls";

    @Autowired
    private CmConferenceService cmConferenceService;

    @Autowired
    private CmConferenceConscustService cmConferenceConscustService;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    @Autowired
    private ConscustService custService;

    @Autowired
    private HbOrganizationService hbOrganizationService;

    @Autowired
    private com.howbuy.crm.hb.service.conference.CmConferenceKinsfolkService cmConferenceKinsfolkService;

    @Autowired
    private CmConferenceInfomationService cmConferenceInfomationService;

    @Autowired
    private CmConferenceCustInfomationService cmConferenceCustInfomationService;

    @RequestMapping(value = "/conferencecustListForClub.do")
    public String conferenceList() {
        return "/conference/conferencecustListForClub";
    }

    @ResponseBody
    @RequestMapping("/queryConferenceCustListForClub.do")
    public Map<String, Object> queryConferenceCustListForClub(HttpServletRequest request) throws Exception {
        // 设置查询参数
        Map<String, String> param = null;
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();

        String consCode = request.getParameter("consCode");
        String orgCode = request.getParameter("orgCode");

        if (StringUtils.isNotEmpty(consCode) && !"null".equals(consCode) && !"ALL".equals(consCode)) {
            param.put("consCode", consCode);
        } else {
            param.put("consCode", null);
            param.put("teamCode", Util.getSubQueryByOrgCode(orgCode));
        }

        List<CmConferenceConscust> listtemp = cmConferenceConscustService.listCmConferenceConscustForClub(param);
        Map<String, Object> resultMap = new HashMap<String, Object>();

        for (CmConferenceConscust v : listtemp) {

            ConsOrgCache orgcache = ConsOrgCache.getInstance();
            String uporgcode = orgcache.getUpOrgMapCache().get(v.getOrgcode());
            if("0".equals(uporgcode)){
                v.setUporgname(orgcache.getOrgMap().get(v.getOrgcode()));
            }else{
                v.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
            }

            v.setOrgcode(orgcache.getOrgMap().get(v.getOrgcode()));

            v.setAppointmentstypename(ConstantCache.getInstance().getConstantKeyVal("appointmentsType").get(v.getAppointmentstype()));
            if (StringUtils.isNotEmpty(v.getCutoffdt())) {

                long now = Long.parseLong(DateUtil.getDateFormat(new Date(), "yyyyMMddHHmmss"));
                long cutoffdt = Long.parseLong(v.getCutoffdt());
                if (cutoffdt < now) {
                    v.setCutoffdt("0");
                }
            }
        }

        resultMap.put("rows", listtemp);
        return resultMap;
    }

    @ResponseBody
    @RequestMapping("/saveConferenceConscustForclub.do")
    public String saveConferenceConscustForclub(HttpServletRequest request,
                                                HttpServletResponse response) throws Exception {
        String result = "success";

        User user = (User) request.getSession().getAttribute("loginUser");
        String update = request.getParameter("update");

        JSONArray jsonUpdateArray = JSONArray.parseArray(update);

        if (jsonUpdateArray != null) {
            for (Object obj : jsonUpdateArray) {
                JSONObject entity = (JSONObject) obj;
                CmConferenceConscust bean = JSON.parseObject(entity.toJSONString(), CmConferenceConscust.class);

                bean.setModifier(user.getUserId());
                cmConferenceConscustService.updateCmConferenceConscust(bean);

                String appointmentstype = bean.getAppointmentstype();

                Map<String, String> param = new HashMap<String, String>();
                param.put("conferenceid", bean.getConferenceid());
                param.put("conscustno", bean.getConscustno());
                if ("2".equals(appointmentstype)) {
                    cmConferenceKinsfolkService.deleteAllCmConferenceKinsfolk(param);
                } else {
                    Integer numb = cmConferenceKinsfolkService.countCmConferenceKinsfolk(param);
                    Integer newnumb = bean.getAppointmentsnub() - numb;
                    if (newnumb > 0) {

                        for (int i = 0; i < newnumb; i++) {
                            CmConferenceCustKinsfolk cmConferenceCustKinsfolk = new CmConferenceCustKinsfolk();
                            cmConferenceCustKinsfolk.setConferenceid(bean.getConferenceid());
                            cmConferenceCustKinsfolk.setConscustno(bean.getConscustno());
                            cmConferenceKinsfolkService.insertCmConferenceKinsfolk(cmConferenceCustKinsfolk);
                        }
                    } else if (newnumb < 0) {

                        Integer b = Math.abs(newnumb);

                        List<CmConferenceCustKinsfolk> cmConferenceCustKinsfolklist = cmConferenceKinsfolkService.cmConferenceNoKinsfolkList(param);
                        //小于当前值，则删减数据，删减的数据来自于没有填写名称的参会信息
                        for (int i = 0; i < b; i++) {
                            cmConferenceKinsfolkService.deleteCmConferenceKinsfolk(cmConferenceCustKinsfolklist.get(i).getKinsfolkid());
                        }
                    }
                }
            }
        }

        return result;
    }

    @ResponseBody
    @RequestMapping("/delConferenceConscustForclub.do")
    public String delConferenceConscustForclub(HttpServletRequest request,
                                               HttpServletResponse response) throws Exception {
        String result = "success";

        String conferenceid = request.getParameter("conferenceid");
        String conscustno = request.getParameter("conscustno");

        try {
            Map<String, String> param = new HashMap<String, String>();
            param.put("conferenceid", conferenceid);
            param.put("conscustno", conscustno);

            //删除参会投顾数据
            cmConferenceConscustService.deleteConferenceConscust(param);
			/*//删除参会人员数据
			CmConferenceKinsfolkService.deleteAllCmConferenceKinsfolk(param);
			//删除新增信息栏数据
			cmConferenceCustInfomationService.deleteCustInfomation(param);*/
        } catch (Exception e) {
            LOG.error("删除至臻年会参会人员数据异常", e);
            result = "false";
        }

        return result;
    }

    @RequestMapping("/exportConferenceCustForClub.do")
    public void exportConferenceCustForClub(HttpServletRequest request, @RequestParam Map<String, String> params,
                                            HttpServletResponse response) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();

        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数
        String orgCode = request.getParameter("orgCode");
        String conferenceid = request.getParameter("conferenceid");
        String appointmentsType = request.getParameter("appointmentsType");
        String custName = request.getParameter("custName");

        custName = URLDecoder.decode(custName.trim(), "UTF-8");

        param.put("conferenceid", conferenceid);
        if (StringUtils.isNotEmpty(custName)) {
            param.put("custName", custName);
        }
        if (StringUtils.isNotEmpty(appointmentsType)) {
            param.put("appointmentsType", appointmentsType);
        }
        String consCode = request.getParameter("consCode");

        if (StringUtils.isNotEmpty(consCode) && !"null".equals(consCode) && !"ALL".equals(consCode)) {
            param.put("consCode", consCode);
        } else {
            param.put("consCode", null);
            param.put("teamCode", Util.getSubQueryByOrgCode(orgCode));
        }

        List<String> conferenceids = new ArrayList<String>();
        List<CmConferenceConscust> cmConferenceConscustlist = cmConferenceConscustService.listCmConferenceConscustForClub(param);
        if (cmConferenceConscustlist != null && cmConferenceConscustlist.size() > 0) {
            for (CmConferenceConscust v : cmConferenceConscustlist) {

                ConsOrgCache orgcache = ConsOrgCache.getInstance();
                String uporgcode = orgcache.getUpOrgMapCache().get(v.getOrgcode());

                if(StringUtils.isBlank(uporgcode)){
                    v.setUporgname("");
                }else if("0".equals(uporgcode)){
                    v.setUporgname(orgcache.getOrgMap().get(v.getOrgcode()));
                }else{
                    v.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
                }

                v.setOrgcode(orgcache.getOrgMap().get(v.getOrgcode()));

                v.setAppointmentstypename(ConstantCache.getInstance().getConstantKeyVal("appointmentsType").get(v.getAppointmentstype()));
                conferenceids.add(v.getConferenceid());
            }
            resultMap.put("rows", cmConferenceConscustlist);
        } else {
            LOG.error("查询对应会议信息失败！");
        }

        List<CmConferenceInfomation> infomationlist = cmConferenceInfomationService.listCmConferenceInfomation(conferenceid);

        List<CmConferenceCustInfomation> custInfomationlist = cmConferenceCustInfomationService.listCmConferenceCustInfomationByid(conferenceid);

        List<Map<String, String>> custInfomationmap = new ArrayList<Map<String, String>>();

        for (CmConferenceCustInfomation custInfomation : custInfomationlist) {
            String key = custInfomation.getConferenceid() + custInfomation.getConscustno() + custInfomation.getField();
            Map<String, String> custmap = new HashMap<String, String>();
            custmap.put(key, custInfomation.getFieldvalue());

            custInfomationmap.add(custmap);
        }
        WritableWorkbook wwb;

        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("至臻年会参会成员记录.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            //填充头
            List<String> columnNames = new ArrayList<String>();
            String[] columnName = {
                    "会议ID", "参会区域","参会部门", "所属投顾", "会议名称", "客户号", "客户姓名", "参会状态", "参会人数"
            };

            for (int i = 0; i < columnName.length; i++) {
                columnNames.add(columnName[i]);
            }

            if (infomationlist != null && infomationlist.size() > 0) {
                for (CmConferenceInfomation infomation : infomationlist) {
                    columnNames.add(infomation.getField());
                }
            }

            // 建立excel文件
            WritableWorkbook book = Workbook.createWorkbook(os);
            if(book !=null){
	            // 创建excelSheet页
	            WritableSheet sheet = book.createSheet("至臻年会参会成员记录", 0);
	
	            Label label;
	            // 增加excel头
	            for (int i = 0; i < columnNames.size(); i++) {
	                label = new Label(i, 0, columnNames.get(i));
	                sheet.addCell(label);
	                sheet.setColumnView(i, 9);
	            }
	
	            //判断表中是否有数据
	            if (cmConferenceConscustlist != null && cmConferenceConscustlist.size() > 0) {
	                //循环写入表中数据
	                for (int i = 0; i < cmConferenceConscustlist.size(); i++) {
	
	                    //转换成map集合{activyName:测试功能,count:2}
	                    CmConferenceConscust conscust = cmConferenceConscustlist.get(i);
	
	                    //ps：因为要“”通用”“导出功能，所以这里循环的时候不是get("Name"),而是通过map.get(o)
	                    sheet.addCell(new Label(0, i + 1, String.valueOf(conscust.getConferenceid())));
	                    sheet.addCell(new Label(1, i + 1, String.valueOf(conscust.getUporgname())));
	                    sheet.addCell(new Label(2, i + 1, String.valueOf(conscust.getOrgcode())));
	                    sheet.addCell(new Label(3, i + 1, String.valueOf(conscust.getConsname())));
	                    sheet.addCell(new Label(4, i + 1, String.valueOf(conscust.getConferencename())));
	                    sheet.addCell(new Label(5, i + 1, String.valueOf(conscust.getConscustno())));
	                    sheet.addCell(new Label(6, i + 1, String.valueOf(conscust.getCustname())));
	                    sheet.addCell(new Label(7, i + 1, String.valueOf(conscust.getAppointmentstypename())));
	                    sheet.addCell(new Label(8, i + 1, String.valueOf(conscust.getAppointmentsnub())));
	
	                    for (int k = 0; k < infomationlist.size(); k++) {
	
	                        boolean flag = true;
	                        for (int j = 0; j < custInfomationmap.size(); j++) {
	                            String flagkey = conscust.getConferenceid() + conscust.getConscustno() + infomationlist.get(k).getField();
	
	                            if (custInfomationmap.get(j).containsKey(flagkey)) {
	                                sheet.addCell(new Label(columnName.length + k, i + 1, String.valueOf(custInfomationmap.get(j).get(flagkey))));
	                                flag = false;
	                            }
	                        }
	
	                        if (flag) {
	                            sheet.addCell(new Label(columnName.length + k, i + 1, String.valueOf("")));
	                        }
	                    }
	                }
	            }
	            // 将主体内容写入文件
	            book.write();
	            if (book != null) {
	                book.close();
	            }
	            os.close(); // 关闭流
            }
        } catch (Exception e) {
            LOG.error("文件导出异常", e);
        }

    }

    @RequestMapping("/modelConferenceConscustForClubReport.do")
    public String downloadModelDoc(HttpServletRequest request,
                                   HttpServletResponse response) {
        return dowmloadTemplate(MODEL_FILE_NAME,DOWNLOAD_FILE_NAME,request,response);
    }

    @RequestMapping(value = "/impConferenceCustForClubReport.do", method = RequestMethod.POST)
    public @ResponseBody
    Map<String, Object> inputPCust(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();

        User user = (User) request.getSession().getAttribute("loginUser");

        InputStream input = null;
        Workbook workBook;

        String errorMsg = "";
        String uploadFlag = "success";

        try {
            // 转型为MultipartHttpRequest：
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            // 获得文件：
            MultipartFile file = multipartRequest.getFile("file");
            // 获得输入流：
            input = file.getInputStream();

            workBook = Workbook.getWorkbook(input);

            // 去掉之前导入的一二级旧来源，统一成现在的新来源编码
            String[] colPropertity = {"conferenceid", "conscustno"};

            Sheet sheet = workBook.getSheet(0);

            List<CmConferenceConscust> addconfconscustList = new ArrayList<CmConferenceConscust>();
            // 将之前获取Excel的13列数据改为12列
            List<CmConferenceConscust> postList = ExcelUtils.getListByReadShell(sheet, 1, 0, 2, colPropertity, CmConferenceConscust.class);

            //--------------update by tjy end(version 3.5.3)--------------------------------
            if (null == postList || postList.isEmpty()) {
                errorMsg = "没有上传记录";
                uploadFlag = "error";
            } else {
                int line = 2;
                for (CmConferenceConscust importParam : postList) {
                    String validateMsg = this.checkAdd(importParam);

                    CmConference cmConference = cmConferenceService.queryCmConferenceInfo(importParam.getConferenceid());

                    if (cmConference == null) {
                        validateMsg = "会议ID不存在";
                    }

                    if (!StringUtil.isEmpty(validateMsg)) {
                        errorMsg = "第 " + line + " 行错误是：" + validateMsg;
                        uploadFlag = "error";
                        break;
                    } else {

                        Map<String, String> conferenceparam = new HashMap<String, String>();
                        conferenceparam.put("conferenceid", importParam.getConferenceid());
                        conferenceparam.put("conscustno", importParam.getConscustno());
                        CmConferenceConscust confconscust = cmConferenceConscustService.getConferenceConscustinfo(conferenceparam);

                        if (confconscust == null) {

                            errorMsg = this.saveConferenceConscust(importParam.getConferenceid(), importParam.getConscustno(), user.getUserId());
                            if (!"success".equals(errorMsg)) {
                                errorMsg = "第 " + line + " 行错误是：" + errorMsg;
                                uploadFlag = "error";
                                break;
                            }
                        } else {
                            confconscust.setActualnub(importParam.getActualnub());
                            /*confconscust.setActualnubdt(new Date());*/
                            confconscust.setModifier(user.getUserId());
                            addconfconscustList.add(confconscust);
                        }
                    }

                    line++;
                }

                if("success".equals(uploadFlag)){  //符合条件

                    for(CmConferenceConscust importConscust:addconfconscustList){
                        cmConferenceConscustService.updateCmConferenceConscust(importConscust);
                    }
                }
            }

            resultMap.put("uploadFlag", uploadFlag);
            resultMap.put("errorMsg", errorMsg);

        } catch (Exception e) {
            LOG.error("导入客户名单 出现异常！", e);
            resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "请检查模板是否正确");
        } finally {
            try {
            	if(input != null){
            		input.close();
            	}
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return resultMap;
    }

    /**
     * @param addRequest
     * @return String
     * @throws
     * @Title: checkAdd
     * @Description: TODO(方法说明描述)
     */
    protected String checkAdd(CmConferenceConscust addRequest) {
        Map comonMap = ValidateUtil.commonMap;
        String validateCode = null;
        try {
            if (StringUtil.isEmpty(addRequest.getConferenceid())) {
                validateCode = "会议ID为空";
                return validateCode;
            }
            validateCode = VarChar2Validator.validate(addRequest.getConferenceid(), 1,
                    180, true, "参与会议", comonMap);
            if (!StringUtil.isEmpty(validateCode)) {
                return validateCode;
            }
            if (StringUtil.isEmpty(addRequest.getConscustno())) {
                validateCode = "投顾客户号为空";
                return validateCode;
            }
            validateCode = VarChar2Validator.validate(addRequest.getConscustno(), 1,
                    180, true, "投顾客户号", comonMap);
            if (!StringUtil.isEmpty(validateCode)) {
                return validateCode;
            }
        } catch (Exception e) {
            validateCode = "信息检查不通过";
        }
        return validateCode;
    }

    public String saveConferenceConscust(String conferenceid, String conscustno, String userid) throws Exception {
        String result = "success";
        // 查询换成调用core中接口
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        if (StringUtil.isNotNullStr(conscustno)) {
            queryRequest.setConscustno(conscustno);
        } else {
            queryRequest.setConscustno("xxxx");
        }

        Conscust cust =null;
        if(StringUtil.isNotNullStr(conscustno)){
            cust = custService.getConscust(conscustno);
        }
        if (cust != null) {
            Map<String, String> param = new HashMap<String, String>();
            param.put("conscustno", conscustno);

            Map<String, String> paramMap = new HashMap<String, String>();
            paramMap.put("conferenceid", conferenceid);
            paramMap.put("conscustno", conscustno);
            paramMap.put("conferencetype", "6");

            CmConferenceConscust cmConfConscust = cmConferenceConscustService.getConferenceConscustinfo(paramMap);
            if (cmConfConscust == null) {

                String outletcode = ConsOrgCache.getInstance().getCons2OutletMap().get(cust.getConscode());

                if (StringUtil.isNotNullStr(outletcode)) {
                    param.put("conferenceId", conferenceid);
                    param.put("orgcode", outletcode);

                    if (CacheUtil.lock(CacheCode.CMCONFERENCE, conferenceid)) {
                        Map<String, Object> allMap = cmConferenceService.getCmConferenceOrgNum(param);
                        Map<String, Object> joinMap = cmConferenceService.getCmConferenceJoinOrgNum(param);
                        Map<String, Object> infoMap = cmConferenceService.getCmConferenceJoinAllNum(param);

                        int allNum = Integer.parseInt(allMap.get("MAXNUMBER") == null ? "0" : allMap.get("MAXNUMBER").toString());
                        int orgjoinNum = Integer.parseInt(joinMap == null ? "0" : joinMap.get("ORGNUMB").toString());
                        int alljoinNum = Integer.parseInt(infoMap == null ? "0" : infoMap.get("ALLNUMB").toString());
                        LOG.info("导入客户名单, conferenceId: {}, 会议人数上限: {}, orgjoinNum：{}, alljoinNum: {}",
                                conferenceid, allNum, orgjoinNum, alljoinNum);

                        //获取截止时间
                        long cutoffdt = Long.parseLong(allMap.get("CUTOFFDT") == null ? "0" : allMap.get("CUTOFFDT").toString());
                        if (cutoffdt != 0) {

                            long now = Long.parseLong(DateUtil.getDateFormat(new Date(), "yyyyMMddHHmmss"));

                            if (cutoffdt < now) {
                                result = "custoff";
                                return result;
                            }
                        }

                        List<HbOrganization> listout = hbOrganizationService.listHbOrganizationOutByCode(param);

                        if (listout != null && listout.size() > 0) {
                            for (HbOrganization organization : listout) {

                                param.put("orgcode", organization.getOrgcode());
                                Map<String, Object> orgMap = cmConferenceService.getCmConferenceOrgNum(param);
                                LOG.info("查询团队人数，orgCode:{}, conferenceId: {}, 查询结果: {}",
                                        organization.getOrgcode(),
                                        conferenceid,
                                        JSON.toJSONString(orgMap));

                                int orgNum = Integer
                                        .parseInt(orgMap.get("ORGCODENUB") == null ? "0" : orgMap.get("ORGCODENUB").toString());

                                if (orgNum != 0) {
                                    if (orgjoinNum > orgNum) {
                                        result = "moreorg";
                                        return result;
                                    }
                                }

                            }
                        }
                        if (alljoinNum > allNum) {
                            result = "moreall";
                            return result;
                        }
                        CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
                        cmConferenceConscust.setConferenceid(conferenceid);
                        cmConferenceConscust.setConscode(cust.getConscode());
                        cmConferenceConscust.setConscustno(conscustno);
                        cmConferenceConscust.setOrgcode(outletcode);
                        cmConferenceConscust.setCreatdt(DateTime.now().toString("yyyyMMdd"));
                        cmConferenceConscust.setCreater(userid);
                        cmConferenceConscust.setAppointmentsnub(0);
                        cmConferenceConscust.setAppointmentstype("2");
                        cmConferenceConscustService.insertCmConferenceConscust(cmConferenceConscust);
                        CacheUtil.unlock(CacheCode.CMCONFERENCE, conferenceid);
                    } else {
                        result = "wait";
                    }

                } else {
                    result = conscustno + "客户不存在对应投顾";
                }
            }
        } else {
            result = conscustno + "客户不是有效客户!";
        }

        return result;
    }

}
