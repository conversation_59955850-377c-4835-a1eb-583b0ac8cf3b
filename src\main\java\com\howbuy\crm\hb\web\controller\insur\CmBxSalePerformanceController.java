package com.howbuy.crm.hb.web.controller.insur;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.request.consultant.QueryManageCoeffRequest;
import com.howbuy.crm.account.client.response.consultant.QueryManageCoeffVO;
import com.howbuy.crm.hb.domain.insur.*;
import com.howbuy.crm.hb.enums.BxCommissionWayEnum;
import com.howbuy.crm.hb.persistence.insur.CmBxCurrencyRmbRateMapper;
import com.howbuy.crm.hb.persistence.insur.CmBxPrebookBuyinfoMapper;
import com.howbuy.crm.hb.persistence.insur.CmBxProductCoeffRatioMapper;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.insur.CmBxPrebookBuyinfoService;
import com.howbuy.crm.hb.service.insur.CmBxPrebookSigninfoService;
import com.howbuy.crm.hb.service.insur.CmBxPrebookinfoService;
import com.howbuy.crm.hb.service.insur.CmBxProductCoeffService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.insur.CmBxPrebookBuyWithPayInfoDto;
import com.howbuy.crm.hbconstant.service.HbConstantService;
import com.howbuy.crm.organization.dto.OrgLayerInfo;
import com.howbuy.crm.organization.service.HbOrganizationService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.util.BigDecimalUtil;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 销售业绩
 * @reason:
 * @Date: 2019/12/17 9:46
 */
@Controller
@RequestMapping("/insur")
@Slf4j
public class CmBxSalePerformanceController  extends BaseController {

    @Autowired
    private CmBxPrebookinfoService cmBxPrebookinfoService;
    
    @Autowired
    private CmBxPrebookBuyinfoService cmBxPrebookBuyinfoService;
    @Autowired
    private CmBxPrebookSigninfoService cmBxPrebookSigninfoService;

    @Autowired
	private PageVisitLogService pageVisitLogService;

    @Autowired
    private HbOrganizationService coreOrganizationService;

    @Autowired
    private HbConstantService hbConstantService;

    @Autowired
    private CmBxCurrencyRmbRateMapper cmBxCurrencyRmbRateMapper;
    @Autowired
    private CmBxPrebookBuyinfoMapper cmBxPrebookBuyinfoMapper;

    @Autowired
    private CmBxProductCoeffRatioMapper cmBxProductCoeffRatioMapper;

    @Autowired
    private CmBxProductCoeffService cmBxProductCoeffService;

    @Autowired
    private CommonService commonService;
    
    private String dateexp = "^[0-9]{8}$";
    
    private String loooooo = "1000000";
    private static final String MANAGE_POINT_MAX = "9999";
    /**
     * 分页最大值
     */
    private static final Integer PAGE_SIZE_MAX = 9999;
    /**
     * 返回标志
     */
    private static final String SAVE_FLAG = "saveFlag";
    /**
     * 错误标志
     */
    private static final String ERROR = "error";
    /**
     * 错误信息
     */
    private static final String ERROR_MSG = "errorMsg";
    /**
     * 管理系数-分总常量
     */
    private static final String MANAGE_POINT_CONSTANT = "管理系数-分总";
    /**
     * 管理系数-区副常量
     */
    private static final String MANAGE_COEFF_REGIONALSUBTOTAL_CONSTANT = "管理系数-区副";
    /**
     * 管理系数-区总常量
     */
    private static final String MANAGE_COEFF_REGIONALTOTAL_CONSTANT = "管理系数-区总";

    @RequestMapping("/listSalePerformance.html")
    public String salePerformanceList(){
        return "insur/listSalePerformance";
    }

    @RequestMapping("/salePerformanceListByPageJson.do")
    @ResponseBody
    public Object salePerformanceListByPageJson(HttpServletRequest request) throws Exception{
        // 设置查询参数
        Map<String, String> param = new ParamUtil(request).getParamMap();
        PageData<CmBxPrebookinfo> pageData = cmBxPrebookinfoService.listCmBxPrebookinfoByPage(param);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<CmBxPrebookinfo> listdata = pageData.getListData();
        ConstantCache constantCache = ConstantCache.getInstance();
        LinkedHashMap<String, String> bxCommissionWayMap = constantCache.getConstantKeyVal("bxCommissionWay");
        Map<String, String> allUserMap = ConsOrgCache.getInstance().getAllUserMap();
        // 获取管理系数Map
        Map<String, QueryManageCoeffVO.ManageCoeffInfo> manageCoeffMap = commonService.getManageCoeffMap(listdata, vo ->{
            QueryManageCoeffRequest.PreIdInfo preIdInfo = new QueryManageCoeffRequest.PreIdInfo();
            preIdInfo.setPreId(String.valueOf(vo.getId()));
            preIdInfo.setConsCustNo(vo.getConscustno());
            preIdInfo.setConsCode(vo.getConscode());
            //签单日期
            preIdInfo.setCreDt(vo.getSigndt());
            return preIdInfo;
        });
        for (CmBxPrebookinfo info : listdata) {
        	//如果当前投顾和预约投顾不是同一人，佣金打八折（*0.8）
            if(StringUtil.isNotNullStr(info.getNowconscode()) && !info.getNowconscode().equals(info.getConscode()) && info.getCommission() != null){
            	info.setCommission(info.getCommission().multiply(new BigDecimal("0.8")));
            }
            info.setExpCreator(allUserMap.get(info.getExpCreator()));
        	info.setCredt(info.getCredt().substring(0, 8));
            info.setPrestateval(ConstantCache.getInstance().getVal("insurprestate", info.getPrestate()));
            info.setInsurstateval(ConstantCache.getInstance().getVal("insurstate", info.getInsurstate()));
            info.setCheckStateVal(ConstantCache.getInstance().getVal("insurcheckstate", info.getCheckstate()));
            ConsOrgCache orgcache = ConsOrgCache.getInstance();
            String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
            if("0".equals(uporgcode)){
                info.setUporgname(orgcache.getAllOrgMap().get(info.getOrgcode()));
            }else{
                info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
            }
            OrgLayerInfo orgLayerInfo = coreOrganizationService.getOrgLayerInfoByOrgCode(info.getOrgcode());
            if(orgLayerInfo!= null){
                info.setPreCenterName(orgLayerInfo.getCenterOrgName());
            }
            info.setOrgcode(ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
            info.setConsName(allUserMap.get(info.getConscode()));
            info.setRelation(constantCache.getVal("insurrelation", info.getRelation()));
            info.setBusitype(constantCache.getVal("insurbusitype", info.getBusitype()));
            info.setProdtype(constantCache.getVal("insurprodtype", info.getProdtype()));
            info.setPaystateval(constantCache.getVal("insurpaystate", info.getPaystate()));
            info.setPayyears(constantCache.getVal("insurpayyears", info.getPayyears()));
            info.setEnsureyears(constantCache.getVal("insurensureyears", info.getEnsureyears()));
            info.setIschangereturn(constantCache.getVal("yesOrNo", info.getIschangereturn()));
            String bxcommissionway = info.getBxcommissionway();
            if(StringUtil.isNullStr(bxcommissionway)){
                bxcommissionway = BxCommissionWayEnum.BXCOMMISSIONWAY_ONE.getCode();
            }
            info.setBxcommissionway(bxCommissionWayMap.get(bxcommissionway));
            info.setCollamk(info.getCollamk() == null ? null : info.getCollamk().setScale(2, BigDecimal.ROUND_DOWN));
            if(info.getPayyears() != null && info.getPayyears().contains("至") && info.getInsurage() != null){
                try {
                    info.setPayyears2(Integer.toString(Integer.parseInt(info.getPayyears().replace("至","").replace("岁","")) - info.getInsurage()));
                }catch (NumberFormatException e){
                    info.setPayyears2(info.getPayyears());
                }
            }else {
                info.setPayyears2(info.getPayyears());
            }
            //方案一时方案二为-
            if(BxCommissionWayEnum.BXCOMMISSIONWAY_ONE.getCode().equals(bxcommissionway)){
                info.setCommissioncxdfortwoStr("-");
                info.setCommissionCxdStr("-");
            }else if(BxCommissionWayEnum.BXCOMMISSIONWAY_TWO.getCode().equals(bxcommissionway)){

                //方案2-存续D，受总保费>= 40w影响
                info.setCommissioncxdfortwo(cmBxPrebookinfoService.calComissionCxd(info, true));
                info.setCommissioncxdfortwoStr(info.getCommissioncxdfortwo() == null ? "-" : info.getCommissioncxdfortwo().toPlainString());
                //正常存续D，不受总保费>= 40w影响
                info.setCommissionCxd(cmBxPrebookinfoService.calComissionCxd(info, false));
                info.setCommissionCxdStr(info.getCommissionCxd() == null ? "-" : info.getCommissionCxd().toPlainString());
            }
            //计算管理系数
            cmBxPrebookinfoService.setManageCoeffByCmBxBookInfo(info, manageCoeffMap);
            //取佣金
            setCommission(info, bxcommissionway);
            if (bxcommissionway != null) {
                info.setTotalCommission(calculateTotalCommission(info.getCommission(), info.getCommissionfortwo()));
                info.setPointCommission(calculatePointCommission(info.getManageTotalCommission(), info.getManagePoint()));
                info.setManageCoeffRegsubtotalComm(calculatePointCommissionByBigDecimal(info.getManageTotalCommission(), info.getManageCoeffRegionalsubtotal()));
                info.setManageCoeffRegtotalComm(calculatePointCommissionByBigDecimal(info.getManageTotalCommission(), info.getManageCoeffRegionaltotal()));
            }
            log.info("Insurid:{} comcommissionratio:{}", info.getInsurid(), info.getCommissionratio());
        }
        resultMap.put("rows", listdata);
        return resultMap;
    }

    /**
     * @description:(返回值或默认值1)
     * @param value
     * @return java.math.BigDecimal
     * @author: shijie.wang
     * @date: 2025/8/21 19:49
     * @since JDK 1.8
     */
    private BigDecimal getValueOrDefaultOneByBigDecimal(BigDecimal value){
        return value == null ? BigDecimal.ONE : value;
    }

    /**
     * @description:(计算管理奖金基数)
     * @param totalCommission
     * @param managePoint
     * @return java.math.BigDecimal
     * @author: shijie.wang
     * @date: 2025/8/22 13:58
     * @since JDK 1.8
     */
    private BigDecimal calculatePointCommissionByBigDecimal(String totalCommission, BigDecimal managePoint) {
        if (StringUtils.isEmpty(totalCommission) || Objects.isNull(managePoint)) {
            return null;
        }
        return new BigDecimal(totalCommission).multiply(getValueOrDefaultOneByBigDecimal(managePoint));
    }

    /**
     * @description 验证管理系数是否超出上限
     * @param value 要验证的管理系数值
     * @param fieldName 字段名称，用于错误信息
     * @return 如果超出上限返回错误信息，否则返回null
     * @author: Generated
     * @date: 2025/1/25
     * @since JDK 1.8
     */
    private String validateManageCoeffLimit(String value, String fieldName) {
        if (StringUtil.isNotNullStr(value)) {
            if (new BigDecimal(value).compareTo(new BigDecimal(MANAGE_POINT_MAX)) > 0) {
                return fieldName + "超出上限（9999）！";
            }
        }
        return null;
    }

    /**
     * @description 批量验证管理系数字段
     * @param managePoint 管理系数-分总
     * @param manageCoeffRegionalsubtotal 管理系数-区副
     * @param manageCoeffRegionaltotal 管理系数-区总
     * @param resultMap 结果Map，用于设置错误信息
     * @return 如果验证失败返回true，验证通过返回false
     * @author: shijie.wang
     * @date: 2025/08/22 16:22
     * @since JDK 1.8
     */
    private boolean validateManageCoeffFields(String managePoint, String manageCoeffRegionalsubtotal, 
                                            String manageCoeffRegionaltotal, Map<String, Object> resultMap) {
        // 定义字段映射：字段值 -> 字段名称
        Map<String, String> fieldMap = new LinkedHashMap<>();
        fieldMap.put(managePoint, MANAGE_POINT_CONSTANT);
        fieldMap.put(manageCoeffRegionalsubtotal, MANAGE_COEFF_REGIONALSUBTOTAL_CONSTANT);
        fieldMap.put(manageCoeffRegionaltotal, MANAGE_COEFF_REGIONALTOTAL_CONSTANT);
        
        // 循环验证每个字段
        for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
            String fieldValue = entry.getKey();
            String fieldName = entry.getValue();
            if (StringUtil.isNotNullStr(fieldValue) && new BigDecimal(fieldValue).compareTo(new BigDecimal(MANAGE_POINT_MAX)) > 0) {
                resultMap.put(SAVE_FLAG, ERROR);
                resultMap.put(ERROR_MSG, fieldName + "超出上限（" + MANAGE_POINT_MAX + "）！");
                return true;
            }
        }
        return false;
    }

    /**
     * @description 设置佣金相关
     * @param cmBxPrebookinfo
     * @return void
     * @author: jianjian.yang
     * @date: 2024/5/8 15:36
     * @since JDK 1.8
     */
    private void setCommission(CmBxPrebookinfo cmBxPrebookinfo, String bxCommissionWay){

        if(cmBxPrebookinfo.getBuyid() == null){
            log.error("preId:{}对应的保单buyId不存在", cmBxPrebookinfo.getId());
            return;
        }
        Map<String, Object> param1 = new HashMap<String, Object>(1);
        param1.put("preid", cmBxPrebookinfo.getId());
        CmBxPrebookSigninfo signinfo = cmBxPrebookSigninfoService.getCmBxPrebookSigninfo(param1);
        CmBxPrebookBuyinfo cmBxPrebookBuyinfoResult = calCommission(cmBxPrebookinfo, cmBxPrebookinfo.getCommissionrate(), signinfo, bxCommissionWay);
        cmBxPrebookinfo.setCommission(cmBxPrebookBuyinfoResult.getCommission());
        cmBxPrebookinfo.setCommissionfortwo(cmBxPrebookBuyinfoResult.getCommissionForTwo());
        //佣金
        cmBxPrebookinfo.setCommissionStr(cmBxPrebookBuyinfoResult.getCommission() == null ? "-" : cmBxPrebookBuyinfoResult.getCommission().toPlainString());
        //佣金率
        cmBxPrebookinfo.setCommissionratio(cmBxPrebookBuyinfoResult.getCommissionratio());
        cmBxPrebookinfo.setManageCommissionRatio(cmBxPrebookBuyinfoResult.getManageCommissionRatio());
        cmBxPrebookinfo.setCommissionfortwoStr(cmBxPrebookBuyinfoResult.getCommissionForTwo() == null ? "-" : cmBxPrebookBuyinfoResult.getCommissionForTwo().toPlainString());
        // 管理总佣金
        BigDecimal manageTotalCommission = BigDecimalUtil.add(cmBxPrebookBuyinfoResult.getManageCommission(), cmBxPrebookBuyinfoResult.getManageCommissionForTwo());
        log.info("管理总佣金:{}", manageTotalCommission);
        cmBxPrebookinfo.setManageTotalCommission(manageTotalCommission.toPlainString());
    }

    /**
     * @description 计算佣金
     * @param cmBxPrebookinfo
     * @param rate
     * @param signinfo
     * @param bxCommissionWay
     * @return com.howbuy.crm.hb.domain.insur.CmBxPrebookBuyinfo
     * @author: jianjian.yang
     * @date: 2024/5/8 15:37
     * @since JDK 1.8
     */
    public CmBxPrebookBuyinfo calCommission(CmBxPrebookinfo cmBxPrebookinfo,
                                            BigDecimal rate, CmBxPrebookSigninfo signinfo, String bxCommissionWay) {
        Map<String,Object> param = new HashMap<>();
        param.put("id", cmBxPrebookinfo.getBuyid());
        CmBxPrebookBuyinfo cmBxPrebookBuyinfo = cmBxPrebookBuyinfoMapper.getCmBxPrebookBuyinfo(param);
        //查产品系数
        List<CmBxProductCoeff> availList = getAvailCoeffList(cmBxPrebookBuyinfo, cmBxPrebookinfo, signinfo);
        // 获取投顾佣金率
        BigDecimal commissionratio = cmBxPrebookBuyinfo.getExpcommissionratio();
        // 获取管理佣金率
        BigDecimal manageCommissionRatio = cmBxPrebookBuyinfo.getExpManageCommissionRatio();
        BigDecimal defaultCommissionRatio = getUsedCommissionratio(cmBxPrebookBuyinfo, availList);
        if (commissionratio == null) {
            commissionratio = defaultCommissionRatio;
        }
        if (manageCommissionRatio == null) {
            manageCommissionRatio = defaultCommissionRatio;
        }

        log.info("处理佣金dealCommission参数commissionratio:{},manageCommissionRatio:{},cmBxPrebookBuyinfo:{},cmBxPrebookinfo:{}",
                commissionratio, manageCommissionRatio, JSON.toJSONString(cmBxPrebookBuyinfo), JSON.toJSONString(cmBxPrebookinfo));
        final BigDecimal benchMark = new BigDecimal(400000);

        /*判断该保单的总保费是否≥40万，满足则为“是”，不满足则为“否”；
        总保费 = 年缴保费 * 年限，年限=min（缴费年限，10）
        至**岁的都转成**    因为这里min之后都是10   所以** 统一写55
        */
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("preid", cmBxPrebookinfo.getId());
        List<CmBxPrebookBuyinfo> list = cmBxPrebookBuyinfoMapper.listCmBxPrebookBuyinfo(paramMap);
        log.info("根据预约id:{}获取所有主险附加险购买信息:{}", JSON.toJSONString(cmBxPrebookinfo.getId()), JSON.toJSONString(list));
        BigDecimal allbf = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(list)) {
            log.info("根据预约id:{}获取所有主险附加险购买条数:{}", JSON.toJSONString(cmBxPrebookinfo.getId()), JSON.toJSONString(list.size()));
            for (CmBxPrebookBuyinfo a : list) {
                BigDecimal currencyRate = rate != null ? rate : BigDecimal.ONE;
                BigDecimal multiply = a.getYearamk().multiply(new BigDecimal(Math.min(
                        StringUtils.isNumeric(a.getPayyears()) ? Integer.parseInt(a.getPayyears()) : 55
                        , 10))).multiply(currencyRate);
                log.info("buyid:{},Yearamk:{},Payyears:{},currencyRate:{},单条计算佣金的保费:{}", JSON.toJSONString(a.getId()),
                        JSON.toJSONString(a.getYearamk()), JSON.toJSONString(a.getPayyears()), currencyRate
                        , JSON.toJSONString(multiply));
                allbf = allbf.add(multiply);
            }
        }
        log.info("根据预约id:{}获取所有主险附加险计算佣金的保费:{}", JSON.toJSONString(cmBxPrebookinfo.getId()), JSON.toJSONString(allbf));

        String isMoreThenForty = allbf.compareTo(benchMark) >= 0 ? StaticVar.CHINESE_YES : StaticVar.CHINESE_NO;


        CmBxPrebookBuyinfo cmBxPrebookBuyinfoResult = new CmBxPrebookBuyinfo();
        cmBxPrebookBuyinfoResult.setCommissionratio(commissionratio);
        cmBxPrebookBuyinfoResult.setManageCommissionRatio(manageCommissionRatio);
        if (rate != null && cmBxPrebookBuyinfo.getYearamk() != null && commissionratio!= null) {
            log.info("conscustno:{}", cmBxPrebookinfo.getConscustno());
            BigDecimal commissionamk = cmBxPrebookBuyinfo.getYearamk().multiply(rate);
            //保单状态是退保（冷静期内）、延保/拒保，则折标销量为0
            if (StaticVar.INSUR_STATE_TBL.equals(cmBxPrebookinfo.getInsurstate()) || StaticVar.INSUR_STATE_YB.equals(cmBxPrebookinfo.getInsurstate())) {
                cmBxPrebookBuyinfoResult.setCommission(new BigDecimal(0));
            } else {
                //方案一     原值*100%
                if (BxCommissionWayEnum.BXCOMMISSIONWAY_ONE.getCode().equals(bxCommissionWay)) {
                    cmBxPrebookBuyinfoResult.setCommission(commissionamk);
                } else if (BxCommissionWayEnum.BXCOMMISSIONWAY_TWO.getCode().equals(bxCommissionWay)) {
                    /**
                     * 总保费 ≥ 40万：
                     * 首年佣金：按销售业绩（首年）的佣金取值规则 * 50%；
                     * 总保费 ＜ 40万：
                     * 首年佣金：按销售业绩（首年）的佣金取值规则 * 100%；
                     *
                     * 总保费 ≥ 40万：
                     * 存续D = 公司首年佣金收入 * 30倍
                     * （公司首年佣金收入 = 年缴保费 * 首年公司佣金率 ；仅算首年，不算续佣）
                     *  crm 1.4.5 迭代修改  公司首年佣金收入 == 年缴保费 * 核保汇率 * 首年公司佣金率
                     * 总保费 ＜ 40万：
                     * 存续D =0；
                     */
                    cmBxPrebookBuyinfoResult.setCommissionForTwo(StaticVar.CHINESE_YES.equals(isMoreThenForty) ? commissionamk.multiply(new BigDecimal(0.5))
                            : commissionamk);
                    //公司首年佣金收入
                    BigDecimal hbRate = cmBxPrebookBuyinfo.getRate();
                    log.info("核保汇率,{},年缴保费,{}", hbRate, cmBxPrebookBuyinfo.getYearamk());
                }
            }
        }else {
            if (BxCommissionWayEnum.BXCOMMISSIONWAY_ONE.getCode().equals(bxCommissionWay)) {
                cmBxPrebookBuyinfoResult.setCommission(BigDecimal.ZERO);
            } else if (BxCommissionWayEnum.BXCOMMISSIONWAY_TWO.getCode().equals(bxCommissionWay)) {
                cmBxPrebookBuyinfoResult.setCommissionForTwo(BigDecimal.ZERO);
            }
        }
        cmBxPrebookBuyinfoResult.setManageCommission(BigDecimalUtil.multiply(cmBxPrebookBuyinfoResult.getCommission(), manageCommissionRatio));
        cmBxPrebookBuyinfoResult.setManageCommissionForTwo(BigDecimalUtil.multiply(cmBxPrebookBuyinfoResult.getCommissionForTwo(), manageCommissionRatio));
        cmBxPrebookBuyinfoResult.setCommission(BigDecimalUtil.multiply(cmBxPrebookBuyinfoResult.getCommission(), commissionratio));
        cmBxPrebookBuyinfoResult.setCommissionForTwo(BigDecimalUtil.multiply(cmBxPrebookBuyinfoResult.getCommissionForTwo(), commissionratio));

        return cmBxPrebookBuyinfoResult;
    }

    /**
     * @description 查产品系数
     * @param model
     * @param cmBxPrebookinfo
     * @param cmBxPrebookSigninfo
     * @return java.util.List<com.howbuy.crm.hb.domain.insur.CmBxProductCoeff>
     * @author: jianjian.yang
     * @date: 2024/5/8 15:42
     * @since JDK 1.8
     */
    private List<CmBxProductCoeff> getAvailCoeffList(CmBxPrebookBuyinfo model, CmBxPrebookinfo cmBxPrebookinfo, CmBxPrebookSigninfo cmBxPrebookSigninfo) {
        List<CmBxProductCoeff> availList = new ArrayList<CmBxProductCoeff>();
        //不存在签单信息，空指针修复。 业务含义 暂不理解
        if(cmBxPrebookSigninfo==null || model==null || cmBxPrebookinfo==null){
            return availList;
        }
        String targetpayyears = model.getPayyears();
        String targetensureyears = model.getEnsureyears();
        BigDecimal targetyearamk = model.getYearamk();


        //2025年6月26日 16:38:46    因 targerChanncode 为空，导致空指针。  以下校验：3.验证合作渠道 的逻辑调整：
//        参数CmBxProductCoeff  【渠道】  = A
//        预约CmBxPrebookBuyinfo 【渠道】 =B
//          是否 ：availList.add 的判断 条件 为： A == null ||  (A==NULL?'':A).EQUALS(B==NULL?'':B)
         String targerChannCode =  StringUtil.null2String(cmBxPrebookinfo.getChanncode()) ;


        String signdt = cmBxPrebookSigninfo.getSigndt();
        String passdt = cmBxPrebookSigninfo.getPassdt();

        Map<String, String> para = new HashMap<String, String>(3);
        para.put("isdel", StaticVar.INSUR_ISDEL_NO);
        para.put("fundcode", cmBxPrebookinfo.getFundcode());
        para.put("detailfundcode", model.getFundcode());
        List<CmBxProductCoeff> listCoeff = cmBxProductCoeffService.listCmBxProductCoeff(para);

        for (CmBxProductCoeff coeff : listCoeff) {
            if (
                    validateDate(signdt, coeff.getSignbegdt(), coeff.getSignenddt()) //1.验证签单日期
                            && validateDate(passdt, coeff.getPassbegdt(), coeff.getPassenddt())//2.验证核保通过日期
                            && (coeff.getChanncode() == null || targerChannCode.equals(StringUtil.null2String(coeff.getChanncode()))) //3.验证合作渠道
                            && (coeff.getPayyears() == null || targetpayyears.compareTo(coeff.getPayyears()) == 0)//4.验证缴费年限
                            && (coeff.getEnsureyears() == null || targetensureyears.compareTo(coeff.getEnsureyears()) == 0)//5.验证保障期限
                            && validateDecimal(targetyearamk, coeff.getYearamk(), coeff.getYearamkend()) //6.验证年缴金额
            ) {
                availList.add(coeff);
            }
        }
        return availList;
    }

    /**
     * 根据 配置的 下限金额-configMinNum 、上限金额-configMaxNum  [闭合区间]  校验 validateNum 是否合法 。
     * 逻辑：
     * 只有配置了 [configMinNum|configMaxNum] 才做校验
     * 如果 参数validateNum  不在金额上下限之内，则 返回 false
     *
     * @param validateNum
     * @param configMinNum
     * @param configMaxNum
     * @return
     */
    public static boolean validateDecimal(BigDecimal validateNum, BigDecimal configMinNum, BigDecimal configMaxNum) {
        Assert.notNull(validateNum);
        if (StringUtil.isNotNullStr(configMinNum) && validateNum.compareTo(configMinNum) < 0) {
            return false;
        }
        if (StringUtil.isNotNullStr(configMaxNum) && validateNum.compareTo(configMaxNum) > 0) {
            return false;
        }
        return true; //默认校验通过
    }
    /**
     * 根据 配置的 起始时间-configBeginDt 、结束时间-configEndDt  校验 validateDt 是否合法 。
     * 逻辑：
     * 只有配置了 [configBeginDt|configEndDt] 才做校验
     * 如果 参数validateDt  不在起始日期之内，则 返回 false
     *
     * @param validateDt
     * @param configBeginDt
     * @param configEndDt
     * @return
     */
    public static boolean validateDate(String validateDt, String configBeginDt, String configEndDt) {

        if (StringUtil.isNotNullStr(configBeginDt)) {
            if(validateDt == null || validateDt.compareTo(configBeginDt) < 0) {
                return false;
            }
        }
        if (StringUtil.isNotNullStr(configEndDt)) {
            if(validateDt == null || validateDt.compareTo(configEndDt) > 0) {
                return false;
            }
        }
        return true; //默认校验通过
    }

    /**
     * @description 获取投顾佣金率
     * @param buyinfo
     * @param availList
     * @return java.math.BigDecimal
     * @author: jianjian.yang
     * @date: 2024/5/8 15:43
     * @since JDK 1.8
     */
    private BigDecimal getUsedCommissionratio(CmBxPrebookBuyinfo buyinfo, List<CmBxProductCoeff> availList) {
        // 如果有人工修改的佣金率，则无需计算
        if(buyinfo.getExpcommissionratio()!=null && buyinfo.getExpManageCommissionRatio() != null){
            return null;
        }
        Assert.notNull(availList);
        BigDecimal commissionratio = null;
        if (availList.size() == 1) {
            //获取产品系数id，根据产品系数id查询出首条投顾佣金率 佣金率类型1：公司；2：投顾
            commissionratio = getFirstCommissionRatio(availList.get(0), StaticVar.TWO);
        }
        return commissionratio;
    }

    /**
     * @description 首条投顾佣金率
     * @param coeff
     * @param ratioType
     * @return java.math.BigDecimal
     * @author: jianjian.yang
     * @date: 2024/5/8 15:43
     * @since JDK 1.8
     */
    private BigDecimal getFirstCommissionRatio(CmBxProductCoeff coeff, String ratioType) {
        BigDecimal ratio = null;
        BigDecimal id = coeff.getId();
        Map<String, Object> param = new HashMap<String, Object>(4);
        param.put("coeffid", id);
        param.put("isdel", StaticVar.INSUR_ISDEL_NO);
        param.put("ratiotype", ratioType);
        param.put("year", BigDecimal.ONE);
        List<CmBxProductCoeffRatio> list = cmBxProductCoeffRatioMapper.listCmBxProductCoeffRatio(param);
        if (list != null && list.size() > 0) {
            CmBxProductCoeffRatio obj = list.get(0);
            ratio = obj.getRatio();
        }
        return ratio;
    }


    /**
     * type:commission-投顾佣金核算 ；sales-核算销量
     * @param request
     * @return
     * @throws Exception
     */
    /*@RequestMapping("/batchCalculateAmk.do")
    @ResponseBody
    public String batchCalculateAmk(HttpServletRequest request) throws Exception{
    	String result="";
    	User user = (User) request.getSession().getAttribute("loginUser");
    	// 设置查询参数
        Map<String, String> param = new ParamUtil(request).getParamMap();
        List<CmBxPrebookinfo> list = cmBxPrebookinfoService.listCmBxPrebookinfoByExp(param);
        List<String> listbuy = new ArrayList<String>();
        for(CmBxPrebookinfo pre : list){
        	if(pre.getBuyid() != null){
        		listbuy.add(pre.getBuyid().toPlainString());
        	}
        }
        if(listbuy != null && listbuy.size() > 0){
        	Map<String,Object> querybuy = new HashMap<String,Object>(2);
        	querybuy.put("ids", Util.getOracleSQLIn(listbuy,999,"t.id"));
            querybuy.put("channcode",param.get("channcode"));
        	List<CmBxPrebookBuyinfo> listdata = cmBxPrebookBuyinfoService.listCmBxPrebookBuyinfo(querybuy);
        	cmBxPrebookBuyinfoService.batchCalculateAmk(listdata, user.getUserId(), param.get("type"));
        }
		result = "success";
        return result;
    }*/

    /**
     * 佣金计算
     * @param request
     * @return
     * @throws Exception
     */
   /* @RequestMapping("/batchCalculateAmkCommision.do")
    @ResponseBody
    public int batchCalculateAmkCommision(CmBxPrebookBuyWithPayVo vo,HttpServletRequest request){
        User user = getLoginUser(request) ;
        // 设置分页条数为9999，表示不分页，默认只有50条
        vo.setRows(PAGE_SIZE_MAX);
        int updateCount=cmBxPrebookBuyinfoService.batchCalculateAmkCommision(vo,user.getUserId());
        return updateCount;
    }*/

    /**
     * 导出操作
     * @param request
     * @param response
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/exportSalePerformance.do")
    public void exportSalePerformance(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
        Map<String, String> param = new ParamUtil(request).getParamMap();
        List<CmBxPrebookinfo> exportList = cmBxPrebookinfoService.listCmBxPrebookinfoByExp(param);
        List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
		User user = (User) request.getSession().getAttribute("loginUser");
		String ip = getIpAddr(request);
        ConstantCache instance = ConstantCache.getInstance();
        Map<String, String> allOrgMap = ConsOrgCache.getInstance().getAllOrgMap();
        LinkedHashMap<String, String> bxCommissionWayMap = instance.getConstantKeyVal("bxCommissionWay");
        Map<String, String> allUserMap = ConsOrgCache.getInstance().getAllUserMap();
        // 获取管理系数Map
        Map<String, QueryManageCoeffVO.ManageCoeffInfo> manageCoeffMap = commonService.getManageCoeffMap(exportList, vo ->{
            QueryManageCoeffRequest.PreIdInfo preIdInfo = new QueryManageCoeffRequest.PreIdInfo();
            preIdInfo.setPreId(String.valueOf(vo.getId()));
            preIdInfo.setConsCustNo(vo.getConscustno());
            preIdInfo.setConsCode(vo.getConscode());
            //签单日期
            preIdInfo.setCreDt(vo.getSigndt());
            return preIdInfo;
        });
        for (CmBxPrebookinfo info : exportList) {
        	//如果当前投顾和预约投顾不是同一人，佣金打八折（*0.8）
//            if(StringUtil.isNotNullStr(info.getNowconscode()) && !info.getNowconscode().equals(info.getConscode()) && info.getCommission() != null){
//            	info.setCommission(info.getCommission().multiply(new BigDecimal("0.8")));
//            }
            info.setExpCreator(allUserMap.get(info.getExpCreator()));
        	info.setCredt(info.getCredt().substring(0, 8));
            info.setPrestateval(instance.getVal("insurprestate", info.getPrestate()));
            info.setInsurstateval(instance.getVal("insurstate", info.getInsurstate()));
            info.setCheckStateVal(instance.getVal("insurcheckstate", info.getCheckstate()));
            ConsOrgCache orgcache = ConsOrgCache.getInstance();
            String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
            if("0".equals(uporgcode)){
                info.setUporgname(allOrgMap.get(info.getOrgcode()));
            }else{
                info.setUporgname(allOrgMap.get(uporgcode));
            }
            info.setConsName(ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
            info.setRelation(instance.getVal("insurrelation", info.getRelation()));
            info.setBusitype(instance.getVal("insurbusitype", info.getBusitype()));
            info.setProdtype(instance.getVal("insurprodtype", info.getProdtype()));
            info.setPaystate(instance.getVal("insurpaystate", info.getPaystate()));
            info.setPayyears(instance.getVal("insurpayyears", info.getPayyears()));
            info.setEnsureyears(instance.getVal("insurensureyears", info.getEnsureyears()));
            info.setCollamk(info.getCollamk() == null ? null : info.getCollamk().setScale(2, BigDecimal.ROUND_DOWN));
            info.setIschangereturn(instance.getVal("yesOrNo", info.getIschangereturn()));
            if(info.getPayyears() != null && info.getPayyears().contains("至") && info.getInsurage() != null){
                try {
                    info.setPayyears2(Integer.toString(Integer.parseInt(info.getPayyears().replace("至","").replace("岁","")) - info.getInsurage()));
                }catch (NumberFormatException e){
                    info.setPayyears2(info.getPayyears());
                }
            }else {
                info.setPayyears2(info.getPayyears());
            }
            String bxcommissionway = info.getBxcommissionway();
            if(StringUtil.isNullStr(bxcommissionway)){
                bxcommissionway = BxCommissionWayEnum.BXCOMMISSIONWAY_ONE.getCode();
            }
            info.setBxcommissionway(bxCommissionWayMap.get(bxcommissionway));
            //方案一时方案二为-
            if(BxCommissionWayEnum.BXCOMMISSIONWAY_ONE.getCode().equals(bxcommissionway)){
                info.setCommissioncxdfortwoStr("-");
                info.setCommissionCxdStr("-");
            }else if(BxCommissionWayEnum.BXCOMMISSIONWAY_TWO.getCode().equals(bxcommissionway)){
                //方案2-存续D，受总保费>= 40w影响
                info.setCommissioncxdfortwo(cmBxPrebookinfoService.calComissionCxd(info, true));
                //正常存续D，不受总保费>= 40w影响
                info.setCommissionCxd(cmBxPrebookinfoService.calComissionCxd(info, false));
                info.setCommissioncxdfortwoStr(null==info.getCommissioncxdfortwo() ?"-":info.getCommissioncxdfortwo().toPlainString());
                info.setCommissionCxdStr(null == info.getCommissionCxd() ? "-" : info.getCommissionCxd().toPlainString());
            }


            if (null != info.getRate() && null != info.getYearamk() && StringUtils.isNotEmpty(info.getPayyears())) {
                //总保费保持和页面展示的一样  四舍五入保留两位
                info.setTotalPremium(info.getRate().multiply(info.getYearamk())
                        .multiply(new BigDecimal(transferPayYears(info.getPayyears()))).setScale(2, RoundingMode.HALF_UP)
                        .toPlainString());
            }
            OrgLayerInfo orgLayerInfo = coreOrganizationService.getOrgLayerInfoByOrgCode(info.getOrgcode());
            if(orgLayerInfo!= null){
                info.setPreCenterName(orgLayerInfo.getCenterOrgName());
            }
            info.setOrgcode(allOrgMap.get(info.getOrgcode()));
            //计算管理系数
            cmBxPrebookinfoService.setManageCoeffByCmBxBookInfo(info, manageCoeffMap);
            //取佣金
            setCommission(info, bxcommissionway);
            if (bxcommissionway != null) {
                info.setTotalCommission(calculateTotalCommission(info.getCommission(), info.getCommissionfortwo()));
                info.setPointCommission(calculatePointCommission(info.getManageTotalCommission(), info.getManagePoint()));
                info.setManageCoeffRegsubtotalComm(calculatePointCommissionByBigDecimal(info.getManageTotalCommission(), info.getManageCoeffRegionalsubtotal()));
                info.setManageCoeffRegtotalComm(calculatePointCommissionByBigDecimal(info.getManageTotalCommission(), info.getManageCoeffRegionaltotal()));
            }
            //导出日志
            PageVisitLog pageVisitLog = new PageVisitLog();
            pageVisitLog.setConscustno(info.getConscustno());
            pageVisitLog.setUserId(user.getUserId());
            pageVisitLog.setVisitUrl(request.getRequestURI());
            pageVisitLog.setOperation("销售业绩导出");
            pageVisitLog.setVisitTime(new Date());
            pageVisitLog.setPreid(info.getId());
            pageVisitLog.setIp(ip);
            listlog.add(pageVisitLog);
        }
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("销量业绩导出.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String[] columnName = new String[]{"预约id", "订单明细ID", "录入时间", "签单日期", "预约状态", "复核状态", "保单状态", "中心(预约时)", "区域(预约时)", "部门(预约时)",
                    "投顾(预约时)", "员工编号(预约时)", "投顾客户号", "投保人", "受保人", "业务类型", "保险公司", "产品类型", "产品名称", "缴费年限", "缴费年数",
                    "年缴保费/TP", "保障年限", "首年缴费状态", "首年保费缴清日", "保单号", "核保通过日期", "冷静期截止日",
                    "核保汇率", "正常存续D", "是否核算", "投顾佣金率", "管理佣金率", "绩效汇率", "创新方案",
                    "方案1-佣金", "方案2-佣金", "总保费（RMB）", "方案2-存续D",
                    "是否核保通过变退保", "备注", "佣金合计", MANAGE_POINT_CONSTANT, MANAGE_COEFF_REGIONALSUBTOTAL_CONSTANT, MANAGE_COEFF_REGIONALTOTAL_CONSTANT, "管理奖金基数-分总",
                    "管理奖金基数-区副", "管理奖金基数-区总","最近修改人"};

            String [] beanProperty = new String [] {"id","buyid","credt","signdt","prestateval","checkStateVal","insurstateval","preCenterName","uporgname","orgcode",
                    "consName","conscode","conscustno","custname","insurname","busitype","compname","prodtype","fundname","payyears","payyears2",
                    "yearamk","ensureyears","paystate","paydt","insurid","passdt","caltime",
                    "rate", "commissionCxdStr","isMoreThenForty","commissionratio","manageCommissionRatio","commissionrate","bxcommissionway",
                    "commissionStr","commissionfortwoStr","totalPremium","commissioncxdfortwoStr",
                    "ischangereturn","consremark", "totalCommission", "managePoint", "manageCoeffRegionalsubtotal", "manageCoeffRegionaltotal",
                    "pointCommission","manageCoeffRegsubtotalComm","manageCoeffRegtotalComm","expCreator"};
            ExcelWriter.writeExcel(os, "销量业绩（首年）", 0, exportList, columnName, beanProperty);
            os.close(); // 关闭流
            for(PageVisitLog log : listlog){
            	pageVisitLogService.recordLog(log);
            }
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }

    }
    
    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    @ResponseBody
    @RequestMapping("/changeCalData.do")
    public ModelAndView changeCalData(HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
		String buyids = request.getParameter("buyids");
        Map<String,Object> map = new HashMap<String,Object>(1);
        map.put("buyids", buyids);
        return new ModelAndView("insur/changeCalData", "map", map);
    }
    
    @ResponseBody
	@RequestMapping("/saveChangeCalData")
	public Map<String, Object> saveChangeCalData(HttpServletRequest request) throws Exception {

		Map<String, Object> resultMap = new HashMap<String, Object>(2);

		try {
			User user = (User) request.getSession().getAttribute("loginUser");
			String buyids = request.getParameter("buyids");
			String bxCommissionWay = request.getParameter("bxCommissionWay");
			String commissionratio = request.getParameter("commissionratio");
            String manageCommissionRatio = request.getParameter("manageCommissionRatio");
			String managePoint = request.getParameter("managePoint");
            String manageCoeffRegionalsubtotal = request.getParameter("manageCoeffRegionalsubtotal");
            String manageCoeffRegionaltotal = request.getParameter("manageCoeffRegionaltotal");

			if(StringUtil.isNotNullStr(commissionratio)){
				if(new BigDecimal(commissionratio).compareTo(new BigDecimal(loooooo)) > 0){
					resultMap.put(SAVE_FLAG,ERROR);
					resultMap.put(ERROR_MSG, "投顾佣金率太大！");
					return resultMap;
				}
			}
			// 验证所有管理系数字段
			if (validateManageCoeffFields(managePoint, manageCoeffRegionalsubtotal, manageCoeffRegionaltotal, resultMap)) {
				return resultMap;
			}
            CmBxPrebookinfo cmBxPrebookinfo = new CmBxPrebookinfo();
            cmBxPrebookinfo.setExpCreator(user.getUserId());
            cmBxPrebookinfo.setBxcommissionway(bxCommissionWay);
            cmBxPrebookinfo.setCommissionratio(StringUtil.isNotNullStr(commissionratio) ? new BigDecimal(commissionratio) : null);
            cmBxPrebookinfo.setManageCommissionRatio(StringUtil.isNotNullStr(manageCommissionRatio) ? new BigDecimal(manageCommissionRatio) : null);
            cmBxPrebookinfo.setManagePoint(StringUtil.isNotNullStr(managePoint) ? managePoint : null);
            cmBxPrebookinfo.setManageCoeffRegionalsubtotal(StringUtil.isNotNullStr(manageCoeffRegionalsubtotal) ?
                    new BigDecimal(manageCoeffRegionalsubtotal) : null);
            cmBxPrebookinfo.setManageCoeffRegionaltotal(StringUtil.isNotNullStr(manageCoeffRegionaltotal) ?
                    new BigDecimal(manageCoeffRegionaltotal) : null);
			boolean issuccess = cmBxPrebookBuyinfoService.batchChangeCalData(buyids, cmBxPrebookinfo);
			if(issuccess){
				resultMap.put(SAVE_FLAG, "success");
			}
		}catch(Exception e){
			log.error("saveChangeCalData信息异常：", e);
			resultMap.put(SAVE_FLAG,ERROR);
			resultMap.put(ERROR_MSG, "保存异常，请联系系统人员处理");
		}

		return resultMap;
	}


    @ResponseBody
    @RequestMapping("/saveChangeCalDataRyc")
    public Map<String, Object> saveChangeCalDataRyc(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        try {
            User user =getLoginUser(request);
            String endPayIds = request.getParameter("endPayIds");
            String ratio = request.getParameter("ratio");
            String managePoint = request.getParameter("managePoint");
            String manageCoeffRegionalsubtotal = request.getParameter("manageCoeffRegionalsubtotal");
            String manageCoeffRegionaltotal = request.getParameter("manageCoeffRegionaltotal");

            // 验证所有管理系数字段
            if (validateManageCoeffFields(managePoint, manageCoeffRegionalsubtotal, manageCoeffRegionaltotal, resultMap)) {
                return resultMap;
            }
            // 设置为null处理
            managePoint = setManageCoeffNull(managePoint);
            manageCoeffRegionalsubtotal = setManageCoeffNull(manageCoeffRegionalsubtotal);
            manageCoeffRegionaltotal = setManageCoeffNull(manageCoeffRegionaltotal);

            BigDecimal updtateRatio;
            if(StringUtil.isNotNullStr(ratio)){
                updtateRatio=new BigDecimal(ratio);
                if(updtateRatio.compareTo(new BigDecimal(loooooo)) > 0){
                    resultMap.put(SAVE_FLAG,ERROR);
                    resultMap.put(ERROR_MSG, "投顾佣金率太大！");
                    return resultMap;
                }
            }else{
                updtateRatio=null;
            }

            List<BigDecimal> endPayIdList=Lists.newArrayList();
            if(StringUtil.isNotNullStr(endPayIds)){
                String[] array=endPayIds.split("\\,");
                Arrays.stream(array).forEach(id->endPayIdList.add(new BigDecimal(id)));
            }
            if(CollectionUtils.isEmpty(endPayIdList)){
                resultMap.put(SAVE_FLAG,ERROR);
                resultMap.put(ERROR_MSG, "更新缴款列表为空！");
                return resultMap;
            }
            int updateCount = cmBxPrebookBuyinfoService.batchChangeCalDataRyc(endPayIdList, updtateRatio, user.getUserId(), managePoint, manageCoeffRegionalsubtotal, manageCoeffRegionaltotal);
            resultMap.put(SAVE_FLAG, "success");
            resultMap.put("updateCount",updateCount);
        }catch(Exception e){
            log.error("saveChangeCalDataRyc信息异常：", e);
            resultMap.put(SAVE_FLAG,ERROR);
            resultMap.put(ERROR_MSG, "保存异常，请联系系统人员处理");
        }
        return resultMap;
    }

    /**
     * @description:(设置为null处理)
     * @param manageCoeff
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2025/8/22 16:36
     * @since JDK 1.8
     */
    private String setManageCoeffNull(String manageCoeff){
        return StringUtil.isNullStr(manageCoeff) ? null : manageCoeff;
    }

    /**
     * 首年度佣金FYC：first year commission
     * 续年度佣金RYC： renewal year commission
     * @return
     */
    @RequestMapping("/listSalePerformanceRyc.do")
    public String listSalePerformanceRyc(){
        return "insur/listSalePerformanceRyc";
    }

    @RequestMapping("/listSalePerformanceRycJson.do")
    @ResponseBody
    public Object listSalePerformanceRycJson(CmBxPrebookBuyWithPayVo vo){
        log.info("listSalePerformanceRycJson:{}", JSON.toJSONString(vo));
        //业务条件：冷静期截止日<系统日期 且 核保状态 = 核保通过  缴费状态=已交清
        vo.setCaltimeEndDt(DateUtil.getDateYYYYMMDD());
        vo.setInsurState(StaticVar.INSUR_STATE_HBTG);
        vo.setPayState(StaticVar.INSUR_PAYSTAT_HASPAY);
        PageData<CmBxPrebookBuyWithPayInfo> pageData = cmBxPrebookinfoService.listCmBxPrebookBuyWithPayInfoByPage(vo);
        if(pageData.getListData() != null){
            log.info("listSalePerformanceRycJson:{},pageData.size:{}", 1,pageData.getListData().size());
        }
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("total", pageData.getPageBean().getTotalNum());

        List<CmBxPrebookBuyWithPayInfoDto> pageList = Lists.newArrayList();
        List<CmBxPrebookBuyWithPayInfo> listdata = pageData.getListData();
        //获取管理系数Map
        Map<String, QueryManageCoeffVO.ManageCoeffInfo> manageCoeffMap = getManageCoeffMapByCmBxPrebookBuyWithPayInfo(listdata);
        for (CmBxPrebookBuyWithPayInfo info : listdata) {
            //计算管理系数
            cmBxPrebookinfoService.setManageCoeffByCmBxPrebookBuyWithPayInfo(info, manageCoeffMap);
            pageList.add(transferPageInfo(info));
        }
        resultMap.put("rows", pageList);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        return resultMap;
    }

    @RequestMapping("/exportSalePerformanceRyc.do")
    public void exportSalePerformanceRyc(CmBxPrebookBuyWithPayVo vo, HttpServletRequest request,HttpServletResponse response) throws Exception{
        Map<String, String> param = new ParamUtil(request).getParamMap();
        vo.setPage(1);
        vo.setRows(50000);
        //业务条件：冷静期截止日<系统日期 且 核保状态 = 核保通过  缴费状态=已交清
        vo.setCaltimeEndDt(DateUtil.getDateYYYYMMDD());
        vo.setInsurState(StaticVar.INSUR_STATE_HBTG);
        vo.setPayState(StaticVar.INSUR_PAYSTAT_HASPAY);
        PageData<CmBxPrebookBuyWithPayInfo> pageData = cmBxPrebookinfoService.listCmBxPrebookBuyWithPayInfoByPage(vo);
        List<Map> valueMapList= Lists.newArrayList();
        List<CmBxPrebookBuyWithPayInfo> listdata = pageData.getListData();
        //获取管理系数Map
        Map<String, QueryManageCoeffVO.ManageCoeffInfo> manageCoeffMap = getManageCoeffMapByCmBxPrebookBuyWithPayInfo(listdata);
        listdata.stream().forEach(dto-> {
            //计算管理系数
            cmBxPrebookinfoService.setManageCoeffByCmBxPrebookBuyWithPayInfo(dto, manageCoeffMap);
            CmBxPrebookBuyWithPayInfoDto exportDto= transferPageInfo(dto);
            valueMapList.add(new org.apache.commons.beanutils.BeanMap(exportDto));
        });
        String [] columnName = new String[] {"录入时间","明细ID","签单日期","预约状态","复核状态","保单状态","区域(预约时)","部门(预约时)","投顾(预约时)","中心(算绩效)","投顾(算绩效)",
                "员工编号(算绩效)","投顾客户号","投保人","受保人","业务类型","保险公司","产品类型","产品名称","缴费年限",
                "缴费年数","年缴保费/TP","保障年限","保单号","核保通过日期","冷静期截止日",
                "缴费状态","实际缴费日期","绩效汇率","第几年缴费","投顾佣金率","方案1-佣金","方案2-佣金",
                "是否核保通过变退保","备注", "佣金合计", MANAGE_POINT_CONSTANT, MANAGE_COEFF_REGIONALSUBTOTAL_CONSTANT, MANAGE_COEFF_REGIONALTOTAL_CONSTANT,
                "管理奖金基数-分总","管理奖金基数-区副","管理奖金基数-区总","最近修改人"};

        String [] beanProperty = new String[]{"creDtStr","preId","signDt","prestateval","checkStateVal","insurstateval","uporgname","orgName","consName","calCenterName","calconsname","calconscode",
                "conscustno","custName","insurName","busiTypeVal","compname","prodTypeVal","fundName","payyearsVal",
                "payyears2Val","yearAmk","ensureyearsVal","insurId","passDt","calTime",
                "paystateval","realPayDt","commissionRate","endPayOrder","commissionRatio","commissionStr","commissionfortwoStr",
                "ischangereturnVal","consremark", "totalCommission", "managePoint", "manageCoeffRegionalsubtotal", "manageCoeffRegionaltotal",
                "pointCommission","manageCoeffRegsubtotalComm","manageCoeffRegtotalComm","expModifier"};

        outputStream(
                 response,
                "销售业绩(续佣)_"+ DateUtil.getDateFormat(new Date(),"yyyyMMddHHmmss")+".xls",
                "销售业绩(续佣)",
                 columnName,
                 beanProperty,
                 valueMapList
        );
    }



    private CmBxPrebookBuyWithPayInfoDto  transferPageInfo(CmBxPrebookBuyWithPayInfo  info){
        CmBxPrebookBuyWithPayInfoDto pageDto=new CmBxPrebookBuyWithPayInfoDto();

        if(info != null){
            //算绩效时投顾
            info.setCalconscode(cmBxPrebookinfoService.getCalConsCode(info.getCalconscode(), info.getConscustno(), info.getRealPayDt()));
	        BeanUtils.copyProperties(info,pageDto);
	        pageDto.setCreDtStr(info==null?"":DateUtil.getDateFormat(info.getCreDt(),"yyyyMMdd"));
	
	        //页面翻译：
	        pageDto.setPrestateval(ConstantCache.getInstance().getVal("insurprestate", info.getPreState()));
	        pageDto.setInsurstateval(ConstantCache.getInstance().getVal("insurstate", info.getInsurState()));
	        ConsOrgCache orgcache = ConsOrgCache.getInstance();
	        String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgCode());
	        if("0".equals(uporgcode)){
	            pageDto.setUporgname(orgcache.getAllOrgMap().get(info.getOrgCode()));
	        }else{
	            pageDto.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
	        }
	        pageDto.setOrgName(ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgCode()));
	        pageDto.setConsName(ConsOrgCache.getInstance().getAllUserMap().get(info.getConsCode()));
            pageDto.setCalconsname(ConsOrgCache.getInstance().getAllUserMap().get(info.getCalconscode()));
            pageDto.setExpModifier(ConsOrgCache.getInstance().getAllUserMap().get(info.getExpModifier()));
            OrgLayerInfo orgLayerInfo = coreOrganizationService.getOrgLayerInfoByOrgCode(info.getOrgCode());
            if(orgLayerInfo!= null){
                pageDto.setCalCenterName(orgLayerInfo.getCenterOrgName());
            }
	//        pageDto.setRelation(ConstantCache.getInstance().getVal("insurrelation", info.getRelation()));
	        pageDto.setBusiTypeVal(ConstantCache.getInstance().getVal("insurbusitype", info.getBusiType()));
	        pageDto.setProdTypeVal(ConstantCache.getInstance().getVal("insurprodtype", info.getProdType()));
	        pageDto.setPaystateval(ConstantCache.getInstance().getVal("insurpaystate", info.getPayState()));
	        pageDto.setPayyearsVal(ConstantCache.getInstance().getVal("insurpayyears", info.getPayYears()));
	        if(info.getPayYears() != null && info.getPayYears().contains("至") && info.getInsurAge() != null){
	            try {
	                pageDto.setPayyears2Val(Integer.toString(Integer.parseInt(info.getPayYears().replace("至","").replace("岁","")) - info.getInsurAge()));
	            }catch (NumberFormatException e){
	                pageDto.setPayyears2Val(info.getPayYears());
	            }
	        }else {
	            pageDto.setPayyears2Val(info.getPayYears());
	        }
            String bxcommissionway = info.getBxcommissionway();
            if(StringUtil.isNullStr(bxcommissionway)){
                bxcommissionway = BxCommissionWayEnum.BXCOMMISSIONWAY_ONE.getCode();
                info.setBxcommissionway(bxcommissionway);
            }

            //佣金计算
            cmBxPrebookBuyinfoService.calPerformanceCommissionRyc(info);
            pageDto.setCommissionRatio(info.getCommissionRatio());
	        pageDto.setEnsureyearsVal(ConstantCache.getInstance().getVal("insurensureyears", info.getEnsureYears()));
	        pageDto.setIschangereturnVal(ConstantCache.getInstance().getVal("yesOrNo", info.getIsChangeReturn()));
            pageDto.setBxcommissionway(ConstantCache.getInstance().getVal("bxCommissionWay", bxcommissionway));
            //方案一时方案二为-
            if (BxCommissionWayEnum.BXCOMMISSIONWAY_ONE.getCode().equals(bxcommissionway)) {
                pageDto.setCommissionStr(null == info.getCommission() ? "-" : info.getCommission().toPlainString());
                pageDto.setCommissionfortwoStr("-");
            } else if (BxCommissionWayEnum.BXCOMMISSIONWAY_TWO.getCode().equals(bxcommissionway)) {
                pageDto.setCommissionStr("-");
                pageDto.setCommissionfortwoStr(null == info.getCommissionfortwo() ? "-" : info.getCommissionfortwo().toPlainString());
            }
            if (bxcommissionway != null) {
                //佣金总和
                pageDto.setTotalCommission(calculateTotalCommission(info.getCommission(), info.getCommissionfortwo()));
                //管理奖金基数（佣金总和 * 管理系数）
                pageDto.setPointCommission(calculatePointCommission(pageDto.getTotalCommission(), info.getManagePoint()));
                pageDto.setManageCoeffRegsubtotalComm(calculatePointCommissionByBigDecimal(pageDto.getTotalCommission(), info.getManageCoeffRegionalsubtotal()));
                pageDto.setManageCoeffRegtotalComm(calculatePointCommissionByBigDecimal(pageDto.getTotalCommission(), info.getManageCoeffRegionaltotal()));
            }
            pageDto.setCheckStateVal(hbConstantService.getHbConstantDesc("insurcheckstate", info.getCheckState()));
            pageDto.setManagePoint(info.getManagePoint());
        }
        return pageDto;
    }

    /**
     * @param commissionOne 方案1佣金
     * @param commissionTwo 方案2的佣金
     * @return
     * @description 计算方案1，2的佣金和
     * <AUTHOR>
     * @date 2023/9/5 下午5:08
     * @since JDK 1.8
     */
    private String calculateTotalCommission(BigDecimal commissionOne, BigDecimal commissionTwo) {
        if (commissionOne == null) {
            commissionOne = new BigDecimal(0);
        }
        if (commissionTwo == null) {
            commissionTwo = new BigDecimal(0);
        }
        return commissionOne.add(commissionTwo).toPlainString();
    }

    /**
     * @param totalCommission 佣金和
     * @param managePoint     管理系数
     * @return
     * @description 计算管理奖金基数
     * <AUTHOR>
     * @date 2023/9/5 下午5:08
     * @since JDK 1.8
     */
    private String calculatePointCommission(String totalCommission, String managePoint) {
        if (StringUtils.isEmpty(totalCommission) || StringUtils.isEmpty(managePoint)) {
            return "";
        }
        return new BigDecimal(totalCommission).multiply(new BigDecimal(managePoint)).toPlainString();
    }

    /**
     * @param pageList
     * @param commissionCalStartDt 开始时间
     * @param commissionCalEndDt   结束时间
     * @return
     * @description 页面新增了【佣金核算日期】的查询条件 过滤结果
     * <AUTHOR>
     * @date 2023/9/5 下午5:09
     * @since JDK 1.8
     */
    private List<CmBxPrebookBuyWithPayInfoDto> filterPageListWithRealPayDt(List<CmBxPrebookBuyWithPayInfoDto> pageList, String commissionCalStartDt, String commissionCalEndDt) {
        if (StringUtils.isEmpty(commissionCalStartDt) && StringUtils.isEmpty(commissionCalEndDt)) {
            return pageList;
        }
        if (CollectionUtils.isNotEmpty(pageList)) {
            pageList = pageList.stream().filter(info -> judgeBeginAndEndTime(commissionCalStartDt, commissionCalEndDt, info.getRealPayDt())).collect(Collectors.toList());
        }
        return pageList;

    }

    /**
     * @param commissionCalStartDt
     * @param commissionCalEndDt
     * @param realDt
     * @return
     * @description 当开始时间有值，结束时间无值：查询 开始时间≤实际缴款日期 的数据
     * 当开始时间无值，结束时间有值：查询 实际缴款日期≤结束时间 的数据
     * 当开始时间有值，结束时间有值：查询 开始时间≤实际缴款日期≤结束时间 的数据
     * <AUTHOR>
     * @date 2023/9/5 下午5:21
     * @since JDK 1.8
     */
    private boolean judgeBeginAndEndTime(String commissionCalStartDt, String commissionCalEndDt, String realDt) {
        if (StringUtils.isNotEmpty(realDt)) {
            if (StringUtils.isNotEmpty(commissionCalStartDt) && StringUtils.isNotEmpty(commissionCalEndDt)) {
                return commissionCalStartDt.compareTo(realDt) <= 0
                        && commissionCalEndDt.compareTo(realDt) >= 0;
            }
            if (StringUtils.isEmpty(commissionCalStartDt) && StringUtils.isNotEmpty(commissionCalEndDt)) {
                return commissionCalEndDt.compareTo(realDt) >= 0;
            }
            if (StringUtils.isEmpty(commissionCalEndDt) && StringUtils.isNotEmpty(commissionCalStartDt)) {
                return commissionCalStartDt.compareTo(realDt) <= 0;
            }
        } else {
            //实际缴款日期为空的时候，如果筛选条件有值，则不展示该条数
            if (StringUtils.isNotEmpty(commissionCalStartDt) || StringUtils.isNotEmpty(commissionCalEndDt)) {
                return false;
            }
        }
        //默认正常展示所有条数
        return true;
    }


    /**
     * @description 转换查询出来的payyears  表中存在中文数据 （eg. 至70岁）
     * @param payYearsInDB
     * @return
     * <AUTHOR>
     * @date 2023/9/5 下午5:20
     * @since JDK 1.8
     */
    private String transferPayYears(String payYearsInDB) {
        if (payYearsInDB.startsWith("至") && payYearsInDB.endsWith("岁")) {
            return payYearsInDB.substring(payYearsInDB.indexOf("至")+1, payYearsInDB.indexOf("岁"));
        }
        return payYearsInDB;
    }


    /**
     * @description:(创新-销售业绩（续佣）-获得管理系数)
     * @param cmBxPrebookinfoList
     * @return void
     * @author: shijie.wang
     * @date: 2025/8/22 15:33
     * @since JDK 1.8
     */
    private Map<String, QueryManageCoeffVO.ManageCoeffInfo> getManageCoeffMapByCmBxPrebookBuyWithPayInfo(List<CmBxPrebookBuyWithPayInfo> cmBxPrebookinfoList){
        // 获取管理系数Map
        return commonService.getManageCoeffMap(cmBxPrebookinfoList, vo ->{
            QueryManageCoeffRequest.PreIdInfo preIdInfo = new QueryManageCoeffRequest.PreIdInfo();
            preIdInfo.setPreId(String.valueOf(vo.getPreId()));
            preIdInfo.setConsCustNo(vo.getConscustno());
            preIdInfo.setConsCode(vo.getConsCode());
            //实际缴款日期
            preIdInfo.setCreDt(vo.getRealPayDt());
            return preIdInfo;
        });
    }
}
