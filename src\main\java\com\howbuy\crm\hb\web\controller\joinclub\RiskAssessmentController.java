package com.howbuy.crm.hb.web.controller.joinclub;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.acccenter.facade.query.kyc.*;
import com.howbuy.acccenter.facade.query.kyc.bean.AnswerHisInfoBean;
import com.howbuy.acccenter.facade.query.kyc.bean.ExamInfoBean;
import com.howbuy.acccenter.facade.query.kyc.bean.OptionInfoBean;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoFacade;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoRequest;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoResponse;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.crm.account.client.response.custinfo.HkKycAnswerVO;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.joinclub.CustAnswerHisBeanLocal;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.outerservice.CrmAccountOuterService;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.util.RMIConstant;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageResult;
import crm.howbuy.base.enums.CrmCustInvestTypeEnum;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.enums.DisCodeEnum;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.DisCodeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RiskAssessmentController.java
 * @Description TODO
 * @createTime 2021年09月27日 17:44:00
 */
@Slf4j
@Controller
@RequestMapping(value = "/riskassessment")
public class RiskAssessmentController {

    @Autowired
    private ConscustService custService;

    @Autowired
    private QueryLatestAnswerFacade queryLatestAnswerFacade;

    @Autowired
    private KycInfoFacade kycInfoFacade;

    @Autowired
    private DecryptSingleFacade decryptSingleFacade;

    @Autowired
    private QueryAnswerHistoryListFacade queryAnswerHistoryListFacade;
    @Autowired
    private QueryAnswerHistoryByIdFacade queryAnswerHistoryByIdFacade;

    @Autowired
    private CrmAccountOuterService crmAccountOuterService;

    /**
     * @description:(获取风险评测历史数据)
     * @param consCustno	投顾客户号
     * @param channelCode	分销机构代码
     * @return java.lang.String
     * @author: shucheng.luo
     * @date: 2023/5/12 13:45
     * @since JDK 1.8
     */
    @RequestMapping("/showRiskAssessmentHis")
    public String showRiskAssessmentHis(@RequestParam String consCustno,
                                        @RequestParam(required = false) String channelCode,
                                        HttpServletRequest request) {
        request.setAttribute("consCustno", consCustno);
        request.setAttribute("channelCode", channelCode);
        // 默认显示的起止时间为，最近一年的
        LocalDate today = LocalDate.now();
        LocalDate oneYearAgoToday = today.minusYears(1);
        request.setAttribute("startDate", oneYearAgoToday.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        request.setAttribute("endDate", DateUtil.getDateYYYYMMDD());
        return "joinclub/showriskassessmentHis";
    }


    /**
     * @api {GET} /riskassessment/showHkRiskAssessmentHis showHkRiskAssessmentHis()
     * @apiVersion 1.0.0
     * @apiGroup RiskAssessmentController
     * @apiName showHkRiskAssessmentHis()
     * @apiParam (请求参数) {String} hkTxAcctNo
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=F05
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "4xmlCFh"
     */
    @RequestMapping("/showHkRiskAssessmentHis")
    public String showHkRiskAssessmentHis(@RequestParam String hkTxAcctNo,
                                        HttpServletRequest request) {
        request.setAttribute("hkTxAcctNo", hkTxAcctNo);
        // 默认显示的起止时间为，最近一年的
        LocalDate today = LocalDate.now();
        LocalDate oneYearAgoToday = today.minusYears(1);
        request.setAttribute("startDate", oneYearAgoToday.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        request.setAttribute("endDate", DateUtil.getDateYYYYMMDD());
        return "joinclub/showHkRiskAssessmentHis";
    }


    /**
     * @description:(获取风险评测历史数据)
     * @param consCustno	投顾客户号
     * @param channelCode	分销机构代码
     * @param startDate	开始日期（yyyyMMdd）
     * @param endDate	结束日期（yyyyMMdd）
     * @return java.lang.String
     * @author: shucheng.luo
     * @date: 2023/5/12 13:45
     * @since JDK 1.8
     */
    @RequestMapping("/showRiskAssessmentHis_json")
    @ResponseBody
    public PageResult<CustAnswerHisBeanLocal> showRiskAssessmentHis(@RequestParam String consCustno,
                                        @RequestParam(required = false) String channelCode,
                                        @RequestParam(required = false) String startDate,
                                        @RequestParam(required = false) String endDate) {
        DisChannelCodeEnum channelCodeEnum=DisChannelCodeEnum.getEnum(channelCode);
        if(channelCodeEnum==null){
            channelCodeEnum=DisChannelCodeEnum.HOWBUY;//默认查看HOWBUY分销的
        }

        Conscust cust = custService.getConscust(consCustno);
        String invsttype = cust.getInvsttype();

        if (StringUtils.isBlank(invsttype)) {
            invsttype = StaticVar.INVST_TYPE_PERSONAL;
        }
        CrmCustInvestTypeEnum investTypeEnum=CrmCustInvestTypeEnum.getEnum(invsttype);
        DisCodeEnum disCodeEnum=DisCodeUtil.getBusiDisCode(channelCodeEnum,investTypeEnum );

        // 调用账户中心接口，获取风险评测历史
        QueryAnswerHistoryListRequest historyListRequest = new QueryAnswerHistoryListRequest();
        historyListRequest.setHboneNo(cust.getHboneno());
        historyListRequest.setDisCode(disCodeEnum.getCode());
        historyListRequest.setStartDate(startDate);
        historyListRequest.setEndDate(endDate);
        log.info("查询风险评测历史，一账通账号：{} 分销渠道：{} startDate：{} endDate：{}",
                cust.getHboneno(), disCodeEnum.getDescription(), startDate, endDate);
        QueryAnswerHistoryListResponse historyListResponse = queryAnswerHistoryListFacade.execute(historyListRequest);
        log.info("查询风险评测历史，request:{}，response: {} ", JSONObject.toJSONString(historyListRequest), JSONObject.toJSONString(historyListResponse));
        // 请求失败或没有数据
        if (historyListResponse == null || !RMIConstant.RMISuccNew.equals(historyListResponse.getReturnCode())
                || CollectionUtils.isEmpty(historyListResponse.getCustAnswerHisBeanList())) {
            return new PageResult<>();
        }

        // 对接口数据进行转换
        Map<String, String> newgpsRiskLevelMap = ConstantCache.getInstance().getConstantKeyVal("newgpsRiskLevel");
        List<CustAnswerHisBeanLocal> answerHisList = historyListResponse.getCustAnswerHisBeanList().stream().map(custAnswerHisBean -> {
            CustAnswerHisBeanLocal hisBeanLocal = new CustAnswerHisBeanLocal();
            BeanUtils.copyProperties(custAnswerHisBean, hisBeanLocal);
            // 风测日期
            String createStr = DateUtil.getDateFormat(hisBeanLocal.getCreateDate(), "yyyyMMdd HH:mm:ss");
            hisBeanLocal.setCreateDateStr(createStr);
            // 来源（如果code为CRM，则展示CRM；其他code，显示为“APP/网站”）
            String outletCodeName = StaticVar.JOIN_CLUB_OUTLET_CODE_CRM.equals(hisBeanLocal.getOutletCode())
                    ? StaticVar.JOIN_CLUB_OUTLET_CODE_NAME_CRM : StaticVar.JOIN_CLUB_OUTLET_CODE_NAME_OTHER;
            hisBeanLocal.setOutletCodeName(outletCodeName);
            // 高端风险评测结果
            String levelName = newgpsRiskLevelMap.get(hisBeanLocal.getLevelValue());
            hisBeanLocal.setLevelName(levelName);
            return hisBeanLocal;
        }).collect(Collectors.toList());

        PageResult<CustAnswerHisBeanLocal> pageResult = new PageResult<>();
        pageResult.setRows(answerHisList);
        pageResult.setTotal(answerHisList.size());
        return pageResult;
    }


    /**
     * @api {GET} /riskassessment/showHkRiskAssessmentHis_json showHkRiskAssessmentHis()
     * @apiVersion 1.0.0
     * @apiGroup RiskAssessmentController
     * @apiName showHkRiskAssessmentHis()
     * @apiParam (请求参数) {String} hkTxAcctNo
     * @apiParam (请求参数) {String} startDate
     * @apiParam (请求参数) {String} endDate
     * @apiParamExample 请求参数示例
     * hkTxAcctNo=iI9&endDate=Cg&startDate=olUm
     * @apiSuccess (响应结果) {Array} rows
     * @apiSuccess (响应结果) {String} rows.levelValue
     * @apiSuccess (响应结果) {Number} rows.score
     * @apiSuccess (响应结果) {String} rows.derivativeKnowledge
     * @apiSuccess (响应结果) {String} rows.riskToleranceDate
     * @apiSuccess (响应结果) {String} rows.riskToleranceTerm
     * @apiSuccess (响应结果) {Number} rows.stimestamp
     * @apiSuccess (响应结果) {String} rows.outletCodeName
     * @apiSuccess (响应结果) {Number} total
     * @apiSuccessExample 响应结果示例
     * {"total":7027,"rows":[{"score":2481.283087985635,"derivativeKnowledge":"H","riskToleranceDate":"elgzkG","stimestamp":2932833248034,"outletCodeName":"up7bjr9eie","riskToleranceTerm":"VSH","levelValue":"Ckla2oMFO1"}]}
     */
    @RequestMapping("/showHkRiskAssessmentHis_json")
    @ResponseBody
    public PageResult<HkKycAnswerVO> showHkRiskAssessmentHis(@RequestParam String hkTxAcctNo,
                                                                      @RequestParam String startDate,
                                                                      @RequestParam String endDate) {
        List<HkKycAnswerVO> hkKycAnswerVOS = crmAccountOuterService.queryHkKycAnswerListByHkTxAcctNo(hkTxAcctNo, startDate, endDate);
        hkKycAnswerVOS.forEach(hkKycAnswerVO -> {
            //翻译 客户等级
            hkKycAnswerVO.setLevelValue(getRiskShowStr(hkKycAnswerVO.getLevelValue()));
            //排序
            sortMisMatch(hkKycAnswerVO.getMismatchFundList());
        }
    );

        PageResult<HkKycAnswerVO> pageResult = new PageResult<>();
        pageResult.setRows(hkKycAnswerVOS);
        pageResult.setTotal(hkKycAnswerVOS.size());
        return pageResult;
    }


    /**
     * @description:(风险评测结果的基金列表 排序处理)
     * @param misMatchList
     * @return void
     * @author: haoran.zhang
     * @date: 2024/8/12 13:20
     * @since JDK 1.8
     */
    private void sortMisMatch(List<HkKycAnswerVO.HkMismatchFundVO> misMatchList){
        if(CollectionUtils.isEmpty(misMatchList)){
            return;
        }
        //列表排序规则：优先按产品风险等级降序排(R5>R4)；产品风险等级相同的，按产品代码升序排(字母>数字；A>Z，1>9)
        misMatchList.sort((o1, o2) -> {
            if(StringUtil.isEmpty(o1.getFundRiskLevel())){
                o1.setFundRiskLevel("");
            }
            if (o1.getFundRiskLevel().equals(o2.getFundRiskLevel())) {
                return o1.getFundCode().compareTo(o2.getFundCode());
            }
            return o2.getFundRiskLevel().compareTo(o1.getFundRiskLevel());
        });
    }


    /**
     * @description:(请在此添加描述)
     * @param levelValue
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2023/12/29 14:26
     * @since JDK 1.8
     */
    private String getRiskShowStr(String levelValue) {
        if ("1".equals(levelValue)) {
            return "保守型（C1）";
        } else if ("2".equals(levelValue)) {
            return "中度保守型（C2）";
        } else if ("3".equals(levelValue)) {
            return "平稳型（C3）";
        } else if ("4".equals(levelValue)) {
            return "中度进取型（C4）";
        } else if ("5".equals(levelValue)){
            return "进取型（C5）";
        }
        return "";

    }

    /**
     * @description:(获取风险评测历史详情)
     * @param consCustno	投顾客户号
     * @param answerHisId	问卷历史id
     * @return java.lang.String
     * @author: shucheng.luo
     * @date: 2023/5/12 17:45
     * @since JDK 1.8
     */
    @RequestMapping("/showRiskAssessmentHisDetail")
    public String showRiskAssessmentHisDetail(@RequestParam String consCustno,
                                              @RequestParam String answerHisId,
                                              HttpServletRequest request) {
        Conscust cust = custService.getConscust(consCustno);

        String invsttype = cust.getInvsttype();
        if (StringUtils.isBlank(invsttype)) {
            invsttype = StaticVar.INVST_TYPE_PERSONAL;
        }

        //机构问卷
        if (StaticVar.INVST_TYPE_ORG.equals(invsttype)) {
            String idtype = ConstantCache.getInstance().getVal("IDTypesOfInst", cust.getIdtype());
            if (StringUtils.isNotBlank(idtype)) {
                cust.setIdtype(idtype);
            }
        }

        if(StringUtil.isNotNullStr(cust.getIdnoCipher())){
            cust.setIdno(decryptSingleFacade.decrypt(cust.getIdnoCipher()).getCodecText());
        }
        if(StringUtil.isNotNullStr(cust.getAddrCipher())){
            cust.setAddr(decryptSingleFacade.decrypt(cust.getAddrCipher()).getCodecText());
        }
        if(StringUtil.isNotNullStr(cust.getEmailCipher())){
            cust.setEmail(decryptSingleFacade.decrypt(cust.getEmailCipher()).getCodecText());
        }

        // 设置客户信息
        request.setAttribute("cust", cust);
        
        String view = StaticVar.INVST_TYPE_PERSONAL.equals(invsttype) ? "joinclub/showriskassessmentHisDetail"
                : "joinclub/showorgriskassessmentHisDetail";
        
        QueryAnswerHistoryByIdRequest historyByIdRequest = new QueryAnswerHistoryByIdRequest();
        historyByIdRequest.setHboneNo(cust.getHboneno());
        historyByIdRequest.setAnswerHisId(answerHisId);
        log.info("查询风险评测历史详情，一账通账号：{} 问卷历史id：{}", cust.getHboneno(), answerHisId);
        QueryAnswerHistoryByIdResponse historyByIdResponse = queryAnswerHistoryByIdFacade.execute(historyByIdRequest);
        log.info("查询风险评测历史，request:{} ，response: {} ", JSONObject.toJSONString(historyByIdRequest), JSONObject.toJSONString(historyByIdResponse));
        // 请求失败
        if (historyByIdResponse == null || !RMIConstant.RMISuccNew.equals(historyByIdResponse.getReturnCode())) {
            request.setAttribute("domain", Maps.newHashMap());
            request.setAttribute("questions", Lists.newArrayList());
            return view;
        }
        
        // 问卷题目信息
        ExamInfoBean examInfo = historyByIdResponse.getExamInfo();
        if (examInfo != null && examInfo.getQuestions() != null) {
            request.setAttribute("questions", examInfo.getQuestions());
        } else {
            request.setAttribute("questions", Lists.newArrayList());
        }

        // 问卷答案信息
        AnswerHisInfoBean answerHisInfo = historyByIdResponse.getAnswerHisInfo();
        if (answerHisInfo != null && answerHisInfo.getAnswers() != null) {
            Map<String, String> ansmap = new HashMap<String, String>();
            Map<String, OptionInfoBean> lastanswermap = answerHisInfo.getAnswers();
            for(Map.Entry<String, OptionInfoBean> entry : lastanswermap.entrySet()){
                ansmap.put(entry.getKey(),entry.getValue().getOptionId());
            }
            request.setAttribute("domain", ansmap);
        } else {
            request.setAttribute("domain", Maps.newHashMap());
        }
        return view;
    }

    /**
     * 兼容 默认查看好买分销
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/showRiskAssessmentView")
    public String showRiskAssessmentView(HttpServletRequest request) throws Exception {
        String custno = request.getParameter("consCustno");
        DisChannelCodeEnum channelCodeEnum=DisChannelCodeEnum.getEnum(request.getParameter("channelCode"));
        if(channelCodeEnum==null){
            channelCodeEnum=DisChannelCodeEnum.HOWBUY;//默认查看HOWBUY分销的
        }


        Conscust cust = custService.getConscust(custno);

        String invsttype = cust.getInvsttype();

        if (StringUtils.isBlank(invsttype)) {
            invsttype = "1";
        }
        CrmCustInvestTypeEnum investTypeEnum=CrmCustInvestTypeEnum.getEnum(cust.getInvsttype());

        DisCodeEnum disCodeEnum=DisCodeUtil.getBusiDisCode(channelCodeEnum,investTypeEnum );

        QueryLatestAnswerRequest lastAnswerRequest = new QueryLatestAnswerRequest();
        lastAnswerRequest.setHboneNo(cust.getHboneno());
        lastAnswerRequest.setShowExam("Y");
        lastAnswerRequest.setDisCode(disCodeEnum.getCode());
        log.info("查询风险评测问卷，一账通账号：{} 分销渠道:{}", cust.getHboneno(),disCodeEnum.getDescription());
        QueryLatestAnswerResponse lastAnswerResponse = queryLatestAnswerFacade.execute(lastAnswerRequest);
        log.info("查询风险评测问卷，request:{} ，response: {} " , JSONObject.toJSONString(lastAnswerRequest),JSONObject.toJSONString(lastAnswerResponse));

        if ("0000000".equals(lastAnswerResponse.getReturnCode())) {
            if (lastAnswerResponse.getAnswerHisInfo() != null && lastAnswerResponse.getAnswerHisInfo().getAnswers() != null) {
                Map<String, String> ansmap = new HashMap<String, String>();
                Map<String, OptionInfoBean> lastanswermap = lastAnswerResponse.getAnswerHisInfo().getAnswers();
                for(Map.Entry<String, OptionInfoBean> entry : lastanswermap.entrySet()){
                    ansmap.put(entry.getKey(),entry.getValue().getOptionId());
                }
                request.setAttribute("domain", ansmap);
            } else {
                request.setAttribute("domain", Maps.newHashMap());
            }

            if (lastAnswerResponse.getExamInfo() != null && lastAnswerResponse.getExamInfo().getQuestions() != null) {
                request.setAttribute("questions", lastAnswerResponse.getExamInfo().getQuestions());
            } else {
                request.setAttribute("questions", Lists.newArrayList());
            }

        } else {
            request.setAttribute("domain", Maps.newHashMap());
            request.setAttribute("questions", Lists.newArrayList());
        }
        //机构问卷
        if (StaticVar.INVST_TYPE_ORG.equals(invsttype)) {
            String idtype = ConstantCache.getInstance().getVal("IDTypesOfInst", cust.getIdtype());
            if (StringUtils.isNotBlank(idtype)) {
                cust.setIdtype(idtype);
            }
        }

        if(StringUtil.isNotNullStr(cust.getIdnoCipher())){
            cust.setIdno(decryptSingleFacade.decrypt(cust.getIdnoCipher()).getCodecText());
        }
        if(StringUtil.isNotNullStr(cust.getAddrCipher())){
            cust.setAddr(decryptSingleFacade.decrypt(cust.getAddrCipher()).getCodecText());
        }
        if(StringUtil.isNotNullStr(cust.getEmailCipher())){
            cust.setEmail(decryptSingleFacade.decrypt(cust.getEmailCipher()).getCodecText());
        }

        request.setAttribute("cust", cust);
        //个人问卷
        if (StaticVar.INVST_TYPE_PERSONAL.equals(invsttype)) {
            return "joinclub/showriskassessment";
        } else {
            return "joinclub/showorgriskassessment";
        }
    }

    /**
     *合格投资人承诺书--查看
     * channelCode 为产品的分销代码，Eg: HB000A001-好买  HZ000N001-好臻
     * 默认查看  HB000A001-好买
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/showInvestorView")
    public String showInvestorView(HttpServletRequest request) throws Exception {
        String custno = request.getParameter("consCustno");
        String channelCode=request.getParameter("channelCode");
        DisChannelCodeEnum channelCodeEnum=DisChannelCodeEnum.getEnum(channelCode);
        if(channelCodeEnum==null){
            channelCodeEnum=DisChannelCodeEnum.HOWBUY;//默认查看HOWBUY分销的
        }

        Conscust cust = custService.getConscust(custno);

        CrmCustInvestTypeEnum investTypeEnum=CrmCustInvestTypeEnum.getEnum(cust.getInvsttype());

        DisCodeEnum disCodeEnum=DisCodeUtil.getBusiDisCode(channelCodeEnum,investTypeEnum );

        //查询KYC问卷
        KycInfoRequest queryUserRequest = new KycInfoRequest();
        queryUserRequest.setHboneNo(cust.getHboneno());
        queryUserRequest.setDisCode(disCodeEnum.getCode());
        log.info("查询KYC问卷，一账通账号：{} 分销渠道:{}", cust.getHboneno(),disCodeEnum.getDescription());
        KycInfoResponse kycInfoResponse = kycInfoFacade.execute(queryUserRequest);
        log.info("查询KYC问卷 ，response: {} " , JSONObject.toJSONString(kycInfoResponse));
        String qualifiedinvestor = "";
        //合格投资人承诺书
        if (StaticVar.ZT_SUCCESS_FLAG.equals(kycInfoResponse.getReturnCode())) {
            int i = 0;
            if (kycInfoResponse.getAnnualIncomeConfirmation()) {
                qualifiedinvestor = "01";
                i++;
            }

            if (kycInfoResponse.getFinancialAssetConfirmation()) {

                if (i > 0) {
                    qualifiedinvestor = "01,02";
                } else {
                    qualifiedinvestor = "02";
                }
            }
        }

        if(StringUtil.isNotNullStr(cust.getIdnoCipher())){
            cust.setIdno(decryptSingleFacade.decrypt(cust.getIdnoCipher()).getCodecText());
        }
        if(StringUtil.isNotNullStr(cust.getAddrCipher())){
            cust.setAddr(decryptSingleFacade.decrypt(cust.getAddrCipher()).getCodecText());
        }
        if(StringUtil.isNotNullStr(cust.getEmailCipher())){
            cust.setEmail(decryptSingleFacade.decrypt(cust.getEmailCipher()).getCodecText());
        }
        request.setAttribute("cust", cust);
        log.info("答案：qualifiedinvestor:"+qualifiedinvestor);
        request.setAttribute("qualifiedinvestor", qualifiedinvestor);
        //个人问卷
        if (CrmCustInvestTypeEnum.PERSONAL.equals(investTypeEnum)) {
            return "joinclub/showinvestor";
        } else {
            return "joinclub/showorginvestor";
        }

    }

    @RequestMapping("/showFundSurveyView")
    public String showFundSurveyView(HttpServletRequest request) throws Exception {
        String custno = request.getParameter("consCustno");

        Conscust cust = custService.getConscust(custno);

        String invsttype = cust.getInvsttype();
        //查询KYC问卷
        KycInfoRequest queryUserRequest = new KycInfoRequest();
        queryUserRequest.setHboneNo(cust.getHboneno());

        if(!"1".equals(invsttype)){
            queryUserRequest.setDisCode("FOF201710");
        }else{
            queryUserRequest.setDisCode("HB000A001");
        }
        log.info("查询KYC问卷，一账通账号：{} 分销渠道:{}", cust.getHboneno(),queryUserRequest.getDisCode());
        KycInfoResponse kycInfoResponse = kycInfoFacade.execute(queryUserRequest);
        log.info("查询KYC问卷 ，response: {} " , JSONObject.toJSONString(kycInfoResponse));
        String qualifiedinvestor = "";
        if (StaticVar.ZT_SUCCESS_FLAG.equals(kycInfoResponse.getReturnCode())) {//合格投资人承诺书
            if (StringUtil.isNotNullStr(kycInfoResponse.getQualifyFlag())) {
                qualifiedinvestor = kycInfoResponse.getQualifyFlag();
            }
        }

        if(StringUtil.isNotNullStr(cust.getIdnoCipher())){
            cust.setIdno(decryptSingleFacade.decrypt(cust.getIdnoCipher()).getCodecText());
        }
        log.info("答案：qualifiedinvestor:"+qualifiedinvestor);
        request.setAttribute("cust", cust);
        request.setAttribute("qualifiedinvestor", qualifiedinvestor);

        return "joinclub/showfundsurveyview";
    }
}
