package com.howbuy.crm.hb.web.controller.prosale;

import com.howbuy.crm.base.DivModeEnum;
import com.howbuy.crm.base.PreBookArchTypeEnum;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.domain.conscust.CustAcctAttrVo;
import com.howbuy.crm.hb.domain.manage.UpLoadPrivateTrade;
import com.howbuy.crm.hb.domain.prosale.zxtraderecordaudit.CmPrivatefundtradeAudit;
import com.howbuy.crm.hb.enums.TradeType;
import com.howbuy.crm.hb.enums.UploadTradeTypeEnum;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.product.ProductManagerService;
import com.howbuy.crm.hb.service.prosale.CmPrivatefundtradeAuditService;
import com.howbuy.crm.hb.service.prosale.CustprivatefundService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.reader.ExcelReader;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.utils.StringUtil;
import jxl.CellType;
import jxl.NumberCell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.read.biff.BiffException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description: 上传直销导入记录 控制层
 * <AUTHOR>
 * @date 2022/4/20 11:02
 * @since JDK 1.8
 */
@Controller
@RequestMapping(value = "/prosale")
public class UpLoadZxPrivateTradeController  extends BaseController {

	private static Logger LOG = LoggerFactory.getLogger(UpLoadZxPrivateTradeController.class);

	private final String DOWNLOAD_FILE_NAME="CRM直销交易记录导入模板.xls";

	private final String MODEL_FILE_NAME="crmzxtrademodel.xls";

	private final String DOWNLOAD_FILE_NAME_INFO="CRM直销交易导入功能使用教程.pdf";

	private final String MODEL_FILE_NAME_INFO="crmtrademodelinfo.pdf";

    private static final String ERROR = "error";
    private static final String ERROR_MSG = "errorMsg";

    private static final int DATE_LENGTH = 8;
    private static final int DISCSUMMARY_MAX_LENGTH = 30;
    private static final String ZERO = "0";

    /**
     * 对于股权类型的产品，备注有值时的待确认数据
     */
    private Map<String, List<CmPrivatefundtradeAudit>> userIdAndConfirmedAuditMap = new HashMap<>();

	@Autowired
	private ConscustService custService;

	@Autowired
	private CustprivatefundService custprivatefundService;

	@Autowired
	private CmPrivatefundtradeAuditService cmPrivatefundtradeAuditService;

    @Autowired
    private ProductManagerService productManagerService;

    @Autowired
    private PrebookBasicInfoService prebookBasicInfoService;

    @RequestMapping(value = "/loadPrivateTradeXls.do", method = RequestMethod.GET)
    public ModelAndView loadPrivateTradeXls() {
        return new ModelAndView("/prosale/uploadprivatetrade");
    }


    @RequestMapping(value = "/uploadPrivateTrade.do", method = RequestMethod.POST)
    public @ResponseBody Map<String, Object> uploadPrivateTrade(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("uploadFlag", ERROR);

        String tradeType = request.getParameter("htradetype");
        InputStream input = null;
        Workbook workBook;
        try {
            // 转型为MultipartHttpRequest：
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            // 获得文件：
            MultipartFile file = multipartRequest.getFile("file");
            // 获得输入流：
            input = file.getInputStream();
            workBook = Workbook.getWorkbook(input);

            // 解析上传的数据，获取交易记录列表
            List<UpLoadPrivateTrade> privateTradeList = getUpLoadPrivateTradeList(workBook, tradeType);
            if (privateTradeList == null || privateTradeList.isEmpty()) {
                resultMap.put(ERROR_MSG, "上传数据不能为空！");
                return resultMap;
            }
            String judgeCustomIsExsit = judgeCustomIsExsit(privateTradeList);
            if (StringUtil.isNotNullStr(judgeCustomIsExsit)) {
                resultMap.put(ERROR_MSG, judgeCustomIsExsit);
                return resultMap;
            }

            // 判断客户号必填项
            String conScustNoErrorMsg = judgeVoidConScustNo(privateTradeList);
            if (StringUtil.isNotNullStr(conScustNoErrorMsg)) {
                resultMap.put(ERROR_MSG, conScustNoErrorMsg);
                return resultMap;
            }
            String promptMsg = checkDividendMode(privateTradeList);

            // 校验数据的完整性
            String dataIntegrityErrorMsg = judgeDataIntegrity(privateTradeList);
            if (StringUtil.isNotNullStr(dataIntegrityErrorMsg)) {
                resultMap.put(ERROR_MSG, dataIntegrityErrorMsg);
                return resultMap;
            }

            User userlogin = (User) request.getSession().getAttribute("loginUser");
            List<CmPrivatefundtradeAudit> privatefundtradeAudits = getCmPrivatefundtradeAudits(privateTradeList, userlogin);
            // 交易类型为强赎，强减。强增 判断备注是否填写
            String checkRemarkMsg = checkRemark(privateTradeList);
            if (StringUtil.isNotNullStr(checkRemarkMsg)) {
                // 对于股权类型的产品，如果未填备注，将数据放入待确认缓存中，等页面上用户再次点击确认后，调用"confirmupload.do"接口 执行插入
                userIdAndConfirmedAuditMap.put(userlogin.getUserId(), privatefundtradeAudits);
                resultMap.put("isdiscsummary", checkRemarkMsg);
                return resultMap;
            }

            //批量插入直销交易待审核表
            ReturnMessageDto<Integer> resultobj = cmPrivatefundtradeAuditService.batchInsertCmPrivatefundtradeAudit(privatefundtradeAudits);
            if (!resultobj.isSuccess()) {
                resultMap.put(ERROR_MSG, "导入失败！");
                return resultMap;
            }

            LOG.info(userlogin.getUserId() + "：导入成功！");
            resultMap.put("uploadFlag", "success");
            resultMap.put(ERROR_MSG, "");
            resultMap.put("promptMsg", promptMsg);
        } catch (BiffException e) {
            e.printStackTrace();
            resultMap.put(ERROR_MSG, e.getMessage());
        } catch (IOException e) {
            e.printStackTrace();
            resultMap.put(ERROR_MSG, e.getMessage());
        } finally {
            try {
                if (input != null) {
                    input.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return resultMap;
    }

    private String checkDividendMode(List<UpLoadPrivateTrade> privateTradeList) {
        StringBuilder result = new StringBuilder();
        String tradeType = privateTradeList.get(0).getTradetype();
        String tradeTypeDescription = UploadTradeTypeEnum.getDescription(privateTradeList.get(0).getTradetype());

        if (!StaticVar.LOAD_TRADETYPE_DIV.equals(tradeType)) {
            for (UpLoadPrivateTrade upLoadPrivateTrade : privateTradeList) {
                if (upLoadPrivateTrade.getDividendMode() != null) {
                    result.append(String.format("客户【%s】，交易类型为【%s】, 无需录入【分红方式】",
                            upLoadPrivateTrade.getConscustno(), tradeTypeDescription)).append(".</br>");
                }
            }
        }
        return result.toString();
    }

    /**
     * @description: 将上传的excel数据 转成持久类
     * @param privateTradeList	上传的excel数据
     * @param userlogin	上传的用户
     * @return java.util.List<com.howbuy.crm.hb.domain.prosale.zxtraderecordaudit.CmPrivatefundtradeAudit>
     * @author: jin.wang03
     * @date: 2023/9/13 15:08
     * @since JDK 1.8
    */
    private static List<CmPrivatefundtradeAudit> getCmPrivatefundtradeAudits(List<UpLoadPrivateTrade> privateTradeList, User userlogin) {
        List<CmPrivatefundtradeAudit> privatefundtradeAudits = new ArrayList<>(privateTradeList.size());
        for (UpLoadPrivateTrade vo : privateTradeList) {
            CmPrivatefundtradeAudit ulpt = new CmPrivatefundtradeAudit();
            ulpt.setConscustno(vo.getConscustno());
            ulpt.setFundcode(vo.getFundcode());
            ulpt.setTradedt(vo.getTradedt());
            ulpt.setTradetype(vo.getTradetype());
            ulpt.setNav(vo.getNav());
            ulpt.setAckamt(vo.getAckamt());
            ulpt.setAckvol(vo.getAckvol());
            ulpt.setIsmessage(vo.getIsmessage());
            ulpt.setIshaiwai(vo.getIshaiwai());
            ulpt.setDiscsummary(vo.getDiscsummary());
            ulpt.setModifier(userlogin.getUserId());
            ulpt.setAuditstatus(StaticVar.ZX_TRADE_AUDIT_WAIT_CHECK);
            ulpt.setTransferprice(vo.getTransferPrice());
            ulpt.setDividendMode(vo.getDividendMode());
            privatefundtradeAudits.add(ulpt);
        }
        return privatefundtradeAudits;
    }

    /**
     * @description: 检测上传excel的备注
     * @param privateTradeList	上传的excel数据
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2023/9/13 15:09
     * @since JDK 1.8
    */
    private String checkRemark(List<UpLoadPrivateTrade> privateTradeList) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < privateTradeList.size(); i++) {
            UpLoadPrivateTrade upLoadPrivateTrade = privateTradeList.get(i);
            String tradeType = upLoadPrivateTrade.getTradetype();
            String discSummary = upLoadPrivateTrade.getDiscsummary();

            boolean hasGqLabel = false;
            if (tradeType.equals(StaticVar.LOAD_TRADETYPE_FJYZC) || tradeType.equals(StaticVar.LOAD_TRADETYPE_FJXZR)
                    || tradeType.equals(StaticVar.LOAD_TRADETYPE_DUMP) || tradeType.equals(StaticVar.LOAD_TRADETYPE_QXTZ)
                    || tradeType.equals(StaticVar.LOAD_TRADETYPE_QZSH)) {
                // 根据产品代码查询对应的产品类型
                hasGqLabel = productManagerService.hasGqlabel(upLoadPrivateTrade.getFundcode());
            }

            // 交易类型为强赎-强减-强增-  产品类型为股权的时候- ----- 备注必填
            if (Boolean.TRUE.equals(hasGqLabel) && StringUtil.isNullStr(discSummary)) {
                result.append("股权产品建议使用【非交易过户】【基金清盘】交易类型处理，如需使用【").append(TradeType.getTypeNameByValue(tradeType))
                        .append("】类型,请备注上写明原因").append(".</br>");
            } else if (Boolean.TRUE.equals(hasGqLabel) && StringUtil.isNotNullStr(discSummary)) {
                result.append("股权产品建议使用【非交易过户】【基金清盘】交易类型处理，当前使用【").append(TradeType.getTypeNameByValue(tradeType))
                        .append("】类型，请确认").append(".</br>");
            }
        }
        return result.toString();
    }


    /**
     * 校验客户是否存在
     * @param privateTradeList
     * @return
     */
    private String judgeCustomIsExsit(List<UpLoadPrivateTrade> privateTradeList) {
        if (CollectionUtils.isEmpty(privateTradeList)) {
            return null;
        }

        StringBuilder result = new StringBuilder();
        List<String> list = privateTradeList.stream()
                .map(UpLoadPrivateTrade::getConscustno)
                .collect(Collectors.toList());

        DecimalFormat df = new DecimalFormat("0");
        // 6位-香港客户号 10位-投顾客户号
        Map<Integer, List<String>> map = privateTradeList.stream()
                .map(UpLoadPrivateTrade::getConscustno)
                .filter(StringUtil::isNotNullStr)
                .map(v -> df.format(Double.valueOf(v)))
                .collect(Collectors.groupingBy(String::length));

        List<String> tgList = map.getOrDefault(10, new ArrayList<>());
        List<String> hkList = map.getOrDefault(7, new ArrayList<>());
        List<String> mergedList = Stream.concat(tgList.stream(), hkList.stream())
                .collect(Collectors.toList());
        List<String> noList = (List<String>) CollectionUtils.subtract(list, mergedList);
        List<CustAcctAttrVo> custNos = custService.getKeyAcctInfoByCustNos(tgList);
        List<CustAcctAttrVo> hkAcctNos = custService.getAcctInfoByHkAcctNos(hkList);
        if (CollectionUtils.isNotEmpty(custNos)) {
            List<String> custNoList = custNos.stream().map(CustAcctAttrVo::getCustNo).distinct().collect(Collectors.toList());
            for (String consCustNo : tgList) {
                if (!custNoList.contains(consCustNo)) {
                    noList.add(consCustNo);
                }
            }
        } else {
            noList.addAll(tgList);
        }
        if (CollectionUtils.isNotEmpty(hkAcctNos)) {
            List<String> hkAcctNoList = hkAcctNos.stream().map(CustAcctAttrVo::getHkTxAcctNo).distinct().collect(Collectors.toList());
            for (String hkCustNo : hkList) {
                if (!hkAcctNoList.contains(hkCustNo)) {
                    noList.add(hkCustNo);
                }
            }

        } else {
            noList.addAll(hkList);
        }

        if (CollectionUtils.isEmpty(noList)) {
            return null;
        }

        for (String custNo : noList) {
            result.append("客户号").append(custNo).append("：").append("未匹配到客户.</br>");
        }
        return result.toString();
    }

    /**
     * @description: 校验上传的excel数据的完整性
     * @param privateTradeList	上传的excel数据
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2023/9/13 15:10
     * @since JDK 1.8
    */
    private String judgeDataIntegrity(List<UpLoadPrivateTrade> privateTradeList) {
        StringBuilder result = new StringBuilder();
        for (UpLoadPrivateTrade upLoadPrivateTrade : privateTradeList) {
            // 对象完整性处理
            String errorMsg = checkInfo(upLoadPrivateTrade);

            if (StringUtil.isNotNullStr(errorMsg)) {
                result.append(errorMsg).append(".</br>");
            }
        }
        return result.toString();
    }

    /**
     * @description:校验直销交易记录数据的完整性
     * @param vo 直销交易记录
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2023/9/13 15:11
     * @since JDK 1.8
    */
    private String checkInfo(UpLoadPrivateTrade vo) {
        StringBuilder result = new StringBuilder();
        //判断客户姓名必填项
        if (StringUtil.isNullStr(vo.getCustname())) {
            addmesg(result, vo, "客户姓名必填");
        }
        //判断产品名称必填项
        if (StringUtil.isNullStr(vo.getFundname())) {
            addmesg(result, vo, "产品名称必填");
        }
        //判断产品代码必填项
        if (StringUtil.isNullStr(vo.getFundcode())) {
            addmesg(result, vo, "产品代码必填");
        }

        //前置校验已经校验 客户号不为空
        //拦截香港产品
        String archType =prebookBasicInfoService.getArchType(vo.getConscustno(),vo.getFundcode());
        if(PreBookArchTypeEnum.HW.getCode().equals(archType)){
            addmesg(result, vo, String.format("产品代码：%s,不支持海外产品操作",vo.getFundcode()));
        }

        //判断确认日期必填项
        if (StringUtil.isNullStr(vo.getTradedt())) {
            addmesg(result, vo, "确认日期必填");
        }
        //判断确认日期格式是否正确
        if (StringUtil.isNotNullStr(vo.getTradedt()) && vo.getTradedt().length() != DATE_LENGTH) {
            addmesg(result, vo, "日期格式不正确");
        }
        //判断净值必填项
        if (vo.getNav() == null) {
            addmesg(result, vo, "净值必填");
        }
        // 净值保留8位小数
        if (vo.getNav() != null) {
            vo.setNav(vo.getNav().setScale(8, RoundingMode.HALF_UP));
        }
        //判断确认金额必填项
        if (vo.getAckamt() == null) {
            addmesg(result, vo, "确认金额必填");
        }
        // 确认金额保留2位小数
        if (vo.getAckamt() != null) {
            vo.setAckamt(vo.getAckamt().setScale(2, RoundingMode.HALF_UP));
        }
        //判断确认份额必填项
        if (vo.getAckvol() == null) {
            addmesg(result, vo, "确认份额必填");
        }
        if (vo.getAckvol() != null) {
            vo.setAckvol(vo.getAckvol().setScale(6, RoundingMode.HALF_UP));
        }

        if (StringUtil.isNullStr(vo.getIshaiwai())) {
            addmesg(result, vo, "是否海外特殊处理必填");
        }

        if (StringUtil.isNotNullStr(vo.getIshaiwai()) && !StaticVar.CHINESE_YES.equals(vo.getIshaiwai()) && !StaticVar.CHINESE_NO.equals(vo.getIshaiwai())) {
            addmesg(result, vo, "是否海外特殊处理只能填是和否");
        }
        // 判断交易类型,从而判断是否要获取该用户的任持有的产品类型里面有股权类型
        String tradetype = vo.getTradetype();
        boolean hasGqLabel = false;
        if (tradetype.equals(StaticVar.LOAD_TRADETYPE_FJYZC) || tradetype.equals(StaticVar.LOAD_TRADETYPE_FJXZR)
                || tradetype.equals(StaticVar.LOAD_TRADETYPE_DUMP)
                || tradetype.equals(StaticVar.LOAD_TRADETYPE_QXTZ) || tradetype.equals(StaticVar.LOAD_TRADETYPE_QZSH)) {
            // 根据产品代码查询对应的产品类型
            hasGqLabel = productManagerService.hasGqlabel(vo.getFundcode());
        }

        // 判断当 交易类型为非过户转出，非交易过户转入  产品类型为股权 时， 转让价格必填
        if (Boolean.TRUE.equals(hasGqLabel) && StringUtil.isNull(vo.getTransferPrice())) {
            addmesg(result, vo, "转让价格必填");
        }
        // 转让价格保留2位小数
        if (vo.getTransferPrice() != null) {
            vo.setTransferPrice(vo.getTransferPrice().setScale(2, RoundingMode.HALF_UP));
        }

        //是否短信处理
        messageDeal(vo, result);
        // 持仓2.2 修改  备注 最多可以输入30字符
        if (StringUtil.isNotNullStr(vo.getDiscsummary()) && vo.getDiscsummary().length() > DISCSUMMARY_MAX_LENGTH) {
            addmesg(result, vo, "备注不能超过30个字");
        }

        // 分红方式
        judgeDividendMode(vo, result);

        if (StringUtil.isNotNullStr(vo.getFundcode()) && vo.getAckvol() != null) {
            ReturnMessageDto<Void> checktradeResult = custprivatefundService.checktrade(vo);
            if (!checktradeResult.isSuccess()) {
                addmesg(result, vo, checktradeResult.getReturnMsg());
            }
        }

        return result.toString();
    }

    /**
     * @description:判断分红方式字段的合法性
     * @param vo 直销交易记录
     * @param result 错误记录
     * @return void
     * @author: jin.wang03
     * @date: 2023/9/13 15:12
     * @since JDK 1.8
    */
    private void judgeDividendMode(UpLoadPrivateTrade vo, StringBuilder result) {
        /**
         * 对于交易类型为“分红”时：
         * 若模版中所填的【分红方式】= “红利再投”
         *  a）判断是否【确认份额】> 0，如否，则报错提示：客户{【投顾客户号】}：分红方式为红利再投，未填写确认份额！
         *  b）判断是否【确认金额】= 0，如否，则报错提示：客户{【投顾客户号】}：分红方式为红利再投，无需写确认金额！
         *  c）以上两个校验任一不满足，报错提示且数据不导入，若都不满足，则合并报错提示；如都满足，校验通过
         * 若模版中所填的【分红方式】= “现金分红”
         *  a）判断是否【确认金额】> 0，如否，则报错提示：客户{【投顾客户号】}：分红方式为现金分红，未填写确认金额！
         *  b）判断是否【确认份额】= 0，如否，则报错提示：客户{【投顾客户号】}：分红方式为现金分红，无需写确认份额！
         *  c）以上两个校验任一不满足，报错提示且数据不导入，若都不满足，则合并报错提示；如都满足，校验通过
         */
        String tradeType = vo.getTradetype();
        if (StaticVar.LOAD_TRADETYPE_DIV.equals(tradeType)) {
            String dividendMode = vo.getDividendMode();
            if (StringUtil.isNullStr(dividendMode)) {
                addmesg(result, vo, "交易类型为“分红”，【分红方式】字段必填！");
            } else {
                String str = "";
                if (DivModeEnum.DIV_MODE_VOL.getName().equals(dividendMode)) {
                    if (vo.getAckvol() != null && vo.getAckvol().compareTo(Util.ObjectToBigDecimalNull(ZERO)) < 0) {
                        str += "确认份额不能为负数";
                    }

                    if (vo.getAckvol() != null && vo.getAckvol().compareTo(Util.ObjectToBigDecimalNull(ZERO)) == 0) {
                        str += "未填写确认份额";
                    }

                    if (vo.getAckamt() != null && vo.getAckamt().compareTo(Util.ObjectToBigDecimalNull(ZERO)) != 0) {
                        str = str.isEmpty() ? "无需填写确认金额" : str + "且无需填写确认金额";
                    }

                }
                if (DivModeEnum.DIV_MODE_AMT.getName().equals(dividendMode)) {
                    if (vo.getAckamt() != null && vo.getAckamt().compareTo(Util.ObjectToBigDecimalNull(ZERO)) < 0) {
                        str += "确认金额不能为负数";
                    }
                    if (vo.getAckamt() != null && vo.getAckamt().compareTo(Util.ObjectToBigDecimalNull(ZERO)) == 0) {
                        str += "未填写确认金额";
                    }
                    if (vo.getAckvol() != null && vo.getAckvol().compareTo(Util.ObjectToBigDecimalNull(ZERO)) != 0) {
                        str = str.isEmpty() ? "无需填写确认份额" : str + "且无需填写确认份额";
                    }

                }
                if (!str.isEmpty()) {
                    addmesg(result, vo, "分红方式为【" + dividendMode + "】，" + str);
                }
            }
        } else {
            vo.setDividendMode(null);
        }
    }

    /**
     * @description:判断投顾客户号不能为空
     * @param privateTradeList	上传的excel数据
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2023/9/13 15:13
     * @since JDK 1.8
    */
    private String judgeVoidConScustNo(List<UpLoadPrivateTrade> privateTradeList) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < privateTradeList.size(); i++) {
            UpLoadPrivateTrade upLoadPrivateTrade = privateTradeList.get(i);
            if (StringUtil.isNullStr(upLoadPrivateTrade.getConscustno())) {
                result.append(String.format("第%d行的客户号为空！", i + 2)).append("\n");
            }
        }
        return result.toString();
    }

    /**
     * @description: 将上传的excel数据解析为 直销交易记录DTO
     * @param workBook	exccel对象
     * @param tradeType	交易类型
     * @return java.util.List<com.howbuy.crm.hb.domain.manage.UpLoadPrivateTrade>
     * @author: jin.wang03
     * @date: 2023/9/13 15:13
     * @since JDK 1.8
    */
    private static List<UpLoadPrivateTrade> getUpLoadPrivateTradeList(Workbook workBook, String tradeType) {
        List<UpLoadPrivateTrade> privateTradeList = new ArrayList<>();
        for (Sheet sheet : workBook.getSheets()) {
            //在第二行开始遍历
            for (int i = 1; i < sheet.getRows(); i++) {
                UpLoadPrivateTrade vo = new UpLoadPrivateTrade();
                vo.setTradetype(tradeType);
                vo.setCustname(ExcelReader.getValue(sheet.getCell(0, i)).trim());
                vo.setConscustno(ExcelReader.getValue(sheet.getCell(1, i)).trim());
                vo.setFundname(ExcelReader.getValue(sheet.getCell(2, i)).trim());
                vo.setFundcode(ExcelReader.getValue(sheet.getCell(3, i)).trim());
                vo.setTradedt(ExcelReader.getValue(sheet.getCell(4, i)).trim());

                String nav = "";
                if (sheet.getCell(5, i).getType() == CellType.NUMBER) {
                    double numericValue = ((NumberCell) sheet.getCell(5, i)).getValue();
                    nav = String.valueOf(numericValue);
                } else {
                    nav = ExcelReader.getValue(sheet.getCell(5, i)).trim();
                }
                vo.setNav(Util.ObjectToBigDecimalNull(nav));


                String ackamt = "";
                if (sheet.getCell(6, i).getType() == CellType.NUMBER) {
                    double numericValue = ((NumberCell) sheet.getCell(6, i)).getValue();
                    ackamt = String.valueOf(numericValue);
                } else {
                    ackamt = ExcelReader.getValue(sheet.getCell(6, i)).trim();
                }
                vo.setAckamt(Util.ObjectToBigDecimalNull(ackamt));


                // 确认份额精度处理
                String ackvol = "";
                if (sheet.getCell(7, i).getType() == CellType.NUMBER) {
                    double numericValue = ((NumberCell) sheet.getCell(7, i)).getValue();
                    ackvol = String.valueOf(numericValue);
                } else {
                    ackvol = ExcelReader.getValue(sheet.getCell(7, i)).trim();
                }
                vo.setAckvol(Util.ObjectToBigDecimalNull(ackvol));
                // 分红方式
                vo.setDividendMode(ExcelReader.getValue(sheet.getCell(8, i)).trim());

                String transferPrice = "";
                if (sheet.getCell(9, i).getType() == CellType.NUMBER) {
                    double numericValue = ((NumberCell) sheet.getCell(9, i)).getValue();
                    transferPrice = String.valueOf(numericValue);
                } else {
                    transferPrice = ExcelReader.getValue(sheet.getCell(9, i)).trim();
                }
                vo.setTransferPrice(Util.ObjectToBigDecimalNull(transferPrice));

                vo.setIshaiwai(ExcelReader.getValue(sheet.getCell(10, i)).trim());
                vo.setIsmessage(ExcelReader.getValue(sheet.getCell(11, i)).trim());
                vo.setDiscsummary(ExcelReader.getValue(sheet.getCell(12, i)).trim());
                privateTradeList.add(vo);
            }
        }
        return privateTradeList;
    }


    /**
	 * @api {POST} /prosale/confirmupload.do confirmedUpload()
	 * @apiVersion 1.0.0
	 * @apiGroup UpLoadZxPrivateTradeController
	 * @apiName confirmedUpload()
	 * @apiDescription 确认操作接口
	 * @apiSuccess (响应结果) {Object} response
	 * @apiSuccessExample 响应结果示例
	 * {}
	 */
	@RequestMapping(value = "/confirmupload.do", method = RequestMethod.POST)
	public @ResponseBody Map<String, Object> confirmedUpload(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<>(2);
        User userlogin = (User) request.getSession().getAttribute("loginUser");
		//批量插入直销交易待审核表
		try {
            List<CmPrivatefundtradeAudit> privatefundtradeAudits = userIdAndConfirmedAuditMap.get(userlogin.getUserId());
            userIdAndConfirmedAuditMap.remove(userlogin.getUserId());
            cmPrivatefundtradeAuditService.batchInsertCmPrivatefundtradeAudit(privatefundtradeAudits);
			resultMap.put("uploadFlag", "success");
		} catch (Exception e) {
			resultMap.put("uploadFlag", ERROR);
			resultMap.put(ERROR_MSG, e.getMessage());
		}
		return resultMap;
	}

    /**
     * 是否短信处理
     *
     * @param vo
     * @param sb1
     */
    private void messageDeal(UpLoadPrivateTrade vo, StringBuilder sb1) {
        //是否发送短信 交易类别 = 私募股权回款/分红时，必填
        if (StaticVar.LOAD_TRADETYPE_RETURN.equals(vo.getTradetype()) || StaticVar.LOAD_TRADETYPE_DIV.equals(vo.getTradetype())) {
            if (StringUtil.isNullStr(vo.getIsmessage())) {
                addmesg(sb1, vo, "是否发送短信必填");
            }
            if (StringUtil.isNotNullStr(vo.getIsmessage()) && !StaticVar.CHINESE_YES.equals(vo.getIsmessage()) && !StaticVar.CHINESE_NO.equals(vo.getIsmessage())) {
                addmesg(sb1, vo, "是否发送短信只能填是和否");
            }
        } else {
            vo.setIsmessage(null);
        }
    }

    private void addmesg(StringBuilder sb1, UpLoadPrivateTrade vo, String msg) {
        if (StringUtil.isNotNullStr(sb1.toString())) {
            sb1.append("、").append(msg);
        } else {
            String conscustno = vo.getConscustno();
            sb1.append("客户号").append(conscustno).append("：").append(msg);
        }
    }

    @RequestMapping("/downloadMode.do")
    public String downloadModel(HttpServletRequest request,
                                HttpServletResponse response) {
        return dowmloadTemplate(MODEL_FILE_NAME,DOWNLOAD_FILE_NAME,request,response);
    }

    @RequestMapping("/downloadModeInfo.do")
    public String downloadModelInfo(HttpServletRequest request,
                                    HttpServletResponse response) {
        return dowmloadTemplate(MODEL_FILE_NAME_INFO,DOWNLOAD_FILE_NAME_INFO,request,response);
    }

}
