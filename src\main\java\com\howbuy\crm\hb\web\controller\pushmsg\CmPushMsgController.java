package com.howbuy.crm.hb.web.controller.pushmsg;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.base.YesOrNoEnum;
import com.howbuy.crm.conscust.request.ConsCustListRequest;
import com.howbuy.crm.conscust.response.ConsultantAndOrgResponse;
import com.howbuy.crm.consultant.dto.ConsultantAndOrgInfoDTO;
import com.howbuy.crm.consultant.service.QueryConsultantInfoService;
import com.howbuy.crm.hb.domain.custinfo.CmConsultant;
import com.howbuy.crm.hb.domain.pushmsg.CmMsgType;
import com.howbuy.crm.hb.domain.pushmsg.CmPushMsg;
import com.howbuy.crm.hb.domain.pushmsg.CmPushMsgAnnex;
import com.howbuy.crm.hb.domain.pushmsg.CmWechatPushTask;
import com.howbuy.crm.hb.domain.system.HbOrganization;
import com.howbuy.crm.hb.domain.system.HbRole;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.pushmsg.CmMsgTypeService;
import com.howbuy.crm.hb.service.pushmsg.CmPushMsgService;
import com.howbuy.crm.hb.service.pushmsg.CmWechatPushTaskService;
import com.howbuy.crm.hb.service.system.HbOrganizationService;
import com.howbuy.crm.hb.service.system.HbUserroleService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.pushmsg.CmPushMsgVO;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping("/pushmsg")
public class CmPushMsgController extends BaseController {

    @Autowired
    private HbOrganizationService hbOrganizationService;

    @Autowired
    private HbUserroleService hbUserRoleService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private CmPushMsgService cmPushMsgService;

    @Autowired
    private CmMsgTypeService cmMsgTypeService;

    @Autowired
    private CmMsgBlackController cmMsgBlackController;
    @Autowired
    private QueryConsultantInfoService queryConsultantInfoService;
    @Autowired
    private CmWechatPushTaskService cmWechatPushTaskService;

    public static final String 	ORGANIZATION_ROOT = "0"; //表示组织架构根节点

    /**
     * 消息 任务类型 ：1- 企微任务
     */
    private static final String WECHAT_TASK="1";

    /**
     * 消息 任务类型 ：2- CARD
     */
    private static final String CARD_TASK="2";

    @Value("${WECHAT_WEB_URL}")
    private String wechatTaskWebUrl;

    @RequestMapping("/pushmsgindex")
    public String pushMsgIndex(HttpServletRequest request){
        request.setAttribute("wechatTaskWebUrl", wechatTaskWebUrl);
        return "pushmsg/pushMsgIndex";
    }

    @RequestMapping("/querypushmsg")
    public String querypushmsg(Map map){
    	List<Map<String,Object>> cmMsgTypeList = new ArrayList<>();
    	Map<String, Object> cmMsgTypemap = new HashMap<>(2);
    	cmMsgTypemap.put("id", "");
    	cmMsgTypemap.put("text", "请选择");
    	cmMsgTypeList.add(cmMsgTypemap);
    	List<CmMsgType> list = cmMsgTypeService.listAllSubType();
    	if(CollectionUtils.isNotEmpty(list)){
    		for(CmMsgType cmMsgType : list){
    			if(cmMsgType != null && YesOrNoEnum.YES.getCode().equals(cmMsgType.getIsEnable())){
    				cmMsgTypemap = new HashMap<>(2);
        	    	cmMsgTypemap.put("id", cmMsgType.getMsgTypeCode());
        	    	cmMsgTypemap.put("text", cmMsgType.getMsgTypeName());
        	    	cmMsgTypeList.add(cmMsgTypemap);
    			}
    		}
    	}
    	
    	map.put("cmMsgTypeList",cmMsgTypeList);
        return "pushmsg/queryPushMsg";
    }

    /**
     * 组织架构页面的左边树形菜单的生成方法
     * @throws Exception
     */
    @RequestMapping("/org_tree.do")
    public void getorgtree(HttpServletRequest request, HttpServletResponse response) throws Exception{

        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        param.put("orgcode", ORGANIZATION_ROOT);	//表示组织架构根节点
        HbOrganization getHbOrganizationRoot = 	hbOrganizationService.getHbOrganization(param);

        param = new HashMap<String, String>();
        param.put("status", "0");
        List<HbOrganization> listHbOrganization = hbOrganizationService.listHbOrganizationTree(param);

        param = new HashMap<String, String>();
        param.put("status", "0");
        List<HbOrganization> listHbOrganizationParentCode = hbOrganizationService.listHbOrganizationParentCode(param);

        String zNodes="";
        List<String> ListParentId = new ArrayList<String>();
        if (listHbOrganizationParentCode != null && listHbOrganizationParentCode.size() > 0) {
            HbOrganization hbOrganization = null;
            for (int i = 0; i < listHbOrganizationParentCode.size(); i++) {
                hbOrganization = (HbOrganization) listHbOrganizationParentCode.get(i);
                ListParentId.add(hbOrganization.getParentorgcode());
            }
        }

        if(getHbOrganizationRoot!=null)  //type=0:根节点，组织架构跟节点：howbuy；type=1:父节点（有子节点的）；type=2:叶子节点（没有子节点）
        {
            if(listHbOrganizationParentCode==null)
            {    //组织架构，没有任何内容，只有一个根节点
                zNodes +="{id:'"+getHbOrganizationRoot.getOrgcode()+"',pId:'-1',name:'"+getHbOrganizationRoot.getOrgname()+"',orgtype:'0',type:'0',font:{'font-weight':'bold'},isParent:false,open:false,nocheck:true},";
            }else
            {    //组织架构，已经有了子部门，最顶部的根节点展开
                zNodes +="{id:'"+getHbOrganizationRoot.getOrgcode()+"',pId:'-1',name:'"+getHbOrganizationRoot.getOrgname()+"',orgtype:'0',type:'0',isParent:true,open:true,nocheck:true},";
            }

            if (listHbOrganization != null && listHbOrganization.size() > 0) {
                HbOrganization hbOrganizationTree = null;
                String orgtype ="";
                String orgname ="";
                for (int i = 0; i < listHbOrganization.size(); i++) {
                    hbOrganizationTree = (HbOrganization) listHbOrganization.get(i);
                    orgtype= hbOrganizationTree.getOrgtype();
                    orgname = hbOrganizationTree.getOrgname();
                    if("1".equals(orgtype))
                    {
                        orgname = hbOrganizationTree.getOrgname()+"(团)"	;
                    }
                    if(ListParentId!=null&&ListParentId.size()>0&&ListParentId.contains(hbOrganizationTree.getOrgcode())) //表示父节点下面有子节点
                    {
                        zNodes +="{id:'"+hbOrganizationTree.getOrgcode()+"',pId:'"+hbOrganizationTree.getParentorgcode()+"',name:'"+orgname+"',englishname:'"+hbOrganizationTree.getEnglishname()+"',showflag:'"+hbOrganizationTree.getShowflag()+"',orgtype:'"+hbOrganizationTree.getOrgtype()+"',type:'1',isParent:true},";
                    }else //表示该节点下面没有子节点，自己为叶子节点
                    {
                        zNodes +="{id:'"+hbOrganizationTree.getOrgcode()+"',pId:'"+hbOrganizationTree.getParentorgcode()+"',name:'"+orgname+"',englishname:'"+hbOrganizationTree.getEnglishname()+"',showflag:'"+hbOrganizationTree.getShowflag()+"',orgtype:'"+hbOrganizationTree.getOrgtype()+"',type:'2',isParent:false},";
                    }
                }
            }

            zNodes = zNodes.substring(0, zNodes.length()-1);

        }

        String jsonZnodes="["+zNodes+"]";
        response.setContentType("text/plain; charset=UTF-8");
        response.getOutputStream().write(jsonZnodes.getBytes("UTF-8"));
    }

    /**
     * 组织架构页面的左边树形菜单的生成方法
     * @throws Exception
     */
    @RequestMapping("/role_tree.do")
    public void getroletree(HttpServletRequest request, HttpServletResponse response) throws Exception{

        String qrolename = request.getParameter("rolename");
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        param.put("userId", "aaaaaaaaaaaaa");

        if(StringUtils.isNotBlank(qrolename)){
            param.put("rolename", qrolename);
        }
        // 调用获取异常次数方法
        List<HbRole> roleList = hbUserRoleService.listHbRoleAndUser(param);
        String zNodes="";

        if(roleList!=null)  //type=0:根节点，组织架构跟节点：howbuy；type=1:父节点（有子节点的）；type=2:叶子节点（没有子节点）
        {

                //组织架构，已经有了子部门，最顶部的根节点展开
            zNodes +="{id:'role',pId:'-1',name:'角色',isParent:true,open:true,nocheck:true},";

            if (roleList != null && roleList.size() > 0) {
                HbRole hbrole = null;
                String rolecode ="";
                String rolename ="";
                for (int i = 0; i < roleList.size(); i++) {
                    hbrole = (HbRole) roleList.get(i);
                    rolecode= hbrole.getRolecode();
                    rolename = hbrole.getRolename();
                    zNodes +="{id:'"+rolecode+"',pId:'role',name:'"+rolename+"',englishname:'',type:'2',isParent:false},";
                }
            }
            zNodes = zNodes.substring(0, zNodes.length()-1);
        }

        String jsonZnodes="["+zNodes+"]";
        response.setContentType("text/plain; charset=UTF-8");
        response.getOutputStream().write(jsonZnodes.getBytes("UTF-8"));
    }

    @ResponseBody
    @RequestMapping("/queryconscodelist.do")
    public Map<String, Object> queryConsCodeList(HttpServletRequest request) throws Exception{
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();

        param = new ParamUtil(request).getParamMap();

        List<CmConsultant> cmConferencePage = hbOrganizationService.listConsCodeByConsCode(param);
        Map<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("total", cmConferencePage.size());
        resultMap.put("rows", cmConferencePage);
        return resultMap;
    }

    @ResponseBody
    @RequestMapping("/showconscodelist.do")
    public Map<String, Object> showConsCodeList(HttpServletRequest request) throws Exception{
        // 设置查询参数
        Map<String, List> param = new HashMap<String, List>();

        String[] allorgcode = request.getParameterValues("allorgcode[]");
        String[] allrolecode = request.getParameterValues("allrolecode[]");
        String[] allconscode = request.getParameterValues("allconscode[]");
        String[] allfundcode = request.getParameterValues("allfundcode[]");
        String[] allcompanycode = request.getParameterValues("allcompanycode[]");

        if(allorgcode != null && allorgcode.length > 0){
            List<String> orgcodelist = new ArrayList<String>();
            Collections.addAll(orgcodelist, allorgcode);
            param.put("orgcodelist",orgcodelist);
        }

        if(allrolecode != null && allrolecode.length > 0){
            List<String> rolecodelist = new ArrayList<String>();
            Collections.addAll(rolecodelist, allrolecode);
            param.put("rolecodelist",rolecodelist);
        }

        if(allconscode != null && allconscode.length > 0){
            List<String> conscodelist = new ArrayList<String>();
            Collections.addAll(conscodelist, allconscode);
            param.put("conscodelist",conscodelist);
        }

        if(allfundcode != null && allfundcode.length > 0){
            List<String> fundcodelist = new ArrayList<String>();
            Collections.addAll(fundcodelist, allfundcode);
            param.put("fundcodelist",fundcodelist);
        }

        if(allcompanycode != null && allcompanycode.length > 0){
            List<String> companycodelist = new ArrayList<String>();
            Collections.addAll(companycodelist, allcompanycode);
            param.put("companycodelist",companycodelist);
        }

        List<CmConsultant> cmConferencePage = hbOrganizationService.showConsCodeList(param);
        Map<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("total", cmConferencePage.size());
        resultMap.put("rows", cmConferencePage);
        return resultMap;
    }

    @ResponseBody
    @RequestMapping("/savePushMsg.do")
    public String savePushMsg(HttpServletRequest request)  {
        String userId =getLoginUserId();

        String result = "error";

        Map<String, List> param = new HashMap<String, List>();
        String[] allorgcode = request.getParameterValues("allorgcode[]");
        String[] allrolecode = request.getParameterValues("allrolecode[]");
        String[] allconscode = request.getParameterValues("allconscode[]");
        String[] allfundcode = request.getParameterValues("allfundcode[]");
        String[] allcompanycode = request.getParameterValues("allcompanycode[]");
        //消息通道 1PC   2企业微信
        String[] msgchannel = request.getParameterValues("msgchannel[]");

        String msgtitle = request.getParameter("msgtitle");
        String msgtype = request.getParameter("msgtype");
        String msgcontant = request.getParameter("msgcontant");
        //如果 输入链接。 默认发送 卡片 消息任务 。 同  tab【自定义发送-企微任务】
        String hrefUrl =request.getParameter("hrefUrl");
        String msg_timer = request.getParameter("msg_timer");

        //预计推送日期
        final Date  expectPushDt=
                StringUtil.isNotNullStr(msg_timer)
                        ?DateTimeUtil.strToDate(msg_timer, "yyyy/MM/dd HH:mm:ss")
                        :new Date();

        if(allorgcode != null && allorgcode.length > 0){
            List<String> orgcodelist = new ArrayList<String>();
            Collections.addAll(orgcodelist, allorgcode);
            param.put("orgcodelist",orgcodelist);
        }

        if(allrolecode != null && allrolecode.length > 0){
            List<String> rolecodelist = new ArrayList<String>();
            Collections.addAll(rolecodelist, allrolecode);
            param.put("rolecodelist",rolecodelist);
        }

        if(allconscode != null && allconscode.length > 0){
            List<String> conscodelist = new ArrayList<String>();
            Collections.addAll(conscodelist, allconscode);
            param.put("conscodelist",conscodelist);
        }

        if(allfundcode != null && allfundcode.length > 0){
            List<String> fundcodelist = new ArrayList<String>();
            Collections.addAll(fundcodelist, allfundcode);
            param.put("fundcodelist",fundcodelist);
        }

        if(allcompanycode != null && allcompanycode.length > 0){
            List<String> companycodelist = new ArrayList<String>();
            Collections.addAll(companycodelist, allcompanycode);
            param.put("companycodelist",companycodelist);
        }
        List<CmConsultant> consultantlist = hbOrganizationService.showConsCodeList(param);
        if(CollectionUtils.isEmpty(consultantlist)){
            return "nocount";
        }

      //如果 链接不为空， 默认发送 卡片 消息任务 。 同  tab【自定义发送-企微任务】
        boolean isCardMsg=StringUtil.isNotNullStr(hrefUrl);
        //消息通道 1PC   2企业微信
        boolean containsWechatChannel= Arrays.asList(msgchannel).contains("2");
        if(containsWechatChannel && isCardMsg){
            CmPushMsgVO cmPushMsgVO=new CmPushMsgVO();
            cmPushMsgVO.setMsgType(msgtype);
            //固定 推送任务： 卡片类型任务
            cmPushMsgVO.setPushType("2");
            cmPushMsgVO.setDetailList(Lists.newArrayList());

            CmPushMsgVO.CardMessageDTO cardMessageDTO = new CmPushMsgVO.CardMessageDTO();
            cardMessageDTO.setContent(msgcontant);
            cardMessageDTO.setHrefUrl(hrefUrl);
            cardMessageDTO.setTitle(msgtitle);
            cmPushMsgVO.setCardMessageDTO(cardMessageDTO);

            List<CmPushMsg> list = Lists.newArrayList();
            //保存企微任务
            String taskId = saveWechatTask(cmPushMsgVO);
            log.info("taskId:{}, detailSize:{}", taskId, cmPushMsgVO.getDetailList() == null ? 0 : cmPushMsgVO.getDetailList().size());
            //构建明细
            consultantlist.forEach(cmConsultant -> {
                CmPushMsg cmPushMsg = new CmPushMsg();
                cmPushMsg.setPushid(commonService.getSeqValue(SEQ_PUSH_MSG));
                cmPushMsg.setTaskType(CARD_TASK);
                //客户号  空
                cmPushMsg.setConscustNo(null);
                cmPushMsg.setConscode(cmConsultant.getConscode());
                cmPushMsg.setOrgcode(cmConsultant.getOutletcode());
                cmPushMsg.setMsgtype(cmPushMsgVO.getMsgType());
                cmPushMsg.setMsgcontent(msgcontant);
                cmPushMsg.setTitle(msgtitle);
                //企业微信
                cmPushMsg.setMsgchannel("2");
                //人工
                cmPushMsg.setMsgstyle("1");
                cmPushMsg.setCreator(userId);
                cmPushMsg.setCredt(new Date());
                cmPushMsg.setExpectpushdt(expectPushDt);
                cmPushMsg.setTaskId(taskId);
                //卡片信息url -->   msgLink
                cmPushMsg.setMsgLink(hrefUrl);
                list.add(cmPushMsg);
            });
            cmPushMsgService.insertCmPushMsg(list);
        }

        List<CmPushMsg> pushmsglist=Lists.newArrayList();
        //如果 链接为空，默认历史 发送逻辑：
        consultantlist.forEach(consultant -> {
            for (String channel : msgchannel) {
                //配置了链接的 企微消息 。  走 企微任务发送
                if("2".equals(channel) && isCardMsg){
                    continue;
                }
                CmPushMsg pushmsg = new CmPushMsg();
                String id = commonService.getSeqValue(SEQ_PUSH_MSG);
                pushmsg.setPushid(id);
                pushmsg.setTitle(msgtitle);
                pushmsg.setMsgtype(msgtype);
                pushmsg.setMsgcontent(msgcontant);
                pushmsg.setConscode(consultant.getConscode());
                pushmsg.setOrgcode(consultant.getOutletcode());
                pushmsg.setCreator(userId);
                pushmsg.setMsgchannel(channel);
                pushmsg.setMsgstyle("1");
                pushmsg.setExpectpushdt(expectPushDt);
                pushmsg.setMsgLink(hrefUrl);
                pushmsglist.add(pushmsg);
            }
        });
        cmPushMsgService.insertCmPushMsg(pushmsglist);

        return  "success";
    }

    @RequestMapping("/viewAnnex")
    public String viewAnnex(HttpServletRequest request){

        String pushid = request.getParameter("pushid");
        CmPushMsgAnnex pushMsgAnnex = cmPushMsgService.getCmPushMsgAnnexTitle(pushid);

        request.setAttribute("pushMsgAnnex",pushMsgAnnex);
        return "pushmsg/viewAnnex";
    }

    @ResponseBody
    @RequestMapping("/queryViewAnnex")
    public Map<String, Object> queryViewAnnexList(HttpServletRequest request) throws Exception{
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();

        PageData<CmPushMsgAnnex> cmConferencePage = cmPushMsgService.listCmPushMsgAnnexByPage(param);
        Map<String, Object> resultMap = new HashMap<String, Object>();

        resultMap.put("total", cmConferencePage.getPageBean().getTotalNum());
        resultMap.put("rows", cmConferencePage.getListData());
        return resultMap;
    }
    
    @RequestMapping("/listpushmsg")
    public String listpushmsg(HttpServletRequest request) {

        // 设置查询参数
        List<CmMsgType> listbx = cmMsgTypeService.listMasterTypeAll();
        List<CmMsgType> msgtypelist = cmMsgBlackController.getCmMsgTypeList();
        for(CmMsgType msgtype:listbx){
            msgtypelist.add(msgtype);
        }

        List<CmMsgType> listsubbx = cmMsgTypeService.listAllSubType();
        List<CmMsgType> msgsubtypelist = cmMsgBlackController.getCmMsgTypeList();
        for(CmMsgType msgsubtype:listsubbx){
            msgsubtypelist.add(msgsubtype);
        }

        request.setAttribute("msgtypelist",msgtypelist);
        request.setAttribute("msgsubtypelist",msgsubtypelist);
        return "pushmsg/listpushmsg";
    }
    
    /**
	 * 加载页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listpushmagByPage_json.do")
	public Map<String, Object> listpushmagByPage_json(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String consname = request.getParameter("consname");
		String title = request.getParameter("title");
		String msgtype = request.getParameter("msgtype");
        String submsgtype = request.getParameter("submsgtype");
        String pushmsgMode = request.getParameter("pushmsgMode");
        String pushmsgPass = request.getParameter("pushmsgPass");
		String creator = request.getParameter("creator");
		String content = request.getParameter("content");
		String pushflag = request.getParameter("pushflag");
		String sendBeginDt = request.getParameter("sendBeginDt");
		String sendEndDt = request.getParameter("sendEndDt");
        String conscust = request.getParameter("conscust");

		if (StringUtils.isNotBlank(consname)) {
			param.put("consname", consname);
		} else {
			param.put("consname", null);
		}

		if (StringUtils.isNotBlank(title)) {
			param.put("title", title);
		} else {
			param.put("title", null);
		}
		
		if (StringUtils.isNotBlank(msgtype)) {
			param.put("msgtype", msgtype);
		} else {
			param.put("msgtype", null);
		}
		
		if (StringUtils.isNotBlank(creator)) {
			param.put("creator", creator);
		} else {
			param.put("creator", null);
		}
		
		if (StringUtils.isNotBlank(content)) {
			param.put("content", content);
		} else {
			param.put("content", null);
		}
		
		if (StringUtils.isNotBlank(pushflag)) {
			param.put("pushflag", pushflag);
		} else {
			param.put("pushflag", null);
		}
		
		if (StringUtils.isNotBlank(sendBeginDt)) {
			param.put("sendBeginDt", sendBeginDt);
		} else {
			param.put("sendBeginDt", null);
		}
		
		if (StringUtils.isNotBlank(sendEndDt)) {
			param.put("sendEndDt", sendEndDt);
		} else {
			param.put("sendEndDt", null);
		}

        if (StringUtils.isNotBlank(submsgtype)) {
            param.put("submsgtype", submsgtype);
        } else {
            param.put("submsgtype", null);
        }

        if (StringUtils.isNotBlank(pushmsgMode)) {
            param.put("msgstyle", pushmsgMode);
        } else {
            param.put("msgstyle", null);
        }

        if (StringUtils.isNotBlank(pushmsgPass)) {
            param.put("msgchannel", pushmsgPass);
        } else {
            param.put("msgchannel", null);
        }
        if (StringUtils.isNotBlank(conscust)) {
            param.put("conscust", conscust);
        } else {
            param.put("conscust", null);
        }
		PageData<CmPushMsg> pageData = cmPushMsgService.listPushMsgByPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmPushMsg> listdata = pageData.getListData();
		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        ConstantCache constantCache = ConstantCache.getInstance();
        for (CmPushMsg info : listdata) {
            info.setMsgtypeval(cmPushMsgService.getMsgTypeName(info.getMsgtype()));
            info.setPushflagval(constantCache.getConstantKeyVal("pushmsgFlag").get(info.getPushflag()));
            info.setMsgchannelval(constantCache.getConstantKeyVal("pushmsgPass").get(info.getMsgchannel()));
            info.setMsgstyleval(constantCache.getConstantKeyVal("pushmsgMode").get(info.getMsgstyle()));
            if(StringUtil.isNotNullStr(info.getConscode())){
            	info.setConscode(consOrgCache.getAllUserMap().get(info.getConscode()));
            }
            if(StringUtil.isNotNullStr(info.getCreator()) && StringUtil.isNotNullStr(info.getMsgstyle()) && !"2".equals(info.getMsgstyle())){
            	info.setCreator(consOrgCache.getAllUserMap().get(info.getCreator()));
            }
            if(StringUtil.isNotNullStr(info.getOrgcode())){
            	info.setOrgcode(consOrgCache.getAllOrgMap().get(info.getOrgcode()));
            }
        }
		resultMap.put("rows", listdata);
		return resultMap;
	}
	
	//导出操作
	@SuppressWarnings("unchecked")
	@ResponseBody
    @RequestMapping("/exportPushMsg.do")
    public Map<String, Object> exportPushMsg(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// 设置查询参数
		
		String consname = request.getParameter("consname");
		String title = request.getParameter("title");
		String msgtype = request.getParameter("msgtype");
        String submsgtype = request.getParameter("submsgtype");
        String pushmsgMode = request.getParameter("pushmsgMode");
        String pushmsgPass = request.getParameter("pushmsgPass");
		String creator = request.getParameter("creator");
		String content = request.getParameter("content");
		String pushflag = request.getParameter("pushflag");
		String sendBeginDt = request.getParameter("sendBeginDt");
		String sendEndDt = request.getParameter("sendEndDt");
		Map<String, String> param = new HashMap<>();
		if (StringUtils.isNotBlank(consname)) {
			param.put("consname", consname);
		} else {
			param.put("consname", null);
		}

		if (StringUtils.isNotBlank(title)) {
			param.put("title", title);
		} else {
			param.put("title", null);
		}
		
		if (StringUtils.isNotBlank(msgtype)) {
			param.put("msgtype", msgtype);
		} else {
			param.put("msgtype", null);
		}
		
		if (StringUtils.isNotBlank(creator)) {
			param.put("creator", creator);
		} else {
			param.put("creator", null);
		}
		
		if (StringUtils.isNotBlank(content)) {
			param.put("content", content);
		} else {
			param.put("content", null);
		}
		
		if (StringUtils.isNotBlank(pushflag)) {
			param.put("pushflag", pushflag);
		} else {
			param.put("pushflag", null);
		}
		
		if (StringUtils.isNotBlank(sendBeginDt)) {
			param.put("sendBeginDt", sendBeginDt);
		} else {
			param.put("sendBeginDt", null);
		}
		
		if (StringUtils.isNotBlank(sendEndDt)) {
			param.put("sendEndDt", sendEndDt);
		} else {
			param.put("sendEndDt", null);
		}

        if (StringUtils.isNotBlank(submsgtype)) {
            param.put("submsgtype", submsgtype);
        } else {
            param.put("submsgtype", null);
        }

        if (StringUtils.isNotBlank(pushmsgMode)) {
            param.put("msgstyle", pushmsgMode);
        } else {
            param.put("msgstyle", null);
        }

        if (StringUtils.isNotBlank(pushmsgPass)) {
            param.put("msgchannel", pushmsgPass);
        } else {
            param.put("msgchannel", null);
        }
 		List<CmPushMsg> listdata = cmPushMsgService.listPushMsg(param);
 		if(listdata != null && listdata.size() > 10000){
 			resultMap.put("msg", "moreData");
 			return resultMap;
 		}
 		if(listdata == null || (listdata != null && listdata.size() == 0)){
 			resultMap.put("msg", "dataError");
 			return resultMap;
 		}
		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		ConstantCache constantCache = ConstantCache.getInstance();
		for (CmPushMsg info : listdata) {
            info.setMsgtypeval(cmPushMsgService.getMsgTypeName(info.getMsgtype()));
            //info.setMsgtypeval(constantCache.getConstantKeyVal("pushmsgType").get(info.getMsgtype()));
            info.setPushflagval(constantCache.getConstantKeyVal("pushmsgFlag").get(info.getPushflag()));
            info.setMsgchannelval(constantCache.getConstantKeyVal("pushmsgPass").get(info.getMsgchannel()));
            info.setMsgstyleval(constantCache.getConstantKeyVal("pushmsgMode").get(info.getMsgstyle()));
            //如果是消息形式是系统则不显示预计发送时间
            if("2".equals(info.getMsgstyle())){
                info.setExpectpushdtval("");
            }
            if(StringUtil.isNotNullStr(info.getConscode())){
            	info.setConscode(consOrgCache.getAllUserMap().get(info.getConscode()));
            }
            if(StringUtil.isNotNullStr(info.getCreator()) && StringUtil.isNotNullStr(info.getMsgstyle()) && !"2".equals(info.getMsgstyle())){
            	info.setCreator(consOrgCache.getAllUserMap().get(info.getCreator()));
            }
            if(StringUtil.isNotNullStr(info.getOrgcode())){
            	info.setOrgcode(consOrgCache.getAllOrgMap().get(info.getOrgcode()));
            }
		}
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("推送消息信息.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String[] columnName = {"发送时间","预计发送时间","标题","消息类型","内容","接收投顾","接收投顾部门","接收客户","发送状态","发送人","消息形式","消息通道"};

            String[] beanProperty = {"pushdtval","expectpushdtval","title","msgtypeval","msgcontent","conscode","orgcode", "custName","pushflagval","creator","msgstyleval","msgchannelval"};
            ExcelWriter.writeExcel(os, "预约信息", 0, listdata, columnName, beanProperty);
            os.close(); // 关闭流
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }
        resultMap.put("msg", "success");
		return resultMap;
    }
	
	@RequestMapping("/toCancelPushMsg.do")
    @ResponseBody
    public String toCancelPushMsg(HttpServletRequest request){
    	String result="";
    	String pushid = request.getParameter("pushid");
    	Map<String,String> param = new HashMap<String,String>();
    	param.put("pushid", pushid);
    	param.put("pushflag", StaticVar.MSG_PUSHFLAG_TOBESEND);
    	CmPushMsg pushmsg = cmPushMsgService.getPushMsg(param);
    	if(pushmsg != null){
	        User user = (User) request.getSession().getAttribute("loginUser");
	        boolean issuccess = cmPushMsgService.cancelPushMsg(pushid, user.getUserId());
	        if(issuccess){
	        	result = "success";
	        }
        }else{
        	result = "nottobeflag";
        }
        return result;
    }
	
	@RequestMapping("/toBatchCancelPushMsg.do")
    @ResponseBody
    public String toBatchCancelPushMsg(HttpServletRequest request){
    	String result="";
    	String pushids = request.getParameter("pushids");
    	String [] arrids = pushids.split(",");
    	StringBuilder sb = new StringBuilder();
    	sb.append(" ( ");
    	for(String pushid : arrids){
    		if(StringUtil.isNotNullStr(pushid)){
    			sb.append(" '"+pushid+"', ");
    		}
    	}
    	sb.append(" '' ) "); 
    	Map<String,String> param = new HashMap<String,String>();
    	param.put("ids", sb.toString());
    	List<CmPushMsg> pushmsgs = cmPushMsgService.listPushMsg(param);
    	//查询选中的消息中是否有非待发送状态的，如果有，就直接返回
    	for(CmPushMsg pushmsg : pushmsgs){
    		if(!StaticVar.MSG_PUSHFLAG_TOBESEND.equals(pushmsg.getPushflag())){
    			result = "nottobeflag";
    			return result;
    		}
    	}
        User user = (User) request.getSession().getAttribute("loginUser");
        boolean issuccess = cmPushMsgService.batchCancelPushMsg(sb.toString(), user.getUserId());
        if(issuccess){
        	result = "success";
        }
        
        return result;
    }

    //导出
    @ResponseBody
    @RequestMapping("/exportPushMsgInfo")
    public Map<String, Object> exportPushMsgInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String title = request.getParameter("title");
        String content = request.getParameter("content");
        String pushid = request.getParameter("pushid");

        String[] columnName = title.substring(0, title.length() / 2).split(",");
        String[] beanProperty = content.substring(0, content.length() / 2).split(",");
        List<CmPushMsgAnnex> listData = cmPushMsgService.listPushMsgAnnex(pushid);
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("推送消息信息.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();
            ExcelWriter.writeExcel(os, "消息信息", 0, listData, columnName, beanProperty);
            os.close(); // 关闭流
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }
        resultMap.put("msg", "success");
        return resultMap;
    }

    /**
     * @api {POST} /pushmsg/savewechattaskpushmsg saveWechatTaskPushMsg
     * @apiVersion 1.0.0
     * @apiGroup CmPushMsgController
     * @apiName saveWechatTaskPushMsg
     * @apiDescription 保存企微任务及消息
     * @apiParam (请求体) {String} msgType 消息类型
     * @apiParam (请求体) {String} pushType 推送类型 1-只推送企微任务 2-均推送卡片 3-针对添加客户推任务，针对未添加客户推卡片消息
     * @apiParam (请求体) {Object} wechatTaskDTO 企微任务配置
     * @apiParam (请求体) {String} wechatTaskDTO.hrefTitle 链接标题
     * @apiParam (请求体) {String} wechatTaskDTO.hrefDesc 链接描述
     * @apiParam (请求体) {String} wechatTaskDTO.title 标题
     * @apiParam (请求体) {String} wechatTaskDTO.content 内容
     * @apiParam (请求体) {String} wechatTaskDTO.hrefUrl 链接地址
     * @apiParam (请求体) {Object} cardMessageDTO 卡片消息配置
     * @apiParam (请求体) {String} cardMessageDTO.title 标题
     * @apiParam (请求体) {String} cardMessageDTO.content 内容
     * @apiParam (请求体) {String} cardMessageDTO.hrefUrl 链接地址
     * @apiParam (请求体) {Array} detailList 明细
     * @apiParam (请求体) {String} detailList.conscustNo 客户号
     * @apiParam (请求体) {String} detailList.title 标题
     * @apiParam (请求体) {String} detailList.msgType 消息类型
     * @apiParam (请求体) {String} detailList.msgContent 消息内容
     * @apiParam (请求体) {String} detailList.expectPushDt 预计发送时间
     * @apiParam (请求体) {String} detailList.taskType 任务类型 1-企微任务 2-卡片消息
     * @apiParamExample 请求体示例
     * {"msgType":"2bZD","wechatTaskDTO":{"hrefTitle":"R3BrfH","hrefDesc":"RCwh1aL9d","hrefUrl":"oRTwY6N59","title":"vK49P","content":"Y"},"detailList":[{"conscustNo":"RGTIcMCMj0","taskType":"A","msgType":"Ma","msgContent":"9zPw","title":"N06","expectPushDt":"0Lqb5gacm"}],"cardMessageDTO":{"hrefUrl":"1mkpn","title":"gczGiWXy","content":"SLCnbhj7a"},"pushType":"7KV1"}
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @PostMapping("/savewechattaskpushmsg")
    @ResponseBody
    public void saveWechatTaskPushMsg(@RequestBody CmPushMsgVO cmPushMsgVO){
        cmPushMsgVO.setCreator(getLoginUserId());
        saveWechatTaskMsg(cmPushMsgVO);
    }


    /*
     * @description:(获取消息链接。 )
     * @param cmPushMsgVO
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2024/9/19 10:31
     * @since JDK 1.8
     */
    private String getMsgLink(CmPushMsgVO cmPushMsgVO){
//        根据 企微群发助手任务 、 卡片消息任务
//        不同的任务，取 各自不同的链接
        if(cmPushMsgVO.getWechatTaskDTO()!=null){
            return cmPushMsgVO.getWechatTaskDTO().getHrefUrl();
        }
        if(cmPushMsgVO.getCardMessageDTO()!=null){
            return cmPushMsgVO.getCardMessageDTO().getHrefUrl();
        }
        return null;
    }


    /**
     * @description:(保存企微任务及消息.注意：detailList中存储的是客户号。改方法只支持按照客户列表发送)
     * @param cmPushMsgVO
     * @return void
     * @author: haoran.zhang
     * @date: 2024/9/18 16:49
     * @since JDK 1.8
     */
    private void saveWechatTaskMsg(CmPushMsgVO cmPushMsgVO){
        List<CmPushMsg> list = Lists.newArrayList();
        //保存企微任务
        String taskId =saveWechatTask(cmPushMsgVO);

        //为适配 普通消息，也支持 链接配置
        //新增 msgLink .该链接 放在 CM_PUSH_MSG 中。 目前只做展示使用。
        //业务发送 卡片消息、群发助手消息 使用的  链接。仍然记在 CM_WECHAT_PUSH_TASK 中
        final String msgLink=getMsgLink(cmPushMsgVO);

        log.info("taskId:{}, detailSize:{}", taskId, cmPushMsgVO.getDetailList() == null ? 0 : cmPushMsgVO.getDetailList().size());
        if(CollectionUtils.isNotEmpty(cmPushMsgVO.getDetailList())) {
            List<String> conscustNos = cmPushMsgVO.getDetailList().stream().map(CmPushMsgVO.CmPushMsgDetailVO::getConscustNo).collect(Collectors.toList());
            Map<String, ConsultantAndOrgInfoDTO> consultantMap = getconsultantMap(conscustNos);
            cmPushMsgVO.getDetailList().forEach(cmPushMsgDetailVO -> {
                CmPushMsg cmPushMsg = new CmPushMsg();
                ConsultantAndOrgInfoDTO consultantAndOrgInfoDTO = consultantMap.get(cmPushMsgDetailVO.getConscustNo());
                if(consultantAndOrgInfoDTO == null){
                    return;
                }
                String id = commonService.getSeqValue(SEQ_PUSH_MSG);
                cmPushMsg.setPushid(id);
                cmPushMsg.setTaskType(cmPushMsgDetailVO.getTaskType());
                cmPushMsg.setConscustNo(cmPushMsgDetailVO.getConscustNo());
                cmPushMsg.setConscode(consultantAndOrgInfoDTO.getConsCode());
                cmPushMsg.setOrgcode(consultantAndOrgInfoDTO.getOrgCode());
                cmPushMsg.setMsgtype(cmPushMsgVO.getMsgType());
                cmPushMsg.setMsgcontent(cmPushMsgDetailVO.getMsgContent());
                cmPushMsg.setTitle(cmPushMsgDetailVO.getTitle());
                //企业微信
                cmPushMsg.setMsgchannel("2");
                //人工
                cmPushMsg.setMsgstyle("1");
                cmPushMsg.setCreator(cmPushMsgVO.getCreator());
                cmPushMsg.setCredt(new Date());
                if(StringUtil.isNotNullStr(cmPushMsgDetailVO.getExpectPushDt())) {
                    cmPushMsg.setExpectpushdt(DateUtil.getString2Date(cmPushMsgDetailVO.getExpectPushDt(), "yyyyMMddHHmm"));
                }
                cmPushMsg.setTaskId(taskId);
                cmPushMsg.setMsgLink(msgLink);
                list.add(cmPushMsg);
            });
            cmPushMsgService.insertCmPushMsg(list);
        }
    }

    /**
     * @description 查投顾信息
     * @param conscustNos
     * @return java.util.Map<java.lang.String,com.howbuy.crm.consultant.dto.ConsultantAndOrgInfoDTO>
     * @author: jianjian.yang
     * @date: 2024/3/18 17:44
     * @since JDK 1.8
     */
    private Map<String, ConsultantAndOrgInfoDTO> getconsultantMap(List<String> conscustNos){
        Map<String, ConsultantAndOrgInfoDTO> consultantMap = Maps.newHashMap();
        int maxCount = 999;
        if(CollectionUtils.isEmpty(conscustNos)){
            return consultantMap;
        }
        List<List<String>> partition = Lists.partition(conscustNos, maxCount);
        for (List<String> list : partition) {
            ConsCustListRequest consCustListRequest = new ConsCustListRequest();
            consCustListRequest.setConscustNos(list);
            ConsultantAndOrgResponse response = queryConsultantInfoService.listConsCodeAndOrgCodeByCust(consCustListRequest);
            if(response != null && response.getConsultantAndOrgList() != null){
                response.getConsultantAndOrgList().forEach(consultantAndOrgInfoDTO -> consultantMap.put(consultantAndOrgInfoDTO.getConscustNo(), consultantAndOrgInfoDTO));
            }
        }
        return consultantMap;
    }

    /**
     * @description 保存企微任务
     * @param cmPushMsgVO
     * @return void
     * @author: jianjian.yang
     * @date: 2023/10/13 18:19
     * @since JDK 1.8
     */
    private String saveWechatTask(CmPushMsgVO cmPushMsgVO){
        String taskId = commonService.getSeqValue(SEQ_PUSH_MSG);
        CmWechatPushTask cmWechatPushTask = new CmWechatPushTask();
        cmWechatPushTask.setId(Integer.valueOf(taskId));
        cmWechatPushTask.setMsgType(cmPushMsgVO.getMsgType());
        cmWechatPushTask.setPushType(cmPushMsgVO.getPushType());
        CmPushMsgVO.WechatTaskDTO wechatTaskDTO = cmPushMsgVO.getWechatTaskDTO();
        if(wechatTaskDTO != null) {
            cmWechatPushTask.setTaskTitle(wechatTaskDTO.getTitle());
            cmWechatPushTask.setTaskContent(wechatTaskDTO.getContent());
            cmWechatPushTask.setTaskUrl(wechatTaskDTO.getHrefUrl());
            cmWechatPushTask.setTaskUrlTitle(wechatTaskDTO.getHrefTitle());
            cmWechatPushTask.setTaskUrlDesc(wechatTaskDTO.getHrefDesc());
        }
        CmPushMsgVO.CardMessageDTO cardMessageDTO = cmPushMsgVO.getCardMessageDTO();
        if(cardMessageDTO != null) {
            cmWechatPushTask.setCardUrl(cardMessageDTO.getHrefUrl());
            cmWechatPushTask.setCardContent(cardMessageDTO.getContent());
            cmWechatPushTask.setCardTitle(cardMessageDTO.getTitle());
        }
        cmWechatPushTask.setCreateTime(new Date());
        cmWechatPushTask.setCreator(cmPushMsgVO.getCreator());
        cmWechatPushTaskService.saveCmWechatPushTask(cmWechatPushTask);

        return  taskId;
    }

}
