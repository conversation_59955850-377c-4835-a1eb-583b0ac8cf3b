package com.howbuy.crm.hb.web.controller.counter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.howbuy.acccenter.facade.query.idcert.QueryDealImageDataInfoFacade;
import com.howbuy.acccenter.facade.query.querykycexamdtl.QueryKycExamDtlFacade;
import com.howbuy.acccenter.facade.query.querykycexamdtl.QueryKycExamDtlRequest;
import com.howbuy.acccenter.facade.query.querykycexamdtl.QueryKycExamDtlResponse;
import com.howbuy.acccenter.facade.query.querykycexamdtl.bean.ExamInfoBean;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.cc.center.feature.kycinfo.domain.UserTypeEnum;
import com.howbuy.cc.center.feature.question.domain.ExamType;
import com.howbuy.crm.base.*;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.constants.CrmTradeServerPathConstant;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.fixed.CmFixedIntention;
import com.howbuy.crm.hb.domain.hkconscust.HkConscustVO;
import com.howbuy.crm.hb.domain.joinclub.CmConscustsurveyanswer;
import com.howbuy.crm.hb.domain.joinclub.CmConscustsurveyrec;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.fixed.CmFixedIntentionService;
import com.howbuy.crm.hb.service.hkconscust.HkConscustService;
import com.howbuy.crm.hb.service.joinclub.CmConscustsurveyanswerService;
import com.howbuy.crm.hb.service.joinclub.CmConscustsurveyrecService;
import com.howbuy.crm.hb.web.dto.counter.CmCounterOuterFileDto;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub;
import com.howbuy.crm.nt.accountrelation.service.CmRelationAccountSubService;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.webapp.util.SessionUserManager;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.prebook.dto.CmPreBookProductInfo;
import com.howbuy.crm.prebook.dto.CmPrebookBankInfo;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import com.howbuy.crm.trade.common.constant.TradeServerStaticVar;
import com.howbuy.crm.trade.common.enums.counter.CounterBusiEnum;
import com.howbuy.crm.trade.common.enums.counter.CounterBusiProidEnum;
import com.howbuy.crm.trade.common.enums.counter.CounterPreTypeEnum;
import com.howbuy.crm.trade.common.response.BaseResponse;
import com.howbuy.crm.trade.common.util.CounterUtil;
import com.howbuy.crm.trade.model.counter.busiparam.CxgDetailDto;
import com.howbuy.crm.trade.model.counter.busiparam.HkCxgParamDto;
import com.howbuy.crm.trade.model.counter.busiparam.HkDepositParamDto;
import com.howbuy.crm.trade.model.counter.busiparam.HkVoucherParamDto;
import com.howbuy.crm.trade.model.counter.dto.CmCounterOrderDto;
import com.howbuy.crm.trade.model.counter.dto.CmCounterOrderFileDto;
import com.howbuy.crm.trade.model.counter.dto.CmCounterOrderfileFileStreamInfo;
import com.howbuy.crm.trade.model.counter.po.CmCounterOrder;
import com.howbuy.crm.trade.model.counter.po.CmCounterOrderFile;
import com.howbuy.crm.trade.model.counter.request.EditCmCounterOrderRequest;
import com.howbuy.crm.trade.model.counter.request.SaveCounterArchRequest;
import com.howbuy.crm.trade.model.counter.request.SaveCounterMailRequest;
import com.howbuy.crm.trade.model.counter.request.SaveCounterSignRequest;
import com.howbuy.crm.transfervol.dto.CmCustTransferVol;
import com.howbuy.crm.transfervol.service.CustTransferVolService;
import com.howbuy.simu.service.base.product.SmccProductJbxxService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.enums.DisCodeEnum;
import crm.howbuy.base.utils.DateUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: Controller
 * @version 1.0
 */
@Slf4j
@Controller
@RequestMapping(value = "/counter")
public class CmCounterOrderFileController  extends BaseCounterController {

	@Autowired
	private CommonService commonService;
	
	@Autowired
    private SmccProductJbxxService smccProductJbxxService;

    @Autowired
    private CmRelationAccountSubService cmRelationAccountSubService;


    @Autowired
    private CmFixedIntentionService cmFixedIntentionService;

    @Autowired
    private QueryDealImageDataInfoFacade queryDealImageDataInfoFacade;


    @Autowired
    private QueryPreBookService queryPreBookService;

    @Autowired
    private PrebookBasicInfoService prebookBasicInfoService;

    @Autowired
    private ConscustService conscustService;

    @Autowired
    private CustTransferVolService custTransferVolService;
    @Autowired
    private JjxxInfoService jjxxInfoService;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    @Autowired
    private CmConscustsurveyrecService cmConscustsurveyrecService;
    @Autowired
    private QueryKycExamDtlFacade queryKycExamDtlFacade;
    @Autowired
    private DecryptSingleFacade decryptSingleFacade;
    @Autowired
    private CmConscustsurveyanswerService cmConscustsurveyanswerService;
    @Autowired
    private HkConscustService hkConscustService;
	
	/**
     * 跳转到adduptmail页面方法
     *
     * @return String
     */
    @RequestMapping("/addUptMail.do")
    public ModelAndView adduptmailmanage(HttpServletRequest request,HttpServletResponse response) throws Exception {
    	//邮寄类别： 1-邮寄（管理人）；2-邮寄（分部）
        String mailType = ObjectUtils.replaceNull(request.getParameter("mailType"));
        String id = request.getParameter("id");
        String mailDate = ObjectUtils.replaceNull(request.getParameter("mailDate"));
        String mailNo = ObjectUtils.replaceNull(request.getParameter("mailNo"));
        if("null".equals(mailDate)){
        	mailDate="";
        }
        if("null".equals(mailNo)){
        	mailNo="";
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("mailDate", mailDate);
        map.put("mailNo", mailNo);
        map.put("userId",getLoginUserId());
        if (StringUtils.isNotBlank(id)) {
        	List<CmCounterOrderFileDto> list = listCmCounterOrderFileDto(id,null);
            if (list != null && list.size() > 0) {
                map.put("list", list);
            }
        }
        String url = null;
        if(TradeServerStaticVar.MAIL_MANAGE.equals(mailType)){
    		url = "/counter/adduptmailmanage";
    	}else if(TradeServerStaticVar.MAIL_DEPT.equals(mailType)){
    		url = "/counter/adduptmaildept";
    	}
        return new ModelAndView(url, "map", map);
    }
    

    
    private  String trimUndefined(String  paramStr){
        return  ("undefined".equals(paramStr) || "null".equals(paramStr) ) ?null: paramStr;
    }
    
    /**
     * 处理是否邮寄  mailType:1-邮寄（管理人）；2- 邮寄（分部）
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/saveMail.do")
    public String saveMail(HttpServletRequest request) throws Exception {
        User user = getLoginUser();
        String result = "error";
        String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
        String mailDt = ObjectUtils.replaceNull(request.getParameter("mailDt"));
        String mailNo = ObjectUtils.replaceNull(request.getParameter("mailNo"));
        //邮寄类别： 1-邮寄（管理人）；2-邮寄（分部）
        String mailType = ObjectUtils.replaceNull(request.getParameter("mailType"));
        log.info("操作人：{} .saveMail.do请求。参数： ids:{},mailDt:{},mailNo:{},mailType:{}",user.getUserId(),ids,mailDt,mailNo,mailType);
        if (StringUtils.isNotBlank(ids)) {
        	//将用|符合隔开的每条记录分离开来
        	ids = ids.replaceFirst("\\|", "");
            String[] mails = ids.split("\\|");
        	List<CmCounterOrderFile> listCmCounterOrderFile = new ArrayList<CmCounterOrderFile>();
        	for(String mail : mails){
        		//解析每条记录的主键、是否通过、备注
            	String id = mail.split("`")[0];
            	String isMail = mail.split("`")[1];
            	String mailDes = mail.split("`")[2];

            	Map<String,String> param = new HashMap<String,String>(8);
            	param.put("id", id);
            	BaseResponse<CmCounterOrderFile> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.GET_COUNTER_ORDER_FILE, param,new ParameterizedTypeReference<BaseResponse<CmCounterOrderFile>>(){});
            	CmCounterOrderFile file = httpRsp.isSuccess() ? httpRsp.getData() : null;
            	file.setId(id);
            	if(TradeServerStaticVar.MAIL_MANAGE.equals(mailType)){
            		file.setIsMailManage(trimUndefined(isMail));
            		file.setMailManageDes(mailDes.trim());
                	file.setMailManageOptor(user.getUserId());
                	file.setMailManageOptdate(new Date());
            	}else if(TradeServerStaticVar.MAIL_DEPT.equals(mailType)){
            		file.setIsMailDept(trimUndefined(isMail));
            		file.setMailDeptDes(mailDes.trim());
                	file.setMailDeptOptor(user.getUserId());
                	file.setMailDeptOptdate(new Date());
            	}
            	listCmCounterOrderFile.add(file);
        	}
        	
        	// 设置参数  
        	SaveCounterMailRequest saveCounterMailRequest = new SaveCounterMailRequest();
        	saveCounterMailRequest.setMailMsgList(listCmCounterOrderFile);
        	saveCounterMailRequest.setOperatorNo(user.getUserId());
        	saveCounterMailRequest.setMailDt(mailDt);
        	saveCounterMailRequest.setMailNo(mailNo);
        	saveCounterMailRequest.setMailType(mailType);
			BaseResponse<String> httpRsp = getPostEntityByJsonObject(CrmTradeServerPathConstant.SAVE_COUNTER_MAIL, saveCounterMailRequest,new ParameterizedTypeReference<BaseResponse<String>>(){});
			if (httpRsp.isSuccess()) {
				result = "success";
			}
        } else {
            result = "paramError";
        }
        return result;
    }

    /**
     * 跳转到viewMail页面方法
     *
     * @return String
     */
    @RequestMapping("/viewMail.do")
    public ModelAndView viewMail(HttpServletRequest request,HttpServletResponse response) throws Exception {
    	//邮寄类别： 1-邮寄（管理人）；2-邮寄（分部）
        String mailType = ObjectUtils.replaceNull(request.getParameter("mailType"));
        String id = request.getParameter("id");
        String mailDate = ObjectUtils.replaceNull(request.getParameter("mailDate"));
        String mailNo = ObjectUtils.replaceNull(request.getParameter("mailNo"));
        if("null".equals(mailDate)){
        	mailDate="";
        }
        if("null".equals(mailNo)){
        	mailNo="";
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("mailDate", mailDate);
        map.put("mailNo", mailNo);
        map.put("userId",getLoginUserId());
        if (StringUtils.isNotBlank(id)) {
        	List<CmCounterOrderFileDto> list = listCmCounterOrderFileDto(id,null);
            if (list != null && list.size() > 0) {
                map.put("list", list);
            }
        }
        String url = null;
        if(TradeServerStaticVar.MAIL_MANAGE.equals(mailType)){
    		url = "/counter/viewmailmanage";
    	}else if(TradeServerStaticVar.MAIL_DEPT.equals(mailType)){
    		url = "/counter/viewmaildept";
    	}
        return new ModelAndView(url, "map", map);
    }

    /**
     * 跳转到addUptsign页面方法
     *
     * @return String
     */
    @RequestMapping("/addUptsign.do")
    public ModelAndView addUptsign(HttpServletRequest request,HttpServletResponse response) throws Exception {
        String id = request.getParameter("id");
        String signDate = ObjectUtils.replaceNull(request.getParameter("signDate"));
        if("null".equals(signDate)){
        	signDate = "";
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("signDate", signDate);
        map.put("userId",getLoginUserId());
        if (StringUtils.isNotBlank(id)) {
        	List<CmCounterOrderFileDto> list = listCmCounterOrderFileDto(id,null);
            if (list != null && list.size() > 0) {
                map.put("list", list);
            }
        }
        return new ModelAndView("/counter/adduptsign", "map", map);
    }
    
    //处理是否签收
    @ResponseBody
    @RequestMapping("/saveSign.do")
    public String saveSign(HttpServletRequest request) throws Exception {
        User user = getLoginUser();
        String result = "";
        String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
        String signDt = ObjectUtils.replaceNull(request.getParameter("signDt"));
        if (StringUtils.isNotBlank(ids)) {
        	List<CmCounterOrderFile> updatelist = new ArrayList<>();
        	//将用|符合隔开的每条记录分离开来
        	ids = ids.replaceFirst("\\|", "");
            String[] signs = ids.split("\\|");
            for(String sign : signs){
            	//根据主键id获取该条附件信息，修改相关字段
            	String id = sign.split("`")[0];
            	String isSign = sign.split("`")[1];
            	String signDes = sign.split("`")[2];
            	
            	Map<String,String> param = new HashMap<String,String>(8);
            	param.put("id", id);
            	BaseResponse<CmCounterOrderFile> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.GET_COUNTER_ORDER_FILE, param,new ParameterizedTypeReference<BaseResponse<CmCounterOrderFile>>(){});
            	CmCounterOrderFile file = httpRsp.isSuccess() ? httpRsp.getData() : null;
            	if("undefined".equals(isSign)){
            		file.setIsSignSmop(null);;
                } else if (null != isSign) {
                    file.setIsSignSmop(isSign);
                }
                if (null != signDes) {
                    file.setSignSmopDes(signDes.trim());
                }
                if (null != user.getUserId()) {
                    file.setSignSmopOptor( user.getUserId());

                }
            	file.setSignSmopOptdate(new Date());
            	updatelist.add(file);
            }
            
            // 设置参数  
            SaveCounterSignRequest saveCounterSignRequest = new SaveCounterSignRequest();
            saveCounterSignRequest.setSignMsgList(updatelist);
            saveCounterSignRequest.setOperatorNo(user.getUserId());
            saveCounterSignRequest.setSignSmopOptDt(signDt);
			BaseResponse<String> httpRsp = getPostEntityByJsonObject(CrmTradeServerPathConstant.SAVE_COUNTER_SIGN, saveCounterSignRequest,new ParameterizedTypeReference<BaseResponse<String>>(){});
			if (httpRsp.isSuccess()) {
				result = "success";
			}
        } else {
            result = "paramError";
        }
        return result;
    }
    
    
    /**
     * 跳转到viewsign页面方法
     *
     * @return String
     */
    @RequestMapping("/viewSign.do")
    public ModelAndView viewSign(HttpServletRequest request,HttpServletResponse response) throws Exception {
        String id = request.getParameter("id");
        String signDate = ObjectUtils.replaceNull(request.getParameter("signDate"));
        if("null".equals(signDate)){
        	signDate = "";
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("signDate", signDate);
        map.put("userId",getLoginUserId());
        if (StringUtils.isNotBlank(id)) {
            List<CmCounterOrderFileDto> list = listCmCounterOrderFileDto(id,null);
            if (list != null && list.size() > 0) {
                map.put("list", list);
            }
        }
        return new ModelAndView("/counter/viewsign", "map", map);
    }
    
    
    /**
     * 跳转到addUptarch页面方法
     * 展示归档编辑页面
     *
     * @return String
     */
    @RequestMapping("/addUptarch.do")
    public ModelAndView addUptarch(HttpServletRequest request,HttpServletResponse response) throws Exception {
        String id = request.getParameter("id");
        String archDate = ObjectUtils.replaceNull(request.getParameter("archDate"));
        String docNo  = ObjectUtils.replaceNull(request.getParameter("docNo"));
        if("null".equals(archDate)){
        	archDate = "";
        }
        if("null".equals(docNo)){
        	docNo = "";
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("archDate", archDate);
        map.put("docNo", docNo);
        map.put("userId",getLoginUserId());
        if (StringUtils.isNotBlank(id)) {
        	List<CmCounterOrderFileDto> list = listCmCounterOrderFileDto(id,null);
            if (list != null && list.size() > 0) {
                map.put("list", list);
            }
        }
        return new ModelAndView("/counter/adduptarch", "map", map);
    }

    //处理是否归档
    @ResponseBody
    @RequestMapping("/saveArch.do")
    public String saveArch(HttpServletRequest request) throws Exception {
        User user = getLoginUser();
        String result = "";
        String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
        String archDt = ObjectUtils.replaceNull(request.getParameter("archDt"));
        String docNo = ObjectUtils.replaceNull(request.getParameter("docNo"));
        if (StringUtils.isNotBlank(ids)) {
        	List<CmCounterOrderFile> updatelist = new ArrayList<>();
        	//将用|符合隔开的每条记录分离开来
        	ids = ids.replaceFirst("\\|", "");
            String[] archs = ids.split("\\|");
            for(String arch : archs){
            	//根据主键id获取该条附件信息，修改相关字段
            	String id = arch.split("`")[0];
            	String isArch = arch.split("`")[1];
            	String archDes = arch.split("`")[2];
            	Map<String,String> param = new HashMap<String,String>(8);
            	param.put("id", id);
            	BaseResponse<CmCounterOrderFile> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.GET_COUNTER_ORDER_FILE, param,new ParameterizedTypeReference<BaseResponse<CmCounterOrderFile>>(){});
            	CmCounterOrderFile file = httpRsp.isSuccess() ? httpRsp.getData() : null;
            	if("undefined".equals(isArch)){
            		file.setIsArchSmop(null);
                } else if (null != isArch) {
                    file.setIsArchSmop(isArch);
                }
                if (null != archDes) {
                    file.setArchSmopDes(archDes.trim());
                }
                if (null != user.getUserId()) {
                    file.setArchSmopOptor(user.getUserId());
                }
            	file.setArchSmopOptdate(new Date());
            	updatelist.add(file);
            }
            
            // 设置参数  
            SaveCounterArchRequest saveCounterArchRequest = new SaveCounterArchRequest();
            saveCounterArchRequest.setArchMsgList(updatelist);
            saveCounterArchRequest.setOperatorNo(user.getUserId());
            saveCounterArchRequest.setArchSmopOptDt(archDt);
            saveCounterArchRequest.setDocSmopNo(docNo);
			BaseResponse<String> httpRsp = getPostEntityByJsonObject(CrmTradeServerPathConstant.SAVE_COUNTER_ARCH, saveCounterArchRequest,new ParameterizedTypeReference<BaseResponse<String>>(){});
			if (httpRsp.isSuccess()) {
				result = "success";
			}
        } else {
            result = "paramError";
        }
        return result;
    }
    
    /**
     * 跳转到viewarch页面方法
     * 展示归档查看页面
     *
     * @return String
     */
    @RequestMapping("/viewarch.do")
    public ModelAndView viewarch(HttpServletRequest request,HttpServletResponse response) throws Exception {
        String id = request.getParameter("id");
        String archdate = ObjectUtils.replaceNull(request.getParameter("archdate"));
        String docno  = ObjectUtils.replaceNull(request.getParameter("docno"));
        if("null".equals(archdate)){
        	archdate = "";
        }
        if("null".equals(docno)){
        	docno = "";
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("archdate", archdate);
        map.put("docno", docno);
        map.put("userId",getLoginUserId());
        if (StringUtils.isNotBlank(id)) {
            List<CmCounterOrderFileDto> list = listCmCounterOrderFileDto(id,null);
            if (list != null && list.size() > 0) {
                map.put("list", list);
            }
        }
        return new ModelAndView("/counter/viewarch", "map", map);
    }
    
    /**
     * 批量邮寄验证
     * @param request
     * @param handleMailType
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/verificationMail.do")
    public Map<String, String> verificationMail(HttpServletRequest request, String handleMailType) throws Exception {
    	Map<String,String> returnMap = new HashMap<String,String>(8);
        String result = "";
        String needBatchMailIds = "";
        String ids = ObjectUtils.replaceNull(request.getParameter("orderIds"));
        if (StringUtils.isNotBlank(ids)) {
        	//将用|符合隔开的每条记录分离开来
        	ids = ids.replaceFirst(",", "");
        	// 设置默认参数
	    	Map<String,String> postParam = new HashMap<String,String>();
	    	postParam.put("orderIdList", ids);	
	    	List<CmCounterOrder> listOrder = null;
			BaseResponse<List<CmCounterOrder>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_ORDR, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterOrder>>>(){});
			if (httpRsp.isSuccess()) {
				listOrder = httpRsp.getData();
			}

        	if(CollectionUtils.isNotEmpty(listOrder)){
        	    String mailStat = null;
        	    String signstat = null;
        	    //无需邮寄或者全部邮寄统计
        	    int notNeedOrAllMail = 0;
        	    //部分邮寄未签收统计
        	    int partMailNotSign = 0;

                /** 邮寄（管理人） */
//                public static final String HANDLE_MAIL_TYPE_MANAGE = "1";
//                /** 邮寄（分部） */
//                public static final String HANDLE_MAIL_TYPE_DEPT = "2";


        	    String handleMailName = TradeServerStaticVar.MAIL_MANAGE.equals(handleMailType) ? "（管理人）" :
                        TradeServerStaticVar.MAIL_DEPT.equals(handleMailType) ? "（分部）" : "";
        	    for(CmCounterOrder order : listOrder){
        	    	signstat = order.getSignSmopStat();
        	    	if(TradeServerStaticVar.MAIL_MANAGE.equals(handleMailType)){
        	    		mailStat = order.getManageMailStat();
        	    	}else if(TradeServerStaticVar.MAIL_DEPT.equals(handleMailType)){
        	    		mailStat = order.getDeptMailStat();
        	    	}
        	    	
        	    	//判断是否存在 邮寄状态= 无需邮寄/全部邮寄 
    	    		if(StaticVar.MAIL_ALLYES.equals(mailStat) || StaticVar.MAIL_NONEED.equals(mailStat)){
    	    			notNeedOrAllMail++;
    	    			continue;
    	    		}
    	    		//判断是否存在 邮寄状态= 部分邮寄 且 签收状态 = 未签收
    	    		if(StaticVar.MAIL_PARTNO.equals(mailStat) && StaticVar.SIGN_ALLNO.equals(signstat)){
    	    			partMailNotSign++;
    	    			continue;
    	    		}
    	    		needBatchMailIds += "," + order.getId(); 
        	    }
     
        	    if (notNeedOrAllMail > 0) {
        	    	result += "存在" + notNeedOrAllMail + "条邮寄状态" + handleMailName + "为无需邮寄或全部邮寄的数据，该数据不会进行批量邮寄处理！<br/>";
        	    }
        	    if (partMailNotSign > 0) {
        	    	result += "存在" + partMailNotSign + "条未签收的数据，该数据不会进行批量邮寄处理！<br/>";
        	    }
        	}
        } else {
            result = "paramError";
        }

        if(StringUtils.isNotBlank(needBatchMailIds)){
        	needBatchMailIds = needBatchMailIds.replaceFirst(",", "");
        }
		
        returnMap.put("result", result);
        returnMap.put("needBatchMailIds", needBatchMailIds);
        return returnMap;
    }
    
    /**
	 * 跳转到批量邮寄页面
	 * @param request
	 * @return ModelAndView
	 */
    @RequestMapping("/batchMailView.do")
	public ModelAndView batchMailView(HttpServletRequest request) {
		String orderIds = ObjectUtils.replaceNull(request.getParameter("orderIds"));
		String handleMailType = request.getParameter("handleMailType");
		String currentDay = DateUtil.getDateYYYYMMDD();
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/counter/batchMailView");
		modelAndView.addObject("currentDay", currentDay);
		modelAndView.addObject("handleMailType", handleMailType);
		modelAndView.addObject("orderIds", orderIds);
		return modelAndView;
	}
	
	//批量处理邮寄
    @ResponseBody
    @RequestMapping("/dealBatchMail.do")
    public String dealBatchMail(HttpServletRequest request, String handleMailType) throws Exception {
        User user = getLoginUser();
        String result = "error";
        String ids = ObjectUtils.replaceNull(request.getParameter("orderIds"));
        String mailNo = request.getParameter("mailNo");
        String mailDate = request.getParameter("mailDate");
        if (StringUtils.isNotBlank(ids)) {
        	//批量邮寄验证
        	Map<String,String> retMap = verificationMail(request,handleMailType);
        	if(retMap != null && StringUtils.isNotBlank(retMap.get("needBatchMailIds"))){
        		// 设置默认参数
            	Map<String,String> postParam = new HashMap<String,String>();
            	postParam.put("orderIdList", retMap.get("needBatchMailIds"));   
            	postParam.put("operatorNo", user.getUserId());   
            	postParam.put("handleMailType", handleMailType);   
            	postParam.put("mailNo", mailNo);   
            	postParam.put("mailDate", mailDate);   
    			BaseResponse<String> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.DEAL_BATCH_MAIL, postParam,new ParameterizedTypeReference<BaseResponse<String>>(){});
    			if (httpRsp.isSuccess()) {
    				result = "success";
    			}
        	}
        } else {
            result = "paramError";
        }
        return result;
    }


    /**
     * 跳转到vieworder页面方法
     *
     * @return String
     */
    @RequestMapping("/vieworeditorder.do")
    public ModelAndView vieworeditorder(HttpServletRequest request,HttpServletResponse response) {
        String id = request.getParameter("id");
        String type = request.getParameter("type");

        Assert.notNull(id,"订单Id不能为空！");

        String url =   "edit".equals(type) ? "/counter/editorder": "/counter/vieworder";

        //全局Map
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId",getLoginUserId());
        // 获取常量缓存
        ConstantCache constantCache = ConstantCache.getInstance();

        //订单信息
        CmCounterOrderDto orderInfo=getOrderById(id);
        String forId=orderInfo.getForId();
        //订单业务信息
        CounterBusiEnum busiEnum=CounterBusiEnum.getEnum(orderInfo.getBusiId());
        CounterBusiProidEnum busiProidEnum=CounterBusiProidEnum.getEnum(orderInfo.getBusiProId());

        //查附件信息
        List<CmCounterOrderFileDto> list=listCmCounterOrderFileDto(id, null);
        map.put("list", list);

        //外部获取的业务文件类型列表 Eg： 海外签约线上签约，客户签署的协议列表
        List<CmCounterOuterFileDto> outerFileList=Lists.newArrayList();
        if(CounterUtil.PREBOOK_HK_TRADELIST_ONLINE.contains(busiEnum)){
            outerFileList=getHkSignFileListByPreId(new BigDecimal(forId));
        }
        map.put("outerFileList", outerFileList);

        //domainMap信息
        Map<String, Object> domainMap = new HashMap<String, Object>();
        //客户信息
        QueryConscustInfoRequest queryCustRequest=new QueryConscustInfoRequest();
        queryCustRequest.setConscustno(orderInfo.getConscustno());
        QueryConscustInfoResponse custResponse=queryConscustInfoService.queryConscustInfo(queryCustRequest);
        ConscustInfoDomain custInfo=custResponse.getConscustinfo();

        // 当前用户是否有“投顾客户-客户姓名链接”权限
        map.put("hasCustLinkAuth", SessionUserManager.hasAuth("020107_1"));

        map.put("custName", custInfo.getCustname());// 设置页面客户姓名
        map.put("custNo",custInfo.getConscustno());
        // 设置页面客户类型
        map.put("invstType", orderInfo.getCustType());
        map.put("invstTypeName", orderInfo.getCustTypeName());

        map.put("id", orderInfo.getId());
        map.put("conscustno", orderInfo.getConscustno());

        map.put("invsttype", orderInfo.getCustType());
        map.put("invsttypeval", orderInfo.getCustTypeName());
        map.put("busiid", orderInfo.getBusiId());
        //不同业务存储的 特殊字段 json字符串
        String specialBusiParam=orderInfo.getSpecialBusiParam();
        // 特殊业务字段 添加

        map.put("busiVal", orderInfo.getBusiIdName());
        
        map.put("idtype", custInfo.getIdtype());
        map.put("idnoCipher", custInfo.getIdnoCipher());
        map.put("idnoMask", custInfo.getIdnoMask());
        map.put("curcheckdes", orderInfo.getCurCheckdes());
        map.put("fundCode","");
        map.put("fundName","");
        // 设置页面基金编码和基金名称
        JjxxInfo jjxxInfo;
        if (StringUtils.isNotBlank(orderInfo.getFundCode())) {
            jjxxInfo=jjxxInfoService.getJjxxByJjdm(orderInfo.getFundCode(),false);
            map.put("fundCode", jjxxInfo.getJjdm());
            map.put("fundName", jjxxInfo.getJjjc());
            map.put("zzxs", jjxxInfo.getZzxs());
            map.put("sfxg", jjxxInfo.getSfxg());
        }

        //定投意向单 业务
        if(CounterUtil.FIXED_AIP_BUSILIST.contains(busiEnum)){
            // 业务属性：“交易-购买”；业务id：定投新增 41、定投终止 42
            map.put("type", "investFixed");

            // 获取定投意向单的信息
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("planid", forId);
            CmFixedIntention cmFixedIntention = cmFixedIntentionService.getCmFixedIntention(paramMap);//TODO: 意向单逻辑待迁移
            String planrate = cmFixedIntention.getPlanrate();

            domainMap.put("planid", forId);

            // 定投周期
            domainMap.put("planrateName", constantCache.getVal("fixedPlanRate", planrate));
            // 定投期数
            domainMap.put("planTotalNum", cmFixedIntention.getPlantotalnum());
            // 定投金额/期
            domainMap.put("planAmount", cmFixedIntention.getPlanamount());
            // 支付方式
            domainMap.put("planPaymentTypeName", constantCache.getVal("fixedpaytype", cmFixedIntention.getPaytype()));

            map.put("domain",domainMap);
            return new ModelAndView(url,  map);
        }
        //份额转让
        if(CounterBusiEnum.TRADE_TRANS_VOL.equals(busiEnum)){
            map.put("type", "transf");
            // 获取份额转让信息
            CmCustTransferVol transferVol=custTransferVolService.getTransferVolById(new BigDecimal(forId));
            // 设置页面转让人
            domainMap.put("transferor", transferVol.getTransferor());
            if (StringUtils.isNotBlank(transferVol.getTransferor())) {
                domainMap.put("transferorname", transferVol.getTransferorname());
            }
            // 设置页面受让人
            domainMap.put("assignee", transferVol.getAssignee());
            if (StringUtils.isNotBlank(transferVol.getAssignee())) {
                domainMap.put("assigneename", transferVol.getAssigneename());
            }
            // 设置页面转让份额
            if (transferVol.getTransfervol()!=null) {
                domainMap.put("transfervol", transferVol.getTransfervol());
            }
            map.put("domain",domainMap);
            return new ModelAndView(url, map);
        }

        //预约相关
        if(CounterUtil.PREBOOK_BUSILIST.contains(busiEnum)){
            BigDecimal preId=new BigDecimal(forId);
            map.put("type", "trade");
            CmPreBookProductInfo preBookInfo=prebookBasicInfoService.getPreBookById(preId);
            //预约
            map.put("preBookInfo", preBookInfo);

            //货币
            map.put("currencyDesc", CurrencyEnum.getDescription(preBookInfo.getCurrency()));
            // 赎回方式
            if (StringUtils.isNotBlank(preBookInfo.getRedeemMode())) {
                map.put("redeemMode", RedeemModeEnum.getDescription(preBookInfo.getRedeemMode()));
                map.put("redeemModeCode", preBookInfo.getRedeemMode());
            }
            //香港产品
            if(YesOrNoEnum.YES.getCode().equals(preBookInfo.getSfxg())) {
                //支付方式
                List<String> paymentList= PaymentModeEnum.getPaymentEnumListByValue(preBookInfo.getPaymentMode())
                        .stream().map(PaymentModeEnum::getDesc).collect(Collectors.toList());

                //赎回方向
                List<String> redeemList= RedeemDirectEnum.getRedeemEnumListByValue(preBookInfo.getRedeemDirection())
                        .stream().map(RedeemDirectEnum::getDesc).collect(Collectors.toList());

                map.put("paymentDesc", String.join(",",paymentList));
                map.put("redeemDesc", String.join(",",redeemList));
                map.put("divModeDesc", DivModeEnum.getName(preBookInfo.getDivMode()));

                //卡信息列表
                List<CmPrebookBankInfo>  hkBankList=prebookBasicInfoService.getHkBankAcctInfoList(preId);
                map.put("hkBankList", hkBankList);

                //56-香港-交易打款凭证
                if(CounterBusiEnum.TRADE_HK_PAYMENT_VOUCHER==busiEnum){
                    if (StringUtils.isNotBlank(specialBusiParam)) {
                        HkVoucherParamDto paramDto= JSON.parseObject(specialBusiParam,HkVoucherParamDto.class);
                        // 【汇款金额】 备注：客户APP下单时支持传打款凭证，将触发CRM生成一条业务类型为“好买香港-交易打款凭证”的审核流水
                        map.put("hkCustUploadRemitAmt",
                                paramDto.getRemitAmt() == null ?
                                "" : String.format("%.4f", paramDto.getRemitAmt()));
                        // 【汇款币种】 备注：客户APP下单时支持传打款凭证，将触发CRM生成一条业务类型为“好买香港-交易打款凭证”的审核流水
                        map.put("hkCustUploadCurrencyDesc", paramDto.getCurrency() == null ?
                                "" : CurrencyEnum.getDescription(paramDto.getCurrency()));
                        map.put("hkCustUploadCurrency", paramDto.getCurrency());
                        // 【凭证备注】
                        map.put("hkUploadRemark", paramDto.getRemark() == null ?
                                "" : paramDto.getRemark());

                    }
                }
            }
            if (CounterBusiEnum.LEGAL_DOCUMENT == busiEnum
                    || CounterBusiEnum.HZ_LEGAL_DOCUMENT == busiEnum) {
                String legalDocSendCustomerDt = "";
                if (StringUtils.isNotBlank(specialBusiParam) && specialBusiParam.contains("legalDocSendCustomerDt")) {
                    JSONObject jsonObject = JSON.parseObject(specialBusiParam);
                    legalDocSendCustomerDt = jsonObject.getString("legalDocSendCustomerDt");

                }
                domainMap.put("legalDocSendCustomerDt", legalDocSendCustomerDt);

            }

            domainMap.put("pretypeval", StringUtils.isNotBlank(preBookInfo.getPretype())?CounterPreTypeEnum.getDesc(preBookInfo.getPretype()):"");

            domainMap.put("buyamt", preBookInfo.getBuyamt() == null  ? "0.000000" : preBookInfo.getBuyamt().divide(new BigDecimal(10000),6, RoundingMode.HALF_UP));
            domainMap.put("sellvol", preBookInfo.getSellvol() == null ? BigDecimal.ZERO : preBookInfo.getSellvol());
            domainMap.put("sellamt", preBookInfo.getSellAmt() == null ? BigDecimal.ZERO : preBookInfo.getSellAmt());


            domainMap.put("preid", preBookInfo.getId());
            domainMap.put("pretype", preBookInfo.getPretype());
            domainMap.put("tradetype",preBookInfo.getTradeType() );

            map.put("domain",domainMap);
            return new ModelAndView(url,  map);
        }

        //好臻好买入会 编辑
        if(CounterBusiEnum.RISK_ANALYSE_HZ.equals(busiEnum)
                || CounterBusiEnum.RISK_ANALYSE_HOWBUY.equals(busiEnum)){
            map.put("type",type);
            if(CounterBusiEnum.RISK_ANALYSE_HZ.equals(busiEnum)){
                map.put("joinType", "risk_analyse_hz");
            }else if (CounterBusiEnum.RISK_ANALYSE_HOWBUY.equals(busiEnum)){
                map.put("joinType", "risk_analyse_howbuy");
            }
            domainMap.put("appserialno",forId);
            Map<String, String> param = new HashMap<String, String>();
            param.put("appserialno", forId);
            CmConscustsurveyrec rec = cmConscustsurveyrecService.getCmConscustsurveyrec(param);
            String conscustno = "";
            String investtype = "";
            if (rec != null) {
                conscustno = rec.getConscustno();
                domainMap.put("singdate", rec.getSingdate());
            }
            if (crm.howbuy.base.utils.StringUtil.isNotNullStr(conscustno)) {
                param.clear();
                param.put("conscustno", conscustno);
                Conscust cust = conscustService.getConscust(conscustno);

                if (cust != null) {
                    investtype = cust.getInvsttype();
                    map.put("investtype",investtype);
                    if(crm.howbuy.base.utils.StringUtil.isNotNullStr(cust.getIdnoCipher())){
                        cust.setIdno(decryptSingleFacade.decrypt(cust.getIdnoCipher()).getCodecText());
                    }
                    if(crm.howbuy.base.utils.StringUtil.isNotNullStr(cust.getAddrCipher())){
                        cust.setAddr(decryptSingleFacade.decrypt(cust.getAddrCipher()).getCodecText());
                    }
                    if(crm.howbuy.base.utils.StringUtil.isNotNullStr(cust.getEmailCipher())){
                        cust.setEmail(decryptSingleFacade.decrypt(cust.getEmailCipher()).getCodecText());
                    }

                    domainMap.put("cust", cust);
                    param.clear();
                    param.put("surveyserialno", forId);
                    List<CmConscustsurveyanswer> answerList = cmConscustsurveyanswerService.listCmConscustsurveyanswer(param);
                    Map<String, String> ansmap = new HashMap<String, String>();
                    Map<String, String> erroransmap = new HashMap<String, String>();
                    for (CmConscustsurveyanswer obj : answerList) {
                        ansmap.put(obj.getQuestionid(), obj.getAcode());
                        if ("0".equals(obj.getErrorFlag())) {
                            erroransmap.put(obj.getQuestionid(), obj.getErrorFlag());
                        }
                    }
                    domainMap.put("ansmap", ansmap);
                    domainMap.put("errmap", erroransmap);
                    QueryKycExamDtlRequest req = new QueryKycExamDtlRequest();
                    req.setOutletCode("CRM");
                    //好臻 或  好买
                    QueryKycExamDtlResponse res = new QueryKycExamDtlResponse();
                    if (StaticVar.INVST_TYPE_PERSONAL.equals(investtype)) {
                        if(CounterBusiEnum.RISK_ANALYSE_HZ.equals(busiEnum)){
                            req.setDisCode(DisChannelCodeEnum.HZ.getCode());
                        }else if (CounterBusiEnum.RISK_ANALYSE_HOWBUY.equals(busiEnum)){
                            req.setDisCode(DisChannelCodeEnum.HOWBUY.getCode());
                        }
                        req.setExamType(ExamType.HIGH_END.getValue());
                        req.setInvstType(UserTypeEnum.PERSONAL.getValue());
                        log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(req));
                        res = queryKycExamDtlFacade.execute(req);
                        log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(res));
                        ExamInfoBean domain = res.getExamInfoBean();
                        if (domain != null) {
                            domainMap.put("domain", domain);
                            domainMap.put("idtypeval", constantCache.getVal("idtype", cust.getIdtype()));
                            domainMap.put("qcount", domain.getQuestions().size());
                            map.put("domain",domainMap);
                            return new ModelAndView("edit".equals(type) ? "/joinclub/editJoinClubOrder": "/joinclub/viewJoinClubOrder",  map);
                        } else {
                            log.info("queryKycExamDtlFacade.execute(req)接口未返回任何个人问卷信息！");
                        }
                    } else {
                        if(CounterBusiEnum.RISK_ANALYSE_HZ.equals(busiEnum)){
                            req.setDisCode(DisChannelCodeEnum.HZ.getCode());
                        }else if (CounterBusiEnum.RISK_ANALYSE_HOWBUY.equals(busiEnum)){
                            req.setDisCode(DisCodeEnum.FOF.getCode());
                        }
                        //获取机构问卷
                        req.setExamType(ExamType.INSTITUTION.getValue());
                        req.setInvstType(UserTypeEnum.INSTITUTION.getValue());
                        log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(req));
                        res = queryKycExamDtlFacade.execute(req);
                        log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(res));
                        ExamInfoBean domain = res.getExamInfoBean();
                        if (domain != null) {
                            domainMap.put("domain", domain);

                            // 根据投资者类型不同，展示不同的证件类型
                            if (StaticVar.INVST_TYPE_ORG.equals(cust.getInvsttype())) {
                                domainMap.put("idtypeval", constantCache.getVal("IDTypesOfInstNew", cust.getIdtype()));
                            } else if (StaticVar.INVST_TYPE_FUND.equals(cust.getInvsttype())) {
                                domainMap.put("idtypeval", constantCache.getVal("IDTypesOfFund", cust.getIdtype()));
                            }else if (StaticVar.INVST_TYPE_PRODUCT.equals(investtype)) {
                                domainMap.put("idtypeval", constantCache.getVal("IDTypesOfProduct", cust.getIdtype()));
                            }
                            // 设置题目总数
                            domainMap.put("qcount", domain.getQuestions().size());
                            map.put("domain",domainMap);
                            return new ModelAndView("edit".equals(type) ? "/joinclub/editJoinClubOrder": "/joinclub/viewJoinClubOrder",  map);

                        } else {
                            log.info("queryKycExamDtlFacade.execute(req)接口未返回任何机构问卷信息！");
                        }
                    }
                }
            }
        }

        //业务属性：非交易类、
        //业务属性：交易类中：  交易-修改分红方式
        if (CounterBusiProidEnum.NON_TRADE.equals(busiProidEnum) || CounterBusiProidEnum.NON_TRADE_RELATION.equals(busiProidEnum)
                || CounterBusiEnum.BONUS_TYPE_MODIFY.equals(busiEnum)) {
            map.put("type", "notrade");
            Map<String, Object> newparam = new HashMap<String, Object>();
            newparam.put("orderid", orderInfo.getForId());

            List<CmRelationAccountSub> relationsublist = cmRelationAccountSubService.listRelationAccountSub(newparam);

            if(!relationsublist.isEmpty()){
                map.put("type", "notradeandsub");
                for (CmRelationAccountSub relationaccountsub:relationsublist){
                    relationaccountsub.setRelationstr(constantCache.getVal("relatedAccountRole", relationaccountsub.getRelation()));
                    relationaccountsub.setRelationstatestr(constantCache.getVal("relationstate", relationaccountsub.getRelationstate()));
                }
                domainMap.put("relationsublist", relationsublist);
            }

            // busiCode= 54- 好买香港-开户入金 57-好买香港-现金存入   存储特殊属性
            // 历史逻辑  设计问题。此处应该以特殊业务来区分 是否赋值， 而不是直接判断非空。 2024年4月16日重构
            if (CounterBusiEnum.INCOME_PAYMENT_HK==busiEnum || CounterBusiEnum.CASH_DEPOSIT_HK==busiEnum) {
                if(StringUtils.isNotBlank(specialBusiParam)){
                    HkDepositParamDto depositDto=JSON.parseObject(specialBusiParam,HkDepositParamDto.class);
                    //银行卡 资金账号
                    domainMap.put("hkCpAcctNo", depositDto.getHkCpAcctNo());
                    //银行卡 展示名称
                    domainMap.put("bankAcctMask",depositDto.getBankAcctMask());
                    domainMap.put("curCode", depositDto.getCurCode());
                    domainMap.put("curCodeDesc", constantCache.getVal("currencys", depositDto.getCurCode()));
                    domainMap.put("amount", depositDto.getAmount());
                    domainMap.put("refNo", depositDto.getRefNo());
                    domainMap.put("tradeChannel", depositDto.getTradeChannel());
                    domainMap.put("hkCustNo", depositDto.getHkCustNo());
                    domainMap.put("memo", depositDto.getMemo());
                }
            }

            //58-好买香港-储蓄罐协议签署  59-好买香港-储蓄罐底层变更   60-好买香港-储蓄罐协议终止  字段使用
            if(CounterUtil.CXG_RELATED_BUSI_LIST.contains(busiEnum)){
                if(StringUtils.isNotBlank(specialBusiParam)){
                    HkCxgParamDto paramDto=JSON.parseObject(specialBusiParam,HkCxgParamDto.class);
                    //前端只展示 1 个产品
                    if(CollectionUtils.isNotEmpty(paramDto.getCxgFundList())){
                        domainMap.put("piggyFundCode", paramDto.getCxgFundList().get(0).getPiggyFundCode());
                        domainMap.put("piggyFundName", paramDto.getCxgFundList().get(0).getPiggyFundName());
                    }
                }
            }

            map.put("domain",domainMap);
            return new ModelAndView(url,  map);
        }

        //其他交易类的大类
        List<CounterBusiProidEnum> tradeBusiProdList= Lists.newArrayList(CounterBusiProidEnum.TRADE_BUY,CounterBusiProidEnum.TRADE_REDEEM,CounterBusiProidEnum.TRADE_RECALL);
        if(tradeBusiProdList.contains(busiProidEnum)){
            map.put("type", "trade");

            map.put("domain",domainMap);
            return new ModelAndView(url, "map", map);
        }
        log.error("类型：{} 未做处理！ 订单id为 ：{} ",busiProidEnum.getDesc(),orderInfo.getId());
        map.put("domain",domainMap);
        return new ModelAndView(url, map);
    }

    /**
     * 提交上传文件
     * 资料管理订单 编辑 提交
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/uploadeditorderfile", method = RequestMethod.POST)
    public BaseResponse<String>  uploadeditorderfile(HttpServletRequest request) {
        //request参数
        String orderId = request.getParameter("orderid");
        String delfileids = request.getParameter("delfileids");
        //待删除文件列表
        List<String> deleteFileIdList = Lists.newArrayList();
        if(StringUtil.isNotBlank(delfileids)){
            delfileids = delfileids.replaceFirst("#", "");
            String [] delids = delfileids.split("#");
            for(String delid : delids){
                deleteFileIdList.add(delid.split("_")[2]);
            }
        }

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> files = multipartRequest.getFiles("filelist[]");

        EditCmCounterOrderRequest editFileVo=new EditCmCounterOrderRequest();

        //获取 待编辑的 资料order
        CmCounterOrderDto  editOrder=getOrderById(orderId);
        String custNo=editOrder.getConscustno();
        CounterBusiEnum busiEnum=CounterBusiEnum.getEnum(editOrder.getBusiId());

        // busiCode= 54- 好买香港-开户入金 57-好买香港-现金存入   存储特殊属性
        // 历史逻辑  设计问题。此处应该以特殊业务来区分 是否赋值， 而不是直接判断非空。 2024年4月16日重构
        if (CounterBusiEnum.INCOME_PAYMENT_HK==busiEnum ||
                CounterBusiEnum.CASH_DEPOSIT_HK==busiEnum) {
            HkDepositParamDto hkDepositParamDto=new HkDepositParamDto();
            hkDepositParamDto.setAmount(request.getParameter("amount"));
            hkDepositParamDto.setMemo(request.getParameter("memo"));
            hkDepositParamDto.setCurCode(request.getParameter("curCode"));
            hkDepositParamDto.setHkCpAcctNo(request.getParameter("hkCpAcctNo"));
            hkDepositParamDto.setRefNo(request.getParameter("refNo"));
            hkDepositParamDto.setTradeChannel(TradeChannelEnum.CRM_PC.getCode());
            // 根据投顾客户号获取香港号
            HkConscustVO hkConscustVO = hkConscustService.queryHkCustInfoByCustNo(custNo);
            String hkCustNo = hkConscustVO==null?"":hkConscustVO.getHkTxAcctNo();
            hkDepositParamDto.setHkCustNo(hkCustNo);
            editFileVo.setSpecialBusiParam(JSON.toJSONString(hkDepositParamDto));
        }
        //58-好买香港-储蓄罐协议签署  59-好买香港-储蓄罐底层变更   60-好买香港-储蓄罐协议终止  字段使用
        if(CounterUtil.CXG_RELATED_BUSI_LIST.contains(busiEnum)){
            HkCxgParamDto paramDto=new HkCxgParamDto();
            paramDto.setCxgFundList(Lists.newArrayList());
            CxgDetailDto cxgDetailDto=new CxgDetailDto();
            cxgDetailDto.setPiggyFundCode(request.getParameter("piggyFundCode"));
            cxgDetailDto.setPiggyFundName(request.getParameter("piggyFundName"));
            paramDto.getCxgFundList().add(cxgDetailDto);
            paramDto.setTradeChannel(TradeChannelEnum.CRM_PC.getCode());
            editFileVo.setSpecialBusiParam(JSON.toJSONString(paramDto));
        }

        //6-香港-交易打款凭证
        if (CounterBusiEnum.TRADE_HK_PAYMENT_VOUCHER==busiEnum) {
            HkVoucherParamDto paramDto=new HkVoucherParamDto();
            //TODO: 此处参数 需要更多
            paramDto.setRemark(request.getParameter("paymentVoucherRemark"));
            paramDto.setTradeChannel(TradeChannelEnum.CRM_PC.getCode());
            paramDto.setCurrency(request.getParameter("currency"));
            String remitAmtStr = request.getParameter("remitAmt");
            paramDto.setRemitAmt(new BigDecimal(remitAmtStr).multiply(BIG_DECIMAL_1W).setScale(4, RoundingMode.HALF_UP));
            paramDto.setHkCpAcctNo(request.getParameter("hkCpAcctNo"));
            editFileVo.setSpecialBusiParam(JSON.toJSONString(paramDto));
        }

        editFileVo.setOrderId(orderId);
        editFileVo.setOperatorNo(getLoginUserId(request));
        editFileVo.setDeleteFileIdList(deleteFileIdList);

        //文件处理
        Map<String, List<String>>  fileMap=processCounterFile (files);
        editFileVo.setAddFileMap(fileMap);

        return getPostEntityByJsonObject(CrmTradeServerPathConstant.EXECUTE_EDIT,
                editFileVo,
                new ParameterizedTypeReference<BaseResponse<String>>(){});

    }


    @RequestMapping(value = "/previewCounterImg")
    public String previewCounterImg(HttpServletRequest request) {
        String docId = request.getParameter("docId");
        if (StringUtils.isNotBlank(docId)) {
            request.setAttribute("docid", docId);
            try {
                //插入日志
//                this.creatLog(user.getUserId(), docfile.getFid(), "'" + docfile.getName() + "." + docfile.getSufname() + "'", StaticVar.OPT_TYPE_FILE_VIEW);

            } catch (Exception e) {
                log.error("previewCounterImg error", e);
            }
        }

        return "/counter/previewCounterImg";
    }

    @ResponseBody
    @RequestMapping("/getCounterFileStream")
    public void getCounterFileStream(HttpServletRequest request, HttpServletResponse response) {
        String docId = request.getParameter("docId");
        String userId=getLoginUserId();
        Map<String, String> param = new HashMap<String, String>();
        param.put("id", docId);
        CmCounterOrderfileFileStreamInfo fileStreamInfo = null;
        BaseResponse<CmCounterOrderfileFileStreamInfo> httpRsp =
                getPostEntityByMap(CrmTradeServerPathConstant.GET_CM_COUNTER_ORDER_FILE_STREAM,
                        param,
                        new ParameterizedTypeReference<BaseResponse<CmCounterOrderfileFileStreamInfo>>() {
                        });
        if (httpRsp.isSuccess()) {
            fileStreamInfo = httpRsp.getData();
        }else{
            fileStreamInfo=new CmCounterOrderfileFileStreamInfo();
        }

        byte[] bytes= fileStreamInfo.getFilebyte();
        ServletOutputStream outputStream = null;
        try {
            response.setContentType("application/octet-stream");
            //输出文件流
            outputStream = response.getOutputStream();
            outputStream.write(bytes);
        }catch (Exception e){
            log.error("文档预览失败，fileName：{}, useId：{}", fileStreamInfo.getFilename(), userId, e);
        }finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("CrmCounterFileOuterService>>>downloadPdf 关闭文件流异常", e);
                }
            }
        }

    }


}