package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.howbuy.common.date.DateUtil;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.base.privatetrade.ImpTypeEnum;
import com.howbuy.crm.base.privatetrade.TradeBusiCodeEnum;
import com.howbuy.crm.base.privatetrade.TradeImportAuditStatusEnum;
import com.howbuy.crm.hb.domain.conscust.CustAcctAttrVo;
import com.howbuy.crm.hb.domain.prosale.Prebookproductinfo;
import com.howbuy.crm.hb.domain.prosale.preandtradeimport.CmPreAndTradeExportViewDto;
import com.howbuy.crm.hb.domain.prosale.preandtradeimport.CmPreAndTradeImportViewDto;
import com.howbuy.crm.hb.listener.PreAndTradeExcelListener;
import com.howbuy.crm.hb.listener.PreAndTradeExcelReadInfo;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.prosale.CustprivatefundService;
import com.howbuy.crm.hb.service.prosale.PreAndTradeHbFactory;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.privatetrade.dto.CmPreTradeImport;
import com.howbuy.crm.privatetrade.service.CmPreTradeBusinessService;
import com.howbuy.crm.privatetrade.service.CmPreTradeImportService;
import com.howbuy.crm.privatetrade.vo.CmPreTradeImpAudtiVo;
import com.howbuy.crm.privatetrade.vo.CmPreTradeImportPageVo;
import com.howbuy.crm.util.prosale.PreAndTradeImpUtil;
import crm.howbuy.base.db.PageResult;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (产品预约-直销交易 导入处理 controler)
 * @date 2023年3月17日 17:38
 * @since JDK 1.8
 */
@Controller
@RequestMapping(value = "/prosale")
public class PreAndTradeImportController  extends BaseController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());



    /**
     * 模板下载 名称
     */
    private static final String DOWNLOAD_FILE_NAME = "预约+交易记录导入模板.xls";
    /**
     * 模板存放的名称
     */
    private final String MODEL_FILE_NAME = "crmpreandtradeimp.xls";

    @Autowired
    private CustprivatefundService custprivatefundService;
    @Autowired
    private PreAndTradeHbFactory preAndTradeHbFactory;
    @Autowired
    private ConscustService conscustService;
    @Autowired
    private CmPreTradeImportService cmPreTradeImportService;
    @Autowired
    private PrebookproductinfoService prebookproductinfoService;
    @Autowired
    private CmPreTradeBusinessService cmPreTradeBusinessService;

    @RequestMapping(value = "/preandtradeimport.do", method = RequestMethod.GET)
    public ModelAndView preAndTradeimport() {
        return new ModelAndView("/prosale/preAndTradeImport");
    }


    /**
     * @api {GET} /prosale/downloadpreandtradetemplate.do 下载Excel导入模板
     * @apiVersion 1.0.0
     * @apiGroup PreAndTradeImportController
     * @apiName downloadPreAndTradeTemplate
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "7"
     */
    @RequestMapping(value = "/downloadpreandtradetemplate.do", method = RequestMethod.GET)
    public String downloadPreAndTradeTemplate(HttpServletRequest request, HttpServletResponse response) {
        return dowmloadTemplate(MODEL_FILE_NAME,DOWNLOAD_FILE_NAME,request,response);
    }


    /**
     * @api {POST} /prosale/listpreandtradebypage.do listPreAndTradeByPage 分页查询直销交易记录列表
     * @apiVersion 1.0.0
     * @apiGroup PreAndTradeImportController
     * @apiName listPreAndTradeByPage
     * @apiParam (请求参数) {Array} impIdList
     * @apiParam (请求参数) {Number} preId
     * @apiParam (请求参数) {String} custNo
     * @apiParam (请求参数) {String} custName
     * @apiParam (请求参数) {String} prodCode
     * @apiParam (请求参数) {String} prodName
     * @apiParam (请求参数) {String} creator
     * @apiParam (请求参数) {Number} beginCreateTimestamp
     * @apiParam (请求参数) {Number} endCreateTimestamp
     * @apiParam (请求参数) {String} modifier
     * @apiParam (请求参数) {Number} beginModifyTimestamp
     * @apiParam (请求参数) {Number} endModifyTimestamp
     * @apiParam (请求参数) {String} auditor
     * @apiParam (请求参数) {Number} beginAuditTimestamp
     * @apiParam (请求参数) {Number} endAuditTimestamp
     * @apiParam (请求参数) {String} auditStatus
     * @apiParam (请求参数) {String} recStat
     * @apiParam (请求参数) {Array} busiCodeList
     * @apiParam (请求参数) {Array} impTypeList
     * @apiParam (请求参数) {Number} page
     * @apiParam (请求参数) {Number} rows
     * @apiParamExample 请求参数示例
     * preId=8414&custNo=e&creator=DrPDb&busiCodeList=R&beginAuditTimestamp=1063504060191&impIdList=vnjTbjobK&modifier=BphLKyR5S&auditor=C076fHco&endModifyTimestamp=696782480568&custName=DcM7iq73&rows=3707&impTypeList=tp4&beginCreateTimestamp=2231552831437&beginModifyTimestamp=2054487071351&prodCode=4mn20Rk&endCreateTimestamp=1762296614538&prodName=CuuVib9xn2&endAuditTimestamp=2331803660597&auditStatus=gNB8S3x&page=3894&recStat=zzx
     * @apiSuccess (响应结果) {Array} rows
     * @apiSuccess (响应结果) {String} rows.impId
     * @apiSuccess (响应结果) {Number} rows.preId
     * @apiSuccess (响应结果) {String} rows.custNo
     * @apiSuccess (响应结果) {String} rows.custName
     * @apiSuccess (响应结果) {String} rows.prodCode
     * @apiSuccess (响应结果) {String} rows.prodName
     * @apiSuccess (响应结果) {String} rows.preType
     * @apiSuccess (响应结果) {String} rows.currency
     * @apiSuccess (响应结果) {Number} rows.appAmt
     * @apiSuccess (响应结果) {String} rows.paymentType
     * @apiSuccess (响应结果) {Number} rows.appVol
     * @apiSuccess (响应结果) {String} rows.redeemDirection
     * @apiSuccess (响应结果) {String} rows.expectPayDt
     * @apiSuccess (响应结果) {String} rows.expectTradeDt
     * @apiSuccess (响应结果) {String} rows.realPayDt
     * @apiSuccess (响应结果) {Number} rows.realPayAmt
     * @apiSuccess (响应结果) {Number} rows.realPayFee
     * @apiSuccess (响应结果) {String} rows.ackDt
     * @apiSuccess (响应结果) {Number} rows.nav
     * @apiSuccess (响应结果) {Number} rows.ackAmt
     * @apiSuccess (响应结果) {Number} rows.ackFee
     * @apiSuccess (响应结果) {String} rows.rateDt
     * @apiSuccess (响应结果) {Number} rows.balanceFactor
     * @apiSuccess (响应结果) {String} rows.haiwai
     * @apiSuccess (响应结果) {String} rows.message
     * @apiSuccess (响应结果) {String} rows.impSummary
     * @apiSuccess (响应结果) {String} rows.creator
     * @apiSuccess (响应结果) {Number} rows.createTimestamp
     * @apiSuccess (响应结果) {String} rows.modifier
     * @apiSuccess (响应结果) {Number} rows.modifyTimestamp
     * @apiSuccess (响应结果) {String} rows.auditor
     * @apiSuccess (响应结果) {Number} rows.auditTimestamp
     * @apiSuccess (响应结果) {String} rows.auditStatus
     * @apiSuccess (响应结果) {String} rows.recStat
     * @apiSuccess (响应结果) {String} rows.auditAdvice
     * @apiSuccess (响应结果) {String} rows.busiCode
     * @apiSuccess (响应结果) {String} rows.impType
     * @apiSuccess (响应结果) {Number} rows.ackVol
     * @apiSuccess (响应结果) {Number} total
     * @apiSuccessExample 响应结果示例
     * {"total":5289,"rows":[{"expectPayDt":"SGu","preId":3758,"appAmt":5287.189622627611,"preType":"KlK","realPayFee":5139.3164665620125,"rateDt":"C9idVPtnCp","realPayAmt":5289.861530760674,"modifier":"w","paymentType":"qXQaceJm","redeemDirection":"3I","haiwai":"PH","createTimestamp":1409902218636,"modifyTimestamp":1489792709362,"ackAmt":7040.400941358455,"appVol":3851.8262919495505,"ackFee":7693.690324440992,"prodName":"0C","currency":"9dRGhyx","balanceFactor":5472.513860574098,"ackVol":7534.507687669973,"custNo":"hV","nav":5521.587482112364,"creator":"ziQ486Mb","auditTimestamp":3724994811139,"impType":"vF","busiCode":"Zd18EwqAF","auditor":"f","custName":"ROTwK","expectTradeDt":"8","message":"YU3h0","prodCode":"IXVmAH6","auditAdvice":"Th3GCNgbxx","realPayDt":"zkhU","auditStatus":"lpcd","ackDt":"xCVKglgSJ","impId":"L5dpDOZ","recStat":"ze0CBNkcy","impSummary":"Jp6LWC4jiM"}]}
     */
    @RequestMapping(value = "/listpreandtradebypage.do")
    @ResponseBody
    public PageResult<CmPreTradeImport> listPreAndTradeByPage(HttpServletRequest request, CmPreTradeImportPageVo vo) {
        // 返回查询结果
        PageResult<CmPreTradeImport> pageData = new PageResult<>();
        try {
            List<String> gdfxcplist = (List<String>) request.getSession().getAttribute("gdfxcplist");
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(gdfxcplist) && gdfxcplist.size() == 1 && gdfxcplist.contains("4")) {
                vo.setSfxg("1");
            }
            PageResult<CmPreTradeImport> page= cmPreTradeImportService.selectPageByPageVo(vo);
            pageData.setRows(page.getRows());
            pageData.setTotal(page.getTotal());
        } catch (Exception e) {
            log.error("查询异常！", e);
        }
        return pageData;
    }


    /**
     * @api {POST} /prosale/batchauditpreandtrade.do 批量审核
     * @apiVersion 1.0.0
     * @apiGroup PreAndTradeImportController
     * @apiName batchAuditPreAndTrade
     * @apiParam (请求体) {Array} impIdList
     * @apiParam (请求体) {String} operator
     * @apiParam (请求体) {String} auditStatus
     * @apiParam (请求体) {String} auditAdvice
     * @apiParamExample 请求体示例
     * {"auditAdvice":"KcG8EVqj","impIdList":["8LaGxaQ"],"auditStatus":"M0ao","operator":"YO5QotPrK"}
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"0000","returnMsg":"WiNW","returnObject":"BC5Bs2222","returnList":["tW"]}
     */
    @RequestMapping("/batchauditpreandtrade.do")
    @ResponseBody
    public ReturnMessageDto<String> batchAuditPreAndTrade(@RequestBody CmPreTradeImpAudtiVo batchUpdateVo,
                                                           HttpServletRequest request) {
        try {
            batchUpdateVo.setOperator(getLoginUserId(request));
            return cmPreTradeBusinessService.audit(batchUpdateVo);
        } catch (Exception e) {
            log.error("批量审核异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }


    /**
     * @api {POST} /prosale/batchcancelpreandtrade.do 批量作废
     * @apiVersion 1.0.0
     * @apiGroup PreAndTradeImportController
     * @apiName batchCancelPreAndTrade
     * @apiParam (请求体) {Array} impIdList
     * @apiParam (请求体) {String} operator
     * @apiParam (请求体) {String} auditStatus
     * @apiParam (请求体) {String} auditAdvice
     * @apiParamExample 请求体示例
     * {"auditAdvice":"XKxHk9sT","impIdList":["BHax"],"auditStatus":"P8T04n9sXj","operator":"sP"}
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"lrCi","returnMsg":"dOnfxNpT","returnObject":"g","returnList":["V"]}
     */
    @RequestMapping("/batchcancelpreandtrade.do")
    @ResponseBody
    public ReturnMessageDto<String> batchCancelPreAndTrade(@RequestBody CmPreTradeImpAudtiVo batchUpdateVo,
                                                          HttpServletRequest request) {
        try {
            return cmPreTradeBusinessService.cancel(batchUpdateVo.getImpIdList(),getLoginUserId(request));
        } catch (Exception e) {
            log.error("批量作废异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }


    /**
     * @api {GET} /prosale/editpreandtrade.do 跳转到编辑页面
     * @apiVersion 1.0.0
     * @apiGroup PreAndTradeImportController
     * @apiName editPreAndTrade
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "I3WXB5"
     */
    @RequestMapping(value = "/editpreandtrade.do", method = RequestMethod.GET)
    public String editPreAndTrade(HttpServletRequest request){
        CmPreTradeImport record=cmPreTradeImportService.selectByPrimaryKey(request.getParameter("impId"));
        request.setAttribute("record", record);

        //翻译信息
        request.setAttribute("busiCodeStr", TradeBusiCodeEnum.getDescription(record.getBusiCode()));
        request.setAttribute("impTypeStr", ImpTypeEnum.getDescription(record.getImpType()));
        return "/prosale/editPreAndTrade";
    }


    @RequestMapping(value = "/auditpreandtrade.do", method = RequestMethod.GET)
    public ModelAndView auditPreAndTrade(HttpServletRequest request){
        CmPreTradeImport record=cmPreTradeImportService.selectByPrimaryKey(request.getParameter("impId"));
        request.setAttribute("record", PreAndTradeImpUtil.transferToViewDto(record));
        //翻译信息
        return new ModelAndView("/prosale/auditPreAndTrade");
    }


    /**
     * @api {POST} /prosale/updatepreandtrade.do 更新提交 保存的 修改记录
     * @apiVersion 1.0.0
     * @apiGroup PreAndTradeImportController
     * @apiName updatePreAndTrade
     * @apiParam (请求参数) {String} impId
     * @apiParam (请求参数) {Number} preId
     * @apiParam (请求参数) {String} custNo
     * @apiParam (请求参数) {String} custName
     * @apiParam (请求参数) {String} prodCode
     * @apiParam (请求参数) {String} prodName
     * @apiParam (请求参数) {String} preType
     * @apiParam (请求参数) {String} currency
     * @apiParam (请求参数) {Number} appAmt
     * @apiParam (请求参数) {String} paymentType
     * @apiParam (请求参数) {Number} appVol
     * @apiParam (请求参数) {String} redeemDirection
     * @apiParam (请求参数) {String} expectPayDt
     * @apiParam (请求参数) {String} expectTradeDt
     * @apiParam (请求参数) {String} realPayDt
     * @apiParam (请求参数) {Number} realPayAmt
     * @apiParam (请求参数) {Number} realPayFee
     * @apiParam (请求参数) {String} ackDt
     * @apiParam (请求参数) {Number} nav
     * @apiParam (请求参数) {Number} ackAmt
     * @apiParam (请求参数) {Number} ackFee
     * @apiParam (请求参数) {String} rateDt
     * @apiParam (请求参数) {Number} balanceFactor
     * @apiParam (请求参数) {String} haiwai
     * @apiParam (请求参数) {String} message
     * @apiParam (请求参数) {String} impSummary
     * @apiParam (请求参数) {String} creator
     * @apiParam (请求参数) {Number} createTimestamp
     * @apiParam (请求参数) {String} modifier
     * @apiParam (请求参数) {Number} modifyTimestamp
     * @apiParam (请求参数) {String} auditor
     * @apiParam (请求参数) {Number} auditTimestamp
     * @apiParam (请求参数) {String} auditStatus
     * @apiParam (请求参数) {String} recStat
     * @apiParam (请求参数) {String} auditAdvice
     * @apiParam (请求参数) {String} busiCode
     * @apiParam (请求参数) {String} impType
     * @apiParam (请求参数) {Number} ackVol
     * @apiParamExample 请求参数示例
     * expectPayDt=qeURLTEVh&preId=344&appAmt=1576.86013662778&preType=75anIAPKpZ&realPayFee=2738.547925181972&rateDt=dob&realPayAmt=8018.53953400928&modifier=ZoV&paymentType=wCCiQCVpij&redeemDirection=F76Rl1&haiwai=9ZaIE&createTimestamp=1547088276129&modifyTimestamp=637983127643&ackAmt=9793.896815185868&appVol=3516.709153363835&ackFee=2370.0463059191457&prodName=fQWZ&currency=FXvBD8r1&balanceFactor=258.20896899677814&ackVol=5353.787450021075&custNo=0QzKQ&nav=2304.042369501622&creator=K2Lf3mIuQ&auditTimestamp=1105976936700&impType=pRe&busiCode=T55xrh&auditor=5b5fgSX4&custName=hOo4KVHq&expectTradeDt=q&message=NwTOilq&prodCode=J3s&auditAdvice=feVqTeR3A&realPayDt=PBR0wJ&auditStatus=rOX2uI&ackDt=GQOC1e&impId=Wd&recStat=Mg&impSummary=pgCMdKS
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"eu6OH5gXQo","returnMsg":"fkbj9cs","returnObject":"1zj6D7","returnList":["N7m"]}
     */
    @RequestMapping(value = "/updatepreandtrade.do",method =RequestMethod.POST )
    @ResponseBody
    public ReturnMessageDto<String> updatePreAndTrade(CmPreTradeImport record,HttpServletRequest request){
        try {

            CmPreTradeImport existRecord= cmPreTradeImportService.selectByPrimaryKey(record.getImpId());

            if(!TradeImportAuditStatusEnum.AUDIT_REJECT.getCode().equals(existRecord.getAuditStatus())){
                  return  ReturnMessageDto.fail("审核状态为：3-审核不通过 才可以编辑！");
            }


            //不允许修改的对象
            record.setBusiCode(existRecord.getBusiCode());
            record.setImpId(existRecord.getImpId());
            record.setProdCode(existRecord.getProdCode());
            record.setCustNo(existRecord.getCustNo());
//            record.setPreType(existRecord.getPreType());
            record.setImpType(existRecord.getImpType());
            record.setPreId(existRecord.getPreId());
            record.setMainCallId(existRecord.getMainCallId());

            //再去校验
            String errorMsg= preAndTradeHbFactory.validateSingleForTradeData(record);
            if(!StringUtil.isEmpty(errorMsg)){
                return ReturnMessageDto.fail(errorMsg);
            }
            record.setModifier(getLoginUserId(request));
            record.setAuditStatus(TradeImportAuditStatusEnum.AUDIT_WAIT.getCode());

            //校验 是否合规
            cmPreTradeImportService.updateTradeInfoAttribule(record);
            return ReturnMessageDto.ok();
        } catch (Exception e) {
            log.error("更新导入数据异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }


    /**
     * 校验 预约Id
     * @param preId
     * @return
     */
     private String  validPreId(BigDecimal preId){
         StringBuilder errorMsg=new StringBuilder();
         if(preId!=null){
             Prebookproductinfo preInfo=prebookproductinfoService.getPrebookproductinfoById(String.valueOf(preId));
             if(preInfo==null){
                 errorMsg.append(String.format("预约ID:%s 不存在！",preId));
             }
         }
         return errorMsg.toString();
     }


    /**
     * @api {POST} /prosale/uploadpreandtrade.do 上传文件
     * @apiVersion 1.0.0
     * @apiGroup PreAndTradeImportController
     * @apiName uploadPreAndTrade
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"omvyCO3","returnMsg":"4QyEqM","returnObject":"PQ2zl","returnList":["n6umu"]}
     */
    @RequestMapping(value = "/uploadpreandtrade.do", method = RequestMethod.POST)
    @ResponseBody
    public   ReturnMessageDto<String> uploadPreAndTrade(HttpServletRequest request) {
        String userId=getLoginUserId(request);
        String  busiCode=request.getParameter("busiCode");
        String  impType=request.getParameter("impType");

        TradeBusiCodeEnum busiCodeEnum=TradeBusiCodeEnum.getEnum(busiCode);
        if(busiCodeEnum==null){
            return ReturnMessageDto.fail("非法交易类别！");
        }
        ImpTypeEnum impTypeEnum=ImpTypeEnum.getEnum(impType);
        if(impTypeEnum==null){
            return ReturnMessageDto.fail("非法申赎类型！");
        }

        if(!PreAndTradeImpUtil.getValidImpTyleList(busiCodeEnum).contains(impTypeEnum)){
            return ReturnMessageDto.fail(String.format("交易类别:%s 不支持申赎类型:%s",busiCodeEnum.getDescription(),impTypeEnum.getDescription()));
        }

        try {
            // 转型为MultipartHttpRequest：
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            // 获得文件：
            MultipartFile file = multipartRequest.getFile("file");
            if (StringUtil.isNull(file)) {
                return ReturnMessageDto.fail("请上传文件");
            }

            Consumer<List<CmPreAndTradeImportViewDto>> persistConsumer = list -> {
                List<CmPreTradeImport> saveList = Lists.newArrayList();
                list.stream().forEach(data -> {
                    saveList.add(PreAndTradeImpUtil.transfer(data));
                });
                // 香港客户号转投顾客户号入库
                buildHkAcctNo(saveList);
                cmPreTradeImportService.batchInsertList(saveList);
            };

            PreAndTradeExcelListener listener=new PreAndTradeExcelListener(busiCodeEnum,
                    impTypeEnum,
                    userId,
                    data->preAndTradeHbFactory.validateSingleImpData(data),
                    persistConsumer);
            EasyExcel.read(file.getInputStream(), CmPreAndTradeImportViewDto.class,listener).sheet().headRowNumber(1).doRead();

            PreAndTradeExcelReadInfo readResultInfo=listener.getReadResultInfo();

            List<String>  errorMsgList=readResultInfo.getErrorMsgList();
            if(!CollectionUtils.isEmpty(errorMsgList)){
               return  ReturnMessageDto.fail(String.join("\n",errorMsgList));
            }

        } catch (Exception e) {
            log.error("预约直销交易记录导入报错：" + e.getMessage(), e);
            return ReturnMessageDto.fail("系统异常！");
        }
        return ReturnMessageDto.ok();
    }

    /**
     * 香港客户号转投顾客户号入库
     * @param saveList  结果列表
     */
    private void buildHkAcctNo(List<CmPreTradeImport> saveList) {
        try {
            Map<Integer, List<String>> map = saveList.stream()
                    .map(CmPreTradeImport::getCustNo)
                    .filter(StringUtil::isNotNullStr)
                    .collect(Collectors.groupingBy(String::length));
            List<String> hkList = map.getOrDefault(7, new ArrayList<>());
            List<CustAcctAttrVo> hkAcctNos = conscustService.getAcctInfoByHkAcctNos(hkList);
            Map<String, String> custNoMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(hkAcctNos)) {
                custNoMap = hkAcctNos.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(CustAcctAttrVo::getHkTxAcctNo, CustAcctAttrVo::getCustNo, (v1, v2) -> v1));
            }
            for (CmPreTradeImport anImport : saveList) {
                if (null != anImport && null != anImport.getCustNo() && anImport.getCustNo().length() == 7) {
                    String hkAcctNo = custNoMap.get(anImport.getCustNo());
                    anImport.setCustNo(hkAcctNo);
                }
            }
        } catch (Exception e) {
            log.error("buildHkAcctNo error={}", e.getMessage(), e);
        }
    }

    @RequestMapping("/exportpreandtrade.do")
    public void export(CmPreTradeImportPageVo vo,
                       HttpServletResponse response) throws IOException {

        // 导出数据
        List<CmPreAndTradeExportViewDto> dataList = Lists.newArrayList();
        vo.setPage(1);
        vo.setRows(50000);
        // 查询数据
        PageResult<CmPreTradeImport> page= cmPreTradeImportService.selectPageByPageVo(vo);
        page.getRows().forEach(impData->dataList.add(PreAndTradeImpUtil.transferToViewDto(impData)));

        String fileName="直销预约交易记录_"+ DateUtil.getSystemDate(DateUtil.YYYYMMDDHHMMSS);

        // 设置response头信息
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-disposition", String.format("attachment;filename=%s.xlsx",fileName));
        ServletOutputStream outputStream = response.getOutputStream();

        // 写入Excel文件
        ExcelWriter writer = EasyExcel.write(outputStream, CmPreAndTradeExportViewDto.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("预约直销导入信息").build();
        writer.write(dataList, writeSheet);

        writer.finish();

        outputStream.flush();
        outputStream.close();

     }



}
