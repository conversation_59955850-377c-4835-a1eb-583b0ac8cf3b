package com.howbuy.crm.hb.web.controller.insur;

import com.alibaba.dubbo.common.json.JSON;
import com.alibaba.dubbo.common.json.ParseException;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.crm.hb.domain.insur.*;
import com.howbuy.crm.hb.domain.system.CmConsultantExp;
import com.howbuy.crm.hb.enums.BxCommissionWayEnum;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.insur.*;
import com.howbuy.crm.hb.service.system.CmConsultantExpService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ExcelUtils;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.page.framework.utils.Util;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
import jxl.Sheet;
import jxl.Workbook;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: Controller
 * <AUTHOR> @version 1.0
 * @created 
 */
@Slf4j
@Controller
@RequestMapping(value = "/insur")
public class CmBxPrebookInfoCheckController extends BaseController {
	
	@Autowired
	private CmBxPrebookBuyinfoService cmBxPrebookBuyinfoService;
	
	@Autowired
	private CmBxProductGroupService cmBxProductGroupService;
	
	@Autowired
	private CmBxCompanyService cmBxCompanyService;
	
	@Autowired
	private CmBxChannelService cmBxChannelService;
	
	@Autowired
	private CmBxProductChannelService cmBxProductChannelService;
	
	@Autowired
	private CmBxProductService cmBxProductService;
	
	@Autowired
	private CommonService commonService;
	
	@Autowired
	private CmBxPrebookinfoService cmBxPrebookinfoService;
	
	@Autowired
	private CmBxPrebookSigninfoService cmBxPrebookSigninfoService;
	
	@Autowired
	private CmBxPrebookAnnexService cmBxPrebookAnnexService;
	
	
	@Autowired
    private CmBxPrebookEndpayListService cmBxPrebookEndpayListService;
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Autowired
	private DecryptSingleFacade decryptSingleFacade;
	
	@Autowired
	private PageVisitLogService pageVisitLogService;

	@Autowired
	private CmConsultantExpService cmConsultantExpService;

	@Autowired
	private CmBxEditLogService cmBxEditLogService;

	
	private final String DOWNLOAD_FILE_NAME="导入格式模板.xls";
	
	private final String MODEL_FILE_NAME="importinsurmodel.xls";
	

	/**
	 * 跳转到产品预约管理页面方法
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listinsurcheckprebook.do")
	public ModelAndView listinsurprebook(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/insur/listinsurprebookcheck");
		return modelAndView;
	}
	
	/**
	 * 跳转到产品预约管理页面方法
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listinsurtblprebook.do")
	public ModelAndView listinsurtblprebook(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/insur/listinsurprebooktbl");
		return modelAndView;
	}

	/**
	 * 加载核保管理列表页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listInsurPrebookcheckByPage_json.do")
	public Map<String, Object> listInsurPrebookByPage(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		PageData<CmBxPrebookinfo> pageData = cmBxPrebookinfoService.listCmBxPrebookinfoByPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmBxPrebookinfo> listdata = pageData.getListData();
		//新增所属投顾的投顾名称，和投顾部门
		Map<String, String> cons2OutletMap=ConsOrgCache.getInstance().getCons2OutletMap();
		Map<String, String> allUserMap=ConsOrgCache.getInstance().getAllUserMap();
		for (CmBxPrebookinfo info : listdata) {
			info.setPrestateval(ConstantCache.getInstance().getVal("insurprestate", info.getPrestate()));
			info.setInsurstateval(ConstantCache.getInstance().getVal("insurstate", info.getInsurstate()));
			ConsOrgCache orgcache = ConsOrgCache.getInstance();
			String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
			if("0".equals(uporgcode)){
				info.setUporgname(orgcache.getAllOrgMap().get(info.getOrgcode()));
			}else{
				info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
			}
			info.setPresentorgname(ConsOrgCache.getInstance().getAllOrgMap().get(cons2OutletMap.get(info.getNowconscode())));
			info.setPresentconsname(allUserMap.get(info.getNowconscode()));
			info.setOrgcode(ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
			info.setConscode(ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
			info.setRelation(ConstantCache.getInstance().getVal("insurrelation", info.getRelation()));
			info.setBusitype(ConstantCache.getInstance().getVal("insurbusitype", info.getBusitype()));
			info.setProdtype(ConstantCache.getInstance().getVal("insurprodtype", info.getProdtype()));
			info.setPaystateval(ConstantCache.getInstance().getVal("insurpaystate", info.getPaystate()));
			info.setPayyears(ConstantCache.getInstance().getVal("insurpayyears", info.getPayyears()));
			info.setEnsureyears(ConstantCache.getInstance().getVal("insurensureyears", info.getEnsureyears()));
			info.setBusisourceval(ConstantCache.getInstance().getVal("insurbusisource", info.getBusisource()));
			info.setCollamk(info.getCollamk() == null ? null : info.getCollamk().setScale(2,BigDecimal.ROUND_DOWN));
			if(info.getPayyears() != null && info.getPayyears().contains("至") && info.getInsurage() != null){
                try {
                    info.setPayyears2(Integer.toString(Integer.parseInt(info.getPayyears().replace("至","").replace("岁","")) - info.getInsurage()));
                }catch (NumberFormatException e){
                    info.setPayyears2(info.getPayyears());
                }
            }else {
                info.setPayyears2(info.getPayyears());
            }
			// 查询页面 根据修改记录进行字段标蓝
			List<CmBxEditLog> cmBxEditLogs = cmBxEditLogService.listCmBxEditLog(info.getId()).stream().filter(it -> it.getBuyid().equals(info.getBuyid())).collect(Collectors.toList());;
			List<CmBxEditLog> cmBxEditLogList = mergeCmBxEditLogList(cmBxEditLogs);
			CmBxEditLog cmBxEditLog = new CmBxEditLog();
			if (cmBxEditLogList.size() != 0) {
				cmBxEditLog = cmBxEditLogList.get(0);
			}
			if (null != cmBxEditLog.getPayamt() || null != cmBxEditLog.getEnsureyears()
					|| null != cmBxEditLog.getPayyears() || null != cmBxEditLog.getCaltime()
					|| null != cmBxEditLog.getPassdt() || null != cmBxEditLog.getChannel() || null != cmBxEditLog.getPaydt()) {
				info.setIsEdit(true);
			}
		}
		resultMap.put("rows", listdata);
		return resultMap;
	}
	
	
	/**
	 * (跳转到预约确认界面)
	 */
	@ResponseBody
	@RequestMapping("/prebookconfirm.do")
	public ModelAndView prebookconfirm(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		List<Map<String,Object>> channList = new ArrayList<>();
		List<Map<String,Object>> currList = new ArrayList<>();
		//产品对应的产品组
		List<CmBxProductGroup> listCmBxProductGroup = new ArrayList<CmBxProductGroup>();
		String defaultchann = "";
		String defaultcurr = "";
		
		String id = request.getParameter("id");
		Map<String,Object> param = new HashMap<String,Object>(1);
		param.put("id", id);
		CmBxPrebookinfo cmBxPrebookinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(param);
		param.clear();
		param.put("preid", id);
		param.put("isdel", StaticVar.INSUR_ISDEL_NO);
		List<CmBxPrebookBuyinfo> listCmBxPrebookBuyinfo = cmBxPrebookBuyinfoService.listCmBxPrebookBuyinfo(param);
		List<CmBxPrebookAnnex> listCmBxPrebookAnnex = cmBxPrebookAnnexService.listCmBxPrebookAnnex(param);
		
		Map<String,String> par = new HashMap<String,String> (2);
		par.put("fundcode", cmBxPrebookinfo.getFundcode());
		par.put("isdel", StaticVar.INSUR_ISDEL_NO);
		CmBxProduct cmBxProduct = cmBxProductService.getCmBxProduct(par);
		
		//查询合作渠道
		par.clear();
		par.put("isdel", StaticVar.INSUR_ISDEL_NO);
		par.put("fundcode", cmBxProduct.getFundcode());
        List<CmBxProductChannel> listProChannel = cmBxProductChannelService.listCmBxProductChannel(par);
        defaultchann = cmBxPrebookinfo.getChanncode();
        if(CollectionUtils.isNotEmpty(listProChannel)){
        	Map<String, Object> channmap = new HashMap<>(2);
			channmap.put("id", "");
    		channmap.put("text", "请选择");
	        channList.add(channmap);
        	for(CmBxProductChannel proChannel : listProChannel){
        		par.put("channcode", proChannel.getChanncode());
        		CmBxChannel channel = cmBxChannelService.getCmBxChannel(par);
        		if(channel != null){
        			channmap = new HashMap<>(2);
        			channmap.put("id", channel.getChanncode());
            		channmap.put("text", channel.getChannname());
        	        channList.add(channmap);
        		}
        	}
        }
		
        //查询币种
        LinkedHashMap<String, String> mapcache = ConstantCache.getInstance().getConstantKeyVal("currencys");
        String[] arrcur = cmBxProduct.getCurrency().split(",");
        defaultcurr = cmBxPrebookinfo.getCurrency();
        for(String cur : arrcur){
        	Map<String, Object> curr = new HashMap<>(2);
        	curr.put("id", cur);
        	curr.put("text", mapcache.get(cur));
        	currList.add(curr);
        }
        
        //查询保险公司
        par.clear();
        par.put("compcode", cmBxProduct.getCompcode());
        CmBxCompany cmBxCompany = cmBxCompanyService.getCmBxCompany(par);
        
        if(StringUtil.isNotNullStr(cmBxPrebookinfo.getIdnoCipher())){
			cmBxPrebookinfo.setIdno(decryptSingleFacade.decrypt(cmBxPrebookinfo.getIdnoCipher()).getCodecText());
		}
		if(StringUtil.isNotNullStr(cmBxPrebookinfo.getInsuridnoCipher())){
			cmBxPrebookinfo.setInsuridno(decryptSingleFacade.decrypt(cmBxPrebookinfo.getInsuridnoCipher()).getCodecText());
		}
		Map<String,Object> map = new HashMap<String,Object>(14);
		map.put("id",id);
		map.put("cmBxPrebookinfo",cmBxPrebookinfo);
		map.put("listCmBxPrebookBuyinfo",listCmBxPrebookBuyinfo);
		map.put("listCmBxPrebookAnnex",listCmBxPrebookAnnex);
		map.put("cmBxProduct",cmBxProduct);
		
		map.put("channList",channList);
        map.put("currList", currList);
        map.put("defaultchann",defaultchann);
        map.put("defaultcurr", defaultcurr);
        map.put("cmBxCompany", cmBxCompany);
        
        //查询投保人的姓名、所属部门、所属投顾
        par.clear();
        par.put("conscustno", cmBxPrebookinfo.getConscustno());
        par.put("id", cmBxPrebookinfo.getId().toString());
        PageData<CmBxPrebookinfo> pageData = cmBxPrebookinfoService.listCmBxPrebookinfoByPage(par);
        CmBxPrebookinfo info= pageData.getListData().get(0);
        map.put("consname", ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
        map.put("orgname", ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
        map.put("custname", info.getCustname());
        
        par.clear();
        par.put("isdel", StaticVar.INSUR_ISDEL_NO);
        par.put("fundcode", cmBxProduct.getFundcode());
        //1.产品
  		if("1".equals(cmBxProduct.getProdproper())){
  			CmBxProductGroup group = new CmBxProductGroup();
  			group.setAttfundcode(cmBxProduct.getFundcode());
  			group.setAttfundname(cmBxProduct.getFundname());
  			group.setProdtype(cmBxProduct.getProdtype());
  		    //1主险；2附加险
  			group.setProdproper(cmBxProduct.getProdproper());
  			listCmBxProductGroup.add(group);
  			
  			List<CmBxProductGroup> listGroup = cmBxProductGroupService.listCmBxProductGroupMsg(par);
  			if(CollectionUtils.isNotEmpty(listGroup)){
  				for(CmBxProductGroup model : listGroup){
  					model.setProdtype(model.getProdtype());
  					listCmBxProductGroup.add(model);
  				}
  			}
  		}else{
  			CmBxProductGroup group = new CmBxProductGroup();
  			group.setAttfundcode(cmBxProduct.getFundcode());
  			group.setAttfundname(cmBxProduct.getFundname());
  			group.setProdtype(cmBxProduct.getProdtype());
  		    //1主险；2附加险
  			group.setProdproper(cmBxProduct.getProdproper());
  			listCmBxProductGroup.add(group);
  		}
  		map.put("listCmBxProductGroup", listCmBxProductGroup);
        
		return new ModelAndView("insur/prebookconfirm", "map", map);
	}
	
	/**
     * 保存预约确认信息
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/savePrebookConfirm.do", method = RequestMethod.POST)
    public Map<String, Object> savePrebookConfirm(HttpServletRequest request) throws Exception{
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        User user = getLoginUser();
    	
    	String preid = request.getParameter("id");
        String age = request.getParameter("age");
        String insurage = request.getParameter("insurage");
        String insurname = request.getParameter("insurname");
        String insuridtype = request.getParameter("insuridtype");
        String insuridno = request.getParameter("insuridno");
        String relation = request.getParameter("relation");
        String otherrelation = request.getParameter("otherrelation");
        String expectsigndt = request.getParameter("expectsigndt");
        String channcode = request.getParameter("channcode");
        String currency = request.getParameter("currency");
        String consremark = request.getParameter("consremark");
        String confirmremark = request.getParameter("confirmremark");
        String attrinsurs = request.getParameter("attrinsurs");
        String busisource = request.getParameter("busisource");
        log.info("attrinsurs:{}" , attrinsurs);
          
        CmBxPrebookinfo prebookinfo = new CmBxPrebookinfo();
        prebookinfo.setId(new BigDecimal(preid));
        prebookinfo.setAge(Integer.parseInt(age));
        prebookinfo.setInsurage(Integer.parseInt(insurage));
        prebookinfo.setInsurname(insurname);
        prebookinfo.setInsuridtype(insuridtype);
        if(StringUtil.isNotNullStr(insuridno)){
    		prebookinfo.setInsuridnoDigest(DigestUtil.digest(insuridno.trim()));
    		prebookinfo.setInsuridnoMask(MaskUtil.maskIdNo(insuridno.trim()));
    		prebookinfo.setInsuridnoCipher(encryptSingleFacade.encrypt(insuridno.trim()).getCodecText());
    	}
        prebookinfo.setRelation(relation);
        prebookinfo.setOtherrelation(otherrelation);
        prebookinfo.setExpectsigndt(expectsigndt);
        prebookinfo.setChanncode(channcode);
        prebookinfo.setCurrency(currency);
        prebookinfo.setConsremark(consremark);
        prebookinfo.setConfirmremark(confirmremark);
        prebookinfo.setBusisource(busisource);
        //预约状态：已确认
        prebookinfo.setPrestate(StaticVar.INSUR_PRESTATE_CONFORM);
        prebookinfo.setModifydt(new Date());
        prebookinfo.setModifier(user.getUserId());
        cmBxPrebookinfoService.updateCmBxPrebookinfo(prebookinfo);
        log.info("saveCmBxPrebookinfo:{}" , JSON.json(prebookinfo));
        
        if(StringUtils.isBlank(attrinsurs)){
        	resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "至少输入一条购买产品信息！");
            return resultMap;
        }
        
        String[]  arrCmBxPrebookBuyinfo = attrinsurs.split(":");
        String checkstat = null;
        for(String strCmBxPrebookBuyinfo : arrCmBxPrebookBuyinfo){
        	if(StringUtils.isNotBlank(strCmBxPrebookBuyinfo)){
        		String [] arrField = strCmBxPrebookBuyinfo.split(",");
        		checkstat = arrField[0];
        		CmBxPrebookBuyinfo cmBxPrebookBuyinfo = new CmBxPrebookBuyinfo();
        		if("1".equals(checkstat) || "0".equals(checkstat)){
        			//勾选:更新
        			if("1".equals(checkstat)){
                    	cmBxPrebookBuyinfo.setPayyears(arrField[2]);
                    	cmBxPrebookBuyinfo.setEnsureyears(arrField[3]);
                    	cmBxPrebookBuyinfo.setYearamk(StringUtils.isBlank(arrField[4]) ? null : new BigDecimal(arrField[4]));
                    	if(arrField.length==6){
                    		cmBxPrebookBuyinfo.setInsuramk(StringUtils.isBlank(arrField[5]) ? null : new BigDecimal(arrField[5]));
                    	}
                    	cmBxPrebookBuyinfo.setIsdel(StaticVar.INSUR_ISDEL_NO);
        			//删除	
        			}else{
            			cmBxPrebookBuyinfo.setIsdel(StaticVar.INSUR_ISDEL_YES);
        			}
        			cmBxPrebookBuyinfo.setId(new BigDecimal(arrField[1]));
        			cmBxPrebookBuyinfo.setModifier(user.getUserId());
                	cmBxPrebookBuyinfo.setModifydt(new Date());
        			cmBxPrebookBuyinfoService.updateCmBxPrebookBuyinfo(cmBxPrebookBuyinfo);
        		//新增	
        		}else if("2".equals(checkstat)){
        			cmBxPrebookBuyinfo.setId(new BigDecimal(commonService.getSeqValue("SEQ_INSUR_ID")));
        			cmBxPrebookBuyinfo.setPreid(new BigDecimal(preid));
        			cmBxPrebookBuyinfo.setFundcode(arrField[1]);
        			cmBxPrebookBuyinfo.setPayyears(arrField[2]);
                	cmBxPrebookBuyinfo.setEnsureyears(arrField[3]);
                	cmBxPrebookBuyinfo.setYearamk(StringUtils.isBlank(arrField[4]) ? null : new BigDecimal(arrField[4]));
                	if(arrField.length==6){
                		cmBxPrebookBuyinfo.setInsuramk(StringUtils.isBlank(arrField[5]) ? null : new BigDecimal(arrField[5]));
                	}
        			cmBxPrebookBuyinfo.setCreator(user.getUserId());
                	cmBxPrebookBuyinfo.setCreatdt(new Date());
        			cmBxPrebookBuyinfo.setIsdel(StaticVar.INSUR_ISDEL_NO);
        			cmBxPrebookBuyinfoService.insertCmBxPrebookBuyinfo(cmBxPrebookBuyinfo);
        		}
        	}
        }
        resultMap.put("uploadFlag", "success");
        resultMap.put("errorMsg", "确认预约投保成功");
        
        //更新预约表中投保人或受保人年龄
        boolean status = upPreCheckAgeAndInsurage(request,expectsigndt);
        if(status){
        	resultMap.put("uploadFlag", "upPreCheckAgeAndInsurage");
            resultMap.put("errorMsg", "请注意，投保人年龄或受保人年龄已更新！");
        }
        
        return resultMap;
    }

	/**
	 * @description:(比较保单原有信息和此次修改的值的差异)
	 * @param listData
	 * @param request
	 * @return java.util.List<com.howbuy.crm.hb.domain.insur.CmBxEditLog>
	 * @author: xfc
	 * @date: 2023/3/27
	 * @since JDK 1.8
	 */
	private List<CmBxEditLog> checkDiffer(List<CmBxPrebookBuyWithPayInfo> listData, HttpServletRequest request) throws ParseException {
		String channcode = request.getParameter("channcode");
		String passdt = request.getParameter("passdt");
		String paydt = request.getParameter("paydt");
		String caltime = request.getParameter("caltime");
		String attrinsurs = request.getParameter("attrinsurs");

		List<CmBxEditLog> cmBxEditLogList = new ArrayList<>();
		for (CmBxPrebookBuyWithPayInfo info : listData) {
			CmBxEditLog cmBxEditLog = new CmBxEditLog();
			String realChannCode = info.getChannCode();
			String realPassDt = info.getPassDt();
			String realPayDt = info.getPayDt();
			String realCalTime = info.getCalTime();
			BigDecimal realYearAmk = info.getYearAmk();
			String realEnsureYears = info.getEnsureYears();
			String realPayYears = info.getPayYears();
			if (!channcode.equals(realChannCode)) {
				cmBxEditLog.setChannel(new BigDecimal(1));
			}
			if (!passdt.equals(realPassDt)) {
				cmBxEditLog.setPassdt(new BigDecimal(1));
			}
			if (!paydt.equals(realPayDt)) {
				cmBxEditLog.setPaydt(new BigDecimal(1));
			}
			if (!caltime.equals(realCalTime)) {
				cmBxEditLog.setCaltime(new BigDecimal(1));
			}
			// 处理购买信息
			String[] arrCmBxPrebookBuyinfo = attrinsurs.split(":");
			for (String strCmBxPrebookBuyinfo : arrCmBxPrebookBuyinfo) {
				if (StringUtils.isNotBlank(strCmBxPrebookBuyinfo)) {
					String[] arrField = strCmBxPrebookBuyinfo.split(",");
					if (arrField[1].equals(info.getBuyId().toString())) {
						// 判断缴费年限
						String payYears = arrField[2];
						// 保障期限(年)
						String ensuredt = arrField[3];
						// 年缴保费
						BigDecimal yearAmt = new BigDecimal(arrField[4]);
						if (!payYears.equals(realPayYears)) {
							cmBxEditLog.setPayyears(new BigDecimal(1));
						}
						if (!ensuredt.equals(realEnsureYears)) {
							cmBxEditLog.setEnsureyears(new BigDecimal(1));
						}
						if (yearAmt.compareTo(realYearAmk) != 0) {
							cmBxEditLog.setPayamt(new BigDecimal(1));
						}
						cmBxEditLog.setBuyid(info.getBuyId());
					}
				}
			}
			// 判断是否有过修改，没有则不记录日志
			if (checkIsNullObject(cmBxEditLog)) {
				continue;
			}
			cmBxEditLog.setPreid(info.getPreId());
			cmBxEditLogList.add(cmBxEditLog);
		}
		return cmBxEditLogList;
	}


	/**
	 * @description:(判断对象中属性是否为空)
	 * @param object
	 * @return java.lang.Boolean
	 * @author: xfc
	 * @date: 2023/3/27 16:42
	 * @since JDK 1.8
	 */
	public Boolean checkIsNullObject(Object object){
		if (null == object) {
			return true;
		}
		for (Field field : object.getClass().getDeclaredFields()) {
			field.setAccessible(true);
			try {
				if (null != field.get(object) && StringUtils.isNotBlank(field.get(object).toString())) {
					return false;
				}
			} catch (IllegalAccessException e) {
				log.error("error in checkIsNullObject,{}", e.getStackTrace());
			}
		}
		return true;
	}
	
    
    private boolean upPreCheckAgeAndInsurage(HttpServletRequest request,String signdt){
    	boolean upstatus = false;
    	
    	Integer age = Integer.parseInt(request.getParameter("age"));
    	String insuridtype = request.getParameter("insuridtype");
    	String insuridno = request.getParameter("insuridno");
    	Integer insurage = Integer.parseInt(request.getParameter("insurage"));
    	String id = request.getParameter("id");
    	Map<String,Object> param = new HashMap<String,Object> (1);
    	param.put("id", new BigDecimal(id));
    	CmBxPrebookinfo cmBxPrebookinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(param);
    	String idtype = cmBxPrebookinfo.getIdtype();
    	String idno = "";
    	if(StringUtil.isNotNullStr(cmBxPrebookinfo.getIdnoCipher())){
			idno = decryptSingleFacade.decrypt(cmBxPrebookinfo.getIdnoCipher()).getCodecText();
		}
		Integer signYear = Integer.parseInt(signdt.substring(0, 4));
    	String expectsign = signdt.substring(4, 8);
         if("0".equals(idtype) || "0".equals(insuridtype)){
        	Integer tempAge = null;
         	Integer tempInsurage = null;
         	if("0".equals(idtype)){
         		if(idno.length() == 15){
         			tempAge = signYear - Integer.parseInt("19" + idno.substring(6,8));
         			if(expectsign.compareTo(idno.substring(8,12)) < 0){
         				tempAge--;
         			}
             	}
                if(idno.length() == 18){
                	tempAge = signYear - Integer.parseInt(idno.substring(6,10));
                 	if(expectsign.compareTo(idno.substring(10,14)) < 0){
                 		tempAge--;
         			}
             	}
         	}
         	
         	if("0".equals(insuridtype)){
         		if(insuridno.length() == 15){
         			tempInsurage = signYear - Integer.parseInt("19" + insuridno.substring(6,8));
         			if(expectsign.compareTo(insuridno.substring(8,12)) < 0){
         				tempInsurage--;
         			}
             	}
                if(insuridno.length() == 18){
                	tempInsurage = signYear - Integer.parseInt(insuridno.substring(6,10));
                 	if(expectsign.compareTo(insuridno.substring(10,14)) < 0){
                 		tempInsurage--;
         			}
             	}
         	}

         	if( (tempAge != null && !tempAge.equals(age)) || (tempInsurage != null && !tempInsurage.equals(insurage))){
         		CmBxPrebookinfo info = new CmBxPrebookinfo();
                info.setId(new BigDecimal(id));
           	    info.setAge(tempAge);
           	    info.setInsurage(tempInsurage);
                cmBxPrebookinfoService.updateCmBxPrebookinfoOnly(info);
                upstatus = true;
            }
         }
         
         	
        return upstatus;
    }
    
	@RequestMapping("/insurCheckPrebook.do")
    public ModelAndView insurCheckPrebook(HttpServletRequest request){
        String id = request.getParameter("id");
        Map<String,Object> map = new HashMap<String,Object>(3);
        Map<String,Object> param = new HashMap<String,Object>(1);
        param.put("id", new BigDecimal(id));
        CmBxPrebookinfo preinfo = cmBxPrebookinfoService.getViewCmBxPrebookinfo(param);
        if(preinfo != null){
        	preinfo.setPrestateval(ConstantCache.getInstance().getVal("insurprestate", preinfo.getPrestate()));
        	preinfo.setInsurstateval(ConstantCache.getInstance().getVal("insurstate", preinfo.getInsurstate()));
        	preinfo.setOrgcode(ConsOrgCache.getInstance().getAllOrgMap().get(preinfo.getOrgcode()));
        	preinfo.setConscode(ConsOrgCache.getInstance().getAllUserMap().get(preinfo.getConscode()));
        	preinfo.setIdtype(ConstantCache.getInstance().getVal("idtype", preinfo.getIdtype()));
        	preinfo.setInsuridtype(ConstantCache.getInstance().getVal("idtype", preinfo.getInsuridtype()));
        	preinfo.setRelation(ConstantCache.getInstance().getVal("insurrelation", preinfo.getRelation()));
        	preinfo.setBusitype(ConstantCache.getInstance().getVal("insurbusitype", preinfo.getBusitype()));
        	preinfo.setCurrency(ConstantCache.getInstance().getVal("currencys", preinfo.getCurrency()));
        	preinfo.setBusisourceval(ConstantCache.getInstance().getVal("insurbusisource", preinfo.getBusisource()));
        }else{
        	preinfo = new CmBxPrebookinfo();
        }
        map.put("info", preinfo);
        //查询签单信息
        Map<String,Object> paramsign = new HashMap<String,Object>(2);
        paramsign.put("preid", new BigDecimal(id));
        paramsign.put("isdel", StaticVar.INSUR_ISDEL_NO);
        CmBxPrebookSigninfo sign =  cmBxPrebookSigninfoService.getCmBxPrebookSigninfo(paramsign);
        if(sign != null){
        	sign.setPaystate(ConstantCache.getInstance().getVal("insurpaystate", sign.getPaystate()));
        }else{
        	sign = new CmBxPrebookSigninfo();
        }
        map.put("sign", sign);
        //购买信息
        Map<String,Object> parambuy = new HashMap<String,Object>(2);
        parambuy.put("preid", new BigDecimal(id));
        parambuy.put("isdel", StaticVar.INSUR_ISDEL_NO);
        List<CmBxPrebookBuyinfo> listbuy = cmBxPrebookBuyinfoService.listCmBxPrebookBuyinfo(parambuy);
        for(CmBxPrebookBuyinfo buy : listbuy){
			buy.setProdtype(ConstantCache.getInstance().getVal("insurprodtype", buy.getProdtype()));
			buy.setPayyears(ConstantCache.getInstance().getVal("insurpayyears", buy.getPayyears()));
			buy.setEnsureyears(ConstantCache.getInstance().getVal("insurensureyears", buy.getEnsureyears()));
        }
        map.put("listbuy", listbuy);

		// 查询修改信息
		List<CmBxEditLog> cmBxEditLogs = cmBxEditLogService.listCmBxEditLog(new BigDecimal(id));
		List<CmBxEditLog> cmBxEditLogList = mergeCmBxEditLogList(cmBxEditLogs);
		// 修改的信息有几种 合作渠道、 缴费年限 、 保障期限、 年缴保费、核保通过日期、 保费缴清日、冷静期截止日
		if (cmBxEditLogList.size() != 0) {
			map.put("cmBxEditLogs", cmBxEditLogList.get(0));
		}else {
			CmBxEditLog cmBxEditLog = new CmBxEditLog();
			cmBxEditLog.setCaltime(new BigDecimal(2));
			cmBxEditLog.setPaydt(new BigDecimal(2));
			cmBxEditLog.setPassdt(new BigDecimal(2));
			cmBxEditLog.setPayamt(new BigDecimal(2));
			cmBxEditLog.setChannel(new BigDecimal(2));
			cmBxEditLog.setEnsureyears(new BigDecimal(2));
			cmBxEditLog.setPayyears(new BigDecimal(2));
			map.put("cmBxEditLogs", cmBxEditLog);
		}

        return new ModelAndView("insur/checkPrebook","map",map);
    }
	
	@RequestMapping("/checkPassPrebook.do")
    @ResponseBody
    public String checkPassPrebook(HttpServletRequest request){
    	String result="";
        String id = request.getParameter("id");
        String edittims = request.getParameter("edittims");
        String remark = request.getParameter("remark");
        User userlogin = getLoginUser();
        //查询预约
        Map<String,Object> parapre = new HashMap<String,Object>(1);
        parapre.put("id", id);
        CmBxPrebookinfo preinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(parapre);
        if(preinfo == null){
            return "预约不存在";
        }
        if(preinfo.getEdittims() != null && !preinfo.getEdittims().equals(edittims)){
        	return "预约已经改变，重新打开再复核";
        }
        preinfo.setCheckstate(StaticVar.INSUR_CHECHSTATE_HASCHECK);
        preinfo.setCheckremark(remark);
        preinfo.setModifier(userlogin.getUserId());
        preinfo.setModifydt(new Date());
        cmBxPrebookinfoService.updateCmBxPrebookinfoCheckPass(preinfo,userlogin.getUserId());
		result = "success";
		// 复核成功后删除掉操作日志
		cmBxEditLogService.delCmBxEditLog(new BigDecimal(id));

        return result;
    }
	
	
	
	
	/**
	 * (跳转到录入核保信息或修改界面)
	 */
	@ResponseBody
	@RequestMapping("/enterCheckOrUpPrebook.do")
	public ModelAndView enterCheckOrUpPrebook(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		List<Map<String,Object>> channList = new ArrayList<>();
		List<Map<String,Object>> currList = new ArrayList<>();
		//产品对应的产品组
		List<CmBxProductGroup> listCmBxProductGroup = new ArrayList<CmBxProductGroup>();
		String defaultchann = "";
		String defaultcurr = "";
		
		String id = request.getParameter("id");
		Map<String,Object> param = new HashMap<String,Object>(1);
		param.put("id", id);
		CmBxPrebookinfo cmBxPrebookinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(param);
		Map<String,Object> map = new HashMap<String,Object>(16);
		if(cmBxPrebookinfo != null){
			if(StringUtil.isNotNullStr(cmBxPrebookinfo.getIdnoCipher())){
				cmBxPrebookinfo.setIdno(decryptSingleFacade.decrypt(cmBxPrebookinfo.getIdnoCipher()).getCodecText());
			}
			if(StringUtil.isNotNullStr(cmBxPrebookinfo.getInsuridnoCipher())){
				cmBxPrebookinfo.setInsuridno(decryptSingleFacade.decrypt(cmBxPrebookinfo.getInsuridnoCipher()).getCodecText());
			}
			param.clear();
			param.put("preid", id);
			param.put("isdel", StaticVar.INSUR_ISDEL_NO);
			List<CmBxPrebookBuyinfo> listCmBxPrebookBuyinfo = cmBxPrebookBuyinfoService.listCmBxPrebookBuyinfo(param);
			List<CmBxPrebookAnnex> listCmBxPrebookAnnex = cmBxPrebookAnnexService.listCmBxPrebookAnnex(param);
			
			CmBxPrebookSigninfo cmBxPrebookSigninfo = cmBxPrebookSigninfoService.getCmBxPrebookSigninfo(param);
			
			Map<String,String> par = new HashMap<String,String> (2);
			par.put("fundcode", cmBxPrebookinfo.getFundcode());
			par.put("isdel", StaticVar.INSUR_ISDEL_NO);
			CmBxProduct cmBxProduct = cmBxProductService.getCmBxProduct(par);
			
			//查询合作渠道
			par.clear();
			par.put("isdel", StaticVar.INSUR_ISDEL_NO);
			par.put("fundcode", cmBxProduct.getFundcode());
	        List<CmBxProductChannel> listProChannel = cmBxProductChannelService.listCmBxProductChannel(par);
	        defaultchann = cmBxPrebookinfo.getChanncode();
			map.put("channcode", defaultchann);
	        if(CollectionUtils.isNotEmpty(listProChannel)){
	        	for(CmBxProductChannel proChannel : listProChannel){
	        		par.put("channcode", proChannel.getChanncode());
	        		CmBxChannel channel = cmBxChannelService.getCmBxChannel(par);
	        		if(channel != null){
	        			Map<String, Object>  channmap = new HashMap<>(2);
	        			channmap.put("id", channel.getChanncode());
	            		channmap.put("text", channel.getChannname());
	        	        channList.add(channmap);
	        		}
	        	}
	        }
			
	        //查询币种
	        LinkedHashMap<String, String> mapcache = ConstantCache.getInstance().getConstantKeyVal("currencys");
	        String[] arrcur = cmBxProduct.getCurrency().split(",");
	        defaultcurr = cmBxPrebookinfo.getCurrency();
	        for(String cur : arrcur){
	        	Map<String, Object> curr = new HashMap<>(2);
	        	curr.put("id", cur);
	        	curr.put("text", mapcache.get(cur));
	        	currList.add(curr);
	        }
	        
	        //查询保险公司
	        par.clear();
	        par.put("compcode", cmBxProduct.getCompcode());
	        CmBxCompany cmBxCompany = cmBxCompanyService.getCmBxCompany(par);
	        
			
			map.put("id",id);
			map.put("cmBxPrebookinfo",cmBxPrebookinfo);
			map.put("listCmBxPrebookBuyinfo",listCmBxPrebookBuyinfo);
			map.put("listCmBxPrebookAnnex",listCmBxPrebookAnnex);
			map.put("cmBxProduct",cmBxProduct);
			
			map.put("channList",channList);
	        map.put("currList", currList);
	        map.put("defaultchann",defaultchann);
	        map.put("defaultcurr", defaultcurr);
	        map.put("cmBxCompany", cmBxCompany);
	        
	        //查询投保人的姓名、所属部门、所属投顾
	        par.clear();
	        par.put("conscustno", cmBxPrebookinfo.getConscustno());
	        par.put("id", cmBxPrebookinfo.getId().toString());
	        PageData<CmBxPrebookinfo> pageData = cmBxPrebookinfoService.listCmBxPrebookinfoByPage(par);
	        CmBxPrebookinfo info= pageData.getListData().get(0);
	        map.put("consname", ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
	        map.put("orgname", ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
	        map.put("custname", info.getCustname());
	        
	        par.clear();
	        par.put("isdel", StaticVar.INSUR_ISDEL_NO);
	        par.put("fundcode", cmBxProduct.getFundcode());
	        //1.产品
	  		if("1".equals(cmBxProduct.getProdproper())){
	  			CmBxProductGroup group = new CmBxProductGroup();
	  			group.setAttfundcode(cmBxProduct.getFundcode());
	  			group.setAttfundname(cmBxProduct.getFundname());
	  			group.setProdtype(cmBxProduct.getProdtype());
	  		    //1主险；2附加险
	  			group.setProdproper(cmBxProduct.getProdproper());
	  			listCmBxProductGroup.add(group);
	  			
	  			List<CmBxProductGroup> listGroup = cmBxProductGroupService.listCmBxProductGroupMsg(par);
	  			if(CollectionUtils.isNotEmpty(listGroup)){
	  				for(CmBxProductGroup model : listGroup){
	  					model.setProdtype(model.getProdtype());
	  					listCmBxProductGroup.add(model);
	  				}
	  			}
	  		}else{
	  			CmBxProductGroup group = new CmBxProductGroup();
	  			group.setAttfundcode(cmBxProduct.getFundcode());
	  			group.setAttfundname(cmBxProduct.getFundname());
	  			group.setProdtype(cmBxProduct.getProdtype());
	  		    //1主险；2附加险
	  			group.setProdproper(cmBxProduct.getProdproper());
	  			listCmBxProductGroup.add(group);
	  		}
	  		map.put("listCmBxProductGroup", listCmBxProductGroup);
	  		map.put("cmBxPrebookSigninfo", cmBxPrebookSigninfo);
	  		map.put("optype", request.getParameter("optype"));
		}
		
  		//特殊权限修改历史记录
  		if("3".equals(request.getParameter("optype"))){
  			return new ModelAndView("insur/updatehisprebook", "map", map);
  		}else{
  			return new ModelAndView("insur/entercheckorupprebook", "map", map);
  		}
	}
	
	/**
     * 保存录入核保信息或修改信息
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveEnterCheckOrUpPrebook.do", method = RequestMethod.POST)
    public Map<String, Object> saveEnterCheckOrUpPrebook(HttpServletRequest request) throws Exception{
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        User user = getLoginUser();
        String optype = request.getParameter("optype");
    	String preid = request.getParameter("id");
        String age = request.getParameter("age");
        String insurage = request.getParameter("insurage");
        String insurname = request.getParameter("insurname");
        String insuridtype = request.getParameter("insuridtype");
        String insuridno = request.getParameter("insuridno");
        String relation = request.getParameter("relation");
        String otherrelation = request.getParameter("otherrelation");
        String channcode = request.getParameter("channcode");
        String currency = request.getParameter("currency");
        String consremark = request.getParameter("consremark");
        String attrinsurs = request.getParameter("attrinsurs");
        String confirmremark = request.getParameter("confirmremark");
        String busisource = request.getParameter("busisource");
        log.info("attrinsurs:{}" , attrinsurs);
        if(StringUtils.isBlank(attrinsurs)){
        	resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "至少输入一条购买产品信息！");
            return resultMap;
        }

    	String insurid = request.getParameter("insurid");
    	//判断这个保单号是否已经存在非自己的有效的预约单子中
    	//判断除这个预约以外的预约是否用了这个保单号？
        Map<String, Object> paramsign = new HashMap<String, Object>(3);
        paramsign.put("prestates", " ('"+StaticVar.INSUR_PRESTATE_CONFORM+"','"+StaticVar.INSUR_PRESTATE_NOTCONFORM+"') ");
        paramsign.put("insurid", insurid);
        paramsign.put("isnotpreid", preid);
        List<CmBxPrebookSigninfo> signlist = cmBxPrebookSigninfoService.listCmBxPrebookSigninfo(paramsign);
        if(signlist != null && signlist.size() > 0){
        	resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "保单号已存在，请重新输入");
            return resultMap;
        }
    	String passdt = request.getParameter("passdt");
    	String signdt = request.getParameter("signdt");
    	String paydt = request.getParameter("paydt");
    	String caltime = request.getParameter("caltime");
    	String nextyearpaydt = request.getParameter("nextyearpaydt");
    	String ratiodt = request.getParameter("ratiodt");
    	String cancelsurdt = request.getParameter("cancelsurdt");
    	String insurremark = request.getParameter("insurremark");
    	String paystate = request.getParameter("paystate");
    	String insurstate = request.getParameter("insurstate");

		// 根据保单号查询该保单下原有的数据信息
		List<CmBxPrebookBuyWithPayInfo> listData =  cmBxPrebookinfoService.listCmBxPrebookinfoByInsuid(preid);
		List<CmBxEditLog> cmBxEditLogList = checkDiffer(listData, request);

		CmBxPrebookSigninfo cmBxPrebookSigninfo = new CmBxPrebookSigninfo();
        cmBxPrebookSigninfo.setPreid(new BigDecimal(preid));
        cmBxPrebookSigninfo.setInsurid(insurid);
        cmBxPrebookSigninfo.setPassdt(passdt);
        cmBxPrebookSigninfo.setSigndt(signdt);
        cmBxPrebookSigninfo.setPaydt(paydt);
        cmBxPrebookSigninfo.setCaltime(caltime);
        cmBxPrebookSigninfo.setNextyearpaydt(nextyearpaydt);
        cmBxPrebookSigninfo.setRatiodt(ratiodt);
        cmBxPrebookSigninfo.setCancelsurdt(cancelsurdt);
        cmBxPrebookSigninfo.setInsurremark(insurremark);
        cmBxPrebookSigninfo.setPaystate(paystate);
        cmBxPrebookSigninfo.setModifier(user.getUserId());
        cmBxPrebookSigninfo.setModifydt(new Date());
        cmBxPrebookSigninfoService.updateCmBxPrebookSigninfo(cmBxPrebookSigninfo);
        log.info("updateCmBxPrebookSigninfo:{}" , JSON.json(cmBxPrebookSigninfo));
		//查询预约往前移
		Map<String,Object> parampreinfo = new HashMap<String,Object>(1);
		parampreinfo.put("id", preid);
		CmBxPrebookinfo prebookinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(parampreinfo);

		//根据创建者 即所属投顾  获取创新方案
		String nowBxCommissionWay = "";
		if(null!= prebookinfo && StringUtils.isNotEmpty(prebookinfo.getCreator())){
			Map<String,String> param = new HashMap<>(1);
			param.put("conscode", prebookinfo.getCreator());
			CmConsultantExp info = cmConsultantExpService.getCmConsultantExpByConscode(param);
			nowBxCommissionWay = (null==info? BxCommissionWayEnum.BXCOMMISSIONWAY_ONE.getCode() : info.getBxcommissionway());
			log.info("saveEnterCheckOrUpPrebook根据所属投顾:{}获取创新方案:{}",prebookinfo.getCreator(), nowBxCommissionWay);
		}

        String[]  arrCmBxPrebookBuyinfo = attrinsurs.split(":");
        String checkstat = null;
        for(String strCmBxPrebookBuyinfo : arrCmBxPrebookBuyinfo){
        	if(StringUtils.isNotBlank(strCmBxPrebookBuyinfo)){
        		CmBxPrebookBuyinfo cmBxPrebookBuyinfo = new CmBxPrebookBuyinfo();
        		String [] arrField = strCmBxPrebookBuyinfo.split(",");
        		checkstat = arrField[0];
        		if("1".equals(checkstat) || "0".equals(checkstat)){
        			//勾选:更新
        			if("1".equals(checkstat)){
                    	cmBxPrebookBuyinfo.setPayyears(arrField[2]);
                    	cmBxPrebookBuyinfo.setEnsureyears(arrField[3]);
                    	cmBxPrebookBuyinfo.setYearamk(StringUtils.isBlank(arrField[4]) ? null : new BigDecimal(arrField[4]));
                    	if(arrField.length==6){
                    		cmBxPrebookBuyinfo.setInsuramk(StringUtils.isBlank(arrField[5]) ? null : new BigDecimal(arrField[5]));
                    	}
                    	cmBxPrebookBuyinfo.setIsdel(StaticVar.INSUR_ISDEL_NO);
        			//删除	
        			}else{
            			cmBxPrebookBuyinfo.setIsdel(StaticVar.INSUR_ISDEL_YES);
        			}
        			cmBxPrebookBuyinfo.setId(new BigDecimal(arrField[1]));
        			cmBxPrebookBuyinfo.setModifier(user.getUserId());
                	cmBxPrebookBuyinfo.setModifydt(new Date());
					cmBxPrebookBuyinfo.setBxCommissionWay(nowBxCommissionWay);
        			cmBxPrebookBuyinfoService.updateCmBxPrebookBuyinfo(cmBxPrebookBuyinfo);
        			log.info("updateCmBxPrebookBuyinfo:{}" , JSON.json(cmBxPrebookBuyinfo));
        		//新增	
        		}else if("2".equals(checkstat)){
        			cmBxPrebookBuyinfo.setId(new BigDecimal(commonService.getSeqValue("SEQ_INSUR_ID")));
        			cmBxPrebookBuyinfo.setPreid(new BigDecimal(preid));
        			cmBxPrebookBuyinfo.setFundcode(arrField[1]);
        			cmBxPrebookBuyinfo.setPayyears(arrField[2]);
                	cmBxPrebookBuyinfo.setEnsureyears(arrField[3]);
                	cmBxPrebookBuyinfo.setYearamk(StringUtils.isBlank(arrField[4]) ? null : new BigDecimal(arrField[4]));
                	if(arrField.length==6){
                		cmBxPrebookBuyinfo.setInsuramk(StringUtils.isBlank(arrField[5]) ? null : new BigDecimal(arrField[5]));
                	}
        			cmBxPrebookBuyinfo.setCreator(user.getUserId());
                	cmBxPrebookBuyinfo.setCreatdt(new Date());
        			cmBxPrebookBuyinfo.setIsdel(StaticVar.INSUR_ISDEL_NO);
					cmBxPrebookBuyinfo.setBxCommissionWay(nowBxCommissionWay);
        			cmBxPrebookBuyinfoService.insertCmBxPrebookBuyinfo(cmBxPrebookBuyinfo);
        			log.info("insertCmBxPrebookBuyinfo:{}" , JSON.json(cmBxPrebookBuyinfo));
        		}

        	}
        }

        prebookinfo.setAge(Integer.valueOf(age));
        prebookinfo.setInsurage(Integer.valueOf(insurage));
        prebookinfo.setInsurname(insurname);
        prebookinfo.setInsuridtype(insuridtype);
        prebookinfo.setInsuridno(insuridno);
        if(StringUtil.isNotNullStr(insuridno)){
    		prebookinfo.setInsuridnoDigest(DigestUtil.digest(insuridno.trim()));
    		prebookinfo.setInsuridnoMask(MaskUtil.maskIdNo(insuridno.trim()));
    		prebookinfo.setInsuridnoCipher(encryptSingleFacade.encrypt(insuridno.trim()).getCodecText());
    	}
        prebookinfo.setRelation(relation);
        prebookinfo.setOtherrelation(otherrelation);
        prebookinfo.setChanncode(channcode);
        prebookinfo.setCurrency(currency);
        prebookinfo.setConsremark(consremark);
        prebookinfo.setConfirmremark(confirmremark);
        //修改情况
        if(StringUtils.isNotBlank(optype) && "2".equals(optype)){
        	prebookinfo.setCheckstate(StaticVar.INSUR_CHECHSTATE_NOTCHECK);
        	if(StaticVar.INSUR_STATE_HBTG.equals(prebookinfo.getInsurstate()) && StaticVar.INSUR_STATE_TBL.equals(insurstate)){
        		prebookinfo.setIschangereturn(StaticVar.INSUR_ISCHANGERETURN_YES);
        	}
        }
        //核保状态
        prebookinfo.setInsurstate(insurstate);
		// 设置保单状态
		// 当保单状态为核保通过时，复核状态设置为待复核
		if (StringUtils.isNotBlank(insurstate) && StaticVar.INSUR_STATE_HBTG.equals(insurstate)) {
			prebookinfo.setCheckstate(StaticVar.INSUR_CHECHSTATE_NOTCHECK);
		} else if (StringUtils.isNotBlank(insurstate) && (StaticVar.INSUR_STATE_YB.equals(insurstate) ||
				StaticVar.INSUR_STATE_TBL.equals(insurstate))) {
			// 当保单状态为延保/拒保、 退保（冷静期内）
			prebookinfo.setCheckstate(StaticVar.INSUR_CHECHSTATE_NOCHECK);
			// 如果该保单生成了缴款计划，则把 缴款计划对应的缴款状态置为 "未缴清"   缴款计划的状态置为"未生效"
			Map<String, String> params = new HashMap<>();
			params.put("insurid", insurid);
			List<CmBxPrebookEndpayList> cmBxPrebookEndpayLists = cmBxPrebookEndpayListService.listCmBxPrebookEndpayListByidOrState(params);
			if (cmBxPrebookEndpayLists.size() != 0) {
				cmBxPrebookEndpayListService.updateCmBxPrebookEndpayListByIds(cmBxPrebookEndpayLists);
			}
		} else if (StaticVar.INSUR_STATE_TB.equals(insurstate)) {
			// 正常退保（冷静期后）
			prebookinfo.setCheckstate(StaticVar.INSUR_CHECHSTATE_NOCHECK);
			// 如果该保单生成了缴款计划，则把 缴款计划中 缴款状态为未缴清的数据置为未生效
			Map<String, String> params = new HashMap<>();
			params.put("insurid", insurid);
			List<CmBxPrebookEndpayList> cmBxPrebookEndpayLists = cmBxPrebookEndpayListService.listCmBxPrebookEndpayListByidOrState(params);
			if (cmBxPrebookEndpayLists.size() != 0) {
				List<CmBxPrebookEndpayList> cmBxPrebookEndpayNotPayLists = cmBxPrebookEndpayLists.stream().filter(it -> it.getPaystate().equals(StaticVar.INSUR_PAYSTAT_NOTPAY)).collect(Collectors.toList());
				cmBxPrebookEndpayListService.updateCmBxPrebookEndpayListByIds(cmBxPrebookEndpayNotPayLists);
			}
		}

        prebookinfo.setBusisource(busisource);
        prebookinfo.setModifydt(new Date());
        prebookinfo.setModifier(user.getUserId());
        cmBxPrebookinfoService.updateCmBxPrebookinfo(prebookinfo);
        log.info("saveCmBxPrebookinfo:{}" , JSON.json(prebookinfo));

        resultMap.put("uploadFlag", "success");
        resultMap.put("errorMsg", "录入核保信息成功");
        
        //更新预约表中投保人或受保人年龄
        boolean status = upPreCheckAgeAndInsurage(request,signdt);
        if(status){
        	resultMap.put("uploadFlag", "upPreCheckAgeAndInsurage");
            resultMap.put("errorMsg", "请注意，投保人年龄或受保人年龄已更新！");
        }
		// 新增记录修改操作日志
		cmBxEditLogList.stream().forEach(it -> cmBxEditLogService.insertCmBxEditLog(it));
        return resultMap;
    }
    
    /**
     * 核保管理导出操作
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportInsurCheckPreInfo.do")
    public void exportInsurCheckPreInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
    	Map<String, String> param = new ParamUtil(request).getParamMap();
    	List<CmBxPrebookinfo> exportList = cmBxPrebookinfoService.listCmBxPrebookinfoByExp(param);
    	List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
		User user = getLoginUser();
		String ip = getIpAddr(request);
    	for (CmBxPrebookinfo info : exportList) {
			info.setPrestateval(ConstantCache.getInstance().getVal("insurprestate", info.getPrestate()));
			info.setInsurstateval(ConstantCache.getInstance().getVal("insurstate", info.getInsurstate()));
			ConsOrgCache orgcache = ConsOrgCache.getInstance();
			String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
			if("0".equals(uporgcode)){
				info.setUporgname(orgcache.getAllOrgMap().get(info.getOrgcode()));
			}else{
				info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
			}
			info.setOrgcode(ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
			info.setConscode(ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
			info.setRelation(ConstantCache.getInstance().getVal("insurrelation", info.getRelation()));
			info.setBusitype(ConstantCache.getInstance().getVal("insurbusitype", info.getBusitype()));
			info.setProdtype(ConstantCache.getInstance().getVal("insurprodtype", info.getProdtype()));
			info.setPaystate(ConstantCache.getInstance().getVal("insurpaystate", info.getPaystate()));
			info.setPayyears(ConstantCache.getInstance().getVal("insurpayyears", info.getPayyears()));
			info.setBusisourceval(ConstantCache.getInstance().getVal("insurbusisource", info.getBusisource()));
			info.setEnsureyears(ConstantCache.getInstance().getVal("insurensureyears", info.getEnsureyears()));
			info.setCurrency(ConstantCache.getInstance().getVal("currencys", info.getCurrency()));
			if(info.getPayyears() != null && info.getPayyears().contains("至") && info.getInsurage() != null){
                try {
                    info.setPayyears2(Integer.toString(Integer.parseInt(info.getPayyears().replace("至","").replace("岁","")) - info.getInsurage()));
                }catch (NumberFormatException e){
                    info.setPayyears2(info.getPayyears());
                }
            }else {
                info.setPayyears2(info.getPayyears());
            }
			if (null != info.getRate() && null != info.getYearamk() && StringUtils.isNotEmpty(info.getPayyears())) {
				BigDecimal years = BigDecimal.ONE;
				boolean includeCn = payYearsIncludeCn(info.getPayyears());
				if(includeCn){
					years = new BigDecimal(transferPayYears(info.getPayyears())).subtract(new BigDecimal(info.getAge()));
				}else{
					years = new BigDecimal(info.getPayyears());
				}
				//总保费保持和页面展示的一样  四舍五入保留两位
				info.setTotalPremium(info.getRate().multiply(info.getYearamk())
						.multiply(years).setScale(2, RoundingMode.HALF_UP)
						.toPlainString());

				info.setNewTotalPremium(info.getYearamk()
						.multiply(years).setScale(2, RoundingMode.HALF_UP)
						.toPlainString());
			}
			//参考前端JS逻辑,这里重复给Busitype 赋值
			if(StringUtils.isNotBlank(info.getBusitype()) && !"大陆".equals(info.getBusitype().trim())){
				info.setIsInvestment("是");
			}else{
				info.setIsInvestment("否");
			}
			// 考虑为空的情况
			if (StringUtils.isBlank(info.getSfsyxbx())){
				info.setSfsyxbx(null);
			}else if ("1".equals(info.getSfsyxbx())) {
				info.setSfsyxbx("收益型保险");
			}else{
				info.setSfsyxbx("非收益型保险");
			}

			//导出日志
            PageVisitLog pageVisitLog = new PageVisitLog();
            pageVisitLog.setConscustno(info.getConscustno());
            pageVisitLog.setUserId(user.getUserId());
            pageVisitLog.setVisitUrl(request.getRequestURI());
            pageVisitLog.setOperation("核保管理导出");
            pageVisitLog.setVisitTime(new Date());
            pageVisitLog.setPreid(info.getId());
            pageVisitLog.setIp(ip);
            listlog.add(pageVisitLog);
    	}
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("核保管理信息导出.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String [] columnName = new String [] {"预约id","录入时间","签单日期","预约状态","保单状态","区域(预约时)","部门(预约时)","投顾(预约时)","投顾客户号","投保人","证件号","受保人","投保人与受保人关系","其他关系","业务类型","保险公司","产品类型","合作渠道代码","合作渠道","明细ID","产品代码","产品名称","缴费年限","缴费年数","年缴保费/TP","币种","保障年限","业务来源","首单缴费状态","首单保费缴清日","保单号","核保通过日期","冷静期截止日","次年保费日","退保/拒保日期","汇率","总保费","总保费（RMB）","一级策略","是否投资海外","佣金结算日","投顾备注","保单备注","复核人备注"};

            String [] beanProperty = new String []{"id","credt","signdt","prestateval","insurstateval","uporgname","orgcode","conscode","conscustno","custname","idnoMask","insurname","relation","otherrelation","busitype","compname","prodtype","channcode","channname","buyid","buyfundcode","fundname","payyears","payyears2","yearamk","currency","ensureyears","busisourceval","paystate","paydt","insurid","passdt","caltime","nextyearpaydt","cancelsurdt","rate","newTotalPremium","totalPremium","sfsyxbx","isInvestment","ratiodt","consremark","insurremark","checkremark"};

            ExcelWriter.writeExcel(os, "核保管理信息", 0, exportList, columnName, beanProperty);
            // 关闭流
            os.close(); 
            for(PageVisitLog log : listlog){
            	pageVisitLogService.recordLog(log);
            }
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }
    	
    }


	/**
	 * 是否包含特殊汉字，至和岁
	 * @param payYearsInDB
	 * @return
	 */
	private boolean payYearsIncludeCn(String payYearsInDB) {
		if(StringUtils.isBlank(payYearsInDB)){
			return false;
		}
		return payYearsInDB.startsWith("至") && payYearsInDB.endsWith("岁");
	}

	/**
	 * @description 转换查询出来的payyears  表中存在中文数据 （eg. 至70岁）
	 * @param payYearsInDB
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/5 下午5:20
	 * @since JDK 1.8
	 */
	private String transferPayYears(String payYearsInDB) {
		if (payYearsInDB.startsWith("至") && payYearsInDB.endsWith("岁")) {
			return payYearsInDB.substring(payYearsInDB.indexOf("至")+1, payYearsInDB.indexOf("岁"));
		}
		return payYearsInDB;
	}
    
    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    /**
     * 退保（冷静期内）导出功能
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportTblPreInfo.do")
    public void exportTblPreInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
    	Map<String, String> param = new ParamUtil(request).getParamMap();
    	List<CmBxPrebookinfo> exportList = cmBxPrebookinfoService.listCmBxPrebookinfoByExp(param);
    	List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
		User user = getLoginUser();
		String ip = getIpAddr(request);
    	for (CmBxPrebookinfo info : exportList) {
			ConsOrgCache orgcache = ConsOrgCache.getInstance();
			String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
			if("0".equals(uporgcode)){
				info.setUporgname(orgcache.getAllOrgMap().get(info.getOrgcode()));
			}else{
				info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
			}
			info.setPrestateval(ConstantCache.getInstance().getVal("insurprestate", info.getPrestate()));
			info.setInsurstateval(ConstantCache.getInstance().getVal("insurstate", info.getInsurstate()));
			info.setOrgcode(ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
			info.setConscode(ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
			info.setRelation(ConstantCache.getInstance().getVal("insurrelation", info.getRelation()));
			info.setBusitype(ConstantCache.getInstance().getVal("insurbusitype", info.getBusitype()));
			info.setBusisourceval(ConstantCache.getInstance().getVal("insurbusisource", info.getBusisource()));
			info.setProdtype(ConstantCache.getInstance().getVal("insurprodtype", info.getProdtype()));
			info.setPaystate(ConstantCache.getInstance().getVal("insurpaystate", info.getPaystate()));
			info.setPayyears(ConstantCache.getInstance().getVal("insurpayyears", info.getPayyears()));
			info.setEnsureyears(ConstantCache.getInstance().getVal("insurensureyears", info.getEnsureyears()));
			info.setCurrency(ConstantCache.getInstance().getVal("currencys", info.getCurrency()));
			if(info.getPayyears() != null && info.getPayyears().contains("至") && info.getInsurage() != null){
                try {
                    info.setPayyears2(Integer.toString(Integer.parseInt(info.getPayyears().replace("至","").replace("岁","")) - info.getInsurage()));
                }catch (NumberFormatException e){
                    info.setPayyears2(info.getPayyears());
                }
            }else {
                info.setPayyears2(info.getPayyears());
            }
			//导出日志
            PageVisitLog pageVisitLog = new PageVisitLog();
            pageVisitLog.setConscustno(info.getConscustno());
            pageVisitLog.setUserId(user.getUserId());
            pageVisitLog.setVisitUrl(request.getRequestURI());
            pageVisitLog.setOperation("退保（冷静期内）导出");
            pageVisitLog.setVisitTime(new Date());
            pageVisitLog.setPreid(info.getId());
            pageVisitLog.setIp(ip);
            listlog.add(pageVisitLog);
    	}
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("退保（冷静期内）信息导出.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String [] columnName = new String [] {"预约id","录入时间","签单日期","预约状态","保单状态","区域(预约时)","部门(预约时)","投顾(预约时)","投顾客户号","投保人","证件号","受保人","投保人与受保人关系","其他关系","业务类型","保险公司","产品类型","合作渠道代码","合作渠道","明细ID","产品代码","产品名称","缴费年限","缴费年数","年缴保费/TP","币种","保障年限","业务来源","首年缴费状态","首年保费缴清日","保单号","核保通过日期","冷静期截止日","次年保费日","退保/拒保日期","汇率","佣金结算日","投顾备注","保单备注","复核人备注"};

            String [] beanProperty = new String [] {"id","credt","signdt","prestateval","insurstateval","uporgname","orgcode","conscode","conscustno","custname","idnoMask","insurname","relation","otherrelation","busitype","compname","prodtype","channcode","channname","buyid","buyfundcode","fundname","payyears","payyears2","yearamk","currency","ensureyears","busisourceval","paystate","paydt","insurid","passdt","caltime","nextyearpaydt","cancelsurdt","rate","ratiodt","consremark","insurremark","checkremark"};

            ExcelWriter.writeExcel(os, "退保（冷静期内）信息", 0, exportList, columnName, beanProperty);
            // 关闭流
            os.close(); 
            for(PageVisitLog log : listlog){
            	pageVisitLogService.recordLog(log);
            }
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }
    	
    }
    
    @RequestMapping("/downloadImportMode.do")
	public String downloadImportMode( HttpServletRequest request,
			HttpServletResponse response) {
		return dowmloadTemplate(MODEL_FILE_NAME,DOWNLOAD_FILE_NAME,request,response);
	}
    
    @RequestMapping(value="/impInsurPrebook.do",method=RequestMethod.POST)
	public  @ResponseBody Map<String, Object> impInsurPrebook(HttpServletRequest request){
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		
		User user = (User)request.getSession().getAttribute("loginUser");
		
		InputStream input = null;
		Workbook workBook = null;
		
		String errorMsg = "";
		String uploadFlag = "success";
		
		try {  
			// 转型为MultipartHttpRequest：  
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;  
			// 获得文件：  
			MultipartFile file = multipartRequest.getFile("file");  
			// 获得输入流：  
			input = file.getInputStream();  
			
			workBook = Workbook.getWorkbook(input);
				
			// 去掉之前导入的一二级旧来源，统一成现在的新来源编码
			String[] colPropertity = {"id","conscustno","age","insurname","insuridtype","insuridno","relation","otherrelation","channcode","currency","buyid","fundcode","fundname","payyears","yearamk","ensureyears","insuramk","signdt","paystate","paydt","insurid","insurstate","passdt","caltime","nextyearpaydt","cancelsurdt","insurremark","ratiodt"};
				
			Sheet sheet = workBook.getSheet(0);
			
			// 将之前获取Excel的13列数据改为12列
			List<CmBxPrebookinfo> postList = ExcelUtils.getListByReadShell(sheet, 1, 0, 28, colPropertity,CmBxPrebookinfo.class);
				
			if (null == postList || postList.isEmpty()) {
				errorMsg = "没有上传记录";
				uploadFlag = "error";
			} else {
				int line = 2;
				for (CmBxPrebookinfo info : postList) {
					String checkstr = checkPreInfo(info);
					if (StringUtil.isNotNullStr(checkstr)) {
						errorMsg += "第 " + line + " 行错误是：" + checkstr+"</br>";
						uploadFlag = "error";
					}
					line++;
				}
				//符合条件	
				if("success".equals(uploadFlag)){  
					String result = cmBxPrebookinfoService.batchImportInsurPrebook(postList,user.getUserId());
					if(!"success".equals(result)){
						uploadFlag = "error";
						errorMsg = result;
					}
				}
			}
			resultMap.put("uploadFlag", uploadFlag);  
			resultMap.put("errorMsg", errorMsg);  
			
	     } catch (Exception e) {   
	            e.printStackTrace();  
	            resultMap.put("uploadFlag", "error");  
	            resultMap.put("errorMsg", "请检查模板是否正确");  
	     }finally{
	    	try {
	    		if(input != null){
	    			input.close();
	    		}
			} catch (IOException e) {
				e.printStackTrace();
			}
	     }
		 
		return resultMap;
	}
    
    private String checkPreInfo(CmBxPrebookinfo info){
    	CmBxPrebookinfo pre = new CmBxPrebookinfo();
    	StringBuilder sb = new StringBuilder();
    	if(info.getId() == null){
    		sb.append("，预约号必填");
    	}else{
    		//查询预约符合状态,如果是已复核的，不能导入
        	Map<String,Object> parampre = new HashMap<String,Object>(1);
        	parampre.put("id", info.getId());
        	pre = cmBxPrebookinfoService.getCmBxPrebookinfo(parampre);
        	if(StaticVar.INSUR_CHECHSTATE_HASCHECK.equals(pre.getCheckstate())){
        		sb.append("，已复核不能导入");
        	}
    	}
    	
    	//受保人证件类型判断
    	if(StringUtil.isNotNullStr(info.getInsuridtype())){
    		if(StringUtil.isNullStr(ConstantCache.getInstance().getVal("idtype", info.getInsuridtype()))){
    			sb.append("，受保人证件类型格式不对");
    		}
    	}
    	//投保人与受保人关系
    	if(StringUtil.isNotNullStr(info.getRelation())){
    		if(StringUtil.isNullStr(ConstantCache.getInstance().getVal("insurrelation", info.getRelation()))){
    			sb.append("，投保人与受保人关系格式不对");
    		}
    	}
    	//合作渠道号
    	if(StringUtil.isNotNullStr(info.getChanncode())){
    		Map<String,String> param = new HashMap<String,String> (2);
	        param.put("channcode", info.getChanncode());
	        param.put("isdel", "1");
	        CmBxChannel channel = cmBxChannelService.getCmBxChannel(param);
	        if(channel == null){
	        	sb.append("，合作渠道不正确");
	        }
    	}
    	//币种
    	if(StringUtil.isNotNullStr(info.getCurrency())){
    		if(StringUtil.isNullStr(ConstantCache.getInstance().getVal("currencys", info.getCurrency()))){
    			sb.append("，币种格式不对");
    		}
    	}
    	//缴费年限
    	if(StringUtil.isNotNullStr(info.getPayyears())){
    		if(StringUtil.isNullStr(ConstantCache.getInstance().getVal("insurpayyears", info.getPayyears()))){
    			sb.append("，缴费年限格式不对");
    		}
    	}
    	//保障年限
    	if(StringUtil.isNotNullStr(info.getEnsureyears())){
    		if(StringUtil.isNullStr(ConstantCache.getInstance().getVal("insurensureyears", info.getEnsureyears()))){
    			sb.append("，保障年限格式不对");
    		}
    	}
    	//缴费状态
    	if(StringUtil.isNotNullStr(info.getPaystate())){
    		if(StringUtil.isNullStr(ConstantCache.getInstance().getVal("insurpaystate", info.getPaystate()))){
    			sb.append("，缴费状态格式不对");
    		}
    	}
    	//保单状态
    	if(StringUtil.isNotNullStr(info.getInsurstate())){
    		if(!(StaticVar.INSUR_STATE_HBTG.equals(info.getInsurstate()) || StaticVar.INSUR_STATE_YB.equals(info.getInsurstate()) || StaticVar.INSUR_STATE_TBL.equals(info.getInsurstate()) || StaticVar.INSUR_STATE_TB.equals(info.getInsurstate()))){
    			sb.append("，保单状态格式不对");
    		}
    	}
    	//新增购买的产品是否已经存在
    	if(info.getBuyid() == null){
    		//判断是否是附加险
	        Map<String,String> paramprod = new HashMap<String,String> (3);
	        paramprod.put("fundcode", info.getFundcode());
	        paramprod.put("isdel", StaticVar.INSUR_ISDEL_NO);
	        paramprod.put("prodproper", StaticVar.INSUR_PROPER_ATTR);
	        CmBxProduct product = cmBxProductService.getCmBxProduct(paramprod);
	        if(product == null ){
	        	sb.append("，新增的产品不是附加险");
	        }else{
	        	Map<String,String> paramgroup = new HashMap<String,String> (2);
	        	paramgroup.put("fundcode", pre.getFundcode());
	        	paramgroup.put("attfundcode", info.getFundcode());
	        	List<CmBxProductGroup> listgroup = cmBxProductGroupService.listCmBxProductGroup(paramgroup);
	        	if(listgroup == null || (listgroup != null && listgroup.size()==0)){
	        		sb.append("，新增的附加险没有绑定该主险");
	        	}
	    		Map<String,Object> param = new HashMap<String,Object> (3);
		        param.put("preid", info.getId());
		        param.put("fundcode", info.getFundcode());
		        param.put("isdel", StaticVar.INSUR_ISDEL_NO);
		        List<CmBxPrebookBuyinfo> listbuy = cmBxPrebookBuyinfoService.listCmBxPrebookBuyinfo(param);
		        if(listbuy != null && listbuy.size() > 0){
		        	sb.append("，新增的附加险已经存在");
		        }
	        }
    	}
    	if(StringUtil.isNotNullStr(sb.toString())){
    		return sb.toString().replaceFirst("，", "");
    	}else{
    		return sb.toString();
    	}
    	
    }
    
    /**
	 * 更新预约单到期状态
	 * @param request
	 * @return String
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/changeExpireDt.do")
	public String changeExpireDt(HttpServletRequest request) throws Exception{
		String result = "";
		User userlogin = getLoginUser();
		String id = request.getParameter("id");
		if (StringUtils.isNotBlank(id)) {
			//查询预约
	        Map<String,Object> parapre = new HashMap<String,Object>(1);
	        parapre.put("id", id);
			CmBxPrebookinfo preinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(parapre);
	        if(preinfo == null){
	            return "paramError";
	        }
			cmBxPrebookBuyinfoService.dealCmBxPrebookBuyinfoExpire(preinfo,userlogin.getUserId());
			result = "success";
		} else {
			result = "paramError";
		}
		return result;
	}
	
	/**
	 * 更新预约单缴费计划
	 * @param request
	 * @return String
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/updatePayPlay.do")
	public String updatePayPlay(HttpServletRequest request) throws Exception{
		String result = "";
		User userlogin = getLoginUser();
		String id = request.getParameter("id");
		if (StringUtils.isNotBlank(id)) {
			//查询预约
	        Map<String,Object> parapre = new HashMap<String,Object>(1);
	        parapre.put("id", id);
			CmBxPrebookinfo preinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(parapre);
	        if(preinfo == null){
	            return "paramError";
	        }
	        cmBxPrebookEndpayListService.dealCmBxPrebookEndpayList(preinfo,userlogin.getUserId());
			result = "success";
		} else {
			result = "paramError";
		}
		return result;
	}
	
	/**
     * 保存修改历史记录的预约
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveUpdateHisPrebook.do", method = RequestMethod.POST)
    public Map<String, Object> saveUpdateHisPrebook(HttpServletRequest request) throws Exception{
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        User user = getLoginUser();
    	String preid = StringUtil.replaceNull(request.getParameter("id"));
        String age = StringUtil.replaceNull(request.getParameter("age"));
        String insurage = StringUtil.replaceNull(request.getParameter("insurage"));
        String insurname = StringUtil.replaceNull(request.getParameter("insurname"));
        String insuridtype = StringUtil.replaceNull(request.getParameter("insuridtype"));
        String insuridno = StringUtil.replaceNull(request.getParameter("insuridno"));
        String relation = StringUtil.replaceNull(request.getParameter("relation"));
        String busisource = StringUtil.replaceNull(request.getParameter("busisource"));
        String otherrelation = StringUtil.replaceNull(request.getParameter("otherrelation"));
        String attrinsurs = StringUtil.replaceNull(request.getParameter("attrinsurs"));
        log.info("attrinsurs:{}" , attrinsurs);
        if(StringUtils.isBlank(attrinsurs)){
        	resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "至少输入一条购买产品信息！");
            return resultMap;
        }
        
    	String passdt = StringUtil.replaceNull(request.getParameter("passdt"));
    	String signdt = StringUtil.replaceNull(request.getParameter("signdt"));
    	String paydt = StringUtil.replaceNull(request.getParameter("paydt"));
    	String caltime = StringUtil.replaceNull(request.getParameter("caltime"));
    	String nextyearpaydt = StringUtil.replaceNull(request.getParameter("nextyearpaydt"));
    	String ratiodt = StringUtil.replaceNull(request.getParameter("ratiodt"));

    	Map<String, Object> paramsign = new HashMap<>(1);
    	paramsign.put("preid", preid);
        CmBxPrebookSigninfo cmBxPrebookSigninfo = cmBxPrebookSigninfoService.getCmBxPrebookSigninfo(paramsign);
        if(cmBxPrebookSigninfo != null){
	        cmBxPrebookSigninfo.setPassdt(passdt);
	        cmBxPrebookSigninfo.setSigndt(signdt);
	        cmBxPrebookSigninfo.setPaydt(paydt);
	        cmBxPrebookSigninfo.setCaltime(caltime);
	        cmBxPrebookSigninfo.setNextyearpaydt(nextyearpaydt);
	        cmBxPrebookSigninfo.setRatiodt(ratiodt);
	        cmBxPrebookSigninfo.setModifier(user.getUserId());
	        cmBxPrebookSigninfo.setModifydt(new Date());
        }

		//查询预约 往前移
		Map<String,Object> parampreinfo = new HashMap<String,Object>(1);
		parampreinfo.put("id", preid);
		CmBxPrebookinfo prebookinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(parampreinfo);
		//根据创建者 即所属投顾  获取创新方案
		String nowBxCommissionWay = "";
		if(null!= prebookinfo && StringUtils.isNotEmpty(prebookinfo.getCreator())){
			Map<String,String> param = new HashMap<>(1);
			param.put("conscode", prebookinfo.getCreator());
			CmConsultantExp info = cmConsultantExpService.getCmConsultantExpByConscode(param);
			nowBxCommissionWay = (null==info?"":info.getBxcommissionway());
			log.info("saveEnterCheckOrUpPrebook根据所属投顾:{}获取创新方案:{}",prebookinfo.getCreator(), nowBxCommissionWay);
		}

        List<CmBxPrebookBuyinfo> listbuyinfo = new ArrayList<>();
        String[]  arrCmBxPrebookBuyinfo = attrinsurs.split(":");
        for(String strCmBxPrebookBuyinfo : arrCmBxPrebookBuyinfo){
        	if(StringUtils.isNotBlank(strCmBxPrebookBuyinfo)){
        		String [] arrField = strCmBxPrebookBuyinfo.split(",");
        		Map<String, Object> parambuyinfo = new HashMap<>(1);
        		parambuyinfo.put("id", new BigDecimal(arrField[0]));
        		CmBxPrebookBuyinfo cmBxPrebookBuyinfo = cmBxPrebookBuyinfoService.getCmBxPrebookBuyinfo(parambuyinfo);
        		cmBxPrebookBuyinfo.setPayyears(arrField[1]);
            	cmBxPrebookBuyinfo.setEnsureyears(arrField[2]);
            	cmBxPrebookBuyinfo.setYearamk(StringUtils.isBlank(arrField[3]) ? null : new BigDecimal(arrField[3]));
            	if(arrField.length==5){
            		cmBxPrebookBuyinfo.setInsuramk(StringUtils.isBlank(arrField[4]) ? null : new BigDecimal(arrField[4]));
            	}
    			cmBxPrebookBuyinfo.setModifier(user.getUserId());
				cmBxPrebookBuyinfo.setBxCommissionWay(nowBxCommissionWay);
            	cmBxPrebookBuyinfo.setModifydt(new Date());
            	listbuyinfo.add(cmBxPrebookBuyinfo);
        		
        	}
        }

        prebookinfo.setAge("".equals(age) ? null : Integer.valueOf(age));
        prebookinfo.setInsurage("".equals(insurage) ? null : Integer.valueOf(insurage));
        prebookinfo.setInsurname(insurname);
        prebookinfo.setInsuridtype(insuridtype);
        prebookinfo.setInsuridno(insuridno);
        if(StringUtil.isNotNullStr(insuridno)){
    		prebookinfo.setInsuridnoDigest(DigestUtil.digest(insuridno.trim()));
    		prebookinfo.setInsuridnoMask(MaskUtil.maskIdNo(insuridno.trim()));
    		prebookinfo.setInsuridnoCipher(encryptSingleFacade.encrypt(insuridno.trim()).getCodecText());
    	}
        prebookinfo.setRelation(relation);
        prebookinfo.setBusisource(busisource);
        prebookinfo.setOtherrelation(otherrelation);
        prebookinfo.setModifydt(new Date());
        prebookinfo.setModifier(user.getUserId());
        cmBxPrebookinfoService.updateHisCmBxPrebookinfo(prebookinfo, cmBxPrebookSigninfo, listbuyinfo);
        resultMap.put("uploadFlag", "success");
        resultMap.put("errorMsg", "录入核保信息成功");
        return resultMap;
    }
    
    @RequestMapping("/opCalculateAmk.do")
    @ResponseBody
    public String opCalculateAmk(HttpServletRequest request) throws Exception{
    	String result="";
    	User user = getLoginUser();
    	String buyids = request.getParameter("buyids");
    	String type = request.getParameter("type");
    	if(StringUtils.isNotBlank(buyids)){
    		String[] buyidsArr = buyids.split(",");
    		if(buyidsArr != null && buyidsArr.length>0){
    			List<String> listBuyIds = Arrays.asList(buyidsArr);
    			Map<String,Object> querybuy = new HashMap<String,Object>(1);
            	querybuy.put("ids", Util.getOracleSQLIn(listBuyIds,999,"t.id"));
            	List<CmBxPrebookBuyinfo> listdata = cmBxPrebookBuyinfoService.listCmBxPrebookBuyinfo(querybuy);
            	cmBxPrebookBuyinfoService.batchCalculateAmk(listdata, user.getUserId(), type);
    		}
    	}

		result = "success";
        return result;
    }

	/**
	 * @description:(提供刷新历史接口 一次性)
	 * @param
	 * @return void
	 * @author: xfc
	 * @date: 2023/3/29 18:41
	 * @since JDK 1.8
	 */
	@RequestMapping(value = "/refreshhisotrydata", method = RequestMethod.GET)
	@ResponseBody
	public void refreshhisotrydata() {
		try {
			log.info("enter into enter refreshhisotrydata");
			// 查询 核保状态为 延保/拒保  退保(冷静期) 的缴款计划的id
			Map<String, String> params = new HashMap<>();
			params.put("paystate", StaticVar.INSUR_STATE_YB);
			List<CmBxPrebookEndpayList> yblists = cmBxPrebookEndpayListService.listCmBxPrebookEndpayListByidOrState(params);

			List<BigDecimal> ybIdList = yblists.stream().map(CmBxPrebookEndpayList::getId).collect(Collectors.toList());
			log.info("保单状态为 延保/拒保 的数据量，{}, id，{}", yblists.size(), JSON.json(ybIdList));
			params.clear();
			params.put("paystate", StaticVar.INSUR_STATE_TBL);
			List<CmBxPrebookEndpayList> tbllists = cmBxPrebookEndpayListService.listCmBxPrebookEndpayListByidOrState(params);
			List<BigDecimal> tblIdLists = tbllists.stream().map(CmBxPrebookEndpayList::getId).collect(Collectors.toList());
			log.info("保单状态为 退保(冷静期) 的数据量，id,{},{}", tbllists.size(), JSON.json(tblIdLists));
			yblists.addAll(tbllists);
			// 查询 核保状态为 正常退保(冷静期)的缴款计划的id
			params.clear();
			params.put("paystate", StaticVar.INSUR_STATE_TB);
			List<CmBxPrebookEndpayList> tblists = cmBxPrebookEndpayListService.listCmBxPrebookEndpayListByidOrState(params);
			List<BigDecimal> tbidlists = tblists.stream().map(CmBxPrebookEndpayList::getId).collect(Collectors.toList());
			log.info("保单状态为 正常退保(冷静期) 的数据量，{},id,{}", tblists.size(), JSON.json(tbidlists));
			// 每次 500 条数据批量操作
			int size = 500;
			batchUpdateList(size, yblists, false);
			batchUpdateList(size, tblists, true);
		} catch (Exception e) {
			log.error("error in refreshhisotrydata,{}", e.getMessage());
		}
	}


	/**
	 * @description:(批量更新核保状态和缴款状态)
	 * @param size
	 * @param cmBxPrebookEndpayList
	 * @param b
	 * @return void
	 * @author: xfc
	 * @date: 2023/3/28 16:19
	 * @since JDK 1.8
	 */
	public void batchUpdateList(int size, List<CmBxPrebookEndpayList> cmBxPrebookEndpayList, boolean b) {
		if (b) {
			cmBxPrebookEndpayList = cmBxPrebookEndpayList.stream().filter(it -> it.getPaystate().equals(StaticVar.INSUR_PAYSTAT_NOTPAY)).collect(Collectors.toList());
		}
		List<CmBxPrebookEndpayList> cmBxPrebookEndpayLists = new ArrayList<>();
		for (int i = 0; i < cmBxPrebookEndpayList.size(); i++) {
			cmBxPrebookEndpayLists.add(cmBxPrebookEndpayList.get(i));
			if (cmBxPrebookEndpayLists.size() == size) {
				cmBxPrebookEndpayListService.updateCmBxPrebookEndpayListByIds(cmBxPrebookEndpayLists);
				cmBxPrebookEndpayLists.clear();
			}
			if ((i+1) == cmBxPrebookEndpayList.size()) {
				cmBxPrebookEndpayListService.updateCmBxPrebookEndpayListByIds(cmBxPrebookEndpayLists);
				cmBxPrebookEndpayLists.clear();
			}
		}
	}

	/**
	 * @description(合并日志对象的属性)
	 * @param cmBxEditLogs
	 * @return java.util.List<com.howbuy.crm.hb.domain.insur.CmBxEditLog>
	 * @author: xfc
	 * @date: 2023/4/2 23:09
	 * @since JDK 1.8
	 */
	public List<CmBxEditLog> mergeCmBxEditLogList(List<CmBxEditLog> cmBxEditLogs) {
		List<CmBxEditLog> cmBxEditLogList = cmBxEditLogs.stream().collect(Collectors.toMap(CmBxEditLog::getPreid, a -> a, (o1, o2) -> {
			if (o1.getCaltime() != null || o2.getCaltime() != null) {
				o1.setCaltime(new BigDecimal(1));
			}
			if (o1.getPaydt() != null || o2.getPaydt() != null) {
				o1.setPaydt(new BigDecimal(1));
			}
			if (o1.getPayamt() != null || o2.getPayamt() != null) {
				o1.setPayamt(new BigDecimal(1));
			}
			if (o1.getEnsureyears() != null || o2.getEnsureyears() != null) {
				o1.setEnsureyears(new BigDecimal(1));
			}
			if (o1.getPassdt() != null || o2.getPassdt() != null) {
				o1.setPassdt(new BigDecimal(1));
			}
			if (o1.getChannel() != null || o2.getChannel() != null) {
				o1.setChannel(new BigDecimal(1));
			}
			if (o1.getPayyears() != null || o2.getPayyears() != null) {
				o1.setPayyears(new BigDecimal(1));
			}
			return o1;
		})).values().stream().collect(Collectors.toList());
		return cmBxEditLogList;
	}
}