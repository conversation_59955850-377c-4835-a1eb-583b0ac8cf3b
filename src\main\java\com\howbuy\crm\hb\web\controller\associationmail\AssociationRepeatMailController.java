package com.howbuy.crm.hb.web.controller.associationmail;

import com.howbuy.crm.hb.domain.associationmail.AssociationMail;
import com.howbuy.crm.hb.domain.associationmail.AssociationRepeatMail;
import com.howbuy.crm.hb.service.associationmail.AssociationMailService;
import com.howbuy.crm.hb.service.associationmail.AssociationRepeatMailService;
import com.howbuy.crm.hb.web.util.ResultCode;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.apache.commons.lang3.ArrayUtils;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 协会邮件-多个投顾客户号
 * Created by shucheng on 2021/6/4 18:32
 */
@Slf4j
@Controller
@RequestMapping("/associationRepeatMail")
public class AssociationRepeatMailController {

    @Autowired
    private AssociationRepeatMailService associationRepeatMailService;
    @Autowired
    private AssociationMailService associationMailService;

    @RequestMapping("/listAssociationRepeatMail.do")
    public ModelAndView listAssociationRepeatMail() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/associationmail/listAssociationRepeatMail");
        return modelAndView;
    }

    @RequestMapping("/listAssociationRepeatMailByPage.do")
    @ResponseBody
    public Map<String, Object> listAssociationRepeatMailByPage(HttpServletRequest request) throws Exception {
        Map<String, String> param = new ParamUtil(request).getParamMap();
        String handleStatus = param.get("handleStatus");
        if (handleStatus == null) {
            // 默认待处理
            param.put("handleStatus", "0");
        }
        PageData<AssociationRepeatMail> pageData = associationRepeatMailService.listAssociationRepeatMailByPage(param);
        // 返回查询结果
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        resultMap.put("rows", pageData.getListData());
        return resultMap;
    }

    /**
     * 显示查看页面
     * @return
     */
    @RequestMapping("/viewAssociationRepeatMail.do")
    public ModelAndView viewAssociationRepeatMail(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/associationmail/viewAssociationRepeatMail");

        String ids = request.getParameter("ids");
        AssociationRepeatMail associationRepeatMail = associationRepeatMailService.findAssociationRepeatMailByIds(ids);
        modelAndView.addObject("associationRepeatMail", associationRepeatMail);
        return modelAndView;
    }

    /**
     * 导出数据
     * @param request
     * @param response
     */
    @RequestMapping("/exportAssociationRepeatMail.do")
    public void exportAssociationRepeatMail(HttpServletRequest request, HttpServletResponse response) {
        ServletOutputStream os = null;
        try {
            Map<String, String> param = new ParamUtil(request).getParamMap();
            List<AssociationRepeatMail> associationRepeatMailList = associationRepeatMailService.listAssociationRepeatMail(param);

            // 导出数据
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            String fileName = "协会解析数据_多个投顾客户号_" + DateTimeUtil.getCurDateTime() + ".xls";
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));

            String [] columnName = new String []{
                    "邮件日期","收件人邮箱","客户姓名","机构名称","投资者账号","初始密码","管理人登记编码","登录链接","一账通号","投顾客户号"
            };

            String [] beanProperty = new String []{
                    "mailDateStr","toMail","custName","orgName","investorAccount","initPassword","managerRegno","loginHref","hboneNos","conscustnos"
            };
            os = response.getOutputStream();
            ExcelWriter.writeExcel(os, "协会解析数据_多个投顾客户号", 0, associationRepeatMailList, columnName, beanProperty);
        } catch (Exception e) {
            log.error("文件导出异常", e);
        } finally {
            // 关闭流
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 执行关联客户操作
     * @param request
     * @param associationRepeatMail 多个投顾客户号实体参数
     * @return
     */
    @RequestMapping("/relateConscustno.do")
    @ResponseBody
    public Map<String, Object> relateConscustno(HttpServletRequest request, AssociationRepeatMail associationRepeatMail) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            //选中的投顾客户号对应的那一条数据的ID
            String id = associationRepeatMail.getId();
            //新增校验   正常表是否已存在
            AssociationRepeatMail nowDto = associationRepeatMailService.findAssociationRepeatMailByIds(id);
            AssociationMail associationMail = new AssociationMail();
            BeanUtils.copyProperties(nowDto, associationMail);
            boolean exist = associationMailService.checkExcelDataExist(associationMail);
            if (exist) {
                resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
                resultMap.put("errorMsg", "操作失败，和已有数据重复");
            } else {
                User user = (User)request.getSession().getAttribute("loginUser");
                associationRepeatMail.setCreator(user.getUserId());
                associationRepeatMail.setModifier(user.getUserId());
                associationRepeatMailService.relateConscustno(associationRepeatMail);
                resultMap.put("errorCode", ResultCode.RESULT_CODE_SUCCESS);
                resultMap.put("errorMsg", "操作成功");
            }
        } catch (Exception e) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "操作失败");
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }

    /**
     * 批量修改处理状态
     * @param request
     * @param idsStr 待修改记录id的集合
     * @param associationRepeatMail
     * @return
     */
    @RequestMapping("/batchUpdateHandleStatusForRepeat.do")
    @ResponseBody
    public Map<String, Object> batchUpdateHandleStatusForRepeat(HttpServletRequest request, String idsStr, AssociationRepeatMail associationRepeatMail) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            String[] idArr = idsStr.split(",");
            User user = (User)request.getSession().getAttribute("loginUser");
            if (ArrayUtils.isNotEmpty(idArr)) {
                associationRepeatMailService.batchUpdateHandleStatusRemarkForRepeat(Arrays.asList(idArr), associationRepeatMail.getHandleStatus(),
                        user.getUserId(), associationRepeatMail.getRemark());
            } else {
                throw new RuntimeException();
            }
            resultMap.put("errorCode", ResultCode.RESULT_CODE_SUCCESS);
            resultMap.put("errorMsg", "操作成功");
        } catch (Exception e) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "操作失败");
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }
}
