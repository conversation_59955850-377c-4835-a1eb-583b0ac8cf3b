package com.howbuy.crm.hb.tools.excel.conf;

import com.howbuy.crm.hb.tools.excel.bean.BatchImpBean;
import com.howbuy.crm.hb.tools.excel.bean.BatchImpFile;

/**
 * @ClassName: Public
 * @Description:双录零售导入类
 */

public class publicDoubleRsBatchImp extends BatchImpBean {
	@Override
	public BatchImpBean inits() {
		
		// 交易单号
		BatchImpFile batchImpFile = new BatchImpFile();
		batchImpFile.setBeanPorperties("contractNo");
		batchImpFile.setRequire(true);
		batchImpFile.setBeanChineseName("交易单号");
		
		BatchImpFile[] arrays = new BatchImpFile[] {batchImpFile};
		this.setArrays(arrays);
		this.setBeanClassName("com.howbuy.crm.hb.domain.doubletrade.AddDoubleRsInfo");
		this.setColumnIndex(new int[] {0});
		return this;
	}

}
