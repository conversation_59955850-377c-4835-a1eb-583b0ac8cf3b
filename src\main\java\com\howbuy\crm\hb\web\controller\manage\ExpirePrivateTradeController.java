package com.howbuy.crm.hb.web.controller.manage;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.howbuy.crm.hb.service.manage.ExpirePrivateTradeService;
import com.howbuy.crm.hb.service.prosale.CustprivatefundService;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prosale.dto.Custprivatefund;
import com.howbuy.simu.dto.business.product.SmjzAndHbDto;
import com.howbuy.simu.service.business.product.SmjzAndHbService;

import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value = "/privatetrade")
public class ExpirePrivateTradeController {

	private static Logger LOG = LoggerFactory.getLogger(ExpirePrivateTradeController.class);
	
	@Autowired
	private ExpirePrivateTradeService expirePrivateTradeService;
	
	@Autowired
	private CustprivatefundService custprivatefundService;
	
	@Autowired
    private SmjzAndHbService smjzAndHbService;

	@Autowired
	private JjxxInfoService jjxxInfoService;

	@RequestMapping(value="/expireprivatetrade.do")
	public String expireprivatetrade(){
		return "/manage/expireprivatetrade";
	}
	
	/**
     * @param request
     * @param params
     * @return
     * @throws Exception
     * @Description 查看投顾任务完成度等级设置
     */
    @RequestMapping("/listExpirePrivateTradeLogByPage_Json.do")
    @ResponseBody
    public Map<String, Object> listExpirePrivateTradeLogByPage_Json(HttpServletRequest request) throws Exception {
        Map<String, Object> returnMap = new HashMap<>();
        Map<String, Object> param = new ParamUtil(request).getParamObjMap();
        PageData<Map<String, Object>> list = custprivatefundService.selectExpirePrivateTradeLogByPage(param);
        returnMap.put("total", list.getPageBean().getTotalNum());
        returnMap.put("page", list.getPageBean().getCurPage());
        returnMap.put("rows", list.getListData());
        return returnMap;
    }

	
	/**
	 * 手工处理到期赎回
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/dealExpirePrivateTrade.do")
	public Map<String, Object> dealExpirePrivateTrade(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		String fundcode = request.getParameter("fundcode");
		User user = (User) request.getSession().getAttribute("loginUser");
		if (StringUtils.isNotBlank(fundcode)) {
			JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(fundcode, false);
			if (jjxx1 != null) {
				//查询该产品有没有持仓客户
				Map<String,Object> param = new HashMap<>();
				param.put("fundcode", fundcode);
				param.put("hasbalancevol", "1");
				List<Custprivatefund> listfund = custprivatefundService.listCustprivatefund(param);
				if (listfund != null && listfund.size() > 0) {
					//查询该产品最近的净值和净值日期
					LOG.info("查询净值接口参数：jjdm:{} startDate:{} endDate:{}",fundcode,null,DateTimeUtil.getCurDate());
					List<SmjzAndHbDto> smjzAndHbDtos = smjzAndHbService.getByJjdm(fundcode,null,DateTimeUtil.getCurDate());
					LOG.info("--------------查询净值接口返回：{}",smjzAndHbDtos == null ? null : JSON.toJSONString(smjzAndHbDtos));
					Double amount = null;
					String navdt = "";
					if (CollectionUtils.isNotEmpty(smjzAndHbDtos)) {
						SmjzAndHbDto smjzAndHbDto = smjzAndHbDtos.get(0);
		                amount = smjzAndHbDto.getJjjz();
		                navdt = smjzAndHbDto.getJsrq();
		                if (amount != null && StringUtil.isNotNullStr(navdt)) {
		                	expirePrivateTradeService.batchDealExpirePrivateTrade(listfund, user.getUserId(), amount, navdt, jjxx1.getHmcpx());
		                	resultMap.put("saveFlag", "success");
		                	resultMap.put("errorMsg", "操作成功！");
		                } else {
		                	resultMap.put("saveFlag", "error");
		                	if (amount == null) {
								resultMap.put("errorMsg", "交易日净值为空，操作失败！");
			                }
			                if (StringUtil.isNullStr(navdt)) {
								resultMap.put("errorMsg", "交易日期为空，操作失败！");
			                }
		                }
		            } else {
		            	resultMap.put("saveFlag", "error");
						resultMap.put("errorMsg", "DB接口没有查询到净值信息！");
		            }
				} else {
					resultMap.put("saveFlag", "error");
					resultMap.put("errorMsg", "该产品无持仓客户！");
				}
				resultMap.put("saveFlag", "success");
			} else {
				resultMap.put("saveFlag", "error");
				resultMap.put("errorMsg", "产品代码没有传入！");
			}
		} else {
			resultMap.put("saveFlag", "error");
			resultMap.put("errorMsg", "缓存中没有找到该产品的信息！");
		}
		Map<String,String> paramlog = new HashMap<>();
		paramlog.put("conscode", user.getUserId());
		paramlog.put("fundcode", fundcode);
		if(resultMap.get("errorMsg") == null) {
			paramlog.put("des", "系统异常！");
		} else {
			paramlog.put("des", StringUtil.replaceNullStr(resultMap.get("errorMsg")));
		}
		custprivatefundService.insertOptDqshLog(paramlog);
		return resultMap;
	}
	
}
