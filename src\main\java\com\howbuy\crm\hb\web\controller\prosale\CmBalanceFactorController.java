package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.howbuy.crm.hb.domain.prosale.balancefactor.BalanceCustFundVo;
import com.howbuy.crm.hb.domain.prosale.balancefactor.BalanceFactorImportEntity;
import com.howbuy.crm.hb.domain.prosale.balancefactor.CmBalanceFactor;
import com.howbuy.crm.hb.service.prosale.CmBalanceFactorService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/4/19 16:10
 */
@Slf4j
@Controller
@RequestMapping("/balanceFactor")
public class CmBalanceFactorController  extends BaseController {

    @Autowired
    private CmBalanceFactorService cmBalanceFactorService;

    private final String BALANCE_FACTOR_TEMPLATE_NAME = "平衡因子导入模板.xls";
    private final String BALANCE_FACTOR_TEMPLATE_FILE = "balanceFactorTemplate.xls";

    @RequestMapping("/listCustFund")
    public ModelAndView listCustFund(){
        return new ModelAndView("/prosale/balanceFactor/listBalanceFactor");
    }

    @RequestMapping("/listCustFundJson")
    @ResponseBody
    public Object listCustFundJson(HttpServletRequest request) throws Exception{
        Map<String, String> param = new ParamUtil(request).getParamMap();
        PageData<BalanceCustFundVo> pageData = cmBalanceFactorService.listCustAndFundByPage(param);
        Map<String, Object> resultMap = new HashMap(3);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        resultMap.put("rows", pageData.getListData());
        return resultMap;
    }

    /**
     * 查询列表
     *
     * @param cmBalanceFactor
     * @return
     */
    @RequestMapping("/listBalanceFactor")
    @ResponseBody
    public Object listBalanceFactor(CmBalanceFactor cmBalanceFactor) {
        return cmBalanceFactorService.listBalanceFactor(cmBalanceFactor);
    }


    @RequestMapping("/addBalanceFactor")
    public String addBalanceFactor(BalanceCustFundVo balanceCustFundVo, Map map){
        map.put("balanceCustFundVo", balanceCustFundVo);
        return "/prosale/balanceFactor/addBalanceFactor";
    }
    
    @RequestMapping("/editBalanceConvertFinish")
    public String editBalanceConvertFinish(BalanceCustFundVo balanceCustFundVo, Map map){
        map.put("balanceCustFundVo", balanceCustFundVo);
        return "/prosale/balanceFactor/editBalanceConvertFinish";
    }

    @RequestMapping("/modifyBalanceFactor")
    public String modifyBalanceFactor(String id, Map map){
        map.put("balanceCustFundVo", cmBalanceFactorService.getCmBalanceFactorById(id));
        return "/prosale/balanceFactor/modifyBalanceFactor";
    }

    @RequestMapping("/auditBalanceFactor")
    public String auditBalanceFactor(String id, Map map){
        map.put("balanceCustFundVo", cmBalanceFactorService.getCmBalanceFactorById(id));
        return "/prosale/balanceFactor/auditBalanceFactor";
    }

    /**
     * 插入
     *
     * @param cmBalanceFactor
     */
    @RequestMapping("/add.do")
    @ResponseBody
    public String add(CmBalanceFactor cmBalanceFactor, HttpServletRequest request) {
        String id = cmBalanceFactorService.getCmBalanceFactorByCustFund(cmBalanceFactor.getConscustno(), cmBalanceFactor.getFundCode(), cmBalanceFactor.getNavDt());
        if(id != null){
            return "repeat";
        }
        //判断是否有无需审核权限
        List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
        //是否有无需审核权限
        boolean cancheck = false;
        for (String role : roles) {
            List<String> temp = AuthCache.getInstance().getOperListStr(role, "030804");
            if (temp != null && temp.contains("7")) {
            	cancheck = true;
                break;
            }
        }
        String userId = (String) request.getSession().getAttribute("userId");
        cmBalanceFactor.setCreator(userId);
        if(cancheck){
        	//有无需审核权限，直接审核通过
        	cmBalanceFactor.setAuditStatus(StaticVar.BALANCE_FACTOR_AUDIT_PASS);
        }else{
        	cmBalanceFactor.setAuditStatus(StaticVar.BALANCE_FACTOR_AUDIT_UNHANDLE);
        }
        //处理平衡因子转换完成字段
        CmBalanceFactor objparam = new CmBalanceFactor();
        objparam.setConscustno(cmBalanceFactor.getConscustno());
        objparam.setFundCode(cmBalanceFactor.getFundCode());
    	List<CmBalanceFactor> list =cmBalanceFactorService.listBalanceFactor(cmBalanceFactor);
    	//客户+产品维度查到数据
    	if(list != null && list.size() > 0){
    		if(StringUtil.isNotNullStr(list.get(0).getConvertFinish())){
    			cmBalanceFactor.setConvertFinish(list.get(0).getConvertFinish());
    		}else{
    			cmBalanceFactor.setConvertFinish(StaticVar.NO);
    		}
    	}else{
    		cmBalanceFactor.setConvertFinish(StaticVar.NO);
    	}
        cmBalanceFactorService.insert(cmBalanceFactor);
        return "success";
    }

    /**
     * 更新
     *
     * @param cmBalanceFactor
     */
    @RequestMapping("/update.do")
    @ResponseBody
    public String update(CmBalanceFactor cmBalanceFactor, HttpServletRequest request) {
        String id = cmBalanceFactorService.getCmBalanceFactorByCustFund(cmBalanceFactor.getConscustno(), cmBalanceFactor.getFundCode(), cmBalanceFactor.getNavDt());
        if(id != null && !id.equals(cmBalanceFactor.getId())){
            return "repeat";
        }
        //是否有无需审核权限
        List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
        boolean cancheck = false;
        for (String role : roles) {
            List<String> temp = AuthCache.getInstance().getOperListStr(role, "030804");
            if (temp != null && temp.contains("7")) {
            	cancheck = true;
                break;
            }
        }
        String userId = (String) request.getSession().getAttribute("userId");
        cmBalanceFactor.setUpdateMan(userId);
        if(cancheck){
        	//有无需审核权限，直接审核通过
        	cmBalanceFactor.setAuditStatus(StaticVar.BALANCE_FACTOR_AUDIT_PASS);
        }else{
        	cmBalanceFactor.setAuditStatus(StaticVar.BALANCE_FACTOR_AUDIT_UNHANDLE);
        }
        cmBalanceFactorService.update(cmBalanceFactor);
        return "success";
    }
    
    /**
     * 更新平衡因子转换完成
     *
     * @param cmBalanceFactor
     */
    @RequestMapping("/updateConvertFinish.do")
    @ResponseBody
    public String updateConvertFinish(CmBalanceFactor cmBalanceFactor, HttpServletRequest request) {
    	String userId = (String) request.getSession().getAttribute("userId");
        cmBalanceFactor.setUpdateMan(userId);
        cmBalanceFactorService.updateConvertFinish(cmBalanceFactor);
        return "success";
    }

    /**
     * 审核通过
     *
     * @param cmBalanceFactor
     */
    @RequestMapping("/pass.do")
    @ResponseBody
    public String pass(CmBalanceFactor cmBalanceFactor, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        cmBalanceFactor.setUpdateMan(userId);
        cmBalanceFactor.setAuditStatus(StaticVar.BALANCE_FACTOR_AUDIT_PASS);
        cmBalanceFactorService.update(cmBalanceFactor);
        return "success";
    }

    /**
     * 审核不通过
     *
     * @param cmBalanceFactor
     */
    @RequestMapping("/reject.do")
    @ResponseBody
    public String reject(CmBalanceFactor cmBalanceFactor, HttpServletRequest request) {
        String userId = (String) request.getSession().getAttribute("userId");
        cmBalanceFactor.setUpdateMan(userId);
        cmBalanceFactor.setAuditStatus(StaticVar.BALANCE_FACTOR_AUDIT_REJECT);
        cmBalanceFactorService.update(cmBalanceFactor);
        return "success";
    }

    /**
     * 删除
     *
     * @param id
     */
    @RequestMapping("/delete.do")
    @ResponseBody
    public String delete(String id) {
        if(StringUtil.isNullStr(id)){
            return "error";
        }
        cmBalanceFactorService.delete(id);
        return "success";
    }

    /**
     *
     *
     * @param request
     */
    @RequestMapping("importBalanceFactor.do")
    @ResponseBody
    public String importBalanceFactor(HttpServletRequest request)throws Exception {
    	
        String userId = (String) request.getSession().getAttribute("userId");
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("file");
        StringBuilder result = new StringBuilder();
        //读取excel
        EasyExcel.read(file.getInputStream(), BalanceFactorImportEntity.class, new ReadListener<BalanceFactorImportEntity>() {

            /**
             * 单次缓存的数据量
             */
            public static final int BATCH_COUNT = 1000;
            /**
             * 临时缓存
             */
            private List<BalanceFactorImportEntity> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

            @Override
            public void invoke(BalanceFactorImportEntity data, AnalysisContext context) {
                cachedDataList.add(data);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext context) {
                try {
                	//判断是否有无需审核权限
                    List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
                    //是否有无需审核权限
                    boolean cancheck = false;
                    for (String role : roles) {
                        List<String> temp = AuthCache.getInstance().getOperListStr(role, "030804");
                        if (temp != null && temp.contains("7")) {
                        	cancheck = true;
                            break;
                        }
                    }
                    result.append(cmBalanceFactorService.importData(cachedDataList, userId, cancheck));
                }catch (Exception e){
                    log.error(e.getMessage(), e);
                    result.append(BaseConstantEnum.SYS_ERROR.getDescription());
                }
                log.info("存储数据库成功！");
            }
        }).sheet().doRead();
        return result.toString();
    }

    @RequestMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response){
        dowmloadTemplate(BALANCE_FACTOR_TEMPLATE_FILE,BALANCE_FACTOR_TEMPLATE_NAME,request,response);
    }
}
