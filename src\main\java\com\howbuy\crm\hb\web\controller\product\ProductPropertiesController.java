package com.howbuy.crm.hb.web.controller.product;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.howbuy.crm.hb.web.util.ParamUtil;
import com.howbuy.crm.hb.domain.product.ProductManagerInfo;
import com.howbuy.crm.hb.domain.product.ProductPropertirs;
import com.howbuy.crm.hb.service.product.ProductPropertirsService;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.ConstantCache;
import crm.howbuy.base.db.CommPageBean;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateUtil;

@Controller
@RequestMapping(value = "/proPer")
public class ProductPropertiesController {
	@Autowired
	private ProductPropertirsService productPropertirsService;

	@RequestMapping(value="/toProPerInfoListView.do")
	public String toProductInfoList(HttpServletRequest request){
		return "/product/proPerInfoList";
	}
	
	@ResponseBody
	@RequestMapping(value="/proPerList.do")
	public Map<String,Object> proPerList(HttpServletRequest request) throws Exception{
		Map<String,Object> resultMap = new HashMap<String, Object>();
		Map<String,String> paramMap = new HashMap<String, String>();
		paramMap = new ParamUtil(request).getParamMap();
		PageData<ProductPropertirs> result = productPropertirsService.getProductInfoList(paramMap);
		CommPageBean bean = result.getPageBean();
		List<ProductPropertirs> listData = result.getListData();
		ConstantCache instance = ConstantCache.getInstance();
		LinkedHashMap<String, String> eleContractMap = instance.getConstantKeyVal("isEleContract");
		LinkedHashMap<String, String> calTradeNumMap=instance.getConstantKeyVal("isCalTradeNum");
		for (ProductPropertirs pro : listData) {
			String eleContract = pro.getIsElecContract();
			String calTradeNum = pro.getIscaltradenum();
			
			if(StringUtils.isNotBlank(eleContract)){
				if(eleContractMap.containsKey(eleContract)){
					pro.setIsElecContract(eleContractMap.get(eleContract));
				}
			}
			
			if(StringUtils.isNotBlank(calTradeNum)){
				if(calTradeNumMap.containsKey(calTradeNum)){
					pro.setIscaltradenum(calTradeNumMap.get(calTradeNum));
				}
			}
		}
		resultMap.put("rows", listData);
		resultMap.put("total",bean.getTotalNum() );
		return resultMap;
	}
	
	
	@ResponseBody
	@RequestMapping(value="/toUpdProPreDetail.do")
	public ProductPropertirs toUpdProPreDetail(String pcode,HttpServletRequest request) throws Exception{
		ProductPropertirs bean = productPropertirsService.getProductPropertirsDetail(pcode);
		return bean;
	}
	
	
	@ResponseBody
	@RequestMapping(value="/updateProPre.do")
	public String updateProductPropertirs(ProductPropertirs bean,HttpServletRequest request) throws Exception{
		HttpSession session = request.getSession();
		String userId=(String) session.getAttribute("userId");
		bean.setModifier(userId);
		bean.setModdt(DateUtil.getDateFormat(new Date(), "yyyyMMdd"));
		productPropertirsService.updateProductPropertirsInfo(bean);
		return "updSucc";
	}
	
	@ResponseBody
	@RequestMapping(value="/getProPreDetail.do")
	public ProductPropertirs getProPreDetail(String pcode,HttpServletRequest request) throws Exception{
		ProductPropertirs bean = productPropertirsService.getProductPropertirsDetail(pcode);
		return bean;
	}
	
	@ResponseBody
	@RequestMapping(value="/insertProPre.do")
	public String insertProductPropertirs(ProductPropertirs bean,HttpServletRequest request) throws Exception{
		HttpSession session = request.getSession();
		String userId=(String) session.getAttribute("userId");
		bean.setCreator(userId);
		bean.setCredt(DateUtil.getDateFormat(new Date(), "yyyyMMdd"));
		bean.setCheckflag("1");
		productPropertirsService.insertProductPropertirsInfo(bean);
		return "addSucc";
	}
	
	
	@ResponseBody
	@RequestMapping(value="/autoProductList.do")
	public Map<String, List<FundCode>> autoProductList(HttpServletRequest request){
		Map<String, String> param = new HashMap<String, String>();
		String searchParam = request.getParameter("term");
		param.put("searchParam", searchParam.toUpperCase());
		List<ProductManagerInfo> listProduct = productPropertirsService.listProductAuto(param);
		List<FundCode> list = new ArrayList<FundCode>();
		for(ProductManagerInfo info : listProduct){
			FundCode fc = new FundCode();
			fc.setCode(Util.ObjectToString(info.getPcode()));
			fc.setName(Util.ObjectToString(info.getPname()));
			fc.setPinyin(Util.ObjectToString(info.getJjpy()));
			list.add(fc);
		}
		Map<String,List<FundCode>> result = new HashMap<String,List<FundCode>>();
		result.put("result", list);
		return result;
	}
	
	
	@ResponseBody
	@RequestMapping(value="/autoProPertiesList.do")
	public Map<String, List<FundCode>> autoProPertiesList(HttpServletRequest request){
		Map<String, String> param = new HashMap<String, String>();
		String searchParam = request.getParameter("term");
		param.put("searchParam", searchParam.toUpperCase());
		 List<ProductPropertirs> listProductPropertirsAuto = productPropertirsService.listProductPropertirsAuto(param);
		List<FundCode> list = new ArrayList<FundCode>();
		for(ProductPropertirs info : listProductPropertirsAuto){
			FundCode fc = new FundCode();
			fc.setCode(Util.ObjectToString(info.getPcode()));
			fc.setName(Util.ObjectToString(info.getPname()));
			fc.setPinyin(Util.ObjectToString(info.getJjpy()));
			list.add(fc);
		}
		Map<String,List<FundCode>> result = new HashMap<String,List<FundCode>>();
		result.put("result", list);
		return result;
	}

	
}
