package com.howbuy.crm.hb.web.controller.prosale;

import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.base.trade.FeeRateMethodEnum;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.vo.PreBookDealPayViewVo;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.prebook.dto.ShouldFeeResultDto;
import com.howbuy.crm.prebook.request.PrebookTradeDealRequest;
import com.howbuy.crm.prebook.service.PrebookPayService;
import com.howbuy.crm.prebook.service.PrebookTradeDealService;
import com.howbuy.crm.prebook.vo.CmPreBookBankVo;
import com.howbuy.crm.prebook.vo.PreBookDealPayVo;
import com.howbuy.crm.prebook.vo.PreBookPayVo;
import com.howbuy.crm.util.exception.BusinessException;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2019/8/5 10:09
 */
@Slf4j
@Controller
@RequestMapping(value = "/prosale")
public class PrebookPayController  extends BaseController {

    @Autowired
    private PrebookTradeDealService prebookTradeDealService ;

    @Autowired
    private PrebookPayService prebookPayService;

    @RequestMapping("/applyPay.do")
    public ModelAndView applyPay(HttpServletRequest request){
        String id = request.getParameter("id");
        Map<String,Object> map = new HashMap<>();
        map.put("menucode", request.getParameter("menuCode"));
        map.put("optcode", request.getParameter("optcode"));

        BigDecimal preId = new BigDecimal(id);
        ReturnMessageDto<PreBookDealPayVo>  payResp = prebookPayService.getPayInfoVo(preId);
        if(!payResp.isSuccess()){
            throw  new BusinessException(payResp.getReturnMsg());
        }
        PreBookDealPayVo payVo = payResp.getReturnObject();
        //转义为 页面对象
        PreBookDealPayViewVo viewVo = transferToView(payVo);
        //转换为页面 map
        map.putAll(transformToMap(viewVo));
        map.put("menucode", request.getParameter("menuCode"));
        map.put("optcode", request.getParameter("optcode"));

        return new ModelAndView("prosale/applyPay","map",map);
    }

    /**
     * @description:(页面属性 map)
     * @param viewVo
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @author: haoran.zhang
     * @date: 2025/6/17 19:05
     * @since JDK 1.8
     */
  private Map<String, Object> transformToMap(PreBookDealPayViewVo viewVo) {
    Map<String, Object> map = new HashMap<>();

    map.put("id", viewVo.getId());
    map.put("pname", viewVo.getPname());
    map.put("conscustname", viewVo.getConscustname());
    map.put("currency", viewVo.getCurrency());
    map.put("realpayamt", viewVo.getRealpayamt());
    map.put("buyamt", viewVo.getBuyamt());
    map.put("expectpayamtdt", viewVo.getExpectpayamtdt());
    map.put("expecttradedt", viewVo.getExpecttradedt());
    map.put("realpayamtdt", viewVo.getRealpayamtdt());
    map.put("realbuyman", viewVo.getRealbuyman());
    map.put("custno", viewVo.getCustno());
    map.put("fee", viewVo.getFee());
    map.put("dxflag", viewVo.isDxflag());
    map.put("isfccl", viewVo.getIsfccl());
    map.put("shouldfee", viewVo.getShouldfee());
    map.put("feeRateMethod", viewVo.getFeeRateMethod());
    map.put("totalamt", viewVo.getTotalamt());
    map.put("sfxg", viewVo.isSfxg());
    map.put("bankacct", viewVo.getBankacct());
    map.put("bankcode", viewVo.getBankcode());
    map.put("bankprov", viewVo.getBankprov());
    map.put("bankcity", viewVo.getBankcity());
    map.put("bankaddr", viewVo.getBankaddr());
    map.put("bankList", viewVo.getBankList());
    map.put("bankSize", viewVo.getBankSize());
    map.put("discountway", viewVo.getDiscountway());
    map.put("discountrate", viewVo.getDiscountrate());
    map.put("beforetaxamt", viewVo.getBeforetaxamt());
    map.put("discountStateDesc", viewVo.getDiscountStateDesc());

    return map;
}


    /**
     * @description:(页面属性处理)
     * @param payVo
     * @return com.howbuy.crm.hb.web.vo.PreBookDealPayViewVo
     * @author: haoran.zhang
     * @date: 2025/4/17 17:42
     * @since JDK 1.8
     */
    private PreBookDealPayViewVo  transferToView(PreBookDealPayVo payVo){
        PreBookDealPayViewVo vo = new PreBookDealPayViewVo();
        vo.setId(payVo.getPreId().toPlainString());
        vo.setPname(payVo.getProdName());
        vo.setConscustname(payVo.getConsCustName());
        vo.setCurrency(ConstantCache.getInstance().getVal("currencys", payVo.getCurrency()));
        vo.setRealpayamt(payVo.getRealPayAmt() == null ? null : payVo.getRealPayAmt().divide(BIG_DECIMAL_1W).toPlainString());
        vo.setBuyamt(payVo.getBuyAmt() == null ? null : payVo.getBuyAmt().divide(BIG_DECIMAL_1W).toPlainString());
        vo.setExpectpayamtdt(payVo.getExpectPayAmtDt());
        vo.setExpecttradedt(payVo.getExpectTradeDt());
        vo.setRealpayamtdt(payVo.getRealPayAmtDt());
        vo.setRealbuyman(payVo.getRealBuyMan());
        vo.setCustno(payVo.getConsCustNo());
        vo.setFee(payVo.getFee() == null ? null : payVo.getFee().toPlainString());

        vo.setDxflag(payVo.isDxFlag());

        //是否 分次call
        vo.setIsfccl(payVo.getIsFccl());

        DecimalFormat df = new DecimalFormat("#0.00");

        //计算  应收打款金额
        vo.setShouldfee(payVo.getShouldFee() == null ? "" : df.format(payVo.getShouldFee().doubleValue()));

        //认缴金额
        BigDecimal subscribeAmt = payVo.getSubscribeAmt();
        vo.setFeeRateMethod(payVo.getFeeRateMethod() == null ? null : FeeRateMethodEnum.getDescription(payVo.getFeeRateMethod()));
        vo.setTotalamt(subscribeAmt == null ? null : subscribeAmt.divide(BIG_DECIMAL_1W).toPlainString());

        //是否香港产品
        vo.setSfxg(payVo.isSfxg());
        //预约银行卡信息
        PreBookDealPayVo.PayBankInnerVo  bankInnerVo= payVo.getBankInfo();
        if(bankInnerVo != null){
            vo.setBankacct(bankInnerVo.getBankAcct());
            vo.setBankcode(bankInnerVo.getBankCode());
            vo.setBankprov(bankInnerVo.getBankProv());
            vo.setBankcity(bankInnerVo.getBankCity());
            vo.setBankaddr(bankInnerVo.getBankAddr());
        }else{
            vo.setBankacct("");
            vo.setBankcode("");
            vo.setBankprov("");
            vo.setBankcity("");
            vo.setBankaddr("");
        }
        //用户持有银行卡
        List<Map<String,Object>> bankViewList =transferBankListView(payVo.getBankList());
        vo.setBankList(bankViewList);
        vo.setBankSize(bankViewList.size());

        vo.setDiscountway(payVo.getDiscountWay()==null?"":
                ConstantCache.getInstance().getConstantKeyVal("newdiscountWays").get(payVo.getDiscountWay()));
        vo.setDiscountrate(payVo.getDiscountRate()==null?"":
                df.format(payVo.getDiscountRate().doubleValue()));
        vo.setBeforetaxamt(payVo.getBeforeTaxAmt()==null?"":
                df.format(payVo.getBeforeTaxAmt().doubleValue()));
        vo.setDiscountStateDesc(payVo.getDiscountStateDesc());
        return  vo;
    }

    /**
     * 获取页面下拉框 银行卡 信息列表
     * @param innerBankList
     * @return
     */
    private List<Map<String,Object>> transferBankListView(List<PreBookDealPayVo.PayBankInnerVo>  innerBankList){
        List<Map<String,Object>> bankList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(innerBankList)){
            innerBankList.forEach(innerBank->{
                Map<String, Object> bank = new HashMap<>();
                bank.put("id", innerBank.getBankAcct());
                bank.put("text", innerBank.getBankAcct() + ' ' + innerBank.getBankName());
                bank.put("bankprov", innerBank.getBankProv());
                bank.put("bankcity", innerBank.getBankCity());
                bank.put("bankaddr", innerBank.getBankAddr());
                bank.put("bankcode", innerBank.getBankCode());
                bank.put("bankname", innerBank.getBankName());
                bankList.add(bank);
            });
        }
        return bankList;
    }

    
    @ResponseBody
	@RequestMapping("/saveApplyPay")
	public ReturnMessageDto<String> saveApplyPay(HttpServletRequest request) throws Exception {

			String preid = request.getParameter("preid");
			String realpayamt = request.getParameter("realpayamt");
			String fee = request.getParameter("fee");
			String realpayamtdt = request.getParameter("realpayamtdt");
			String bankacct = request.getParameter("bankacct");
			String bankcode = request.getParameter("bankcode");
			String bankprov = request.getParameter("bankprov");
			String bankcity = request.getParameter("bankcity");
			String bankaddr = request.getParameter("bankaddr");
            String hkCpAcctNoList = request.getParameter("hkCpAcctNoList");


            //构建 请求参数
            PrebookTradeDealRequest payRequest = new PrebookTradeDealRequest();
            payRequest.setPreId(new BigDecimal(preid));
            payRequest.setOperator(getLoginUserId());

            // 当为香港产品的并且选择了对应的银行卡
            if (StringUtil.isNotNullStr(hkCpAcctNoList)) {
                payRequest.setHkCpAcctNoList(Arrays.asList(hkCpAcctNoList.split(",")));
            }
            //打款金额相关
            PreBookPayVo payVo = new PreBookPayVo();
            payVo.setFee(new BigDecimal(fee));
            payVo.setRealPayAmt(new BigDecimal(realpayamt).multiply(new BigDecimal(10000)));
            payVo.setRealPayAmtDt(realpayamtdt);

            //银行卡信息
            CmPreBookBankVo bankVo = new CmPreBookBankVo();
            if(StringUtil.isNotNullStr(bankacct)) {
                bankVo.setBankAcct(bankacct);
            }
            if(StringUtil.isNotNullStr(bankcode)) {
                bankVo.setBankCode(bankcode);
            }
            if(StringUtil.isNotNullStr(bankprov)) {
                bankVo.setBankProv(bankprov);
            }
            if(StringUtil.isNotNullStr(bankcity)) {
                bankVo.setBankCity(bankcity);
            }
            if(StringUtil.isNotNullStr(bankaddr)) {
                bankVo.setBankAddr(bankaddr);
            }
            payRequest.setPreBookPayVo(payVo);
            payRequest.setBankVo(bankVo);
            return prebookTradeDealService.executeApplyPay(payRequest);

	}
    
    @ResponseBody
	@RequestMapping("/chargeFeeException")
	public String chargeFeeException(HttpServletRequest request){
		String result = "";
		String preid = request.getParameter("preid");
		String fee = request.getParameter("fee");
		BigDecimal shouldFee = BigDecimal.ZERO;
		 //查询应打款手续费金额
        ReturnMessageDto<ShouldFeeResultDto> shouldFeeResp= prebookPayService.calculateShouldFee(new BigDecimal(preid));
        if(shouldFeeResp != null && shouldFeeResp.isSuccess()){
        	shouldFee = shouldFeeResp.getReturnObject().getShouldFee();
        }
        if(shouldFee.compareTo(new BigDecimal(fee))==0){
        	result = "success";
        }else{
        	result = "tip";
        }
		return result;
	}
}
