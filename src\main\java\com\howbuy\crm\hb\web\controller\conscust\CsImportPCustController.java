package com.howbuy.crm.hb.web.controller.conscust;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.utils.DataPattern;
import com.howbuy.crm.hb.domain.conscust.AddPCustByAdviserInfo;
import com.howbuy.crm.hb.domain.conscust.AddPCustInfo;
import com.howbuy.crm.hb.domain.conscust.CsPotentialUploadLog;
import com.howbuy.crm.hb.service.conscust.CsPotentialUploadLogService;
import com.howbuy.crm.hb.service.conscust.PotentialCustService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ExcelUtils;
import com.howbuy.crm.page.framework.utils.ValidateUtil;
import com.howbuy.crm.util.ArtificialGrade;
import com.howbuy.crm.util.ParamUtil;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.validate.IDContentValidator;
import crm.howbuy.base.validate.VarChar2Validator;
import jxl.Sheet;
import jxl.Workbook;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "/conscust")
@Slf4j
public class CsImportPCustController  extends BaseController {
	private static final String DOWNLOAD_FILE_NAME = "名单导入模版.xls";

	private static final String MODEL_FILE_NAME = "inputcustmodel.xls";
	
	private static final String DOWNLOAD_USER_FILE_NAME = "CRM名单导入功能使用教程.pdf";

	private static final String USER_MODEL_FILE_NAME = "inputcustloadusermodel.pdf";
	
	private static final String DOWNLOAD_CONS_MODEL_NAME = "名单导入模版（投顾）.xls";
	
	private static final String DOWNLOAD_CONS_MODEL_FILE = "inputcustconsmodel.xls";
	
	private static final String DOWNLOAD_CONS_GUIDE_NAME = "CRM名单导入功能使用教程（投顾）.pdf";
	
	private static final String DOWNLOAD_CONS_GUIDE_FILE = "inputcustloadconsguide.pdf";

	private static final String CN_MOBILE_AREA_CODE = "+86";
	
	@Autowired
	private PotentialCustService potentialCustService;
	@Autowired
	private CsPotentialUploadLogService csPotentialUploadLogService;
	
	/**
	 * <p>功能描述：导入用户页面</p>
	 * <p>创建日期：2014年11月27日</p>
	 * @return String
	 * <AUTHOR>
	 * @update [更改日期 yyyy-MM-dd] [更改人姓名]
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value="/inputPCustIndex.do")
	public String inputPCustIndex(HttpServletRequest request, HttpServletResponse response, ModelMap model){
		List<String> roles = (List<String>)request.getSession().getAttribute("loginRoles");
		response.setContentType("text/html; charset=utf-8");
		String menuCode = "120424";
		String operCode = "01";
		boolean hasAuth = false;
		for(String role : roles){
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menuCode);
			if(temp != null && temp.contains(operCode)){
				hasAuth = true;
			}
		}
		
		model.put("hasAuth", hasAuth);
		return "/conscust/inputPCustIndex";
	}
	
	
	/**
     * <p>功能描述：获取所有上传日志文件信息</p>
     * <p>创建日期：2014年11月27日</p>
     * @return Map<String,Object>
     * <AUTHOR>
     * @update [更改日期 yyyy-MM-dd] [更改人姓名]
     */
	@RequestMapping("/listPotentialCustLogInfo.do")
	public @ResponseBody Map<String, Object> listPotentialCustLogInfo(HttpServletRequest request,HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		// 获取分页参数
		param = new ParamUtil(request).getParamMap();
		
		// 根据权限模型判断列表显示内容
		param.put("conscode", Util.getSubUserQueryByUserId(request));
		PageData<CsPotentialUploadLog> csPotentialUploadLogData = csPotentialUploadLogService.listPotentialCustByPage(param);
		
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", csPotentialUploadLogData.getPageBean().getTotalNum());
		resultMap.put("rows", csPotentialUploadLogData.getListData());
		
		return resultMap;
	}
	
	/**
	 * <p>功能描述：导入用户</p>
	 * <p>创建日期：2014年11月27日</p>
	 * @return Map<String,Object>
	 * <AUTHOR>
	 * @update [更改日期 yyyy-MM-dd] [更改人姓名]
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value="/inputPCust.do",method=RequestMethod.POST)
	public  @ResponseBody Map<String, Object> inputPCust(HttpServletRequest request){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		InputStream input = null;
		Workbook workBook = null;
		
		String errorMsg = "";
		String uploadFlag = "success";
		
		try {  
			// 转型为MultipartHttpRequest：  
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;  
			// 获得文件：  
			MultipartFile file = multipartRequest.getFile("file");  
			// 获得文件名：  
			String fileName = file.getOriginalFilename();    
			
			String batchID = multipartRequest.getParameter("batchID");
			
			// 获得输入流：  
			input = file.getInputStream();  
			
			Map<String,Object> param = new HashMap<String,Object>();
			param.put("batchID", batchID);
			
			boolean existFlag = potentialCustService.existBatchID(param);
			
			if(!existFlag){
				workBook = Workbook.getWorkbook(input);
				
				// 去掉之前导入的一二级旧来源，统一成现在的新来源编码
				String[] colPropertity = {"newSourceNo",
						"custName", "addr", "postCode", "mobileAreaCode","mobile", "telNo",
						"email", "consCode", "PGradeStr","memo"};
				
				Sheet sheet = workBook.getSheet(0);
				
				// 将之前获取Excel的13列数据改为12列
				List<AddPCustInfo> postList = ExcelUtils.getListByReadShell(sheet, 1, 0, 10, colPropertity,AddPCustInfo.class);
				
				if (null == postList || postList.isEmpty()) {
					errorMsg = "没有上传记录";
					uploadFlag = "error";
				} else {
					int line = 2;
					for (AddPCustInfo importParam : postList) {
						String validateMsg = this.checkAdd(importParam);
						if (StringUtils.isNotEmpty(validateMsg)) {
							errorMsg = "第 " + line + " 行错误是：" + validateMsg;
							uploadFlag = "error";
							
							break;
						}
						line++;
					}
					
					if("success".equals(uploadFlag)){  //符合条件
						HttpSession loginSession = request.getSession(); 
						User loginUser = (User)loginSession.getAttribute("loginUser");
						String userId = loginUser.getUserId();
						
						Map<String,Object> paramMap = new HashMap<String,Object>();
						paramMap.put("userId", userId);
						paramMap.put("batchID", batchID);
						paramMap.put("postList", postList);
						paramMap.put("fileName", fileName);
						
						potentialCustService.addPotentialCust(paramMap);
					}
				}
				
			}else{
				errorMsg = "输入的批次号已经存在！";
				uploadFlag = "error";
			}

			resultMap.put("uploadFlag", uploadFlag);  
			resultMap.put("errorMsg", errorMsg);  
			
	     } catch (Exception e) {   
				log.error("导入用户异常", e);
	            resultMap.put("uploadFlag", "error");  
	            resultMap.put("errorMsg", "请检查模板是否正确");  
	     }finally{
	    	try {
	    		if(input != null){
	    			input.close();
	    		}
			} catch (IOException e) {
				log.error("导入用户异常", e);
			}
	     }
		 
		return resultMap;
	}
	
	/**
	 * <p>功能描述：导入用户（投顾）</p>
	 * <p>创建日期：2014年11月27日</p>
	 * @return Map<String,Object>
	 * <AUTHOR>
	 * @update [更改日期 yyyy-MM-dd] [更改人姓名]
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping(value="/inputPCustByAdviser.do",method=RequestMethod.POST)
	public  @ResponseBody Map<String, Object> inputPCustByAdviser(HttpServletRequest request){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		InputStream input = null;
		Workbook workBook = null;
		
		String errorMsg = "";
		String uploadFlag = "success";
		
		try {  
			// 转型为MultipartHttpRequest：  
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;  
			// 获得文件：  
			MultipartFile file = multipartRequest.getFile("file");  
			// 获得文件名：  
			String fileName = file.getOriginalFilename();    
			
			String batchID = multipartRequest.getParameter("batchID");
			
			// 获得输入流：  
			input = file.getInputStream();  
			
			Map<String,Object> param = new HashMap<String,Object>();
			param.put("batchID", batchID);
			
			boolean existFlag = potentialCustService.existBatchID(param);
			
			if(!existFlag){
				workBook = Workbook.getWorkbook(input);
				
				// 去掉之前导入的一二级旧来源，统一成现在的新来源编码
				String[] colPropertity = {"custName", "addr", "postCode",
						"mobileAreaCode", "mobile", "telNo", "email", "PGrade","memo"};
				
				Sheet sheet = workBook.getSheet(0);
				
				// 导入的模板数据总共为10列
				List<AddPCustByAdviserInfo> postList = ExcelUtils.getListByReadShell(sheet, 1, 0, 8, colPropertity, AddPCustByAdviserInfo.class);
				log.info("获取导入文件数据对象：{}", JSON.toJSONString(postList));
				if (CollectionUtils.isEmpty(postList)) {
					errorMsg = "没有上传记录";
					uploadFlag = "error";
				} else {
					int line = 2;
					for (AddPCustByAdviserInfo importParam : postList) {
						String validateMsg = this.checkImportInfo(importParam);
						if (StringUtils.isNotEmpty(validateMsg)) {
							errorMsg = "第 " + line + " 行错误是：" + validateMsg;
							uploadFlag = "error";
							
							break;
						}
						line++;
					}
					
					if("success".equals(uploadFlag)){  //符合条件
						HttpSession loginSession = request.getSession(); 
						User loginUser = (User)loginSession.getAttribute("loginUser");
						String userId = loginUser.getUserId();
						
						Map<String,Object> paramMap = new HashMap<>();
						paramMap.put("userId", userId);
						paramMap.put("batchID", batchID);
						paramMap.put("postList", postList);
						paramMap.put("fileName", fileName);
						
						potentialCustService.addCustToTable(paramMap);
					}
				}
				
			}else{
				errorMsg = "输入的批次号已经存在！";
				uploadFlag = "error";
			}

			resultMap.put("uploadFlag", uploadFlag);  
			resultMap.put("errorMsg", errorMsg);  
			
	     } catch (Exception e) {   
			    log.error("导入用户异常", e);
	            resultMap.put("uploadFlag", "error");  
	            resultMap.put("errorMsg", "请检查模板是否正确");  
	     }finally{
	    	try {
	    		if(input != null){
	    			input.close();
	    		}
			} catch (IOException e) {
				log.error("导入用户异常", e);
			}
	     }
		 
		return resultMap;
	}
	
    
	
	/**
	 * <p>功能描述：信息检查</p>
	 * <p>创建日期：2014年11月27日</p>
	 * @return String
	 * <AUTHOR>
	 * @update [更改日期 yyyy-MM-dd] [更改人姓名]
	 */
	@SuppressWarnings("rawtypes")
	protected String checkAdd(AddPCustInfo addRequest) {
		Map comonMap = ValidateUtil.commonMap;
		String validateCode = null;
		try{

			validateCode = VarChar2Validator.validate(addRequest.getNewSourceNo(), 1,
					180, true, "客户来源", comonMap);
			if (StringUtils.isNotEmpty(validateCode)) {
				return validateCode;
			}
			validateCode = VarChar2Validator.validate(addRequest.getCustName(), 1,
					180, true, "客户名称", comonMap);
			if (StringUtils.isNotEmpty(validateCode)) {
				return validateCode;
			}
			validateCode = VarChar2Validator.validate(addRequest.getAddr(), 1,
					180, false, "地址", comonMap);
			if (StringUtils.isNotEmpty(validateCode)) {
				return validateCode;
			}
			validateCode = VarChar2Validator.validate(addRequest.getPostCode(),
					false, DataPattern.ZIPCODE_PATTERN, "邮编", comonMap);
			if (StringUtils.isNotEmpty(validateCode)) {
				return validateCode;
			}
			//手机号格式校验逻辑优化
			//a）原逻辑会校验【手机】字段是否为11位，不是11位会报错拦截；此逻辑需要做修订，因为国外的手机号可能不满足11位的条件
			//b）先判断【手机区号】是否为“+86”，若是，则校验手机号是都满足11位，不满足报错拦截；若否，无需校验手机号位数
			if (CN_MOBILE_AREA_CODE.equals(addRequest.getMobileAreaCode())) {
				validateCode = VarChar2Validator.validate(addRequest.getMobile(), 1,
						30, false, DataPattern.MOBILE_PATTERN, "手机", comonMap);
				if (StringUtils.isNotEmpty(validateCode)) {
					return validateCode;
				}
			}
			validateCode = VarChar2Validator.validate(addRequest.getTelNo(), 1, 30,
					false, "固定电话", comonMap);
			if (StringUtils.isNotEmpty(validateCode)) {
				return validateCode;
			}
			validateCode = IDContentValidator.validate(addRequest.getEmail(), 1, 1,
					40, false, "email", comonMap);
			if (StringUtils.isNotEmpty(validateCode)) {
				return validateCode;
			}
			validateCode = VarChar2Validator.validate(addRequest.getConsCode(), 1,
					100, false, "投顾代码", comonMap);
			if (StringUtils.isNotEmpty(validateCode)) {
				return validateCode;
			}
			if (StringUtils.isNotEmpty(addRequest.getPGradeStr())
					&& null == ArtificialGrade.getEnumDesc(addRequest
							.getPGradeStr())) {
				validateCode = "客户评分错误";
			}
		}catch(Exception e){
			validateCode = "信息检查不通过";
		}
		return validateCode;
	}
	
	
	/**
	 * <p>功能描述：投顾导入客户作息检查</p>
	 * <p>创建日期：2014年11月27日</p>
	 * @return String
	 * <AUTHOR>
	 * @update [更改日期 yyyy-MM-dd] [更改人姓名]
	 */
	@SuppressWarnings("rawtypes")
	protected String checkImportInfo(AddPCustByAdviserInfo addRequest) {
		Map comonMap = ValidateUtil.commonMap;
		String validateCode = null;
		try{
			
			// 验证客户名称是否合法
			validateCode = VarChar2Validator.validate(addRequest.getCustName(), 1, 180, true, "客户名称", comonMap);
			if (StringUtils.isNotEmpty(validateCode)) {
				return validateCode;
			}

			// 验证客户手机号码是否合法
			if (CN_MOBILE_AREA_CODE.equals(addRequest.getMobileAreaCode())) {
				validateCode = VarChar2Validator.validate(addRequest.getMobile(), 1, 30, false, DataPattern.MOBILE_PATTERN, "手机", comonMap);
				if (StringUtils.isNotEmpty(validateCode)) {
					return validateCode;
				}
			}
			
			// 验证客户联系电话是否合法
			validateCode = VarChar2Validator.validate(addRequest.getTelNo(), 1, 30,	false, "固定电话", comonMap);
			if (StringUtils.isNotEmpty(validateCode)) {
				return validateCode;
			}
			
			// 验证客户邮箱是否合法
			validateCode = IDContentValidator.validate(addRequest.getEmail(), 1, 1,	40, false, "email", comonMap);
			if (StringUtils.isNotEmpty(validateCode)) {
				return validateCode;
			}
			
			// 验证客户评分是否合法
			if (StringUtils.isNotEmpty(addRequest.getPGrade().toString()) && null == ArtificialGrade.getEnumDesc(addRequest.getPGrade().toString())) {
				validateCode = "客户评分错误";
			}
		}catch(Exception e){
			log.error("校验异常，",e);
			validateCode = "信息检查不通过";
		}
		return validateCode;
	}
	
	
	@RequestMapping("/downLoadModelInputCust.do")
	public String downLoadModelInputCust(HttpServletRequest request,
			HttpServletResponse response) {
		return dowmloadTemplate(MODEL_FILE_NAME,DOWNLOAD_FILE_NAME,request,response);
	}
	
	@RequestMapping("/downLoadUserModelInputCust.do")
	public String downLoadUserModelInputCust(HttpServletRequest request,
			HttpServletResponse response) {
		return dowmloadTemplate(USER_MODEL_FILE_NAME,DOWNLOAD_USER_FILE_NAME,request,response);
	}
	
	@RequestMapping("/downloadConsModelFile.do")
	public String downloadConsModelFile(HttpServletRequest request,
			HttpServletResponse response) {
		return dowmloadTemplate(DOWNLOAD_CONS_MODEL_FILE,DOWNLOAD_CONS_MODEL_NAME,request,response);
	}
	
	@RequestMapping("/downloadConsGuideFile.do")
	public String downloadConsGuideFile(HttpServletRequest request,
			HttpServletResponse response) {
		return dowmloadTemplate(DOWNLOAD_CONS_GUIDE_FILE,DOWNLOAD_CONS_GUIDE_NAME,request,response);
	}
	

	
}
