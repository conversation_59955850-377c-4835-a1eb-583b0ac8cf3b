package com.howbuy.crm.hb.web.controller.custinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.decrypt.DecryptBatchFacade;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.CreateConscustInfoRequest;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.request.UpdateConscustInfoRequest;
import com.howbuy.crm.conscust.response.CreateConscustInfoResponse;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.CreateConscustInfoService;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.conscust.service.UpdateConscustInfoService;
import com.howbuy.crm.hb.domain.custinfo.CmCustconstant;
import com.howbuy.crm.hb.domain.custinfo.CmOrgCust;
import com.howbuy.crm.hb.domain.custinfo.CmOrgLinkman;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.CmCustconstantService;
import com.howbuy.crm.hb.service.custinfo.CmOrgLinkmanService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.custinfo.OrgCustService;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.nt.basedetail.dto.CustBaseDetailDomain;
import com.howbuy.crm.nt.basedetail.dto.CustKycInfo;
import com.howbuy.crm.nt.basedetail.request.QueryCustBaseDetailRequest;
import com.howbuy.crm.nt.basedetail.response.QueryCustBaseDetailResponse;
import com.howbuy.crm.nt.basedetail.service.QueryCustBaseDetailService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.constants.CRM3ErrorCode;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;

/**
 * <AUTHOR>
 * @Description: 机构客户
 * @reason:
 * @Date: 2020/6/1 10:24
 */
@Slf4j
@Controller
@RequestMapping("/orgCust")
public class CmOrgCustController {

    @Autowired
    private OrgCustService orgCustService;
    
    @Autowired
    private ConscustService conscustService;
    
    @Autowired
    private QueryCustBaseDetailService queryCustBaseDetailService;
    
    @Autowired
    private QueryConscustInfoService queryConscustInfoService;
    
    @Autowired
    private UpdateConscustInfoService updateConscustInfoService;
    
    @Autowired
    private CmCustconstantService cmCustconstantService;
    @Autowired
    private CreateConscustInfoService createConscustInfoService;

    @Autowired
    private CmOrgLinkmanService cmOrgLinkmanService;

    @Autowired
    private CommonService commonService;
    @Autowired
    private PageVisitLogService pageVisitLogService;
    
    @Autowired
	private EncryptSingleFacade encryptSingleFacade;
    @Autowired
    private DecryptBatchFacade decryptBatchFacade;
    @Autowired
    private DecryptSingleFacade decryptSingleFacade;

    @RequestMapping("/listOrgCust.html")
    public String listOrgCust(HttpServletRequest request, Map map){
        map.put("userId", request.getSession().getAttribute("userId"));
        return "/custinfo/listOrgCust";
    }

    @RequestMapping("/listOrgCustJson.do")
    @ResponseBody
    public Object listOrgCustJson(HttpServletRequest request) throws Exception{
        Map<String, Object> result = new HashMap<>(2);
        Map<String, String> param = new ParamUtil(request).getParamMap();
        if(StringUtil.isNotNullStr(param.get("idNo"))) {
            param.put("idNo", DigestUtil.digest(param.get("idNo")));
        }
        PageData<CmOrgCust> pageData = orgCustService.listOrgCustByPage(param);
        ConstantCache constantCache = ConstantCache.getInstance();
        for(CmOrgCust cmOrgCust : pageData.getListData()){
            if(StaticVar.INVST_TYPE_ORG.equals(cmOrgCust.getInvestType())){
                cmOrgCust.setOrgType(constantCache.getVal("orgType",cmOrgCust.getOrgType()));
            }
            if(StaticVar.INVST_TYPE_PRODUCT.equals(cmOrgCust.getInvestType())){
                cmOrgCust.setOrgType(constantCache.getVal("orgProdType",cmOrgCust.getOrgType()));
            }
            if(StringUtils.isNotBlank(cmOrgCust.getConscode())){
                cmOrgCust.setAddress(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_ADDRESS, cmOrgCust.getAddress(), cmOrgCust.getConscode(), null));
            }
        }
        result.put("rows", pageData.getListData());
        result.put("total", pageData.getPageBean().getTotalNum());
        return result;
    }

    @RequestMapping("/getAllLinkman")
    @ResponseBody
    public String getAllLinkman(String custNo){
        return orgCustService.getAllLinkman(custNo);
    }

    /**
     * 机构用户详情索引页
     * @param request
     * @return
     */
    @RequestMapping("/orgCustIndex")
    public String forwardOrgCustIndex(HttpServletRequest request) {
        String conscustno = request.getParameter("conscustno");
        String pubcustno = request.getParameter("pubcustno");
        String menucode = request.getParameter("menucode");
        if(StringUtil.isNullStr(pubcustno)){
            Map<String, String> param = new HashMap<>(1);
            param.put("conscustno",conscustno);
            Map hboneMap = conscustService.getHboneInfo(param);
            if(hboneMap != null){
                pubcustno = ObjectUtils.ObjectToString(hboneMap.get("CUSTNO"));
            }
        }
        // 设置传入参数
        request.setAttribute("conscustno", conscustno);
        request.setAttribute("pubcustno", pubcustno);
        request.setAttribute("menucode", menucode);
        String ip = getIpAddr(request);
        //记录访问日志
        PageVisitLog pageVisitLog = new PageVisitLog();
        String userId= (String)request.getSession().getAttribute("userId");
        pageVisitLog.setConscustno(conscustno);
        pageVisitLog.setUserId(userId);
        pageVisitLog.setVisitUrl(request.getRequestURI());
        pageVisitLog.setOperation("访问页面");
        pageVisitLog.setVisitTime(new Date());
        pageVisitLog.setIp(ip);
        pageVisitLogService.recordLog(pageVisitLog);
        return "/custinfo/orgCustIndex";

    }
    
    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 导出操作
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportCust.do")
    public void exportCust(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String userId = (String) request.getSession().getAttribute("userId");
        // 设置查询参数
        String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
        if (StringUtils.isNotBlank(ids)) {
            String[] custs = ids.split(",");
            //传过来的客户号
            List<String> list = new ArrayList<String>();
            //将数组转list
            CollectionUtils.addAll(list, custs);
            // 检查打标的客户是否有在申请划转的客户中
            Map<String,String> paramsql = new HashMap<String,String>(1);
            String sqlins = Util.getOracleSQLIn(list,999,"t1.conscustno");
            paramsql.put("sqlins", sqlins);
            List<CmOrgCust> exportList = orgCustService.selectExportCust(paramsql);

            ConstantCache constantCache = ConstantCache.getInstance();
            for(CmOrgCust cmOrgCust : exportList){
                if(StaticVar.INVST_TYPE_ORG.equals(cmOrgCust.getInvestType())){
                    cmOrgCust.setOrgType(constantCache.getVal("orgType",cmOrgCust.getOrgType()));
                }
                if(StaticVar.INVST_TYPE_PRODUCT.equals(cmOrgCust.getInvestType())){
                    cmOrgCust.setOrgType(constantCache.getVal("orgProdType",cmOrgCust.getOrgType()));
                }
                if(StringUtils.isNotBlank(cmOrgCust.getConscode())){
                    cmOrgCust.setAddress(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_ADDRESS, cmOrgCust.getAddress(), cmOrgCust.getConscode(), null));
                }
            }

            try {
                // 清空输出流
                response.reset();
                // 设置文件格式和名字
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition",
                        "attachment;fileName=" + new String("机构客户.xls".getBytes("gb2312"), "ISO8859-1"));
                ServletOutputStream os = response.getOutputStream();

                String [] columnName = new String[]{"客户名称", "机构类型", "联系人姓名","联系人地址","最近拜访日期","拜访内容","所属投顾", "所属部门"};

                String [] beanProperty = new String[]{"custName", "orgType", "linkmanName", "address", "lastVisitDt", "commContent", "consName","orgName"};
                ExcelWriter.writeExcel(os, "机构客户", 0, exportList, columnName, beanProperty);
                os.close(); // 关闭流
            } catch (Exception e) {
                log.error("文件导出异常", e);
            }
        }
    }
    
   /**
    * 机构用户详情索引页
    * @param request
    * @return
    */
    @RequestMapping("/orgCustDetail")
    public String orgCustDetail(HttpServletRequest request) {
    	String menucode = request.getParameter("menucode");
        String custno = request.getParameter("conscustno");
        
        request.setAttribute("menucode", menucode);
        Conscust cust = conscustService.getConscust(StringUtils.isNoneBlank(custno)?custno:"xxxx");
        //查询一账通接口判断手机与证件验证状态
        String hboneno = cust.getHboneno();
        if (StringUtils.isNotBlank(hboneno)) {
        	try {
        		QueryCustBaseDetailRequest custBaseDetailRequest = new QueryCustBaseDetailRequest();
            	custBaseDetailRequest.setHboneno(hboneno);
            	QueryCustBaseDetailResponse custBaseDetailResponse = queryCustBaseDetailService.queryConscustInfo(custBaseDetailRequest);
     
            	if(CRM3ErrorCode.success.getValue().equals(custBaseDetailResponse.getReturnCode())) {
            		CustBaseDetailDomain custbasedetail = custBaseDetailResponse.getCustbasedetaildomain();
            		if(custbasedetail != null) {
            			request.setAttribute("custbasedetail", custbasedetail);
                        List<CustKycInfo> displayKycList= Lists.newArrayList();
                        //kyc信息
                        Arrays.stream(DisChannelCodeEnum.values()).forEach(channelCodeEnum->{
                            if(custbasedetail.getKycInfoMap()!=null && custbasedetail.getKycInfoMap().containsKey(channelCodeEnum.getCode())){
                                displayKycList.add(custbasedetail.getKycInfoMap().get(channelCodeEnum.getCode()));
                            }else{
                                CustKycInfo kycInfo=new CustKycInfo();
                                kycInfo.setDisChannelCode(channelCodeEnum.getCode());
                                displayKycList.add(kycInfo);
                            }
                        });
                        request.setAttribute("displayKycList", displayKycList);
            		}
            	}
        	}catch(Exception e) {
        		e.getMessage();
        	}
        }
        String newSourceB= "B";
        String newSourceM= "M";
        if (StringUtils.isNotBlank(cust.getNewsource()) && (newSourceB.equals(cust.getNewsource()) || newSourceM.equals(cust.getNewsource())) && StringUtils.isNotBlank(cust.getCustSourceRemark())) {
            String sourceRemarkHis = "99";
        	if (sourceRemarkHis.equals(StringUtil.replaceNull(cust.getCustSourceRemark()))) {
                cust.setCustSourceRemark("历史客户");
            } else {
                Conscust conscust = conscustService.getConscust(StringUtil.replaceNull(cust.getCustSourceRemark()));
                if (conscust != null) {
                    cust.setCustSourceRemark(conscust.getCustname());
                }
            }
        }
        cust.setProvcode(ConstantCache.getInstance().getProvCityMap().get(cust.getProvcode()));
        cust.setCitycode(ConstantCache.getInstance().getProvCityMap().get(cust.getCitycode()));
        //机构客户的机构类型去机构的枚举值
        if(StaticVar.INVST_TYPE_ORG.equals(cust.getInvsttype())){
        	cust.setOrgtype(ConstantCache.getInstance().getConstantKeyVal("orgType").get(cust.getOrgtype()));
        //产品客户的机构类型取产品的枚举值
        }else if(StaticVar.INVST_TYPE_PRODUCT.equals(cust.getInvsttype())){
        	cust.setOrgtype(ConstantCache.getInstance().getConstantKeyVal("orgProdType").get(cust.getOrgtype()));
        }
        cust.setInvsttype(ConstantCache.getInstance().getConstantKeyVal("InvstTypes").get(cust.getInvsttype()));
        //判断是否入会
        if (StringUtils.isNotBlank(cust.getIdtype()) && StringUtils.isNotBlank(cust.getIdno())) {
            if (StaticVar.IS_JOIN_CLUBS_HASIN.equals(cust.getIsjoinclub())) {
                request.setAttribute("hasjoinclub", "true");
            } else {
                request.setAttribute("hasjoinclub", "false");
            }
        }
        User user = (User) request.getSession().getAttribute("loginUser");
        request.setAttribute("userid", user.getUserId());
        if (StringUtils.isNotBlank(cust.getConscode())) {
            request.setAttribute("conscode", cust.getConscode());
            request.setAttribute("seniormgrcode", cust.getSeniormgrcode());
        }
        if(StringUtil.isNotNullStr(cust.getAddrCipher())) {
            cust.setAddr(decryptSingleFacade.decrypt(cust.getAddrCipher()).getCodecText());
        }
        if(StringUtil.isNotNullStr(cust.getIdnoCipher())) {
            cust.setIdno(decryptSingleFacade.decrypt(cust.getIdnoCipher()).getCodecText());
        }
        request.setAttribute("cust", cust);
        return "/custinfo/orgCustBaseDetail";

    }

    @RequestMapping("addCust.html")
    public String addCust(HttpServletRequest request){
        boolean custSourceType = conscustService.getcustSourceType((String) request.getSession().getAttribute("userId"));
        request.setAttribute("custSourceType", custSourceType);
        return "/custinfo/addOrgCust";
    }
    
    /**
     * 修改机构客户信息
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/orgCustEdit")
    public String orgCustEdit(HttpServletRequest request) throws Exception {
        String custno = request.getParameter("conscustno");
        String menucode = request.getParameter("menucode");
        Conscust cust = conscustService.getConscust(StringUtils.isNotBlank(custno)?custno:"xxxx");
        if(StringUtil.isNotNullStr(cust.getIdno())){
            cust.setIdno(decryptSingleFacade.decrypt(cust.getIdnoCipher()).getCodecText());
        }
        if(StringUtil.isNotNullStr(cust.getAddrCipher())){
            cust.setAddr(decryptSingleFacade.decrypt(cust.getAddrCipher()).getCodecText());
        }
        request.setAttribute("cust", cust);
        request.setAttribute("menucode", menucode);
        return "/custinfo/orgCustEdit";

    }
    
    @ResponseBody
    @RequestMapping("/editOrgConscust")
    public String editOrgConscust(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute("loginUser");
        String result = null;
        String conscustno = request.getParameter("conscustno");
        String custName = request.getParameter("custName");
        String orgType = request.getParameter("orgType");
        String idType = request.getParameter("IDType");
        String idNo = request.getParameter("IDNo");
        String provCode = request.getParameter("provCode");
        String cityCode = request.getParameter("cityCode");
        String addr = request.getParameter("addr");
        
        if (StringUtils.isBlank(conscustno)) {
            return "paramError";
        }
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(conscustno);
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        ConscustInfoDomain cust = queryResponse.getConscustinfo();
        if (cust != null) {
        	String xinhao = "*";
            if (StringUtils.isNotBlank(custName)) {
                if (custName.indexOf(xinhao) == -1) {
                    cust.setCustname(custName);
                }
            }else {
                cust.setCustname("");
            }
            cust.setOrgtype(orgType);
            cust.setIdtype(idType);
            if (StringUtils.isNotBlank(idNo)) {
                if (idNo.indexOf(xinhao) == -1) {
                    cust.setIdno(idNo);
                    cust.setIdnoMask(MaskUtil.maskIdNo(idNo));
                    cust.setIdnoDigest(DigestUtil.digest(idNo));
                    cust.setIdnoCipher(encryptSingleFacade.encrypt(idNo).getCodecText());
                }
            }else {
                cust.setIdno("");
            }
            cust.setProvcode(provCode);
            cust.setCitycode(cityCode);
            if (StringUtils.isNotBlank(addr)) {
                if (addr.indexOf(xinhao) == -1) {
                    cust.setAddr(addr);
                    cust.setAddrMask(MaskUtil.maskIdNo(addr));
                    cust.setAddrDigest(DigestUtil.digest(addr));
                    cust.setAddrCipher(encryptSingleFacade.encrypt(addr).getCodecText());
                }
            } else {
                cust.setAddr("");
            }
            //整合重复客户提示
    		if((result = repeatCustCheck(idNo, idType, custName, conscustno)) != null){
    		   return result;
            }
            result = "success";
            try {
                // 调用接口，修改客户信息
                UpdateConscustInfoRequest updateRequest = new UpdateConscustInfoRequest();
                cust.setCreator(user.getUserId());
                updateRequest.setConscustinfo(cust);
                updateConscustInfoService.updateConsCustInfo(updateRequest);
            } catch (RuntimeException e) {
                result = "false";
            }

        }
        return result;
    }

    private String repeatCustCheck(String idNo, String idType, String custName, String conscustno){
        String result = null;
        List<String> conscustlist = new ArrayList<>();
        if (StringUtils.isNotBlank(idNo) && StringUtils.isNotBlank(idType)) {
            Map<String, String> paramid = new HashMap<>(5);
            paramid.put("idno", idNo);
            paramid.put("idtype", idType);
            paramid.put("conscuststatus", "0");
            paramid.put("custname", custName);
            paramid.put("isJgAndCp", "1");
            List<Conscust> consCustlist = conscustService.listConscustByMap(paramid);
            if (consCustlist != null && consCustlist.size() > 0) {
                for(Conscust conscust:consCustlist){
                    if(!conscustno.equals(conscust.getConscustno())){
                        conscustlist.add(conscust.getConscustno());
                    }
                }
            }
            if(conscustlist.size() > 0){
                //result = "repeatid存在证件号重复客户,";
                StringBuilder sb = new StringBuilder();
                sb.append("repeatid存在证件号重复客户,");
                for(String custno:conscustlist){
                    Map<String,String> paramcons = new HashMap<>(1);
                    paramcons.put("custno", custno);
                    CmCustconstant constant = cmCustconstantService.getCmCustconstant(paramcons);
                    if(constant != null){
                        String orgCode = ConsOrgCache.getInstance().getCons2OutletMap().get(constant.getConscode());
                        //result+="投顾客户号:"+custno+","+"部门:"+ConsOrgCache.getInstance().getAllOrgMap().get(orgCode)+"; ";
                        sb.append("投顾客户号:"+custno+","+"部门:"+ConsOrgCache.getInstance().getAllOrgMap().get(orgCode)+"; ");
                    }
                }
                result = sb.toString();
            }
        }
        return result;
    }

    /**
     * 新增机构客户。暂时没有请求。
     * @param request
     * @return
     */
    @Deprecated
    @RequestMapping("/saveCust.do")
    @ResponseBody
    public Object saveOrgCust(HttpServletRequest request){
        try {
            String userId = (String) request.getSession().getAttribute("userId");
            String rowJson = request.getParameter("rowsStr");
            String custName = request.getParameter("custName");
            String orgType = request.getParameter("orgType");
            String idType = request.getParameter("idType");
            String idNo = request.getParameter("idNo");
            String provCode = request.getParameter("provCode");
            String cityCode = request.getParameter("cityCode");
            String address = request.getParameter("address");
            String custSource = request.getParameter("custSource");
            String custSourceRemark = request.getParameter("custSourceRemark");
            JSONArray jsonArray = JSON.parseArray(rowJson);
            List<CmOrgLinkman> linkmanList = jsonArray.toJavaList(CmOrgLinkman.class);
            String conscustno = commonService.getConsCustNo();
            //校验联系人是否重复
            Map orgLinkMap = new HashMap(linkmanList.size());
            for(CmOrgLinkman cmOrgLinkman : linkmanList){
                if(orgLinkMap.containsKey(cmOrgLinkman.getMobile())){
                    return "repeatLink";
                }
                orgLinkMap.put(cmOrgLinkman.getMobile(),"");
                cmOrgLinkman.setCustNo(conscustno);
                cmOrgLinkman.setId(Long.parseLong(commonService.getSeqValue("seq_org_linkman")));
            }
            String result;
            if((result = repeatCustCheck(idNo, idType, custName,conscustno)) != null){
                return result;
            }
            CreateConscustInfoRequest createConscustInfoRequest = new CreateConscustInfoRequest();
            ConscustInfoDomain conscustInfoDomain = new ConscustInfoDomain();
            conscustInfoDomain.setCustname(custName);
            conscustInfoDomain.setOrgtype(orgType);
            conscustInfoDomain.setIdtype(idType);
            conscustInfoDomain.setInvsttype(StaticVar.INVST_TYPE_ORG);
            if(StringUtil.isNotNullStr(idNo)){
                conscustInfoDomain.setIdno(idNo);
            	conscustInfoDomain.setIdnoDigest(DigestUtil.digest(idNo));
            	conscustInfoDomain.setIdnoMask(MaskUtil.maskIdNo(idNo));
            	conscustInfoDomain.setIdnoCipher(encryptSingleFacade.encrypt(idNo).getCodecText());
            }
            conscustInfoDomain.setProvcode(provCode);
            conscustInfoDomain.setCitycode(cityCode);
            if(StringUtil.isNotNullStr(address)){
                conscustInfoDomain.setAddr(address);
            	conscustInfoDomain.setAddrDigest(DigestUtil.digest(address));
            	conscustInfoDomain.setAddrMask(MaskUtil.maskAddr(address));
            	conscustInfoDomain.setAddrCipher(encryptSingleFacade.encrypt(address).getCodecText());
            }
            conscustInfoDomain.setNewsourceno(custSource);
            conscustInfoDomain.setCustsourceremark(custSourceRemark);
            conscustInfoDomain.setCreator(userId);
            conscustInfoDomain.setConscode(userId);
            conscustInfoDomain.setConscustno(conscustno);
            conscustInfoDomain.setConscuststatus("0");
            createConscustInfoRequest.setConscustinfo(conscustInfoDomain);
            CreateConscustInfoResponse createConscustInfoResponse = createConscustInfoService.insertConsCustInfo(createConscustInfoRequest);
            if (createConscustInfoResponse.isSuccessful()) {
                cmOrgLinkmanService.batchInsert(linkmanList);
            }else {
                return "errorCons";
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return "fail";
        }
        return "success";
    }

    @RequestMapping("/linkmanList.html")
    public String linkmanList(HttpServletRequest request, String conscustno, Map map){
        List<CmOrgLinkman> cmOrgLinkmanList = cmOrgLinkmanService.listOrgLinkmanByCustNo(getLinkmanParams(request, conscustno));
        if(CollectionUtils.isNotEmpty(cmOrgLinkmanList)){
            ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
            for(CmOrgLinkman cmOrgLinkman : cmOrgLinkmanList){
                cmOrgLinkman.setMobile(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_MOBILE, cmOrgLinkman.getMobile(), cmOrgLinkman.getCounterpartMan(), null));
                cmOrgLinkman.setTel(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_PHONE, cmOrgLinkman.getTel(), cmOrgLinkman.getCounterpartMan(), null));
                cmOrgLinkman.setEmail(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_EMAIL, cmOrgLinkman.getEmail(), cmOrgLinkman.getCounterpartMan(), null));
                cmOrgLinkman.setAddress(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_ADDRESS, cmOrgLinkman.getAddress(), cmOrgLinkman.getCounterpartMan(), null));

                cmOrgLinkman.setCounterpartMan(consOrgCache.getAllUserMap().get(cmOrgLinkman.getCounterpartMan()));
            }
        }
        map.put("linkmanList", cmOrgLinkmanList);
        map.put("custNo",conscustno);
        return "/custinfo/linkmanList";
    }

    /**
     * 投顾客户查询
     * @param request
     * @param conscustno
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/querylinkmanList")
    public Map<String, Object> querylinkmanList(HttpServletRequest request,String conscustno) throws Exception{
        Map<String, Object> map = new HashMap<String, Object>(1);
        List<CmOrgLinkman> cmOrgLinkmanList = cmOrgLinkmanService.listOrgLinkmanByCustNo(getLinkmanParams(request, conscustno));
        if(CollectionUtils.isNotEmpty(cmOrgLinkmanList)){
            ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
            for(CmOrgLinkman cmOrgLinkman : cmOrgLinkmanList){
                cmOrgLinkman.setMobile(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_MOBILE, cmOrgLinkman.getMobile(), cmOrgLinkman.getCounterpartMan(), null));
                cmOrgLinkman.setTel(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_PHONE, cmOrgLinkman.getTel(), cmOrgLinkman.getCounterpartMan(), null));
                cmOrgLinkman.setEmail(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_EMAIL, cmOrgLinkman.getEmail(), cmOrgLinkman.getCounterpartMan(), null));
                cmOrgLinkman.setAddress(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_ADDRESS, cmOrgLinkman.getAddress(), cmOrgLinkman.getCounterpartMan(), null));

                cmOrgLinkman.setCounterpartMan(consOrgCache.getAllUserMap().get(cmOrgLinkman.getCounterpartMan()));
            }
        }
        map.put("linkmanList", cmOrgLinkmanList);
        return map;
    }

    private Map<String, String> getLinkmanParams(HttpServletRequest request, String conscustno){
        Map<String, String> params = new HashMap<>(4);
        String topgd = (String) request.getSession().getAttribute("topgddata");
        String isdel = request.getParameter("isdel");
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        String conscode = userlogin.getUserId();
        String outletcode = ConsOrgCache.getInstance().getUser2OutletMap().get(userlogin.getUserId());
        String tearmcode = ConsOrgCache.getInstance().getUser2TeamMap().get(userlogin.getUserId());
        String orgCode = "";
        if (StaticVar.DATARANGE_GD_ALL.equals(topgd) || StaticVar.DATARANGE_GD_ALL_NOWFP.equals(topgd)) {
            orgCode = "0";
        } else if (StaticVar.DATARANGE_GD_OUTLET.equals(topgd)) {
            orgCode = outletcode;
        } else if (StaticVar.DATARANGE_GD_TEARM.equals(topgd)) {
            orgCode = tearmcode;
        }
        params.put("isdel", isdel);
        params.put("orgCode", orgCode);
        params.put("conscode", conscode);
        params.put("custNo", conscustno);
        return params;
    }

    @RequestMapping("/saveLinkman.do")
    @ResponseBody
    public Object saveLinkman(HttpServletRequest request){
        User user = (User) request.getSession().getAttribute("loginUser");
        Map result = new HashMap(3);
        try {
            String rowJson = request.getParameter("rowStr");
            CmOrgLinkman cmOrgLinkman = JSON.parseObject(rowJson, CmOrgLinkman.class);
            cmOrgLinkman.setCustNo(request.getParameter("custNo"));
            List<CmOrgLinkman> cmOrgLinkmanList = cmOrgLinkmanService.listOrgLinkmanByCustNo(getLinkmanParams(request, cmOrgLinkman.getCustNo()));
            Map linkmanMap = new HashMap(cmOrgLinkmanList.size());
            for(CmOrgLinkman cmOrgLinkman1 : cmOrgLinkmanList){
                linkmanMap.put(cmOrgLinkman1.getMobile(), cmOrgLinkman1.getId());
            }
            if(cmOrgLinkman.getId() == null) {
                if(linkmanMap.containsKey(cmOrgLinkman.getMobile())){
                    result.put("code","repeatLink");
                    return result;
                }else {
                    cmOrgLinkmanService.insert(cmOrgLinkman,user.getUserId());
                    result.put("id", cmOrgLinkman.getId());
                }
            }else {
                if(linkmanMap.containsKey(cmOrgLinkman.getMobile()) && !cmOrgLinkman.getId().equals(linkmanMap.get(cmOrgLinkman.getMobile()))){
                    result.put("code","repeatLink");
                    return result;
                }else {
                	String fourXin = "****";
                    String address = cmOrgLinkman.getAddress();
                    if(address.contains(fourXin)){
                        cmOrgLinkman.setAddress(null);
                    }
                    String mobile = cmOrgLinkman.getMobile();
                    if(mobile.contains(fourXin)){
                        cmOrgLinkman.setMobile(null);
                    }
                    String tel = cmOrgLinkman.getTel();
                    if(tel.contains(fourXin)){
                        cmOrgLinkman.setTel(null);
                    }
                    String email = cmOrgLinkman.getEmail();
                    if(email.contains(fourXin)){
                        cmOrgLinkman.setEmail(null);
                    }
                    cmOrgLinkmanService.update(cmOrgLinkman,user.getUserId());
                }
            }
            result.put("code","success");
            return result;
        }catch (Exception e){
            log.error(e.getMessage(), e);
            result.put("code","fail");
            return result;
        }
    }

    @RequestMapping("/delLinkman.do")
    @ResponseBody
    public Object delLinkman(HttpServletRequest request){
        try {
            String id = request.getParameter("id");
            cmOrgLinkmanService.delete(Long.parseLong(id));
            return "success";
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return "fail";
        }
    }

    @ResponseBody
    @RequestMapping("/ajaxlinkmanlog")
    public void ajaxlinkmanlog(HttpServletRequest request, HttpServletResponse response) {
        String linkmanid = request.getParameter("linkmanid");
        String tips = request.getParameter("tips");
        StringBuffer result = new StringBuffer();
        Map<String, String> param = new HashMap<>(5);
        param.put("linkmanid", linkmanid);
        String mobileStr = "mobile";
        String telStr = "tel";
        String emailStr = "email";
        String addressStr = "address";
        if(tips.contains(mobileStr)){
            param.put("upmobile", "1");
        }else if(tips.contains(telStr)){
            param.put("uptel", "1");
        }else if(tips.contains(emailStr)){
            param.put("upemail", "1");
        }else if(tips.contains(addressStr)){
            param.put("upaddress", "1");
        }
        List<Map<String, String>> list = cmOrgLinkmanService.ajaxlinkmanlog(param);
        if (list != null && list.size() > 0) {
            ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
            result.append("<table style='height:55px;width:335px;color:black'>");
            result.append("<tr><td style='width:125px'>修改时间</td><td style='width:60px'>修改人</td><td style='width:60px'>修改内容</td></tr>");
            for (Map<String, String> map : list) {
                result.append("<tr>");
                result.append("<td>" + ObjectUtils.ObjectToString(map.get("UPDATETIME")) + "</td>");
                result.append("<td>" + ObjectUtils.ObjectToString(consOrgCache.getAllUserMap().get(map.get("UPDATETER"))) + "</td>");
                if(tips.contains("mobile")){
                    result.append("<td>" + ObjectUtils.ObjectToString(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_MOBILE, map.get("MOBILE"), map.get("COUNTERPARTMAN"), null)) + "</td>");
                }else if(tips.contains("tel")){
                    result.append("<td>" + ObjectUtils.ObjectToString(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_PHONE, map.get("TEL"), map.get("COUNTERPARTMAN"), null)) + "</td>");
                }else if(tips.contains("email")){
                    result.append("<td>" + ObjectUtils.ObjectToString(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_EMAIL, map.get("EMAIL"), map.get("COUNTERPARTMAN"), null)) + "</td>");
                }else if(tips.contains("address")){
                    result.append("<td>" + ObjectUtils.ObjectToString(ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_ADDRESS, map.get("ADDRESS"), map.get("COUNTERPARTMAN"), null)) + "</td>");
                }
                result.append("</tr>");
            }
            result.append("</table>");
        }
        response.setContentType("text/html; charset=utf-8");
        PrintWriter pw = null;
        try {
            pw = response.getWriter();
            pw.print(result.toString());
            pw.flush();
        } catch (IOException e) {
            log.error("数据写入异常", e);
        } finally {
            if (pw != null) {
                pw.close();
            }
        }
    }
}
