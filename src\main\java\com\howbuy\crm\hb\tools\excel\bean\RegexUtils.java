/**   
 * @Title: RegexUtils.java 
 * @Package com.hb.crm.web.util.excel.bean 
 * @Description: TODO(用一句话描述该文件做什么) 
 * <AUTHOR>
 * @date 2016年4月28日 下午2:40:43 
 * @version V1.0   
 */

package com.howbuy.crm.hb.tools.excel.bean;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName: RegexUtils
 * @Description: 正则表达式公用类
 * <AUTHOR>
 * @date 2016年4月28日 下午3:30:12
 * 
 */
public class RegexUtils {
	/**
	 * @Fields PERCENTAGE_PATTERN : 百分率正则匹配 例如 20%
	 */
	public final static String PERCENTAGE_PATTERN = "((0|[1-9][0-9]{0,})|((0|[1-9][0-9]{0,})(.[0-9]{1,})))%$";
	
	/**
	 * @Fields PERCENTAGE_PATTERN : 百分率正则匹配 例如 0.3
	 */
	public final static String DOUBLE_PERCENTAGE_PATTERN = "(0)|((0)(.[0-9]{1,}))|(1)|(1.[0]{1,})";

	
	/**
	 * @Fields PHONE_PATTERN : 正整数匹配
	 */
	public final static String NUMBER_PATTERN = "(0|[1-9][0-9]{0,})";

	/**
	 * @Fields DATE_PATTERN : 日期正则表达式
	 */
	public final static String DATE_PATTERN = "([0-9]{4})(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])";
	/**
	 * @Fields BUS_BOOLEAN_PATTERN : 业务是否状态
	 */
	public final static String BUS_BOOLEAN_PATTERN = "(是|否)";

	/**
	 * @Fields BUS_FUNDS_TYPE_PATTERN : 基金类型
	 */
	public final static String BUS_FUNDS_TYPE_PATTERN = "(货币型|QDII|股票型|混合型|结构型|债券型|公募专户|储蓄罐)";
	
	/**
	 * @Fields BUS_FUNDS_TYPE_PATTERN : 保险产品类型
	 */
	public final static String BUS_INSURANCE_TYPE_PATTERN = "(封闭式|开放式|活期保险1|活期保险2|定期保险1|定期保险2)";
	
	/** 
	* @Fields BUS_INCOME_DATE_TYPE_PATTERN :收入日期判断
	*/ 
	public final static String BUS_INCOME_DATE_TYPE_PATTERN = "(前一日|当日)";

	/**
	 * @Fields PHONE_PATTERN :数字校验
	 */
	public final static String NUMBER_ALL_PATTERN = "\\d{1,}";
	
	/**
	 * @Fields FUNDS_PATTERN :基金代码
	 */
	public final static String FUNDS_PATTERN = "[a-zA-Z0-9]{1,6}";
	
	

	/**
	 * @Fields DOUBLE_PATTERN :double类型数据验证
	 */
	public final static String DOUBLE_PATTERN = "(0|[1-9][0-9]{0,})|((0|[1-9][0-9]{0,})(.[0-9]{1,}))";

	/**
	 * @Title: startCheck
	 * @Description: 正则校验方法
	 * @param reg
	 * @param string
	 * @return
	 */
	public static boolean startCheck(String reg, String string) {
		boolean tem = false;
		string = string.replaceAll(" ", "");
		Pattern pattern = Pattern.compile(reg);
		Matcher matcher = pattern.matcher(string);
		tem = matcher.matches();
		return tem;
	}

}
