package com.howbuy.crm.hb.constants;

/**
 * @Description crm-trade-server  资料管理订单接口 path
 * <AUTHOR>
 * @Date 15点22分$ 2022年8月9日$
 */
public class CrmTradeServerPathConstant {


    /**
     * 查询业务类型和文件关系
     */
    public static final String QUERY_COUNTER_BUSI_FILE_LIST = "/counterbusifile/querycounterbusifilelist";

    /**
     * 分页查询线上化业务
     */
    public static final String QUERY_COUNTER_BUSINESS_BY_PAGE = "/counterbusiness/querycounterbusinessbypage";

    /**
     * 查询单个线上化业务
     */
    public static final String GET_CM_COUNTER_BUSSINESS="/counterbusiness/getcmcounterbusiness";
    
    /**
     * 查询线上化业务列表
     */
    public static final String QUERY_COUNTER_BUSINESS_LIST="/counterbusiness/querycounterbusinesslist";

    /**
     * 更新线上化业务信息
     */
    public static final String UPDATE_CM_COUNTER_BUSINESS = "/counterbusiness/updatecmcounterbusiness";

    /**
     * 查询业务类型和文件关系和文件配置列表
     */
    public static final String QUERY_COUNTER_BUSI_FILE_AND_FILE_TYPE_LIST = "/counterbusifile/querycounterbusifileandfiletypelist";

    /**
     * 查询文件类型列表
     */
    public static final String QUERY_COUNTER_FILE_TYPE_LIST = "/counterfiletype/querycounterfiletypelist";

    /**
     * 保存配置文件信息
     */
    public static final String SAVE_COUNTER_CONFIG_FILE = "/counterbusifile/savecounterconfigfile";

    /**
     * 查询单个业务信息，包含业务类型和文件关系信息
     */
    public static final String GET_COUNTER_BUSINESS_MSG = "/counterbusiness/getcounterbusinessmsg";

    /**
     * 上下移动 业务类型和文件关系
     */
    public static final String MOVE_COUNTER_BUSI_FILE_STEP = "/counterbusifile/movecounterbusifilestep";

    /**
     * 更新业务类型和文件关系信息
     */
    public static final String UPDATE_CM_COUNTER_BUSI_FILE = "/counterbusifile/updatecmcounterbusifile";

    /**
     * 通过业务类型和文件关系查询模板列表
     */
    public static final String LIST_CM_COUNTER_FILE_MODEL_BY_BUSI_FILE_ID = "/counterfilemodel/listcmcounterfilemodelbybusifileid";

    /**
     * 分页查询类型配置信息
     */
    public static final String QUERY_CM_COUNTER_FILE_TYPE_BY_PAGE = "/counterfiletype/querycmcounterfiletypebypage";

    /**
     * 查询单个文件模板
     */
    public static final String GET_CM_COUNTER_FILE_MODEL = "/counterfilemodel/getcmcounterfilemodel";

    /**
     * 保存类型配置及模板信息
     */
    public static final String SANE_COUNTER_FILE_TYPE = "/counterfiletype/savecountfiletype";

    /**
     * 查询类型类型及模板列表
     */
    public static final String QUERY_COUNTER_FILE_TYPE_DTO_LIST = "/counterfiletype/querycounterfiletypedtolist";

    /**
     * 启用或者禁用类型配置操作
     */
    public static final String DEL_COUNTER_FILE_TYPE = "/counterfiletype/delcounterfiletype";

    /**
     * 查询柜台线上化订单附件信息
     */
    public static final String QUERY_COUNTER_ORDER_FILE_DTO_LIST = "/counterorderfile/querycounterorderfiledtolist";

    /**
     * 保存邮寄信息
     */
    public static final String SAVE_COUNTER_MAIL = "/counterorder/savecountermail";

    /**
     * 保存签收信息
     */
    public static final String SAVE_COUNTER_SIGN = "/counterorder/savecountersign";

    /**
     * 保存配置文件信息
     */
    public static final String SAVE_COUNTER_ARCH = "/counterorder/savecounterarch";

    /**
     * 根据id列表 查询订单列表
     */
    public static final String QUERY_COUNTER_ORDR = "/counterorder/querycounterorder";


    /**
     * 根据 vo对象 查询订单列表
     */
    public static final String QUERY_COUNTER_ORDR_BY_VO = "/counterorder/querycounterorderbyvo";

    /**
     * 保存批量邮寄信息
     */
    public static final String DEAL_BATCH_MAIL = "/counterorder/dealbatchmail";

    /**
     * 查询单个订单文件与附件关联关系
     */
    public static final String GET_CM_COUNTER_ORDER_FILE_FILE = "/counterorderfilefile/getcmcounterorderfilefile";


    /**
     * 获取文件 流
     */
    public static final String GET_CM_COUNTER_ORDER_FILE_STREAM="/counterorderfilefile/getCmCounterOrderfileFileStream";


    /**
     * 获取文件  配置文件 流
     */
    public static final String GET_CM_COUNTER_MODEL_FILE_STREAM="/counterfilemodel/getfilemodelstream";


    /**
     * 获取文件 签名文件 流
     */
    public static final String GET_COUNTER_SIGN_FILE_STREAM="/countersignfile/getcountersignfilestream";


    /**
     * 查询订单流水列表
     */
    public static final String QUERY_COUNTER_ORDER_FLOW_LIST = "/counterorderflow/querycounterorderflowlist";

    /**
     * 查询单个订单流水信息
     */
    public static final String GET_COUNTER_ORDER_FLOW = "/counterorderflow/getcounterorderflow";

    /**
     * 查询订单附件流水列表
     */
    public static final String QUERY_COUNTER_ORDER_FILE_FLOW_DTO_LIST = "/counterorderfileflow/querycounterorderfileflowdtolist";

    /**
     * 分页查询订单信息
     */
    public static final String QUERY_COUNTER_ORDER_BY_PAGE = "/counterorder/querycounterorderbypage";

    /**
     * 查询签字文件列表
     */
    public static final String QUERY_CM_COUNTER_ORDER_SIGN_FILE_LIST = "/countersignfile/querycmCounterordersignfilelist";

    /**
     * 上传签字文件
     */
    public static final String UPLOAD_COUNTER_ORDER_SIGN_FILE = "/countersignfile/uploadcounterordersignfile";

    /**
     * 查询单个签字文件信息
     */
    public static final String GET_CM_COUNTER_ORDER_SIGN_FILE = "/countersignfile/getcmcounterordersignfile";

    /**
     * 作废订单信息
     */
    public static final String INVALID_COUNTER_ORDER = "/counterorder/invalidcounterorder";

    /**
     * 订单撤回
     */
    public static final String WITHDRAW_COUNTER_ORDER = "/counterorder/withdrawcounterorder";

    /**
     * 查询订单相关信息
     */
    public static final String QUERY_COUNTER_ORDR_DTO_LIST= "/counterorder/querycounterorderdtolist";

    /**
     * 查询订单导出详情列表
     */
    public static final String QUERY_COUNTER_ORDER_RETURN_DETAIL = "/counterorder/querycounterorderreturndetail";

    /**
     * 查询订单最新驳回详情
     */
    public static final String QUERY_COUNTER_ORDER_LATEST_RETURN_DETAIL = "/counterorder/querycounterorderlatestreturndetail";

    /**
     * 根据业务类型和文件关系id查询类型配置列表：busiFileId
     */
    public static final String QUERY_COUNTER_FILE_TYPE_LIST_BY_CONDITION = "/counterfiletype/querycounterfiletypelistbycondition";

    /**
     * 查询订单附件列表
     */
    public static final String QUERY_ORDER_FILE_DTO_LIST = "/counterorderfile/querycounterorderfiledtolist";

    /**
     * 查询订单上传文件信息
     */
    public static final String QUERY_COUNTER_ORDER_FILE_FILE_LIST_BY_CONDITION = "/counterorderfilefile/querycounterorderfilefilelistbycondition";




    public static final String GET_BUSSINESS_BY_BUSITYPE="/counterorder/getbussinessbybusitype";

    /**
     * 新增时根据 busiType选取 bdId
     */
    public static final String EXECUTE_BEFORE_CHECK="/counterorder/executebeforecheck";



    /**
     * 新增时根据 busiType选取 bdId
     */
    public static final String EXECUTE_EDIT="/counterorder/executeedit";


    /**
     * 审核接口 path
     */
    public static final String COUNTER_CHECK="/counterorder/executecheck";
    
    
    /**
     * 查询单个订单附件
     */
    public static final String GET_COUNTER_ORDER_FILE="/counterorderfile/getcounterorderfile";


    /**
     * 新增订单
     */
    public static  final String  INSERT_COUNTER_ORDER="/counterorder/insertcmCounterorderbyconscustno";
    

    /**
     * 查询线上化订单文件与附件关联关系列表
     */
    public static  final String  QUERY_COUNTER_ORDER_FILE_FILE_DTO="/counterorderfilefile/querycounterorderfilefiledto";


    /**
     * 针对06-换卡 特殊逻辑：更新订单的卡信息字段  [TODO: 仅限 hb换卡流程调用，op初审审核操作前，更新订单信息。待删除]
     */
    public static final String UPDATE_ORDER_BANK_INFO="/counterorder/updateorderbankinfo";
    
    /**
     * 更新线上化订单客户号
     */
    public static final String UP_COUNTER_ORDER_CONSCUSTNO="/counterorder/upcounterorderconscustno";

}
