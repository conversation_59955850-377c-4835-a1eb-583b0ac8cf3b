/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.enums;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/10/19 10:03
 * @since JDK 1.8
 */
public enum UploadTradeTypeEnum {
    /**
     * 强赎
     */
    QS("6", "强赎"),
    /**
     * 强增
     */
    QZ("5", "强增"),
    /**
     * 强减
     */
    QJ("3", "强减"),
    /**
     * 股权回款
     */
    GQHK("1", "股权回款"),
    /**
     * 分红
     */
    FH("2", "分红"),
    /**
     * 非交易过户转出
     */
    FJYGHZC("7", "非交易过户转出"),
    /**
     * 非交易过户转入
     */
    FJYGHZR("8", "非交易过户转出"),
    /**
     * 基金清盘
     */
    JJQP("9", "基金清盘"),
    ;
    private String code;
    private String description;

    UploadTradeTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 通过code获得
     *
     * @param code
     *            系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code) {
        UploadTradeTypeEnum statusEnum = getEnum(code);
        return statusEnum == null ? null : statusEnum.getDescription();
    }

    /**
     * 通过code直接返回 整个枚举类型
     *
     * @param code
     *            系统返回参数编码
     * @return BaseConstantEnum
     */
    public static UploadTradeTypeEnum getEnum(String code) {
        for(UploadTradeTypeEnum statusEnum : UploadTradeTypeEnum.values()){
            if(statusEnum.getCode().equals(code)){
                return statusEnum;
            }
        }
        return null;
    }

    /**
     * 通过 description 直接返回 整个枚举类型
     * @param description
     * @return
     */
    public static UploadTradeTypeEnum getEnumByDesc(String description) {
        for(UploadTradeTypeEnum statusEnum : UploadTradeTypeEnum.values()){
            if(statusEnum.getDescription().equals(description)){
                return statusEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public static void main(String[] args) {
        System.out.println(UploadTradeTypeEnum.getDescription("1"));
    }
}
