/**   
 * @Title: ExcelImpTableConf.java 
 * @Package com.hb.crm.web.util.excel.conf 
 * @Description: TODO(用一句话描述该文件做什么) 
 * <AUTHOR>
 * @date 2016年4月28日 下午1:31:44 
 * @version V1.0   
 */

package com.howbuy.crm.hb.tools.excel.conf;


import com.howbuy.crm.hb.tools.excel.bean.BatchImpBean;

import java.util.HashMap;

/**
 * @ClassName: ExcelImpTableConf
 * @Description: 文件导入总配置类
 * <AUTHOR>
 * @date 2016年4月28日 下午1:31:44
 * 
 */

public class ExcelImpTableConf {

	public final static HashMap<String, BatchImpBean> confMap;

	static {
		confMap = new HashMap<String, BatchImpBean>();
		confMap.put("publicDoubleRs", new publicDoubleRsBatchImp().inits());
	}

}
