package com.howbuy.crm.hb.web.controller.manage;

import com.howbuy.crm.hb.domain.manage.CmAnnounce;
import com.howbuy.crm.hb.service.manage.CmAnnounceService;
import com.howbuy.crm.hb.web.util.ParamUtil;
import crm.howbuy.base.db.PageData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: Controller
 * <AUTHOR> @version 1.0
 * @created 
 */
@Controller
@RequestMapping(value = "/manage")
public class CmAnnounceController {
	@Autowired
	private CmAnnounceService cmAnnounceService;

    /**
     * 投顾首页获取部分公告数据方法
     */
	@RequestMapping("/listAnnounce_json.do")
	public @ResponseBody Map<String, Object> listAnnounce(HttpServletRequest request,HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		param = new ParamUtil(request).getParamMap();
		param.put("bankNotice", "1");
		List<CmAnnounce> listAnnounce = cmAnnounceService.listCmAnnounce(param);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("rows", listAnnounce);
		return resultMap;
	}
	
	/**
     * 投顾首页获取部分公告数据方法
     */
	@RequestMapping("/listBankAnnounce_json.do")
	public @ResponseBody Map<String, Object> listBankAnnounce_json(HttpServletRequest request,HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		param = new ParamUtil(request).getParamMap();
		param.put("bankNotice", "0");
		List<CmAnnounce> listAnnounce = cmAnnounceService.listCmAnnounce(param);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("rows", listAnnounce);
		return resultMap;
	}

}