/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.tools.excel.write;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import lombok.extern.slf4j.Slf4j;

import org.apache.poi.ss.usermodel.*;

import org.apache.poi.xssf.usermodel.XSSFRichTextString;

import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

/**
 * @description: (easyexecl工具类)
 * <AUTHOR>
 * @date 2024/11/7 19:50
 * @since JDK 1.8
 */
@Slf4j
public class EasyExcelTool {
    private EasyExcelTool() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 数字型
     */
    private static final String NUMERIC_TYPE="NUMERIC";

    /**
     * @description:(字段颜色变更导出数据)
     * @param os
     * @param list
     * @param clazz
     * @param sheetName
     * @param exportFontColorMap
     * @param columns
     * @return java.lang.String
     * @author: shijie.wang
     * @date: 2024/11/8 16:49
     * @since JDK 1.8
     */
    public static <T> String exportByEasyExcelByColumn(OutputStream os, List<T> list, Class<T> clazz, String sheetName,
                                                            Map<String, HashSet<String>> exportFontColorMap, Set<String> columns) {
        try {
            ExcelWriter excelWriter = EasyExcelFactory.write(os, clazz).includeColumnFiledNames(columns).inMemory(true).build();
            // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(sheetName).
                    registerWriteHandler(new HorizontalCellStyleStrategy(getHeadStyle(), getContentStyle())).
                    registerWriteHandler(new CellWriteHandler(){
                        /**
                         * key：行索引，value：行中需要修改的列字段名
                         */
                        Map<Integer, HashSet<String>> fontColorMap = new HashMap<>();
                        /**
                         * key：字段列索引, value：引字段名
                         */
                        Map<Integer, String> cellIndexMap = new HashMap<>();

                        @Override
                        public void afterCellCreate(CellWriteHandlerContext context) {
                            CellWriteHandler.super.afterCellCreate(context);
                        }

                        @Override
                        public void afterCellDispose(CellWriteHandlerContext context) {
                            Cell cell = context.getCell();
                            Workbook workbook = context.getWriteWorkbookHolder().getWorkbook();
                            String stringValue = null;
                            if(NUMERIC_TYPE.equals(cell.getCellType().name())){
                                stringValue = String.valueOf(cell.getNumericCellValue());
                            }else{
                                stringValue = cell.getStringCellValue();
                            }
                            int rowIndex = cell.getRowIndex();
                            int columnIndex = cell.getColumnIndex();
                            //表头不处理
                            if(0 == rowIndex){
                                cellIndexMap.put(columnIndex, stringValue);
                                return;
                            }
                            //每行的第一格userid判断是否存在fontColorMap值，存在写入当前行需要变红的字段
                            if(0 < rowIndex && 0 == columnIndex && exportFontColorMap.containsKey(stringValue)){
                                fontColorMap.put(rowIndex, exportFontColorMap.get(stringValue));
                            }
                            String columnName = cellIndexMap.get(columnIndex);
                            HashSet<String> fontColorSet = fontColorMap.get(rowIndex);
                            if(fontColorSet.contains(columnName)){
                                Font font = workbook.createFont();
                                font.setColor(Font.COLOR_RED);
                                XSSFRichTextString richString = new XSSFRichTextString(stringValue);
                                richString.applyFont(font);
                                cell.setCellValue(richString);
                            }
                        }
            }).build();
            excelWriter.write(list, writeSheet);
            excelWriter.finish();
            os.flush();
        }catch (IOException e){
            log.error("导出excel异常", e);
        }
        return null;
    }

    /**
     * 设置头部样式
     * @return
     */
    public static WriteCellStyle getHeadStyle(){
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        //设置自动换行
        headWriteCellStyle.setWrapped(false);
        headWriteCellStyle.setFillPatternType(FillPatternType.NO_FILL);
        headWriteCellStyle.setFillBackgroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headFont = new WriteFont();
        headFont.setFontName("宋体");
        headFont.setFontHeightInPoints((short)11);
        headFont.setBold(true);
        headWriteCellStyle.setWriteFont(headFont);
        return headWriteCellStyle;
    }

    /**
     * 设置内容样式
     * @return
     */
    public static WriteCellStyle getContentStyle(){
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //设置自动换行
        contentWriteCellStyle.setWrapped(false);
        return contentWriteCellStyle;
    }

}