package com.howbuy.crm.hb.web.controller.custinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.hb.constants.DfileConstants;
import com.howbuy.crm.hb.domain.custinfo.CmCustfamily;
import com.howbuy.crm.hb.domain.custinfo.CmCustfamilyFile;
import com.howbuy.crm.hb.domain.custinfo.CmCustfamilySub;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.CmCustfamilyFileService;
import com.howbuy.crm.hb.service.custinfo.CmCustfamilyService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.dfile.HFileService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @Description: Controller
 * <AUTHOR> @version 1.0
 * @created 
 */
@Slf4j
@Controller
@RequestMapping(value = "/conscust")
public class CmCustfamilyController {
	@Autowired
	private CmCustfamilyService cmCustfamilyService;
	
	@Autowired
	private ConscustService custService;
	
	@Autowired
    private CommonService commonService;
	
	@Autowired
    private CmCustfamilyFileService cmCustfamilyFileService;

	//原nacos配置。 切换webdav后，配置无需再用 。
	//历史存储 完整路径。  仍然存储。
	// 读取文件时，  截取 [/data/files/familyfile/] . 取相对路径 使用webdav

//	@Value("${FAMILY.FILE.PATH}")
    private static  String familyFilePath = "/data/files/familyfile/doc/";


    //webdav 定义文件目录在 【/data/files/familyfile/】 。 此处定义 下级目录 /doc
	//实际写入时，  二级目录 需要拼凑。  历史逻辑 。
	private static final String  DOC_PATH ="/doc";

//    @Value("${FAMILY.FILE.IMG.PATH}")
//    private String familyFileImgPath  = "/data/files/familyfile/img/";
	    
	    
	@RequestMapping("/listCmCustfamily.do")
	public String listCmCustfamily(HttpServletRequest request) {
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		request.setAttribute("userId", userlogin.getUserId());
		return "/custinfo/custFamilyList";
	}
	
	/**
	 * 加载页面数据方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listCmCustfamilyByPage.do")
	public Map<String, Object> listCmCustfamilyByPage(HttpServletRequest request, HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		// 获取分页参数
		param = new ParamUtil(request).getParamMap();
		String custname = request.getParameter("custname");
		String checkflag = request.getParameter("checkflag");
		String orgCode = request.getParameter("orgCode");
		String consCode = request.getParameter("consCode");
		String beginDt = request.getParameter("creatDeginDt");
		String endDt = request.getParameter("creatEndDt");
		
		//查询条件（客户名）不为空
		if(StringUtil.isNotNullStr(custname)){
			param.put("custname", custname);
		}else{
			param.put("custname", null);
		}
		
		//如果查询条件（审核标志）不为空
		if(StringUtil.isNotNullStr(checkflag)){
			param.put("checkflag", checkflag);
		}else{
			param.put("checkflag", null);
		}
		if(StringUtil.isNotNullStr(beginDt)){
			param.put("beginDt", beginDt);
		}
		if(StringUtil.isNotNullStr(endDt)){
			param.put("endDt", endDt);
		}
		if(StringUtil.isNotNullStr(consCode)){
			param.put("conscode", consCode);
		}else{
			//选择了未分配组
			if(orgCode.startsWith("other")){
				param.put("othertearm", orgCode.replaceFirst("other", ""));
			}else{
				String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
				//选择了团队
				if(StaticVar.ORGTYPE_TEAM.equals(orgType)){
					param.put("teamcode", orgCode);
				}else{
					if(!"0".equals(orgCode)){
						List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
						param.put("outletcodes", Util.getSqlInStr(suborgs));
					}
				}
			}
		}
		
		PageData<CmCustfamily> pageData = cmCustfamilyService.listCmCustfamilyByPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmCustfamily> listdata= pageData.getListData();
		for(CmCustfamily info : listdata){
			ConsOrgCache orgcache = ConsOrgCache.getInstance();
			info.setCheckflag(ConstantCache.getInstance().getVal("familycheckflag", info.getCheckflag()));
			info.setOrgname(orgcache.getAllOrgMap().get(orgcache.getCons2OutletMap().get(info.getConscode())));
			info.setConsname(orgcache.getAllConsMap().get(info.getConscode()));

			String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(info.getConscode()));
			if("0".equals(uporgcode)){
				info.setUporgname(info.getOrgname());
			}else{
				info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
			}

			StringBuffer sb = new StringBuffer();
			if(StringUtil.isNotNullStr(info.getSource())){
				sb.append(info.getSource());
			}
			if(StringUtil.isNotNullStr(info.getSubsource())){
				sb.append("--"+info.getSubsource());
			}
			if(StringUtil.isNotNullStr(info.getSubsourcetype())){
				sb.append("--"+info.getSubsourcetype());
			}
			info.setSourcename(sb.toString());			
		}
		resultMap.put("rows", listdata);
		return resultMap;
	}
	
	/**
	 * 补充客户信息方法
	 * @param request
	 * @param response
	 */
	@ResponseBody
	@RequestMapping("/getSourceByCustNo.do")
	public void getSourceByCustNo(HttpServletRequest request, HttpServletResponse response) {
		String custno = request.getParameter("custno");
		Conscust conscust = custService.getConscust(custno);
		if(conscust != null){
			if ("B".equals(conscust.getNewsource())	&& StringUtil.isNotNullStr(conscust.getCustSourceRemark())) {
				Conscust cust = custService.getConscust(StringUtil.replaceNull(conscust.getCustSourceRemark()));
				if (cust != null) {
					conscust.setCustSourceRemark(cust.getCustname());
				}
			}
			StringBuffer source = new StringBuffer();
			if (StringUtil.isNotNullStr(conscust.getNewsourcename())) {
				source.append(conscust.getNewsourcename());
			}
			if (StringUtil.isNotNullStr(conscust.getNewsubsourcename())) {
				source.append("--" + conscust.getNewsubsourcename());
			}
			if (StringUtil.isNotNullStr(conscust.getNewsubsourcetypename())) {
				source.append("--" + conscust.getNewsubsourcetypename());
			}
			StringBuffer sb = new StringBuffer();
			sb.append("{\"source\":\""+source+"\",\"sourceremark\":\""+StringUtil.replaceNull(conscust.getCustSourceRemark())+"\",\"regdt\":\""+StringUtil.replaceNull(conscust.getRegdt())+"\"}");
			response.setContentType("text/html; charset=utf-8");
			PrintWriter pw = null;
			try {
				pw = response.getWriter();
				pw.print(sb.toString());
				pw.flush();
			} catch (IOException e) {
				e.printStackTrace();
			} finally{
				if(pw!=null) {
					pw.close();
				}
			}
		}
	}

	/**
	 * 保存关联账户信息方法
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/saveCustFamily.do")
	public String saveCustFamily(HttpServletRequest request){
		User user = (User)request.getSession().getAttribute("loginUser");
		String result = "success";
		String custstr = request.getParameter("custstr");
		if(StringUtil.isNotNullStr(custstr)){
			String [] arr = custstr.split(",");
			StringBuffer sb = new StringBuffer();
			for(int i = 0;i < arr.length;i++){
				sb.append(",'"+arr[i].split("\\|")[0]+"'");
			}
			String insql = sb.toString().replaceFirst(",", "");
			Map<String,String> param = new HashMap<String,String>();
			param.put("insql", insql);
			int count = cmCustfamilyService.getCmCustfamilyCountByCusts(param);
			if(count > 0){
				result = "hasotherfamily";
			}else{
				String master = arr[0];
				master = master.replace("|","");
				CmCustfamily masterobj = new CmCustfamily();
				masterobj.setConscustno(master);
				masterobj.setCheckflag(StaticVar.FAMILY_CHECK_FLAG_NO);
				masterobj.setCreator(user.getUserId());
				masterobj.setCredt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
				List<CmCustfamilySub> subcust = new ArrayList<CmCustfamilySub>();
				if(arr.length > 1){
					for(int i = 1;i<arr.length;i++ ){
						CmCustfamilySub sub = new CmCustfamilySub();
						String[] ar = arr[i].split("\\|");
						sub.setConscustno(arr[i].split("\\|")[0]);
						sub.setFamilycode(master);
						sub.setCreator(user.getUserId());
						if(ar.length > 1){
							sub.setRemark(arr[i].split("\\|")[1]);
						}else{
							sub.setRemark("");
						}
						sub.setCredt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
						subcust.add(sub);
					}
				}
				
				

				MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
	            List<MultipartFile> files = multipartRequest.getFiles("filelist[]");
	            List<CmCustfamilyFile> listCmCustfamilyFile = new ArrayList<CmCustfamilyFile>();
	            for (int i = 0; i < files.size(); i++) {
	            	String servicefilename = null;
	                try {
	                	MultipartFile file = files.get(i);
	                    String filename = file.getOriginalFilename();
	                    String suffix = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
	                    filename = filename.substring(0,filename.length() - suffix.length() - 1);

	                    CmCustfamilyFile cmCustfamilyFile = new CmCustfamilyFile();
	                    int filesize = (int) file.getSize();
	                    cmCustfamilyFile.setId(String.valueOf(commonService.getSeqValue("SEQ_CM_CUSTFAMILY_FILE")));
	                    cmCustfamilyFile.setConscustno(master);;
	                    cmCustfamilyFile.setFilename(filename);
	                    cmCustfamilyFile.setFilesize(new BigDecimal(filesize));
	                    cmCustfamilyFile.setFilesuffix(suffix);
						//   /data/files/familyfile/doc/20210303
	                    cmCustfamilyFile.setFilepath(familyFilePath + DateTimeUtil.getCurDate());
	                    cmCustfamilyFile.setIsdel("0");//有效
	                    cmCustfamilyFile.setUploaddate(new Date());
	                    cmCustfamilyFile.setUploador(user.getUserId());
	                    listCmCustfamilyFile.add(cmCustfamilyFile);
	                    
	                    servicefilename = filename + "." + suffix;
	                    uploadFile(file.getInputStream(), cmCustfamilyFile.getId(), servicefilename);
						//不在上传转img图片，  后续预览方案为：  使用onlyOffice .参考 资料管理
						//该功能为历史功能，所以 此处不再实现 img图片生成， 也没有支持 onlyOffice 预览
//	                    if ("PDF".equals(suffix)) {
//	                        pdfToImg(cmCustfamilyFile.getId(),servicefilename, nowdate, path, imgpath);
//	                    }
	                } catch (Exception e) {
	                    log.error("上传关联账户文件失败:" + servicefilename + ":" + e.getMessage(), e);
	                    result = "fileFail";
	            		return result;
	                }
	            }
	            
				cmCustfamilyService.insertCmCustfamily(masterobj, subcust,listCmCustfamilyFile);
			}
		}else{
			result = "paramError";
		}
		return result;
	}
	
	/**
	 * 修改关联账户信息方法
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/updateCustFamily.do")
	public String updateCustFamily(HttpServletRequest request){
		String result = "success";
		User user = (User)request.getSession().getAttribute("loginUser");
		String custstr = request.getParameter("custstr");
		String delfileids = request.getParameter("delfileids").replaceFirst("#", "");
		if(StringUtil.isNotNullStr(custstr)){
			String [] arr = custstr.split(",");
			String master = arr[0];
			master = master.replace("|","");
			StringBuffer sb = new StringBuffer();
			for(int i = 0;i < arr.length;i++){
				sb.append(",'"+arr[i].split("\\|")[0]+"'");
			}
			String insql = sb.toString().replaceFirst(",", "");
			Map<String,String> param = new HashMap<String,String>();
			param.put("insql", insql);
			param.put("conscustno", master);
			int count = cmCustfamilyService.getCmCustfamilyCountByCusts(param);
			if(count > 0){
				result = "hasotherfamily";
			}else{
				Map<String,String> parm = new HashMap<String,String>();
				parm.put("conscustno", master);
				CmCustfamily masterf = cmCustfamilyService.getCmCustfamily(parm);
				if(masterf != null){
					masterf.setCheckflag(StaticVar.FAMILY_CHECK_FLAG_NO);
					masterf.setModifier(user.getUserId());
					masterf.setModdt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
				}else{
					result = "paramError";
					return result;
				}
				List<CmCustfamilySub> subcust = new ArrayList<CmCustfamilySub>();
				if(arr.length > 1){
					for(int i = 1;i<arr.length;i++ ){
						CmCustfamilySub sub = new CmCustfamilySub();
						sub.setConscustno(arr[i].split("\\|")[0]);
						sub.setFamilycode(master);
						sub.setCreator(user.getUserId());
						sub.setRemark(arr[i].split("\\|")[1]);
						sub.setCredt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
						subcust.add(sub);
					}
				}
				
				
				MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
	            List<MultipartFile> files = multipartRequest.getFiles("filelist[]");
	            List<CmCustfamilyFile> insertFamilyFileList = new ArrayList<CmCustfamilyFile>();
	            if(files == null || files.size() ==0){
	            	if(StringUtils.isNotEmpty(delfileids)){
	            		param.clear();
	            		param.put("conscustno", master);
	                	param.put("isdel", "0");
	                	List<CmCustfamilyFile> listCmCustfamilyFile = cmCustfamilyFileService.listCmCustfamilyFile(param);
	                	count = listCmCustfamilyFile.size();
	                	
	                	String [] delids = delfileids.split("#");
	                	for(CmCustfamilyFile familyFile : listCmCustfamilyFile){
	                		for(String id : delids){
	                			if(id.equals(familyFile.getId())){
	                				count --;
	                				break;
	                			}
	                    	}
	                	}
	                	
	                	if(count == 0){
	                		result = "notFile";
	                		return result;
	                	}
	            	}
	            }else{
	            	for (int i = 0; i < files.size(); i++) {
		            	String servicefilename = null;
		                try {
		                	MultipartFile file = files.get(i);
		                    String filename = file.getOriginalFilename();
		                    String suffix = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
		                    filename = filename.substring(0,filename.length() - suffix.length() - 1);

		                    CmCustfamilyFile cmCustfamilyFile = new CmCustfamilyFile();
		                    int filesize = (int) file.getSize();
		                    cmCustfamilyFile.setId(String.valueOf(commonService.getSeqValue("SEQ_CM_CUSTFAMILY_FILE")));
		                    cmCustfamilyFile.setConscustno(master);;
		                    cmCustfamilyFile.setFilename(filename);
		                    cmCustfamilyFile.setFilesize(new BigDecimal(filesize));
		                    cmCustfamilyFile.setFilesuffix(suffix);
		                    cmCustfamilyFile.setFilepath(familyFilePath + DateTimeUtil.getCurDate());
		                    cmCustfamilyFile.setIsdel("0");//有效
		                    cmCustfamilyFile.setUploaddate(new Date());
		                    cmCustfamilyFile.setUploador(user.getUserId());
		                    insertFamilyFileList.add(cmCustfamilyFile);
		                    
		                    servicefilename = filename + "." + suffix;
		                    uploadFile(file.getInputStream(), cmCustfamilyFile.getId(), servicefilename);

		                } catch (Exception e) {
		                    log.error("上传关联账户文件失败:" + servicefilename + ":" + e.getMessage(), e);
		                    result = "fileFail";
		            		return result;
		                }
		            }
	            }
	            
	            List<CmCustfamilyFile> deleteFamilyFileList = new ArrayList<CmCustfamilyFile>();
	            if(StringUtils.isNotEmpty(delfileids)){
	            	String [] delids = delfileids.split("#");
	            	for(String id : delids){
	            		CmCustfamilyFile custfamilyFile = new CmCustfamilyFile();
	            		custfamilyFile.setConscustno(master);
	            		custfamilyFile.setId(id);
	            		custfamilyFile.setIsdel("1");
	            		custfamilyFile.setDelor(user.getUserId());
	            		custfamilyFile.setDeldate(new Date());
	            		deleteFamilyFileList.add(custfamilyFile);
	            	}
	            }
	            
				cmCustfamilyService.updateCmCustfamily(masterf, subcust,insertFamilyFileList,deleteFamilyFileList);
				
			}
		}else{
			result = "paramError";
		}
		return result;
	}
	

	@ResponseBody
	@RequestMapping("/getFamilyFileStream.do")
	public void getFamilyFileStream(HttpServletRequest request, HttpServletResponse response) {
		String id = request.getParameter("id");
		Map<String, String> param = new HashMap<String, String>();
		param.put("id", id);
		CmCustfamilyFile file = cmCustfamilyFileService.getCmCustfamilyFile(param);

// 数据库数据
//		ID	CONSCUSTNO	FILENAME	FILESIZE	FILESUFFIX	FILEPATH	ISDEL	UPLOADOR	UPLOADDATE
//		160	1177858311	机构账户授权协议	2641293	JPG	/data/files/familyfile/doc/20210303	0	wenying.liu	2021-03-03 17:43:38.090000


//实际文件存储
///data/files/familyfile/doc/20210303/160/机构账户授权协议.JPG

		// /data/files/familyfile/doc/20210303
		String  dbPath = file.getFilepath();
		// 历史功能，存储全路径。 截取 [/data/files/familyfile]  -->   /doc/20210303
		String relativePah = dbPath.replace(DfileConstants.CUST_FAMILY_PREFIX_PATH, "");
		//读取 存储路径 ： /doc/20210303  +   /160
		String readRelativePath  = relativePah + File.separator + id ;


		String fileName =file.getFilename() +"." + file.getFilesuffix();

		//读取
		HFileService instance = HFileService.getInstance();
		byte[]  fileBytes= new byte[0];
		try {
			log.info("文件file：{} 读取开始，relativePath:{},fileName:{}", JSON.toJSONString(file),relativePah,fileName);
			fileBytes = instance.read2Bytes(DfileConstants.CUST_FAMILY_STORE_CONFIG,
					readRelativePath,
					fileName);
		} catch (Exception e) {
			log.error("文件file：{} ，读取异常！",JSON.toJSONString(file));
			log.error("读取文件异常！",e);
		}

		ServletOutputStream outputStream = null;
		try {
			response.setContentType("application/octet-stream");
			response.setHeader("Content-Disposition", "attachment;fileName=" + new String(fileName.getBytes("gb2312"), StandardCharsets.ISO_8859_1));
			//输出文件流
			outputStream = response.getOutputStream();
			outputStream.write(fileBytes);
		}catch (Exception e){
			log.error("预览失败，fileName：{}, useId：{}", fileName, e);
		}finally {
			if (null != outputStream) {
				try {
					outputStream.close();
				} catch (IOException e) {
					log.error("关闭文件流异常", e);
				}
			}
		}

	}
	
	/**
	 * 跳转到客户明细列表
	 * @param request
	 * @return String
	 */
	@RequestMapping("/getSubfamilyCustDetail.do")
	public String getSubfamilyCustDetail(HttpServletRequest request) {
		String conscustno = request.getParameter("conscustno");
		request.setAttribute("conscustno", conscustno);
		return "/custinfo/custFamilySubDetail";
	}
	
	/**
	 * 展示关联账户子成员方法
	 * @param request
	 * @param response
	 * @param model
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listSubfamilyCustDetail.do")
	public Map<String, Object> listSubfamilyCustDetail(HttpServletRequest request, HttpServletResponse response,ModelMap model) throws Exception {
		String conscustno = request.getParameter("conscustno");
		
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		if (StringUtil.isNotNullStr(conscustno)) {
			param.put("conscustno", conscustno);
		} else {
			param.put("conscustno", "XXXXXXXXXX");
		}

		List<CmCustfamily> list = cmCustfamilyService.listCmCustfamilySubByMasterCust(param);
		for (CmCustfamily info : list) {
			StringBuffer sb = new StringBuffer();
			if (StringUtil.isNotNullStr(info.getSource())) {
				sb.append(info.getSource());
			}
			if (StringUtil.isNotNullStr(info.getSubsource())) {
				sb.append("--" + info.getSubsource());
			}
			if (StringUtil.isNotNullStr(info.getSubsourcetype())) {
				sb.append("--" + info.getSubsourcetype());
			}
			info.setSourcename(sb.toString());
		}
		resultMap.put("rows", list);
		return resultMap;
	}
	
	/**
	 * 获取家庭客户信息方法
	 * @param request
	 * @return Map<String, Object>
	 */
	@ResponseBody
	@RequestMapping("/getCustFamily.do")
	public Map<String, Object> getCustFamily(HttpServletRequest request) {
		User user = (User) request.getSession().getAttribute("loginUser");

		Map<String, Object> resultMap = new HashMap<String, Object>();
		String conscustno = request.getParameter("conscustno");
		
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		if (StringUtil.isNotNullStr(conscustno)) {
			param.put("conscustno", conscustno);
		} else {
			param.put("conscustno", "XXXXXXXXXX");
		}
		
		List<CmCustfamily> list = cmCustfamilyService.listCmCustfamilyAllByMasterCust(param);
		CmCustfamily master = new CmCustfamily();
		List<CmCustfamily> sublist = new ArrayList<CmCustfamily>();
		for (CmCustfamily info : list) {
			StringBuffer sb = new StringBuffer();
			if (StringUtil.isNotNullStr(info.getSource())) {
				sb.append(info.getSource());
			}
			if (StringUtil.isNotNullStr(info.getSubsource())) {
				sb.append("--" + info.getSubsource());
			}
			if (StringUtil.isNotNullStr(info.getSubsourcetype())) {
				sb.append("--" + info.getSubsourcetype());
			}
			info.setSourcename(sb.toString());
			if (info.getConscustno().equals(conscustno)) {
				master = info;
			} else {
				sublist.add(info);
			}
		}
		
		param.clear();
		param.put("conscustno", conscustno);
		param.put("isdel", "0");
		//NOTICE : 2025年3月27日 WEBDAV迁移标记： 家庭账户上传文件表 CM_CUSTFAMILY_FILE 日期停留在  2022-03-21 18:16:27.151000
        // 搜索[2025年3月27日 WEBDAV迁移标记] 获取更多相关注释
		List<CmCustfamilyFile> listCmCustfamilyFile = cmCustfamilyFileService.listCmCustfamilyFile(param);

		resultMap.put("usercode", user.getUserId());
		resultMap.put("master", master);
		resultMap.put("sublist", sublist);
		resultMap.put("listCmCustfamilyFile", listCmCustfamilyFile);
		return resultMap;
	}
	
	/**
	 * 删除关联账户信息方法
	 * @param request
	 * @param response
	 */
	@ResponseBody
	@RequestMapping("/delCustFamily.do")
	public void delCustFamily(HttpServletRequest request, HttpServletResponse response) {
		String conscustno = request.getParameter("conscustno");
		String result = "";
		User user = (User) request.getSession().getAttribute("loginUser");

		log.info("cmCustfamilyService.delCmCustfamily:"+conscustno);
		cmCustfamilyService.delCmCustfamily(conscustno);
		
		Map<String,String> param = new HashMap<String,String>();
		param.put("conscustno", conscustno);
		param.put("isdel", "0");
		List<CmCustfamilyFile> list = cmCustfamilyFileService.listCmCustfamilyFile(param);
		if(CollectionUtils.isNotEmpty(list)){
			for(CmCustfamilyFile familyFile : list){
				CmCustfamilyFile cmCustfamilyFile = new CmCustfamilyFile();
				cmCustfamilyFile.setId(familyFile.getId());
				cmCustfamilyFile.setIsdel("1");
				cmCustfamilyFile.setDeldate(new Date());
				cmCustfamilyFile.setDelor(user.getUserId());
				cmCustfamilyFileService.updateCmCustfamilyFile(cmCustfamilyFile);
			}
		}
		
		result = "1";
		response.setContentType("text/html;charset=UTF-8");
		PrintWriter pw = null;
		try {
			pw = response.getWriter();
			pw.print(result);
			pw.flush();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (pw != null) {
				pw.close();
			}
		}
	}
	
	/**
	 * 关联账户审核方法
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/auditCustFamilyDeal.do")
	public String auditCustFamilyDeal(HttpServletRequest request){
		User user = (User) request.getSession().getAttribute("loginUser");
		String result = null;
		String conscustno = request.getParameter("conscustno");
		String custfamilyremark = request.getParameter("custfamilyremark");
		String stat = request.getParameter("stat");
		Map<String, String> parm = new HashMap<String, String>();
		if (StringUtil.isNotNullStr(conscustno)	&& StringUtil.isNotNullStr(stat)) {
			parm.put("conscustno", conscustno);
		} else {
			result = "paramError";
			return result;
		}
		if (StaticVar.FAMILY_CHECK_FLAG_PASS.equals(stat)) {
			int conscodenum = cmCustfamilyService.getDistinctConscodeNumByMastercode(parm);
			if (conscodenum != 1) {
				result = "distinctconscode";
				return result;
			}
		}
		CmCustfamily masterf = cmCustfamilyService.getCmCustfamily(parm);
		if (masterf != null) {
			masterf.setCheckflag(stat);
			masterf.setChecker(user.getUserId());
			masterf.setCustfamilyremark(custfamilyremark);
			masterf.setCheckdt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
			cmCustfamilyService.updateCmCustfamily(masterf);
			result = "success";
		} else {
			result = "paramError";
		}
		return result;
	}
	
	
	/**
	 *webDav写入文件
	 * @param inputStream 上传的文件流
	 * @param fileName 文件名称（包括后缀名称）
	 * @throws Exception
	 */
	private static void uploadFile(InputStream inputStream,String id ,String fileName) throws Exception{
		//读取
		HFileService instance = HFileService.getInstance();
		//   /doc/20250429/id
		String readRelativePath= String.join(File.separator,DOC_PATH,DateTimeUtil.getCurDate(),id);
		byte[]  fileBytes= new byte[0];
		log.info("文件写入开始，relativePath:{},fileName:{}", readRelativePath,fileName);
		instance.write(DfileConstants.CUST_FAMILY_STORE_CONFIG,
				readRelativePath,
				fileName,
				inputStream);

	}

	
}