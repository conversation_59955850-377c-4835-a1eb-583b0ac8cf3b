package com.howbuy.crm.hb.web.controller.prosale;

import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.prosale.CmPreCalendarLogService;
import com.howbuy.crm.hb.service.prosale.CmXkCalendarWarningService;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prosale.dto.CmPreWarningThreshold;
import com.howbuy.crm.prosale.dto.CmXkCalendarWarning;
import com.howbuy.crm.prosale.service.PreXkCalendarWarningService;

import crm.howbuy.base.db.PageData;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.howbuy.crm.base.BaseConstantEnum;
import com.howbuy.crm.base.BaseResponse;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Description: 销控日历预警Controller
 * <AUTHOR> @version 1.0
 * @created 
 */
@Controller
@RequestMapping(value = "/prosale")
public class CmXkCalendarWarningController {
	
	@Autowired
    private CmXkCalendarWarningService cmXkCalendarWarningService;

	@Autowired
	private CommonService commonService;
	
	@Autowired
	private PreXkCalendarWarningService preXkCalendarWarningService;
	
	@Autowired
	private CmPreCalendarLogService cmPreCalendarLogService;
	
	/**
	 * 跳转到销控通配日历预警信息页面方法
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listCmXkCalendarWarning.do")
	public ModelAndView listCmXkCalendarWarning(HttpServletRequest request) {
		User user = (User) request.getSession().getAttribute("loginUser");
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/prosale/listCmXkCalendarWarning");
		modelAndView.addObject("operator",ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(user.getUserId()))));
		return modelAndView;
	}

	/**
	 * 加载销控通配日历预警信息页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/listCmXkCalendarWarningByPage_json.do")
	public Map<String, Object> listCmXkCalendarWarningByPage(HttpServletRequest request) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(3);
		
		Map<String, String> param = new ParamUtil(request).getParamMap();
		PageData<CmXkCalendarWarning> pageData = cmXkCalendarWarningService.listCmXkCalendarWarningByPage(param);
		List<CmXkCalendarWarning> listdata = pageData.getListData();
		if(CollectionUtils.isNotEmpty(listdata)){
			for(CmXkCalendarWarning cmXkCalendarWarning : listdata){
				if(StringUtils.isNotBlank(cmXkCalendarWarning.getChecker())){
					cmXkCalendarWarning.setChecker(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(cmXkCalendarWarning.getChecker()))));
				}
				if(StringUtils.isNotBlank(cmXkCalendarWarning.getCreator())){
					cmXkCalendarWarning.setCreator(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(cmXkCalendarWarning.getCreator()))));
				}
				if(StringUtils.isNotBlank(cmXkCalendarWarning.getModifier())){
					cmXkCalendarWarning.setModifier(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(cmXkCalendarWarning.getModifier()))));
				}
				
			}
		}
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		resultMap.put("rows", pageData.getListData());
		return resultMap;
	}
	
	
	
	/**
     * 跳转到addCmXkCalendarWarning页面方法
     * @return String
     */
    @ResponseBody
    @RequestMapping("/addCmXkCalendarWarning.do")
    public ModelAndView addCmXkCalendarWarning(HttpServletRequest request) throws Exception {
        return new ModelAndView("prosale/addCmXkCalendarWarning");
    }
	
	
	/**
	 * 保存通配日历预警信息
	 * @param request
	 * @return
	 * @throws Exception
	 */
    @ResponseBody
	@RequestMapping("/saveCmXkCalendarWarning.do")
	public String saveCmXkCalendarWarning(HttpServletRequest request) throws Exception {
		String result="";
		User user = (User) request.getSession().getAttribute("loginUser");
		//验证日历通配预警信息参数是否合法
		result = validateXkCalendarWarning(request);
		if(StringUtils.isNotBlank(result)){
			return result;
		}
		
		//封装日历通配预警参数对象
		CmXkCalendarWarning cmXkCalendarWarning = this.handCmXkCalendarWarning(request);
		//保存日历预警通配参数
		BaseResponse res = preXkCalendarWarningService.saveCmXkCalendarWarning(cmXkCalendarWarning);
		if(BaseConstantEnum.SUCCESS.getCode().equals(res.getReturnCode())){
			result="success";
			
			//isUpCmXkCalendarWarning：1-修改通配
	    	if("1".equals(request.getParameter("isUpCmXkCalendarWarning"))){
	    		cmPreCalendarLogService.insertCmPreCalendarLog(cmXkCalendarWarning.getId(), "3", user.getUserId(), "1");
	    	}
		}else{
			result = res.getDescription();
		}
		
		return result;
	}
    
    /**
     * 封装日历通配预警参数对象
     * @param request
     * @return
     */
    private CmXkCalendarWarning handCmXkCalendarWarning(HttpServletRequest request){
    	User user = (User) request.getSession().getAttribute("loginUser");
    	String id = request.getParameter("id");
    	String calendarId = request.getParameter("calendarId");
		String currentAmountRatio = request.getParameter("currentAmountRatio");
		String currentPersonRatio = request.getParameter("currentPersonRatio");
		String warningThresholdStr = request.getParameter("warningThresholdStr");
    	
		CmXkCalendarWarning cmXkCalendarWarning = null;
		if(StringUtils.isNotBlank(id)){
			cmXkCalendarWarning = preXkCalendarWarningService.getCmXkCalendarWarningById(id);
			cmXkCalendarWarning.setCalendarId(calendarId);
			cmXkCalendarWarning.setUpdateStimestamp(new Date());
			cmXkCalendarWarning.setModifier(user.getUserId());
		}else{
			cmXkCalendarWarning = new CmXkCalendarWarning();
			cmXkCalendarWarning.setId(commonService.getSeqValue("SEQ_PRE_CALENDAR"));
			cmXkCalendarWarning.setCalendarId("*");
			cmXkCalendarWarning.setCreateStimestamp(new Date());
			cmXkCalendarWarning.setCreator(user.getUserId());
		}
    	//待审核
		cmXkCalendarWarning.setCheckStatus("1");
    	cmXkCalendarWarning.setCurrentAmountRatio(StringUtils.isBlank(currentAmountRatio) ? null : new BigDecimal(currentAmountRatio));
    	cmXkCalendarWarning.setCurrentPersonRatio(StringUtils.isBlank(currentPersonRatio) ? null : new BigDecimal(currentPersonRatio));
    	
    	if(StringUtils.isNotBlank(warningThresholdStr)){
    		List<CmPreWarningThreshold> listCmPreWarningThreshold = new ArrayList<CmPreWarningThreshold>(100);
    		String[] arr = warningThresholdStr.split(";");
    		
    		for(String str : arr){
    			if(StringUtils.isNotBlank(str)){
    				String thresholdId = commonService.getSeqValue("SEQ_PRE_CALENDAR");
    				String[] thresholdArr = str.split(",");
    				CmPreWarningThreshold cmPreWarningThreshold = new CmPreWarningThreshold ();
    				cmPreWarningThreshold.setId(thresholdId);
        			cmPreWarningThreshold.setWarningId(cmXkCalendarWarning.getId());
        			cmPreWarningThreshold.setCreateStimestamp(new Date());
        			cmPreWarningThreshold.setCreator(user.getUserId());
        			cmPreWarningThreshold.setWarningType(StringUtils.isBlank(thresholdArr[0]) ? null : thresholdArr[0]);
        			cmPreWarningThreshold.setWarningNumber(StringUtils.isBlank(thresholdArr[1]) ? null : new BigDecimal(thresholdArr[1]));
        			listCmPreWarningThreshold.add(cmPreWarningThreshold);
    			}
    			
    		}
    		if(CollectionUtils.isNotEmpty(listCmPreWarningThreshold)){
    			cmXkCalendarWarning.setListCmPreWarningThreshold(listCmPreWarningThreshold);
    		}
    	}
    	
    	return cmXkCalendarWarning;
    }
    
    /**
     * 验证日历预警信息参数是否合法
     * @param request
     * @return
     */
    private String validateXkCalendarWarning(HttpServletRequest request){
    	String result = null;
		String warningThresholdStr = request.getParameter("warningThresholdStr");

		//Op和投顾打款预警参数相同字段名称的数值不能相同
		Map<String,String> warningMap = new HashMap<String,String>(100);
		if(StringUtils.isNotBlank(warningThresholdStr)){
			String[] arr = warningThresholdStr.split(";");
			for(String thresholdStr : arr){
				if(warningMap.containsKey(thresholdStr)){
					result = "【参数名称】存在相同参数值，请重新输入！";
					return result;
				}else{
					warningMap.put(thresholdStr, thresholdStr);
				}
			}
		}
		
		String id = request.getParameter("id");
		if(StringUtils.isBlank(id)){
			CmXkCalendarWarning cmXkCalendarWarning = cmXkCalendarWarningService.getCmXkWarningByCalendarId("*");
			if(cmXkCalendarWarning != null){
				result = "已存在通配参数，不允许新增！！";
				return result;
			}
		}
		
		return result;
    }
    
    
    
    /**
	 * 删除销控日历预警及其相关信息
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/deleteCmXkCalendarWarning.do")
	public String deleteCmXkCalendarWarning(HttpServletRequest request) {
		String result = null;
		String id = request.getParameter("id");
		
		if (StringUtils.isNotBlank(id)) {
			BaseResponse res = preXkCalendarWarningService.delCmXkCalendarWarning(id);
			if(BaseConstantEnum.SUCCESS.getCode().equals(res.getReturnCode())){
				result="success";
			}else{
				result = res.getDescription();
			}
		} else {
			result = "paramError";
		}
		return result;
	}
	
	
	
	/**
     * 跳转到viewCmXkCalendarWarning页面方法
     *
     * @return String
     */
    @RequestMapping("/viewCmXkCalendarWarning.do")
    public ModelAndView viewCmXkCalendarWarning(HttpServletRequest request,HttpServletResponse response) throws Exception {
        String id = request.getParameter("id");
        //type：1-审核；2-详情；3-编辑;4-查看op预警参数；5-查看投顾预警参数
        String type = request.getParameter("type");
        Map<String, Object> map = new HashMap<String, Object>();
        CmXkCalendarWarning cmXkCalendarWarning = null;
        if (StringUtils.isNotBlank(id)) {
        	cmXkCalendarWarning = preXkCalendarWarningService.getCmXkCalendarWarningById(id);
        	map.put("cmXkCalendarWarning", cmXkCalendarWarning);
        	map.put("listCmPreWarningThreshold", cmXkCalendarWarning.getListCmPreWarningThreshold());
        	map.put("type",type);
        }
        String url = "/prosale/checkCmXkCalendarWarning";
        if("3".equals(type)){
        	url = "/prosale/editCmXkCalendarWarning";
        //查看op预警参数或者投顾预警参数	
        }else if("4".equals(type) || "5".equals(type)){
        	url = "/prosale/viewCmXkCalendarWarning";
        }
        return new ModelAndView(url, "map", map);
    }
    
    
    
    /**
	 * 审核预约日历及其相关信息
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/checkCmXkCalendarWarning.do")
	public String checkCmXkCalendarWarning(HttpServletRequest request) {
		String result = null;
		String id = request.getParameter("id");
		String checkStatus = request.getParameter("checkStatus");
		
		if (StringUtils.isNotBlank(id)) {
			User user = (User) request.getSession().getAttribute("loginUser");
			BaseResponse res = preXkCalendarWarningService.checkCmXkCalendarWarning(id, user.getUserId(),checkStatus);
			if(BaseConstantEnum.SUCCESS.getCode().equals(res.getReturnCode())){
				result="success";
			}else{
				result = res.getDescription();
			}
		} else {
			result = "paramError";
		}
		return result;
	}
}