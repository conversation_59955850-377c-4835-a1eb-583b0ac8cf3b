package com.howbuy.crm.hb.web.controller.checkauth;


import com.howbuy.crm.page.core.service.PageAuthService;
import com.howbuy.crm.page.framework.domain.User;

import crm.howbuy.base.utils.StringUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.Map;

/**
 * 检验是否有权限操作按钮
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping("/checkauth")
public class CheckAuthController {
	
	@Autowired
    private PageAuthService authService;
    
	/**
	 * 根据用户id、菜单code和按钮code检查是否有对应的按钮操作权限
	 * @param request
	 * @return
	 * @throws Exception
	 */
    @ResponseBody
    @RequestMapping("/checkAuth.do")
    public String checkAuth(HttpServletRequest request) throws Exception{
        String result = "";
        String menucode = request.getParameter("menucode");
        String optcode = request.getParameter("optcode");
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        String userid = userlogin.getUserId();
        //是否有操作权限
        Map<String,String> param = new HashMap<>(3);
        param.put("userId", userid);
        param.put("menuCode", menucode);
        param.put("optCode", optcode);
        int count = authService.getUserOptAuthCount(param);
        if(count > 0){
        	result = "success";
        }
        return result;
    }

}
