/**   
 * @Title: BatchImpFile.java 
 * @Package com.hb.crm.web.util.excel.bean 
 * @Description: TODO(用一句话描述该文件做什么) 
 * <AUTHOR>
 * @date 2016年4月28日 下午1:08:49 
 * @version V1.0   
 */

package com.howbuy.crm.hb.tools.excel.bean;

/**
 * @ClassName: BatchImpFile
 * @Description: 导入文件的属性类
 * <AUTHOR>
 * @date 2016年4月28日 下午1:08:49
 * 
 */

public class BatchImpFile {

	/**
	 * @Fields beanPorperties : 属性名称
	 */
	private String beanPorperties;
	
	/** 
	* @Fields columnLength : 字段列长度 
	*/ 
	private int columnLength;
	/** 
	* @Fields beanChineseName : 字段中文名称
	*/ 
	private  String beanChineseName;

	/**
	 * @Fields require : 字段是否为必填项
	 */
	private boolean require;

	/**
	 * @Fields checkStatus : 是否进行数据格式验证
	 */
	private boolean checkStatus = false;

	/**
	 * @Fields regex : 校验正则表达式
	 */
	private String regex;
	/**
	 * @Fields errorMessage :错误提示信息
	 */
	private String errorMessage;

	// 类型 date boolean string 格式化 验证

	/**
	 * @Fields dataFormatType :数据类型
	 */
	private String dataFormatType;
	
	/** 
	* @Fields argument :参数映射关系 
	*/ 
	private String argument;
	
	/**  
	 * @Title:  getArgument <BR>  
	 * @Description: please write your description <BR>  
	 * @return: String <BR>  
	 */
	
	public String getArgument() {
		return argument;
	}

	/**  
	 * @Title:  setArgument <BR>  
	 * @Description: please write your description <BR>  
	 * @return: String <BR>  
	 */
	
	public void setArgument(String argument) {
		this.argument = argument;
	}

	/**  
	 * @Title:  getColumnLength <BR>  
	 * @Description: please write your description <BR>  
	 * @return: int <BR>  
	 */
	
	public int getColumnLength() {
		return columnLength;
	}

	/**  
	 * @Title:  setColumnLength <BR>  
	 * @Description: please write your description <BR>  
	 * @return: int <BR>  
	 */
	
	public void setColumnLength(int columnLength) {
		this.columnLength = columnLength;
	}

	/**  
	 * @Title:  getBeanChineseName <BR>  
	 * @Description: please write your description <BR>  
	 * @return: String <BR>  
	 */
	
	public String getBeanChineseName() {
		return beanChineseName;
	}

	/**  
	 * @Title:  setBeanChineseName <BR>  
	 * @Description: please write your description <BR>  
	 * @return: String <BR>  
	 */
	
	public void setBeanChineseName(String beanChineseName) {
		this.beanChineseName = beanChineseName;
	}

	/**
	 * @Title: getDataFormatType <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public String getDataFormatType() {
		return dataFormatType;
	}

	/**
	 * @Title: setDataFormatType <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public void setDataFormatType(String dataFormatType) {
		this.dataFormatType = dataFormatType;
	}

	/**
	 * @Title: isRequire <BR>
	 * @Description: please write your description <BR>
	 * @return: boolean <BR>
	 */

	public boolean isRequire() {
		return require;
	}

	/**
	 * @Title: setRequire <BR>
	 * @Description: please write your description <BR>
	 * @return: boolean <BR>
	 */

	public void setRequire(boolean require) {
		this.require = require;
	}

	/**
	 * @Title: isCheckStatus <BR>
	 * @Description: please write your description <BR>
	 * @return: boolean <BR>
	 */

	public boolean getCheckStatus() {
		return checkStatus;
	}

	/**
	 * @Title: setCheckStatus <BR>
	 * @Description: please write your description <BR>
	 * @return: boolean <BR>
	 */

	public void setCheckStatus(boolean checkStatus) {
		this.checkStatus = checkStatus;
	}

	/**
	 * @Title: getBeanPorperties <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public String getBeanPorperties() {
		return beanPorperties;
	}

	/**
	 * @Title: setBeanPorperties <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public void setBeanPorperties(String beanPorperties) {
		this.beanPorperties = beanPorperties;
	}

	/**
	 * @Title: getRegex <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public String getRegex() {
		return regex;
	}

	/**
	 * @Title: setRegex <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public void setRegex(String regex) {
		this.regex = regex;
	}

	/**
	 * @Title: getErrorMessage <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public String getErrorMessage() {
		return errorMessage;
	}

	/**
	 * @Title: setErrorMessage <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

}
