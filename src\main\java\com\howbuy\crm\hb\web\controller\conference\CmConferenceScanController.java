package com.howbuy.crm.hb.web.controller.conference;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.base.YesOrNoEnum;
import com.howbuy.crm.base.model.BaseConstantEnum;
import com.howbuy.crm.base.model.CenterOrgEnum;
import com.howbuy.crm.consultant.dto.ConsultantSimpleInfoDto;
import com.howbuy.crm.consultant.service.ConsultantInfoService;
import com.howbuy.crm.hb.constants.MenuButtonConstant;
import com.howbuy.crm.hb.domain.conference.CmConference;
import com.howbuy.crm.hb.domain.conference.CmConferenceScan;
import com.howbuy.crm.hb.domain.conference.CmConferenceScanHandleVO;
import com.howbuy.crm.hb.request.ApplyDistributeRequest;
import com.howbuy.crm.hb.request.conferencescan.BatchSaveNewCustRequest;
import com.howbuy.crm.hb.service.callout.CsCalloutMyTaskService;
import com.howbuy.crm.hb.service.conference.CmConferenceScanService;
import com.howbuy.crm.hb.service.conference.CmConferenceService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.system.HbOrganizationService;
import com.howbuy.crm.hb.tools.excel.write.ExcelWriter;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.conference.MobileCustInfoDisplayDto;
import com.howbuy.crm.nt.base.enums.ConferCustStateEnum;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.dto.CmConferenceConscust;
import com.howbuy.crm.nt.conference.dto.CmConferenceScanDisplayDto;
import com.howbuy.crm.nt.conference.dto.MobileCustInfoDto;
import com.howbuy.crm.nt.conference.request.CmConferenceScanPageVo;
import com.howbuy.crm.nt.conference.service.CmConferenceCustService;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.trade.common.response.BaseResponse;
import crm.howbuy.base.db.PageResult;
import crm.howbuy.base.utils.MapRemoveNullUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by shucheng on 2021/6/17 17:03
 */
@Slf4j
@Controller
@RequestMapping(value = "/conferenceScan")
public class CmConferenceScanController extends BaseController {
    
    @Autowired
    private CmConferenceScanService cmConferenceScanService;
    @Autowired
    private CmConferenceCustService ntConferenceCustService;

    @Autowired
    private com.howbuy.crm.nt.conference.service.CmConferenceScanService ntScanService;

    @Autowired
    private CsCalloutMyTaskService csCalloutMyTaskService;

    @Autowired
    private ConscustService conscustService;

    @Autowired
    private HbOrganizationService hbOrganizationService;

    @Autowired
    private ConsultantInfoService consultantInfoService;

    @Autowired
    private CmConferenceService cmConferenceService;

    @RequestMapping("/listCmConferenceScan.do")
    public ModelAndView listCmConferenceScan(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();

        // "扫码参会客户"菜单code
        String menuCode = "02050404";
        // "处理"按钮权限code
        String handleNewCustCode = "2";
        boolean handleNewCustAuth = false;

        List<String> roles = getLonginRoles(request);
        for (String role : roles) {
            List<String> temp = AuthCache.getInstance().getOperListStr(role, menuCode);
            if (temp != null && temp.contains(handleNewCustCode) && !handleNewCustAuth) {
                handleNewCustAuth = true;
            }
        }
        modelAndView.addObject("handleNewCustAuth", handleNewCustAuth);
        String userId=getLoginUserId(request);
        modelAndView.addObject("userId",userId);

        //默认 开始日期默认为查询当年的第一天，如 20220101
        modelAndView.addObject("curYearStartDt",DateUtil.thisYearStart());

        //是否允许看到 客户号为空的数据 逻辑： 持有 [查所有] 、[查新客]、[查询多客户] 权限的人员，可以看到 客户号为空的数据。
        //"查所有"：扫码参会  查所有权限
        Boolean  allCustAuth=checkUserOperateAuth(MenuButtonConstant.OPT_SCAN_ALL,
                MenuButtonConstant.MENU_CONFERENCE_SCAN);
        //"查新客"：可查询到当前用户的组织架构与会议创建人相同 或是其上级部门，对应投顾客户号为空 且 通过手机号未匹配上投顾客户号的参会信息；
        Boolean  newCustAuth=checkUserOperateAuth(MenuButtonConstant.OPT_SCAN_NEW_CUST,
                MenuButtonConstant.MENU_CONFERENCE_SCAN);
        //"查询多客户"：可查询当前用户的组织架构与会议创建人相同或是其上级部门，对应投顾客户号为空 且 通过手机号匹配上投顾客户号的参会信息；
        Boolean  multiCustAuth=checkUserOperateAuth(MenuButtonConstant.OPT_SCAN_MULTI_CUST,
                MenuButtonConstant.MENU_CONFERENCE_SCAN);
        //含有任一权限，都可以看到 客户号为空的数据
        Boolean  hasNullCustAuth=Boolean.TRUE.equals(allCustAuth) || Boolean.TRUE.equals(newCustAuth)
                || Boolean.TRUE.equals(multiCustAuth);
        modelAndView.addObject("hasNullCustAuth",hasNullCustAuth);


        modelAndView.setViewName("/conference/listCmConferenceScan");
        return modelAndView;
    }

    /**
     * 参数
     * @param request
     * @return
     */
    private void buildParamMap(CmConferenceScanPageVo queryVo,HttpServletRequest request) {
//        CmConferenceScanPageVo queryVo=new CmConferenceScanPageVo();
        //通用处理，处理查询权限条件
        fillAuthVo(request,queryVo);

        //conferenceBeginDt
        String conferenceBeginDt=queryVo.getConferenceBeginDt();
        if(StringUtil.isNotBlank(conferenceBeginDt)){
            queryVo.setConferenceBeginDt(String.join("",conferenceBeginDt,"000000"));
        }
        //conferenceEndDt
        String conferenceEndDt=queryVo.getConferenceEndDt();
        if(StringUtil.isNotBlank(conferenceEndDt)){
            queryVo.setConferenceEndDt(String.join("",conferenceEndDt,"235959"));
        }

       //custState :是否匹配投顾客户号 1-是 0-否 特殊处理
        String custState=queryVo.getCustState();
        if(YesOrNoEnum.YES.getCode().equals(custState)){
            //是否匹配投顾客户号 1-是 . 包括： 手机号码只有一个客户，或者手机号码有多个客户
            queryVo.setCustStateList(Lists.newArrayList(ConferCustStateEnum.EXIST_MANY.getCode(),ConferCustStateEnum.EXIST_ONLY.getCode()));
        }else if(YesOrNoEnum.NO.getCode().equals(custState)){
            //是否匹配投顾客户号 0-否 . 包括： 手机号码没有客户
            queryVo.setCustStateList(Lists.newArrayList(ConferCustStateEnum.NOT_EXIST.getCode()));
        }
        //使用 list
        queryVo.setCustState(null);


        //"查所有"：扫码参会  查所有权限
        Boolean  allCustAuth=checkUserOperateAuth(MenuButtonConstant.OPT_SCAN_ALL,
                MenuButtonConstant.MENU_CONFERENCE_SCAN);
        //"查新客"：可查询到当前用户的组织架构与会议创建人相同 或是其上级部门，对应投顾客户号为空 且 通过手机号未匹配上投顾客户号的参会信息；
        Boolean  newCustAuth=checkUserOperateAuth(MenuButtonConstant.OPT_SCAN_NEW_CUST,
                MenuButtonConstant.MENU_CONFERENCE_SCAN);
        //"查询多客户"：可查询当前用户的组织架构与会议创建人相同或是其上级部门，对应投顾客户号为空 且 通过手机号匹配上投顾客户号的参会信息；
        Boolean  multiCustAuth=checkUserOperateAuth(MenuButtonConstant.OPT_SCAN_MULTI_CUST,
                MenuButtonConstant.MENU_CONFERENCE_SCAN);

        //没有持有 [查所有] 权限的话。
        if(! Boolean.TRUE.equals(allCustAuth)) {
            //当前登录人员，如果为IC/HBC 则 只能查看 创建人所属中心=对应的IC/HBC 的 会议数据
            ConsultantSimpleInfoDto  simpleInfoDto=consultantInfoService.getInfoByconsCode(getLoginUserId());
            String centerOrgCode=simpleInfoDto.getCenterOrgCode();
            if(CenterOrgEnum.getIcHbcList().contains(centerOrgCode)){
                List<String>  createOrgList=ConsOrgCache.getInstance().getAllOrgSubsMap().get(centerOrgCode);
                log.info("根据orgCode:{},获取 subOrgCode List:{}",centerOrgCode, JSONObject.toJSONString(createOrgList));
                queryVo.setCreateOrgList(Lists.partition(createOrgList,1000));
            }

            //当前登录人员
            User loginUser=getLoginUser(request);
            //当前登录人员所属部门
            String loginOrgCode=loginUser.getOutletcode();

            //只作用于：客户号为空的数据上面。 规范 无客户的数据，用权限来控制，
            boolean hasNullDataAuth= Boolean.TRUE.equals(newCustAuth) || Boolean.TRUE.equals(multiCustAuth);
            //查新客 查多客 持有任一权限
            if(hasNullDataAuth){
                List<String> createOrgSubList=ConsOrgCache.getInstance().getAllOrgSubsMap().get(loginOrgCode);
                queryVo.setNullCustCreateOrgList(Lists.partition(createOrgSubList,1000));
                List<String> speStateList=Lists.newArrayList();
                if (Boolean.TRUE.equals(newCustAuth)){
                    speStateList.add(ConferCustStateEnum.NOT_EXIST.getCode());
                }
                if(Boolean.TRUE.equals(multiCustAuth)){
                    speStateList.add(ConferCustStateEnum.EXIST_MANY.getCode());
                }
                queryVo.setNullCustStateList(speStateList);

            }

            log.info("当前登录人员：{}，登录人员部门：{}，查所有权限：{}，查新客权限：{}，查询多客户权限：{}, 查询vo:{}",
                    loginUser.getUserId(),loginOrgCode,allCustAuth,
                    newCustAuth,multiCustAuth,JSON.toJSONString(queryVo));


        }

    }




    @RequestMapping("/listCmConferenceScanByPage.do")
    @ResponseBody
    public PageResult<CmConferenceScanDisplayDto>  listCmConferenceScanByPage(CmConferenceScanPageVo queryVo,HttpServletRequest request) {

        buildParamMap(queryVo,request);
        log.info("查询扫码参会客户，参数vo信息为：{}",JSON.toJSONString(queryVo));
        PageResult<CmConferenceScanDisplayDto> page= ntScanService.selectPageByPageVo(queryVo);

        //翻译  部门名称
        ConsOrgCache orgcache = ConsOrgCache.getInstance();
        Map<String, String> cons2OutletMap = orgcache.getCons2OutletMap();
        Map<String, String> allOrgMap = orgcache.getAllOrgMap();
        Map<String, String> upOrgMap=orgcache.getUpOrgMapCache();

        page.getRows().forEach(scan->fillScanInfo(scan,cons2OutletMap,allOrgMap,upOrgMap));

        return page;
    }


    /**
     * @api {GET} /conferenceScan/toapplydistribute 跳转到申请分配的页面
     * @apiVersion 1.0.0
     * @apiGroup CmConferenceScanController
     * @apiName toapplydistribute()
     * @apiDescription 跳转到申请分配的页面
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":true,"status":"FORBIDDEN"}
     */
    @GetMapping("/toapplydistribute")
    public ModelAndView toapplydistribute(HttpServletRequest request) {
        // 扫码参会客户主键
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/conference/applydistribute");
        modelAndView.addObject("scanIds", request.getParameter("scanIds"));
        modelAndView.addObject("custNos", request.getParameter("custNos"));
        return modelAndView;
    }


    /**
     * @api {POST} /conferenceScan/applycancel applycancel()
     * @apiVersion 1.0.0
     * @apiGroup CmConferenceScanController
     * @apiName applycancel()
     * @apiDescription 申请取消参会
     * @apiSuccess (响应结果) {Boolean} success
     * @apiSuccess (响应结果) {String} data
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"22We","returnMsg":"cbNlq","data":"2","success":true}
     */
    @PostMapping("/applycancel")
    @ResponseBody
    public BaseResponse<String> applycancel(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute("loginUser");
        try {
            List<String> stringList = Arrays.asList(request.getParameterValues("scanIDs[]"));
            List<BigDecimal> scanIdList = stringList.stream()
                    .map(BigDecimal::new)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(scanIdList)) {
                ntScanService.batchCancel(scanIdList, user.getUserId());
            }
            return BaseResponse.ok();
        } catch (Exception e) {
            log.error("error in applycancel", e);
            return BaseResponse.fail("申请取消参会失败");
        }
    }


    /**
     * @api {POST} /conferenceScan/applydistribute 申请分配获取选中的数据
     * @apiVersion 1.0.0
     * @apiGroup CmConferenceScanController
     * @apiName applyDistribute()
     * @apiDescription 申请分配获取选中的数据
     * @apiParam (请求体) {Array} requestBody
     * @apiParam (请求体) {String} requestBody.scanId
     * @apiParam (请求体) {String} requestBody.custNo
     * @apiParamExample 请求体示例
     * [{"custNo":"uhJ","scanId":"XSaX"}]
     * @apiSuccess (响应结果) {Array} response
     * @apiSuccess (响应结果) {String} response.id 主键
     * @apiSuccess (响应结果) {String} response.conferenceId 会议主键(外键)
     * @apiSuccess (响应结果) {String} response.conferenceName 会议名称
     * @apiSuccess (响应结果) {String} response.mobile 手机号
     * @apiSuccess (响应结果) {String} response.mobileMask
     * @apiSuccess (响应结果) {String} response.mobileDigest
     * @apiSuccess (响应结果) {String} response.mobileCipher
     * @apiSuccess (响应结果) {String} response.credt 记录创建日期
     * @apiSuccess (响应结果) {String} response.creator 创建人
     * @apiSuccess (响应结果) {String} response.conscustno
     * @apiSuccess (响应结果) {String} response.custname
     * @apiSuccess (响应结果) {String} response.consname
     * @apiSuccess (响应结果) {String} response.conscode
     * @apiSuccess (响应结果) {String} response.isconspre 是否投顾预约参会
     * @apiSuccess (响应结果) {Number} response.appointmentsnub 预约参会人数
     * @apiSuccess (响应结果) {String} response.uporgname 所属区域
     * @apiSuccess (响应结果) {String} response.orgname 所属部门
     * @apiSuccess (响应结果) {String} response.custnamesr 客户姓名(输入)
     * @apiSuccess (响应结果) {String} response.consnamesr 投顾姓名(输入)
     * @apiSuccess (响应结果) {Number} response.meetingnumber 实际人数(输入)
     * @apiSuccessExample 响应结果示例
     * [{"appointmentsnub":7467,"creator":"f1PWoZV7","conferenceName":"RVDF","consnamesr":"x","isconspre":"Wod3luE6s","mobile":"tOX45Zy4Fa","meetingnumber":8578,"mobileCipher":"Kc3","credt":"EM5Wq","uporgname":"Mz99Q","conscustno":"R8","orgname":"UAa3JpuCw","conferenceId":"V","mobileDigest":"oZuLA","consname":"QCW75","id":"Tx1","custnamesr":"8SYKIG2Kxr","custname":"HI2Ufeesn","conscode":"LqBe","mobileMask":"zCSIvGs4"}]
     */
    @PostMapping("/applydistribute")
    @ResponseBody
    public List<CmConferenceScan> applyDistribute(@RequestBody List<ApplyDistributeRequest> request) throws Exception {
        return cmConferenceScanService.listCmConferenceScanByScanIds(request);
    }


   /**
    * @description:(补充翻译信息)
    * @param scanDisplayDto	 待翻译对象
    * @param cons2OutletMap	 投顾客户号对应的部门
    * @param allOrgMap	 部门信息
    * @param upOrgMap 上级部门信息
    * @return void
    * @author: haoran.zhang
    * @date: 2023/11/21 14:44
    * @since JDK 1.8
    */
    private void fillScanInfo(CmConferenceScanDisplayDto scanDisplayDto,
                              Map<String, String> cons2OutletMap,
                              Map<String, String> allOrgMap,
                              Map<String, String> upOrgMap){
        String consCode=scanDisplayDto.getConsCode();
        if (StringUtils.isEmpty(consCode)) {
            return;
        }
        String orgCode=cons2OutletMap.get(consCode);
        String orgName=allOrgMap.get(orgCode);
        scanDisplayDto.setOrgName(orgName);

        String uporgcode = upOrgMap.get(orgCode);
        String districtName = "";
        if("0".equals(uporgcode)){
            districtName=orgName;
        }else{
            districtName=allOrgMap.get(uporgcode);
        }
        scanDisplayDto.setDistrictName(districtName);
        return;
        }

    /**
     * 导出-参会信息
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/expConferenceScan.do")
    public void expConferenceScan(CmConferenceScanPageVo queryVo,
                                  HttpServletRequest request,
                                  HttpServletResponse response){

        buildParamMap(queryVo,request);
        queryVo.setPage(1);
        queryVo.setRows(50000);
        log.info("导出扫码参会客户，参数vo信息为：{}",JSON.toJSONString(queryVo));
        PageResult<CmConferenceScanDisplayDto> page= ntScanService.selectPageByPageVo(queryVo);

        //翻译  部门名称
        ConsOrgCache orgcache = ConsOrgCache.getInstance();
        Map<String, String> cons2OutletMap = orgcache.getCons2OutletMap();
        Map<String, String> allOrgMap = orgcache.getAllOrgMap();
        Map<String, String> upOrgMap=orgcache.getUpOrgMapCache();

        //导出数据列表
        List<Map> valueMapList= Lists.newArrayList();

        page.getRows().forEach(scan->{
            fillScanInfo(scan,cons2OutletMap,allOrgMap,upOrgMap);
            //翻译字典值
            scan.setAppoint(YesOrNoEnum.getDescription(scan.getAppoint()));

            //转换为Map
            Map<String, Object> valueMap = (JSONObject) JSON.toJSON(scan);
            //替换  creDt
            valueMap.put("credtStr", DateUtil.date2String(scan.getCredt(),DateUtil.DEFAULT_DATESFM));
            valueMapList.add(valueMap);
        });

        try {
            // 清空输出流
            response.reset();
            String fileName="扫码参会客户列表_"+ DateUtil.date2String(new Date(),DateUtil.STR_PATTERN)+".xls";
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String [] columnName = new String[] {"会议ID","会议名称","参会手机号","手机号摘要","签到时间","投顾客户号","客户姓名",
                    "所属投顾","所属部门","所属区域","客户姓名(输入)","投顾姓名(输入)","是否预约参会","预约参会人数","实际人数(输入)"};

            String [] beanProperty = new String[]{"conferenceid","conferenceName","mobileMask","mobileDigest","credtStr","custNo","relatedCustName",
                    "relatedConsName","orgName","districtName","custname","consname","appoint","appointmentsNub","meetingnumber"};
            ExcelWriter.writeExcel(os, "扫码参会客户列表", 0, valueMapList, columnName, beanProperty);
            os.close(); // 关闭流
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }

    }



    /**
     * 显示"添加客户页面"
     * @param id 扫码参会表的主键
     * @return
     */
    @RequestMapping("/showAddNewCustPage.do")
    public String showAddNewCustPage(HttpServletRequest request, String id) {
        // 扫码参会客户主键
        request.setAttribute("scanId", id);
        CmConferenceScan cmConferenceScan = cmConferenceScanService.findCmConferenceScanById(id);
        request.setAttribute("cmConferenceScan", cmConferenceScan);
        // 根据会议id获取对应会议的数据 (省市区)
        if (!StringUtil.isEmpty(cmConferenceScan.getConferenceId())) {
            request.setAttribute("conferenceId", cmConferenceScan.getConferenceId());
            CmConference cmConference = cmConferenceService.queryCmConferenceInfo(cmConferenceScan.getConferenceId());
            request.setAttribute("provCode", cmConference.getProvcode());
            request.setAttribute("cityCode", cmConference.getCitycode());
        }


        // 生成投顾客户号
        String consCustNo = csCalloutMyTaskService.getConsCustNo();
        request.setAttribute("consCustNo", consCustNo);

        //获取产品列表
        ConstantCache c = ConstantCache.getInstance();
        LinkedHashMap<String, String> map = c.getConstantKeyVal("consultType");
        TreeMap<String, String> tm = new TreeMap<>(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return Integer.parseInt(o1) - Integer.parseInt(o2);
            }
        });
//        for (String key : map.keySet()) {
//            tm.put(key, map.get(key));
//        }
        
        for (Map.Entry<String, String> entry : map.entrySet()) {
            tm.put(entry.getKey(), entry.getValue());
        }
        request.setAttribute("consultTypes", tm);

        HttpSession session = request.getSession();
        String userId = (String) session.getAttribute("userId");
        boolean custSourceType = conscustService.getcustSourceType(userId);
        request.setAttribute("custSourceType", custSourceType);

        return "/conference/addNewCust";
    }


    /**
     * @api {POST} /conferenceScan/ismatch isMatch()
     * @apiVersion 1.0.0
     * @apiGroup CmConferenceScanController
     * @apiName isMatch()
     * @apiDescription 查询是否有客户信息匹配上
     * @apiParam (请求体) {String} requestBody
     * @apiParamExample 请求体示例
     * "gL4Wf"
     * @apiSuccess (响应结果) {Boolean} success
     * @apiSuccess (响应结果) {String} data
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"IBZTX","returnMsg":"OOpA3zYMS","data":"R","success":true}
     */
    @ResponseBody
    @PostMapping(value = "/ismatch")
    public BaseResponse<String> isMatch(HttpServletRequest request) {
        return BaseResponse.ok(String.valueOf(cmConferenceScanService.getCmConscustByScanMobile(request.getParameter("id"))));
    }


    /**
     * @api {POST} /conferenceScan/validatebeforebatchhandle validateBeforeBatchHandle()
     * @apiVersion 1.0.0
     * @apiGroup CmConferenceScanController
     * @apiName validateBeforeBatchHandle()
     * @apiDescription 前缀校验：批量处理
     * @apiSuccess (响应结果) {Boolean} success
     * @apiSuccess (响应结果) {String} data
     * @apiSuccess (响应结果) {Array} responseList
     * @apiSuccess (响应结果) {String} responseList.key
     * @apiSuccess (响应结果) {String} responseList.desc
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"GbPB","returnMsg":"7pXSyTgf5","data":"p","success":true,"responseList":[{"key":"3HZt","desc":"P"}]}
     */
    @ResponseBody
    @PostMapping(value = "/validatebeforebatchhandle")
    public BaseResponse<String> validateBeforeBatchHandle(HttpServletRequest request) {
        String ids = request.getParameter("ids");
        if (StringUtil.isEmpty(ids)) {
            return BaseResponse.ok("请选择要操作的数据");
        }
        String[] idArray = ids.split(",");

        return cmConferenceScanService.validateBeforeBatchHandle(Lists.newArrayList(idArray));
    }


    /**
     * @api {GET} /conferenceScan/batchhandle batchHandle()
     * @apiVersion 1.0.0
     * @apiGroup CmConferenceScanController
     * @apiName batchHandle()
     * @apiDescription 页面跳转：批量处理
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":false,"status":"PARTIAL_CONTENT"}
     */
    @GetMapping(value = "/batchhandle")
    public ModelAndView batchHandle(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/conference/conferencescan/batchHandleScan");

        String idsStr = request.getParameter("ids");
        if (StringUtil.isEmpty(idsStr)) {
            return modelAndView;
        }
        String[] idArray = idsStr.split(",");
        List<String> ids = Lists.newArrayList(idArray);

        BaseResponse<String> validateResponse = cmConferenceScanService.validateBeforeBatchHandle(ids);
        if (!validateResponse.isSuccess()) {
            return modelAndView;
        }

        List<CmConferenceScanHandleVO> cmConferenceScanHandleVOS = cmConferenceScanService.listBatchHandleVOByIds(ids);
        modelAndView.addObject("handleList", cmConferenceScanHandleVOS);

        String userId = (String) request.getSession().getAttribute("userId");
        boolean custSourceType = conscustService.getcustSourceType(userId);
        modelAndView.addObject("custSourceType", custSourceType);

        return modelAndView;
    }

    /**
     * @api {POST} /conferenceScan/batchsavenewcust batchSaveNewCust()
     * @apiVersion 1.0.0
     * @apiGroup CmConferenceScanController
     * @apiName batchSaveNewCust()
     * @apiDescription 处理：批量新增客户
     * @apiParam (请求体) {Array} rows
     * @apiParam (请求体) {String} rows.scanId 扫描id
     * @apiParam (请求体) {String} rows.custName 新增客户姓名
     * @apiParam (请求体) {String} rows.custSource 新增客户来源
     * @apiParam (请求体) {String} rows.consCode 新增客户所属的投顾
     * @apiParam (请求体) {String} rows.provinceCode 新增客户所属省份
     * @apiParam (请求体) {String} rows.cityCode 新增客户所属城市
     * @apiParamExample 请求体示例
     * {"rows":[{"custSource":"fEsPEarI","scanId":"Twpylh8e","provinceCode":"CD9l","cityCode":"bOJ3x","custName":"lFmbE","consCode":"UQkjmmb6t"}]}
     * @apiSuccess (响应结果) {Boolean} success
     * @apiSuccess (响应结果) {Array} data
     * @apiSuccess (响应结果) {Array} responseList
     * @apiSuccess (响应结果) {String} responseList.key
     * @apiSuccess (响应结果) {String} responseList.desc
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"75pEZ6","returnMsg":"QIPoW","data":["78"],"success":false,"responseList":[{"key":"S","desc":"jW4vQx"}]}
     */
    @ResponseBody
    @RequestMapping("/batchsavenewcust")
    public BaseResponse<List<String>> batchSaveNewCust(HttpServletRequest request,
                                                 @RequestBody BatchSaveNewCustRequest batchSaveRequest) {
        BaseResponse<List<String>> response;
        try {
            log.info("CmConferenceScanController.batchSaveNewCust:" + JSON.toJSONString(batchSaveRequest));
            response = cmConferenceScanService.batchSaveNewCust(batchSaveRequest, getLoginUserId(request));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BaseResponse.fail(BaseConstantEnum.UNKNOWN_ERROR.getCode(),
                    new ArrayList<>());

        }
        return response;
    }

    /**
     * 新增客户操作(商学院扫码新增客户信息)
     */
    @ResponseBody
    @RequestMapping("/saveNewCust")
    public Map<String, Object> saveNewCust(HttpServletRequest request, @RequestParam Map<String, String> pageParam) {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // 移除map中值为空的元素
            MapRemoveNullUtil.removeNullValue(pageParam);
            // 当前登录用户
            pageParam.put("userId", getLoginUserId(request));
            log.info("CmConferenceScanController.saveNewCust:"+JSON.toJSONString(pageParam));
            resultMap = cmConferenceScanService.saveNewCust(pageParam);
        } catch (Exception e) {
            resultMap.put("errorCode", BaseConstantEnum.UNKNOWN_ERROR.getCode());
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }


    /**
     * @api {GET} /conferenceScan/tomultiplecustview toMultipleCustView()
     * @apiVersion 1.0.0
     * @apiGroup CmConferenceScanController
     * @apiName toMultipleCustView()
     * @apiDescription 跳转到 查询客户 页面
     * @apiParam (请求参数) {String} scanId 扫码参会id
     * @apiParam (请求参数) {String} mobileDigest 手机号码
     * @apiParamExample 请求参数示例
     * scanId=Uo&mobileDigest=wDniCzTwQ
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":true,"status":"BAD_GATEWAY"}
     */
    @RequestMapping("/tomultiplecustview")
    public ModelAndView toMultipleCustView(String scanId,String mobileDigest) {
        // 扫码参会客户主键
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/conference/multiCustView");
        List<MobileCustInfoDto>  custList=ntScanService.listExistCustList(mobileDigest);

        List<MobileCustInfoDisplayDto>  displayList=Lists.newArrayList();

        if(CollectionUtils.isNotEmpty(custList)){
            //获取是否有参会预约的标识
            com.howbuy.crm.nt.conference.dto.CmConferenceScan  scanInfo=ntScanService.selectByScanId(scanId);
            //查找预约数据
            List<String> custNoList=custList.stream().map(MobileCustInfoDto::getCustNo).collect(Collectors.toList());
            List<CmConferenceConscust> appointCustList=ntConferenceCustService.selectCustList(scanInfo.getConferenceid(),Lists.partition(custNoList,1000));
            Map<String, CmConferenceConscust> appointCustMap=appointCustList.stream().collect(Collectors.toMap(CmConferenceConscust::getConscustno, a -> a,(k1, k2)->k1));

            ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
            Map<String, String> allUserMap=consOrgCache.getAllUserMap();
            Map<String, String> allOrgMap=consOrgCache.getAllOrgMap();
            Map<String, String> upOrgMap=consOrgCache.getUpOrgMapCache();
            Map<String, String> cons2OutletMap=consOrgCache.getCons2OutletMap();
            custList.forEach(cust->{
                String consCode=cust.getConsCode();
                MobileCustInfoDisplayDto displayDto=new MobileCustInfoDisplayDto();
                displayDto.setCustNo(cust.getCustNo());
                displayDto.setCustName(cust.getCustName());
                displayDto.setConsCode(consCode);
                displayDto.setExistsAppointFlag(cust.getExistsAppointFlag());


                //所属部门
                displayDto.setOrgName(allOrgMap.get(cons2OutletMap.get(consCode)));
                //所属投顾
                displayDto.setConsName(allUserMap.get(consCode));
                //所属区域
                String regionCode = upOrgMap.get(cons2OutletMap.get(consCode));
                if("0".equals(regionCode)){
                    displayDto.setRegionName(displayDto.getOrgName());
                }else{
                    displayDto.setRegionName(allOrgMap.get(regionCode));
                }
                //是有有预约数据
                if(appointCustMap.containsKey(cust.getCustNo())) {
                    displayDto.setExistsAppointFlag(YesOrNoEnum.YES.getCode());
                }else{
                    displayDto.setExistsAppointFlag(YesOrNoEnum.NO.getCode());
                }
                displayList.add(displayDto);
            });
        }
        modelAndView.addObject("scanId", scanId);
        modelAndView.addObject("mobileDigest", mobileDigest);
        modelAndView.addObject("custList", displayList);
        return modelAndView;
    }


    /**
     * @api {GET} /conferenceScan/querymultiplecust queryMultipleCust()
     * @apiVersion 1.0.0
     * @apiGroup CmConferenceScanController
     * @apiName queryMultipleCust()
     * @apiDescription 根据 扫码参会id 和 手机号码 查询 多个客户的列表信息
     * @apiParam (请求参数) {String} scanId 扫码参会id
     * @apiParam (请求参数) {String} mobileDigest 手机号码
     * @apiParamExample 请求参数示例
     * scanId=JQy8qPOr42&mobileDigest=cD7
     * @apiSuccess (响应结果) {Array} response
     * @apiSuccess (响应结果) {String} response.custNo 客户号
     * @apiSuccess (响应结果) {String} response.custName 客户名称
     * @apiSuccess (响应结果) {String} response.consCode 所属投顾
     * @apiSuccess (响应结果) {String} response.existsAppointFlag 该客户是否有该会议的参会预约
     * @apiSuccessExample 响应结果示例
     * [{"custNo":"AhCkC6f","custName":"o","consCode":"LTzs3P","existsAppointFlag":"E"}]
     */
    @ResponseBody
    @RequestMapping("/querymultiplecust")
    public List<MobileCustInfoDto> queryMultipleCust(String scanId,String mobileDigest){
        List<MobileCustInfoDto>  infoList=ntScanService.listExistCustList(mobileDigest);
        return infoList;
    }

    /**
     * @api {GET} /conferenceScan/executespecifycustNo executeSpecifyCustNo()
     * @apiVersion 1.0.0
     * @apiGroup CmConferenceScanController
     * @apiName executeSpecifyCustNo()
     * @apiDescription 指定 scanId的 扫码参会表数据 ，为固定的客户号
     * @apiParam (请求参数) {String} scanId 扫码参会表id
     * @apiParam (请求参数) {String} custNo 指定客户号
     * @apiParamExample 请求参数示例
     * custNo=jdx&scanId=bOqHHI
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"mp2uh8eH","returnMsg":"4mCt8d9","returnObject":"l7","returnList":["Mdv"]}
     */
    @ResponseBody
    @RequestMapping("/executespecifycustNo")
    public NtReturnMessageDto<String> executeSpecifyCustNo(String scanId,String custNo){
         return ntScanService.executeSpecifyCustNo(new BigDecimal(scanId),custNo,getLoginUserId());
    }
}
