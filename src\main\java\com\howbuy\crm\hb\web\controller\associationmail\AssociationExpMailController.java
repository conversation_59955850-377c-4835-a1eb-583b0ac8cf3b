package com.howbuy.crm.hb.web.controller.associationmail;

import com.howbuy.crm.hb.domain.associationmail.AssociationExpMail;
import com.howbuy.crm.hb.domain.associationmail.AssociationExpOverRideMail;
import com.howbuy.crm.hb.domain.associationmail.AssociationMail;
import com.howbuy.crm.hb.service.associationmail.AssociationExpMailService;
import com.howbuy.crm.hb.web.util.ResultCode;
import com.howbuy.crm.nt.associationmail.model.AssociationMailConstant;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 协会邮件-异常解析
 * Created by shucheng on 2021/6/3 14:52
 */
@Slf4j
@Controller
@RequestMapping("/associationExpMail")
public class AssociationExpMailController {
    
    @Autowired
    private AssociationExpMailService associationExpMailService;

    @RequestMapping(value="/listAssociationExpMail.do",method=RequestMethod.GET)
    public ModelAndView listAssociationExpMail() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/associationmail/listAssociationExpMail");
        return modelAndView;
    }

    @RequestMapping("/listAssociationExpMailByPage.do")
    @ResponseBody
    public Map<String, Object> listAssociationExpMailByPage(HttpServletRequest request) throws Exception {
        Map<String, String> param = new ParamUtil(request).getParamMap();
        String expStatus = param.get("expStatus");
        String handleStatus = param.get("handleStatus");
        if (expStatus == null) {//默认全部
            param.put("expStatus", "9");
        }
        if (handleStatus == null) {//默认待处理
            param.put("handleStatus", "0");
        }
        PageData<AssociationExpMail> pageData = associationExpMailService.listAssociationExpMailByPage(param);
        // 返回查询结果
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        resultMap.put("rows", pageData.getListData());
        return resultMap;
    }

    /**
     * 显示修改页面
     * @return
     */
    @RequestMapping("/editAssociationExpMail.do")
    public ModelAndView editAssociationExpMail(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/associationmail/editAssociationExpMail");

        String id = request.getParameter("id");
        AssociationExpMail associationExpMail = associationExpMailService.findAssociationExpMailById(id);
        modelAndView.addObject("associationExpMail", associationExpMail);
        return modelAndView;
    }

    /**
     * 显示查看页面
     * @return
     */
    @RequestMapping("/viewAssociationExpMail.do")
    public ModelAndView viewAssociationExpMail(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/associationmail/viewAssociationExpMail");

        String id = request.getParameter("id");
        AssociationExpMail associationExpMail = associationExpMailService.findAssociationExpMailById(id);
        modelAndView.addObject("associationExpMail", associationExpMail);

        String expStatus = associationExpMail.getExpStatus();
        // 重复邮件
        if (AssociationMailConstant.ExpStatusEnum.REPEAT_MAIL.getCode().equals(expStatus)) {
            // 获取重复邮件关联的正常邮件信息
//            AssociationMail associationNormalMail = associationExpMailService.findNormalMailFromRepeatMail(associationExpMail.getCustName(), associationExpMail.getOrgName(),
//                    associationExpMail.getInvestorAccount(), associationExpMail.getManagerRegno());
//            modelAndView.addObject("associationNormalMail", associationNormalMail);
            //获取重复邮件的覆盖历史
            List<AssociationExpOverRideMail> list = associationExpMailService.findOverRideMailFromRepeatMail(id);
            modelAndView.addObject("associationExpOverRideMailList", list);
        }
        return modelAndView;
    }

    /**
     * 修改异常解析数据
     * @param request
     * @return
     */
    @RequestMapping("/update.do")
    @ResponseBody
    public Map<String, Object> save(HttpServletRequest request, AssociationExpMail associationExpMail) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            associationExpMailService.updateAssociationExpMail(associationExpMail);
            resultMap.put("errorCode", ResultCode.RESULT_CODE_SUCCESS);
            resultMap.put("errorMsg", "操作成功");
        } catch (Exception e) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "操作失败");
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }

    /**
     * 更新至正常表
     * @param id
     * @return
     */
    @RequestMapping("/updateToNormalMail.do")
    @ResponseBody
    public Map<String, Object> updateToNormalMail(String id) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            resultMap = associationExpMailService.updateToNormalMail(id);
        } catch (Exception e) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "操作失败");
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }

    /**
     * 批量更新至正常表
     * @param idsStr
     * @return
     */
    @RequestMapping("/batchUpdateToNormalMail.do")
    @ResponseBody
    public Map<String, Object> batchUpdateToNormalMail(String idsStr) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            String[] idsArr = StringUtils.split(idsStr, ",");
            resultMap = associationExpMailService.batchUpdateToNormalMail(idsArr);
        } catch (Exception e) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "操作失败");
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }

    /**
     * 导出数据
     * @param request
     * @param response
     */
    @RequestMapping("/exportAssociationExpMail.do")
    public void exportAssociationMail(HttpServletRequest request, HttpServletResponse response) {
        ServletOutputStream os = null;
        try {
            Map<String, String> param = new ParamUtil(request).getParamMap();
            List<AssociationExpMail> associationExpMailList = associationExpMailService.listAssociationExpMail(param);

            // 导出数据
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            String fileName = "协会解析数据_异常解析_" + DateTimeUtil.getCurDateTime() + ".xls";
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));

            String [] columnName = new String []{
                    "ID","邮件日期","记录创建日期","发件人邮箱","投顾客户号","客户姓名","机构名称","投资者账号","初始密码",
                    "管理人登记编码","登录链接","创建人","修改日期","修改人","邮件主题","收件人邮箱","删除标志","邮件uid","处理状态","异常状态","备注"
            };

            String [] beanProperty = new String []{
                    "id","mailDateStr","credt","sourceMail","conscustno","custName","orgName","investorAccount","initPassword",
                    "managerRegno","loginHref","creator","moddt","modifier","subject","toMail","delFlag","mailUid","handleStatus","expStatus","remark"
            };
            os = response.getOutputStream();
            ExcelWriter.writeExcel(os, "协会解析数据_异常解析", 0, associationExpMailList, columnName, beanProperty);
        } catch (Exception e) {
            log.error("文件导出异常", e);
        } finally {
            // 关闭流
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 批量修改处理状态
     * @param request
     * @param idsStr 待修改记录id的集合
     * @param associationExpMail
     * @return
     */
    @RequestMapping("/batchUpdateHandleStatus.do")
    @ResponseBody
    public Map<String, Object> batchUpdateHandleStatus(HttpServletRequest request, String idsStr, AssociationExpMail associationExpMail) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            String[] idArr = idsStr.split(",");
            User user = (User)request.getSession().getAttribute("loginUser");
            if (ArrayUtils.isNotEmpty(idArr)) {
                associationExpMailService.batchUpdateHandleStatusRemark(Arrays.asList(idArr), associationExpMail.getHandleStatus(),
                        user.getUserId(), associationExpMail.getRemark());
            } else {
                throw new RuntimeException();
            }
            resultMap.put("errorCode", ResultCode.RESULT_CODE_SUCCESS);
            resultMap.put("errorMsg", "操作成功");
        } catch (Exception e) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "操作失败");
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }
}
