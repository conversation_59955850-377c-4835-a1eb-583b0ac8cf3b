package com.howbuy.crm.hb.web.controller.insur;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.howbuy.crm.hb.domain.insur.CmBxCompany;
import com.howbuy.crm.hb.domain.insur.CmBxProduct;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.insur.CmBxChannelService;
import com.howbuy.crm.hb.service.insur.CmBxCompanyService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.db.PageData;

/**
 * @Description: Controller
 * @version 1.0
 */
@Slf4j
@Controller
@RequestMapping(value = "/cmBxCompany")
public class CmBxCompanyController {
	@Autowired
	private CmBxCompanyService cmBxCompanyService;
	
	@Autowired
	private CmBxChannelService cmBxChannelService;
	
	@Autowired
    private CommonService commonService;
	
	@RequestMapping("/listCmBxCompany.do")
	public ModelAndView listCmBxCompany(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();        
        modelAndView.setViewName("/insur/listCmBxCompany");
        return modelAndView;
	}
	
	/**
	 * 加载列表页面数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listCmBxCompanyByPage.do")
	public Map<String, Object> listCmBxCompanyByPage(HttpServletRequest request)	throws Exception {
		Map<String, String> param = new ParamUtil(request).getParamMap();
		param.put("isdel", "1");
		PageData<CmBxCompany> pageData = cmBxCompanyService.listCmBxCompanyByPage(param);

		// 返回查询结果
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmBxCompany> listdata = pageData.getListData();
		resultMap.put("rows", listdata);
		
		return resultMap;
	}
	
	
	
	/**
	 * 更新
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/updateCmBxCompany", method = RequestMethod.POST)
	@ResponseBody
    public Map<String, Object> updateCmBxCompany(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String id = request.getParameter("id");
		String compname = request.getParameter("compname");
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        User user = (User) request.getSession().getAttribute("loginUser");

        if (!StringUtils.isEmpty(id)) {
        	CmBxCompany cmBxCompany = new CmBxCompany();
        	cmBxCompany.setId(new BigDecimal(id));
        	cmBxCompany.setCompname(compname);
        	cmBxCompany.setModifier(user.getUserId());
        	cmBxCompany.setModifydt(new Date());
        	cmBxCompanyService.updateCmBxCompany(cmBxCompany);
        }
        return resultMap;
    }
	
	
	/**
	 * 删除
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/delCmBxCompany", method = RequestMethod.POST)
	@ResponseBody
    public Map<String, Object> delCmBxCompany(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        User user = (User) request.getSession().getAttribute("loginUser");

        String id = request.getParameter("id");
        String compcode = request.getParameter("compcode");
        if (StringUtils.isNotEmpty(id) && StringUtils.isNotBlank(compcode)) {
        	Map<String,String> param = new HashMap<String,String>();
        	param.put("compcode", compcode);
        	List<CmBxProduct>  listCmBxProduct = cmBxChannelService.listUsedCmBxProduct(param);
        	//校验产品信息是否已使用此数据:已经使用，不允许删除
        	if(CollectionUtils.isEmpty(listCmBxProduct)){
        		CmBxCompany cmBxCompany = new CmBxCompany();
            	cmBxCompany.setId(new BigDecimal(id));
            	cmBxCompany.setIsdel("0");//删除
            	cmBxCompany.setModifier(user.getUserId());
            	cmBxCompany.setModifydt(new Date());
            	cmBxCompanyService.updateCmBxCompany(cmBxCompany);
        	}else{
        		resultMap.put("errorMsg", "数据已使用，不允许删除");
                resultMap.put("errorCode", "9999");
        	}
        }
        return resultMap;
    }
	
	
	/**
	 * 新增
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/insertCmBxCompany", method = RequestMethod.POST)
	@ResponseBody
    public  Map<String, Object> insertCmBxCompany(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
		String compcode = request.getParameter("compcode");
		String compname = request.getParameter("compname");
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        
        if(compcode != null && (!compcode.matches("^[0-9a-zA-Z]{1,15}$"))){
        	resultMap.put("errorMsg", "保险公司代码必须为数字或字母！");
            resultMap.put("errorCode", "9999");
            return resultMap;
		}
        
        User user = (User) request.getSession().getAttribute("loginUser");
        Map<String,String> param = new HashMap<String,String> ();
        param.put("compcode", compcode);
        param.put("isdel", "1");
        CmBxCompany company = cmBxCompanyService.getCmBxCompany(param);
        if(company != null){
        	resultMap.put("errorMsg", "当前保险公司代码已存在，请重新输入");
        	resultMap.put("errorCode", "9999");
        	return resultMap;
        }

        CmBxCompany cmBxCompany = new CmBxCompany();
        cmBxCompany.setId(new BigDecimal(commonService.getSeqValue("SEQ_INSUR_ID")));
        cmBxCompany.setCompname(compname);
        cmBxCompany.setCompcode(compcode);
        cmBxCompany.setCreator(user.getUserId());
        cmBxCompany.setModifydt(new Date());
        cmBxCompany.setIsdel("1");
        cmBxCompanyService.insertCmBxCompany(cmBxCompany);
      
        return resultMap;
    }
	
	
	/**
	 * 展示修改页面
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/viewCmBxCompany", method = RequestMethod.POST)
	@ResponseBody
    public  Map<String, Object> viewCmBxCompany(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("errorMsg", "操作成功");
		resultMap.put("errorCode", "0000");
		String id = request.getParameter("id");
		if(StringUtils.isNotBlank(id)){
			Map<String,String> param = new HashMap<String,String> ();
			param.put("id", id);
			CmBxCompany cmBxCompany = cmBxCompanyService.getCmBxCompany(param);
			resultMap.put("domain", cmBxCompany);
		}else{
			resultMap.put("errorMsg", "操作失败：id不能为空");
			resultMap.put("errorCode", "9999");
		}
      
        return resultMap;
    }
	
}
