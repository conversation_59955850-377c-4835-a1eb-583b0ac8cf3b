/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.constants;

/**
 * @description: (基于 菜单 vs 按钮 做一些业务 权限控制 常量类 )
 * <AUTHOR>
 * @date 2023/11/2 20:15
 * @since JDK 1.8
 */
public class MenuButtonConstant {

    /**
     * 路演管理
     */
    public static final String MENU_CONFERENCE="02050401";

    /**
     * 路演会议-新增|修改 自带审核权限
     */
    public static final String OPT_CONFERENCE_WITH_AUTH="10";

    /**
     * 12-生成自己创建二维码
     */
    public static final String OPT_GENERATE_SELF_QRCODE="12";

    /**
     *  11-生成所有二维码
     */
    public static final String OPT_GENERATE_ALL_QRCODE="11";




    /**
     * 扫码参会 管理
     */
    public static final String MENU_CONFERENCE_SCAN="02050404";

    /**
     * [扫码参会页面]  6-【查新客】
     */
    public static final String OPT_SCAN_NEW_CUST="6";

    /**
     * [扫码参会页面]  7-【查询多客户】
     */
    public static final String OPT_SCAN_MULTI_CUST="7";

    /**
     * [扫码参会页面]  8-【查询所有】
     */
    public static final String OPT_SCAN_ALL="8";





}