package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.base.*;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustListRequest;
import com.howbuy.crm.conscust.response.QueryConscustListResponse;
import com.howbuy.crm.conscust.service.QueryConscustListService;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.prosale.CmSaleCalPercentageInfo;
import com.howbuy.crm.hb.domain.prosale.CurrentPreDetailInfoVo;
import com.howbuy.crm.hb.domain.prosale.ProductWhiteModel;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.prosale.*;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.ListPageUtil;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.nt.basedetail.dto.CustBaseDetailDomain;
import com.howbuy.crm.nt.basedetail.dto.CustKycInfo;
import com.howbuy.crm.nt.basedetail.request.QueryCustBaseDetailRequest;
import com.howbuy.crm.nt.basedetail.response.QueryCustBaseDetailResponse;
import com.howbuy.crm.nt.basedetail.service.QueryCustBaseDetailService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prosale.dto.CmPreCalendar;
import com.howbuy.crm.prosale.dto.CmPreUsedCalendar;
import com.howbuy.crm.prosale.dto.CmPreWarningThreshold;
import com.howbuy.crm.prosale.dto.CmXkCalendarWarning;
import com.howbuy.crm.prosale.request.QueryCurrentPreDetailRequest;
import com.howbuy.crm.prosale.response.CurrentPreDetailInfo;
import com.howbuy.crm.prosale.response.QueryCurrentPreDetailResponse;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.crm.prosale.service.PreCalendarService;
import com.howbuy.crm.prosale.service.PreSaleControlService;
import com.howbuy.crm.util.BigDecimalUtils;
import com.howbuy.interlayer.product.model.parameter.ProductWhiteQueryModel;
import com.howbuy.interlayer.product.model.parameter.ProductWhiteQueryPageModel;
import com.howbuy.interlayer.product.service.HighProductForCrmService;
import crm.howbuy.base.constants.CRM3ErrorCode;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @version 1.0
 * @Description: 日历操作Controller
 * @created
 */
@Slf4j
@Controller
@RequestMapping(value = "/prosale")
public class CmPreCalendarController  extends BaseController {

    @Autowired
    private CmPreCalendarService cmPreCalendarService;

    @Autowired
    private PreCalendarService preCalendarService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private HighProductForCrmService highProductForCrmService;
    @Autowired
    private PreSaleControlService preSaleControlService;

    @Resource(name = "taskExecutor")
    private ThreadPoolTaskExecutor threadPool;

    @Autowired
    private CmPreCalendarLogService cmPreCalendarLogService;

    @Autowired
    private PreControlHbService preControlHbService;

    @Autowired
    private QueryConscustListService queryConscustListService;

    @Autowired
    private PreBookService preBookService;

    @Autowired
    private PrebookproductinfoService prebookproductinfoService;

    @Autowired
    private ConscustService conscustService;

    @Autowired
    private QueryCustBaseDetailService queryCustBaseDetailService;

    @Autowired
    private ProductinfoService productinfoService;

    @Autowired
    private JjxxInfoService jjxxInfoService;

    /**
     * 跳转到产品销控参数页面方法
     *
     * @param request
     * @return ModelAndView
     */
    @RequestMapping("/listCmPreCalendar.do")
    public ModelAndView listPreByOther(HttpServletRequest request) {
        String userId = getLoginUserId();
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/prosale/listCmPreCalendar");
        modelAndView.addObject("operator", ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(userId))));
        modelAndView.addObject("userId", userId);
        return modelAndView;
    }

    /**
     * 加载产品销控参数页面数据方法
     *
     * @param request
     * @return Map<String, Object>
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/listCmPreCalendarByPage_json.do")
    public Map<String, Object> listCmPreCalendarByPage(HttpServletRequest request) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>(3);

        Map<String, String> param = buildParam(request);
        PageData<CmPreCalendar> pageData = cmPreCalendarService.listCmPreCalendarByPage(param);
        List<CmPreCalendar> listdata = pageData.getListData();
        if (CollectionUtils.isNotEmpty(listdata)) {
            for (CmPreCalendar cmPreCalendar : listdata) {
                if (StringUtils.isNotBlank(cmPreCalendar.getChecker())) {
                    cmPreCalendar.setChecker(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(cmPreCalendar.getChecker()))));
                }

                if (StringUtils.isNotBlank(cmPreCalendar.getXkParamModifier())) {
                    cmPreCalendar.setXkParamModifier(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(cmPreCalendar.getXkParamModifier()))));
                    cmPreCalendar.setXkParamCreator(cmPreCalendar.getXkParamModifier());
                } else {
                    if (StringUtils.isNotBlank(cmPreCalendar.getXkParamCreator())) {
                        cmPreCalendar.setXkParamCreator(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(cmPreCalendar.getXkParamCreator()))));
                    }
                }

                if (StringUtils.isNotBlank(cmPreCalendar.getModifier())) {
                    cmPreCalendar.setCreator(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(cmPreCalendar.getModifier()))));
                } else {
                    if (StringUtils.isNotBlank(cmPreCalendar.getCreator())) {
                        cmPreCalendar.setCreator(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(cmPreCalendar.getCreator()))));
                    }
                }

            }
        }
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        resultMap.put("rows", pageData.getListData());
        return resultMap;
    }


    /**
     * 跳转到addCmPreCalendar页面方法
     *
     * @return String
     */
    @ResponseBody
    @RequestMapping("/addCmPreCalendar.do")
    public ModelAndView addPrebookproduct(HttpServletRequest request) throws Exception {
        return new ModelAndView("prosale/addCmPreCalendar");
    }


    /**
     * 保存日历参数
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/saveCmPreCalendar.do")
    public String saveCmPreCalendar(HttpServletRequest request) throws Exception {
        String result = "";
        String userId = getLoginUserId();
        //验证日历参数是否合法
        result = validatePreCalendar(request);
        if (StringUtils.isNotBlank(result)) {
            return result;
        }
        String calendarId = request.getParameter("calendarId");
        //封装日历对象
        CmPreCalendar cmPreCalendar = this.handCmPreCalendar(request);
        //保存日历参数
        BaseResponse res = preCalendarService.saveCmPreCalendar(cmPreCalendar);
        if (BaseConstantEnum.SUCCESS.getCode().equals(res.getReturnCode())) {
            result = "success";

            //插入日历编辑和日历预警编辑日志
            //type：1-新增日历；2-修改日历；3-日历预警编辑；4-日历预警审核
            String type = request.getParameter("type");
            if ("2".equals(type) || "3".equals(type)) {
                String opType = null;
                if ("2".equals(type)) {
                    //类型模块：1-日历修改；2-日历预警参数修改；3-通配参数修改
                    opType = "1";
                } else if ("3".equals(type)) {
                    opType = "2";
                }
                cmPreCalendarLogService.insertCmPreCalendarLog(calendarId, opType, userId, "1");
            }
        } else {
            result = res.getDescription();
        }

        return result;
    }

    /**
     * 封装日历对象
     *
     * @param request
     * @return
     */
    private CmPreCalendar handCmPreCalendar(HttpServletRequest request) {
        CmPreCalendar cmPreCalendar = new CmPreCalendar();


        String userId = getLoginUserId();
        //type：1-新增日历；2-修改日历；3-日历预警编辑；4-日历预警审核
        String type = request.getParameter("type");
        String calendarId = request.getParameter("calendarId");
        String checkStatus = request.getParameter("checkStatus");
        String appointStartStimestamp = request.getParameter("appointStartStimestamp");
        String appointEndStimestamp = request.getParameter("appointEndStimestamp");

        //赋值日历参数
        if ("1".equals(type) || "2".equals(type)) {
            String productId = request.getParameter("productId");

            String currentLimit = request.getParameter("currentLimit");
            String currentNum = request.getParameter("currentNum");

            if (StringUtils.isNotBlank(calendarId)) {
                cmPreCalendar = preCalendarService.getCalendarById(calendarId);
                cmPreCalendar.setCalendarId(calendarId);
                cmPreCalendar.setUpdateStimestamp(new Date());
                cmPreCalendar.setModifier(userId);
            } else {
                // 用户新增直销产品日历，日历id前缀为CRM（避免和中台日历id重复）
                cmPreCalendar.setCalendarId("CRM" + commonService.getSeqValue("SEQ_PRE_CALENDAR"));
                // 新增时，插入中台业务码（申购 1124）-用来过滤销控日历的
                cmPreCalendar.setmBusiCode(StaticVar.BUSICODE_APPEND);
                cmPreCalendar.setCreateStimestamp(new Date());
                cmPreCalendar.setCreator(userId);
            }

            cmPreCalendar.setCheckStatus(checkStatus);
            cmPreCalendar.setProductId(productId);
            cmPreCalendar.setAppointStartStimestamp(StringUtils.isBlank(appointStartStimestamp) ? null : DateUtil.string2Date(appointStartStimestamp, "yyyyMMdd hh:mm"));
            cmPreCalendar.setAppointEndStimestamp(StringUtils.isBlank(appointEndStimestamp) ? null : DateUtil.string2Date(appointEndStimestamp, "yyyyMMdd hh:mm"));
            cmPreCalendar.setCurrentLimit(StringUtils.isBlank(currentLimit) ? null : new BigDecimal(currentLimit));
            cmPreCalendar.setCurrentNum(StringUtils.isBlank(currentNum) ? null : Integer.parseInt(currentNum));

        }

        //赋值日历预警信息
        if ("3".equals(type)) {
            String sfmsjg = request.getParameter("sfmsjg");
            //代销
            if ("1".equals(sfmsjg) || "3".equals(sfmsjg)) {
                if (StringUtils.isNotBlank(calendarId)) {
                    cmPreCalendar = preCalendarService.getCalendarById(calendarId);
                    cmPreCalendar.setCalendarId(calendarId);
                    cmPreCalendar.setProductId(request.getParameter("productId"));
                    cmPreCalendar.setAppointStartStimestamp(StringUtils.isBlank(appointStartStimestamp) ? null : DateUtil.string2Date(appointStartStimestamp, "yyyyMMdd hh:mm"));
                    cmPreCalendar.setAppointEndStimestamp(StringUtils.isBlank(appointEndStimestamp) ? null : DateUtil.string2Date(appointEndStimestamp, "yyyyMMdd hh:mm"));
                }
            }

            String warningId = request.getParameter("warningId");
            String xkType = request.getParameter("xkType");
            String isShowSalAmount = request.getParameter("isShowSalAmount");
            String isShowSalPersion = request.getParameter("isShowSalPersion");

            String currentIcLimit = request.getParameter("currentIcLimit");
            String currentHbcLimit = request.getParameter("currentHbcLimit");
            String currentIcPersionLimit = request.getParameter("currentIcPersionLimit");
            String currentHbcPersionLimit = request.getParameter("currentHbcPersionLimit");
            String currentAmountRatio = request.getParameter("currentAmountRatio");
            String currentPersonRatio = request.getParameter("currentPersonRatio");
            String warningThresholdStr = request.getParameter("warningThresholdStr");

            CmXkCalendarWarning cmXkCalendarWarning = new CmXkCalendarWarning();
            cmXkCalendarWarning.setId(StringUtils.isBlank(warningId) ? commonService.getSeqValue("SEQ_PRE_CALENDAR") : warningId);
            cmXkCalendarWarning.setCalendarId(calendarId);
            cmXkCalendarWarning.setCurrentAmountRatio(StringUtils.isBlank(currentAmountRatio) ? null : new BigDecimal(currentAmountRatio));
            cmXkCalendarWarning.setCurrentPersonRatio(StringUtils.isBlank(currentPersonRatio) ? null : new BigDecimal(currentPersonRatio));
            if (StringUtils.isNotBlank(warningId)) {
                cmXkCalendarWarning.setModifier(userId);
                cmXkCalendarWarning.setUpdateStimestamp(new Date());
            } else {
                cmXkCalendarWarning.setCreator(userId);
                cmXkCalendarWarning.setCreateStimestamp(new Date());
            }
            cmXkCalendarWarning.setXkType(xkType);
            cmXkCalendarWarning.setIsShowSalAmount(isShowSalAmount);
            cmXkCalendarWarning.setIsShowSalPersion(isShowSalPersion);
            cmXkCalendarWarning.setCurrentIcLimit(StringUtils.isBlank(currentIcLimit) ? null : new BigDecimal(currentIcLimit));
            cmXkCalendarWarning.setCurrentHbcLimit(StringUtils.isBlank(currentHbcLimit) ? null : new BigDecimal(currentHbcLimit));
            ;
            cmXkCalendarWarning.setCurrentIcPersionLimit(StringUtils.isBlank(currentIcPersionLimit) ? null : Integer.parseInt(currentIcPersionLimit));
            cmXkCalendarWarning.setCurrentHbcPersionLimit(StringUtils.isBlank(currentHbcPersionLimit) ? null : Integer.parseInt(currentHbcPersionLimit));
            cmXkCalendarWarning.setCheckStatus(checkStatus);
            cmPreCalendar.setCmXkCalendarWarning(cmXkCalendarWarning);


            if (StringUtils.isNotBlank(warningThresholdStr)) {
                List<CmPreWarningThreshold> listCmPreWarningThreshold = new ArrayList<CmPreWarningThreshold>(100);
                String[] arr = warningThresholdStr.split(";");

                for (String str : arr) {
                    if (StringUtils.isNotBlank(str)) {
                        String id = commonService.getSeqValue("SEQ_PRE_CALENDAR");
                        String[] thresholdArr = str.split(",");
                        CmPreWarningThreshold cmPreWarningThreshold = new CmPreWarningThreshold();
                        cmPreWarningThreshold.setId(id);
                        cmPreWarningThreshold.setWarningId(cmXkCalendarWarning.getId());
                        cmPreWarningThreshold.setCreateStimestamp(cmXkCalendarWarning.getCreateStimestamp());
                        cmPreWarningThreshold.setCreator(userId);
                        cmPreWarningThreshold.setWarningType(StringUtils.isBlank(thresholdArr[0]) ? null : thresholdArr[0]);
                        cmPreWarningThreshold.setWarningNumber(StringUtils.isBlank(thresholdArr[1]) ? null : new BigDecimal(thresholdArr[1]));
                        listCmPreWarningThreshold.add(cmPreWarningThreshold);
                    }

                }
                if (CollectionUtils.isNotEmpty(listCmPreWarningThreshold)) {
                    cmXkCalendarWarning.setListCmPreWarningThreshold(listCmPreWarningThreshold);
                }
            }
        }

        return cmPreCalendar;
    }

    /**
     * 验证日历参数是否合法
     *
     * @param request
     * @return
     */
    private String validatePreCalendar(HttpServletRequest request) {
        String result = null;
        //type：1-新增日历；2-修改日历；3-日历预警编辑；4-日历预警审核
        String type = request.getParameter("type");

        //验证投顾可预约时间区间是否重叠
        if ("1".endsWith(type) || "2".endsWith(type)) {
            String productId = request.getParameter("productId");
            String exceptCalendarId = request.getParameter("calendarId");
            String appointStartStimestamp = request.getParameter("appointStartStimestamp");
            String appointEndStimestamp = request.getParameter("appointEndStimestamp");

            if (StringUtil.isBlank(appointStartStimestamp) || StringUtil.isBlank(appointEndStimestamp)) {
                result = "投顾可预约时间（分）不允许为空";
                return result;
            }
            //校验区间合法 yyyyMMdd HH:mm
            boolean duplicateValidate = cmPreCalendarService.validateZxDuplicateCalendar(productId,
                    DateUtil.string2Date(appointStartStimestamp, "yyyyMMdd HH:mm"),
                    DateUtil.string2Date(appointEndStimestamp, "yyyyMMdd HH:mm"),
                    exceptCalendarId);
            if (!duplicateValidate) {
                result = "该期间已存在销控参数，请重新输入！";
                return result;
            }

        }

        if ("3".equals(type)) {
            String currentLimit = request.getParameter("currentLimit");
            String currentNum = request.getParameter("currentNum");
            String currentIcLimit = request.getParameter("currentIcLimit");
            String currentHbcLimit = request.getParameter("currentHbcLimit");
            String currentIcPersionLimit = request.getParameter("currentIcPersionLimit");
            String currentHbcPersionLimit = request.getParameter("currentHbcPersionLimit");
            String warningThresholdStr = request.getParameter("warningThresholdStr");

            //验证金额
            if (((StringUtils.isBlank(currentIcLimit) ? BigDecimal.ZERO : new BigDecimal(currentIcLimit)).add(StringUtils.isBlank(currentHbcLimit) ? BigDecimal.ZERO : new BigDecimal(currentHbcLimit))).
                    compareTo(StringUtils.isBlank(currentLimit) ? BigDecimal.ZERO : new BigDecimal(currentLimit)) > 0) {
                result = "本期IC金额额度和本期HBC金额额度汇总值大于本期总金额额度，请重新输入！";
                return result;
            }
            if (((StringUtils.isBlank(currentIcPersionLimit) ? BigDecimal.ZERO : new BigDecimal(currentIcPersionLimit)).add(StringUtils.isBlank(currentHbcPersionLimit) ? BigDecimal.ZERO : new BigDecimal(currentHbcPersionLimit))).
                    compareTo(StringUtils.isBlank(currentNum) ? BigDecimal.ZERO : new BigDecimal(currentNum)) > 0) {
                result = "本期IC人数额度和本期HBC人数额度汇总值大于本期总人数额度，请重新输入！";
                return result;
            }

            //Op和投顾打款预警参数相同字段名称的数值不能相同
            Map<String, String> warningMap = new HashMap<String, String>(100);
            if (StringUtils.isNotBlank(warningThresholdStr)) {
                String[] arr = warningThresholdStr.split(";");
                for (String thresholdStr : arr) {
                    if (warningMap.containsKey(thresholdStr)) {
                        result = "【参数名称】存在相同参数值，请重新输入！";
                        return result;
                    } else {
                        warningMap.put(thresholdStr, thresholdStr);
                    }
                }
            }
        }

        return result;
    }


    /**
     * 删除预约日历及其相关信息
     *
     * @param request
     * @return String
     */
    @ResponseBody
    @RequestMapping("/deletePreCalendar.do")
    public String deletePreCalendar(HttpServletRequest request) {
        String calendarId = request.getParameter("calendarId");
        if (StringUtils.isBlank(calendarId)) {
            return "paramError";
        }
        CmPreCalendar existsCalendar = preCalendarService.getCalendarById(calendarId);
        if (existsCalendar == null) {
            return String.format("Id:%s 产品日历不存在！", calendarId);
        }
        BaseResponse res = preCalendarService.delCmPreCalendar(calendarId);
        if (BaseConstantEnum.SUCCESS.getCode().equals(res.getReturnCode())) {
            threadPool.execute(() ->
                    preSaleControlService.executeControlAfterDelCalendar(existsCalendar.getProductId(), Lists.newArrayList(existsCalendar.getCalendarId())));
            return "success";
        } else {
            return res.getDescription();
        }

    }


    /**
     * 构建参数
     *
     * @param request
     * @return
     * @throws Exception
     */
    private Map<String, String> buildParam(HttpServletRequest request) throws Exception {
        // 设置查询参数
        Map<String, String> param = new ParamUtil(request).getParamMap();
        String appointStartStimestamp = request.getParameter("appointStartStimestamp");
        String appointEndStimestamp = request.getParameter("appointEndStimestamp");
        String openStartStimestamp = request.getParameter("openStartStimestamp");
        String openEndStimestamp = request.getParameter("openEndStimestamp");
        String mBusiCode = request.getParameter("mBusiCode");

        if (StringUtils.isNotBlank(appointStartStimestamp)) {
            appointStartStimestamp += " 00:00:00";
        } else {
            appointStartStimestamp = null;
        }
        if (StringUtils.isNotBlank(appointEndStimestamp)) {
            appointEndStimestamp += " 23:59:59";
        } else {
            appointEndStimestamp = null;
        }

        if (StringUtils.isNotBlank(openStartStimestamp)) {
            openStartStimestamp += " 00:00:00";
        } else {
            openStartStimestamp = null;
        }
        if (StringUtils.isNotBlank(openEndStimestamp)) {
            openEndStimestamp += " 23:59:59";
        } else {
            openEndStimestamp = null;
        }
        param.put("appointStartStimestamp", appointStartStimestamp);
        param.put("appointEndStimestamp", appointEndStimestamp);
        param.put("openStartStimestamp", openStartStimestamp);
        param.put("openEndStimestamp", openEndStimestamp);

        if (StringUtils.isNotBlank(mBusiCode)) {
            //购买
            if ("1".equals(mBusiCode)) {
                //1120-认购；1122-申购
                param.put("mBusiCodes", "1120,1122");
                param.put("mBusiCode", null);
                //赎回
            } else if ("2".equals(mBusiCode)) {
                param.put("mBusiCode", "1124");
            }
        }
        String topcpdata = (String) request.getSession().getAttribute("topcpdata");
        param.put("topcpdata", topcpdata);
        return param;
    }


    /**
     * 跳转到viewPreCalendar页面方法
     *
     * @return String
     */
    @RequestMapping("/viewPreCalendar.do")
    public ModelAndView viewPreCalendar(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String calendarId = request.getParameter("calendarId");
        //type：1-新增日历；2-修改日历；3-日历预警编辑；4-日历预警审核;5-日历预警详情
        String type = request.getParameter("type");
        Map<String, Object> map = new HashMap<String, Object>();
        if (StringUtils.isNotBlank(calendarId)) {
            CmPreCalendar cmPreCalendar = preCalendarService.getCalendarById(calendarId);
            JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(cmPreCalendar.getProductId(), false);
            map.put("productName", jjxx == null ? "" : jjxx.getJjjc());
            map.put("sfmsjg", jjxx == null ? "" : jjxx.getSfmsjg());
            map.put("cmPreCalendar", cmPreCalendar);
            map.put("cmXkCalendarWarning", cmPreCalendar.getCmXkCalendarWarning());
            map.put("listCmPreWarningThreshold", cmPreCalendar.getCmXkCalendarWarning() != null ? cmPreCalendar.getCmXkCalendarWarning().getListCmPreWarningThreshold() : null);
            map.put("type", type);
        }
        String url = "/prosale/checkCmPreCalendarWarning";
        if ("3".equals(type)) {
            url = "/prosale/editCmPreCalendarWarning";
        } else if ("2".equals(type)) {
            url = "/prosale/editCmPreCalendar";
        } else if ("5".equals(type)) {
            //根据日历日历Id 查询统计占比信息
            List<CmSaleCalPercentageInfo> listCmSaleCalPercentageInfo = preControlHbService.getCmSaleCalPercentage(calendarId);
            map.put("listCmSaleCalPercentageInfo", listCmSaleCalPercentageInfo);
        }

        return new ModelAndView(url, "map", map);
    }


    /**
     * 审核预约日历
     *
     * @param request
     * @return String
     */
    @ResponseBody
    @RequestMapping("/checkPreCalendar.do")
    public String checkPreCalendar(HttpServletRequest request) {
        String result = null;
        String calendarId = request.getParameter("calendarId");
        String checkStatus = request.getParameter("checkStatus");
        String userId = getLoginUserId();
        if (StringUtils.isNotBlank(calendarId)) {
            BaseResponse res = preCalendarService.checkCmPreCalendar(calendarId, userId, checkStatus);
            if (BaseConstantEnum.SUCCESS.getCode().equals(res.getReturnCode())) {
                intoPreControl(calendarId, checkStatus);
                result = "success";
            } else {
                result = res.getDescription();
            }
        } else {
            result = "paramError";
        }
        return result;
    }


    /**
     * 触发进入销控-
     *
     * @param calendarId
     * @param checkStatus
     */
    private void intoPreControl(String calendarId, String checkStatus) {
        CmPreCalendar cmPreCalendar = preCalendarService.getCalendarById(calendarId);
        if (cmPreCalendar == null) {
            log.error("日历Id:{},未找到！", calendarId);
            return;
        }

        if ("2".equals(checkStatus)) { //更新为：审核通过，触发销控
            threadPool.execute(() ->
                    preSaleControlService.executeControlAfterUpdateCalendar(cmPreCalendar.getProductId(), cmPreCalendar.getCalendarId()));
        } else { //更新为-->审核不通过
            threadPool.execute(() ->
                    preSaleControlService.executeControlAfterDelCalendar(cmPreCalendar.getProductId(), Lists.newArrayList(cmPreCalendar.getCalendarId())));
        }


    }


    /**
     * 跳转到白名单管理页面方法
     *
     * @param request
     * @return ModelAndView
     */
    @RequestMapping("/listWhiteList.do")
    public ModelAndView listWhiteList(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/prosale/listWhiteList");
        return modelAndView;
    }

    /**
     * 加载白名单管理页页面数据方法
     *
     * @param request
     * @return Map<String, Object>
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/listlistWhiteListByPage_json.do")
    public Map<String, Object> listlistWhiteListByPage(HttpServletRequest request) throws Exception {
        String howBuyCode = "0";
        Map<String, Object> resultMap = new HashMap<String, Object>(3);
        String fundCodeOrName = request.getParameter("productId");
        String hboneNo = request.getParameter("hboneNo");
        String conscode = request.getParameter("conscode");
        String orgcode = request.getParameter("orgcode");
        String page = request.getParameter("page");
        String rows = request.getParameter("rows");
        String topCpData = (String) request.getSession().getAttribute("topcpdata");
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        List<ProductWhiteModel> listreal = new ArrayList<ProductWhiteModel>();
        ProductWhiteQueryModel productWhiteQueryModel = new ProductWhiteQueryModel();
        if (StringUtil.isNotBlank(fundCodeOrName)) {
            productWhiteQueryModel.setFundCodeOrName(fundCodeOrName);
        }
        if (StringUtil.isNotBlank(hboneNo)) {
            productWhiteQueryModel.setHboneNo(hboneNo);
        }
        productWhiteQueryModel.setPageNum(1);
        productWhiteQueryModel.setPageSize(100000);
        log.info("highProductForCrmService.queryLockWhiteListByPage(productWhiteQueryModel)" + JSON.toJSON(productWhiteQueryModel));
        ProductWhiteQueryPageModel productWhiteQueryPageModel = highProductForCrmService.queryLockWhiteListByPage(productWhiteQueryModel);
        log.info("highProductForCrmService.queryLockWhiteListByPage(productWhiteQueryPageModel)" + JSON.toJSON(productWhiteQueryPageModel));

        List<ProductWhiteQueryModel> queryList = Lists.newArrayList();
        if (productWhiteQueryPageModel != null && CollectionUtils.isNotEmpty(productWhiteQueryPageModel.getProductWhiteQueryModel())) {
            queryList = productWhiteQueryPageModel.getProductWhiteQueryModel();
        }
        //根据所选的投顾或客户查询对应的一账通
        List<String> listhboneno = new ArrayList<>();
        queryList.forEach(model1 -> {
            if (!listhboneno.contains(model1.getHboneNo())) {
                listhboneno.add(model1.getHboneNo());
            }
        });
        //去重
        listhboneno.stream().distinct().collect(Collectors.toList());
        List<String> listnew = new ArrayList<>();
        if (listhboneno.size() > 0) {
            //查询符合条件的一账通list
            Map<String, String> paramhboneno = new HashMap<String, String>();
            if (StringUtils.isNotBlank(conscode)) {
                paramhboneno.put("conscode", conscode);
            } else {
                paramhboneno.put("orgcode", orgcode);
            }
            String sqlins = Util.getOracleSQLIn(listhboneno, 999, "T.Hbone_No");
            paramhboneno.put("hbones", sqlins);
            listnew = cmPreCalendarService.listHbonesByParam(paramhboneno);
        }
        if (listnew.size() > 0) {
            for (ProductWhiteQueryModel model : queryList) {
                //产品广度为常规产品的，非常规产品数据不让看
                if(StaticVar.CP_TOP_DATA_CG.equals(topCpData) && crm.howbuy.base.utils.StringUtil.isNotNullStr(model.getFundCode())){
                    Integer count = prebookproductinfoService.checkNormalProduct(model.getFundCode());
                    if(count == 0){
                        continue;
                    }
                }
                //如果存在符合条件的客户
                if (listnew.contains(model.getHboneNo())) {
                    ProductWhiteModel pwm = new ProductWhiteModel();
                    pwm.setHboneNo(model.getHboneNo());
                    pwm.setFundCode(model.getFundCode());
                    pwm.setFundName(model.getFundName());
                    listreal.add(pwm);
                }
            }
        }
        List<ProductWhiteModel> listreturn = new ArrayList<>();
        if (listreal != null && listreal.size() > 0) {
            //对过滤后的数据进行分页
            ListPageUtil<ProductWhiteModel> listPageUtil = new ListPageUtil<ProductWhiteModel>(listreal, StringUtils.isBlank(rows) ? 20 : Integer.parseInt(rows));
            listreturn = listPageUtil.getPagedList(StringUtils.isBlank(page) ? 1 : Integer.parseInt(page));
            //对分页后的数据付客户姓名、所属投顾、所属部分、所属区域
            listreturn.forEach(model -> {
                if (StringUtil.isNotBlank(model.getHboneNo())) {
                    ConscustInfoDomain domain = getCustInfoByHboneNo(model.getHboneNo());
                    if (domain != null) {
                        model.setCustName(domain.getCustname());
                        model.setConsname(consOrgCache.getAllUserMap().get((Util.ObjectToString(domain.getConscode()))));
                        model.setOutletName(consOrgCache.getOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(domain.getConscode())));
                        String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(domain.getConscode()));
                        if (howBuyCode.equals(uporgcode)) {
                            model.setUporgname(model.getOutletName());
                        } else {
                            model.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
                        }
                    }
                }
            });
        }
        resultMap.put("total", listreal.size());
        resultMap.put("rows", listreturn);
        return resultMap;
    }

    /**
     * 根据一账通账号 获取 客户信息
     *
     * @param hboneNo
     * @return
     */
    private ConscustInfoDomain getCustInfoByHboneNo(String hboneNo) {
        QueryConscustListRequest requestParam = new QueryConscustListRequest();
        requestParam.setHboneno(hboneNo);
        QueryConscustListResponse responseData = queryConscustListService.queryConscustInfo(requestParam);
        List<ConscustInfoDomain> conscustlist = responseData.getConscustlist();
        if (CollectionUtils.isNotEmpty(conscustlist)) {
            return conscustlist.get(0);
        }
        return null;
    }


    /**
     * 获取审核预约日历
     *
     * @param request
     * @return String
     */
    @ResponseBody
    @RequestMapping("/selectPreCalendarDate.do")
    public Map<String, Object> selectPreCalendarDate(HttpServletRequest request) {
        Map<String, Object> retMap = new HashMap<String, Object>(2);

        String pcode = request.getParameter("pcode");
        Map<String, String> param = new HashMap<String, String>(1);
        param.put("productId", pcode);
        List<CmPreUsedCalendar> listCmPreCalendar = cmPreCalendarService.selectUsedListByProductIdForSaleControl(pcode, null);

        //按预约起始日期升序排序
        listCmPreCalendar
                = listCmPreCalendar
                .stream()
                .sorted(Comparator.comparing(CmPreUsedCalendar::getExpectTradeStartStimestamp))
                .collect(Collectors.toList());

        //取 默认选中值
        final String pattern = "yyyyMMdd HH:mm";
        String currentDtStr = DateUtil.date2String(new Date(), pattern);
        CmPreUsedCalendar selectedOne =
                listCmPreCalendar.stream().filter(
                        source -> DateUtil.date2String(source.getExpectTradeEndStimestamp(), pattern).compareTo(currentDtStr) > 0
                ).findFirst().orElse(null);
        retMap.put("selectedId", selectedOne == null ? null : selectedOne.getCalendarId());

        //下拉框列表
        List<Map<String, String>> selectList = Lists.newArrayList(); //Map : key : dropDown下拉框的value    value: dropDown下拉框的Name
        listCmPreCalendar.stream().forEach(source -> {
            Map<String, String> dropDown = Maps.newHashMap();
            dropDown.put("key", source.getCalendarId());
            dropDown.put("value", String.format("%s - %s",
                    DateUtil.date2String(source.getExpectTradeStartStimestamp(), DateUtil.SHORT_DATEPATTERN),
                    DateUtil.date2String(source.getExpectTradeEndStimestamp(), DateUtil.SHORT_DATEPATTERN)));
            selectList.add(dropDown);
        });
        retMap.put("selectList", selectList);
        return retMap;
    }


    /**
     * 跳转到申购赎回日历（新）页面方法
     *
     * @param request
     * @return ModelAndView
     */
    @RequestMapping("/listCmPreCalendarNew.do")
    public ModelAndView listCmPreCalendarNew(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject("pCode", request.getParameter("pCode"));
        modelAndView.addObject("mBusiType", request.getParameter("mBusiType"));
        modelAndView.setViewName("/prosale/listCmCalendarNew");
        return modelAndView;
    }


    /**
     * 加载申购赎回日历（新）页面数据方法
     *
     * @param request
     * @return Map<String, Object>
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/listCmPreCalendarNewByPage_json.do")
    public Map<String, Object> listCmPreCalendarNewByPage(HttpServletRequest request) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>(3);

        Map<String, String> param = buildParam(request);
        PageData<CmPreCalendar> pageData = cmPreCalendarService.listCmPreCalendarByPageNew(param);
        List<CmPreCalendar> listdata = pageData.getListData();
        handleCmPreCalendar(listdata, (String) param.get("orgCode"), (String) param.get("consCode"));

        resultMap.put("total", pageData.getPageBean().getTotalNum());
        resultMap.put("rows", listdata);
        return resultMap;
    }

    private void handleCmPreCalendar(List<CmPreCalendar> listCmPreCalendar, String orgcode, String conscode) {
        if (CollectionUtils.isNotEmpty(listCmPreCalendar)) {
            for (CmPreCalendar cmPreCalendar : listCmPreCalendar) {

                Map<String, List<String>> resultMap = productinfoService.queryProductControl(cmPreCalendar.getProductId());
                if (resultMap != null) {
                    cmPreCalendar.setOnlySuppleFlag(resultMap.get("onlySuppleFlags") != null ? resultMap.get("onlySuppleFlags") : null);
                    cmPreCalendar.setBuyUserType(resultMap.get("buyUserTypes") != null ? resultMap.get("buyUserTypes") : null);
                }

                cmPreCalendar.setOpenStartStimestampStr(cmPreCalendar.getOpenStartStimestamp() == null ? "" : DateTimeUtil.PatternDate(cmPreCalendar.getOpenStartStimestamp(), DateTimeUtil.DATE_PATTERN));
                cmPreCalendar.setOpenEndStimestampStr(cmPreCalendar.getOpenEndStimestamp() == null ? "" : DateTimeUtil.PatternDate(cmPreCalendar.getOpenEndStimestamp(), DateTimeUtil.DATE_PATTERN));
                cmPreCalendar.setAllowOrderStartdtStr(cmPreCalendar.getAllowOrderStartdt() == null ? "" : DateTimeUtil.PatternDate(cmPreCalendar.getAllowOrderStartdt(), "yyyy-MM-dd HH:mm:ss"));
                cmPreCalendar.setAllowOrderEnddtStr(cmPreCalendar.getAllowOrderEnddt() == null ? "" : DateTimeUtil.PatternDate(cmPreCalendar.getAllowOrderEnddt(), "yyyy-MM-dd HH:mm:ss"));
                cmPreCalendar.setPayDeadlineStimestampStr(cmPreCalendar.getPayDeadlineStimestamp() == null ? "" : DateTimeUtil.PatternDate(cmPreCalendar.getPayDeadlineStimestamp(), "yyyy-MM-dd HH:mm:ss"));
                String mBusiCode = cmPreCalendar.getmBusiCode();
                if (StringUtils.isNotBlank(mBusiCode)) {
                    ReturnMessageDto<Integer> returnMessageDto = preCalendarService.currentPreCountByCalendarId(cmPreCalendar.getCalendarId(), orgcode, conscode);
                    if (returnMessageDto != null && BaseConstantEnum.SUCCESS.getCode().equals(returnMessageDto.getReturnCode())) {
                        cmPreCalendar.setCurrentPreCount(returnMessageDto.getReturnObject() == null ? 0 : returnMessageDto.getReturnObject());
                    }
                }
                // 本期总金额额度
                cmPreCalendar.setCurrentLimitStr(BigDecimalUtils.displayThousandPercentile(cmPreCalendar.getCurrentLimit()));
                // 本期总人数额度
                cmPreCalendar.setCurrentNumStr(Objects.isNull(cmPreCalendar.getCurrentNum())
                        ? "--" : cmPreCalendar.getCurrentNum().toString());
            }
        }
    }


    /**
     * 导出操作:申购赎回日历（新）
     *
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportCmPreCalendarNew.do")
    public void exportCmPreCalendarNew(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
        Map<String, String> param = buildParam(request);
        List<CmPreCalendar> listdata = cmPreCalendarService.listCmPreCalendar(param);
        handleCmPreCalendar(listdata, (String) param.get("orgCode"), (String) param.get("consCode"));
        if (CollectionUtils.isNotEmpty(listdata)) {
            String mBusiCodeStr = "";
            for (CmPreCalendar cmPreCalendar : listdata) {
                String mBusiCode = cmPreCalendar.getmBusiCode();
                if ("1120".equals(mBusiCode)) {
                    mBusiCodeStr = "认购";
                } else if ("1122".equals(mBusiCode)) {
                    mBusiCodeStr = "申购";
                } else if ("1124".equals(mBusiCode)) {
                    mBusiCodeStr = "赎回";
                }
                cmPreCalendar.setmBusiCode(mBusiCodeStr);
            }
            try {
                // 清空输出流
                response.reset();
                // 设置文件格式和名字
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition",
                        "attachment;fileName=" + new String("申购赎回日历（新）.xls".getBytes("gb2312"), "ISO8859-1"));
                ServletOutputStream os = response.getOutputStream();

                String[] columnName = new String[]{"基金代码", "基金名称", "产品成立日", "业务类型", "开放开始日", "开放结束日", "预约开始时间", "预约结束时间", "打款截止时间", "当期预约人数"};

                String[] beanProperty = new String[]{"productId", "productName", "clrq", "mBusiCode", "openStartStimestampStr", "openEndStimestampStr", "allowOrderStartdtStr", "allowOrderEnddtStr", "payDeadlineStimestampStr", "currentPreCount"};
                ExcelWriter.writeExcel(os, "申购赎回日历（新）", 0, listdata, columnName, beanProperty);
                os.close(); // 关闭流

            } catch (Exception e) {
                log.error("文件导出异常:", e);
            }
        }
    }


    /**
     * 跳转到当期预约明细页面方法
     *
     * @param request
     * @return ModelAndView
     */
    @RequestMapping("/listCurrentPreDetail.do")
    public ModelAndView listCurrentPreDetail(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject("calendarId", request.getParameter("calendarId"));
        modelAndView.addObject("orgCode", request.getParameter("orgCode"));
        modelAndView.addObject("consCode", request.getParameter("consCode"));
        modelAndView.addObject("mBusiCode", request.getParameter("mBusiCode"));
        modelAndView.setViewName("/prosale/currentPreDetail");
        return modelAndView;
    }

    /**
     * 加载当期预约明细页面数据方法
     *
     * @param request
     * @return Map<String, Object>
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/listCurrentPreDetailByPage_json.do")
    public Map<String, Object> listCurrentPreDetailByPage(HttpServletRequest request) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>(8);
        String pageStr = request.getParameter("page");
        String rowsStr = request.getParameter("rows");
        int curPage = StringUtils.isBlank(pageStr) ? 1 : Integer.parseInt(pageStr);
        int pageSize = StringUtils.isBlank(rowsStr) ? 10 : Integer.parseInt(rowsStr);
        String calendarId = request.getParameter("calendarId");
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");

        QueryCurrentPreDetailRequest req = new QueryCurrentPreDetailRequest();
        req.setCurPage(curPage);
        req.setPageSize(pageSize);
        req.setCalendarId(calendarId);
        req.setOrgcode(orgCode);
        req.setConscode(StringUtils.isBlank(consCode) || "null".equals(consCode) ? null : consCode);

        log.info("preBookService.queryCurrentPreDetailByPage(req)请求:{}", JSON.toJSONString(req));
        QueryCurrentPreDetailResponse res = preBookService.queryCurrentPreDetail(req);
        log.info("preBookService.queryCurrentPreDetailByPage(req)返回:{}", JSON.toJSONString(res));
        List<CurrentPreDetailInfo> list = null;

        if (res != null && BaseConstantEnum.SUCCESS.getCode().equals(res.getReturnCode())) {
            list = res.getCurrentPreDetailInfoList();
            resultMap.put("total", res.getTotalCount());
            resultMap.put("totalBuyAmt", res.getTotalBuyAmt());
            resultMap.put("totalRealPayAmt", res.getTotalRealPayAmt());
            resultMap.put("totalSellVol", res.getTotalSellVol());
        }
        //处理字段值
        List<CurrentPreDetailInfoVo> listVo = handCurrentPreDetailInfo(list, request);
        resultMap.put("rows", listVo);
        return resultMap;
    }

    /**
     * 返回给前端展示
     *
     * @param listInfo
     * @return
     */
    private List<CurrentPreDetailInfoVo> handCurrentPreDetailInfo(List<CurrentPreDetailInfo> listInfo, HttpServletRequest request) {
        List<CurrentPreDetailInfoVo> list = null;
        if (CollectionUtils.isNotEmpty(listInfo)) {
            list = new ArrayList<CurrentPreDetailInfoVo>();
            ConstantCache constantCache = ConstantCache.getInstance();
            ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
            String zero = "0";
            for (CurrentPreDetailInfo currentPreDetailInfo : listInfo) {
                CurrentPreDetailInfoVo info = new CurrentPreDetailInfoVo();
                BeanUtils.copyProperties(currentPreDetailInfo, info);
                info.setConsname(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
                info.setOutletName(consOrgCache.getOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(info.getCreator())));
                String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(info.getCreator()));
                if (zero.equals(uporgcode)) {
                    info.setUporgname(info.getOutletName());
                } else {
                    info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
                }
                info.setSfmsjgVal(ProdSfmsjgEnum.getDescription(info.getSfmsjg()));
                info.setPretypeVal(constantCache.getConstantKeyVal("pretype").get(info.getPretype()));
                info.setOrderstateVal(constantCache.getConstantKeyVal("dealorderStatus").get(info.getOrderstate()));
                info.setPaystateVal(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));
                //是否资金匹配 描述 转义
                String finMatchedDesc = YesOrNoEnum.getDescription(info.getFinMatched());
                info.setFinMatchedDesc(finMatchedDesc==null?"否":finMatchedDesc);


                String paytypeVal = "";
                //支付方式:赎回显示空，其他情况：直销直接显示“银行转账划款”，代销，存在中台订单，显示中台的回款方式，不存在显示空
                if (!StaticVar.PREBOOK_TRADE_TYPE_SALE.equals(info.getTradeType())) {
                    if ("1".equals(info.getIszx())) {
                        paytypeVal = "银行转账划款";
                    } else {
                        if (StringUtil.isNotBlank(info.getDealno())) {
                            paytypeVal = constantCache.getVal("paymenttype", info.getPaymenttype());
                        }
                    }
                }
                info.setPaymenttypeVal(paytypeVal);

                //赎回至：直销直接显示“银行卡”，代销，存在中台订单，显示中台的赎回方式，不存在显示空
                String saleto = "";
                if (StaticVar.PREBOOK_TRADE_TYPE_SALE.equals(info.getTradeType())) {
                    if ("1".equals(info.getIszx())) {
                        saleto = "银行卡";
                    } else {
                        if (StringUtil.isNotBlank(info.getDealno())) {
                            saleto = constantCache.getVal("redeemdirection", info.getRedeemdirection());
                        }
                    }
                }
                info.setRedeemdirectionVal(saleto);
                if (info.getDiscountstate() != null) {
                    info.setDiscountstateVal(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
                } else {
                    info.setDiscountstate("1");
                    info.setDiscountstateVal(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
                }
                info.setZczmstate(prebookproductinfoService.getZczmstate(info.getHboneno(), info.getFundcode(), info.getConscustno()));

                Conscust conscust = conscustService.getConscust(info.getConscustno());
                if (conscust != null) {
                    //长期
                    if ("1".equals(conscust.getValidity())) {
                        info.setValiditydt("长期");
                    } else {
                        info.setValiditydt(conscust.getValiditydt());
                    }
                }

                Map<String, CustKycInfo> kycMap = Maps.newHashMap();
                if (StringUtils.isNotBlank(conscust.getHboneno())) {
                    try {
                        QueryCustBaseDetailRequest custBaseDetailRequest = new QueryCustBaseDetailRequest();
                        custBaseDetailRequest.setHboneno(conscust.getHboneno());
                        log.info("queryCustBaseDetailService.queryConscustInfo(custBaseDetailRequest)请求", JSON.toJSONString(custBaseDetailRequest));
                        QueryCustBaseDetailResponse custBaseDetailResponse = queryCustBaseDetailService.queryConscustInfo(custBaseDetailRequest);
                        log.info("queryCustBaseDetailService.queryConscustInfo(custBaseDetailRequest)返回", JSON.toJSONString(custBaseDetailResponse));
                        if (CRM3ErrorCode.success.getValue().equals(custBaseDetailResponse.getReturnCode())) {
                            CustBaseDetailDomain custbasedetail = custBaseDetailResponse.getCustbasedetaildomain();
                            if (custbasedetail != null) {
                                kycMap = custbasedetail.getKycInfoMap();
                            }
                        }
                    } catch (Exception e) {
                        e.getMessage();
                        log.error("queryCustBaseDetailService.queryConscustInfo(custBaseDetailRequest)接口异常！" + e.getMessage(), e);
                    }
                }
                //kyc信息
                CustKycInfo custKycInfo = null;
                for (DisChannelCodeEnum channelCodeEnum : DisChannelCodeEnum.values()) {
                    // 通过Session获取产品广度信息
                    HttpSession session = request.getSession();
                    String topcpdata = (String) session.getAttribute("topcpdata");

                    // 只保留好买相关，去除好臻产品
                    if ("32".equals(topcpdata) && "HZ000N001".equals(channelCodeEnum.getCode())) {
                        continue;
                    }
                    custKycInfo = kycMap.get(channelCodeEnum.getCode());
                    if (custKycInfo == null) {
                        continue;
                    }
                    if (DisChannelCodeEnum.HOWBUY.getCode().equals(custKycInfo.getDisChannelCode())) {
                        info.setHmfcdqr(custKycInfo.getHighrisktoleranceterm());
                    }
                    if (DisChannelCodeEnum.HZ.getCode().equals(custKycInfo.getDisChannelCode())) {
                        info.setHzfcdqr(custKycInfo.getHighrisktoleranceterm());
                    }
                }

                list.add(info);
            }
        }

        return list;
    }



    /**
     * @description:(处理格式化金额, 除以unit. 四舍五入保留2位)
     * @param unit 格式化单位   比如：1w
     * @param amount
     * @return java.math.BigDecimal
     * @author: haoran.zhang
     * @date: 2025/4/3 16:40
     * @since JDK 1.8
     */
    private BigDecimal processUnit(BigDecimal unit, BigDecimal amount){
        return  amount.divide(unit).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 导出操作:当期预约明细
     *
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportCurrentPreDetail.do")
    public void exportCurrentPreDetail(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String mBusiCode = request.getParameter("mBusiCode");
        String calendarId = request.getParameter("calendarId");
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        QueryCurrentPreDetailRequest req = new QueryCurrentPreDetailRequest();
        req.setCalendarId(calendarId);
        req.setOrgcode(orgCode);
        req.setConscode(StringUtils.isBlank(consCode) || "null".equals(consCode) ? null : consCode);

        log.info("导出:preBookService.queryCurrentPreDetailByPage(req)请求:{}", JSON.toJSONString(req));
        QueryCurrentPreDetailResponse res = preBookService.queryCurrentPreDetail(req);
        log.info("导出:preBookService.queryCurrentPreDetailByPage(req)返回:{}", JSON.toJSONString(res));
        List<CurrentPreDetailInfo> list = null;

        if (res != null && BaseConstantEnum.SUCCESS.getCode().equals(res.getReturnCode())) {
            list = res.getCurrentPreDetailInfoList();
        }
        //处理字段值
        List<CurrentPreDetailInfoVo> listVo = handCurrentPreDetailInfo(list, request);
        if (CollectionUtils.isEmpty(listVo)) {
            return;
        }
        for (CurrentPreDetailInfoVo info : listVo) {
            if ("1".equals(info.getFccl()) && info.getTotalamt() != null) {
                info.setTotalamt(processUnit(info.getTotalamt(),BIG_DECIMAL_1W));
            }
            if (info.getZtbuyamt() != null) {
                info.setBuyamt(processUnit(info.getZtbuyamt(),BIG_DECIMAL_1W));
            } else {
                if (info.getBuyamt() != null) {
                    info.setBuyamt(processUnit(info.getBuyamt(),BIG_DECIMAL_1W));
                }
            }
            if (info.getRealpayamt() != null) {
                info.setRealpayamt(processUnit(info.getRealpayamt(),BIG_DECIMAL_1W));
            }
            if (info.getRealfee() != null) {
                info.setRealfee(info.getRealfee().setScale(2, BigDecimal.ROUND_HALF_UP));
            }

        }

        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("当期预约人数.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String[] columnName = null;
            String[] beanProperty = null;
            //购买
            if ("1120".equals(mBusiCode) || "1122".equals(mBusiCode)) {
                columnName = new String[]{"预约ID", "预计交易日期", "客户姓名", "所属投顾", "所属区域", "所属部门", "销售类型", "预约类型", "是否可线上下单", "下单状态", "线上下单时间", "认缴金额(万)",
                        "预约购买金额(万)", "打款状态", "实际打款日期", "实际打款金额(万)", "实际打款手续费", "支付方式", "是否资金匹配", "冷静期截止时间", "折扣状态", "资产证明状态", "证件到期日", "好买风险到期日", "好臻风测到期日"};
                beanProperty = new String[]{"preId", "expecttradedt", "custname", "consname", "uporgname", "outletName", "sfmsjgVal", "pretypeVal", "islineorder", "orderstateVal", "ordertime", "totalamt",
                        "buyamt", "paystateVal", "realpayamtdt", "realpayamt", "realfee", "paymenttypeVal", "finMatchedDesc", "calmdatetime", "discountstateVal", "zczmstate", "validitydt", "hmfcdqr", "hzfcdqr"};
                //赎回
            } else if ("1124".equals(mBusiCode)) {
                columnName = new String[]{"预约ID", "预计交易日期", "客户姓名", "所属投顾", "所属区域", "所属部门", "销售类型", "预约类型", "是否可线上下单", "下单状态", "线上下单时间",
                        "赎回份额", "赎回至", "折扣状态", "资产证明状态", "证件到期日", "好买风险到期日", "好臻风测到期日"};
                beanProperty = new String[]{"preId", "expecttradedt", "custname", "consname", "uporgname", "outletName", "sfmsjgVal", "pretypeVal", "islineorder", "orderstateVal", "ordertime",
                        "sellvol", "redeemdirectionVal", "discountstateVal", "zczmstate", "validitydt", "hmfcdqr", "hzfcdqr"};
            }
            ExcelWriter.writeExcel(os, "当期预约人数", 0, listVo, columnName, beanProperty);
            os.close(); // 关闭流

        } catch (Exception e) {
            log.error("文件导出异常:", e);
        }
    }
}