package com.howbuy.crm.hb.web.controller.associationmail;

import cn.hutool.core.lang.Validator;
import com.howbuy.crm.hb.domain.associationmail.AssociationMail;
import com.howbuy.crm.hb.service.associationmail.AssociationMailService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.ResultCode;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ExcelUtils;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import jxl.Sheet;
import jxl.Workbook;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.mail.Message;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 协会邮件-正常解析
 * Created by shucheng on 2021/5/26 16:47
 */
@Slf4j
@Controller
@RequestMapping("/associationMail")
public class AssociationMailController  extends BaseController {

    private final String DOWNLOAD_FILE_NAME = "协会邮件-正常解析数据导入模板.xls";

    private final String MODEL_FILE_NAME = "AssociationMail.xls";

    @Autowired
    private AssociationMailService associationMailService;

    @RequestMapping("/listAssociationMail.do")
    public ModelAndView listAssociationMail() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/associationmail/listAssociationMail");
        return modelAndView;
    }

    @RequestMapping("/listAssociationMailByPage.do")
    @ResponseBody
    public Map<String, Object> listAssociationMailByPage(HttpServletRequest request) throws Exception {
        Map<String, String> param = new ParamUtil(request).getParamMap();
        String infoStatus = param.get("infoStatus");
        if (infoStatus == null) {
            // 默认查询“待匹配”、“待发短信”、“待重发短信”的类型
            param.put("infoStatus", "0,1,2");
        }
        PageData<AssociationMail> pageData = associationMailService.listAssociationMailByPage(param);
        // 返回查询结果
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        resultMap.put("rows", pageData.getListData());
        return resultMap;
    }

    /**
     * 显示修改页面
     * @return
     */
    @RequestMapping("/editAssociationMail.do")
    public ModelAndView editAssociationMail(HttpServletRequest request, String showSaveBtn) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/associationmail/editAssociationMail");

        String id = request.getParameter("id");
        if (StringUtils.isNotBlank(id)) {
            AssociationMail associationMail = associationMailService.findAssociationMailById(id);
            String consCode = associationMail.getConsCode();
            String orgName = associationMail.getOrgName();
            if (StringUtils.isNotBlank(consCode)) {
                // 所属区域
                String belongRegion = associationMailService.getBelongRegion(consCode, orgName);
                associationMail.setConsRegionName(belongRegion);
                // 所属中心
                String centerName = associationMailService.getBelongCenter(consCode);
                associationMail.setConsCenterName(centerName);
            }
            modelAndView.addObject("associationMail", associationMail);
            if ("1".equals(showSaveBtn)) {
                modelAndView.addObject("showSaveBtn", "1");
            }
        }
        return modelAndView;
    }

    /**
     * 修改正常解析数据
     * @param request
     * @return
     */
    @RequestMapping("/update.do")
    @ResponseBody
    public Map<String, Object> save(HttpServletRequest request, AssociationMail associationMail) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            boolean exist = associationMailService.checkExcelDataExist(associationMail);
            if (exist) {
                resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
                resultMap.put("errorMsg", "操作失败，和已有数据重复");
            } else {
                associationMailService.updateAssociationMail(associationMail);
                resultMap.put("errorCode", ResultCode.RESULT_CODE_SUCCESS);
                resultMap.put("errorMsg", "操作成功");
            }
        } catch (Exception e) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "操作失败");
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }

    /**
     * 批量修改正常解析数据
     * @param request
     * @param idsStr 待修改记录id的集合
     * @param associationMail
     * @return
     */
    @RequestMapping("/batchUpdateStatusRemark.do")
    @ResponseBody
    public Map<String, Object> batchUpdateStatusRemark(HttpServletRequest request, String idsStr, AssociationMail associationMail) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            String[] idArr = idsStr.split(",");
            User user = (User)request.getSession().getAttribute("loginUser");
            if (ArrayUtils.isNotEmpty(idArr)) {
                associationMailService.batchUpdateStatusRemark(Arrays.asList(idArr), associationMail.getInfoStatus(),
                        user.getUserId(), associationMail.getRemark());
            } else {
                throw new RuntimeException();
            }
            resultMap.put("errorCode", ResultCode.RESULT_CODE_SUCCESS);
            resultMap.put("errorMsg", "操作成功");
        } catch (Exception e) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "操作失败");
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }

    /**
     * 导出数据
     * @param request
     * @param response
     */
    @RequestMapping("/exportAssociationMail.do")
    public void exportAssociationMail(HttpServletRequest request, HttpServletResponse response) {
        ServletOutputStream os = null;
        try {
            Map<String, String> param = new ParamUtil(request).getParamMap();
            List<AssociationMail> associationMailList = associationMailService.listAssociationMail(param);

            // 导出数据
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            String fileName = "协会解析数据_正常解析_" + DateTimeUtil.getCurDateTime() + ".xls";
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));

            String [] columnName = new String []{
                    "ID","邮件日期","记录创建日期","发件人邮箱","一账通号","投顾客户号","客户姓名","机构名称","投资者账号","初始密码",
                    "管理人登记编码","登录链接","创建人","修改日期","修改人","邮件主题","收件人邮箱","删除标志","邮件uid","信息状态","备注"
            };

            String [] beanProperty = new String []{
                    "id","mailDateStr","credt","sourceMail","hboneNo","conscustno","custName","orgName","investorAccount","initPassword",
                    "managerRegno","loginHref","creator","moddt","modifier","subject","toMail","delFlag","mailUid","infoStatus","remark"
            };
            os = response.getOutputStream();
            ExcelWriter.writeExcel(os, "协会解析数据_正常解析", 0, associationMailList, columnName, beanProperty);
        } catch (Exception e) {
            log.error("文件导出异常", e);
        } finally {
            // 关闭流
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    @RequestMapping("/downloadImportTemplate.do")
    public String downloadImportTemplate( HttpServletRequest request,
                                    HttpServletResponse response) {
        return dowmloadTemplate(MODEL_FILE_NAME,DOWNLOAD_FILE_NAME,request,response);
    }

    /**
     * 导入数据
     * @param request
     * @return
     */
    @RequestMapping("/importAssociationMail.do")
    @ResponseBody
    public Map<String, Object> importAssociationMail(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        if (file.isEmpty()) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "文件为空");
        } else {
            // 传了文件

            InputStream input = null;
            Workbook workBook = null;
            String errorMsg = "";
            String uploadFlag = "success";
            List<String> successIds = null;
            try {
                List<AssociationMail> insertDataList = new ArrayList<>();
                User user = (User)request.getSession().getAttribute("loginUser");

                // =====================================读取文件数据=====================================
                // 获得输入流：
                input = file.getInputStream();
                workBook = Workbook.getWorkbook(input);
                boolean hasExport = false;

                String[] colProperty = {"mailDateStr","sourceMail","conscustno","custName","orgName","investorAccount","initPassword",
                        "managerRegno","loginHref","subject","toMail","infoStatus","remark"};
                Sheet sheet = workBook.getSheet(0);

                // 获取Excel的13列数据
                List<AssociationMail> excelDataList = ExcelUtils.getListByReadShell(sheet, 1, 0, 13, colProperty, AssociationMail.class);
                if (CollectionUtils.isEmpty(excelDataList)) {
                    errorMsg = "没有上传记录";
                    uploadFlag = "error";
                } else {
                    // =====================================文件字段校验=====================================
                    // 用来构建完整的错误信息
                    StringBuilder errorMessageBuilder = new StringBuilder();
                    // 用来判断excel本身的数据是否重复
                    Set<String> set = new HashSet<>();
                    // excel数据中“关键数据-第1次出现的行号”的对应关系
                    Map<String, Integer> excelDataLineMap = new HashMap<>();

                    int line = 2;
                    for (AssociationMail am : excelDataList) {
                        validateExcelData(insertDataList, user, errorMessageBuilder, line, am, set, excelDataLineMap);

                        line++;
                    }

                    // 导入数据
                    if (!insertDataList.isEmpty()) {
                        //modify by zhangshuai 20210811上传成功的数据标蓝
                        successIds = associationMailService.batchInsertAssociationMailFromExcel(insertDataList);
                        hasExport = true;
                    }

                    if (StringUtils.isBlank(errorMessageBuilder)) {
                        errorMsg = "导入成功！";
                    } else {
                        if (hasExport) {
                            uploadFlag = "partSuccess";
                            errorMsg = "部分导入成功！<br/>" + errorMessageBuilder;
                        } else {
                            uploadFlag = "error";
                            errorMsg = errorMessageBuilder.toString();
                        }
                    }
                }

                resultMap.put("uploadFlag", uploadFlag);
                resultMap.put("errorMsg", errorMsg);
                resultMap.put("successIds", successIds);
            } catch (Exception e) {
                resultMap.put("uploadFlag", "error");
                resultMap.put("errorMsg", "请检查模板是否正确");
                log.error(e.getMessage(), e);
            } finally {
        		if(workBook != null){
        			workBook.close();
        		}
            	try {
            		if(input != null){
            			input.close();
            		}
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return resultMap;
    }

    /**
     * 验证excel数据
     * @param insertDataList 最终能插到数据库的数据list
     * @param user 当前登录用户信息
     * @param errorMessageBuilder 单条excel数据记录的错误信息
     * @param line 当前行号
     * @param am 待校验的excel数据记录
     * @param set 用来判断excel本身的数据是否重复
     * @param excelDataLineMap excel数据中“关键数据-第1次出现的行号”的对应关系
     */
    private void validateExcelData(List<AssociationMail> insertDataList, User user, StringBuilder errorMessageBuilder, int line, AssociationMail am,
                                   Set<String> set, Map<String, Integer> excelDataLineMap) {
        // 用来构建每行的信息
        StringBuilder lineMessageBuilder = new StringBuilder();

        // 邮件日期
        String mailDateStr = am.getMailDateStr();
        if (StringUtils.isBlank(mailDateStr)) {
            lineMessageBuilder.append("邮件日期不能为空！");
        } else if (!isValidDate(mailDateStr, "yyyy-MM-dd HH:mm:ss")) {
            lineMessageBuilder.append("邮件日期格式为yyyy-MM-dd HH:mm:ss(如：2021-03-01 14:02:15)！");
        }

        // 创建日期
        /*String credt = am.getCredt();
        if (StringUtils.isNotBlank(credt) && !isValidDate(credt, "yyyy-MM-dd HH:mm:ss")) {
            lineMessageBuilder.append("创建日期格式为yyyy-MM-dd HH:mm:ss(如：2021-03-01 14:02:15)！");
        }*/


        // 来源邮箱
        String sourceMail = am.getSourceMail();
        if (StringUtils.isBlank(sourceMail)) {
            am.setSourceMail("手动导入");
        }

        // 投顾客户号
        // String conscustno = am.getConscustno();

        // 客户姓名
        String custName = am.getCustName();
        if (StringUtils.isBlank(custName)) {
            lineMessageBuilder.append("客户姓名不能为空！");
        }

        // 机构名称
        String orgName = am.getOrgName();
        if (StringUtils.isBlank(orgName)) {
            lineMessageBuilder.append("机构名称不能为空！");
        }

        // 投资者账号
        String investorAccount = am.getInvestorAccount();
        if (StringUtils.isBlank(investorAccount)) {
            lineMessageBuilder.append("投资者账号不能为空！");
        }

        // 初始密码
        String initPassword = am.getInitPassword();
        if (StringUtils.isBlank(initPassword)) {
            lineMessageBuilder.append("初始密码不能为空！");
        }

        // 管理人登记编码
        String managerRegno = am.getManagerRegno();
        if (StringUtils.isBlank(managerRegno)) {
            lineMessageBuilder.append("管理人登记编码不能为空！");
        }

        // 登录链接
        String loginHref = am.getLoginHref();
        if (StringUtils.isBlank(loginHref)) {
            lineMessageBuilder.append("登录链接不能为空！");
        }

        // 创建人
        /*String creator = am.getCreator();
        if (StringUtils.isBlank(creator)) {
            lineMessageBuilder.append("创建人不能为空！");
        }*/
        am.setCreator(user.getUserId());

        // 主题
        String subject = am.getSubject();
        if (StringUtils.isBlank(subject)) {
            am.setSubject("手动导入");
        }

        // 收件人邮箱
        String toMail = am.getToMail();
        if (StringUtils.isBlank(toMail)) {
            am.setToMail("手动导入");
        }

        // 删除标志
        String delFlag = am.getDelFlag();
        if (StringUtils.isBlank(delFlag)) {
            am.setDelFlag("0");
        }

        // 信息状态
        String infoStatus = am.getInfoStatus();
        if (StringUtils.isNotBlank(infoStatus)) {
            if (!Validator.isMatchRegex("[0123]", infoStatus)) {
                lineMessageBuilder.append("信息状态取值应为0、1、2、3！");
            }
        }

        // 备注
        String remark = am.getRemark();
        if (StringUtils.isNotBlank(remark)) {
            if (StringUtils.length(remark) > 200) {
                lineMessageBuilder.append("备注长度不能超过200字！");
            }
        }

        if (StringUtils.isBlank(lineMessageBuilder)) {
            // 判断该条数据在数据库中是否已经存在
            if (associationMailService.checkExcelDataExist(am)) {
                lineMessageBuilder.append("该条数据已存在！");
            } else {
                // 获取excel本身的重复邮件
                String key = StringUtils.join(new String[]{custName, investorAccount, managerRegno}, "#");
                boolean addSuccess = set.add(key);
                if (!addSuccess) {
                    // 第一次遇到该条数据的位置
                    Integer firstLine = excelDataLineMap.get(key);
                    lineMessageBuilder.append("该条数据和第"+firstLine+"行数据重复！");
                } else {
                    excelDataLineMap.put(key, line);
                }
            }
        }

        if (StringUtils.isNotBlank(lineMessageBuilder)) {
            errorMessageBuilder.append("第"+line+"行错误是：" + lineMessageBuilder + "<br/>");
        } else {
            insertDataList.add(am);
        }
    }

    // 验证是否为有效日期格式
    public static boolean isValidDate(String dateStr, String dateFormat) {
        try {
            SimpleDateFormat format = new SimpleDateFormat(dateFormat);
            format.setLenient(false);
            format.parse(dateStr);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 导入eml压缩包数据
     * @param request
     * @return
     */
    @RequestMapping("/importAssociationEmlZip.do")
    @ResponseBody
    public Map<String, Object> importAssociationEmlZip(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>(2);

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest)request;
        //所有上传文件集合
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        if (fileMap.isEmpty()) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "文件为空");
        } else {
            // 传了文件
            try {
                // =====================================读取文件数据=====================================
                // 将多个文件转换成messageList
                List<Message> messageList = associationMailService.convertMultiFileToMessageList(fileMap);
                if (!CollectionUtils.isEmpty(messageList)) {
                    // 压缩包中有eml文件
                    log.info("开始导入eml压缩包");
                    // 解析并插入邮件数据
                    associationMailService.resolveAndInsertMailData(messageList, resultMap);
                    log.info("结束导入eml压缩包");
                } else {
                    // 压缩包中没有eml文件
                    resultMap.put("uploadFlag", "error");
                    resultMap.put("errorMsg", "导入失败，压缩包中没有eml文件！");
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                resultMap.put("uploadFlag", "error");
                resultMap.put("errorMsg", "导入失败，请检查文件是否正确！");
            }
        }
        return resultMap;
    }
}
