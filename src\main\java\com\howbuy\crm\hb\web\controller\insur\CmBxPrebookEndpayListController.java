package com.howbuy.crm.hb.web.controller.insur;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.consultant.dto.ConsultantAndOrgInfoDTO;
import com.howbuy.crm.hb.domain.custinfo.CmCustconstant;
import com.howbuy.crm.hb.domain.insur.CmBxPrebookBuyWithPayInfo;
import com.howbuy.crm.hb.domain.insur.CmBxPrebookBuyWithPayVo;
import com.howbuy.crm.hb.domain.insur.CmBxPrebookBuyinfo;
import com.howbuy.crm.hb.domain.insur.CmBxPrebookEndpayList;
import com.howbuy.crm.hb.enums.BxCommissionWayEnum;
import com.howbuy.crm.hb.service.custinfo.CmCustconstantService;
import com.howbuy.crm.hb.service.custinfo.CmCustconstanthisService;
import com.howbuy.crm.hb.service.insur.CmBxPrebookBuyinfoService;
import com.howbuy.crm.hb.service.insur.CmBxPrebookEndpayListService;
import com.howbuy.crm.hb.service.insur.CmBxPrebookinfoService;
import com.howbuy.crm.hb.service.reward.CmCustPrpSourceCoeffService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hbconstant.service.HbConstantService;
import com.howbuy.crm.organization.dto.OrgLayerInfo;
import com.howbuy.crm.organization.service.HbOrganizationService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prosale.service.PreBookService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @version 1.0
 * @Description: Controller
 * @created
 */
@Controller
@RequestMapping(value = "/insur")
@Slf4j
public class CmBxPrebookEndpayListController extends BaseController {

	@Autowired
	private CmBxPrebookEndpayListService cmBxPrebookEndpayListService;

	@Autowired
	private PageVisitLogService pageVisitLogService;

	@Autowired
	private CmBxPrebookinfoService cmBxPrebookinfoService;

	@Autowired
	private CmBxPrebookBuyinfoService cmBxPrebookBuyinfoService;

	@Autowired
	private CmCustconstanthisService cmCustconstanthisService;

	@Autowired
	private CmCustconstantService cmCustconstantService;

	@Autowired
	protected PreBookService preBookService;
	@Autowired
	protected CmCustPrpSourceCoeffService cmCustPrpSourceCoeffService;
	@Autowired
	private HbOrganizationService hbOrganizationService;
	@Autowired
	private HbConstantService hbConstantService;

	/**
	 * 跳转到缴费计划页面方法
	 *
	 * @param request
	 * @return ModelAndView
	 * @throws ParseException
	 */
	@RequestMapping("/listCmBxPrebookEndpayList.do")
	public ModelAndView listCmBxPrebookEndpayList(HttpServletRequest request) throws ParseException {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/insur/listCmBxPrebookEndpayList");
		Map<String,String> map = DateTimeUtil.getMonthFirstLastday(DateTimeUtil.fmtDate(new Date(),"yyyyMM"));
		modelAndView.addObject("map", map);
		return modelAndView;
	}

	/**
	 * 加载页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listCmBxPrebookEndpayListByPage_json.do")
	public Map<String, Object> listCmBxPrebookEndpayListByPageJson(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		PageData<CmBxPrebookEndpayList> pageData = cmBxPrebookEndpayListService.listCmBxPrebookEndpayListByPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmBxPrebookEndpayList> listdata = pageData.getListData();
		Map<String,String> allConsMap = ConsOrgCache.getInstance().getAllConsMap();
		List<String> orgCodes = listdata.stream().map(CmBxPrebookEndpayList::getOrgcode).collect(Collectors.toList());
		//组织架构所属中心
		Map<String, OrgLayerInfo> orgLayerInfoMap = hbOrganizationService.getOrgLayerInfoByOrgCodeList(orgCodes);
		//先取出要用到的常量
		Map<String,String> currencysConstantMap = hbConstantService.getHbConstantMap("currencys");
		Map<String,String> insurpaystateallConstantMap = hbConstantService.getHbConstantMap("insurpaystateall");
		Map<String,String> insurpayyearsConstantMap = hbConstantService.getHbConstantMap("insurpayyears");
		Map<String,String> insurpaycheckstateConstantMap = hbConstantService.getHbConstantMap("insurpaycheckstate");
		for (CmBxPrebookEndpayList info : listdata) {
			if(StringUtil.isNotNullStr(info.getPaystatedetail()) && StaticVar.INSUR_PAYSTAT_NOTPAY.equals(info.getPaystate())){
				info.setPaystateval(insurpaystateallConstantMap.get(info.getPaystatedetail()));
			}else{
				info.setPaystateval(insurpaystateallConstantMap.get(info.getPaystate()));
			}
			info.setPayyears(insurpayyearsConstantMap.get(info.getPayyears()));
			if(StringUtil.isNotNullStr(info.getCheckstate())){
				info.setCheckstateval(insurpaycheckstateConstantMap.get(info.getCheckstate()));
			}
			log.info("calConsCode:{} conscustno:{} realpaydt:{}", info.getCalconscode(), info.getConscustno(), info.getRealpaydt());
			//算绩效投顾
			String calConsCode = cmBxPrebookinfoService.getCalConsCode(info.getCalconscode(), info.getConscustno(), info.getRealpaydt());
			log.info(calConsCode);
			info.setCalconscode(allConsMap.get(calConsCode));
			OrgLayerInfo orgLayerInfo = orgLayerInfoMap.get(info.getOrgcode());
			if(orgLayerInfo != null) {
				info.setCenterOrgName(orgLayerInfo.getCenterOrgName());
				info.setUporgname(orgLayerInfo.getDistrictName());
			}
			info.setCurrency(currencysConstantMap.get(info.getCurrency()));
		}
		resultMap.put("rows", listdata);
		return resultMap;
	}


	/**
	 * 查询 预约单缴款计划明细表
	 * @param buyId
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listPrebookEndpay.do")
	public List<Map<String,Object>> listPrebookEndpay(String buyId){
//		该保单该产品在缴款计划中缴款状态 = 已缴清 且 复核状态 = 已复核
		List<Map<String,Object>> listMap= Lists.newArrayList();

		//首年缴费
		Map<String, Object> parambuyinfo = new HashMap<>(1);
		parambuyinfo.put("id", new BigDecimal(buyId));
		//CM_BX_PREBOOK_BUYINFO 表存的佣金是首年的
		CmBxPrebookBuyinfo cmBxPrebookBuyinfo = cmBxPrebookBuyinfoService.getCmBxPrebookBuyinfoWithBxCommissionWay(parambuyinfo);
		//CmBxPrebookBuyinfo cmBxPrebookBuyinfo = cmBxPrebookBuyinfoService.getCmBxPrebookBuyinfo(parambuyinfo);
        if(cmBxPrebookBuyinfo!=null){
			Map<String,Object> map=Maps.newHashMap();
			map.put("commissionDt",cmBxPrebookBuyinfo.getCommissiondt());
			map.put("commission",cmBxPrebookBuyinfo.getCommission());
			listMap.add(map);
		}

		//缴款计划
		CmBxPrebookBuyWithPayVo vo=new CmBxPrebookBuyWithPayVo();
		vo.setBuyId(new BigDecimal(buyId));
		vo.setPayState(StaticVar.INSUR_PAYSTAT_HASPAY);//已付款
		vo.setPage(1);
		vo.setRows(5000);
		PageData<CmBxPrebookBuyWithPayInfo> endPayPage=cmBxPrebookinfoService.listCmBxPrebookBuyWithPayInfoByPage( vo);
		endPayPage.getListData().stream().forEach(payInfo->{
			BigDecimal commission = BigDecimal.ZERO;
			if(BxCommissionWayEnum.BXCOMMISSIONWAY_ONE.getCode().equals(payInfo.getBxcommissionway())){
				commission = payInfo.getCommission();
			}else if(BxCommissionWayEnum.BXCOMMISSIONWAY_TWO.getCode().equals(payInfo.getBxcommissionway())){
				commission = payInfo.getCommissionfortwo();
			}
			Map<String,Object> map=Maps.newHashMap();
			map.put("commissionDt",payInfo.getCommissionDt());
			map.put("commission", commission);
			listMap.add(map);
		});
		return listMap;
	}
    
    /**
	 * 修改单个缴费计划
	 * @param request
	 * @return String
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/updateCmBxPrebookEndpayList.do")
	public String updateCmBxPrebookEndpayList(HttpServletRequest request) throws Exception{
		String result = "";
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		String id = request.getParameter("id");
		String conscustNo = request.getParameter("conscustno");
		String paystate = request.getParameter("paystate");
		String realPayDt = request.getParameter("realPayDt");
		String payAmt = request.getParameter("payAmt");
		if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(paystate)) {
			CmBxPrebookEndpayList info = new CmBxPrebookEndpayList();
			info.setId(new BigDecimal(id));
			info.setPaystate(paystate);
			if (StaticVar.INSUR_PAYSTAT_HASPAY.equals(paystate)) {
				info.setPaystatedetail("");
			}
			if (StringUtil.isNotNullStr(realPayDt)) {
				info.setRealpaydt(realPayDt);
			} else {
				info.setRealpaydt("");
			}
			if (StringUtils.isNotBlank(payAmt)) {
				info.setPayamt(new BigDecimal(payAmt));
			}
			info.setDealdt(new Date());
			info.setModifier(userlogin.getUserId());
			info.setModifydt(new Date());
			if (StringUtils.isNotEmpty(conscustNo)) {
				//根据客户号查询当前投顾和绩效投顾 判断是否相同
				info.setManagePoint(ifCurConsEqPerformCons(conscustNo, realPayDt) ? getManagePointByCusNo(conscustNo) : "");
			}
			cmBxPrebookEndpayListService.updateCmBxPrebookEndpayList(info);
			// 更新缴款计划中的“绩效汇率”字段
			cmBxPrebookBuyinfoService.updateEndPayRate(new BigDecimal(id));

			result = "success";
		} else {
			result = "paramError";
		}
		return result;
	}

	private boolean ifCurConsEqPerformCons(String custNo, String realPayDt) {
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("custno", custNo);
		CmCustconstant cmCustconstant = cmCustconstantService.getCmCustconstant(paramMap);
		String curConsCode = "";
		if (cmCustconstant != null) {
			curConsCode = cmCustconstant.getConscode();
		}
		String performCons = cmCustconstanthisService.getPerformanceConsByCustNoAndRealDt(custNo, realPayDt);
		log.info("[CmBxPrebookEndpayListController.ifCurConsEqPerformCons]获取{}当前投顾{}，绩效投顾{}", custNo, curConsCode, performCons);
		return Objects.equals(performCons, curConsCode);
	}

	/**
	 * 复核单个缴费计划
	 *
	 * @param request
	 * @return String
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/checkCmBxPrebookEndpayList.do")
	public String checkCmBxPrebookEndpayList(HttpServletRequest request) throws Exception {
		String result = "";
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		String id = request.getParameter("id");
		if (StringUtils.isNotBlank(id)) {
			CmBxPrebookEndpayList info = new CmBxPrebookEndpayList();
			info.setId(new BigDecimal(id));
			info.setCheckdt(new Date());
			info.setCheckstate("2");
			info.setCheckor(userlogin.getUserId());
			info.setModifier(userlogin.getUserId());
			info.setModifydt(new Date());
			cmBxPrebookEndpayListService.updateCmBxPrebookEndpayList(info);
			// 更新缴款计划中的“绩效汇率”字段
			cmBxPrebookBuyinfoService.updateEndPayRate(new BigDecimal(id));
			result = "success";
		} else {
			result = "paramError";
		}
		return result;
	}
	
	/**
	 * 导出操作
	 * @param request
	 * @param response
	 * @throws Exception
	 */
    @ResponseBody
    @RequestMapping("/exportEndPayPlay.do")
    public void exportEndPayPlay(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
        Map<String, String> param = new ParamUtil(request).getParamMap();
        List<CmBxPrebookEndpayList> exportList = cmBxPrebookEndpayListService.listCmBxPrebookEndpayListByExp(param);
        List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
		User user = (User) request.getSession().getAttribute("loginUser");
		String ip = getIpAddr(request);
        for (CmBxPrebookEndpayList info : exportList) {
			ConsOrgCache orgcache = ConsOrgCache.getInstance();
			String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
			if("0".equals(uporgcode)){
				info.setUporgname(orgcache.getAllOrgMap().get(info.getOrgcode()));
			}else{
				info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
			}
        	if(StringUtil.isNotNullStr(info.getPaystatedetail()) && StaticVar.INSUR_PAYSTAT_NOTPAY.equals(info.getPaystate())){
				info.setPaystateval(ConstantCache.getInstance().getVal("insurpaystateall", info.getPaystatedetail()));
			}else{
				info.setPaystateval(ConstantCache.getInstance().getVal("insurpaystateall", info.getPaystate()));
			}
			info.setPayyears(ConstantCache.getInstance().getVal("insurpayyears", info.getPayyears()));
			if(StringUtil.isNotNullStr(info.getCheckstate())){
				info.setCheckstateval(ConstantCache.getInstance().getVal("insurpaycheckstate", info.getCheckstate()));
			}
			//导出日志
            PageVisitLog pageVisitLog = new PageVisitLog();
            pageVisitLog.setConscustno(info.getConscustno());
            pageVisitLog.setUserId(user.getUserId());
            pageVisitLog.setVisitUrl(request.getRequestURI());
            pageVisitLog.setOperation("缴款计划导出");
            pageVisitLog.setVisitTime(new Date());
            pageVisitLog.setPreid(info.getId());
            pageVisitLog.setIp(ip);
            listlog.add(pageVisitLog);
        }
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("缴费计划导出.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String [] columnName = new String []{"区域（当前）","部门（当前）","投顾（当前）","投保客户号","投保人","受保人","产品名称","签单日期","缴费年限","截止缴款日期","缴款金额","缴费状态","实际缴款日期","最近维护缴费日期","复核状态","最近复核日期","保单号"};

            String [] beanProperty = new String []{"uporgname","orgname","consname","conscustno","custname","insurname","fundname","signdt","payyears","paydt","yearamk","paystateval","realpaydt","dealdtStr","checkstateval","checkdtStr","insurid"};
            ExcelWriter.writeExcel(os, "缴费计划", 0, exportList, columnName, beanProperty);
            // 关闭流
            os.close(); 
            for(PageVisitLog log : listlog){
            	pageVisitLogService.recordLog(log);
            }
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }

    }
    
    /**
	 * 获取客户的IP
	 *
	 * @param request
	 * @return
	 */
	private String getIpAddr(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			ip = request.getRemoteAddr();
		}
		return ip;
	}


	/**
	 * @param custNo 客户号
	 * @return
	 * @description 根据客户号获取对应管理系数
	 * @author: chao.chen01
	 * @date: 2023/9/5 下午4:38
	 * @since JDK 1.8
	 */
	public String getManagePointByCusNo(String custNo) {
		String sourceType = preBookService.getCustSourceType(custNo, DateTimeUtil.getCurDate());
		if (StringUtils.isNotEmpty(sourceType)) {
			return cmCustPrpSourceCoeffService.getManageCoeffBySourceType(sourceType);
		}
		return "";
	}
}