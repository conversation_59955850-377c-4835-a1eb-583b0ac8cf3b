package com.howbuy.crm.hb.web.controller.product;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.hb.domain.product.ProductCheckInfo;
import com.howbuy.crm.hb.service.product.ProductInfoCheckService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.web.util.Util;

import crm.howbuy.base.db.CommPageBean;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.howbuy.crm.hb.web.util.ParamUtil;
import com.howbuy.crm.page.cache.ConstantCache;

import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ProductInfoCheckController.java
 * @Description TODO
 * @createTime 2021年10月26日 09:28:00
 */
@Controller
@RequestMapping(value = "/proCheck")
public class ProductInfoCheckController {

    public static final String ROLE_CEO="ROLE_CEO";

    public static final String ROLE_PD_HEAD="ROLE_PD_HEAD";//产品总监

    @Autowired
    private ProductInfoCheckService productInfoCheckService;
    
    @Autowired
    private PrebookproductinfoService prebookproductinfoService;

    @RequestMapping(value="/toProInfoListView.do")
    public String toProductInfoList(HttpServletRequest request){
        return "product/productInfoCheckList";
    }

    @RequestMapping(value="/proInfoList.do")
    @ResponseBody
    public Map<String,Object> productInfoList(HttpServletRequest request) throws Exception{
        Map<String,Object> resultMap = new HashMap<String, Object>();
        Map<String,String> paramMap = new HashMap<String, String>();
        paramMap = new ParamUtil(request).getParamMap();

        // 判断常量表中合规标识：true启用，false停用
        LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
        boolean roleCPFlag = false;
        if (cacheMap != null && !cacheMap.isEmpty()) {
            roleCPFlag = JSON.toJSONString(cacheMap).contains("true");
        }

        // 判断登录人员的角色中是否包括“合规人员”角色
        List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
        if (roleCPFlag || loginRoles.contains("ROLE_CP")) {
            paramMap.put("hascp", "true");
        }
        PageData<ProductCheckInfo> productInfoList = productInfoCheckService.getProductInfoList(paramMap);
        List<ProductCheckInfo> listData = productInfoList.getListData();
        ConstantCache instance = ConstantCache.getInstance();
        LinkedHashMap<String, String> hbtypeMap = instance.getConstantKeyVal("hmcpxs");
        LinkedHashMap<String, String> saleStateMap = instance.getConstantKeyVal("xszts");
        LinkedHashMap<String, String> proStateMap = instance.getConstantKeyVal("cpzts");
        LinkedHashMap<String, String> preStateMap = instance.getConstantKeyVal("yyzts");
        LinkedHashMap<String, String> operStateMap = instance.getConstantKeyVal("czlxs");
        LinkedHashMap<String, String> checkStateMap = instance.getConstantKeyVal("shzts");
        for (ProductCheckInfo info : listData) {
            String hbtype = info.getHbtype();
            String saleState = info.getSaleState();
            String proState = info.getProductState();
            String preState = info.getPrebookState();
            String checkflag = info.getCheckflag();
            String operType = info.getOperationType();
            if(StringUtils.isNotBlank(checkflag)){
                info.setCheckflagStr(checkStateMap.get(checkflag));
            }

            if(StringUtils.isNotBlank(operType)){
                info.setOperationType(operStateMap.get(operType));
            }

            if(StringUtils.isNotBlank(hbtype)){
                info.setHbtype(hbtypeMap.get(hbtype));
            }

            if(StringUtils.isNotBlank(saleState)){
                info.setSaleState(saleStateMap.get(saleState));
            }

            if(StringUtils.isNotBlank(proState)){
                info.setProductState(proStateMap.get(proState));
            }

            if(StringUtils.isNotBlank(preState)){
                info.setPrebookState(preStateMap.get(preState));
            }
        }
        CommPageBean pageBean = productInfoList.getPageBean();
        resultMap.put("rows", listData);
        resultMap.put("total", pageBean.getTotalNum());
        return resultMap;
    }

    /**
     * 自动补全待审核的私募产品方法
     *
     */
    @ResponseBody
    @RequestMapping("/autoCompleteCheckPrvFund.do")
    public Map<String, List<FundCode>> autoCompleteCheckPrvFund(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        String searchParam = request.getParameter("term");
        param.put("searchParam", searchParam.toUpperCase());
        List<ProductCheckInfo> listProductinfo = productInfoCheckService.listProductinfoBySearch(param);
        List<FundCode> list = new ArrayList<FundCode>();
        for(ProductCheckInfo info : listProductinfo){
            FundCode fc = new FundCode();
            fc.setCode(Util.ObjectToString(info.getPcode()));
            fc.setName(Util.ObjectToString(info.getPname()));
            //提取首字母
            fc.setPinyin(Util.ObjectToString(info.getJjpy()));
            list.add(fc);
        }
        Map<String,List<FundCode>> result = new HashMap<String,List<FundCode>>();
        result.put("result", list);
        return result;
    }

    @RequestMapping(value="/toCheckProInfoDetail.do")
    public ModelAndView toCheckProInfoDetail(String checkId){
        Map<String,String> paramMap = new HashMap<String, String>();
        paramMap.put("checkId", checkId);
        ProductCheckInfo proDetail = productInfoCheckService.getProductInfoDetail(paramMap);
        //支持下单方式
        handleSupportPayType(proDetail);
        ModelAndView model = new ModelAndView("product/productInfoCheckDetailCheck", "info", proDetail);
        return model;
    }

    private void handleSupportPayType(ProductCheckInfo proDetail){
    	if(proDetail != null){
    		//支持下单方式
            List<String> listPayType = prebookproductinfoService.listSupportPayType(proDetail.getPcode());
            proDetail.setSupportPayType(CollectionUtils.isEmpty(listPayType)? "" : StringUtils.join(listPayType,"/"));
    	}
    }
    
    @RequestMapping(value="/proInfoDetail.do")
    public ModelAndView proInfoDetail(String checkId){
        Map<String,String> paramMap = new HashMap<String, String>();
        paramMap.put("checkId", checkId);
        ProductCheckInfo proDetail=productInfoCheckService.getProductInfoDetail(paramMap);
        //支持下单方式
        handleSupportPayType(proDetail);
        ModelAndView model = new ModelAndView("product/productInfoCheckDetail", "info", proDetail);
        return model;
    }

    
    //修改产品状态
    @RequestMapping(value="/updProInfoState.do")
    @ResponseBody
    public String updProInfoDetail(String checkId,String pcode,String remark,String checkFlag,HttpServletRequest request){
        HttpSession session = request.getSession();
        String userId=(String) session.getAttribute("userId");
        List<String> listroles = (List<String>) session.getAttribute("loginRoles");
        boolean director =false;
        for (String role : listroles) {

            if(ROLE_PD_HEAD.equals(role)){
                director =true;
                break;
            }

            if(ROLE_CEO.equals(role)){
                director =true;
                break;
            }
        }

        Map<String,Object> paramMap = new HashMap<String, Object>();
        paramMap.put("checkId", checkId);
        paramMap.put("pcode", pcode);
        paramMap.put("checkFlag", checkFlag);
        paramMap.put("remarkes", remark);
        paramMap.put("director", director);
        paramMap.put("checker", userId);
        String result=productInfoCheckService.checkProductInfoList(paramMap);
        return result;
    }

    //修改产品
    @RequestMapping(value="/toEditProInfoDetail.do")
    public ModelAndView toEditProInfoDetail(String checkId){
        Map<String,String> paramMap = new HashMap<String, String>();
        paramMap.put("checkId", checkId);
        ProductCheckInfo proDetail=productInfoCheckService.getProductInfoDetail(paramMap);
        //支持下单方式
        handleSupportPayType(proDetail);
        ModelAndView model = new ModelAndView("product/productInfoCheckDetailEditor","info" , proDetail);
        return model;
    }

    @ResponseBody
    @RequestMapping(value="/editProInfoDetail.do")
    public String editProInfoDetail(ProductCheckInfo check,HttpServletRequest request){
        String result ="";
        HttpSession session = request.getSession();
        String userId=(String)session.getAttribute("userId");
        check.setModifier(userId);
        check.setModdt(DateTimeUtil.fmtDate(new Date(), "yyyyMMdd"));
        result = productInfoCheckService.updateProductCheckInfo(check);
        return result;
    }
}
