package com.howbuy.crm.hb.web.controller.doubletrade;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @Description: 回访自动上传线程池
 * @reason:
 * @Date: 2021/2/15 14:39
 */
@Slf4j
public class VisitRecUploadExecutors extends ThreadPoolExecutor {
    private static final int FIX_THREADS_NUM = 20;

    private static ExecutorService executorService;

    public VisitRecUploadExecutors(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
    }

    public static ExecutorService getExecutors() {
        if(executorService == null){
            executorService = new VisitRecUploadExecutors(FIX_THREADS_NUM, FIX_THREADS_NUM,
                    60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());
            //允许核心线程超时
            ((VisitRecUploadExecutors) executorService).allowCoreThreadTimeOut(true);
        }
        return executorService;
    }

    /**
     * 每次执行任务前调用
     */
    @Override
    protected void beforeExecute(Thread t, Runnable r) {
        monitor("before");
    }

    /**
     * 每次任务完成后调用
     */
    @Override
    protected void afterExecute(Runnable r, Throwable t) {
        monitor("after");
    }

    /**
     * 线程池关闭前调用
     */
    @Override
    protected void terminated() {
        monitor("terminated");
    }

    /**
     * 监控线程池情况
     */
    public void monitor(String method) {
        String logMsg = "回访自动上传线程池" + method + " 正在工作的线程数：" + getActiveCount()
                +"  当前存在的线程数：" + getPoolSize()
                +"  历史最大的线程数：" + getLargestPoolSize()
                +"  已提交的任务数：" + getTaskCount()
                +"  已完成的任务数：" + getCompletedTaskCount()
                +"  队列中的任务数：" + getQueue().size();
        log.info(logMsg);
    }
}
