package com.howbuy.crm.hb.web.controller.pushmsg;

import com.howbuy.crm.hb.domain.pushmsg.CmMsgBusinessTemplate;
import com.howbuy.crm.hb.domain.pushmsg.CmMsgBusinessTemplateParam;
import com.howbuy.crm.hb.domain.pushmsg.CmMsgType;
import com.howbuy.crm.hb.domain.pushmsg.CmMsgWeixinTemplate;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.pushmsg.CmMsgBusinessTemplateService;
import com.howbuy.crm.hb.service.pushmsg.CmMsgTypeService;
import com.howbuy.crm.hb.service.pushmsg.CmMsgWeixinTemplateService;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2021/1/14 16:42
 */
@RequestMapping("/businessTemplate")
@Controller
public class CmMsgBusinessTemplateController {

    @Autowired
    private CmMsgBusinessTemplateService cmMsgBusinessTemplateService;
    @Autowired
    private CmMsgWeixinTemplateService cmMsgWeixinTemplateService;
    @Autowired
    private CmMsgTypeService cmMsgTypeService;
    @Autowired
    private CmMsgTypeController cmMsgTypeController;
    @Autowired
    private CommonService commonService;

    @RequestMapping("/listBusinessTemplate")
    public String listBusinessTemplate(Map map) throws Exception{
        List<CmMsgType> masterTypeList = cmMsgTypeService.listMasterType(true);
        List<CmMsgType> subTypeList = cmMsgTypeService.listSubTypeByMasterId(null);
        map.put("masterTypeList", cmMsgTypeController.getComboList(masterTypeList, true));
        map.put("subTypeList", cmMsgTypeController.getComboList(subTypeList, true));
        return "/pushmsg/listBusinessTemplate";
    }

    @RequestMapping("/listBusinessTemplate_json")
    @ResponseBody
    public Object listBusinessTemplate_json(HttpServletRequest request) throws Exception{
        Map<String, String> param = new ParamUtil(request).getParamMap();
        PageData<CmMsgBusinessTemplate> pageData = cmMsgBusinessTemplateService.listBusinessTemplateByPage(param);
        Map<String, Object> resultMap = new HashMap(3);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        resultMap.put("rows", pageData.getListData());
        return resultMap;
    }

    @RequestMapping("/editTemplate")
    public String editTemplate(String businessId, Map map){
        if(businessId != null){
            CmMsgBusinessTemplate cmMsgBusinessTemplate = cmMsgBusinessTemplateService.getBusinessTemplate(businessId);
            map.put("businessTemplate", cmMsgBusinessTemplate);
            map.put("businessId", cmMsgBusinessTemplate.getBusinessId());
        }else {
            map.put("businessId", commonService.getSeqValue("SEQ_CM_MSG_BUSINESS_ID"));
        }
        List<CmMsgType> masterTypeList = cmMsgTypeService.listMasterType(true);
        List<CmMsgType> subTypeList = cmMsgTypeService.listSubTypeByMasterId(null);
        List<CmMsgWeixinTemplate> weixinTemplates = cmMsgWeixinTemplateService.listWeixinTemplateAll(null);
        map.put("weixinTemplates", weixinTemplates == null ? null : getComboList(weixinTemplates));
        map.put("masterTypeList", cmMsgTypeController.getComboList(masterTypeList, false));
        map.put("subTypeList", cmMsgTypeController.getComboList(subTypeList, false));
        return "/pushmsg/editBusinessTemplate";
    }

    @RequestMapping("/viewTemplate")
    public String viewTemplate(String businessId, Map map){
        CmMsgBusinessTemplate cmMsgBusinessTemplate = cmMsgBusinessTemplateService.getBusinessTemplate(businessId);
        map.put("businessTemplate", cmMsgBusinessTemplate);
        map.put("serial", System.currentTimeMillis());
        return "/pushmsg/viewBusinessTemplate";
    }

    @ResponseBody
    @RequestMapping("/saveTemplate")
    public Object saveTemplate(HttpServletRequest request){
        String userId = (String)request.getSession().getAttribute("userId");
        String businessId = request.getParameter("businessId");
        String businessName = request.getParameter("businessName");
        int count = cmMsgBusinessTemplateService.selectRepeatBusinessName(businessName, businessId);
        if(count > 0){
            return "repeat";
        }
        CmMsgBusinessTemplate cmMsgBusinessTemplate = new CmMsgBusinessTemplate();
        cmMsgBusinessTemplate.setBusinessId(businessId);
        cmMsgBusinessTemplate.setBusinessName(businessName);
        cmMsgBusinessTemplate.setMsgTypeId(request.getParameter("msgTypeId"));
        String isPcTemplate = request.getParameter("isPcTemplate");
        if(StaticVar.YES_OR_NO_YES.equals(isPcTemplate)) {
            cmMsgBusinessTemplate.setIsPcTemplate(isPcTemplate);
            //cmMsgBusinessTemplate.setPcIsPopup(request.getParameter("pcIsPopup"));
            cmMsgBusinessTemplate.setPcTemplateText(request.getParameter("pcTemplateText"));
            cmMsgBusinessTemplate.setPcUrl(request.getParameter("pcUrl"));
        }else if(StaticVar.YES_OR_NO_NO.equals(isPcTemplate)){
            cmMsgBusinessTemplate.setIsPcTemplate(StaticVar.YES_OR_NO_NO);
        }
        List<CmMsgBusinessTemplateParam> list = null;
        String isWeixinTemplate = request.getParameter("isWeixinTemplate");
        if(StaticVar.YES_OR_NO_YES.equals(isWeixinTemplate)) {
            cmMsgBusinessTemplate.setIsWeixinTemplate(isWeixinTemplate);
            cmMsgBusinessTemplate.setWeixinTemplateId(request.getParameter("weixinTemplateId"));
            cmMsgBusinessTemplate.setWeixinStartText(request.getParameter("weixinStartText"));
            cmMsgBusinessTemplate.setWeixinEndText(request.getParameter("weixinEndText"));
            cmMsgBusinessTemplate.setWeixinUrl(request.getParameter("weixinUrl"));
            list = getTemplateParam(request, cmMsgBusinessTemplate.getBusinessId(), userId);
        }else if(StaticVar.YES_OR_NO_NO.equals(isWeixinTemplate)) {
            cmMsgBusinessTemplate.setIsWeixinTemplate(StaticVar.YES_OR_NO_NO);
        }
        cmMsgBusinessTemplateService.saveBusinessAndParam(cmMsgBusinessTemplate, list, StringUtil.isNotNullStr(request.getParameter("isUpdate")) ? true : false, userId);
        return "success";
    }

    private List<CmMsgBusinessTemplateParam> getTemplateParam(HttpServletRequest request, String businessId, String userId){
        Enumeration enumeration = request.getParameterNames();
        List<CmMsgBusinessTemplateParam> list = new ArrayList<>();
        while (enumeration.hasMoreElements()){
            String name = (String) enumeration.nextElement();
            CmMsgBusinessTemplateParam cmMsgBusinessTemplateParam = new CmMsgBusinessTemplateParam();
            if(name.contains("paramName")) {
                String paramName = request.getParameter(name);
                Integer sort = Integer.valueOf(name.substring(9));
                cmMsgBusinessTemplateParam.setParamName(paramName);
                cmMsgBusinessTemplateParam.setParamValue(request.getParameter("paramValue" + sort));
                cmMsgBusinessTemplateParam.setBusinessId(businessId);
                cmMsgBusinessTemplateParam.setCreator(userId);
                cmMsgBusinessTemplateParam.setSort(sort);
                list.add(cmMsgBusinessTemplateParam);
            }
        }
        return list;
    }

    @ResponseBody
    @RequestMapping("/deleteTemplate")
    public Object deleteTemplate(HttpServletRequest request){
        String userId = (String)request.getSession().getAttribute("userId");
        CmMsgBusinessTemplate cmMsgBusinessTemplate = new CmMsgBusinessTemplate();
        cmMsgBusinessTemplate.setBusinessId(request.getParameter("businessId"));
        cmMsgBusinessTemplate.setIsDel(StaticVar.YES_OR_NO_YES);
        cmMsgBusinessTemplate.setUpdateMan(userId);
        cmMsgBusinessTemplateService.update(cmMsgBusinessTemplate);
        return "success";
    }

    public List<Map> getComboList(List<CmMsgWeixinTemplate> weixinTemplates){
        List<Map> list = new ArrayList<>(weixinTemplates.size());
        for(CmMsgWeixinTemplate cmMsgWeixinTemplate : weixinTemplates){
            Map map = new HashMap(3);
            map.put("id", cmMsgWeixinTemplate.getId());
            map.put("text", cmMsgWeixinTemplate.getTitle());
            list.add(map);
        }
        return list;
    }
}
