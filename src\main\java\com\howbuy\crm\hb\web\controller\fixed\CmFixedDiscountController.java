package com.howbuy.crm.hb.web.controller.fixed;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.hb.domain.fixed.CmFixedIntention;
import com.howbuy.crm.hb.service.callout.VconscustService;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.fixed.CmFixedIntentionService;
import com.howbuy.crm.hb.service.prosale.DiscountappService;
import com.howbuy.crm.hb.service.system.HbUserroleService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prosale.dto.CmDiscountLog;
import com.howbuy.crm.prosale.dto.Discountapp;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.interlayer.product.model.HighActiDiscountRatioModel;
import com.howbuy.interlayer.product.service.HighProductService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * 
 * <AUTHOR>
 *
 */
@Slf4j
@Controller
@RequestMapping(value = "/fixed")
public class CmFixedDiscountController  extends BaseController {
	
	private Logger logger= LoggerFactory.getLogger(CmFixedDiscountController.class);
	
	@Autowired
	private CmFixedIntentionService cmFixedIntentionService;

	@Autowired
	private ConscustService conscustService;
	
	@Autowired
	private PreBookService preBookService;
	
	@Autowired
    private HighProductService highProductService;

    @Autowired
    private VconscustService vconscustService;

    @Autowired
    private HbUserroleService hbUserroleService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private DiscountappService discountappService;
    /**
     * 申请折扣
     * @param request
     * @param map
     * @return
     */
    @RequestMapping("/applyDiscount.do")
    public String applyDiscount(HttpServletRequest request,Map<String,Object> map){
        String id = request.getParameter("id");
        Map<String,Object> param = new HashMap<String,Object>(1);
        param.put("planid", id);
        CmFixedIntention info = cmFixedIntentionService.getCmFixedIntention(param);
        map.put("planid", id);
        map.put("fundname", info.getFundname());
        map.put("hasAuthOverDt", "1");
        map.put("conscustname", info.getCustname());
        map.put("conscustno", info.getConscustno());
        map.put("currencytype", StaticVar.CURRENCY_RMB);
        map.put("currency", ConstantCache.getInstance().getVal("currencys", StaticVar.CURRENCY_RMB));
        BigDecimal buyamt = info.getPlanamount();
        BigDecimal exchangerate = new BigDecimal("1.0");
        BigDecimal buyamtnew = buyamt.divide(BIG_DECIMAL_1W, 2, BigDecimal.ROUND_HALF_UP);
        logger.info("定投意向单获取费率客户号："+info.getConscustno()+",产品代码："+info.getFundcode()+",购买金额："+buyamtnew);
        BigDecimal rate = preBookService.getRateFee(info.getConscustno(),info.getFundcode(), buyamtnew);
        logger.info("定投意向单获取费率："+rate);
        //BigDecimal rate = new BigDecimal("0.01");
        map.put("feeRate", rate.setScale(4, BigDecimal.ROUND_HALF_UP).toPlainString());
        map.put("exchangerate", exchangerate.setScale(4, BigDecimal.ROUND_HALF_UP).toPlainString());
        // 调用获取异常次数方法
        map.put("buyamt", buyamt.toPlainString());
        map.put("outletName", ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
        map.put("conscode", ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
        map.put("plantotalnum", info.getPlantotalnum());
        map.put("planrate", ConstantCache.getInstance().getVal("fixedPlanRate", info.getPlanrate()));
        map.put("paytype", ConstantCache.getInstance().getVal("fixedpaytype", info.getPaytype()));
        try {
            map.put("canDiscount", checkCanDiscount(info.getConscustno(),info.getFundcode()));
        }catch (Exception e){
            log.error(e.getMessage(), e);
            map.put("canDiscount", "error");
        }
        ConstantCache constantCache = ConstantCache.getInstance();
        Map<String,String> discountStylesMap = constantCache.getConstantKeyVal("discountStyles");
        Map<String,String> discountWaysMap = constantCache.getConstantKeyVal("discountWays");
        Map<String,String> discountTypesMap = constantCache.getConstantKeyVal("discountTypes");
        Map<String,String> staffRelationsMap = constantCache.getConstantKeyVal("staffrelations");
        map.put("discountStyles",buildSelectMap(discountStylesMap,null));
        //排除工资
        map.put("discountStylesExcludeSalary",buildSelectMap(discountStylesMap,"3"));
        map.put("discountWays",buildSelectMap(discountWaysMap,null));
        //直接少汇排除MGM员工介绍
        map.put("discountTypes",buildSelectMap(discountTypesMap,"7"));
        //好买返回排除正常折扣
        map.put("discountTypesReturn",buildSelectMap(discountTypesMap,"4"));
        //员工关系
        map.put("staffRelations",buildSelectMap(staffRelationsMap,null));
        //折扣计算金额
        map.put("calculateAppAmt",info.getPlanamount());
        
        return "fixed/applyDiscount";
    }
    
    /**
     * checkCanDiscount
     * @param id
     * @return java.lang.String
     * @Author: yu.zhang on 2021/6/17 11:11
     */
    @RequestMapping("/checkCanDiscount.do")
    @ResponseBody
    public String checkCanDiscount(String conscustno,String pcode){
        String dicode = conscustService.getDiscodeByConsCustno(conscustno);
        HighActiDiscountRatioModel highProductInfoModel = highProductService.getHighActiRateDiscount(pcode, "A", "1", "1", "022",
                "***", dicode);
        if(highProductInfoModel != null && highProductInfoModel.getActiDiscountRatio() != null){
            BigDecimal ratio = highProductInfoModel.getActiDiscountRatio();
            //活动折扣率 = 0，则正常弹出折扣申请/折扣修改页，正常提交
            if(ratio.compareTo(BigDecimal.ZERO) == 0){
                return  "can";
                //活动折扣率 = 1，则点击【折扣申请】/【折扣修改】时弹窗提示“该产品不允许申请折扣”
            }else if(ratio.compareTo(BigDecimal.ONE) == 0){
                return "cantdiscount";
                //活动折扣率 != 0或1，则点击【折扣申请】【折扣修改】时弹窗提示“该产品未确认是否可申请折扣，请线下确认 是否确认需要申请/修改折扣”
            }
        }
        return "nodiscount";
    }
    
    /**
     * 意向单审核
     * @return
     */
    @RequestMapping("/auditDiscountList.do")
    public String auditDiscountList(){
        return "fixed/auditDiscountList";
    }

    /**
     * 意向单审核列表
     * @param request
     * @return
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
	@RequestMapping("/auditDiscountListJson.do")
    @ResponseBody
    public Object auditDiscountListJson(HttpServletRequest request) throws Exception{
        // 设置查询参数
        Map<String, String> param;
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();
        String custname = request.getParameter("custname");
        String conscustno = request.getParameter("conscustno");
        String hboneno = request.getParameter("hboneno");
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        String discountstate = request.getParameter("discountstate");
        String planstate = request.getParameter("planstate");
        //查询条件（客户名）不为空，增增加客户名参数
        if(StringUtil.isNotNullStr(custname)){
            param.put("custname", custname);
        }else{
            param.put("custname", null);
        }
        //查询条件（投顾客户号）不为空，则增加投顾客户号参数
        if(StringUtil.isNotNullStr(conscustno)){
            param.put("conscustno", conscustno);
        }else{
            param.put("conscustno", null);
        }
        //查询条件（一账通号）不为空，则增加一账通号参数
        if(StringUtil.isNotNullStr(hboneno)){
            param.put("hboneno", hboneno);
        }else{
            param.put("hboneno", null);
        }
        //如果查询条件（折扣状态）不为空，则增加折扣状态查询参数
        if(StringUtil.isNotNullStr(discountstate)){
            param.put("discountstate", discountstate);
        }else{
            param.put("discountstate", "all");
        }
        if (StringUtil.isNotNullStr(consCode)) {
            param.put("conscode", consCode);
        } else {
            param.put("orgcode", orgCode);
        }
        if(StringUtil.isNotNullStr(planstate)){
            param.put("planstate", planstate);
        }else{
        	param.put("planstate", null);
        }
        // 判断常量表中合规标识：true启用，false停用
 		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
 		boolean roleCpFlag = false;
 		if (cacheMap != null && !cacheMap.isEmpty()) {
 			roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
 		}
 		// 判断登录人员的角色中是否包括“合规人员”角色
 		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
 		if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
            param.put("hascp", "true");
        }
 		PageData<CmFixedIntention> pageData = cmFixedIntentionService.listCmFixedIntentionByPage(param);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<CmFixedIntention> listdata = pageData.getListData();
        ConstantCache constantCache = ConstantCache.getInstance();
        for(CmFixedIntention info : listdata){
        	info.setFixedstateval(ConstantCache.getInstance().getVal("fixedstate", info.getFixedstate()));
			info.setPlanstateval(ConstantCache.getInstance().getVal("fixedplanstate", info.getPlanstate()));
			info.setDiscountstateval(ConstantCache.getInstance().getVal("discountStates", info.getDiscountstate()));
			info.setDoublestateval(ConstantCache.getInstance().getVal("fixedoublestate", info.getDoublestate()));
            ConsOrgCache orgcache = ConsOrgCache.getInstance();
            String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
            if("0".equals(uporgcode)){
                info.setUporgname(orgcache.getAllOrgMap().get(info.getOrgcode()));
            }else{
                info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
            }
			info.setOutletName(ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
			info.setConscode(ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
			info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
            info.setDiscountWayVal(constantCache.getConstantKeyVal("discountWays").get(info.getDiscountWay()));
            info.setDiscountTypeVal(constantCache.getConstantKeyVal("discountTypes").get(info.getDiscountType()));
            info.setDiscountstyle(constantCache.getConstantKeyVal("discountStyles").get(info.getDiscountstyle()));
            info.setStaffRelation(constantCache.getConstantKeyVal("staffrelations").get(info.getStaffRelation()));
        }
        resultMap.put("rows", listdata);
        return resultMap;
    }
    
    /**
     * 审核页面:
     * @param request
     * @param map 返回数据
    * <AUTHOR>
    * @date 2019/9/5
    */
    @RequestMapping("/auditDiscount.do")
    public String auditDiscount(HttpServletRequest request, Map<String,Object> map) throws Exception {
        String id = request.getParameter("id");
        map.put("prebookid",id);
        CmFixedIntention info;
        if(StringUtil.isNotNullStr(id)){
        	Map<String,Object> paramfixed = new HashMap<String,Object>(1);
        	paramfixed.put("planid", id);
            info = cmFixedIntentionService.getCmFixedIntention(paramfixed);
            map.put("fundname",info.getFundname());
            map.put("consName",ConsOrgCache.getInstance().getAllConsMap().get(info.getConscode()));
            map.put("conscustname", info.getCustname());
            map.put("currencyVal", ConstantCache.getInstance().getVal("currencys", StaticVar.CURRENCY_RMB));
            map.put("afterTaxAmt",info.getAfterTaxAmt().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
            map.put("beforeTaxAmt",info.getBeforetaxamt().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
            map.put("befTaxAmtAdjust",info.getBefTaxAmtAdjust() == null ? BigDecimal.ZERO : info.getBefTaxAmtAdjust().setScale(4, BigDecimal.ROUND_HALF_UP).toPlainString());
            //申请折扣和MGM时取实际打款金额
            map.put("buyamt", info.getPlanamount().divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP).toPlainString());
            map.put("currency",StaticVar.CURRENCY_RMB);
            map.put("tradeCount", 1);
            map.put("discountState", info.getDiscountstate());
            map.put("isRefund", info.getIsrefund());
            map.put("discountType", info.getDiscountType());
            map.put("discountRate", info.getDiscountRate().setScale(4, BigDecimal.ROUND_HALF_UP).toPlainString());
            map.put("discountWay", info.getDiscountWay());
            map.put("discountStyle", info.getDiscountstyle());
            map.put("discountReason", info.getDiscountReason());
            map.put("remarks", info.getDisremarks());
            map.put("staffRelation", info.getStaffRelation());
            map.put("outletName", ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
            map.put("conscode", ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
            map.put("plantotalnum", info.getPlantotalnum());
            map.put("planrate", ConstantCache.getInstance().getVal("fixedPlanRate", info.getPlanrate()));
            map.put("paytype", ConstantCache.getInstance().getVal("fixedpaytype", info.getPaytype()));
        }
        return "fixed/auditDiscount";
    }
    
    @RequestMapping("/editDiscount.do")
    public String editDiscount(HttpServletRequest request,Map<String,Object> map){
        String id = request.getParameter("id");
        map.put("endAudit",request.getParameter("endAudit"));
        CmFixedIntention info = null;
        Map<String,Object> paramfixed = new HashMap<String,Object>(1);
        paramfixed.put("planid", id);
        info = cmFixedIntentionService.getCmFixedIntention(paramfixed);
        map.put("prebookid", id);
        //查询是否有申请超季度的权限
        List<String> userroles = (List<String>) request.getSession().getAttribute("loginRoles");
        String menucode = request.getParameter("menuCode");
        String opercode = "99";
        String hasAuth = "0";
        for (String role : userroles) {
            List<String> temp = AuthCache.getInstance().getOperListStr(role, menucode);
            if (temp != null && temp.contains(opercode)) {
                hasAuth = "1";
                break;
            }
        }
        map.put("hasAuthOverDt", hasAuth);
        map.put("totalamt",null);
        map.put("isfccl",StaticVar.FCCL_NO);
        map.put("fundname", info.getFundname());

        map.put("conscustname", info.getCustname());
        map.put("conscustno", info.getConscustno());
        map.put("currencytype", StaticVar.CURRENCY_RMB);
        map.put("currency", ConstantCache.getInstance().getVal("currencys", StaticVar.CURRENCY_RMB));
        BigDecimal exchangerate = new BigDecimal("1.0");
        map.put("exchangerate", exchangerate.setScale(4, BigDecimal.ROUND_HALF_UP).toPlainString());
        BigDecimal buyamt = info.getPlanamount();
        BigDecimal buyamtnew = buyamt.divide(BIG_DECIMAL_1W, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal rate = preBookService.getRateFee(info.getConscustno(),info.getFundcode(), buyamtnew);
        map.put("feeRate", rate.setScale(4, BigDecimal.ROUND_HALF_UP).toPlainString());
        Map<String,Object> params = new HashMap<>(1);
        params.put("prebookid",id);
        map.put("discountWay",info.getDiscountWay());
        map.put("discountStyle",info.getDiscountstyle());
        map.put("discountType",info.getDiscountType());
        map.put("isRefund",info.getIsrefund());
        map.put("discountRate",info.getDiscountRate());
        map.put("beforeTaxAmt",info.getBeforetaxamt());
        map.put("afterTaxAmt",info.getAfterTaxAmt());
        map.put("discountReason",info.getDiscountReason());
        map.put("staffRelation",info.getStaffRelation());
        map.put("buyamt", buyamtnew.toPlainString());
        map.put("outletName", ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
        map.put("conscode", ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
        map.put("plantotalnum", info.getPlantotalnum());
        map.put("planrate", ConstantCache.getInstance().getVal("fixedPlanRate", info.getPlanrate()));
        map.put("paytype", ConstantCache.getInstance().getVal("fixedpaytype", info.getPaytype()));
        map.put("canDiscount", checkCanDiscount(info.getConscustno(),info.getFundcode()));
        ConstantCache constantCache = ConstantCache.getInstance();
        Map<String,String> discountStylesMap = constantCache.getConstantKeyVal("discountStyles");
        Map<String,String> discountWaysMap = constantCache.getConstantKeyVal("discountWays");
        Map<String,String> discountTypesMap = constantCache.getConstantKeyVal("discountTypes");
        Map<String,String> staffRelationsMap = constantCache.getConstantKeyVal("staffrelations");
        map.put("discountStyles",buildSelectMap(discountStylesMap,null));
        //排除工资
        map.put("discountStylesExcludeSalary",buildSelectMap(discountStylesMap,"3"));
        map.put("discountWays",buildSelectMap(discountWaysMap,null));
        //直接少汇排除MGM员工介绍
        map.put("discountTypes",buildSelectMap(discountTypesMap,"7"));
        //好买返回排除正常折扣
        map.put("discountTypesReturn",buildSelectMap(discountTypesMap,"4"));
        //员工关系
        map.put("staffRelations",buildSelectMap(staffRelationsMap,null));
        //交易次数
        map.put("tradeCount", 0);

        map.put("calculateAppAmt",info.getPlanamount());
        
    	//map.put("canDiscount", "can");
        return "fixed/editDiscount";
    }


    /**
     * 保存折扣申请
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/saveFixedDiscountApply.do")
    public String saveDiscountApply(HttpServletRequest request) {
        String calculateAppAmt=request.getParameter("calculateAppAmt");
        String prebookid = request.getParameter("id");
        String discountType = request.getParameter("discountType");
        String discountRate = request.getParameter("discountRate");
        String discountReason = request.getParameter("discountReason");
        String afterTaxAmt = request.getParameter("afterTaxAmt");
        String beforeTaxAmt = request.getParameter("beforeTaxAmt");
        String discountWay = request.getParameter("discountWay");
        String discountStyle = request.getParameter("discountStyle");
        String isRefund = request.getParameter("isRefund");
        String tradeCount = request.getParameter("tradeCount");
        String endAudit = request.getParameter("endAudit");
        String staffRelation = request.getParameter("staffRelation");
        String result;
        if (StringUtil.isNullStr(discountRate) ||
                StringUtil.isNullStr(afterTaxAmt) ||
                StringUtil.isNullStr(beforeTaxAmt) ||
                StringUtil.isNullStr(tradeCount) ||
                StringUtil.isNullStr(prebookid)) {
            return  "paramError";
        }

        //查询是否是定投管理的
        Map<String,Object> paramfixed = new HashMap<String,Object>(1);
        paramfixed.put("planid", prebookid);
        CmFixedIntention fixedinfo = cmFixedIntentionService.getCmFixedIntention(paramfixed);
        if(fixedinfo == null){
            return  "paramError";
        }
        String conscustno = fixedinfo.getConscustno();
        //获取所属投顾的角色
        String conscode = vconscustService.getConscodeFromConscustno(conscustno);
        List<String> userroles  = hbUserroleService.getAllRoleCodeByConsCode(conscode);
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        Discountapp dpp = new Discountapp();
        dpp.setId(new BigDecimal(commonService.getSeqValue("SEQ_PREBOOK")));
        dpp.setCalculateAppAmt(new BigDecimal(calculateAppAmt));
        dpp.setPrebookid(new BigDecimal(prebookid));
        dpp.setDiscountType(discountType);
        dpp.setDiscountRate(new BigDecimal(discountRate));
        dpp.setDiscountReason(discountReason);
        dpp.setAfterTaxAmt(new BigDecimal(afterTaxAmt));
        dpp.setBeforeTaxAmt(new BigDecimal(beforeTaxAmt));
        dpp.setBefTaxAmtAdjust(dpp.getBeforeTaxAmt());
        dpp.setDiscountWay(discountWay);
        dpp.setStaffRelation(staffRelation);
        dpp.setDiscountStyle(discountStyle);
        dpp.setIsrefund(isRefund);
        //正常折扣、活动竞赛，新增或修改后折扣状态为终审通过
        if(StaticVar.DISCOUNT_TYPE_NORMAL.equals(dpp.getDiscountType()) && !userroles.contains(StaticVar.ROLE_SIC_TEMP)){
            dpp.setDiscountState(StaticVar.DISCOUNT_NSTATES_ZS_PASS);
        }else{
            dpp.setDiscountState(StaticVar.DISCOUNT_NSTATES_HAS_APPLY);
        }
        dpp.setTradecount(new BigDecimal(tradeCount));
        dpp.setCreator(userlogin.getUserId());
        dpp.setCredt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
        dpp.setRecstat(StaticVar.CONSCUST_STATUS_NORMAL);
        //注意  此处的 prebookid  是定投意向单的id
        List<Discountapp> list = discountappService.listDiscountappByPreId(new BigDecimal(prebookid));
        CmDiscountLog cmDiscountLog = new CmDiscountLog();
        cmDiscountLog.setOptMan(userlogin.getUserId());
        cmDiscountLog.setRemark("discountRate: " + dpp.getDiscountRate() + " beforeTaxAmt: " + dpp.getBeforeTaxAmt());
        if (list != null && list.size() > 0) {
            Discountapp discountapp = list.get(0);
            dpp.setId(discountapp.getId());
            discountappService.updateDiscountapp2(dpp);
            result = "success";
            cmDiscountLog.setDiscountId(discountapp.getId());
            if(StringUtil.isNullStr(endAudit)){
                //普通修改日志
                cmDiscountLog.setOptType(StaticVar.DISCOUNT_OPT_MODIFY);
            }else{
                //修改终审日志
                cmDiscountLog.setOptType(StaticVar.DISCOUNT_OPT_AUDIT_MODIFY);
            }
            discountappService.insertLog(cmDiscountLog);
        } else {
            discountappService.insertDiscountapp(dpp);
            result = "success";
            cmDiscountLog.setDiscountId(dpp.getId());
            cmDiscountLog.setOptType(StaticVar.DISCOUNT_OPT_APPLY);
            discountappService.insertLog(cmDiscountLog);
        }
        return result;
    }
}
