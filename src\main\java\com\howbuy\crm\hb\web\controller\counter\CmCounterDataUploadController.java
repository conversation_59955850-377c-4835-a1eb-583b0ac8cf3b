package com.howbuy.crm.hb.web.controller.counter;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoRequest;
import com.howbuy.acccenter.facade.query.querytxacctinfofacade.QueryTxAcctInfoResponse;
import com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum;
import com.howbuy.crm.base.*;
import com.howbuy.crm.hb.constants.CrmTradeServerPathConstant;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.hkconscust.HkConscustVO;
import com.howbuy.crm.hb.domain.prosale.CmCustTransfervol;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.external.AcctRelatedService;
import com.howbuy.crm.hb.service.hkconscust.HkConscustService;
import com.howbuy.crm.hb.service.prosale.CmCustTransfervolService;
import com.howbuy.crm.hb.service.prosale.ManyCallPreInfoService;
import com.howbuy.crm.hb.web.dto.ComboboxItem;
import com.howbuy.crm.hb.web.dto.counter.CmCounterBusinessMsgViewDto;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.nt.accountrelation.dto.CmRelationAccount;
import com.howbuy.crm.nt.accountrelation.enums.RelationTypeEnum;
import com.howbuy.crm.nt.accountrelation.service.CmRelationAccountService;
import com.howbuy.crm.nt.accountrelation.service.CmRelationAccountSubService;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.prebook.dto.CmPreBookProductInfo;
import com.howbuy.crm.prebook.dto.CmPrebookBankInfo;
import com.howbuy.crm.prebook.dto.PreRelatedOrderDto;
import com.howbuy.crm.prebook.service.FcclPrebookService;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.prebook.service.PrebookBusinessService;
import com.howbuy.crm.prebook.service.PrebookConfigService;
import com.howbuy.crm.prosale.dto.CmManycallRelation;
import com.howbuy.crm.prosale.dto.Prebookmanycallinfo;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.crm.trade.common.enums.counter.CounterBusiEnum;
import com.howbuy.crm.trade.common.enums.counter.CounterPreTypeEnum;
import com.howbuy.crm.trade.common.response.BaseResponse;
import com.howbuy.crm.trade.common.util.CounterUtil;
import com.howbuy.crm.trade.model.counter.busiparam.CxgDetailDto;
import com.howbuy.crm.trade.model.counter.busiparam.HkCxgParamDto;
import com.howbuy.crm.trade.model.counter.busiparam.HkDepositParamDto;
import com.howbuy.crm.trade.model.counter.busiparam.HkVoucherParamDto;
import com.howbuy.crm.trade.model.counter.dto.CmCounterBusinessMsgDto;
import com.howbuy.crm.trade.model.counter.dto.GetCounterBusinessParamVo;
import com.howbuy.crm.trade.model.counter.po.CmCounterBusiness;
import com.howbuy.crm.trade.model.counter.request.SaveCmCounterOrderRequest;
import com.howbuy.crm.util.exception.BusinessException;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import crm.howbuy.base.enums.CrmCustInvestTypeEnum;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.enums.DisCodeEnum;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping(value = "/counterdataupload")
public class CmCounterDataUploadController  extends BaseCounterController {

    @Autowired
    private CommonService commonService;
    @Autowired
    private ConscustService conscustService;
    @Autowired
    private QueryAcctBalanceFacade queryAcctBalanceFacade;
    @Autowired
    private CmCustTransfervolService cmCustTransfervolService;
    @Autowired
    private AcctRelatedService acctRelatedService;
    @Autowired
    private JjxxInfoService jjxxInfoService;
    @Autowired
    private PreBookService preBookService;
    @Autowired
    private ManyCallPreInfoService manyCallPreInfoService;
    @Autowired
    private CmRelationAccountService cmRelationAccountService;
    @Autowired
    private CmRelationAccountSubService cmRelationAccountSubService;
    @Autowired
    private PrebookBasicInfoService prebookBasicInfoService;
    @Autowired
    private PrebookBusinessService prebookBusinessService;
    @Autowired
    private HkConscustService hkConscustService;
    @Autowired
    private PrebookConfigService prebookConfigService;
    @Autowired
    private FcclPrebookService fcclPrebookService;

    /**
     * 非交易上传界面
     *
     * @return String
     */
    @ResponseBody
    @RequestMapping("/notransaction")
    public ModelAndView noTransaction(HttpServletRequest request,
                                      HttpServletResponse response) throws Exception {
        String custno = request.getParameter("conscustno");
        String mod = StringUtil.replaceNull(request.getParameter("mod"));
        Conscust conscust =null;
        if(StringUtil.isNotNullStr(custno)){
            conscust = conscustService.getConscust(custno);
        }
        if (conscust == null) {
            return null;
        }

        //该客户是否有开户
        //好买是否开户
        String isOpenAcct1 = "false";
        //好臻是否开户
        String isOpenHzAcct = "false";
        //理财通是否开户
        String isOpenLCTAcct = "false";
        //香港开户
        String isOpenHkAcct = "false";
        String isOpenAcct2 = "false";
        // 是否存在证件信息
        String hasIdCrad = "false";
        if (conscust.getIdnoCipher() != null) {
            hasIdCrad = "true";
        }

        
        QueryTxAcctInfoRequest queryTxAcctInfoRequest = new QueryTxAcctInfoRequest();
        HkConscustVO hkConscustVO = hkConscustService.queryHkCustInfoByCustNo(custno);
        //2024年7月29日  HkCustStatus已被outer层作为翻译，外部只能使用 description 判断
        // 新增属性： HkCustStatusCode
        if (hkConscustVO == null || !(
                HkAcctCustStatusEnum.DORMANT.getCode().equals(hkConscustVO.getHkCustStatusCode()) ||
                        HkAcctCustStatusEnum.NORMAL.getCode().equals(hkConscustVO.getHkCustStatusCode()))
        ) {
            isOpenHkAcct = "false";
        } else {
            isOpenHkAcct = "true";
        }

        CrmCustInvestTypeEnum investTypeEnum= CrmCustInvestTypeEnum.getEnum(conscust.getInvsttype());
        try{
        	queryTxAcctInfoRequest.setIdNoDigest(conscust.getIdnoDigest());
        	queryTxAcctInfoRequest.setIdType(conscust.getIdtype());
        	queryTxAcctInfoRequest.setInvstType(conscust.getInvsttype());
            queryTxAcctInfoRequest.setDisCode(investTypeEnum.getUsedDisCodeEnum().getCode());
            //未知原因：机构分销传客户名称
            if(DisCodeEnum.FOF.equals(investTypeEnum.getUsedDisCodeEnum())){
                queryTxAcctInfoRequest.setCustName(conscust.getCustname());
            }

            //好买分销
            if(validateDis(queryTxAcctInfoRequest,investTypeEnum.getUsedDisCodeEnum().getCode())){
                isOpenAcct1 = "true";
            }

            //好买分销
            if(validateDis(queryTxAcctInfoRequest,DisCodeEnum.HZ.getCode())){
                isOpenHzAcct = "true";
            }

            //理财通分销
            if(validateDis(queryTxAcctInfoRequest, DisChannelCodeEnum.LCT.getCode())){
                isOpenLCTAcct = "true";
            }
            //好买、好臻、理财通任一分销开户，则该客户开户
            if("true".equals(isOpenAcct1) ||"true".equals(isOpenHzAcct) ||"true".equals(isOpenLCTAcct)){
                isOpenAcct2 = "true";
            }

        }catch (Exception ex) {
        	log.error("noTransaction:" + ex.getMessage(), ex);
        }
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/counter/upNoTransaction");
        modelAndView.addObject("mod", mod);
        modelAndView.addObject("cust", conscust);
        //好买开户
        modelAndView.addObject("isOpenAcct1", isOpenAcct1);
        //好臻开户
        modelAndView.addObject("isOpenHzAcct", isOpenHzAcct);
        modelAndView.addObject("isOpenAcct2", isOpenAcct2);
        //香港开户
        modelAndView.addObject("isOpenHkAcct", isOpenHkAcct);
        //香港交易账号
        modelAndView.addObject("hkTxAcctNo", hkConscustVO==null?"":hkConscustVO.getHkTxAcctNo());

        // 是否存在证件信息
        modelAndView.addObject("hasIdCrad", hasIdCrad);
        modelAndView.addObject("idMsgFlag", StringUtils.isNotBlank(conscust.getIdtype()) && StringUtils.isNotBlank(conscust.getIdnoDigest()));
        return modelAndView;
    }

    /**
     * 资料管理-非交易上传界面
     *
     * @return String
     */
    @ResponseBody
    @RequestMapping("/notransactioncounter")
    public ModelAndView notransactioncounter(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String mod = StringUtil.replaceNull(request.getParameter("mod"));
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/counter/upNoTransactionCounter");
        modelAndView.addObject("mod", mod);
        modelAndView.addObject("defineConsCode", getLoginUserId());
        return modelAndView;
    }

    /**
     * 资料管理-客户信息
     *
     * @return String
     */
    @ResponseBody
    @RequestMapping("/counterConcust")
    public Map<String, Object> counterConcust(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        String custno = request.getParameter("conscustno");
        Conscust conscust =null;
        if(StringUtil.isNotNullStr(custno)){
            conscust = conscustService.getConscust(custno);
        }


        if (conscust == null) {
            return null;
        }
        //该客户是否有开户
        //好买是否开户
        String isOpenAcct1 = "false";
        //好臻是否开户
        String isOpenHzAcct = "false";
        //理财通是否开户
        String isOpenLCTAcct = "false";
        //香港开户
        String isOpenHkAcct = "false";
        String isOpenAcct2 = "false";
        // 是否存在证件信息
        String hasIdCrad = "false";
        if (conscust.getIdnoCipher() != null) {
            hasIdCrad = "true";
        }

        QueryTxAcctInfoRequest queryTxAcctInfoRequest = new QueryTxAcctInfoRequest();
        HkConscustVO hkConscustVO = hkConscustService.queryHkCustInfoByCustNo(custno);
        if (hkConscustVO == null || !(hkConscustVO.getHkCustStatus().equals(HkAcctCustStatusEnum.DORMANT.getDescription()) || hkConscustVO.getHkCustStatus().equals(HkAcctCustStatusEnum.NORMAL.getDescription()))) {
            isOpenHkAcct = "false";
        } else {
            isOpenHkAcct = "true";
        }

        CrmCustInvestTypeEnum investTypeEnum= CrmCustInvestTypeEnum.getEnum(conscust.getInvsttype());
        try{
            queryTxAcctInfoRequest.setIdNoDigest(conscust.getIdnoDigest());
            queryTxAcctInfoRequest.setIdType(conscust.getIdtype());
            queryTxAcctInfoRequest.setInvstType(conscust.getInvsttype());
            queryTxAcctInfoRequest.setDisCode(investTypeEnum.getUsedDisCodeEnum().getCode());
            //未知原因：机构分销传客户名称
            if(DisCodeEnum.FOF.equals(investTypeEnum.getUsedDisCodeEnum())){
                queryTxAcctInfoRequest.setCustName(conscust.getCustname());
            }

            //好买分销
            if(validateDis(queryTxAcctInfoRequest,investTypeEnum.getUsedDisCodeEnum().getCode())){
                isOpenAcct1 = "true";
            }

            //好买分销
            if(validateDis(queryTxAcctInfoRequest,DisCodeEnum.HZ.getCode())){
                isOpenHzAcct = "true";
            }

            //理财通分销
            if(validateDis(queryTxAcctInfoRequest, DisChannelCodeEnum.LCT.getCode())){
                isOpenLCTAcct = "true";
            }
            //好买、好臻、理财通任一分销开户，则该客户开户
            if("true".equals(isOpenAcct1) ||"true".equals(isOpenHzAcct) ||"true".equals(isOpenLCTAcct)){
                isOpenAcct2 = "true";
            }

        }catch (Exception ex) {
            log.error("noTransaction:" + ex.getMessage(), ex);
        }

        resultMap.put("cust", conscust);
        //好买开户
        resultMap.put("isOpenAcct1", isOpenAcct1);
        //好臻开户
        resultMap.put("isOpenHzAcct", isOpenHzAcct);
        resultMap.put("isOpenAcct2", isOpenAcct2);
        //香港开户
        resultMap.put("isOpenHkAcct", isOpenHkAcct);
        //香港交易账号
        resultMap.put("hkTxAcctNo", hkConscustVO==null?"":hkConscustVO.getHkTxAcctNo());
        // 是否存在证件信息
        resultMap.put("hasIdCrad", hasIdCrad);
        resultMap.put("idMsgFlag", StringUtils.isNotBlank(conscust.getIdtype()) && StringUtils.isNotBlank(conscust.getIdnoDigest()));
        String idtype = conscust.getIdtype();
        ConstantCache constantCache = ConstantCache.getInstance();
        if ("1".equals(conscust.getInvsttype())) {
            resultMap.put("idtype", constantCache.getConstantKeyVal("idtype").get(idtype));
        } else if ("0".equals(conscust.getInvsttype())) {
            resultMap.put("idtype", constantCache.getConstantKeyVal("IDTypesOfInst").get(idtype));
        } else if ("9".equals(conscust.getInvsttype())) {
            resultMap.put("idtype", constantCache.getConstantKeyVal("IDTypesOfFund").get(idtype));
        } else if ("2".equals(conscust.getInvsttype())) {
            resultMap.put("idtype", constantCache.getConstantKeyVal("IDTypesOfProduct").get(idtype));
        }

        return resultMap;
    }

    /**
     * 是否分销开户
     * @param request
     * @param disCode
     * @return
     */
    private boolean validateDis(QueryTxAcctInfoRequest request, String disCode){
        request.setDisCode(disCode);
        ReturnMessageDto<QueryTxAcctInfoResponse> acctReturnDto=acctRelatedService.queryTxAcctInfo(request);
        if(acctReturnDto.isSuccess() &&
                StringUtils.isNotBlank(acctReturnDto.getReturnObject().getDisTxAcctNo ())){
            return true;
        }
        return false;
    }

    /**
     * 补充 分次call预约对应的 资料管理页面。 可选的下拉框 业务类型数据。
     * @param zzxs 组织形势
     * @param isFirstCall 是否为首次call 预约
     * @param returnMap
     */
    private void putFcCallPreBookBusiTypeIntoMap(String zzxs,boolean isFirstCall,
                                                 CmPreBookProductInfo prebookInfo,
                                                 Map<String,Object> returnMap){
        //下拉框  业务类型
        List<CounterBusiEnum> availSelectList = null;
        CounterBusiEnum selectedBusiEnum=null;
        if(isFirstCall){
            if("2".equals(zzxs)){
//                分次call首次  zzxs == '2'  【hb_constant.typeCode=countertransactionyxhh】
//                14	有限合伙类产品认申购   【默认选中】
//                17	撤单
                //有限合伙类产品认申购
                selectedBusiEnum=CounterBusiEnum.TRADE_BUY_LP;
                availSelectList= Lists.newArrayList(selectedBusiEnum,CounterBusiEnum.TRADE_RECALL);
            }else{
//                分次call首次 zzxs != '2' 【hb_constant.typeCode=countertransactionfyyhh】
//                13	非有限合伙类产品认申购 【默认选中】
//                17	撤单
                //非有限合伙类产品认申购
                selectedBusiEnum=CounterBusiEnum.TRADE_BUY_NON_LP;
                availSelectList=Lists.newArrayList(selectedBusiEnum,CounterBusiEnum.TRADE_RECALL);
            }
            boolean qianxiFlag = checkQianXiFlag(prebookInfo.getPcode());
            //千禧年产品用香港的业务类型
            if(qianxiFlag) {
                availSelectList = Lists.newArrayList(CounterBusiEnum.TRADE_BUY_HK, CounterBusiEnum.TRADE_RECALL_HK);
            }

        }else{
            //分次call  非首次 【hb_constant.typeCode=countertransactioncall】
//            20	后续call款 【默认选中】
//            15	追加
//            17	撤单
            //非有限合伙类产品认申购
            selectedBusiEnum=CounterBusiEnum.FOLLOW_CALL_PAY;
            availSelectList=Lists.newArrayList(selectedBusiEnum,CounterBusiEnum.TRADE_APPEND,CounterBusiEnum.TRADE_RECALL);
        }

        if (PreBookLegalDocStatEnum.NOT_UPLOADED.getCode().equals(prebookInfo.getLegaldocStat())) {
            if (DisChannelCodeEnum.HZ.getCode().equals(prebookInfo.getPreDisCode())) {
                // 新增业务类型“好臻-售前留痕材料”，投顾可在好臻预约单上传其业务类型的资料，完成售前留痕材料的上传动作
                availSelectList.add(CounterBusiEnum.HZ_LEGAL_DOCUMENT);
            } else {
                availSelectList.add(CounterBusiEnum.LEGAL_DOCUMENT);
            }
        }

        //默认选中业务类型
        returnMap.put("selectedBusiType", selectedBusiEnum==null?"":selectedBusiEnum.getKey());
        returnMap.put("availSelectList", enumToDropDownMap(availSelectList));
    }


    /**
     * 枚举 --> 下拉框
     * @param availSelectList
     * @return
     */
    private List<Map<String,String>> enumToDropDownMap(List<CounterBusiEnum>  availSelectList){
        List<Map<String,String>> returnList=Lists.newArrayList();
        if(CollectionUtils.isEmpty(availSelectList)){
            return returnList;
        }

        availSelectList.forEach(busiEnum->{
            Map<String,String> map=Maps.newHashMap();
            map.put("key",busiEnum.getKey());
            map.put("desc",busiEnum.getDesc());
            returnList.add(map);
        });
        return returnList;
    }


    /**
     * 补充  [非香港产品] 预约对应的 资料管理页面。 可选的下拉框 业务类型数据。
     * @param tradeTypeEnum
     * @param zzxs
     * @param returnMap
     */
     private void putPreBookBusiTypeIntoMap(PreBookTradeTypeEnum tradeTypeEnum,
                                            String zzxs, CmPreBookProductInfo prebookInfo,
                                            Map<String,Object> returnMap){
         CounterBusiEnum selectedBusiEnum=null;
         if(tradeTypeEnum.equals(PreBookTradeTypeEnum.BUY)){
             if("2".equals(zzxs)){
                 //有限合伙类产品认申购
                 selectedBusiEnum=CounterBusiEnum.TRADE_BUY_LP;
             }else{
                 //非有限合伙类产品认申购
                 selectedBusiEnum=CounterBusiEnum.TRADE_BUY_NON_LP;
             }
         }

         if(tradeTypeEnum.equals(PreBookTradeTypeEnum.APPEND)){
             selectedBusiEnum=CounterBusiEnum.TRADE_APPEND;
         }
         if(tradeTypeEnum.equals(PreBookTradeTypeEnum.SALE)){
             selectedBusiEnum=CounterBusiEnum.TRADE_REDEEM;
         }
         //下拉框 追加 撤单
         //下拉框  业务类型
         List<CounterBusiEnum> availSelectList = Lists.newArrayList(selectedBusiEnum,CounterBusiEnum.TRADE_RECALL);

         if (PreBookLegalDocStatEnum.NOT_UPLOADED.getCode().equals(prebookInfo.getLegaldocStat())) {
             if (DisChannelCodeEnum.HZ.getCode().equals(prebookInfo.getPreDisCode())) {
                 // 新增业务类型“好臻-售前留痕材料”，投顾可在好臻预约单上传其业务类型的资料，完成售前留痕材料的上传动作
                 availSelectList.add(CounterBusiEnum.HZ_LEGAL_DOCUMENT);
             } else {
                 availSelectList.add(CounterBusiEnum.LEGAL_DOCUMENT);
             }
         }

         //默认选中业务类型
         returnMap.put("selectedBusiType", selectedBusiEnum==null?"":selectedBusiEnum.getKey());
         returnMap.put("availSelectList", enumToDropDownMap(availSelectList));
     }



     /**
      * 是否允许选择  认申购 业务类型
      * @param prodCode
      * @return
      */
     private boolean allowChooseBuy(BigDecimal preId,String prodCode){
         //是否允许选中  认申购 业务 资料类型
         boolean allowChooseBuy = true;
         //分次call产品， 但是非首次  提示： 仅认缴(首次实缴预约)需要上传资料，后续call款无需上传资料
         ReturnMessageDto<String>  fcclFlagResp=prebookConfigService.getFcclFlag(prodCode);
         boolean fcclFlag = fcclFlagResp.isSuccess() &&  YesOrNoEnum.YES.getCode().equals(fcclFlagResp.getReturnObject());

         if(fcclFlag){
             boolean isFirst = fcclPrebookService.isFirstPreBookByPreId(preId);
             if(!isFirst){
                 allowChooseBuy=false;
                 log.info("预约Id:{} 产品代码：{}，非首次call，不允许选择业务类型：{}",
                         preId,prodCode,
                         Lists.newArrayList(CounterBusiEnum.TRADE_BUY_HK,CounterBusiEnum.TRADE_APPEND_HK));
             }
         }
         return allowChooseBuy;
     }


    /**
     * @description: 上传资料页面  香港产品 业务类型下拉框赋值
     * @param prebookInfo
     * @param returnMap
     * @return void
     * @author: jin.wang03
     * @date: 2024/7/1 14:44
     * @since JDK 1.8
     */
    private void insertHkPreBookBusiTypeIntoMap(CmPreBookProductInfo prebookInfo,
                                             Map<String, Object> returnMap) {
        BigDecimal preId  = prebookInfo.getId();
        PreBookTradeTypeEnum tradeTypeEnum=PreBookTradeTypeEnum.getEnum(prebookInfo.getTradeType());
        String preType = prebookInfo.getPretype();
        //下拉框  业务类型
        List<CounterBusiEnum> availSelectList = new ArrayList<>();

        if(PreBookTradeTypeEnum.getBuyCodeList().contains(tradeTypeEnum.getCode())){
            if(allowChooseBuy(preId,prebookInfo.getPcode())){
                // 香港-认申购
                if (tradeTypeEnum.equals(PreBookTradeTypeEnum.BUY)) {
                    availSelectList.add(CounterBusiEnum.TRADE_BUY_HK);
                }
                // 香港-追加
                if (tradeTypeEnum.equals(PreBookTradeTypeEnum.APPEND)) {
                    availSelectList.add(CounterBusiEnum.TRADE_APPEND_HK);
                }
            }
            // 【业务类型】= (购买 || 追加) &【预约类型】=电子成单：提供“好买香港-交易打款凭证” 选项
            if(CounterPreTypeEnum.NO.getKey().equals(preType)){
                availSelectList.add(CounterBusiEnum.TRADE_HK_PAYMENT_VOUCHER);
            }
        }

        // 香港-赎回
        if (tradeTypeEnum.equals(PreBookTradeTypeEnum.SALE)) {
            availSelectList.add(CounterBusiEnum.TRADE_REDEEM_HK);
        }

        //下拉框 追加 撤单
        availSelectList.add(CounterBusiEnum.TRADE_RECALL_HK);

        // 默认业务类型
        returnMap.put("selectedBusiType", "");
        returnMap.put("availSelectList", enumToDropDownMap(availSelectList));
    }




    /**
      * @description:(预约 是否允许做资料上传  弹窗前的校验)
      * @param preId
      * @return com.howbuy.crm.base.ReturnMessageDto<java.lang.String>
      * @author: haoran.zhang
      * @date: 2023/6/2 13:15
      * @since JDK 1.8
      */
     @ResponseBody
     @RequestMapping("/validateUploadPrebook")
     public ReturnMessageDto<String> validateUploadPrebook(String preId){
         if(StringUtil.isEmpty(preId)){
             return  ReturnMessageDto.fail("预约Id不能为空！");
         }
         CmPreBookProductInfo preBookInfo= prebookBasicInfoService.getPreBookById(new BigDecimal(preId));
         if(preBookInfo==null){
             return  ReturnMessageDto.fail(String.format("预约Id:%s 无预约数据！",preId));
         }
         return ReturnMessageDto.ok();
     }

    
    /**
     * 交易上传界面
     * 根据预约--> 资料管理订单
     *
     * @return String
     */
    @ResponseBody
    @RequestMapping("/upTransactionByPreBook")
    public ModelAndView upTransactionByPreBook(HttpServletRequest request,
                                    HttpServletResponse response) {
        Map<String,Object> returnMap=Maps.newHashMap();
        String preId = request.getParameter("preId");
        String mod = request.getParameter("mod");
        CmPreBookProductInfo prebookInfo= prebookBasicInfoService.getPreBookById(new BigDecimal(preId));
        if(prebookInfo==null){
            throw new BusinessException(String.format("预约Id:%s 无预约数据！",preId));
        }

        // 为了兼容海外v1.2的特殊需求内容，当交易类型为赎回 且在直销产品销售表中
        Boolean isSpecialProduct = null;
        if (StringUtils.isNotBlank(prebookInfo.getPcode()) && StringUtils.isNotBlank(prebookInfo.getConscustno())) {
            isSpecialProduct = prebookBasicInfoService.getIsSpecialProduct(prebookInfo.getPcode(), prebookInfo.getConscustno());
        }
        String prodCode=prebookInfo.getPcode();

        PreBookArchTypeEnum archTypeEnum = PreBookArchTypeEnum.getEnum(prebookInfo.getArchType());

        JjxxInfo jjxxInfo=jjxxInfoService.getJjxxByJjdm(prodCode,false);
        if(jjxxInfo==null){
            throw new BusinessException(String.format("产品代码:%s 查询不到基金信息！",prodCode));
        }
        returnMap.put("pcodename", jjxxInfo.getJjjc());
        String zzxs = jjxxInfo.getZzxs();
        //是否香港：
        String sfxg= prebookInfo.getSfxg();
        returnMap.put("zzxs", zzxs);
        returnMap.put("sfxg", sfxg);


        Conscust conscust = conscustService.getConscust(prebookInfo.getConscustno());
        if(conscust==null){
            throw new BusinessException(String.format("客户号:%s 无客户信息数据！",prebookInfo.getConscustno()));
        }
        returnMap.put("cust",conscust);
        returnMap.put("investTypeDesc",CrmCustInvestTypeEnum.getDescription(conscust.getInvsttype()));
        returnMap.put("preTypeDesc",PreTypeEnum.getDescription(prebookInfo.getPretype()));
        returnMap.put("preid", preId);
        returnMap.put("pretype", prebookInfo.getPretype());
        returnMap.put("tradetype", prebookInfo.getTradeType());
        returnMap.put("pcode", prodCode);
        returnMap.put("buyamt",prebookInfo.getBuyamt()==null?"0":String.format("%.6f", prebookInfo.getBuyamt().divide(BIG_DECIMAL_1W) ));
        returnMap.put("sellvol",prebookInfo.getSellvol()==null?"0":String.format("%.6f", prebookInfo.getSellvol()));
        // 预约赎回金额
        returnMap.put("sellamt", prebookInfo.getSellAmt() == null ? "0" : String.format("%.6f", prebookInfo.getSellAmt()));
        // 赎回方式
        returnMap.put("redeemMode", RedeemModeEnum.getDescription(prebookInfo.getRedeemMode()) );
        //货币
        returnMap.put("currencyDesc",CurrencyEnum.getDescription(prebookInfo.getCurrency()));
        returnMap.put("mod",mod);

        //是否为分次call的预约
        boolean isFcCall= false;
        boolean isFirstCall=false;//是否为 首次预约
        CmManycallRelation relation=manyCallPreInfoService.getManyCallRelationByPreId(prebookInfo.getId());
        if(relation!=null){
            isFcCall=true;
            //获取对应callId
            Prebookmanycallinfo callInfo= manyCallPreInfoService.getManyCallByCallId(relation.getCallid());
            if(callInfo!=null){
                returnMap.put("totalamt", callInfo.getTotalamt()==null?"0":String.format("%.6f", callInfo.getTotalamt().divide(BIG_DECIMAL_1W) ));
                if(prebookInfo.getId().equals(callInfo.getFirstpreid())){
                    isFirstCall=true;
                }
            }
        }
        returnMap.put("isFcCall", isFcCall);
        returnMap.put("isFirstCall", isFirstCall);

        PreBookTradeTypeEnum tradeTypeEnum=PreBookTradeTypeEnum.getEnum(prebookInfo.getTradeType());

        //可用的 资料管理  业务类型下拉框 赋值
        if (
                PreBookArchTypeEnum.HW==archTypeEnum
                        && (isSpecialProduct == null || !isSpecialProduct)
        ) {
            // 香港产品
            putHkPreBookBusiTypeIntoMap(prebookInfo, returnMap);
        }else {
            //非香港
            if (isFcCall) {
                //分次call的 字段赋值
                putFcCallPreBookBusiTypeIntoMap(zzxs, isFirstCall, prebookInfo, returnMap);
            }else{
                //非分次call的 字段赋值
                putPreBookBusiTypeIntoMap(tradeTypeEnum, zzxs, prebookInfo, returnMap);
            }
        }

        //是否允许 该预约 对应的交易订单做撤单操作
        //预约 对应的交易订单是否允许做撤单操作 校验
        ReturnMessageDto<String> recallValidate = preBookService.isAllowReCallOrderByPreId(new BigDecimal(preId));
        returnMap.put("recallValidate",recallValidate);
        //预约对象
        returnMap.put("preBookInfo", prebookInfo);

        return new ModelAndView("/counter/upTransactionByPreBook", returnMap);
    }

    /**
     * @description: 将香港业务类型等数据写入request的map中
     * @param prebookInfo 预约信息
     * @param returnMap
     * @author: jin.wang03
     * @date: 2024/4/19 13:29
     * @since JDK 1.8
     */
    private void putHkPreBookBusiTypeIntoMap(CmPreBookProductInfo prebookInfo, Map<String, Object> returnMap) {
        // 业务类型的下拉框赋值
        insertHkPreBookBusiTypeIntoMap(prebookInfo, returnMap);
        //支付方式
        List<String> paymentList= PaymentModeEnum.getPaymentEnumListByValue(prebookInfo.getPaymentMode())
                                  .stream().map(PaymentModeEnum::getDesc).collect(Collectors.toList());

        //赎回方向
        List<String> redeemList= RedeemDirectEnum.getRedeemEnumListByValue(prebookInfo.getRedeemDirection())
                .stream().map(RedeemDirectEnum::getDesc).collect(Collectors.toList());

        returnMap.put("paymentDesc", String.join(",",paymentList));
        returnMap.put("redeemDesc", String.join(",",redeemList));
        returnMap.put("divModeDesc",DivModeEnum.getName(prebookInfo.getDivMode()));
        // 海外中台的订单号
        PreRelatedOrderDto outerInfo =prebookBusinessService.getOuterOrderInfo(prebookInfo.getId());
        returnMap.put("hwOrderId", outerInfo == null ? "" : outerInfo.getDealNo());
        //卡信息列表
        List<CmPrebookBankInfo>  hkBankList=prebookBasicInfoService.getHkBankAcctInfoList(prebookInfo.getId());
        returnMap.put("hkBankList", hkBankList);
    }

    /**
     * 交易上传界面
     *
     * @return String
     */
    @ResponseBody
    @RequestMapping("/upTransactionByTransfer")
    public ModelAndView upTransactionByTransfer(HttpServletRequest request,
                                 HttpServletResponse response) {
        //份额转让ID
        String id = request.getParameter("id");

        Map<String, Object> param = new HashMap<String, Object>();
        param.put("id", id);
        CmCustTransfervol custTransfervol = cmCustTransfervolService.getCmCustTransfervol(param);

        if (custTransfervol != null) {
            JjxxInfo  jjxxInfo=jjxxInfoService.getJjxxByJjdm(custTransfervol.getFundcode(),false);
            if(jjxxInfo==null){
                custTransfervol.setFundname("基金信息不存在");
            }else{
                custTransfervol.setFundname(jjxxInfo.getJjjc());
            }
        }

        Map<String,Object> returnMap=Maps.newHashMap();
        returnMap.put("custtransfervol",custTransfervol);

        //份额转让
        CounterBusiEnum selectedBusiEnum=CounterBusiEnum.TRADE_TRANS_VOL;
        //hb_constant.typeCode=countertransfer
        //下拉框  业务类型
        List<CounterBusiEnum>  availSelectList = Lists.newArrayList(selectedBusiEnum);
        //默认选中业务类型
        returnMap.put("selectedBusiType", selectedBusiEnum.getKey());
        returnMap.put("availSelectList", enumToDropDownMap(availSelectList));

        return new ModelAndView("/counter/upTransactionByTransfer", returnMap);
    }

    /**
     * 查询业务对应产品或者模板
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/getcounterbusiness")
    public BaseResponse<CmCounterBusinessMsgViewDto> getCounterBusiness(HttpServletRequest request){

        GetCounterBusinessParamVo paramVo=new GetCounterBusinessParamVo();
        paramVo.setConscustNo(request.getParameter("conscustNo"));
        paramVo.setPCode(request.getParameter("pCode"));
        paramVo.setBusiType(request.getParameter("busiType"));
        paramVo.setPreType(request.getParameter("preType"));
        String preId = request.getParameter("preId");
        //forId . 历史原因 固定只有预约才有特殊处理， 此处原命名preId 比较狭隘
        paramVo.setForId(preId);


        BaseResponse<CmCounterBusinessMsgDto>  counterResp= getPostEntityByJsonObject(CrmTradeServerPathConstant.GET_BUSSINESS_BY_BUSITYPE,
                                                paramVo,
                                                new ParameterizedTypeReference<BaseResponse<CmCounterBusinessMsgDto>>(){});

        BaseResponse<CmCounterBusinessMsgViewDto> viewResp=new BaseResponse<>();
        viewResp.setReturnCode(counterResp.getReturnCode());
        viewResp.setReturnMsg(counterResp.getReturnMsg());

        //补充外部文件信息：
        CmCounterBusinessMsgViewDto viewDto=null;
        CmCounterBusinessMsgDto businessMsgDto=counterResp.getData();
        if(businessMsgDto!=null){
            CounterBusiEnum busiEnum=CounterBusiEnum.getEnum(businessMsgDto.getBusiId());
            viewDto=new CmCounterBusinessMsgViewDto();
            BeanUtils.copyProperties(businessMsgDto,viewDto);

            //外部文件信息列表
            // 海外预约类的业务
            if(CounterUtil.PREBOOK_HK_TRADELIST_ONLINE.contains(busiEnum)){
                viewDto.setOuterBusiFileList(getHkSignFileListByPreId(new BigDecimal(preId)));
            }

        }
        viewResp.setData(viewDto);
        return viewResp;
    }

    /**
     * 如果与产品相关，则获取对应产品编码和产品简称
     *
     * @param request
     * @param response
     */
    @ResponseBody
    @RequestMapping("/getconscustpcode")
    public List<ComboboxItem> getconscustpcode(HttpServletRequest request,
                                 HttpServletResponse response) {

        //TODO :  该逻辑 待重构 。  持仓 应该 来源于 三个渠道：  【crm直销持仓】、【高端中台持仓】、【海外持仓】
        //TODO: 该接口 逻辑 正常。 但是其中  份额大于0 的逻辑 。 待商榷 。

        String hboneno = request.getParameter("hboneno");

        //获取高端持仓产品，并且过滤掉重复产品
        QueryAcctBalanceRequest req = new QueryAcctBalanceRequest();
        req.setOutletCode("A20150120");
        req.setOperIp(getIpAddr(request));
        req.setTxChannel("1");
        req.setDataTrack("crmsale");
        req.setHbOneNo(hboneno);
        req.setDisCodeList(jjxxInfoService.getHbFullDisCodeList());
        List<QueryAcctBalanceResponse.BalanceBean> listbean = queryAcctBalanceFacade.execute(req).getBalanceList();
        List<JjxxInfo> jjxxlist = new ArrayList<JjxxInfo>();
        List<String> jjjcs = new ArrayList<String>();
        for (QueryAcctBalanceResponse.BalanceBean bean : listbean) {
            if (bean.getBalanceVol() != null && bean.getBalanceVol().compareTo(new BigDecimal(0)) > 0 && StringUtils.isNotBlank(bean.getProductCode())) {
                JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(bean.getProductCode(), false);
                if (jjxx1 != null && !jjjcs.contains(jjxx1.getJjdm())) {
                    jjxxlist.add(jjxx1);
                    jjjcs.add(jjxx1.getJjdm());
                }
            }
        }

        List<ComboboxItem> resultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(jjxxlist)) {
            resultList.add(new ComboboxItem("", "请选择"));
            jjxxlist.forEach(jjxx1 -> {
                String jjdm = removeSpecialChar(jjxx1.getJjdm());
                String jjjc = removeSpecialChar(jjxx1.getJjjc());
                resultList.add(new ComboboxItem(jjdm, jjjc));
            });
        }
        return resultList;
    }

    // 移除特殊字符
    private String removeSpecialChar(String str) {
        if (StringUtils.isNotBlank(str)) {
            return str.replaceAll("\n", StringUtils.EMPTY).replaceAll("\t", StringUtils.EMPTY).replaceAll("\r", StringUtils.EMPTY);
        } else {
            return str;
        }
    }

    
    /**
     * 非交易类-资料管理上传（投顾客户）
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/uploadCounterTransactionfile", method = RequestMethod.POST)
    public BaseResponse<String> uploadCounterTransactionfile(HttpServletRequest request) {
        String conscustNo = request.getParameter("conscustno");
        String pcode = request.getParameter("pcode");
        String bdId = request.getParameter("bdid");
        String forId=request.getParameter("forId");

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> files = multipartRequest.getFiles("filelist[]");

        SaveCmCounterOrderRequest orderRequest=new SaveCmCounterOrderRequest();
        orderRequest.setBdId(bdId);
        orderRequest.setConscustNo(conscustNo);
        orderRequest.setPCode(pcode);
        orderRequest.setForId(forId);
        orderRequest.setOperatorNo(getLoginUserId(request));

        //根据bdid 获取 busiId .
        CmCounterBusiness businessInfo=getBusinessById(bdId);
        CounterBusiEnum busiEnum=CounterBusiEnum.getEnum(businessInfo.getBusiId());
        // 拼接特殊的业务参数
        // 好买香港-上传打款凭证
        if (CounterBusiEnum.TRADE_HK_PAYMENT_VOUCHER==busiEnum) {
            HkVoucherParamDto paramDto=new HkVoucherParamDto();
            //TODO: 此处参数 需要更多
            paramDto.setRemark(request.getParameter("paymentVoucherRemark"));
            paramDto.setTradeChannel(TradeChannelEnum.CRM_PC.getCode());
            paramDto.setCurrency(request.getParameter("currency"));
            String remitAmtStr = request.getParameter("remitAmt");
            paramDto.setRemitAmt(new BigDecimal(remitAmtStr).multiply(BIG_DECIMAL_1W).setScale(4, RoundingMode.HALF_UP));
            paramDto.setHkCpAcctNo(request.getParameter("hkCpAcctNo"));
            orderRequest.setSpecialBusiParam(JSON.toJSONString(paramDto));
        }

        // busiCode= 54- 好买香港-开户入金 57-好买香港-现金存入   存储特殊属性
        // 历史逻辑  设计问题。此处应该以特殊业务来区分 是否赋值， 而不是直接判断非空。 2024年4月16日重构
        if (CounterBusiEnum.INCOME_PAYMENT_HK==busiEnum || CounterBusiEnum.CASH_DEPOSIT_HK==busiEnum) {
            HkDepositParamDto hkDepositParamDto=new HkDepositParamDto();
            hkDepositParamDto.setAmount(request.getParameter("amount"));
            hkDepositParamDto.setMemo(request.getParameter("memo"));
            hkDepositParamDto.setCurCode(request.getParameter("curCode"));
            hkDepositParamDto.setHkCpAcctNo(request.getParameter("hkCpAcctNo"));
            hkDepositParamDto.setRefNo(request.getParameter("refNo"));
            hkDepositParamDto.setTradeChannel(TradeChannelEnum.CRM_PC.getCode());
            // 根据投顾客户号获取香港号
            HkConscustVO hkConscustVO = hkConscustService.queryHkCustInfoByCustNo(conscustNo);
            String hkCustNo = hkConscustVO==null?"":hkConscustVO.getHkTxAcctNo();
            hkDepositParamDto.setHkCustNo(hkCustNo);
            orderRequest.setSpecialBusiParam(JSON.toJSONString(hkDepositParamDto));
        }
        //58-好买香港-储蓄罐协议签署  59-好买香港-储蓄罐底层变更   60-好买香港-储蓄罐协议终止  字段使用
        if(CounterUtil.CXG_RELATED_BUSI_LIST.contains(busiEnum)){
            HkCxgParamDto paramDto=new HkCxgParamDto();
            paramDto.setCxgFundList(com.google.common.collect.Lists.newArrayList());
            CxgDetailDto cxgDetailDto=new CxgDetailDto();
            cxgDetailDto.setPiggyFundCode(request.getParameter("piggyFundCode"));
            cxgDetailDto.setPiggyFundName(request.getParameter("piggyFundName"));
            paramDto.getCxgFundList().add(cxgDetailDto);
            paramDto.setTradeChannel(TradeChannelEnum.CRM_PC.getCode());
            orderRequest.setSpecialBusiParam(JSON.toJSONString(paramDto));
        }


        String planAmount=request.getParameter("planAmount");
        String minAmt=request.getParameter("minAmt");
        String prodDiffer=request.getParameter("prodDiffer");

        if(StringUtil.isNotNullStr(planAmount)){
            orderRequest.setPlanAmount(new BigDecimal(planAmount));
        }
        if(StringUtil.isNotNullStr(minAmt)){
            orderRequest.setMinAmt(new BigDecimal(minAmt));
        }
        if(StringUtil.isNotNullStr(prodDiffer)){
            orderRequest.setProdDiffer(new BigDecimal(prodDiffer));
        }

        //文件处理
        Map<String, List<String>>  fileMap=processCounterFile (files);
        orderRequest.setAddFileMap(fileMap);

        return getPostEntityByJsonObject(CrmTradeServerPathConstant.INSERT_COUNTER_ORDER,
                orderRequest,
                new ParameterizedTypeReference<BaseResponse<String>>(){});

    }

    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    public String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }


    /**
     * 提交上传文件- 新增【0-家庭  1-个人 2-机构】 资料管理订单
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/uploadrelationfile", method = RequestMethod.POST)
    public BaseResponse<String> uploadrelationfile(HttpServletRequest request) {
        String operatorNo =getLoginUserId(request);
        String conscustno = request.getParameter("conscustno");
        String bdid = request.getParameter("bdid");
        String relationType = request.getParameter("relatedaccount");
        String subConscustNo = request.getParameter("subconscustno");
        String relation = request.getParameter("relation");

        SaveCmCounterOrderRequest orderRequest=new SaveCmCounterOrderRequest();
        orderRequest.setBdId(bdid);
        orderRequest.setConscustNo(conscustno);
        orderRequest.setOperatorNo(operatorNo);
        //新增关联账户 - 类型：-0-家庭  1-个人 2-机构
        orderRequest.setRelationType(relationType);
        //子账户 客户号
        orderRequest.setSubConscustNo(subConscustNo);
        //1-父亲   2-母亲  3-爱人  ... ...
        orderRequest.setRelation(relation);
        //主账户保存记录  入口   不为 追加子账户
        orderRequest.setIsAppendSub(false);

        //crmRelationId crm数据的唯一主键[区别与账户中心更新的  relationId ] . 表命名为 order_id . 但是作为 关联账户的业务的唯一主键。 该主键作为forId 与 资料管理关联 。
        //所以 ： TODO: NOTICE: SEQ_COUNTER_ID 该sequence不能删除！！！！！
        String crmRelationId = commonService.getSeqValue("SEQ_COUNTER_ID");
        orderRequest.setForId(crmRelationId);


        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> files = multipartRequest.getFiles("filelist[]");
        //文件处理
        Map<String, List<String>>  fileMap=processCounterFile (files);
        orderRequest.setAddFileMap(fileMap);

        BaseResponse<String> counterResp= getPostEntityByJsonObject(CrmTradeServerPathConstant.INSERT_COUNTER_ORDER,
                orderRequest,
                new ParameterizedTypeReference<BaseResponse<String>>(){});

        //上传不成功 ：
        if(!counterResp.isSuccess()){
            return  counterResp;
        }
        //插入关联账户数据
        RelationTypeEnum typeEnum=RelationTypeEnum.getEnum(relationType);
        cmRelationAccountService.insertCmRelationAccount(conscustno,typeEnum,subConscustNo,relation,crmRelationId,operatorNo);
        return BaseResponse.ok();

    }

    /**
     * 提交上传文件-资料管理[34-解除家庭账户  35-解除个人关联账户  36-解除机构关联账户]
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/uploadremoverelationfile", method = RequestMethod.POST)
    public BaseResponse<String> uploadRemoveRelationfile(HttpServletRequest request) {
        String relationId =request.getParameter("relationId");

        CmRelationAccount relationAccount=cmRelationAccountService.getRelationAccount(relationId);
        if(relationAccount==null){
            return  BaseResponse.fail(String.format("根据relationId:%s  无法获取关联账户数据！",relationId));
        }

        String bdid = request.getParameter("bdid");
        String operatorNo =getLoginUserId(request);

        SaveCmCounterOrderRequest orderRequest=new SaveCmCounterOrderRequest();
        orderRequest.setBdId(bdid);
        orderRequest.setConscustNo(relationAccount.getConscustno());
        orderRequest.setOperatorNo(operatorNo);

        //crmRelationId crm数据的唯一主键[区别与账户中心更新的  relationId ] . 表命名为 order_id . 但是作为 关联账户的业务的唯一主键。 该主键作为forId 与 资料管理关联 。
        // 该关联关系 如果--> 解除关联关系。 则 更新orderId--> 新的id .并将该orderId 作为 解除关联关系资料订单的 forId
        //所以 ： TODO: NOTICE: SEQ_COUNTER_ID 该sequence不能删除！！！！！
        String crmRelationId = commonService.getSeqValue("SEQ_COUNTER_ID");
        orderRequest.setForId(crmRelationId);


        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> files = multipartRequest.getFiles("filelist[]");
        //文件处理
        Map<String, List<String>>  fileMap=processCounterFile (files);
        orderRequest.setAddFileMap(fileMap);

        BaseResponse<String> counterResp= getPostEntityByJsonObject(CrmTradeServerPathConstant.INSERT_COUNTER_ORDER,
                orderRequest,
                new ParameterizedTypeReference<BaseResponse<String>>(){});

        //上传不成功 ：
        if(!counterResp.isSuccess()){
            return  counterResp;
        }
        //新增 解除关联账户的 数据
        cmRelationAccountService.removeCmRelationAccount(relationId,crmRelationId,operatorNo);
        return BaseResponse.ok();
    }

    /**
     * 提交上传文件
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/uploadrelationsubfile", method = RequestMethod.POST)
    public BaseResponse<String> uploadRelationSubFile(HttpServletRequest request) {
        String relationId =request.getParameter("relationId");
        String subconscustno=request.getParameter("subconscustno");
        String relation=request.getParameter("relation");

        CmRelationAccount relationAccount=cmRelationAccountService.getRelationAccount(relationId);
        if(relationAccount==null){
            return  BaseResponse.fail(String.format("根据relationId:%s  无法获取关联账户数据！",relationId));
        }


        String bdid = request.getParameter("bdid");
        String operatorNo =getLoginUserId(request);

        SaveCmCounterOrderRequest orderRequest=new SaveCmCounterOrderRequest();
        orderRequest.setBdId(bdid);
        orderRequest.setConscustNo(relationAccount.getConscustno());
        orderRequest.setOperatorNo(operatorNo);


        //子账户 客户号
        orderRequest.setSubConscustNo(subconscustno);
        //1-父亲   2-母亲  3-爱人  ... ...
        orderRequest.setRelation(relation);
        orderRequest.setIsAppendSub(true);


        // 追加 子账户。  新增一条子账户记录。
        //所以 ： TODO: NOTICE: SEQ_COUNTER_ID 该sequence不能删除！！！！！
        String crmRelationId = commonService.getSeqValue("SEQ_COUNTER_ID");
        orderRequest.setForId(crmRelationId);


        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> files = multipartRequest.getFiles("filelist[]");
        //文件处理
        Map<String, List<String>>  fileMap=processCounterFile (files);
        orderRequest.setAddFileMap(fileMap);
        BaseResponse<String> counterResp= getPostEntityByJsonObject(CrmTradeServerPathConstant.INSERT_COUNTER_ORDER,
                orderRequest,
                new ParameterizedTypeReference<BaseResponse<String>>(){});

        //上传不成功 ：
        if(!counterResp.isSuccess()){
            return  counterResp;
        }
        //新增 子账户的 数据

        cmRelationAccountSubService.insertCmRelationAccount(relationId,subconscustno,relation,crmRelationId, operatorNo);
        return BaseResponse.ok();
    }

    /**
     * 提交上传文件
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/uploadremovesubrelationfile", method = RequestMethod.POST)
    public BaseResponse<String>  uploadRemoveSubRelationfile(HttpServletRequest request) {

        String relationId =request.getParameter("relationId");
        String subconscustno =request.getParameter("subconscustno");


        CmRelationAccount relationAccount=cmRelationAccountService.getRelationAccount(relationId);
        if(relationAccount==null){
            return  BaseResponse.fail(String.format("根据relationId:%s  无法获取关联账户数据！",relationId));
        }

        String bdid = request.getParameter("bdid");
        String operatorNo =getLoginUserId(request);
        SaveCmCounterOrderRequest orderRequest=new SaveCmCounterOrderRequest();
        orderRequest.setBdId(bdid);
        orderRequest.setConscustNo(relationAccount.getConscustno());
        orderRequest.setOperatorNo(operatorNo);



        //crmRelationId crm数据的唯一主键[区别与账户中心更新的  relationId ] . 表命名为 order_id . 但是作为 关联账户的业务的唯一主键。 该主键作为forId 与 资料管理关联 。
        // 该关联关系 如果--> 解除关联关系。 则 更新orderId--> 新的id .并将该orderId 作为 解除关联关系资料订单的 forId
        //所以 ： TODO: NOTICE: SEQ_COUNTER_ID 该sequence不能删除！！！！！
        String crmRelationId = commonService.getSeqValue("SEQ_COUNTER_ID");
        orderRequest.setForId(crmRelationId);


        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> files = multipartRequest.getFiles("filelist[]");
        //文件处理
        Map<String, List<String>>  fileMap=processCounterFile (files);
        orderRequest.setAddFileMap(fileMap);
        BaseResponse<String> counterResp= getPostEntityByJsonObject(CrmTradeServerPathConstant.INSERT_COUNTER_ORDER,
                orderRequest,
                new ParameterizedTypeReference<BaseResponse<String>>(){});

        //上传不成功 ：
        if(!counterResp.isSuccess()){
            return  counterResp;
        }
        cmRelationAccountSubService.removeCmRelationSubAccount(relationId,crmRelationId,subconscustno);
        return BaseResponse.ok();
    }
}
