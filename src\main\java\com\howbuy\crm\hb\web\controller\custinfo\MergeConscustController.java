package com.howbuy.crm.hb.web.controller.custinfo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.auth.facade.decrypt.DecryptBatchFacade;
import com.howbuy.auth.facade.response.CodecBatchResponse;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.trade.common.response.BaseResponse;
import com.howbuy.crm.trade.model.counter.po.CmCounterBusiFile;

import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;

import com.howbuy.crm.hb.constants.CrmTradeServerPathConstant;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.custinfo.MergeConscustService;
import com.howbuy.crm.hb.web.controller.counter.BaseCounterController;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;

import lombok.extern.slf4j.Slf4j;

/**
 * @version 1.0
 * @Description: 合并客户管理 Controller
 */
@Slf4j
@Controller
@RequestMapping(value = "/mergeConscust")
public class MergeConscustController   extends BaseCounterController {

    @Autowired
    private ConscustService conscustService;

    @Autowired
    private MergeConscustService mergeConscustService;
    
    @Autowired
	private DecryptBatchFacade decryptBatchFacade;

 
    /**
	 * 跳转到合并客户查询列表页面
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listMergeConscust.do")
	public ModelAndView listMergeConscust(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/custinfo/mergeConscustList");
		return modelAndView;
	}
	
	
	/**
	 * 加载列表页面数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listMergeConscustByPage.do")
	public Map<String, Object> listMergeConscustByPage(HttpServletRequest request)	throws Exception {
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String mobile = request.getParameter("mobile");
		String idno = request.getParameter("idno");
		String custNo = request.getParameter("custNo");
		String hboneNo = request.getParameter("hboneNo");
		param.put("custNo", StringUtils.isBlank(custNo)?null:custNo);
		param.put("hboneNo", StringUtils.isBlank(hboneNo)?null:hboneNo);
		// 是否已配置文件  1:已配置文件，0：未配置文件
		if (StringUtils.isNotBlank(mobile)) {
			param.put("mobile", DigestUtil.digest(mobile.trim()));
		} else {
			param.put("mobile", null);
		}
				
		// 设置查询参数（ 业务属性）
		if (StringUtils.isNotBlank(idno)) {
			param.put("idno", DigestUtil.digest(idno.trim()));
		} else {
			param.put("idno", null);
		}
		param.put("conscuststatus", "0");
		PageData<Conscust> pageData = mergeConscustService.listMergeConscustByPage(param);
		
		// 对枚举字段进行转义
		ConstantCache constantCache= ConstantCache.getInstance();
		for (Conscust info : pageData.getListData()) {
			info.setIdtype(constantCache.getVal("idtype", info.getIdtype()));
		}
		
		//密文转明文
		batchHandleCustDecrypt(pageData.getListData());
		
		// 返回查询结果
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<Conscust> listdata = pageData.getListData();
		resultMap.put("rows", listdata);
		
		return resultMap;
	}
	
	
	/**
	 *
	 * 功能描述：合并客户号>
     * @param request
	 * @return String
	 * @throws Exception 
	 */
	@RequestMapping(value="/searchMergeCust")
	public ModelAndView searchMergeCust(HttpServletRequest request) throws Exception {
		String conscustnostr = request.getParameter("conscustnostr");
		String[] conscustarr = conscustnostr.split("\\|");
		String conscusteStr = "";
		Arrays.sort(conscustarr);
		for(int i=0;i<conscustarr.length;i++){
			conscusteStr = conscusteStr+"'"+conscustarr[i]+"',";
		}
		Conscust paramConscust = new Conscust();
		paramConscust.setConscustStr(conscusteStr.substring(0,conscusteStr.length()-1));
		List<Conscust> mergeConscusts = conscustService.listConscust(paramConscust);
		
		//密文转明文
		batchHandleCustDecrypt(mergeConscusts);
		
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.addObject("mergeList", mergeConscusts);
		modelAndView.addObject("conscusteStr", conscusteStr);
		modelAndView.setViewName("/custinfo/mergeConscust");
		return modelAndView;
	}
	
	/**
	 *
	 * 选择合并的客户号
    * @param request
	 * @return String
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/checkconscustview" , method = RequestMethod.POST)
	public Map<String,Object>  checkconscustview(HttpServletRequest request) throws Exception {
		String conscustno = request.getParameter("conscustno");
		String conscusteStr = request.getParameter("conscusteStr");
		if(StringUtils.isNotBlank(conscusteStr)){
			conscusteStr = conscusteStr.substring(0,conscusteStr.length()-1);
		}
		Conscust parConscust = new Conscust();
		parConscust.setConscustStr(conscusteStr);
		List<Conscust> mergeConscusts = conscustService.listConscust(parConscust);
		//密文转明文
		batchHandleCustDecrypt(mergeConscusts);
		Map<String,String> restypeMap = new HashMap<String,String>();
		Map<String,String> phoneMap = new HashMap<String,String>();
		Map<String,String> telnoMap = new HashMap<String,String>();
		Map<String,String> emailMap = new HashMap<String,String>();
		Map<String,String> addrMap = new HashMap<String,String>();
		Map<String,String> custNameMap = new HashMap<String,String>();
		Conscust conscust = null;
		for (Conscust info : mergeConscusts) {
			//资源类型
			info.setRestype( info.getRestype()==null ? "" : info.getRestype());
			//手机号组
			info.setMobile(info.getMobile()==null ? "" : info.getMobile());
			info.setMobile2(info.getMobile2()==null ? "" : info.getMobile2());
			info.setLinkmobile(info.getLinkmobile()==null ? "" : info.getLinkmobile());
			//座机组
			info.setTelno(info.getTelno()==null ? "" : info.getTelno());
			info.setLinktel(info.getLinktel()==null ? "" : info.getLinktel());
			//邮箱组
			info.setEmail(info.getEmail()==null ? "" : info.getEmail());
			info.setEmail2(info.getEmail2()==null ? "" : info.getEmail2());
			info.setLinkemail(info.getLinkemail()==null ? "" : info.getLinkemail());
			//地址组
			info.setAddr(info.getAddr()==null ? "" : info.getAddr());
			info.setAddr2(info.getAddr2()==null ? "" : info.getAddr2());
			info.setLinkaddr(info.getLinkaddr()==null ? "" : info.getLinkaddr());
			
			if(StringUtils.isNotBlank(info.getRestype())){
				if("0".equals(info.getRestype())){
					restypeMap.put(info.getRestype(), "公司资源");
					info.setRestypename("公司资源");
				}
				if("1".equals(info.getRestype())){
					restypeMap.put(info.getRestype(), "投顾资源");
					info.setRestypename("投顾资源");
				}
			}else{
				restypeMap.put("", "");
				info.setRestypename("");
			}
			if(conscustno.equals(info.getConscustno())){
				conscust = info;
			}
			info.setCustname(info.getCustname()==null ? "" : info.getCustname());
			
			//手机号组
			phoneMap.put(info.getMobile(), StringUtil.replaceNull(info.getMobileMask()));
			phoneMap.put(info.getMobile2(), StringUtil.replaceNull(info.getMobile2Mask()));
			phoneMap.put(info.getLinkmobile(), StringUtil.replaceNull(info.getLinkmobileMask()));
			//座机组
			telnoMap.put(info.getTelno(), StringUtil.replaceNull(info.getTelnoMask()));
			telnoMap.put(info.getLinktel(), StringUtil.replaceNull(info.getLinktelMask()));
			//邮箱组
			emailMap.put(info.getEmail(), info.getEmail());
			emailMap.put(info.getEmail2(), info.getEmail2());
			emailMap.put(info.getLinkemail(), info.getLinkemail());
			//地址组
			addrMap.put(info.getAddr(), info.getAddr());
			addrMap.put(info.getAddr2(), info.getAddr2());
			addrMap.put(info.getLinkaddr(), info.getLinkaddr());
			//客户姓名
			custNameMap.put(info.getCustname(), info.getCustname());
		}
		ConstantCache constantCache = ConstantCache.getInstance();
		LinkedHashMap<String, String>  linkedHashMap = constantCache.getConstantKeyVal("restype");
		for(String code : linkedHashMap.keySet()){
			restypeMap.put(code, linkedHashMap.get(code));
        }
		
		Map<String,Object> returnMap= new HashMap<String ,Object>();
		returnMap.put("conscust", conscust);
		returnMap.put("restypeMap", restypeMap);
		returnMap.put("phoneMap", phoneMap);
		returnMap.put("telnoMap", telnoMap);
		returnMap.put("emailMap", emailMap);
		returnMap.put("addrMap", addrMap);
		returnMap.put("custNameMap", custNameMap);
		return returnMap;
	}
	
	
	
	/**
	 *
	 * 合并客户
     * @param request
	 * @return String
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/mergecust")
	public Map<String, Object> mergecust(HttpServletRequest request, HttpServletResponse response) throws Exception {
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		log.info("接受合并请求，合并发起人：{}" ,userlogin.getUserId());
    	Map<String,Object> resMap = new HashMap<String,Object> ();
    	resMap.put("returnMsg", "处理成功");
    	resMap.put("returnCode", "0000");
    	
    	String conscustno = request.getParameter("conscustno");
    	String conscusteStr = request.getParameter("conscusteStr");
    	log.info("合并发起参数，指定合并客户号：{},选择的客户号集合：{}" ,conscustno,conscusteStr);
    	if(StringUtils.isBlank(conscustno) || StringUtils.isBlank(conscusteStr) ){
    		resMap.put("returnMsg", "参数有误，不能合并客户");
			resMap.put("returnCode", "9999");
			return resMap;
    	}
    	Conscust paramCust = new Conscust();
    	conscusteStr = conscusteStr.substring(0,conscusteStr.length()-1);
    	paramCust.setConscustStr(conscusteStr);
		List<Conscust> mergeConscusts = conscustService.listConscust(paramCust);
		
		//密文转明文
		batchHandleCustDecrypt(mergeConscusts);
		log.info("batchHandleCustDecrypt===");
		Conscust majorCust = null;
		for(Conscust cust : mergeConscusts){
			if(conscustno.equals(cust.getConscustno())){
				majorCust = cust;
			}
		}
		//合并客户
		resMap = mergeConscustService.mergecust(request,userlogin,mergeConscusts,majorCust,conscustno,conscusteStr,resMap);
		if("0000".equals(resMap.get("returnCode"))){
			upMergeCustCmCounterOrder(conscustno,mergeConscusts);
		}

		//在合并客户时，判断被合并客户在资料管理中是否有上传资料，若有上传资料，则将对应资料的投顾客户号修改成合并后留下的客户
		log.info("待被合并的客户列表：{} ，合并后使用的客户号：{}。 相关资料管理订单，更新客户号！", JSONObject.toJSONString(mergeConscusts),conscustno);
//		TODO:  修改CM_COUNTER_ORDER 的     老的被合并的客户列表：mergeConscusts  。 新的合并后使用的客户：conscustno
    	return resMap;
    }
	
	
	/**
	 * 在合并客户时，判断被合并客户在资料管理中是否有上传资料，若有上传资料，则将对应资料的投顾客户号修改成合并后留下的客户
	 * @param mergeConscusts
	 * @param majorConscustno
	 * @throws Exception
	 */
	private void upMergeCustCmCounterOrder(String majorConscustno,List<Conscust> mergeConscusts) throws Exception{
		Map<String,String> param = new HashMap<String,String>(2);
		// 调用trade接口入参修改
		param.put("newConscustno", majorConscustno);
		for(Conscust conscust : mergeConscusts ){
			if(!majorConscustno.equals(conscust.getConscustno())){
				param.put("oldConscustno", conscust.getConscustno());
				getPostEntityByMap(CrmTradeServerPathConstant.UP_COUNTER_ORDER_CONSCUSTNO, param,new ParameterizedTypeReference<BaseResponse<String>>(){});
			}
		}
	}
	
	/**
	 * 处理客户密文转敏文
	 * @param listConscust
	 */
	private void batchHandleCustDecrypt(List<Conscust> listConscust){
		if(CollectionUtils.isNotEmpty(listConscust)){
			List<String> listinfo = new ArrayList<>(6000);
			for(Conscust conscust : listConscust){
				if(StringUtils.isNotBlank(conscust.getMobileCipher())){
					listinfo.add(conscust.getMobileCipher());
				}
				
				if(StringUtils.isNotBlank(conscust.getMobile2Cipher())){
					listinfo.add(conscust.getMobile2Cipher());
				}
				
				if(StringUtils.isNotBlank(conscust.getLinkmobileCipher())){
					listinfo.add(conscust.getLinkmobileCipher());
				}
				
				if(StringUtils.isNotBlank(conscust.getTelnoCipher())){
					listinfo.add(conscust.getTelnoCipher());
				}
				
				if(StringUtils.isNotBlank(conscust.getLinktelCipher())){
					listinfo.add(conscust.getLinktelCipher());
				}
				
				if(StringUtils.isNotBlank(conscust.getEmailCipher())){
					listinfo.add(conscust.getEmailCipher());
				}
				
				if(StringUtils.isNotBlank(conscust.getEmail2Cipher())){
					listinfo.add(conscust.getEmail2Cipher());
				}
				
				if(StringUtils.isNotBlank(conscust.getLinkemailCipher())){
					listinfo.add(conscust.getLinkemailCipher());
				}
				
				if(StringUtils.isNotBlank(conscust.getAddrCipher())){
					listinfo.add(conscust.getAddrCipher());
				}
				
				if(StringUtils.isNotBlank(conscust.getAddr2Cipher())){
					listinfo.add(conscust.getAddr2Cipher());
				}
				
				if(StringUtils.isNotBlank(conscust.getLinkaddrCipher())){
					listinfo.add(conscust.getLinkaddrCipher());
				}
				
				if(StringUtils.isNotBlank(conscust.getIdnoCipher())){
					listinfo.add(conscust.getIdnoCipher());
				}
			}
			
			//密文转明文
			if(CollectionUtils.isNotEmpty(listinfo)){
				CodecBatchResponse res = decryptBatchFacade.decryptBatch(listinfo);
    			if(res != null && res.getCodecMap() != null){
	    			Map<String, String> resmap = res.getCodecMap();
	    			for(Conscust conscust : listConscust){
	    				conscust.setMobile( StringUtils.isBlank(conscust.getMobileCipher()) ? null : resmap.get(conscust.getMobileCipher()) );
	    				conscust.setMobile2( StringUtils.isBlank(conscust.getMobile2Cipher()) ? null : resmap.get(conscust.getMobile2Cipher()) );
	    				conscust.setLinkmobile( StringUtils.isBlank(conscust.getLinkmobileCipher()) ? null : resmap.get(conscust.getLinkmobileCipher()) );
	    				conscust.setTelno( StringUtils.isBlank(conscust.getTelnoCipher()) ? null : resmap.get(conscust.getTelnoCipher()) );
	    				conscust.setLinktel( StringUtils.isBlank(conscust.getLinktelCipher()) ? null : resmap.get(conscust.getLinktelCipher()) );
	    				conscust.setEmail( StringUtils.isBlank(conscust.getEmailCipher()) ? null : resmap.get(conscust.getEmailCipher()) );
	    				conscust.setEmail2( StringUtils.isBlank(conscust.getEmail2Cipher()) ? null : resmap.get(conscust.getEmail2Cipher()) );
	    				conscust.setLinkemail( StringUtils.isBlank(conscust.getLinkemailCipher()) ? null : resmap.get(conscust.getLinkemailCipher()) );
	    				conscust.setAddr( StringUtils.isBlank(conscust.getAddrCipher()) ? null : resmap.get(conscust.getAddrCipher()) );
	    				conscust.setAddr2( StringUtils.isBlank(conscust.getAddr2Cipher()) ? null : resmap.get(conscust.getAddr2Cipher()) );
	    				conscust.setLinkaddr( StringUtils.isBlank(conscust.getLinkaddrCipher()) ? null : resmap.get(conscust.getLinkaddrCipher()) );
	    				conscust.setIdno( StringUtils.isBlank(conscust.getIdnoCipher()) ? null : resmap.get(conscust.getIdnoCipher()) );
	    			}
    			}
			}
		}
	}
	
}
