package com.howbuy.crm.hb.web.controller.custinfo;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.howbuy.crm.hb.domain.custinfo.ProductShareBonus;
import com.howbuy.crm.hb.service.custinfo.RemindOfProductExpirationService;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.constants.ContextManager;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;

/**
 * 到期提醒controller层
 * 
 * author: wu.long
 * date: 2019年7月8日 上午10:14:08
 * version: V1.0
 * since: jdk 1.8,tomcat 8.0
 */
@Slf4j
@Controller
@RequestMapping(value = "/productexpiration")
public class RemindOfProductExpirationController {
	@Autowired
	private RemindOfProductExpirationService remindOfProductExpirationService;
	
	/**
	 * 加载产品到期提醒页面
	 * 
	 * author: wu.long
	 * date: 2019年7月5日 下午5:07:00 
	 * @param request
	 * @return
	 */
	@RequestMapping("/listTimeEndBonusDept")
	public String listTimeEndBonusDept(HttpServletRequest request) {
		Map<String, String> startAndEndMap = DateTimeUtil.getBonusMonthStartAndEnd();
		request.setAttribute("beginDate",startAndEndMap.get("beginDate"));
		request.setAttribute("endDate",startAndEndMap.get("endDate"));
		return "custinfo/remindOfProductExpiration";
	}
	
	/**
	 * 加载部门产品到期数据方法
	 * 
	 * author: wu.long
	 * date: 2019年7月5日 下午5:38:03 
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/loadTimeEndBonusDept_json")
	public Map<String,Object> loadTimeEndBonusDept_json(HttpServletRequest request, HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String,String> param=new HashMap<String,String>();
		// 获取分页参数
		param=new ParamUtil(request).getParamMap();
		// String curPage = request.getParameter("page");
		String jjdm=request.getParameter("jjdm");
		String jjjc=request.getParameter("jjjc");
		String orgCode=request.getParameter("orgCode");
		String consCode = request.getParameter("consCode");
		String beginDate=request.getParameter("beginDate");
		String endDate=request.getParameter("endDate");
		String searchFlag=request.getParameter("searchFlag");

		// 如果查询条件（基金代码）不为空，则增加基金代码查询参数
		if(StringUtil.isNotNullStr(jjdm)) {
			param.put("jjdm",jjdm);
		}
		else {
			param.put("jjdm",null);
		}
		// 如果查询条件（基金简称）不为空，则增加基金简称查询参数
		if(StringUtil.isNotNullStr(jjjc)) {
			param.put("jjjc",jjjc);
		}
		else {
			param.put("jjjc",null);
		}
		// 如果查询条件（所属部门）不为空，则增加所属部门查询参数
		if(StringUtil.isNotNullStr(orgCode)) {
			if(StringUtil.isNotNullStr(consCode)){
				param.put("consCode", "'"+consCode+"'");
			}else{
				param.put("consCode",Util.getSubQueryByOrgCode(orgCode));
			}
		}
		else {
			// 获取默认登陆用户下属投顾信息
			User userlogin=(User)request.getSession().getAttribute("loginUser");
			String outletcode=ConsOrgCache.getInstance().getCons2OutletMap().get(userlogin.getUserId());
			String tearmcode=ConsOrgCache.getInstance().getCons2TeamMap().get(userlogin.getUserId());
			String groupOrgCode=""; // 登陆用户所属的部门编码
			String loginOrgCode=StringUtil.isNotNullStr(tearmcode)?tearmcode:outletcode; // 登陆用户所属的部门编码
			String topgd=(String)request.getSession().getAttribute("topgddata");
			if(StaticVar.DATARANGE_GD_ALL.equals(topgd)||StaticVar.DATARANGE_GD_ALL_NOWFP.equals(topgd)) {
				groupOrgCode="0";
			}
			else if(StaticVar.DATARANGE_GD_OUTLET.equals(topgd)) {
				groupOrgCode=outletcode;
			}
			else if(StaticVar.DATARANGE_GD_TEARM.equals(topgd)) {
				groupOrgCode=tearmcode;
			}
			else {
				groupOrgCode=loginOrgCode;
			}
			param.put("consCode",Util.getSubQueryByOrgCode(groupOrgCode));
		}
		// 设置默认日期查询参数
		if(StringUtil.isNullStr(searchFlag)) {
			Map<String,String> startAndEndMap=DateTimeUtil.getBonusMonthStartAndEnd();
			param.put("beginDate",startAndEndMap.get("beginDate"));
			param.put("endDate",startAndEndMap.get("endDate"));
		}
		else {
			// 如果查询条件（起始日期）不为空，则增加起始日期查询参数
			if(StringUtil.isNotNullStr(beginDate)) {
				param.put("beginDate",beginDate);
			}
			else {
				param.put("beginDate",null);
			}

			// 如果查询条件（终止日期）不为空，则增加终止日期查询参数
			if(StringUtil.isNotNullStr(endDate)) {
				param.put("endDate",endDate);
			}
			else {
				param.put("endDate",null);
			}
		}
		// 将查询权限信息放入session中
		request.getSession().setAttribute("deptConsCode",param.get("consCode"));
		Map<String,Object> resultMap=new HashMap<String,Object>();
		List<ProductShareBonus> listData = remindOfProductExpirationService.listTimeEndBonusDept(param);
		Map<String,String> consMap=ConsOrgCache.getInstance().getAllConsMap();
		BigDecimal totalCnt=new BigDecimal("0");
		BigDecimal totalAmt=new BigDecimal("0");
		// 对产品分配日期进行格式处理，同时转义分配收益频率字段
		for(ProductShareBonus productShareBonus:listData) {
			if(StringUtil.isNotNullStr(productShareBonus.getFprq())) {
				productShareBonus.setFprq(DateTimeUtil.fmtDate(productShareBonus.getFprq(),"yyyyMMdd"));
			}
			if(StringUtil.isNotNullStr(productShareBonus.getSyfppl())&&ContextManager.SYFPPL_STATUS.containsKey(productShareBonus.getSyfppl())) {
				productShareBonus.setSyfppl(ContextManager.SYFPPL_STATUS.get(productShareBonus.getSyfppl()));
			}
			if(StringUtil.isNotNullStr(productShareBonus.getConsCode())&&consMap.containsKey(productShareBonus.getConsCode())) {
				productShareBonus.setConsName(consMap.get(productShareBonus.getConsCode()));
			}

			// 累加人数
			if(StringUtil.isNotNullStr(productShareBonus.getCnt())) {
				totalCnt=totalCnt.add(new BigDecimal(productShareBonus.getCnt()));
			}
			// 累加金额
			if(StringUtil.isNotNullStr(productShareBonus.getAmt())) {
				totalAmt=totalAmt.add(new BigDecimal(productShareBonus.getAmt()));
			}
		}
		// 总计操作
		ProductShareBonus tempProductShareBonus=new ProductShareBonus();
		tempProductShareBonus.setJjdm("总计：");
		tempProductShareBonus.setCnt(totalCnt.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		tempProductShareBonus.setAmt(totalAmt.setScale(2,BigDecimal.ROUND_HALF_UP).toString());
		listData.add(tempProductShareBonus);
		resultMap.put("rows",listData);
		return resultMap;
	}
	
	/**
	 * 跳转到部门产品持仓收益详细页面
	 * 
	 * author: wu.long
	 * date: 2019年7月8日 下午2:41:49 
	 * @param request
	 * @param response
	 * @param model
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping("/listProductHoldingDept")
	public String listProductHoldingDept(HttpServletRequest request,HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
		String jjdm=request.getParameter("jjdm");
		String consCode=request.getParameter("consCode");
		request.setAttribute("jjdm",jjdm);
		request.setAttribute("consCode",consCode);
		return "custinfo/productHoldingDetail";
	}
	
	/**
	 * 根据条件查询部门产品持仓收益明细
	 * 
	 * author: wu.long
	 * date: 2019年7月10日 下午1:34:34 
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/loadProductHoldingDept_json")
	public Map<String,Object> loadProductHoldingDept_json(HttpServletRequest request,HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String,String> param=new HashMap<String,String>();
		// 获取分页参数
		param=new ParamUtil(request).getParamMap();
		// 获取查询权限信息
		String consCode=(String)request.getSession().getAttribute("deptConsCode");
		param.put("consCodes",consCode);
		String curPage=request.getParameter("page");
		String jjdm=request.getParameter("jjdm");
		String custName=request.getParameter("custName");
		// 如果查询条件（基金代码）不为空，则增加基金代码查询参数
		if(StringUtil.isNotNullStr(jjdm)) {
			param.put("jjdm",jjdm);
		}
		else {
			param.put("jjdm",null);
		}
		// 如果查询条件（客户姓名）不为空，则增加客户姓名查询参数
		if(StringUtil.isNotNullStr(custName)) {
			param.put("custName",custName);
		}
		else {
			param.put("custName",null);
		}
		Map<String,Object> resultMap=new HashMap<String,Object>();
		PageData<ProductShareBonus> loadData = remindOfProductExpirationService.listProductHoldingByPage(param);
		// 对产品分配日期进行格式处理，同时转义分配收益频率字段
		for(ProductShareBonus productShareBonus:loadData.getListData()) {
			if(StringUtil.isNotNullStr(productShareBonus.getFprq())) {
				productShareBonus.setFprq(DateTimeUtil.fmtDate(productShareBonus.getFprq(),"yyyyMMdd"));
			}
			if(StringUtil.isNotNullStr(productShareBonus.getSyfppl())&&ContextManager.SYFPPL_STATUS.containsKey(productShareBonus.getSyfppl())) {
				productShareBonus.setSyfppl(ContextManager.SYFPPL_STATUS.get(productShareBonus.getSyfppl()));
			}
		}
		resultMap.put("total",loadData.getPageBean().getTotalNum());
		resultMap.put("page",curPage);
		resultMap.put("rows",loadData.getListData());
		return resultMap;
	}
	
	/**
	 * 加载历史到期查询页面
	 * 
	 * author: wu.long
	 * date: 2019年7月15日 上午10:47:33 
	 * @param request
	 * @param response
	 * @param model
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping("/showHistoryExpirationQueryPage")
	public String showHistoryExpirationQueryPage(HttpServletRequest request,HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
		return "custinfo/historyExpirationQuery";
	}
	
	/**
	 * 根据条件查询历史到期查询信息
	 * 
	 * author: wu.long
	 * date: 2019年7月15日 下午2:39:59 
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/selectHistoryExpirationQueryByParams")
	public Map<String, Object> selectHistoryExpirationQueryByParams(HttpServletRequest request) {
		Map<String, String> paramMap = new HashMap<String, String>();
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			//获取分页参数
			paramMap = new ParamUtil(request).getParamMap();
			paramMap.put("jjdm", request.getParameter("jjdm"));
			paramMap.put("jjjc", request.getParameter("jjjc"));
			resultMap = remindOfProductExpirationService.selectHistoryExpirationQueryByPage(paramMap);
		} catch (Exception e) {
			log.error("历史到期查询异常：", e);
		}
		return resultMap;
	}
	
	
	
	
	
	
	
	
	
	
}
