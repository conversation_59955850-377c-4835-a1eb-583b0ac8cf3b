/**   
 * @Title: StringSQLLength.java 
 * @Package com.hb.crm.web.util.excel.util 
 * @Description: TODO(用一句话描述该文件做什么) 
 * <AUTHOR>
 * @date 2016年5月18日 下午3:17:07 
 * @version V1.0   
 */
package com.howbuy.crm.hb.tools.excel.util;


/**
 * @ClassName: StringSQLLength
 * @Description: TODO(这里用一句话描述这个类的作用)
 * <AUTHOR>
 * @date 2016年5月18日 下午3:17:07
 * 
 */

public class DBStringUtils {
	
	/**   
	 * @Title: isLetter   
	 * @Description: 判断是否是字符，中文判断不为字符
	 * @param c
	 * @return        
	 */
	 
	private static boolean isLetter(char c) {
		int k = 0x80;
		return c / k == 0 ? true : false;
	}

	/**   
	 * @Title: toCharLength   
	 * @Description:非字符的按照2的长度字符按照1的长度  
	 * @param value
	 * @return        
	 */
	 
	public static int toCharLength(String value) {
		int length = 0;
		char[] chars = value.toCharArray();
		for (int i = 0; i < chars.length; i++) {
			char c = chars[i];
			if (isLetter(c)) {
				length = length + 1;
			} else {
				length = length + 2;
			}
		}
		return length;
	}
}
