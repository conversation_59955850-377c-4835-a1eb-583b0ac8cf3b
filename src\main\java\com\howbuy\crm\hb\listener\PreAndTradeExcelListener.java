/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.howbuy.crm.base.privatetrade.ImpTypeEnum;
import com.howbuy.crm.base.privatetrade.TradeBusiCodeEnum;
import com.howbuy.crm.hb.domain.prosale.preandtradeimport.CmPreAndTradeImportViewDto;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/3/24 20:28
 * @since JDK 1.8
 */
@Component
@Slf4j
public class PreAndTradeExcelListener extends AnalysisEventListener<CmPreAndTradeImportViewDto> {


    private Function<CmPreAndTradeImportViewDto, String> validateFunction;

    private Consumer<List<CmPreAndTradeImportViewDto>> persistConsumer;

    /**
     * 交易类别
     */
    private TradeBusiCodeEnum busiCodeEnum;
    /**
     * 申赎类型
     */
    private ImpTypeEnum impTypeEnum;

    /**
     * 创建人
     */
    private String creator;

    public PreAndTradeExcelListener() {
    }


    /**
     *
     * @param busiCodeEnum
     * @param impTypeEnum
     * @param creator
     */
    public PreAndTradeExcelListener(TradeBusiCodeEnum busiCodeEnum,
                                    ImpTypeEnum impTypeEnum,
                                    String creator,
                                    Function<CmPreAndTradeImportViewDto, String> validateFunction ,
                                    Consumer<List<CmPreAndTradeImportViewDto>> persistConsumer) {
        this.busiCodeEnum=busiCodeEnum;
        this.impTypeEnum=impTypeEnum;
        this.creator=creator;
        this.validateFunction=validateFunction;
        this.persistConsumer=persistConsumer;
    }

    private PreAndTradeExcelReadInfo readResultInfo = new PreAndTradeExcelReadInfo();

    @Override
    public void invoke(CmPreAndTradeImportViewDto data, AnalysisContext context) {
        data.setBusiCode(busiCodeEnum.getCode());
        data.setImpType(impTypeEnum.getCode());
        data.setCreator(creator);
        String errorMsg= validateFunction.apply(data);
        if(StringUtil.isNotNullStr(errorMsg)){
            log.info("导入数据未通过校验：data:{},错误信息：{}", JSON.toJSONString(data),errorMsg);
            readResultInfo.getErrorMsgList().add(errorMsg);
        }else{
            // 将泛型数据添加到 returnObj 的 dataList 中
            readResultInfo.getDataList().add(data);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 所有数据解析完成后的逻辑
        if(!CollectionUtils.isEmpty(readResultInfo.getErrorMsgList())){
           log.info("解析Excel. 包含错误行数：{}",readResultInfo.getErrorMsgList().size());
          return;
        }
        log.info("解析Excel. 持久化行数：{}",readResultInfo.getDataList().size());
        if(!CollectionUtils.isEmpty(readResultInfo.getDataList())){
            persistConsumer.accept(readResultInfo.getDataList());
        }


//
//        List<CmPreTradeImport> saveList= Lists.newArrayList();
//        readResultInfo.getDataList().stream().forEach(data->{
//            saveList.add(transfer(data));
//        });
//        cmPreTradeImportService.batchInsertList(saveList);
    }

    public PreAndTradeExcelReadInfo getReadResultInfo() {
        return readResultInfo;
    }



    /**
     * 获取转换异常
     *
     * @param exception ExcelDataConvertException
     * @param context   excel上下文
     */
//    @Override
//    public void onException(Exception exception, AnalysisContext context) {
//        // 如果是某一个单元格的转换异常 能获取到具体行号
//        // 如果要获取头的信息 配合doAfterAllAnalysedHeadMap使用
//        if (exception instanceof ExcelDataConvertException) {
//            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
//
//        }
//    }


}