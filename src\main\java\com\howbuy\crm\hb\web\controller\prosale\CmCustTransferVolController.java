package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoFacade;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoRequest;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoResponse;
import com.howbuy.crm.base.*;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.request.QueryCustconstantInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.response.QueryCustconstantInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.conscust.service.QueryCustconstantInfoService;
import com.howbuy.crm.fractionatedcall.service.NoticeAmtToZtService;
import com.howbuy.crm.hb.domain.custinfo.CmCustconstant;
import com.howbuy.crm.hb.domain.prosale.CmCustTransfervol;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.CmCustconstantService;
import com.howbuy.crm.hb.service.product.ProductManagerService;
import com.howbuy.crm.hb.service.prosale.CmCustTransfervolService;
import com.howbuy.crm.hb.service.prosale.ManyCallPreInfoService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.service.system.CmOptLogService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.organization.dto.OrgLayerInfo;
import com.howbuy.crm.organization.service.HbOrganizationService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.core.webapp.util.SessionUserManager;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.prebook.service.PrebookBusinessService;
import com.howbuy.crm.prebook.vo.CmPreBookInsertVo;
import com.howbuy.crm.privatetrade.dto.CmCustprivatefund;
import com.howbuy.crm.privatetrade.service.CmCustFundBalanceService;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import com.howbuy.crm.prosale.dto.Prebookmanycallinfo;
import com.howbuy.crm.prosale.request.GetInfoByParamRequest;
import com.howbuy.crm.prosale.request.UpdatePrebookInfoRequest;
import com.howbuy.crm.prosale.response.GetPreBalanceVolResponse;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import com.howbuy.simu.dto.base.product.SmjzDto;
import com.howbuy.simu.service.base.product.SmjzService;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceNew.QueryAcctBalanceNewFacade;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.enums.CrmCustInvestTypeEnum;
import crm.howbuy.base.enums.DisCodeEnum;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.DisCodeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description:
 * @reason:
 * @Date: 2020/2/25 16:13
 */
@Controller
@RequestMapping("/prosale")
@Slf4j
public class CmCustTransferVolController  extends BaseController {

    @Autowired
    private CmCustTransfervolService cmCustTransfervolService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private QueryPreBookService queryPreBookService;
    @Autowired
    private QueryConscustInfoService queryConscustInfoService;
    @Autowired
    private QueryCustconstantInfoService queryCustconstantInfoService;
    @Autowired
    private SmjzService smjzService;
    @Autowired
    private QueryAccHboneInfoFacade queryAccHboneInfoFacade;
    @Autowired
    private ManyCallPreInfoService manyCallPreInfoService;
    @Autowired
    private PreBookService preBookService;
    @Autowired
    private PrebookproductinfoService prebookproductinfoService;
    @Autowired
    private CmCustconstantService cmCustconstantService;
    @Autowired
    private CmOptLogService cmOptLogService;
    @Autowired
    private NoticeAmtToZtService noticeAmtToZtService;
    @Autowired
    private ProductManagerService productManagerService;
    @Autowired
    private JjxxInfoService jjxxInfoService;
    @Autowired
    private HbOrganizationService coreOrganizationService;

    @Autowired
    private PrebookBusinessService prebookBusinessService;

    @Autowired
    private CmCustFundBalanceService cmCustFundBalanceService;

    @Autowired
    private PrebookBasicInfoService prebookBasicInfoService;

    @Autowired
    private QueryAcctBalanceNewFacade queryAcctBalanceNewFacade;

    private final String ORG_HOWBUY = "0";

    /**
     * 请求中台接口的固定传参
     */
    public  static final String OUTLET_CODE = "A20150120";
    /**
     * 请求中台接口的固定传参
     */
    public  static final String DATA_TACK = "crmsale";

    /**
     * 持仓类型：2-全部
     */
    public  static final String ALL_BALANCES = "2";

    /**
     * 跳转到listTransferVol页面方法
     *
     * @return String
     */
    @RequestMapping("/transfervolList.do")
    public String listCustTransfervol() {
        return "prosale/listTransferVol";
    }

    /**
     * 加载页面数据方法
     *
     * @param request
     * @return Map<String, Object>
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listTransferVolByPage_json.do")
    public Map<String, Object> listTransferVolByPageJson(HttpServletRequest request) throws Exception {

        PageData<CmCustTransfervol> pageData = cmCustTransfervolService.listTransferVolByPage(queryParam(request));
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<CmCustTransfervol> listdata = pageData.getListData();
        getCmCustTransferList(listdata);
        resultMap.put("rows", listdata);
        return resultMap;
    }

    /**
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @throws
     * @Title: queryParam 参数转换
     * @Author: yu.zhang
     * @DateTime: 2023/2/20 10:49
     * @param: [request]
     */
    private Map<String, String> queryParam(HttpServletRequest request) throws Exception {
        // 设置查询参数
        Map<String, String> param;
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();
        String transferor = request.getParameter("transferor");
        String assignee = request.getParameter("assignee");
        String stradedt = request.getParameter("stradedt");
        String etradedt = request.getParameter("etradedt");
        String fundcode = request.getParameter("fundcode");
        String checkstat = request.getParameter("transferstate");
        String transorgCode = request.getParameter("transorgCode");
        String transconsCode = request.getParameter("transconsCode");
        String assignorgCode = request.getParameter("assignorgCode");
        String assignconsCode = request.getParameter("assignconsCode");
        if (StringUtil.isNotNullStr(transferor)) {
            param.put("transferor", transferor);
        }
        if (StringUtil.isNotNullStr(assignee)) {
            param.put("assignee", assignee);
        }
        if (StringUtil.isNotNullStr(stradedt)) {
            param.put("stradedt", stradedt);
        }
        if (StringUtil.isNotNullStr(etradedt)) {
            param.put("etradedt", etradedt);
        }
        if (StringUtil.isNotNullStr(fundcode)) {
            param.put("fundcode", fundcode);
        }
        if (StringUtil.isNotNullStr(checkstat)) {
            param.put("checkstat", checkstat);
        }
        if (StringUtil.isNotNullStr(transconsCode)) {
            param.put("transconscode", transconsCode);
        } else {
            if (!ORG_HOWBUY.equals(transorgCode)) {
                param.put("transconscodes", Util.getSubQueryByOrgCode(transorgCode));
            }
        }
        if (StringUtil.isNotNullStr(assignconsCode)) {
            param.put("assignconscode", assignconsCode);
        } else {
            if (!ORG_HOWBUY.equals(assignorgCode)) {
                param.put("assignconscodes", Util.getSubQueryByOrgCode(assignorgCode));
            }
        }

        String topCpData = (String) request.getSession().getAttribute("topcpdata");
        if(StaticVar.CP_TOP_DATA_CG.equals(topCpData)){
            param.put("topCpData", topCpData);
        }
        return param;
    }

    /**
     * 加载页面数据方法
     *
     * @param request
     * @return Map<String, Object>
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/exportTransferVol.do")
    public void exportTransferVol(HttpServletRequest request, HttpServletResponse response) throws Exception {

        List<CmCustTransfervol> listdata = cmCustTransfervolService.listTransferVol(queryParam(request));

        if (CollectionUtils.isNotEmpty(listdata)) {
            getCmCustTransferList(listdata);
            try {
                // 清空输出流
                response.reset();
                // 设置文件格式和名字
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition",
                        "attachment;fileName=" + new String("份额转让管理.xls".getBytes("gb2312"), "ISO8859-1"));
                ServletOutputStream os = response.getOutputStream();

                String[] columnName = new String[]{
                        "id", "预计交易日期", "录入时间", "产品名称", "转让份额", "转让金额",
                        "转让人", "转让人-投顾", "转让人-投顾code", "转让人-中心",
                        "转让人-区域", "转让人-部门", "受让人", "受让人-投顾",
                        "受让人-投顾code","受让人-中心","受让人-区域", "受让人-部门",
                        "原认缴金额", "可转让剩余缴款金额", "受让剩余缴款金额", "已分配金额",
                        "确认状态", "确认人", "确认日期", "推介服务费"
                };

                String[] beanProperty = new String[]{
                        "idStr", "tradedt", "creddtVal", "fundname", "transfervol", "transferAmount",
                        "transferorname", "transferorconscode", "transferConsCode","transferCenter",
                        "transferorarea", "transferororg","assigneename", "assigneeconscode",
                        "assigneConsCode","assigneeCenter", "assigneearea", "assigneeorg",
                         "fccloldtotalamt", "fccloldamt", "fcclTransferRemainPayAmt", "allocatedAmt",
                        "checkstatval","checkor", "checkdt", "recommfee"
                };
                ExcelWriter.writeExcel(os, "份额转让管理", 0, listdata, columnName, beanProperty);
                os.close(); // 关闭流
            } catch (Exception e) {
                log.error("文件导出异常", e);
            }
        }
    }

    /**
     * @return java.util.List<com.howbuy.crm.hb.domain.prosale.CmCustTransfervol>
     * @throws
     * @Title: getCmCustTransferList 修改回参
     * @Author: yu.zhang
     * @DateTime: 2023/2/20 10:51
     * @param: [listdata]
     */
    private void getCmCustTransferList(List<CmCustTransfervol> listdata) {
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        //转让人及受让人组织架构
        List<String> orgCodes = Lists.newArrayList();
        listdata.forEach(cmCustTransfervol -> {
            orgCodes.add(cmCustTransfervol.getTransferororg());
            orgCodes.add(cmCustTransfervol.getAssigneeorg());
        });
        //转让人及受让人所属中心
        Map<String, OrgLayerInfo> centerMap = coreOrganizationService.getOrgLayerInfoByOrgCodeList(orgCodes);
        for (CmCustTransfervol info : listdata) {
            info.setIdStr(Long.toString(info.getId()));
            JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
            info.setCheckstatval(Util.getConstantValDes("transferstate", info.getCheckstat()));
            //设投顾编码及所属中心
            info.setTransferConsCode(info.getTransferorconscode());
            OrgLayerInfo transferOrgLayerInfo = centerMap.get(info.getTransferororg());
            OrgLayerInfo assignOrgLayerInfo = centerMap.get(info.getAssigneeorg());
            if(transferOrgLayerInfo != null) {
                info.setTransferCenter(transferOrgLayerInfo.getCenterOrgName());
            }
            //转换转让人的投顾信息
            info.setTransferorconscode(consOrgCache.getAllConsMap().get(info.getTransferorconscode()));
            info.setTransferororg(consOrgCache.getAllOrgMap().get(info.getTransferororg()));
            info.setTransferorarea(consOrgCache.getAllOrgMap().get(info.getTransferorarea()));
            //设投顾编码及所属中心
            info.setAssigneConsCode(info.getAssigneeconscode());
            if(assignOrgLayerInfo != null) {
                info.setAssigneeCenter(assignOrgLayerInfo.getCenterOrgName());
            }
            //转换受让人的投顾信息
            info.setAssigneeconscode(consOrgCache.getAllConsMap().get(info.getAssigneeconscode()));
            info.setAssigneeorg(consOrgCache.getAllOrgMap().get(info.getAssigneeorg()));
            info.setAssigneearea(consOrgCache.getAllOrgMap().get(info.getAssigneearea()));
            info.setCreatorval(consOrgCache.getAllConsMap().get(info.getCreator()));
            info.setCreatororg(consOrgCache.getAllOrgMap().get(consOrgCache.getUser2OutletMap().get(info.getCreator())));
            String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(info.getCreator()));
            if ("0".equals(uporgcode)) {
                info.setUporgname(info.getCreatororg());
            } else {
                info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
            }
            info.setCheckor(consOrgCache.getAllUserMap().get(info.getCheckor()));
            if (jjxx1 != null) {
                info.setFundname(jjxx1.getJjjc());
            }
        }
    }

    /**
     * 跳转到addTransfer页面方法
     *
     * @return String
     */
    @RequestMapping("/addTransfer.do")
    public String addTransfer() throws Exception {
        return "prosale/addTransfer";
    }

    /**
     * 处理转份额录入页面相关的处理
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/addTransferDeal.do")
    public ReturnMessageDto<String> addTransferDeal(HttpServletRequest request) {
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        String transfercustno = request.getParameter("transfercustno");
        String assigncustno = request.getParameter("assigncustno");
        String fundcode = request.getParameter("fundcode");
        String vol = request.getParameter("vol");
        String fcclTransferRemainPayAmt = request.getParameter("fcclTransferRemainPayAmt");
        String totalamt = request.getParameter("totalamt");
        String leftamt = request.getParameter("leftamt");
        String tradedt = request.getParameter("tradedt");
        String fcclflag = request.getParameter("fcclflag");
        String recommfee = request.getParameter("recommfee");
        String transfertotalamt = request.getParameter("transfertotalamt");
        String fccltransfervolamt = request.getParameter("fccltransfervolamt");
        String transferAmount = request.getParameter("transferAmount");


        //拦截香港产品
        String archType =prebookBasicInfoService.getArchType(transfercustno,fundcode);
        if(PreBookArchTypeEnum.HW.getCode().equals(archType)){
            return  ReturnMessageDto.fail("不支持海外产品操作！");
        }

        // 查询该产品是否为股权产品
        //CM_PRODUCTINFO.HBTYPE 好买产品线 （1：现金管理 ，2：债券，3：固定收益，4：对冲，5：股票，6：股权，7：房地产，8：其他）
        //判断该产品 ，是否为 CM_PRODUCTINFO.HBTYPE =='6'
        boolean b = productManagerService.hasGqlabel(fundcode);
        if (b && StringUtils.isBlank(transferAmount)) {
            return  ReturnMessageDto.fail("当前产品是股权产品，转让金额必填！");
        }
        boolean dxflag = queryPreBookService.isDxflag(transfercustno, fundcode);
        //直销产品， 判断持仓
        if (!dxflag) {
            //根据客户号和产品代码
            CmCustprivatefund fund=cmCustFundBalanceService.selectCustPrivateBalance(transfercustno,fundcode);
            if (fund == null || fund.getBalancevol() == null ||
                    fund.getBalancevol().compareTo(new BigDecimal(vol)) < 0  ) {
                return  ReturnMessageDto.fail("转让份额大于转让人当前持有份额，添加失败！");
            }
        }


        String id = commonService.getSeqValue("SEQ_PREBOOK");
        //获取转让人的投顾及部门信息
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        CmCustTransfervol obj = new CmCustTransfervol();
        obj.setId(Long.parseLong(id));
        obj.setTransferor(transfercustno);
        //查询转让人的信息
        Map<String, String> paramconstant = new HashMap<>(1);
        paramconstant.put("custno", transfercustno);
        CmCustconstant tranconstant = cmCustconstantService.getCmCustconstant(paramconstant);
        if (tranconstant != null) {
            String tranfconscode = tranconstant.getConscode();
            if (StringUtil.isNotNullStr(tranfconscode)) {
                obj.setTransferorconscode(tranfconscode);
                //转让人投顾所在部门
                obj.setTransferororg(consOrgCache.getCons2OutletMap().get(tranfconscode));
                //转让人投顾所在区域
                String tranuporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(tranfconscode));
                obj.setTransferorarea(tranuporgcode);
            }
        }

        obj.setAssignee(assigncustno);
        //查询受让人的信息
        paramconstant.clear();
        paramconstant.put("custno", assigncustno);
        CmCustconstant assignconstant = cmCustconstantService.getCmCustconstant(paramconstant);
        if (assignconstant != null) {
            String assigncode = assignconstant.getConscode();
            if (StringUtil.isNotNullStr(assigncode)) {
                obj.setAssigneeconscode(assigncode);
                //受让人投顾所在部门
                obj.setAssigneeorg(consOrgCache.getCons2OutletMap().get(assigncode));
                //受让人投顾所在区域
                String assignuporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(assigncode));
                obj.setAssigneearea(assignuporgcode);
            }
        }
        obj.setFundcode(fundcode);
        obj.setTransfervol(new BigDecimal(vol));
        if ("yes".equals(fcclflag)) {
            obj.setFcclflag("1");
            obj.setFcclTransferRemainPayAmt(new BigDecimal(fcclTransferRemainPayAmt));
            obj.setFccloldtotalamt(new BigDecimal(totalamt));
            obj.setFccloldamt(new BigDecimal(leftamt));
            obj.setTransfertotalamt(new BigDecimal(transfertotalamt));
            obj.setFccltransfervolamt(new BigDecimal(fccltransfervolamt));
        }
        obj.setRecommfee(new BigDecimal(recommfee));
        obj.setTradedt(tradedt);
        obj.setCreator(userlogin.getUserId());
        obj.setCheckstat(StaticVar.TRANSFERSTAT_NOCOMFIRM);
        obj.setTransferAmount(StringUtil.isNullStr(transferAmount) ? null
                : new BigDecimal(transferAmount).setScale(2, RoundingMode.DOWN));
        cmCustTransfervolService.insertCmCustTransfervol(obj);
        return ReturnMessageDto.ok();
    }

    /**
     * 跳转到comfirmTransfer页面方法
     *
     * @return String
     */
    @RequestMapping("/comfirmTransfer.do")
    public String comfirmTransfer(HttpServletRequest request, Map<String, Object> map) {
        String id = request.getParameter("id");
        Map<String, Object> param1 = new HashMap<>(1);
        param1.put("id", Long.parseLong(id));
        CmCustTransfervol obj = cmCustTransfervolService.getCmCustTransfervol(param1);
        if (Objects.isNull(obj)) {
            return "/prosale/confirmTransfer";
        }
        boolean dxflag = queryPreBookService.isDxflag(obj.getTransferor(), obj.getFundcode());
        if (dxflag) {
            map.put("dxflag", "1");
        } else {
            map.put("dxflag", "0");
            GetInfoByParamRequest req = new GetInfoByParamRequest();
            req.setConscustno(obj.getTransferor());
            req.setFundcode(obj.getFundcode());
            //持有份额
            BigDecimal balanceVol = BigDecimal.ZERO;
            GetPreBalanceVolResponse balanceVolResponse = queryPreBookService.getPreBalanceVol(req);
            if (balanceVolResponse.isSuccessful()) {
                balanceVol = balanceVolResponse.getVol();
            }
            map.put("balanceVol", balanceVol);
        }
        //产品名称
        JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(obj.getFundcode(), false);
        if (jjxx1 != null) {
            obj.setFundname(jjxx1.getJjjc());
        }
        //转让人信息
        QueryConscustInfoRequest trConscustInfoRequest = new QueryConscustInfoRequest();
        trConscustInfoRequest.setConscustno(obj.getTransferor());
        QueryConscustInfoResponse trConscustInfoResponse = queryConscustInfoService.queryConscustInfo(trConscustInfoRequest);
        obj.setTransferorname(trConscustInfoResponse.getConscustinfo().getCustname());
        QueryCustconstantInfoRequest trCustconstantInfoRequest = new QueryCustconstantInfoRequest();
        trCustconstantInfoRequest.setCustNo(obj.getTransferor());
        QueryCustconstantInfoResponse trCustconstantInfoResponse = queryCustconstantInfoService.queryCustconstantInfo(trCustconstantInfoRequest);
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        if (trCustconstantInfoResponse.isSuccessful()) {
            String trconscode = Util.ObjectToString(trCustconstantInfoResponse.getCustconstantInfo().getConscode());
            obj.setTransferorcons(consOrgCache.getAllConsMap().get(trconscode));
            obj.setTransferororg(consOrgCache.getOrgMap().get(consOrgCache.getCons2OutletMap().get(trconscode)));
        }
        //受让人信息
        QueryConscustInfoRequest assConscustInfoRequest = new QueryConscustInfoRequest();
        assConscustInfoRequest.setConscustno(obj.getAssignee());
        QueryConscustInfoResponse assConscustInfoResponse = queryConscustInfoService.queryConscustInfo(assConscustInfoRequest);
        obj.setAssigneename(assConscustInfoResponse.getConscustinfo().getCustname());
        QueryCustconstantInfoRequest assCustconstantInfoRequest = new QueryCustconstantInfoRequest();
        assCustconstantInfoRequest.setCustNo(obj.getAssignee());
        QueryCustconstantInfoResponse assCustconstantInfoResponse = queryCustconstantInfoService.queryCustconstantInfo(assCustconstantInfoRequest);
        if (assCustconstantInfoResponse.isSuccessful()) {
            String assConscode = Util.ObjectToString(assCustconstantInfoResponse.getCustconstantInfo().getConscode());
            obj.setAssigneecons(consOrgCache.getAllConsMap().get(assConscode));
            obj.setAssigneeorg(consOrgCache.getOrgMap().get(consOrgCache.getCons2OutletMap().get(assConscode)));
        }

        // 查询该产品是否为股权产品
        boolean hasGqlabel = productManagerService.hasGqlabel(obj.getFundcode());
        map.put("gqFlag", hasGqlabel ? StaticVar.YES : StaticVar.NO);
        if (Boolean.TRUE.equals(hasGqlabel)) {
            // 股权产品，查询该产品[已分配金额]
            BigDecimal accumCollection = getAllocatedAmt(obj.getTransferor(), obj.getFundcode());
            map.put("allocatedAmt", accumCollection);
        }

        map.put("fcclflag", obj.getFcclflag());
        map.put("obj", obj);

        return "/prosale/confirmTransfer";
    }

    /**
     * 处理交易录入审核页面相关的处理
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/comfirmTransferDeal.do")
    public ReturnMessageDto<String> comfirmTransferDeal(HttpServletRequest request) {
        User userlogin = getLoginUser();
        String userId=getLoginUserId();
        String id = request.getParameter("id");
        String nav = request.getParameter("nav");

        String vol = request.getParameter("vol");
        String tradedt = request.getParameter("tradedt");
        String fcclflag = request.getParameter("fcclflag");
        String transfertotalamt = request.getParameter("transfertotalamt");
        String fccltransfervolamt = request.getParameter("fccltransfervolamt");
        String fccloldtotalamt = request.getParameter("fccloldtotalamt");
        String fccloldamt = request.getParameter("fccloldamt");
        String transfervol = request.getParameter("transfervol");
        String recommfee = request.getParameter("recommfee");
        // 转让金额
        String transferAmount = request.getParameter("transferAmount");
        // 受让剩余缴款金额
        String fcclTransferRemainPayAmt = request.getParameter("fcclTransferRemainPayAmt");
        // 已分配金额
        String allocatedAmt = request.getParameter("allocatedAmt");

        Map<String, Object> param1 = new HashMap<>(1);
        param1.put("id", Long.valueOf(id));
        CmCustTransfervol obj = cmCustTransfervolService.getCmCustTransfervol(param1);
        //校验受让人是否有实名一账通，如没有，则提示：“受让人未开通实名一账通，确认失败。请先开通一账通”；
        QueryConscustInfoRequest asConscustInfoRequest = new QueryConscustInfoRequest();
        asConscustInfoRequest.setConscustno(obj.getAssignee());
        QueryConscustInfoResponse conscustInfoResponse = queryConscustInfoService.queryConscustInfo(asConscustInfoRequest);
        if (conscustInfoResponse.isSuccessful() && conscustInfoResponse.getConscustinfo() != null && StringUtils.isNotBlank(conscustInfoResponse.getConscustinfo().getHboneno())) {
            QueryAccHboneInfoRequest queryHboneInfoRequest = new QueryAccHboneInfoRequest();
            queryHboneInfoRequest.setHboneNo(conscustInfoResponse.getConscustinfo().getHboneno());
            QueryAccHboneInfoResponse hboneInfoResponse = queryAccHboneInfoFacade.execute(queryHboneInfoRequest);
            if (StringUtils.isBlank(hboneInfoResponse.getIdNoDigest())) {
                return ReturnMessageDto.fail("受让人未开通实名一账通，确认失败。请先开通一账通！");
            }
        } else {
            return ReturnMessageDto.fail("受让人未开通实名一账通，确认失败。请先开通一账通！");
        }
        String hbzl = "";
        JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(obj.getFundcode(), false);
        if (jjxx1 != null) {
            hbzl = jjxx1.getHbzl();
        }
        //处理确认逻辑
        obj.setCheckstat(StaticVar.TRANSFERSTAT_HASCOMFIRM);
        obj.setCheckor(userId);
        obj.setCheckdt(DateTimeUtil.getCurDate());
        obj.setUpddt(new Date());
        obj.setTradedt(tradedt);
        obj.setRecommfee(new BigDecimal(recommfee));
        if (StringUtil.isNotNullStr(allocatedAmt)) {
            // [已分配金额]不为空，则更新
            obj.setAllocatedAmt(new BigDecimal(allocatedAmt));
        }
        //如果是分次CALL产品
        if (StaticVar.FCCL_YES.equals(fcclflag)) {
            obj.setTransfertotalamt(new BigDecimal(transfertotalamt));
            obj.setFccltransfervolamt(new BigDecimal(fccltransfervolamt));
            obj.setFccloldtotalamt(new BigDecimal(fccloldtotalamt));
            obj.setFccloldamt(new BigDecimal(fccloldamt));
            obj.setTransfervol(new BigDecimal(transfervol));
            obj.setFcclTransferRemainPayAmt(new BigDecimal(fcclTransferRemainPayAmt));
            obj.setTransferAmount(new BigDecimal(transferAmount));
            BigDecimal totalamt = obj.getTransfertotalamt();
            //存在转让份额和受让份额对应实缴金额的情况,需新生成受让人认缴和首次实缴预约（补历史预约）,更新转让人的认缴金额
            if (obj.getTransfervol() != null
                    && obj.getTransfervol().compareTo(BigDecimal.ZERO) > 0
                    && obj.getFccltransfervolamt() != null
                    && obj.getFccltransfervolamt().compareTo(BigDecimal.ZERO) > 0) {
                //新增预约
                CmPreBookInsertVo insertVo = new CmPreBookInsertVo();
                insertVo.setOperator(userId);
                insertVo.setTradeType(PreBookTradeTypeEnum.BUY.getCode());
                insertVo.setPcode(obj.getFundcode());
                insertVo.setCurrency(hbzl);
                insertVo.setPretype(PreTypeEnum.PAPER.getCode());
                insertVo.setBuyamt(obj.getFccltransfervolamt());
                insertVo.setExpectpayamtdt(obj.getTradedt());
                insertVo.setExpecttradedt(obj.getTradedt());
                insertVo.setRealbuyman(conscustInfoResponse.getConscustinfo().getCustname());
                insertVo.setConscustno(obj.getAssignee());
                insertVo.setConscustname(conscustInfoResponse.getConscustinfo().getCustname());
                insertVo.setRestype(conscustInfoResponse.getConscustinfo().getRestype());
                insertVo.setRealcreator(userlogin.getUserId());
                // 转让价格 = 转让金额 （两者是同一概念）
                insertVo.setTransferprice(obj.getTransferAmount());

                //标记为 分次call
                insertVo.setFcclFlag(YesOrNoEnum.YES.getCode());
                insertVo.setSubscribeAmt(totalamt);

                // 份额转让 无需校验生成双录
                insertVo.setCheckDoubleTrade(YesOrNoEnum.NO.getCode());

                ReturnMessageDto<String> insertResp= prebookBusinessService.insertPreBook(insertVo);
                if(!insertResp.isSuccess()){
                    return ReturnMessageDto.fail(String.format("新增分次CALL异常失败！失败信息：%s",insertResp.getReturnMsg()));
                }
                String preId=insertResp.getReturnObject();
                obj.setFcclpreid(new BigDecimal(preId));
                obj.setFccltype(StaticVar.TRANSFER_FCCLTYPE_REPAIR);
                //更新 预约信息   改接口暂时保留。 待交易确认服务端td重构完成，在处理
                UpdatePrebookInfoRequest updateReq=new UpdatePrebookInfoRequest();
                CmPrebookproductinfo updateInfo=new CmPrebookproductinfo();
                //待更新预约信息
                updateInfo.setId(new BigDecimal(preId));
                //更新变动信息
                updateInfo.setModifier(userId);
                String cusrDt=DateUtil.getDateFormat(new Date(), "yyyyMMdd");
                updateInfo.setModdt(cusrDt)  ;
                //已到账确认
                updateInfo.setPaystate(StaticVar.PAY_STATES_HAS_CONFIRM);

                updateInfo.setPrebookstate(StaticVar.PREBOOK_STATES_HAS_CONFIRM);
                updateInfo.setPrebookcheckdt(cusrDt);
                //已交易确认
                updateInfo.setTradestate(StaticVar.TRADE_STATE_YES);
                updateInfo.setTradeverifydt(cusrDt);
                updateInfo.setRealpayamt(obj.getFccltransfervolamt());
                updateInfo.setRealpayamtdt(obj.getTradedt());
                updateReq.setCmPrebookproductinfo(updateInfo);
                BaseResponse updateResp=preBookService.updatePrebook(updateReq);
                if(!updateResp.isSuccessful()){
                    return ReturnMessageDto.fail(String.format("更新分次CALL新增预约失败，预约id:%s ！",preId));
                }
            }
            //查询原分次CALL，修改原分次CALL的认缴金额
            Map<String, Object> paramcall = new HashMap<String, Object>(2);
            paramcall.put("conscustno", obj.getTransferor());
            paramcall.put("pcode", obj.getFundcode());
            List<Prebookmanycallinfo> listinfo = manyCallPreInfoService.listmanycallhasbuyByCustnoAndPcode(paramcall);
            if (listinfo != null && listinfo.size() > 0) {
                Prebookmanycallinfo callinfo = listinfo.get(0);
                String oldtotalamt = callinfo.getTotalamt().toPlainString();
                callinfo.setTotalamt(callinfo.getTotalamt().subtract(totalamt));
                callinfo.setUpddt(new Date());
                manyCallPreInfoService.updatePrebookmanycallinfo(callinfo);
                //转让人份额变化通知中台
                noticeAmtToZtService.sendMq(obj.getTransferor(), obj.getFundcode(), callinfo.getTotalamt(), DateTimeUtil.getCurDateTime());
                //日志
                User user = (User) request.getSession().getAttribute("loginUser");
                cmOptLogService.insertLog(user.getUserId(), StaticVar.OPT_MANYCALL_TRANSF_TOTALAMT, "份额转让将原认缴金额:" + oldtotalamt + "改成了:" + callinfo.getTotalamt().toPlainString(), callinfo.getId().toPlainString());
            }
            //更新份额转让的状态
            cmCustTransfervolService.updateCmCustTransfervol(obj);
        } else {
            //非分次CALL产品
            //校验转让人的转让份额是否小于等于当前持有份额，如转让份额大于当前持有份额，则提示：“转让份额大于转让人当前持有份额，确认失败”；
            obj.setTransfervol(new BigDecimal(vol));
            obj.setTransferAmount(new BigDecimal(transferAmount));
            String transfer = obj.getTransferor();
            boolean dxflag = queryPreBookService.isDxflag(transfer, obj.getFundcode());
            if (!dxflag) {
                GetInfoByParamRequest req = new GetInfoByParamRequest();
                req.setConscustno(transfer);
                req.setFundcode(obj.getFundcode());
                //持有份额
                BigDecimal balanceVol = BigDecimal.ZERO;
                GetPreBalanceVolResponse balanceVolResponse = queryPreBookService.getPreBalanceVol(req);
                if (balanceVolResponse.isSuccessful()) {
                    balanceVol = balanceVolResponse.getVol();
                }
                if (balanceVol.compareTo(new BigDecimal(vol)) < 0) {
                    return ReturnMessageDto.fail("转让份额大于转让人当前持有份额，确认失败");
                }
            }
            cmCustTransfervolService.comfirmCmCustTransfervol(!dxflag, obj, Util.StringToDouble(nav));
        }
        return ReturnMessageDto.ok();
    }

    @ResponseBody
    @RequestMapping("/addFcclPay.do")
    public ReturnMessageDto<String> addFcclPay(HttpServletRequest request){
        String fcclpayamt = request.getParameter("fcclpayamt");
        String fcclpaydt = request.getParameter("fcclpaydt");
        String id = request.getParameter("id");
        Map<String, Object> param = new HashMap<String, Object>(1);

        if (StringUtil.isNullStr(fcclpayamt) || StringUtil.isNullStr(id) || StringUtil.isNullStr(fcclpaydt)) {
            return  ReturnMessageDto.fail("参数错误，新增失败！");
        }
        param.put("id", id);
        CmCustTransfervol obj = cmCustTransfervolService.getCmCustTransfervol(param);
        BigDecimal bfcclpayamt = new BigDecimal(fcclpayamt).multiply(new BigDecimal("10000"));
        if (bfcclpayamt.compareTo(obj.getTransferamt()) > 0) {
            return ReturnMessageDto.fail("受让人本次实缴金额超过需要缴纳的金额，新增失败！");
        }
        String hbzl = "";
        JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(obj.getFundcode(), false);
        if (jjxx1 != null) {
            hbzl = jjxx1.getHbzl();
        }
        String userId=getLoginUserId();
        QueryConscustInfoRequest asConscustInfoRequest = new QueryConscustInfoRequest();
        asConscustInfoRequest.setConscustno(obj.getAssignee());
        QueryConscustInfoResponse conscustInfoResponse = queryConscustInfoService.queryConscustInfo(asConscustInfoRequest);
        CmPreBookInsertVo insertVo = new CmPreBookInsertVo();
        insertVo.setTradeType(StaticVar.PREBOOK_TRADE_TYPE_BUY);
        insertVo.setPcode(obj.getFundcode());
        insertVo.setCurrency(hbzl);
        insertVo.setPretype(StaticVar.PAPER_ORDER);
        insertVo.setBuyamt(bfcclpayamt);
        insertVo.setExpectpayamtdt(fcclpaydt);
        insertVo.setExpecttradedt(obj.getTradedt());
        insertVo.setRealbuyman(conscustInfoResponse.getConscustinfo().getCustname());
        insertVo.setConscustno(obj.getAssignee());
        insertVo.setOperator(userId);
        //历史逻辑： 默认 预约未确认  此处 直接 预约已确认
//            insertVo.setPrebookstate(StaticVar.PREBOOK_STATES_NOT_CONFIRM);
        //实缴分次CALL的认缴金额
        BigDecimal totalamt = obj.getTransferamt();
        //标记为 分次call
        insertVo.setFcclFlag(YesOrNoEnum.YES.getCode());
        insertVo.setSubscribeAmt(totalamt);
        ReturnMessageDto<String>  insertResp=prebookBusinessService.insertPreBook(insertVo);
        if (insertResp.isSuccess()) {
            //更新份额转让的状态
            obj.setFcclpreid(insertVo.getId());
            obj.setFccltype(StaticVar.TRANSFER_FCCLTYPE_ADDNEW);
            obj.setUpddt(new Date());
            cmCustTransfervolService.updateCmCustTransfervol(obj);
        }else{
            return  ReturnMessageDto.fail(String.format("新增分次CALL实缴预约失败！失败信息：%s",insertResp.getReturnMsg()));
        }
        return ReturnMessageDto.ok();
    }

    @ResponseBody
    @RequestMapping("/updateTransferCreator.do")
    public String updateTransferCreator(HttpServletRequest request) throws Exception {
        String result = "";
        String conscode = request.getParameter("conscode");
        String id = request.getParameter("id");
        if (StringUtil.isNotNullStr(conscode) && StringUtil.isNotNullStr(id)) {
            Map<String, Object> param = new HashMap<String, Object>(1);
            param.put("id", id);
            CmCustTransfervol vol = cmCustTransfervolService.getCmCustTransfervol(param);
            vol.setCreator(conscode);
            cmCustTransfervolService.updateCmCustTransfervol(vol);
        } else {
            result = "paramError";
            return result;
        }
        result = "success";
        return result;
    }

    @ResponseBody
    @RequestMapping("/cancelTransferVol.do")
    public String cancelTransferVol(HttpServletRequest request) throws Exception {
        String result = "";
        String id = request.getParameter("id");
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        if (StringUtil.isNotNullStr(id)) {
            CmCustTransfervol vol = new CmCustTransfervol();
            vol.setId(Long.parseLong(id));
            vol.setCheckstat(StaticVar.TRANSFERSTAT_CANCEL);
            vol.setCheckor(userlogin.getUserId());
            vol.setCheckdt(DateTimeUtil.getCurDate());
            vol.setUpddt(new Date());
            cmCustTransfervolService.updateCmCustTransfervol(vol);
        } else {
            result = "paramError";
            return result;
        }
        result = "success";
        return result;
    }

    private Double getNav(String hmcpx, String fundCode, String tradeDt) {
        Double amount = null;
        List<SmjzDto> smjzDtos;
        if (StaticVar.HMCPX_GD.equals(hmcpx) || StaticVar.HMCPX_PEVC.equals(hmcpx)) {
            log.info("--------------查询净值接口参数：jjdm:" + fundCode + ", startDate:,endDate:" + tradeDt);
            smjzDtos = smjzService.selectSmAllLsjzListForCrm(fundCode, null, tradeDt);
        } else {
            log.info("--------------查询净值接口参数：jjdm:" + fundCode + ", startDate:" + tradeDt + ",endDate:" + tradeDt);
            smjzDtos = smjzService.selectSmAllLsjzListForCrm(fundCode, tradeDt, tradeDt);
        }
        log.info("--------------查询净值接口返回：" + smjzDtos == null ? null : JSON.toJSONString(smjzDtos));
        if (CollectionUtils.isNotEmpty(smjzDtos)) {
            SmjzDto smjzDto = smjzDtos.get(0);
            amount = smjzDto.getJjjz();
        }
        return amount;
    }

    /**
     * 获取持有信息:
     *
     * @param request
     * <AUTHOR>
     * @date 2020/5/26
     */
    @RequestMapping("getCustHoldInfoByPcode.do")
    @ResponseBody
    public Map<String, Object> getCustHoldInfoByPcode(HttpServletRequest request) {
        Map<String, Object> map = new HashMap<String, Object>(4);
        String custNo = request.getParameter("custNo");
        String pcode = request.getParameter("pcode");
        //持有份额和市值
        BigDecimal balanceVol = BigDecimal.ZERO;
        GetInfoByParamRequest getInfoByParamRequest = new GetInfoByParamRequest();
        getInfoByParamRequest.setConscustno(custNo);
        getInfoByParamRequest.setFundcode(pcode);
        GetPreBalanceVolResponse balanceVolResponse = queryPreBookService.getPreBalanceVol(getInfoByParamRequest);
        if (balanceVolResponse.isSuccessful()) {
            balanceVol = balanceVolResponse.getVol();
            map.put("balanceVol", balanceVol.divide(new BigDecimal(1), 2, BigDecimal.ROUND_HALF_UP));
        }
        //查询是否是分次CALL产品
        JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(pcode, false);
        if (jjxx1 != null && StaticVar.FCCL_YES.equals(jjxx1.getFccl())) {
            map.put("fccl", "yes");
            Map<String, Object> param = new HashMap<String, Object>(2);
            param.put("conscustno", custNo);
            param.put("pcode", pcode);
            List<Prebookmanycallinfo> listinfo = manyCallPreInfoService.listmanycallhasbuyByCustnoAndPcode(param);
            if (listinfo != null && listinfo.size() > 0) {
                Prebookmanycallinfo callinfo = listinfo.get(0);
                map.put("totalamt", callinfo.getTotalamt().divide(new BigDecimal(1), 2, BigDecimal.ROUND_HALF_UP));
                map.put("leftamt", prebookproductinfoService.findLeftamt(callinfo.getTotalamt(), balanceVol, custNo, pcode));
            }
        } else {
            map.put("fccl", "no");
        }
        return map;
    }


    /**
     * @description: 获取 已分配金额
     * @param conscustno
     * @param pcode
     * @return java.math.BigDecimal
     * @author: jin.wang03
     * @date: 2025/3/6 15:14
     * @since JDK 1.8
     */
    private BigDecimal getAllocatedAmt(String conscustno, String pcode) {
        QueryConscustInfoRequest queryCustRequest = new QueryConscustInfoRequest();
        queryCustRequest.setConscustno(conscustno);
        QueryConscustInfoResponse response = queryConscustInfoService.queryConscustInfo(queryCustRequest);
        if (Objects.isNull(response) || Objects.isNull(response.getConscustinfo())
                || StringUtils.isBlank(response.getConscustinfo().getHboneno())) {
            log.error("获取[已分配金额]时，查询 客户信息失败! 投顾客户号：{}", conscustno);
            return null;
        }

        ConscustInfoDomain custInfo = response.getConscustinfo();

        QueryAcctBalanceRequest queryAcctBalanceRequest = new QueryAcctBalanceRequest();
        queryAcctBalanceRequest.setHbOneNo(custInfo.getHboneno());
        queryAcctBalanceRequest.setProductCode(pcode);
        // 持仓类型：2-全部
        queryAcctBalanceRequest.setBalanceStatus(ALL_BALANCES);

        queryAcctBalanceRequest.setOutletCode(OUTLET_CODE);
        queryAcctBalanceRequest.setOperIp(getIpAddr());
        queryAcctBalanceRequest.setTxChannel("1");
        queryAcctBalanceRequest.setDataTrack(DATA_TACK);

        List<String> fullDisCodeList= DisCodeUtil
                .getFullBusiDisCodeList(CrmCustInvestTypeEnum.getEnum(custInfo.getInvsttype()))
                .stream().map(DisCodeEnum::getCode).collect(Collectors.toList());
        queryAcctBalanceRequest.setDisCodeList(fullDisCodeList);

        QueryAcctBalanceResponse balanceResponse = queryAcctBalanceNewFacade.execute(queryAcctBalanceRequest);
        log.info("QueryAcctBalanceFacade.execute返回" + JSON.toJSON(balanceResponse));

        return Objects.isNull(balanceResponse) ? null : balanceResponse.getTotalCashCollection();
    }


    /**
     * 获取客户的IP
     *
     * @return
     */
    private String getIpAddr() {
        HttpServletRequest request = SessionUserManager.getRequest();
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

}
