package com.howbuy.crm.hb.enums;

/**
 * <AUTHOR>
 * @time 2021-7-19 11:07:03
 */
public enum HmcpxEnum {

    /**
     * 固定收益
     */
    GDSY,
    /**
     * 私募股权
     */
    SMGQ,
    /**
     * 阳关私募
     */
    YGSM;


    /**
     * jjxx.hmcpx
     * =2 :固定收益
     * =5 :私募股权
     * 其他 ; 阳关私募
     * @param productSubType 这个中台用的就是 hmcpx 字段
     * @return
     */
    public static HmcpxEnum getHmcpxEnum(String productSubType) {
//        return Stream.of(HmcpxEnum.values()).filter(tmp -> tmp.name().equals(hmcpx)).findFirst().orElse(null);
        if ("2".equals(productSubType)) {
           return GDSY;
        } else if ("5".equals(productSubType)) {
            return SMGQ;
        } else {
            return YGSM;
        }
    }


}
