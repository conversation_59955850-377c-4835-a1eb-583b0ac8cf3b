package com.howbuy.crm.hb.web.controller.conference;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;

import com.howbuy.crm.hb.domain.conference.CmConference;
import com.howbuy.crm.hb.domain.conference.CmConferenceConscust;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.system.HbOrganization;
import com.howbuy.crm.hb.service.callout.CsCommunicateVisitService;
import com.howbuy.crm.hb.service.conference.CmConferenceConscustService;
import com.howbuy.crm.hb.service.conference.CmConferenceService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.system.HbOrganizationService;
import com.howbuy.crm.hb.web.controller.cache.CacheCode;
import com.howbuy.crm.hb.web.controller.cache.CacheUtil;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.domain.User;

import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

@Slf4j
@Controller
@RequestMapping(value = "/joinconference")
public class CmConferenceJoinController {

	@Autowired
	private ConscustService custService;

	@Autowired
	private CmConferenceService cmConferenceService;

	@Autowired
	private CmConferenceConscustService cmConferenceConscustService;

	@Autowired
	private HbOrganizationService hbOrganizationService;

	@Autowired
	private CsCommunicateVisitService csCommunicateVisitService;
	
	private static Logger LOG = LoggerFactory.getLogger(CmConferenceJoinController.class);

	/**
	 * @param request
	 * @param params [ status=0-只查CM_CONFERENCE表。 status=1-关联CM_CONFERENCE_CONSCUST表]
	 * @return
	 * @throws Exception
	 *             作用：根据输入的会议名称，模糊查询会议 返回的 数据中应带有 开始时间 会议地址
	 */
	@ResponseBody
	@RequestMapping("/comboxconferOrg.do")
	public Map<String, Object> queryConferenceOrg(HttpServletRequest request, @RequestParam Map<String, String> params)
			throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		Map<String, String> paramMap = new HashMap<String, String>();
		paramMap.put("status", params.get("status"));

		if (StringUtils.isNotBlank(params.get("conferencename"))) {
			paramMap.put("conferencename", params.get("conferencename"));
		}
		if (StringUtils.isNotBlank(params.get("conscode"))) {
			paramMap.put("conscode", params.get("conscode"));
		}
		if (StringUtils.isNotBlank(params.get("conscustno"))) {
			paramMap.put("conscustno", params.get("conscustno"));
		}
		if (StringUtils.isNotBlank(params.get("conferencetype"))) {
			paramMap.put("conferencetype", params.get("conferencetype"));
		}
		//conferenceId 2022年6月24日 增加 会议Id查询
		if (StringUtils.isNotBlank(params.get("conferenceId"))) {
			paramMap.put("conferenceId", params.get("conferenceId"));
		}


		List<Map<String, Object>> listMap;
		try {
			listMap = cmConferenceService.getCmConferenceList(params);
			resultMap.put("rowsData", listMap);
			resultMap.put("resultcode", 1);
		} catch (Exception e) {
			LOG.error("查询会议信息出错", e);
			resultMap.put("resultcode", 0);
		}

		return resultMap;
	}

	/**
	 * @param request
	 * @param params
	 * @return
	 * @throws Exception
	 *             作用：根据当前填入的数据判断时进行更新还是增加
	 */
	@ResponseBody
	@RequestMapping("/joinconference.do")
	public Map<String, Object> saveConferenceJoin(HttpServletRequest request, @RequestParam Map<String, String> params)
			throws Exception {
		User user = (User) request.getSession().getAttribute("loginUser");
		Map<String, Object> resultMap = new HashMap<String, Object>();
		String status = params.get("status");
		String conferenceId = params.get("conferenceId");
		String conscustno = params.get("conscustno");
		int conferencesub = Integer.parseInt(params.get("conferencesub").toString());
		Map<String, String> paramMap = new HashMap<String, String>();


		Conscust cust= custService.getConscust(conscustno);

		String orgcode = ConsOrgCache.getInstance().getCons2OutletMap().get(cust.getConscode());
		paramMap.put("orgcode", orgcode);
		paramMap.put("conferenceId", conferenceId);
		if ("add".equals(status)) {

				Map<String, Object> allMap = cmConferenceService.getCmConferenceOrgNum(paramMap);
				//获取截止时间
				long cutoffdt = Long.parseLong(allMap.get("CUTOFFDT") == null ? "0" : allMap.get("CUTOFFDT").toString());
				if (cutoffdt != 0) {

					long now = Long.parseLong(DateUtil.getDateFormat(new Date(),"yyyyMMddHHmmss"));

					if (cutoffdt < now) {
						resultMap.put("resultcode", 0);
						resultMap.put("message", "当前时间超过截止操作时间，不允许报名！");
						return resultMap;
					}
				}
			if (CacheUtil.lock(CacheCode.CMCONFERENCE, conferenceId)) {
				/*Map<String, Object> joinMap = cmConferenceService.getCmConferenceJoinOrgNum(paramMap);//此会议部门已预约人数
				Map<String, Object> infoMap = cmConferenceService.getCmConferenceJoinAllNum(paramMap);//会议已预约人数

				int allNum = Integer.valueOf(allMap.get("MAXNUMBER") == null ? "0" : allMap.get("MAXNUMBER").toString());
				int orgjoinNum = Integer.valueOf(joinMap == null ? "0" : joinMap.get("ORGNUMB").toString());
				int alljoinNum = Integer.valueOf(infoMap == null ? "0" : infoMap.get("ALLNUMB").toString());


				List<HbOrganization> listout = hbOrganizationService.listHbOrganizationOutByCode(paramMap);

				if(listout != null && listout.size() > 0) {

					String uporgcode = "";
					Integer uporgnum = -1;
					//部门往上找，看看有没有人数限制
					for(HbOrganization organization:listout) {

						paramMap.put("orgcode", organization.getOrgcode());
						Map<String, Object> orgMap = cmConferenceService.getCmConferenceOrgNum(paramMap);
						if(orgMap.get("ORGCODENUB") != null){
							uporgcode = organization.getOrgcode();
							uporgnum = Integer.valueOf(orgMap.get("ORGCODENUB").toString());
							break;
						}
					}

					//如果有人数限制，再往下找，看看有没有超过报名人数
					if(StringUtil.isNotNullStr(uporgcode)){
						paramMap.put("orgcode", uporgcode);
						Map<String, Object> otherMap = cmConferenceService.getCmConferenceJoinOtherNum(paramMap);

						if(otherMap != null){
							int otherNum = Integer.valueOf(otherMap.get("OTHERNUMB") == null ? "0" : otherMap.get("OTHERNUMB").toString());

							if(otherNum + conferencesub> uporgnum){
								resultMap.put("resultcode", 0);
								resultMap.put("message", "报名人数超机构人数上限！");
								return resultMap;
							}
						}

					}

					if(uporgnum == 0){
						resultMap.put("resultcode", 0);
						resultMap.put("message", "报名人数超机构人数上限！");
						return resultMap;
					}

					if (uporgnum > 0) {
						if ((conferencesub + orgjoinNum) > uporgnum) {
							resultMap.put("resultcode", 0);
							resultMap.put("message", "报名人数超机构人数上限！");
							return resultMap;
						}
					}

				}

				if ((conferencesub + alljoinNum) > allNum) {
					resultMap.put("resultcode", 0);
					resultMap.put("message", "报名人数超过总人数上限！");
					return resultMap;
				}*/
				try {

				String result = cmConferenceConscustService.checkConferenceConscust(conferenceId,orgcode,conferencesub);
				if(!"success".equals(result)) {
					resultMap.put("resultcode", 0);
					resultMap.put("message", "报名人数已满额！");
					return resultMap;
				}
				CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
				cmConferenceConscust.setConferenceid(conferenceId);
				cmConferenceConscust.setConscode(params.get("conscode"));
				cmConferenceConscust.setConscustno(params.get("conscustno"));
				cmConferenceConscust.setCreatdt(DateTime.now().toString("yyyyMMdd"));
				cmConferenceConscust.setOrgcode(orgcode);
				cmConferenceConscust.setCreater(params.get("conscode"));
				cmConferenceConscust.setAppointmentsnub(conferencesub);
				// 查询客户状态
				String gdcjlabel = cmConferenceConscustService.queryGdcjlabelByConsCustNo(conscustno);
				if (StringUtils.isBlank(gdcjlabel)) {
					gdcjlabel = "0";
				}
				cmConferenceConscust.setGdcjlabel(gdcjlabel);
				cmConferenceConscustService.insertCmConferenceConscust(cmConferenceConscust);

				CmConference cmConference = cmConferenceService.queryCmConferenceInfo(conferenceId);

				String commContent = "预约参会："+cmConference.getConferencename()+"，预约参会人数："+conferencesub;
				// 添加投顾预约及沟通拜访记录
				log.info("CmConferenceJoinController.saveConferenceJoin:" + conscustno);
				csCommunicateVisitService.insertCsCommunicateVisitByConfere(conscustno,user.getUserId(),commContent);

				resultMap.put("resultcode", 1);
				resultMap.put("message", "报名成功！");

				}catch (Exception e){
					LOG.error(e.getMessage(),e);
				}finally {
					CacheUtil.unlock(CacheCode.CMCONFERENCE, conferenceId);
				}
			} else {
				resultMap.put("resultcode", 0);
				resultMap.put("message", "系统正忙请稍后重试！");
			}
		} else if ("del".equals(status)) {
			Map<String, String> conscustparamMap = new HashMap<String, String>();
			conscustparamMap.put("conferenceid", conferenceId);
			conscustparamMap.put("conscustno", params.get("conscustno"));
			cmConferenceConscustService.deleteConferenceConscust(conscustparamMap);
			resultMap.put("resultcode", 1);
			resultMap.put("message", "取消报名成功！");
		} else {
			resultMap.put("resultcode", 0);
			resultMap.put("message", "参数异常请重试！");
		}

		return resultMap;
	}
}
