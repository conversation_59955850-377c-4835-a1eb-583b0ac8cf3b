/**   
* @Title: ExcelImpRsType.java 
* @Package com.hb.crm.web.util.excel.reader 
* @Description: TODO(用一句话描述该文件做什么) 
* <AUTHOR>
* @date 2016年5月3日 下午4:43:11 
* @version V1.0   
*/ 
 package com.howbuy.crm.hb.tools.excel.reader;
 /** 
 * @ClassName: ExcelImpRsType 
 * @Description: 返回数据导入的结果
 * <AUTHOR> 
 * @date 2016年5月3日 下午4:43:11 
 *  
 */

public class ExcelImpRsType {
	/** 
	* @Fields SUCCESS :文件导入成功
	*/ 
	public final static String SUCCESS="success";
	
	/** 
	* @Fields CHECK_INIT : 数据格式不正确
	*/ 
	public final static String CHECK_INIT="check_init";
	
	/** 
	* @Fields CHECK_DATA : 数据数据库校验不通过 
	*/ 
	public final static String CHECK_DATA="check_data";
	
	/** 
	* @Fields CHECK_DATA : 未知数据错误 
	*/ 
	public final static String DATA_ERROR="data_error";

}

 