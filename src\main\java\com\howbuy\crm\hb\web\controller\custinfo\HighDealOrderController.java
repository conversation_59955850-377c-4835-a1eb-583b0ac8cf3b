package com.howbuy.crm.hb.web.controller.custinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.crm.base.util.IdTypeUtil;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.dealorder.request.DealOrderRequest;
import com.howbuy.crm.dealorder.response.CrmDealOrderBeanVo;
import com.howbuy.crm.dealorder.service.CrmHighDealOrderService;
import com.howbuy.crm.hb.constants.CommonConstant;
import com.howbuy.crm.hb.constants.DfileConstants;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.custinfo.CrmBalanceBean;
import com.howbuy.crm.hb.domain.custinfo.CrmDealOrderBean;
import com.howbuy.crm.hb.domain.custinfo.SubCrmBalanceBean;
import com.howbuy.crm.hb.domain.insur.BxBalanceDetail;
import com.howbuy.crm.hb.domain.prosale.*;
import com.howbuy.crm.hb.enums.HmcpxEnum;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.insur.CmBxPrebookBuyinfoService;
import com.howbuy.crm.hb.service.prosale.CustprivatefundtradeService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.service.prosale.ProductinfoService;
import com.howbuy.crm.hb.service.prosale.ZxPrivateTradeFundService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.custinfo.*;
import com.howbuy.crm.hb.web.support.custinfo.HighDealOrderSupport;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.CmCustSourceCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.cache.JgjjCache;
import com.howbuy.crm.page.core.domain.PageCmJjxx1Jgflb;
import com.howbuy.crm.page.core.webapp.util.SessionUserManager;
import com.howbuy.crm.prosale.dto.Custprivatefundtrade;
import com.howbuy.crm.trade.common.constant.MarkConstants;
import com.howbuy.crm.util.MainLogUtils;
import com.howbuy.crm.util.hfile.FileSdkPathInfo;
import com.howbuy.crm.util.hfile.FileSdkUtil;
import com.howbuy.dfile.HFileService;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.simu.dto.base.product.SmcccpJbxxDto;
import com.howbuy.simu.service.base.product.SmccProductJbxxService;
import com.howbuy.tms.high.batch.facade.query.queryhighfundarrivalproof.QueryHighFundArrivalProofFacade;
import com.howbuy.tms.high.batch.facade.query.queryhighfundarrivalproof.QueryHighFundArrivalProofRequest;
import com.howbuy.tms.high.batch.facade.query.queryhighfundarrivalproof.QueryHighFundArrivalProofResponse;
import com.howbuy.tms.high.batch.facade.query.queryhighfundarrivalproof.bean.QueryHighFundArrivalProofCondition;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmFacade;
import com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmRequest;
import com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmResponse;
import com.howbuy.tms.high.orders.facade.search.querydealorder.QueryDealOrderFacade;
import com.howbuy.tms.high.orders.facade.search.querydealorder.QueryDealOrderRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorder.QueryDealOrderResponse;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolFacade;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolRequest;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolResposne;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.bean.CustRepurchaseProtocolBean;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.enums.CrmCustInvestTypeEnum;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.enums.DisCodeEnum;
import crm.howbuy.base.utils.CommonUtil;
import crm.howbuy.base.utils.DisCodeUtil;
import crm.howbuy.base.utils.HttpUtils;
import crm.howbuy.base.utils.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/4/8 10:41
 */
@Slf4j
@Controller
@RequestMapping("/fundtrade")
public class HighDealOrderController  extends BaseController {

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;
    @Autowired
    private QueryDealOrderListFacade queryDealOrderListFacade;
    @Autowired
    private QueryCustRepurchaseProtocolFacade queryCustRepurchaseProtocolFacade;
    @Autowired
    private ConscustService conscustService;
    @Autowired
    private ProductinfoService productinfoService;
    @Autowired
    private CmBxPrebookBuyinfoService cmBxPrebookBuyinfoService;
    @Autowired
    private PrebookproductinfoService prebookproductinfoService;
    @Autowired
	private DecryptSingleFacade decryptSingleFacade;

    @Autowired
    private QueryBalanceVolDtlForCrmFacade queryBalanceVolDtlForCrmFacade;
    @Autowired
    private QueryDealOrderFacade queryDealOrderFacade;

    @Autowired
    private JjxxInfoService jjxxInfoService;
    
    @Autowired
    private ZxPrivateTradeFundService zxPrivateTradeFundService;
    
    @Autowired
    private CustprivatefundtradeService custprivatefundtradeService;
    @Autowired
    private QueryHighFundArrivalProofFacade queryHighFundArrivalProofFacade;
    @Autowired
    private HighProductService highProductService;
    @Autowired
    private SmccProductJbxxService smccProductJbxxService;
    @Autowired
    private CrmHighDealOrderService crmHighDealOrderService;

    private static final String PE0053="PE0053";
    @Autowired
    private HighDealOrderSupport highDealOrderSupport;
    /**
     * 高端交易情况:
    * <AUTHOR>
    * @date 2020/4/8
    */
    @RequestMapping("/highDealOrderList.do")
    public String highDealOrderList(HttpServletRequest request){
        request.setAttribute("conscustno",request.getParameter("conscustno"));
        request.setAttribute("pubcustno",request.getParameter("pubcustno"));
        request.setAttribute("cpTopDataCG", StaticVar.CP_TOP_DATA_CG);
        request.setAttribute("isHaveAuth", true);
        request.setAttribute("isHavePageAuth", highDealOrderSupport.isHavePageAuth(request));
        return "/custinfo/highDealOrderList";

    }
    
    /**
     * 高端交易情况:
    * <AUTHOR>
    * @date 2020/4/8
    */
    @RequestMapping("/zxHighTradeList.do")
    public String zxHighTradeList(HttpServletRequest request){
        request.setAttribute("conscustno",request.getParameter("conscustno"));
        request.setAttribute("pubcustno",request.getParameter("pubcustno"));
        return "/custinfo/zxHighTradeList";

    }
    
    /**
     * 高端产品持仓:
    * <AUTHOR>
    * @date 2020/4/8
    */
    @ResponseBody
    @RequestMapping("/queryAcctBalanceList.do")
    @Deprecated
    public Map<String, Object> queryAcctBalanceList(String conscustno, HttpServletRequest request) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>(8);
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(conscustno);
        log.info("QueryConscustInfoRequest："+JSON.toJSONString(queryRequest));
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        log.info("QueryConscustInfoResponse："+JSON.toJSONString(queryResponse));
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();

        Map<String,Object> resultmap = highDealOrderSupport.getBalanceListbean(CrmCustInvestTypeEnum.getEnum(conscust.getInvsttype()), conscust.getHboneno(), false, null);
        List<QueryAcctBalanceResponse.BalanceBean> listbean = (List<QueryAcctBalanceResponse.BalanceBean>)resultmap.get("list");
        List<CrmBalanceBean> listcrmbean = new LinkedList();
        if (listbean != null && listbean.size() > 0) {
            // 新增参数
            BigDecimal profit = new BigDecimal("0.00");
            BigDecimal value = new BigDecimal("0.00");
            BigDecimal profitUs = new BigDecimal("0.00");
            BigDecimal valueUs = new BigDecimal("0.00");
            int rmbCount = 0;
            int usCount = 0;

            // 判断常量表中合规标识：true启用，false停用
            LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
            boolean roleCpFlag = false;
            if (cacheMap != null && !cacheMap.isEmpty()) {
                roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
            }

            // 判断登录人员的角色中是否包括“合规人员”角色
            List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
            boolean isRoleCp = false;
            if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
                isRoleCp = true;
            }

            Map<String, PageCmJjxx1Jgflb> jgjjBeanMap = JgjjCache.getInstance().getJgjjBeanMap();
            List<String> fundList = new ArrayList<String>();
            for (QueryAcctBalanceResponse.BalanceBean instance : listbean) {
                if (isRoleCp && jgjjBeanMap.containsKey(instance.getProductCode()) && ("21".equals(jgjjBeanMap.get(instance.getProductCode()).getEjfl()) || "31".equals(jgjjBeanMap.get(instance.getProductCode()).getEjfl()) || "41".equals(jgjjBeanMap.get(instance.getProductCode()).getEjfl()) || "61".equals(jgjjBeanMap.get(instance.getProductCode()).getEjfl()))) {
                    continue;
                }
                BigDecimal floatProfit = instance.getCurrentAssetCurrency();
                BigDecimal marketCap = instance.getCurrencyMarketValue();
                if (floatProfit == null) {
                    floatProfit = new BigDecimal("0.00");
                    instance.setCurrentAssetCurrency(floatProfit);
                }
                if (marketCap == null) {
                    marketCap = new BigDecimal("0.00");
                    instance.setCurrencyMarketValue(marketCap);
                }
                if (instance.getBalanceVol() == null) {
                    instance.setBalanceVol(new BigDecimal("0.00"));
                }
                if (instance.getNav() == null) {
                    instance.setNav(new BigDecimal("0.00"));
                }
                if (instance.getAccumRealizedIncome() == null) {
                    instance.setAccumRealizedIncome(new BigDecimal("0.00"));
                }
                // 新增3,5.0 合计项
                if ("156".equals(instance.getCurrency())) {
                	// 市值合计
                    value = value.add(marketCap);
                    if (!fundList.contains(instance.getProductCode()) && "1".equals(instance.getIncomeCalStat())) {
                    	// 持仓收益合计（按产品维度去重且只统计计算完成的）
                    	profit = profit.add(floatProfit);
                    }
                    rmbCount++;
                } else {
                	// 市值合计
                    valueUs = valueUs.add(marketCap);
                    if (!fundList.contains(instance.getProductCode()) && "1".equals(instance.getIncomeCalStat())) {
                    	// 持仓收益合计（按产品维度去重且只统计计算完成的）
                    	profitUs = profitUs.add(floatProfit);
                    }
                    usCount++;
                }
                if (StringUtil.isNotNullStr(instance.getCurrency())) {
                    instance.setCurrency(ConstantCache.getInstance().getConstantKeyVal("currencys").get(instance.getCurrency()));
                }
                if (StringUtil.isNotNullStr(instance.getScaleType())) {

                    JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(instance.getProductCode(), false);
                	if (jjxxInfo != null) {
                		if ("1".equals(jjxxInfo.getSfmsjg())) { // 代销
                			instance.setScaleType("代销");
                		} else if ("3".equals(jjxxInfo.getSfmsjg())) { // 直转代
                			instance.setScaleType("直转代");
                		} else { // 直销
                			instance.setScaleType("直销");
                		}
                	}
                }
                if (StringUtil.isNotNullStr(instance.getProductType())) {
                    if ("7".equals(instance.getProductType())) {
                        instance.setProductType("专户");
                    } else if ("11".equals(instance.getProductType())) {
                        instance.setProductType("私募");
                    } else {
                        instance.setProductType("其他");
                    }
                }
                CrmBalanceBean crmbalance = new CrmBalanceBean();
                try {
                    BeanUtils.copyProperties(instance, crmbalance);
                } catch (Exception e) {
                }
                JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(instance.getProductCode(), false);
                if (jjxxInfo != null) {
                    crmbalance.setCompany(jjxxInfo.getGljc());
                    crmbalance.setHmcpx(jjxxInfo.getHmcpx());
                    crmbalance.setDuedt(jjxxInfo.getDuedt());
                }
                if (crmbalance.getCashCollection() == null) {
                    crmbalance.setCashCollection(new BigDecimal("0.00"));
                }
                listcrmbean.add(crmbalance);

                // 将产品编码放入List中用于判重
                fundList.add(instance.getProductCode());
            }

            CrmBalanceBean newcrmbal = null;
            // 合并后的User存在map中
            Map<String, CrmBalanceBean> map = new HashMap<String, CrmBalanceBean>(8);
            for (CrmBalanceBean crmbalance : listcrmbean) {
                List<SubCrmBalanceBean> listcrmbalance = new ArrayList();
                SubCrmBalanceBean subCrmBalanceBean = new SubCrmBalanceBean();
                newcrmbal = map.get(crmbalance.getProductCode());
                BeanUtils.copyProperties(crmbalance, subCrmBalanceBean);
                if (newcrmbal != null) {
                	// 持仓合并
                    newcrmbal.setBalanceVol(newcrmbal.getBalanceVol().add(crmbalance.getBalanceVol())); 
                    // 当前市值合并
                    newcrmbal.setCurrencyMarketValue(newcrmbal.getCurrencyMarketValue().add(crmbalance.getCurrencyMarketValue()));
                    //累计应收管理费合并
                    if(newcrmbal.getReceivManageFee() == null){
                    	//累计应收管理费合并
                        newcrmbal.setReceivManageFee(BigDecimal.ZERO.add(crmbalance.getReceivManageFee() == null ? BigDecimal.ZERO : crmbalance.getReceivManageFee()));
                    }else{
                    	//累计应收管理费合并
                        newcrmbal.setReceivManageFee(newcrmbal.getReceivManageFee().add(crmbalance.getReceivManageFee() == null ? BigDecimal.ZERO : crmbalance.getReceivManageFee()));
                    }

                    if(newcrmbal.getReceivPreformFee() == null){
                    	//累计应收管理费合并
                        newcrmbal.setReceivPreformFee(BigDecimal.ZERO.add(crmbalance.getReceivPreformFee() == null ? BigDecimal.ZERO : crmbalance.getReceivPreformFee()));
                    }else{
                    	//累计应收管理费合并
                        newcrmbal.setReceivPreformFee(newcrmbal.getReceivPreformFee().add(crmbalance.getReceivPreformFee() == null ? BigDecimal.ZERO : crmbalance.getReceivPreformFee()));
                    }

                    if(newcrmbal.getMarketValueExFee() == null){
                    	//累计应收管理费合并
                        newcrmbal.setMarketValueExFee(BigDecimal.ZERO.add(crmbalance.getMarketValueExFee() == null ? BigDecimal.ZERO : crmbalance.getMarketValueExFee()));
                    }else{
                    	//累计应收管理费合并
                        newcrmbal.setMarketValueExFee(newcrmbal.getMarketValueExFee().add(crmbalance.getMarketValueExFee() == null ? BigDecimal.ZERO : crmbalance.getMarketValueExFee()));
                    }

                    if(newcrmbal.getCurrencyMarketValueExFee() == null){
                    	//累计应收管理费合并
                        newcrmbal.setCurrencyMarketValueExFee(BigDecimal.ZERO.add(crmbalance.getCurrencyMarketValueExFee() == null ? BigDecimal.ZERO : crmbalance.getCurrencyMarketValueExFee()));
                    }else{
                    	//累计应收管理费合并
                        newcrmbal.setCurrencyMarketValueExFee(newcrmbal.getCurrencyMarketValueExFee().add(crmbalance.getCurrencyMarketValueExFee() == null ? BigDecimal.ZERO : crmbalance.getCurrencyMarketValueExFee()));
                    }

                    if (!"2".equals(crmbalance.getHmcpx())) {
                    	// 持仓收益
                        newcrmbal.setCurrentAssetCurrency(newcrmbal.getCurrentAssetCurrency() == null ? BigDecimal.ZERO : newcrmbal.getCurrentAssetCurrency().add(crmbalance.getCurrentAssetCurrency() == null ? BigDecimal.ZERO : crmbalance.getCurrentAssetCurrency()));
                        // 已实现收益
                        newcrmbal.setAccumRealizedIncome(newcrmbal.getAccumRealizedIncome() == null ? BigDecimal.ZERO : newcrmbal.getAccumRealizedIncome().add(crmbalance.getAccumRealizedIncome() == null ? BigDecimal.ZERO : crmbalance.getAccumRealizedIncome()));
                    }

                    if ("0".equals(crmbalance.getIncomeCalStat())) {
                        newcrmbal.setIncomeCalStat(crmbalance.getIncomeCalStat());
                    }
                    newcrmbal.getSubCrmBalanceBeanList().add(subCrmBalanceBean);
                } else {
                    listcrmbalance.add(subCrmBalanceBean);
                    crmbalance.setSubCrmBalanceBeanList(listcrmbalance);
                    map.put(crmbalance.getProductCode(), crmbalance);
                }
            }

            listcrmbean = mapTransitionList(map);
            resultMap.put("total", listcrmbean.size());
            resultMap.put("rows", listcrmbean);

            // 新增 3.5.0 合计项
            resultMap.put("totalValue", value);
            resultMap.put("totalProfit", profit);
            resultMap.put("totalValueUS", valueUs);
            resultMap.put("totalProfitUS", profitUs);
            resultMap.put("rmbCount", rmbCount);
            resultMap.put("usCount", usCount);
            resultMap.put("record", listcrmbean.size());
        }
        return resultMap;
    }


    /**
     * null处理  默认返回BigDecimal.ZERO
     * @param bigDecimal
     * @return
     */
    private BigDecimal dealNullValue(BigDecimal bigDecimal){
         return bigDecimal==null?BigDecimal.ZERO:bigDecimal;
    }


    private String getSaleTypeBySfmsjg(String sfmsjg){
        if ("1".equals(sfmsjg)) { // 代销
            return "代销";
        } else if ("3".equals(sfmsjg)) { // 直转代
            return "直转代";
        } else { // 直销
            return "直销";
        }
    }

    private String getProductType(String code){
        if ("7".equals(code)) {
           return "专户";
        } else if ("11".equals(code)) {
            return "私募";
        } else {
            return "其他";
        }
    }

  //阳关私募 --hmcpx not in ['2','5']
    //listBean大于1条时，生成合并信息
   private CrmYgsmProdDto buildYgsmDto(JjxxInfo jjxxInfo, List<CRMBalanceBean> listbean){
       CrmYgsmProdDto returnDto;
       List<CrmYgsmProdDto> list= Lists.newArrayList();
       String prodCode=jjxxInfo.getJjdm();
       listbean.forEach(bean->{
           CrmYgsmProdDto detailDto=new CrmYgsmProdDto();
           fillProdInfo(jjxxInfo,detailDto);
           fillCalculateInfo(bean,detailDto);
           //填充平衡因子信息
           fillBalanceFactorInfo(bean,detailDto);
           //明细暂定使用subProdCode
           detailDto.setProductCode(bean.getSubProductCode());

           if(Objects.equals(StaticVar.YES,bean.getQianXiFlag())){
               detailDto.setQianXiFlag(bean.getQianXiFlag());
               detailDto.incrUnPaidInAmt(bean.getUnPaidInAmt());
               detailDto.incrCurrencyUnPaidInAmt(bean.getCurrencyUnPaidInAmt());
               detailDto.incrPaidInAmt(bean.getPaidInAmt());
           }

           detailDto.incrAccumIncome(bean.getAccumIncome());
           detailDto.incrAccumRealizedIncome(bean.getAccumRealizedIncome());
           detailDto.incrBalanceVol(bean.getBalanceVol());
           //参考市值=参考市值+待投金额
           detailDto.incrCurrencyMarketValue(bean.getCurrencyMarketValue());

           detailDto.incrCurrencyMarketValueExFee(bean.getCurrencyMarketValueExFee());
           detailDto.incrCurrentAssetCurrency(bean.getCurrentAssetCurrency());
           //持仓收益率
           detailDto.incrCurrentYields(bean.getYieldRate());
           detailDto.incrReceivManageFee(bean.getReceivManageFee());
           detailDto.incrReceivPreformFee(bean.getReceivPreformFee());
           //持仓收益率=[持仓收益/持仓成本]*100    当前币种
//           if(bean.getBalanceCostCurrency()!=null && bean.getCurrentAssetCurrency()!=null
//                         && bean.getBalanceCostCurrency().compareTo(BigDecimal.ZERO)>0){
//               //detailDto.setCurrentYields(bean.getCurrentAssetCurrency().divide(bean.getBalanceCostCurrency(),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
//               detailDto.setCurrentYields(processYieldRate(bean.getBalanceCostCurrency(),bean.getCurrentAssetCurrency()).multiply(BigDecimal.valueOf(100)));
//           }
           detailDto.setDivMode(bean.getDivMode());

           list.add(detailDto);
       });
       /* 当为分期成立产品[StageEstablishFlag=1-是] 或者 有N条持仓明细记录 . 一定合并展示 */
       boolean stageFlag=listbean.stream().filter(bean -> "1".equals(bean.getStageEstablishFlag())).count()>0; //包含[StageEstablishFlag=1]为 true
       if(stageFlag || list.size()>1){
           // 合并展示字段：  “持仓份额”、“参考市值”、“持仓收益”、“累计收益”、“已实现收益”
           returnDto=new CrmYgsmProdDto();
           returnDto.getSubList().addAll(list);
           //group取值： 产品信息
           fillProdInfo(jjxxInfo,returnDto);
           //汇总使用prodCode
           returnDto.setProductCode(prodCode);
           //group取值：get[0]
           fillCalculateInfo(listbean.get(0),returnDto);

           //汇总属性 balance
           incrYgsmBalanceValue(returnDto);

           //汇总是否 计算完成
           returnDto.setCalculateFinish(returnDto.getSubList().stream().filter(d-> !d.isCalculateFinish()).count()==0);

       }else{
           returnDto=list.get(0);
           returnDto.setProductCode(prodCode);
       }
       //汇总取值
       return returnDto;

   }

//    private BigDecimal processYieldRate(BigDecimal balanceCost, BigDecimal currentAsset) {
//        BigDecimal yieldRate = null;
//        if(balanceCost == null || BigDecimal.ZERO.compareTo(balanceCost) == 0){
//            yieldRate = BigDecimal.ZERO;
//        }else{
//            // 收益为负
//            if(currentAsset != null && BigDecimal.ZERO.compareTo(currentAsset) > 0){
//                BigDecimal yieldRate5 = currentAsset.divide(balanceCost, 5, BigDecimal.ROUND_DOWN);
//                BigDecimal yieldRate4 = currentAsset.divide(balanceCost, 4, BigDecimal.ROUND_DOWN);
//                if(yieldRate5.compareTo(yieldRate4) < 0){
//                    yieldRate = yieldRate5.setScale(4, BigDecimal.ROUND_UP);
//                }else {
//                    yieldRate = yieldRate5.setScale(4, BigDecimal.ROUND_DOWN);
//                }
//            }else{
//                // 收益为正
//                yieldRate = (currentAsset==null?BigDecimal.ZERO:currentAsset).divide(balanceCost, 4, BigDecimal.ROUND_DOWN);
//            }
//        }
//        return yieldRate;
//    }
    /**
     * 填充平衡因子信息
     * @param bean
     * @param detailDto
     */
    private void fillBalanceFactorInfo(CRMBalanceBean bean, CrmYgsmProdDto detailDto) {
        detailDto.setBalanceFactor(bean.getBalanceFactor());
        detailDto.setConvertFinish(bean.getConvertFinish());
        detailDto.setBalanceFactorDate(bean.getBalanceFactorDate());
    }

    /**
     * 汇总方法。
     * 只统计明细列表
     * @param resultDto
     */
   private void incrYgsmBalanceValue(CrmYgsmProdDto resultDto){
       if (CollectionUtils.isEmpty(resultDto.getSubList())) {
           return;
       }

//       合并展示字段：  “持仓份额”、“参考市值”、“持仓收益”、“累计收益”、“已实现收益”
       //包含明细  只统计明细列表
       final List<CrmYgsmProdDto> usedList=resultDto.getSubList();
       usedList.forEach(sub->{
           resultDto.incrBalanceVol(sub.getBalanceVol());//持仓份额
           resultDto.incrCurrencyMarketValue(sub.getCurrencyMarketValue());//参考市值加上待投金额
           resultDto.incrCurrencyMarketValue(sub.getCurrencyUnPaidInAmt());//待投金额
           resultDto.incrCurrentAssetCurrency(sub.getCurrentAssetCurrency());//持仓收益
           resultDto.incrAccumIncome(sub.getAccumIncome());//累计收益
           resultDto.incrAccumRealizedIncome(sub.getAccumRealizedIncome());//累计已实现收益

           if(Objects.equals(StaticVar.YES,sub.getQianXiFlag())){
               resultDto.incrUnPaidInAmt(sub.getUnPaidInAmt());
               resultDto.incrCurrencyUnPaidInAmt(sub.getCurrencyUnPaidInAmt());
           }
       });
   }


   //固定收益-- hmcpx=2
    private CrmGdsyProdDto buildGdsyDto(JjxxInfo jjxxInfo, List<CRMBalanceBean> listbean){
        List<CrmGdsyProdDto> list=Lists.newArrayList();
        CrmGdsyProdDto returnDto;

        listbean.forEach(bean->{
            CrmGdsyProdDto detailDto=new CrmGdsyProdDto();
            fillProdInfo(jjxxInfo,detailDto);
            fillCalculateInfo(bean,detailDto);
            detailDto.setProductCode(bean.getSubProductCode());
            detailDto.incrAccumRealizedIncome(bean.getAccumRealizedIncome());//已实现收益

//            累计收益
//            a、当固收二级类型（standardFixedIncomeFlag）=“0股权固收”或“1正常固收”时，取持仓接口中的累计收益（股权固收新算法）原币
//            b、当固收二级类型=2-现金管理或 3-券商集合理财 或4-纯债产品时，取持仓接口中的累计收益字段

            //固收类且固收类型等于“0股权固收”或“1正常固收”）-->累计收益（股权新算法）人民币 .否则：累计收益（人民币）
            if("0".equals(bean.getStandardFixedIncomeFlag()) || "1".equals(bean.getStandardFixedIncomeFlag())){ //standardFixedIncomeFlag:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品
                detailDto.setCurrentYields(null);
                detailDto.incrAccumIncome(bean.getAccumIncomeNew());//累计收益=累计收益（股权固收新算法）原币
            }else{
                detailDto.incrAccumIncome(bean.getAccumIncome());//累计收益=累计收益
//                //持仓收益率=[持仓收益/持仓成本]*100
//                if(bean.getBalanceCost()!=null && bean.getCurrentAsset()!=null
//                               && bean.getBalanceCost().compareTo(BigDecimal.ZERO)>0){
//                    //detailDto.setCurrentYields(bean.getCurrentAsset().divide(bean.getBalanceCost(),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
//                    detailDto.setCurrentYields(processYieldRate(bean.getBalanceCost(),bean.getCurrentAsset()).multiply(BigDecimal.valueOf(100)));
//                }
            }
            //持仓收益率
            detailDto.incrCurrentYields(bean.getYieldRate());
            detailDto.incrBalanceVol(bean.getBalanceVol());
            detailDto.incrCurrencyMarketValue(bean.getCurrencyMarketValue());
            detailDto.incrCurrentAssetCurrency(bean.getCurrentAssetCurrency());

            detailDto.setBenchmark(bean.getBenchmark());
            detailDto.setValueDate(bean.getValueDate());
            detailDto.setDueDate(bean.getDueDate());
            detailDto.setDivMode(bean.getDivMode());
            list.add(detailDto);

        });

        if(list.size()==1){
            returnDto=list.get(0);
            returnDto.setProductCode(jjxxInfo.getJjdm());
        }else{
            returnDto=new CrmGdsyProdDto();
            returnDto.getSubList().addAll(list);
            //group取值： 产品信息
            fillProdInfo(jjxxInfo,returnDto);
            //group取值：get[0]
            fillCalculateInfo(listbean.get(0),returnDto);
            returnDto.setBenchmark(listbean.get(0).getBenchmark());
            returnDto.setValueDate(listbean.get(0).getValueDate());
            returnDto.setDueDate(listbean.get(0).getDueDate());

            //汇总是否 计算完成
            int unCalculateSize=returnDto.getSubList().stream().filter(d-> !d.isCalculateFinish()).collect(Collectors.toList()).size();
            returnDto.setCalculateFinish(unCalculateSize>0?false:true);

            //汇总属性 balance
            incrGdsyBalanceValue(returnDto);
        }
        return returnDto;
    }

    /**
     * 汇总方法。
     * @param resultDto
     */
    private void incrGdsyBalanceValue(CrmGdsyProdDto resultDto){
        if (CollectionUtils.isEmpty(resultDto.getSubList())) {
            return;
        }

        //包含明细  只统计明细列表
        final List<CrmGdsyProdDto> usedList=resultDto.getSubList();
        usedList.forEach(sub->{
            resultDto.incrAccumIncome(sub.getAccumIncome());
            resultDto.incrAccumRealizedIncome(sub.getAccumRealizedIncome());
            resultDto.incrBalanceVol(sub.getBalanceVol());
            resultDto.incrCurrencyMarketValue(sub.getCurrencyMarketValue());
            resultDto.incrCurrentAssetCurrency(sub.getCurrentAssetCurrency());
        });
    }

    /**
     * 回款进度 =已回款/投资成本
     * @param currencyCashCollection 已回款
     * @param currencyNetBuyAmount  投资成本
     * @return
     */
    private BigDecimal getBackYields(BigDecimal currencyCashCollection,BigDecimal currencyNetBuyAmount){
//回款进度 =已回款/投资成本
        if(currencyCashCollection!=null && currencyNetBuyAmount!=null
                && currencyNetBuyAmount.compareTo(BigDecimal.ZERO)>0){
            return currencyCashCollection
                    .divide(currencyNetBuyAmount,4,BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
             }
        return null;
    }

    /**
     * 私募股权 的 产品期限说明
     * 如果产品是清盘[crisisFlag=1-是]中，返回空；否则 返回字段:fundCXQXStr
     * @param crisisFlag 清盘中标识 0-否 1-是
     * @param fundCXQXStr 中台的产品期限说明
     * @return
     */
    private String  getProdQxsm(String crisisFlag, String fundCXQXStr){
        return "1".equals(crisisFlag)?null: fundCXQXStr;
    }


    //私募股权 --hmcpx=5
    private CrmSmgqProdDto buildSmgqDto(JjxxInfo jjxxInfo, List<CRMBalanceBean> listbean){
        List<CrmSmgqProdDto> list=Lists.newArrayList();
        CrmSmgqProdDto returnDto;
        listbean.forEach(bean->{
            CrmSmgqProdDto detailDto=new CrmSmgqProdDto();
            fillProdInfo(jjxxInfo,detailDto);
            fillCalculateInfo(bean,detailDto);
            detailDto.setProductCode(bean.getSubProductCode());

            //持仓份额
            detailDto.incrBalanceVol(bean.getBalanceVol());
            //当前投资成本 -现有持仓份额部分对应购入本金金额-  净购买金额(投资成本)(当前币种)
            detailDto.incrCurrencyNetBuyAmount(bean.getCurrencyNetBuyAmount());
            //已发生回款  项目回款金额总和  私募股权回款(当前币种)
            detailDto.incrCurrencyCashCollection(bean.getCurrencyCashCollection());
            //认缴金额
            detailDto.incrPaidInAmt(bean.getPaidInAmt());
            //投资期限
            detailDto.setInvestmentHorizon(bean.getInvestmentHorizon());
            //产品期限说明
            detailDto.setProdQxsm(getProdQxsm(bean.getCrisisFlag(),bean.getFundCXQXStr()));

            if(PE0053.equals(bean.getProductCode())){// 私募股权-代码等于PE0053时
                //持仓收益 -持仓接口中的持仓收益
                detailDto.incrBalanceIncomeNew(bean.getCurrentAssetCurrency());
                //累计收益 -持仓接口中的累计收益
                detailDto.incrAccumIncomeNew(bean.getAccumIncome());
            }else{ // 私募股权-代码不等于PE0053时，取接口中 累计收益（股权新算法）人民币，
                //持仓收益 -持仓接口中的持仓收益（股权新算法）原币
                detailDto.incrBalanceIncomeNew(bean.getBalanceIncomeNew());
                //累计收益 -取持仓接口中的累计收益（股权新算法）原币
                detailDto.incrAccumIncomeNew(bean.getAccumIncomeNew());
            }
            //回款进度 =已回款/投资成本
            detailDto.setBackYields(getBackYields(bean.getCurrencyCashCollection(),bean.getCurrencyNetBuyAmount()));
            list.add(detailDto);
        });

        if(list.size()==1){
            returnDto=  list.get(0);
            returnDto.setProductCode(jjxxInfo.getJjdm());
        }else{ //合计行
            returnDto=new CrmSmgqProdDto();
            returnDto.getSubList().addAll(list);
            //group取值： 产品信息
            fillProdInfo(jjxxInfo,returnDto);
            returnDto.setProductCode(jjxxInfo.getJjdm());
            //group取值：get[0]
            fillCalculateInfo(listbean.get(0),returnDto);
            returnDto.setInvestmentHorizon(listbean.get(0).getInvestmentHorizon());
            //产品期限说明
            returnDto.setProdQxsm(getProdQxsm(listbean.get(0).getCrisisFlag(),listbean.get(0).getFundCXQXStr()));
//            1、合计行中的“认缴金额”，直接展示为空白，无需展示“--”；
            returnDto.setPaidInAmt(null);
//            2、合计行中的持仓收益和累计收益，直接合计明细值，不需要“计算中”的逻辑判断；
            //固定： 计算完成
            returnDto.setCalculateFinish(true);
            //汇总属性 balance
            incrSmgqBalanceValue(returnDto);
            //回款进度 =已回款/投资成本
            returnDto.setBackYields(getBackYields(returnDto.getCurrencyCashCollection(),returnDto.getCurrencyNetBuyAmount()));
        }
        return returnDto;
    }


    /**
     * 汇总方法。
     * @param resultDto
     */
    private void incrSmgqBalanceValue(CrmSmgqProdDto resultDto){
        if (CollectionUtils.isEmpty(resultDto.getSubList())) {
            return;
        }

        //包含明细  只统计明细列表
        final List<CrmSmgqProdDto> usedList=resultDto.getSubList();
        usedList.forEach(sub->{
            resultDto.incrBalanceVol(sub.getBalanceVol());
            resultDto.incrAccumIncomeNew(sub.getAccumIncomeNew());
            resultDto.incrBalanceIncomeNew(sub.getBalanceIncomeNew());
            resultDto.incrCurrencyCashCollection(sub.getCurrencyCashCollection());
            resultDto.incrCurrencyNetBuyAmount(sub.getCurrencyNetBuyAmount());
            resultDto.incrPaidInAmt(sub.getPaidInAmt());
        });
    }

    /**
     * 全局统计
     * @param pageDto
     * @param beans
     * @param hmcpxEnum 好买产品线Enum
     */
   private void fillTotalPageCount(CrmHighBalancePageDto pageDto,List<CRMBalanceBean> beans ,HmcpxEnum hmcpxEnum){
           //汇总统计
           beans.forEach(bean->{
               // 市值合计 持仓明细中，接口字段“市值（人民币）”的合计值
               pageDto.incrTotalValue(bean.getMarketValue());
//               //判断是否在途，如果是在途
//               pageDto.incrTotalZtValue(bean.getMarketValue());
               pageDto.incrDetailCount();

               if(HmcpxEnum.YGSM.equals(hmcpxEnum)){ //阳光私募 tab  汇总(人民币)
//                   D、新增合计行（人民币），合计字段“参考市值”、“持仓收益”、“累计收益”、“已实现收益”，按接口中对应的人民币金额字段合计；
                   pageDto.getSumYgsmDto().incrAccumIncome(bean.getAccumIncomeRmb());//累计收益
                   pageDto.getSumYgsmDto().incrAccumRealizedIncome(bean.getAccumRealizedIncomeRmb());//累计已实现收益
                   pageDto.getSumYgsmDto().incrBalanceVol(bean.getBalanceVol());//持仓份额
                   pageDto.getSumYgsmDto().incrCurrencyMarketValue(bean.getMarketValue());//参考市值

                   //全局汇总
                   pageDto.incrTotalProfit(bean.getCurrentAsset());// 持仓收益合计-阳光私募和固收类取接口中“持仓收益（人民币）”
                   pageDto.incrTotalAccuProfit(bean.getAccumIncomeRmb());// 累计收益合计-阳光私募类取接口中“累计收益（人民币）”

                   //千禧年产品属于阳光私募,计算千禧年产品代投金额总数，封装千禧年产品用于界面展示
                   if(Objects.equals(StaticVar.YES,bean.getQianXiFlag())){

                       if(!pageDto.isHavQianXiFlag()){
                           pageDto.setHavQianXiFlag(true);
                       }

                       //阳光私募合计加上待投金额
                       pageDto.getSumYgsmDto().incrCurrencyUnPaidInAmt(bean.getUnPaidInAmt());
                       pageDto.getSumYgsmDto().incrCurrencyMarketValue(bean.getUnPaidInAmt());
                       //界面底部合计人民币
                       pageDto.incrCurrencyUnPaidInAmt(bean.getUnPaidInAmt());
                       pageDto.getListShowProductName().add(bean.getProductName());
                   }

               }else if (HmcpxEnum.GDSY.equals(hmcpxEnum)){ //固定收益 tab  汇总(人民币)
//                   G、新增合计行（人民币），合计字段“参考市值”、“持仓收益”、“累计收益”、“已实现收益”，按接口中对应的人民币金额字段合计。注意：不同固收类型取不同的累计收益字段；
                   pageDto.getSumGdsyDto().incrAccumRealizedIncome(bean.getAccumRealizedIncomeRmb());//累计已实现收益
                   pageDto.getSumGdsyDto().incrCurrencyMarketValue(bean.getMarketValue());//参考市值
                   pageDto.getSumGdsyDto().incrCurrentAssetCurrency(bean.getCurrentAsset());//持仓收益

                   //全局汇总
                   pageDto.incrTotalProfit(bean.getCurrentAsset());// 持仓收益合计-阳光私募和固收类取接口中“持仓收益（人民币）”
                   //累计收益合计- 固收类且固收类型等于“0股权固收”或“1正常固收”）-->累计收益（股权新算法）人民币 .否则：累计收益（人民币）
                   if("0".equals(bean.getStandardFixedIncomeFlag()) || "1".equals(bean.getStandardFixedIncomeFlag())){ //standardFixedIncomeFlag:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品
                       pageDto.incrTotalAccuProfit(bean.getAccumIncomeNewRmb());
                       pageDto.getSumGdsyDto().incrAccumIncome(bean.getAccumIncomeNewRmb());//合计行-累计收益
                   }else{
                       pageDto.incrTotalAccuProfit(bean.getAccumIncomeRmb());
                       pageDto.getSumGdsyDto().incrAccumIncome(bean.getAccumIncomeRmb());//合计行-累计收益
                   }

               }else if (HmcpxEnum.SMGQ.equals(hmcpxEnum)){//私募股权 tab  汇总(人民币)
//                   H、新增合计行（人民币），合计字段“当前投资成本”、“已发生回款”、“累计收益”、“持仓收益”，按接口中对应的人民币金额字段合计。
//                   注意：不同的产品取不同的收益字段，既合计行要等于列表明细的人民汇总；
                   pageDto.getSumSmgqDto().incrCurrencyNetBuyAmount(bean.getNetBuyAmount());//合计行-投资成本
                   pageDto.getSumSmgqDto().incrCurrencyCashCollection(bean.getCashCollection());//合计行-私募股权回款

                   //全局汇总
                   pageDto.incrTotalStockRecovery(bean.getCashCollection());//累计已回款-totalStockRecovery：只取股权类的“已发生回款（人民币）”合计值
                   if(PE0053.equals(bean.getProductCode())){
                       pageDto.incrTotalProfit(bean.getCurrentAsset()); // 持仓收益合计-私募股权-代码等于PE0053时，取接口中“持仓收益（人民币）
                       pageDto.getSumSmgqDto().incrBalanceIncomeNew(bean.getCurrentAsset());//合计行-持仓收益
                       pageDto.incrTotalAccuProfit(bean.getAccumIncomeRmb());// 累计收益合计-私募股权-代码等于PE0053时，取接口中“累计收益（人民币）
                       pageDto.getSumSmgqDto().incrAccumIncomeNew(bean.getAccumIncomeRmb());//合计行-累计收益
                   }else{
                       pageDto.incrTotalProfit(bean.getBalanceIncomeNewRmb());// 持仓收益合计-私募股权-代码不等于PE0053时，取接口中“持仓收益（股权新算法）人民币”
                       pageDto.getSumSmgqDto().incrBalanceIncomeNew(bean.getBalanceIncomeNewRmb());//合计行-持仓收益
                       pageDto.incrTotalAccuProfit(bean.getAccumIncomeNewRmb());// 累计收益合计-私募股权-代码不等于PE0053时，取接口中 累计收益（股权新算法）人民币，
                       pageDto.getSumSmgqDto().incrAccumIncomeNew(bean.getAccumIncomeNewRmb());//合计行-累计收益
                   }

               }
           });
   }


    /**
     * 根据产品代码，填充基金相关信息
     * @param jjxxInfo
     * @param dto
     * @return
     */
    private <T extends CrmBalanceProdDto> void fillProdInfo(JjxxInfo jjxxInfo, T dto){
        if (jjxxInfo != null) {
            //转义scaleType
            if (StringUtil.isNotNullStr(dto.getScaleType()) ) { //bean
                dto.setScaleType(getSaleTypeBySfmsjg(jjxxInfo.getSfmsjg()));
            }
            dto.setCompany(jjxxInfo.getGljc());
            dto.setHmcpx(jjxxInfo.getHmcpx());
            dto.setDuedt(jjxxInfo.getDuedt());
            dto.setProductName(jjxxInfo.getJjjc());
            dto.setScaleType(getSaleTypeBySfmsjg(jjxxInfo.getSfmsjg()));
        }
    }

    /**
     * 根据持仓数据，填充 净值、产品持仓计算信息，设置待投金额等
     * @param bean
     * @param dto
     * @return
     */
    private <T extends CrmBalanceProdDto> void fillCalculateInfo(CRMBalanceBean bean, T dto){
        dto.setCurrency(bean.getCurrency());
        dto.setCurrencyStr(ConstantCache.getInstance().getConstantKeyVal("currencys").get(bean.getCurrency()));
        dto.setIncomeDt(bean.getIncomeDt());
        dto.setNav(bean.getNav());
        dto.setNavStr(CommonUtil.formatBigDecimalQfw(bean.getNav()));
        dto.setNavDt(bean.getNavDt());
        dto.setProductType(getProductType(bean.getProductType()));
        dto.setCalculateFinish(isCalculateFinishByStat(bean.getIncomeCalStat())); //1-计算完成 true
    }

    /**
     *  根据计算状态 0-计算中；1-计算完成 返回 是否计算完成
     *  true标识计算完成
     * @param incomeCalStat
     * @return
     */
    private boolean isCalculateFinishByStat(String incomeCalStat){
       return  "1".equals(incomeCalStat);
    }


    /**
     * 高端产品持仓:
     * <AUTHOR>
     * @date 2020/4/8
     */
    @ResponseBody
    @RequestMapping("/queryBalanceVolDtlForCrmFacade.do")
    public Map<String, Object> queryBalanceVolDtlForCrmFacade(String conscustno,String productcode, HttpServletRequest request) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>(8);
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(conscustno);
        log.info("QueryConscustInfoRequest：" + JSON.toJSONString(queryRequest));
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        log.info("QueryConscustInfoResponse：" + JSON.toJSONString(queryResponse));
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();

        QueryBalanceVolDtlForCrmRequest req = new QueryBalanceVolDtlForCrmRequest();
        req.setHbOneNo(conscust.getHboneno());
        req.setFundCode(productcode);
        req.setOutletCode("A20150120");
        req.setOperIp(getIpAddr(request));
        req.setTxChannel("1");
        req.setDataTrack("crmsale");
        req.setDisCodeList(jjxxInfoService.getHbFullDisCodeList());
        QueryBalanceVolDtlForCrmResponse rep = queryBalanceVolDtlForCrmFacade.execute(req);
        log.info("QueryBalanceVolDtlForCrm, request:{} ,response:{}" ,JSON.toJSONString(req), JSON.toJSONString(rep));
        resultMap.put("list", rep.getBalanceVolDtlList());
        return resultMap;
    }


    public static List<CrmBalanceBean> mapTransitionList(Map map) {
        List<CrmBalanceBean> listAll = new ArrayList<CrmBalanceBean>();
        List<CrmBalanceBean> listYgsm = new LinkedList<CrmBalanceBean>();
        List<CrmBalanceBean> listGq = new LinkedList<CrmBalanceBean>();
        List<CrmBalanceBean> listGs = new LinkedList<CrmBalanceBean>();
        // 获得map的Iterator
        Iterator iter = map.entrySet().iterator(); 
        while (iter.hasNext()) {
            Map.Entry<String, CrmBalanceBean> entry = (Map.Entry) iter.next();
            CrmBalanceBean tempCrmBalanceBean = (CrmBalanceBean) entry.getValue();
            if (StringUtils.isNotBlank(tempCrmBalanceBean.getHmcpx())) {
                String subProductCode = tempCrmBalanceBean.getHmcpx();
                log.info("=============subProductCode: " + subProductCode);
                if ("2".equals(subProductCode)) {
                    listGs.add(tempCrmBalanceBean);
                } else if ("5".equals(subProductCode)) {
                    listGq.add(tempCrmBalanceBean);
                } else {
                    listYgsm.add(tempCrmBalanceBean);
                }
            } else {
                listYgsm.add(tempCrmBalanceBean);
            }
        }
        if (listYgsm != null && listYgsm.size() > 0) {
            listAll.addAll(listYgsm);
        }
        if (listGq != null && listGq.size() > 0) {
            listAll.addAll(listGq);
        }
        if (listGs != null && listGs.size() > 0) {
            listAll.addAll(listGs);
        }
        return listAll;
    }

    /**
     * 获取资金到账证明文件pdf文件地址
     * @param dealNo 订单号 NOT NULL
     * @return
     */
    private String getHighFundArrivalPdfFilePath(String dealNo){
        QueryHighFundArrivalProofRequest request=new QueryHighFundArrivalProofRequest();
        QueryHighFundArrivalProofCondition condition=new QueryHighFundArrivalProofCondition();
        condition.setDealNo(dealNo);
        request.setCondition(condition);
//        disCode  outletCode operIp txChannel
        request.setDisCode(DisCodeEnum.HOWBUY.getCode());//无业务含义
        request.setOutletCode("W20170215");
        request.setOperIp("127.0.0.1");
        request.setTxChannel("1");
        QueryHighFundArrivalProofResponse response=queryHighFundArrivalProofFacade.execute(request);
        log.info("查询资金到账证明,request:{},response:{}", JSONObject.toJSONString(request),JSONObject.toJSONString(response));
        if(response!=null && CollectionUtils.isNotEmpty(response.getHighFundArrivalProofBeans())){
            return response.getHighFundArrivalProofBeans().get(0).getPdfPath();
        }
        return  null;
    }

    /**
     * 获取[有限合伙产品]列表
     * @return
     */
    private List<String> getLimitedCooperativeCodeList(){
        List<String> prodCodeList=highProductService.getIsLimitedCooperativeProducts();
        log.info("中台获取有限合伙产品列表：{}", JSONObject.toJSONString(prodCodeList));
        return prodCodeList==null?Lists.newArrayList():prodCodeList;
    }


    /**
     * 高端产品交易记录:
    * <AUTHOR>
    * @date 2020/4/8
    */
    @Deprecated
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/queryAcctBalanceDealList.do")
    public Map<String, Object> queryAcctBalanceDealList(HttpServletRequest request) throws Exception {
        String conscustno = StringUtil.replaceNullStr(request.getParameter("conscustno"));
        String fundcode = StringUtil.replaceNullStr(request.getParameter("fundcode"));
        String busicode = StringUtil.replaceNullStr(request.getParameter("busicode"));
    	Map<String, Object> resultMap = new HashMap<String, Object>(2);
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(conscustno);
        log.info("QueryConscustInfoRequest："+JSON.toJSONString(queryRequest));
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        log.info("QueryConscustInfoResponse："+JSON.toJSONString(queryResponse));
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();

        List<QueryDealOrderListResponse.DealOrderBean> dealOrderlist = this.queryDealOrderList(request,CrmCustInvestTypeEnum.getEnum(conscust.getInvsttype()),conscust.getHboneno());

//        获取[有限合伙产品]列表
        List<String> limitedCoopProdCodesList=getLimitedCooperativeCodeList();
        List<String> orderStatusList=Lists.newArrayList("1","2","3");//订单状态=[1-申请成功  2-部分确认 3-确认成功]
        List<String> buyBusiCodeList=Lists.newArrayList("1120","1122"); //mbusiCode=1120-认购 1122-申购

        List<CrmDealOrderBean> crmdealOrderlist = new ArrayList<>();
        if (dealOrderlist != null && dealOrderlist.size() > 0) {
        	
            // 判断常量表中合规标识：true启用，false停用
            LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
            boolean roleCpFlag = false;
            if (cacheMap != null && !cacheMap.isEmpty()) {
                roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
            }

            // 判断登录人员的角色中是否包括“合规人员”角色
            List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
            boolean isRoleCp = false;
            if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
                isRoleCp = true;
            }
            Map<String, PageCmJjxx1Jgflb> jgjjBeanMap = JgjjCache.getInstance().getJgjjBeanMap();

            // 通过Session获取产品广度信息
            HttpSession session = request.getSession();
            String topcpdata = (String) session.getAttribute("topcpdata");
            for (QueryDealOrderListResponse.DealOrderBean dealorder : dealOrderlist) {
                if (isRoleCp && jgjjBeanMap.containsKey(dealorder.getProductCode()) && ("21".equals(jgjjBeanMap.get(dealorder.getProductCode()).getEjfl()) || "31".equals(jgjjBeanMap.get(dealorder.getProductCode()).getEjfl()) || "41".equals(jgjjBeanMap.get(dealorder.getProductCode()).getEjfl()) || "61".equals(jgjjBeanMap.get(dealorder.getProductCode()).getEjfl()))) {
                    continue;
                }
                if(StringUtil.isNotNullStr(fundcode) && !fundcode.equals(dealorder.getProductCode()) /*&& !fundcode.equals(dealorder.getMjjdm())*/){
                	continue;
            	}
                JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(dealorder.getProductCode(), false);

                if(jjxxInfo==null){
                    log.error("持仓记录数据，基金代码：{} 不存在！",dealorder.getProductCode());
                    continue;
                }

                // 获取产品分类字段信息
                String cpfl = jjxxInfo.getCpfl();

                // 获取是否香港字段信息
                String sfxg = jjxxInfo.getSfxg();

                // 获取是否标准固收信息
                String sfbzgs = jjxxInfo.getSfbzgs();
                if ("32".equals(topcpdata) && jjxxInfo != null) {
                    // 产品过滤条件：只显示满足 （产品分类 = 1-固定收益、2-公募基金、4-私募基金、5-券商集合理财、10-特殊私募）
                    // 且 是否香港 = 否 的产品 且 固收类型（是否标准固收） != （0-股权固收、1-正常固收）
                    if (!("1".equals(cpfl) || "2".equals(cpfl) || "4".equals(cpfl) || "5".equals(cpfl) || "10".equals(cpfl)) || "1".equals(sfxg) || "0".equals(sfbzgs) || "1".equals(sfbzgs)) {
                        continue;
                    }
                }

                //if(StringUtil.isNotNullStr(busicode) && (!busicode.equals(dealorder.getmBusiCode()) || (StaticVar.MBUSICODE_SMGQHK.equals(busicode) && ))){
                //选择了私募股权回款查询，需要
                if (StringUtil.isNotNullStr(busicode) && StaticVar.MBUSICODE_SMGQHK.equals(busicode) && !(jjxxInfo != null && StaticVar.HMCPX_PEVC.equals(jjxxInfo.getHmcpx()) && StaticVar.MBUSICODE_FH.equals(dealorder.getmBusiCode()))){
                	continue;
            	}
                //选择的非分红和私募股权回款的，去除不相符的交易记录
                if (StringUtil.isNotNullStr(busicode) && !StaticVar.MBUSICODE_SMGQHK.equals(busicode) && !busicode.equals(dealorder.getmBusiCode())){
                	continue;
                }
                //选择的分红，但是符合私募股权回款的也要去除
                if (StringUtil.isNotNullStr(busicode) && StaticVar.MBUSICODE_FH.equals(busicode) && jjxxInfo != null && StaticVar.HMCPX_PEVC.equals(jjxxInfo.getHmcpx()) && StaticVar.MBUSICODE_FH.equals(dealorder.getmBusiCode())){
                	continue;
            	}
                //是否为【有限合伙产品】
                boolean limitedCoopProd=limitedCoopProdCodesList.contains(dealorder.getProductCode());


                //产品属于【有限合伙产品】， mbusiCode=1120-认购  1122-申购
                // 且 订单状态=[1-申请成功  2-部分确认 3-确认成功]，付款=4-成功 ,支付方式=01-自划款   的，
                // 才查询 到账证明
                boolean needQueryPdfpath=false;
                if(limitedCoopProd &&
                        dealorder.getmBusiCode()!=null  &&  buyBusiCodeList.contains(dealorder.getmBusiCode()) &&
                        dealorder.getOrderStatus()!=null  &&  orderStatusList.contains(dealorder.getOrderStatus()) &&
                         "4".equals(dealorder.getPayStatus()) &&
                          "01".equals(dealorder.getPaymentType())
                       ){
                    needQueryPdfpath=true;
                }


                if (StringUtil.isNotNullStr(dealorder.getPayStatus())) {
                    dealorder.setPayStatus(ConstantCache.getInstance().getConstantKeyVal("dealpayStatus").get(dealorder.getPayStatus()));
                }

                if (StringUtil.isNotNullStr(dealorder.getOrderStatus())) {
                    dealorder.setOrderStatus(ConstantCache.getInstance().getConstantKeyVal("dealorderStatus").get(dealorder.getOrderStatus()));
                }

                if (StringUtil.isNotNullStr(dealorder.getBankCode()) && !"null".equals(dealorder.getBankCode())) {
                    dealorder.setBankCode(ConstantCache.getInstance().getConstantKeyVal("bankType").get(dealorder.getBankCode()));
                } else {
                    dealorder.setBankCode("");
                }

                if (StringUtil.isNullStr(dealorder.getBankAcct()) || "null".equals(dealorder.getBankAcct())) {
                    dealorder.setBankAcct("");
                } else {
                    dealorder.setBankAcct(dealorder.getBankAcct().replace(" ", ""));
                }

                
                if (StringUtil.isNotNullStr(dealorder.getmBusiCode())) {
                    if (jjxxInfo != null && StaticVar.HMCPX_PEVC.equals(jjxxInfo.getHmcpx()) && StaticVar.MBUSICODE_FH.equals(dealorder.getmBusiCode())) {
                        dealorder.setmBusiCode(ConstantCache.getInstance().getConstantKeyVal("mBusiCode").get(StaticVar.MBUSICODE_SMGQHK));
                    } else {
                        dealorder.setmBusiCode(ConstantCache.getInstance().getConstantKeyVal("mBusiCode").get(dealorder.getmBusiCode()));
                    }
                }
                if (dealorder.getAckAmt() == null) {
                    dealorder.setAckAmt(new BigDecimal("0.00"));
                }

                if (dealorder.getAppAmt() == null) {
                    dealorder.setAppAmt(new BigDecimal("0.00"));
                }

                if (dealorder.getAckVol() == null) {
                    dealorder.setAckVol(new BigDecimal("0.00"));
                }
                if (dealorder.getNav() == null) {
                    dealorder.setNav(new BigDecimal("0.00"));
                }
                if (dealorder.getFee() == null) {
                    dealorder.setFee(new BigDecimal("0.00"));
                }
                CrmDealOrderBean crmdealorder = new CrmDealOrderBean();
                try {
                    BeanUtils.copyProperties(dealorder, crmdealorder);
                } catch (Exception e) {
                }
                if (jjxxInfo != null) {
                    crmdealorder.setClrq(jjxxInfo.getClrq());
                }
                //查询对应预约的手续费
                Map<String,String> paraminfo = new HashMap<String,String>(1);
        		paraminfo.put("dealno", dealorder.getDealNo());
        		Map<String,Object> orderinfo = prebookproductinfoService.getPreFeeBydealno(paraminfo);
        		if(orderinfo != null && orderinfo.get("FEE") != null){
        			crmdealorder.setPrefee(new BigDecimal(StringUtil.replaceNullStr(orderinfo.get("FEE"))));
        		}

                crmdealorder.setLimitedCoopProd(limitedCoopProd);

                //到账证明pdf文件地址
                if(needQueryPdfpath){
                    String arrivalProofPdfFilePath=getHighFundArrivalPdfFilePath(dealorder.getDealNo());
                    crmdealorder.setArrivalProofPdfFilePath(arrivalProofPdfFilePath);
                }

                // 设置业务类型（内容包括：“常规”、“好臻”、“海外”三种类型）
                handleBusiTypeName(dealorder.getDisCode(), dealorder.getHkSaleFlag(), crmdealorder);

                crmdealOrderlist.add(crmdealorder);
            }
            
        }
        StringBuilder sb = new StringBuilder();
        //选择交易类型的情况
        if(StringUtil.isNotNullStr(busicode)){
        	if(crmdealOrderlist.size() > 0){
        		sb.append("该交易类型共"+crmdealOrderlist.size() +"条记录，");
        		BigDecimal totalAppAmt = new BigDecimal("0.00");
        		BigDecimal totalAppFee = new BigDecimal("0.00");
        		BigDecimal totalAckAmt = new BigDecimal("0.00");
        		BigDecimal totalAckFee = new BigDecimal("0.00");
        		BigDecimal totalAckVol = new BigDecimal("0.00");
        		for(CrmDealOrderBean bean : crmdealOrderlist){
        			totalAppAmt = totalAppAmt.add(bean.getAppAmt());
        			if(bean.getPrefee() != null){
        				totalAppFee = totalAppFee.add(bean.getPrefee());
        			}
        			totalAckAmt = totalAckAmt.add(bean.getAckAmt());
	        		totalAckFee = totalAckFee.add(bean.getFee());
	        		totalAckVol = totalAckVol.add(bean.getAckVol());
	        	}
	        	sb.append("累计申请金额："+totalAppAmt.toPlainString()+"，申请手续费："+totalAppFee.toPlainString()+"，确认金额："+totalAckAmt.toPlainString()+"，确认手续费："+totalAckFee.toPlainString()+"，确认份额："+totalAckVol.toPlainString());
	        }else{
	        	//没有查询到数据的情况
	        	sb.append("该交易类型共0条记录，累计申请金额：0.00，申请手续费：0.00，确认金额：0.00，确认手续费：0.00，确认份额：0.00");
	        }
        }else{
        	//选择全部的情况
        	sb.append("可根据筛选的交易类型，汇总显示对应交易情况数据!");
        }
        resultMap.put("resultStr", sb.toString());
        resultMap.put("total", crmdealOrderlist.size());
        resultMap.put("rows", crmdealOrderlist);
        return resultMap;
    }

    /**
     * 设置业务类型名称
     *
     * 取值逻辑如下
     * 好臻：分销渠道=好臻分销
     * 海外：分销渠道=好买分销，且好买香港代销=是
     * 常规：除好臻及海外之外的场景，统一等于常规
     *
     * @param disCode 分销代码
     * @param hkSaleFlag 好买香港代销标识 0-否; 1-是
     * @param crmdealorder
     */
    private void handleBusiTypeName(String disCode, String hkSaleFlag, CrmDealOrderBean crmdealorder) {
        String busiTypeName;
        if (DisChannelCodeEnum.HZ.getCode().equals(disCode)) {
            busiTypeName = "好臻";
        } else if (DisChannelCodeEnum.HOWBUY.getCode().equals(disCode) && "1".equals(hkSaleFlag)) {
            busiTypeName = "海外";
        } else {
            busiTypeName = "常规";
        }
        crmdealorder.setBusiTypeName(busiTypeName);
    }

    /**
     * 高端产品交易记录:
     * <AUTHOR>
     * @date 2020/4/8
     */
    @ResponseBody
    @RequestMapping("/downloadDealOrder.do")
    public String downloadDealOrder(HttpServletRequest request,HttpServletResponse response){
        String conscustno = StringUtil.replaceNullStr(request.getParameter("consCustNo"));
        String dealNo = StringUtil.replaceNullStr(request.getParameter("dealNo"));

        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(conscustno);
        log.info("QueryConscustInfoRequest："+JSON.toJSONString(queryRequest));
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        log.info("QueryConscustInfoResponse："+JSON.toJSONString(queryResponse));
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();

        QueryDealOrderResponse dealorder = this.queryDealOrderFacade(request,conscustno,conscust.getHboneno(),dealNo);

        if (dealorder == null || dealorder.getVolConfirmBookPath() == null) {
            return "nofile";
        }else{
            return dealorder.getVolConfirmBookPath();
        }
    }


    /**
     * 获取文件的相对路径
     * NOTICE : /data/files/logs/default.txt -->  [data/files/logs]
     * @param filePath
     * @return
     */
    public static String getRelativePath(String filePath){
        //dFile 文件路径 。 要求： 不以 / 开头， 不以 / 结尾 。 此处适配 dfile各种参数场景
        int startIndex=0;
        if(filePath.indexOf(MarkConstants.SEPARATOR_SLASH)==0){
            startIndex=1;
        }
        return filePath.substring(startIndex,filePath.lastIndexOf(MarkConstants.SEPARATOR_SLASH));
    }

    /**
     * 获取文件的短名称
     * NOTICE : /data/files/logs/default.txt -->  [default.txt]
     * @param filePath
     * @return
     */
    public static String getFileShortName(String filePath){
        return filePath.substring(filePath.lastIndexOf(MarkConstants.SEPARATOR_SLASH)+1);
    }


    /**
     * @description:(下载份额确认书)
     * @param request	
     * @param response
     * @return void
     * @author: haoran.zhang
     * @date: 2024/7/23 19:35
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/downloadVolPdfFile.do")
    public void downloadVolPdfFile(HttpServletRequest request,HttpServletResponse response){

        //实际文件存储： /data/files/simu/middle/volpdf/88/SAKP55/304200202407191898310055_1446158192_SAKP55_confirm.pdf
        //参数为： 88/SAKP55/304200202407191898310055_1446158192_SAKP55_confirm.pdf
        String filepath = StringUtil.replaceNullStr(request.getParameter("filepath"));

        //实际读取文件：
        if (StringUtils.isNotBlank(filepath)) {

            try {
                String filename = filepath.substring(filepath.lastIndexOf("/") + 1);
                OutputStream fos = null;
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition", "attachment;fileName="+ new String(filename.getBytes("gb2312"), "ISO8859-1"));
                fos = response.getOutputStream();

                HFileService instance = HFileService.getInstance();
                String relativePath=getRelativePath(filepath);
                String shortName=getFileShortName(filepath);
                log.info("下载份额确认书, path:{},relativePath:{},shortName:{}",filepath,relativePath,shortName);
                byte[]  bytes=instance.read2Bytes(DfileConstants.VOL_CONFIRM_CONFIG,relativePath,shortName);
                PdfReader pdfReader = new PdfReader(bytes);
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                PdfStamper pdfStamper = new PdfStamper(pdfReader, bos);
                pdfStamper.close();
                bos.close();
                fos.write(bos.toByteArray());

            } catch (IOException e) {
                log.error("IOException：", e);
            } catch (DocumentException e) {
                log.error("DocumentException：", e);
            } catch (Exception e) {
                log.error("份额确认书读取失败：", e);
            }
        }
    }


    /**
     * 下载资金到账证明:
     * <AUTHOR>
     * @date 2020/4/8
     */
    @ResponseBody
    @RequestMapping("/downloaArrivalPdfFile.do")
    public void downloaArrivalPdfFile(HttpServletRequest request,HttpServletResponse response){
        String filepath = StringUtil.replaceNullStr(request.getParameter("filepath"));
        //filepath 说明：
        //接口：com.howbuy.tms.high.batch.facade.query.queryhighfundarrivalproof.QueryHighFundArrivalProofFacade 接口返回 pdfPath说明：

        //历史返回NFS存储路径：
        // /data/files/simu/middle/highfundarrivalproof/YNJF/PE0281/曹西坤_淄博昭漫股权投资合伙企业（有限合伙）_20241227_20241213000061.pdf
        //2025年3月25日 发现产线变更为：webDav存储路径
        //http://webdav-nfs01.inner.ehowbuy.com/simu/middle/highfundarrivalproof/YNJF/PE0328/伍先帆_淄博景良创业投资合伙企业（有限合伙）_20250207_202501240000010718.pdf

        //实际读取文件：
        if (StringUtils.isNotBlank(filepath)) {

            try {

                FileSdkPathInfo fileSdkPathInfo =
                        FileSdkUtil.convertUrlToFileSdkPathInfo(filepath,
                                DfileConstants.SIMU_MIDDLE_ARRIVAL_PROOF,
                                DfileConstants.SIMU_MIDDLE_ARRIVAL_PROOF_RELATIVE_PATH);
            log.info("根据filepath:{},解析HFile 对象：{}",filepath,JSON.toJSONString(fileSdkPathInfo));
            String filename = fileSdkPathInfo.getFileName();
            OutputStream fos = null;
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition", "attachment;fileName="+ new String(filename.getBytes("gb2312"), "ISO8859-1"));
            fos = response.getOutputStream();

            HFileService instance = HFileService.getInstance();
            byte[]  bytes=instance.read2Bytes(fileSdkPathInfo.getBusinessCode(),fileSdkPathInfo.getMiddlePath(),fileSdkPathInfo.getFileName());
            PdfReader pdfReader = new PdfReader(bytes);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            PdfStamper pdfStamper = new PdfStamper(pdfReader, bos);
            pdfStamper.close();
            bos.close();
            fos.write(bos.toByteArray());

            } catch (IOException e) {
                log.error("IOException：", e);
            } catch (DocumentException e) {
                log.error("DocumentException：", e);
            } catch (Exception e) {
                log.error("下载资金到账证明读取失败：", e);
            }
        }
    }


    /**
     * 查询中台-客户复购意向
     * @param request
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/queryCustRepurchaseProtocolFacade.do")
    public Map<String, Object> queryCustRepurchaseProtocolFacade(HttpServletRequest request, @RequestParam(value = "page", defaultValue = "1")Integer pageNo
                                                                                , @RequestParam(value = "rows", defaultValue = "10")Integer pageSize) throws Exception {
        // 设置查询参数
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("rows", 0);
        resultMap.put("total", 0);
        String pubcustno = request.getParameter("pubcustno");

        // 判断参数是否为空
        if(StringUtils.isBlank(pubcustno)){
            return resultMap;
        }

        try{
            QueryCustRepurchaseProtocolResposne dubboRsp = queryCustRepurchaseProtocol(pubcustno, pageNo, pageSize);
            String code=dubboRsp.getReturnCode();
            if("Z0000000".equals(code)){
                List<CustRepurchaseProtocolBean> resultList = dubboRsp.getCustRepurchaseProtocolList();
                List<CustRepurchaseProtocolBeanLocal> localList = new ArrayList();

                // 判断常量表中合规标识：true启用，false停用
                LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
                boolean roleCpFlag = false;
                if (cacheMap != null && !cacheMap.isEmpty()) {
                    roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
                }

                // 判断登录人员的角色中是否包括“合规人员”角色
                List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
                boolean isRoleCp = false;
                if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
                    isRoleCp = true;
                }
                Map<String, PageCmJjxx1Jgflb> jgjjBeanMap = JgjjCache.getInstance().getJgjjBeanMap();
                for(CustRepurchaseProtocolBean resultInfo : resultList){
                    if (isRoleCp && jgjjBeanMap.containsKey(resultInfo.getFundCode()) && ("21".equals(jgjjBeanMap.get(resultInfo.getFundCode()).getEjfl()) || "31".equals(jgjjBeanMap.get(resultInfo.getFundCode()).getEjfl()) || "41".equals(jgjjBeanMap.get(resultInfo.getFundCode()).getEjfl()) || "61".equals(jgjjBeanMap.get(resultInfo.getFundCode()).getEjfl()))) {
                        continue;
                    }

                    CustRepurchaseProtocolBeanLocal custRepurchaseProtocolBeanLocal = new CustRepurchaseProtocolBeanLocal();
                    BeanUtils.copyProperties(resultInfo, custRepurchaseProtocolBeanLocal);

                    JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(resultInfo.getFundCode(), false);
                    // 设置页面基金编码和基金名称
                    if (StringUtils.isNotBlank(resultInfo.getFundCode()) && jjxxInfo != null) {
                        custRepurchaseProtocolBeanLocal.setFundName(jjxxInfo.getJjjc());
                    }
                    localList.add(custRepurchaseProtocolBeanLocal);
                }
                resultMap.put("rows", localList);
                resultMap.put("total", localList.size());
            }
        } catch(Exception e) {
            log.error("中台接口调用异常queryCustRepurchaseProtocolFacade.execute(dubboREQ)："+e.getMessage(), e);
        }
        return resultMap;
    }

    @RequestMapping("/listPriFundDetail.do")
    public String listPriFundDetail(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
        String pCode = request.getParameter("pCode");
        String type = request.getParameter("type");
        request.setAttribute("pCode", pCode);
        request.setAttribute("type", type);
        List<Jjjlinfo> jjjlList = new ArrayList<>();
        String res = null;
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("fundCode", pCode);
        try {
            long startTime = System.currentTimeMillis();
            res = HttpUtils.get("http://data.howbuy.com/cgi/simu/v634/simuproductmanager.json", paramMap);
            long endTime = System.currentTimeMillis();
            MainLogUtils.httpCallOut("http://data.howbuy.com/cgi/simu/v634/simuproductmanager.json", CommonConstant.HTTP_SUCCESS_CODE, endTime - startTime);
            log.info("http://data.howbuy.com/cgi/simu/v634/simuproductmanager.json传参"+JSON.toJSON(paramMap)+"回参："+res);
        } catch (IOException e) {
            e.printStackTrace();
            log.error("http://data.howbuy.com/cgi/simu/v634/simuproductmanager.json异常！",e);
        }
        if(StringUtils.isNotBlank(res)){
            JSONObject jsonObject = JSON.parseObject(res);
            if("0000".equals(jsonObject.getString("code"))){
            	JSONObject bodyjson = jsonObject.getJSONObject("body");
            	JSONArray jsonArray  = (JSONArray)bodyjson.get("managerList");
            	if(jsonArray != null){
            		for(int i = 0;i< jsonArray.size();i++){
            			JSONObject jl = jsonArray.getJSONObject(i);
            			Jjjlinfo jjjl = new Jjjlinfo();
            			jjjl.setZhpf(jl.getString("zhpf"));
            			jjjl.setBjjrq(jl.getString("bjjrq"));
            			jjjl.setRqhb(jl.getString("rqhb"));
            			String rydm = jsonArray.getJSONObject(i).getString("rydm");
            			if(StringUtils.isNotBlank(rydm)){
            				Map<String, String> paramRydmMap = new HashMap<String, String>();
            				paramRydmMap.put("rydm", rydm);
            		        try {
                                long startTime = System.currentTimeMillis();
                                String resry = HttpUtils.get("http://data.howbuy.com/cgi/simu/v736/manager.json", paramRydmMap);
                                long endTime = System.currentTimeMillis();
                                MainLogUtils.httpCallOut("http://data.howbuy.com/cgi/simu/v736/manager.json", CommonConstant.HTTP_SUCCESS_CODE, endTime - startTime);
            		            log.info("http://data.howbuy.com/cgi/simu/v736/manager.json传参"+JSON.toJSON(paramRydmMap)+"回参："+resry);
            		            if(StringUtils.isNotBlank(resry)){
            		            	JSONObject jsonRyObject = JSON.parseObject(resry);
            		                if("0000".equals(jsonRyObject.getString("code"))){
            		                	JSONObject rybodyjson = jsonRyObject.getJSONObject("body");
            		                	jjjl.setManagerName(rybodyjson.getString("managerName"));
            		                	jjjl.setSummary(rybodyjson.getString("summary"));
            		                	jjjl.setCompanyName(rybodyjson.getString("companyName"));
            		                	jjjl.setJjjlly(rybodyjson.getString("jjjlly"));
            		                	jjjl.setSclx(rybodyjson.getString("sclx"));
            		                	jjjl.setCysj(rybodyjson.getString("cysj"));
            		                	jjjl.setRsmnx(rybodyjson.getString("rsmnx"));
            		                	jjjl.setGljjsl(rybodyjson.getString("gljjsl"));
            		                	jjjl.setCxjjsl(rybodyjson.getString("cxjjsl"));
            		                	jjjl.setCypjhb(rybodyjson.getString("cypjhb"));
            		                	jjjl.setHbjn(rybodyjson.getString("hbjn"));
            		                }
            		            }
            		        } catch (IOException e) {
            		            e.printStackTrace();
            		            log.error("http://data.howbuy.com/cgi/simu/v736/manager.json异常！",e);
            		        }
            			}
            			jjjlList.add(jjjl);
            		}
            	}
            }
        }
        request.setAttribute("jjjlList", jjjlList);
        return "/custinfo/priFundDetail";
    }

    /**
     * 加载私募基金详情数据方法
     *
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listPriFundDetail_json.do")
    public Map<String, Object> listPriFundDetail_json(HttpServletRequest request) throws Exception {
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>(1);
        String pCode = request.getParameter("pCode");
        // 如果查询条件（产品编号）不为空，则增加产品编号查询参数
        if (StringUtil.isNotNullStr(pCode)) {
            param.put("pcode", pCode);
        } else {
            param.put("pcode", null);
        }

        Map<String, Object> resultMap = new HashMap<String, Object>(1);
        Productinfo productinfo = productinfoService.getProductinfoDetail(param);
        resultMap.put("productinfo", productinfo);
        //查基金公司相关信息
        Jjgsinfo jjgs = new Jjgsinfo();
        if(StringUtils.isNotBlank(productinfo.getGlrm())){
        	String res = null;
            Map<String, String> paramMap = new HashMap<String, String>();
            paramMap.put("gsdm", productinfo.getGlrm());
            try {
                long startTime = System.currentTimeMillis();
                res = HttpUtils.get("http://data.howbuy.com/cgi/simu/v736/company.json", paramMap);
                long endTime = System.currentTimeMillis();
                MainLogUtils.httpCallOut("http://data.howbuy.com/cgi/simu/v736/company.json", CommonConstant.HTTP_SUCCESS_CODE, endTime - startTime);
                log.info("http://data.howbuy.com/cgi/simu/v736/company.json传参"+JSON.toJSON(paramMap)+"回参："+res);
            } catch (IOException e) {
                e.printStackTrace();
                log.error("http://data.howbuy.com/cgi/simu/v736/company.json异常！",e);
            }
            if(StringUtils.isNotBlank(res)){
                JSONObject jsonObject = JSON.parseObject(res);
                if("0000".equals(jsonObject.getString("code"))){
                	JSONObject bodyjson = jsonObject.getJSONObject("body");
                	jjgs.setGsqc(bodyjson.getString("gsqc"));
                	jjgs.setGsjc(bodyjson.getString("gsjc"));
                	jjgs.setSummary(bodyjson.getString("summary"));
                	JSONObject jbxxjson = bodyjson.getJSONObject("jbxx");
                	jjgs.setClrq(jbxxjson.getString("clrq"));
                	jjgs.setFrdb(jbxxjson.getString("frdb"));
                	jjgs.setZczb(jbxxjson.getString("zczb"));
                	jjgs.setQxjl(jbxxjson.getString("qxjl"));
                	jjgs.setGljj(jbxxjson.getString("gljj"));
                	jjgs.setCxjj(jbxxjson.getString("cxjj"));
                	jjgs.setBahm(jbxxjson.getString("bahm"));
                	jjgs.setGlgm(jbxxjson.getString("glgm"));
                }
            }
        }
        resultMap.put("jjgs", jjgs);
        
        return resultMap;
    }

    /**
     * @description:(高端产品交易记录列表)
     * @param conscustno
     * @param fundCode
     * @param mBusiCode
     * @return com.howbuy.crm.hb.domain.custinfo.CrmHighDealOrderShowVo
     * @author: shucheng.luo
     * @date: 2023/3/4 21:17
     * @since JDK 1.8
     */
    @RequestMapping("/highDealOrderTradeList.do")
    @ResponseBody
    public CrmHighDealOrderShowVo highDealOrderTradeList(String conscustno, String fundCode, String mBusiCode,
                                                         String orderStatus, String payStatus, String busiType) {

        // 判断 [登录人员的角色中包括“合规人员”角色] 或者 [合规标识：true启用]
        boolean isRoleCp = SessionUserManager.isCompositeRoleCp();
        // 获取监管基金map
        Map<String, PageCmJjxx1Jgflb> jgjjBeanMap = JgjjCache.getInstance().getJgjjBeanMap();

        // 通过Session获取产品广度信息
        String topcpdata = (String) SessionUserManager.getSession().getAttribute("topcpdata");

        DealOrderRequest orderRequest=new DealOrderRequest();
        orderRequest.setSearchDealNo(null);
        orderRequest.setSearchCustNo(conscustno);
        if (StringUtils.isNotBlank(mBusiCode)) {
            orderRequest.setMBusiCodeList(Lists.newArrayList(mBusiCode));
        }
        if (StringUtils.isNotBlank(orderStatus)) {
            orderRequest.setOrderStatus(orderStatus);
        }
        if (StringUtils.isNotBlank(payStatus)) {
            orderRequest.setPayStatus(payStatus);
        }
        if (StringUtils.isNotBlank(busiType)) {
            orderRequest.setBusiType(busiType);
        }
        orderRequest.setSearchProdCode(fundCode);
        List<CrmDealOrderBeanVo> dealList = crmHighDealOrderService.getDealOrderList(orderRequest);
        log.info("获取交易记录: {}", JSON.toJSONString(dealList));

        //过滤 是否显示
        dealList = dealList.stream().filter(dealOrder -> {
            if (Objects.isNull(dealOrder)) {
                log.error("交易记录数据, 交易数据 订单不存在！");
                return false;
            }
            // 后续判断 优先使用productCode，如果productCode为空，则使用 motherProductCode(母基金代码)
            String useProductCode = dealOrder.getProductCode();
            if (StringUtils.isBlank(useProductCode)) {
                useProductCode = dealOrder.getMotherProductCode();
            }

            if (StringUtils.isBlank(useProductCode)) {
                // 如果productCode 和 motherProductCode(母基金代码)都为空，则过滤掉该条记录
                log.error("交易记录数据, 交易数据：{}， 基金代码不存在！", JSON.toJSONString(dealOrder));
                return false;
            }
            JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(useProductCode, false);
            if (jjxxInfo == null) {
                log.error("交易记录数据，基金代码：{} 不存在！", useProductCode);
                return false;
            }
            boolean stayToDisplay = HighDealOrderSupport.stayToDisplay(jjxxInfo, topcpdata, isRoleCp, jgjjBeanMap);
            //不展示， 日志给予提示
            if (!stayToDisplay) {
                log.info("交易记录数据，基金代码：{} 不展示！.判断基于字段，topcpdata:{},isRoleCp:{}", useProductCode, topcpdata, isRoleCp);
            }
            return stayToDisplay;
        }).collect(Collectors.toList());

        // 2.拼接底部展示信息
        String bottomTip = generateBottomTip(mBusiCode, dealList);

        CrmHighDealOrderShowVo showVo = new CrmHighDealOrderShowVo();
        showVo.setDealOrderVoList(dealList);
        showVo.setResultStr(bottomTip);
        return showVo;

//        return highDealOrderSupport.queryHighDealOrderList(conscustno, fundCode, mBusiCode);
    }


    /**
     * @description:(生成底部展示提示语)
     * @param searchMBusiCode
     * @param dealOrderVoList
     * @return java.lang.String
     * @author: shucheng.luo
     * @date: 2023/3/4 17:43
     * @since JDK 1.8
     */
    private String generateBottomTip(String searchMBusiCode, List<CrmDealOrderBeanVo> dealOrderVoList) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(searchMBusiCode)) {
            //选择交易类型的情况
            sb.append("该交易类型共" + dealOrderVoList.size() + "条记录，");
            BigDecimal totalAppAmt = new BigDecimal("0.00");
            BigDecimal totalAppFee = new BigDecimal("0.00");
            BigDecimal totalAckAmt = new BigDecimal("0.00");
            BigDecimal totalAckFee = new BigDecimal("0.00");
            BigDecimal totalAckVol = new BigDecimal("0.00");

            for (CrmDealOrderBeanVo crmDealOrderBeanVo : dealOrderVoList) {
                totalAppAmt = totalAppAmt.add(crmDealOrderBeanVo.getAppAmt());
                if(crmDealOrderBeanVo.getPrefee() != null){
                    totalAppFee = totalAppFee.add(crmDealOrderBeanVo.getPrefee());
                }
                totalAckAmt = totalAckAmt.add(crmDealOrderBeanVo.getAckAmt());
                totalAckFee = totalAckFee.add(crmDealOrderBeanVo.getFee());
                totalAckVol = totalAckVol.add(crmDealOrderBeanVo.getAckVol());
            }
            sb.append(String.format("累计申请金额：%s，申请手续费：%s，确认金额：%s，确认手续费：%s，确认份额：%s",
                    totalAppAmt.toPlainString(),
                    totalAppFee.toPlainString(),
                    totalAckAmt.toPlainString(),
                    totalAckFee.toPlainString(),
                    totalAckVol.toPlainString()));
        } else {
            //选择全部的情况
            sb.append("可根据筛选的交易类型，汇总显示对应交易情况数据!");
        }
        return sb.toString();
    }

    /**
     * @description:(客户交易详细)
     * @param conscustno	投顾客户号
     * @param dealNo	    中台订单号
     * @param disCode	    分销代码
     * @return java.lang.String
     * @author: shucheng.luo
     * @date: 2023/3/4 21:12
     * @since JDK 1.8
     */
    @RequestMapping("/highDealOrderDetailTradeNew.do")
    @ResponseBody
    public List<HbDisplaySubDealSubVo> highDealOrderDetailTradeNew(String conscustno, String dealNo, String disCode) {
        return highDealOrderSupport.queryHighDealOrderByDealNo(conscustno, dealNo, disCode);
    }
    
    /**
     * 客户交易详细
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
    @RequestMapping("/detailTradeNew.do")
    public String detailTradeNew(HttpServletRequest request) throws UnsupportedEncodingException{

        String custno = request.getParameter("consCustNo");
        String bankName = java.net.URLDecoder.decode(request.getParameter("bankName"),"UTF-8");
        String bankAcct = java.net.URLDecoder.decode(request.getParameter("bankAcct"),"UTF-8");
        String mBusiCode = java.net.URLDecoder.decode(request.getParameter("mBusiCode"),"UTF-8");
        String ackVol = request.getParameter("ackVol");
        String ackAmt = request.getParameter("ackAmt");
        String taTradeDt = request.getParameter("taTradeDt");
        String productAttr = java.net.URLDecoder.decode(request.getParameter("productAttr"),"UTF-8");
        String nav = request.getParameter("nav");
        String fee = request.getParameter("fee");
        String clrq = request.getParameter("clrq");
        String busiTypeName = java.net.URLDecoder.decode(request.getParameter("busiTypeName"),"UTF-8");
        Map<String, String> param = new HashMap<>(1);
        Map<String,Object> resultMap = new HashMap<>(16);
        resultMap.put("bankName", bankName);
        resultMap.put("bankAcct", bankAcct);
        resultMap.put("mBusiCode", mBusiCode);

        if(StringUtil.isNotNullStr(ackVol)){
            resultMap.put("ackVol",new BigDecimal(ackVol));
        }
        if(StringUtil.isNotNullStr(ackAmt)){
            resultMap.put("ackAmt", new BigDecimal(ackAmt));
        }
        if(StringUtil.isNotNullStr(nav)){
            BigDecimal tradenav = new BigDecimal(nav);
            resultMap.put("nav", tradenav);
        }
        if(StringUtil.isNotNullStr(fee)){
            BigDecimal tradefee = new BigDecimal(fee);
            resultMap.put("fee", tradefee);
        }
        resultMap.put("taTradeDt", taTradeDt);
        resultMap.put("productAttr", productAttr);
        resultMap.put("clrq", clrq);
        resultMap.put("busiTypeName", busiTypeName);
        param.put("conscustno", custno);
        Conscust conscust = conscustService.getConscust(custno);
        if(conscust != null){
            conscust.setIdtype(IdTypeUtil.getName(conscust.getInvsttype(), conscust.getIdtype()));
            if(StringUtil.isNotNullStr(conscust.getIdnoCipher())){
            	conscust.setIdno(decryptSingleFacade.decrypt(conscust.getIdnoCipher()).getCodecText());
            }else{
            	conscust.setIdno("");
            }
            conscust.setGender(ConstantCache.getInstance().getVal("sex", conscust.getGender()));
            conscust.setProvcode(ConstantCache.getInstance().getProvCityMap().get(conscust.getProvcode()));
            conscust.setCitycode(ConstantCache.getInstance().getProvCityMap().get(conscust.getCitycode()));
            param.put("tradedt", taTradeDt);
            Map<String,String> conmgrmap = conscustService.getConsInfoByCustNoAndTddt(param);
            if(conmgrmap != null){
                conscust.setConscode(Util.ObjectToString(conmgrmap.get("CONSCODE")));
                resultMap.put("seniormgrcode",Util.ObjectToString(conmgrmap.get("SENIORMGRCODE")));
                resultMap.put("consname",ConsOrgCache.getInstance().getAllConsMap().get(Util.ObjectToString(conmgrmap.get("CONSCODE"))));
            }
            if(StringUtils.isNotBlank(conscust.getNewsourceno())){
                resultMap.put("source",conscust.getNewsourcename());
                resultMap.put("subsource",conscust.getNewsubsourcename());
                resultMap.put("subsourcetype",conscust.getNewsubsourcetypename());
            }else{
                Map<String, String> sourceNameMap = CmCustSourceCache.getInstance().getSourceNameMap();
                resultMap.put("source",sourceNameMap.get(conscust.getSource()));
                resultMap.put("subsource",sourceNameMap.get(conscust.getSubsource()));
                resultMap.put("subsourcetype",sourceNameMap.get(conscust.getSubsourcetype()));
            }
            conscust.setMobile(conscust.getMobileMask());
            if(StringUtil.isNotNullStr(conscust.getIdnoCipher())){
            	conscust.setIdno(decryptSingleFacade.decrypt(conscust.getIdnoCipher()).getCodecText());
            }
            resultMap.put("conscust",conscust);
        }
        request.setAttribute("conscustno",request.getParameter("consCustNo"));
        request.setAttribute("map",resultMap);
        return "/custinfo/detailFundTradeNew";
    }

    @RequestMapping("/bxBalanceDetail")
    @ResponseBody
    public Object bxBalanceDetail(String conscustno){
        List<BxBalanceDetail> bxBalanceDetails = cmBxPrebookBuyinfoService.listBxBalance(conscustno);
        ConstantCache constantCache = ConstantCache.getInstance();
        for(BxBalanceDetail bxBalanceDetail : bxBalanceDetails){
            bxBalanceDetail.setCurrency(constantCache.getVal("currencys",bxBalanceDetail.getCurrency()));
            bxBalanceDetail.setProdtype(constantCache.getVal("insurprodtype",bxBalanceDetail.getProdtype()));
            bxBalanceDetail.setExpirestat(constantCache.getVal("insurexpirestat",bxBalanceDetail.getExpirestat()));
        }
        return bxBalanceDetails;
    }

    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    @Data
    public class CRMBalanceBean {
        private String disCode;
        private List<String> disCodeList;
        private String productCode;
        private String subProductCode;
        private String productName;
        private String productType;
        private String productSubType;
        private BigDecimal balanceVol;
        private BigDecimal unconfirmedVol;
        private BigDecimal unconfirmedAmt;
        private String currency;
        private BigDecimal nav;
        private String navDt;
        private String navDivFlag = "0";
        private BigDecimal marketValue;
        private BigDecimal currencyMarketValue;
        private String scaleType;
        private String hkSaleFlag;
        private String StageEstablishFlag;
        private String fractionateCallFlag;
        private String fundCXQXStr;
        private BigDecimal netBuyAmount;
        private BigDecimal currencyNetBuyAmount;
        private BigDecimal cashCollection;
        private BigDecimal currencyCashCollection;
        private BigDecimal paidInAmt;
        private String incomeDt;
        private String incomeCalStat;
        private BigDecimal currentAsset;
        private BigDecimal currentAssetCurrency;
        private BigDecimal dailyAsset;
        private BigDecimal dailyAssetCurrency;
        private BigDecimal accumIncome;
        private BigDecimal accumIncomeRmb;
        private BigDecimal accumRealizedIncome;
        private BigDecimal accumRealizedIncomeRmb;
        private BigDecimal balanceCost;
        private BigDecimal balanceCostCurrency;
        private String rePurchaseFlag;
        private String benchmark;
        private String benchmarkType;
        private String valueDate;
        private String dueDate;
        private String standardFixedIncomeFlag;
        private String investmentHorizon;
        private String cooperation;
        private String crisisFlag;
        private BigDecimal yieldIncome;
        private BigDecimal yieldRate;
        private String yieldIncomeDt;
        private String hwSaleFlag;
        private String regDt;
        private String oneStepType;
        private String twoStepType;
        private String secondStepType;
        private String productSaleType;
        private String naProductFeeType;
        private BigDecimal receivManageFee;
        private BigDecimal receivPreformFee;
        private BigDecimal currencyMarketValueExFee;
        private BigDecimal marketValueExFee;
        private BigDecimal balanceIncomeNew;
        private BigDecimal balanceIncomeNewRmb;
        private BigDecimal accumIncomeNew;
        private BigDecimal accumIncomeNewRmb;
        private BigDecimal balanceFactor;
        private String convertFinish;
        private String balanceFactorDate;

        // 分红方式. 0-红利再投，1-现金红利，9-不分红
        private String divMode;

        /**
         * 待投金额（当前币种）
         */
        private BigDecimal currencyUnPaidInAmt;
        /**
         * 待投金额（人民币）
         */
        private BigDecimal unPaidInAmt;
        /**
         * 4.是否为千禧年产品 0-否、1-是qianXiFlag
         */
        private String qianXiFlag;
    }

    @Deprecated
    private List<QueryDealOrderListResponse.DealOrderBean> queryDealOrderList(HttpServletRequest request, CrmCustInvestTypeEnum investTypeEnum, String hboneno) {
        QueryDealOrderListRequest req = new QueryDealOrderListRequest();
        req.setOutletCode("A20150120");
        req.setOperIp(getIpAddr(request));
        req.setTxChannel("1");
        req.setDataTrack("crmsale");
        req.setPageSize(500);
        if (StringUtil.isNullStr(hboneno)) {
            return new ArrayList();
        }
        req.setHbOneNo(hboneno);
        List<String> fullDisCodeList=DisCodeUtil.getFullBusiDisCodeList(investTypeEnum).stream().map(DisCodeEnum::getCode).collect(Collectors.toList());
        req.setDisCodeList(fullDisCodeList);
        log.info("queryDealOrderListFacade.execute入参"+JSON.toJSON(req));
        List<QueryDealOrderListResponse.DealOrderBean> dealOrderlist = queryDealOrderListFacade.execute(req).getDealOrderList();
        log.info("queryDealOrderListFacade.execute返回"+JSON.toJSON(dealOrderlist));
        return dealOrderlist;
    }

    private QueryDealOrderResponse queryDealOrderFacade(HttpServletRequest request, String custno, String hboneno, String dealNo) {
        QueryDealOrderRequest req = new QueryDealOrderRequest();
        req.setDealNo(dealNo);
        req.setOutletCode("A20150120");
        req.setOperIp(getIpAddr(request));
        req.setTxChannel("1");
        req.setDataTrack("crmsale");
        req.setPageSize(500);
        if (StringUtil.isNullStr(hboneno)) {
            return null;
        }
        req.setHbOneNo(hboneno);
        req.setDisCode(conscustService.getDiscodeByConsCustno(custno));
        log.info("queryDealOrderFacade.execute入参"+JSON.toJSON(req));
        QueryDealOrderResponse response = queryDealOrderFacade.execute(req);
        log.info("queryDealOrderFacade.execute返回"+JSON.toJSON(response));
        return response;
    }

    private QueryCustRepurchaseProtocolResposne queryCustRepurchaseProtocol(String pubcustno, int pageNo, int pageSize) {
        // 调用中台接口
        QueryCustRepurchaseProtocolRequest dubboReq = new QueryCustRepurchaseProtocolRequest();
        dubboReq.setTxAcctNo(pubcustno);
        dubboReq.setDisCode(conscustService.getDiscodeByPubCustno(pubcustno));
        dubboReq.setOutletCode("A20140301");
        dubboReq.setOperIp("**************");
        dubboReq.setTxChannel("5");
        dubboReq.setPageNo(pageNo);
        dubboReq.setPageSize(pageSize);

        // 打印请求数据
        log.info(dubboReq.getClass().getSimpleName()+"|"+JSON.toJSON(dubboReq));
        QueryCustRepurchaseProtocolResposne dubboRsp = queryCustRepurchaseProtocolFacade.execute(dubboReq);
        // 打印响应数据
        log.info(dubboRsp.getClass().getSimpleName()+"|"+JSON.toJSON(dubboRsp));
        return dubboRsp;
    }



    /**
     * 客户复购意向本地实体类
     * <AUTHOR>
     *
     */
    class CustRepurchaseProtocolBeanLocal implements Serializable {
        private static final long serialVersionUID = 24847912626699338L;

        private String txAcctNo;

        private String repurchaseType;

        private String fundCode;

        private String fundName;

        private String repurchaseProtocolNo;

        private BigDecimal repurchaseVol;

        private String endModifyDt;

        private String canModify;

        private String productRepurchaseFlag;

        private BigDecimal balanceVol;

        private Date updateDtm;

        public String getTxAcctNo() {
            return txAcctNo;
        }

        public void setTxAcctNo(String txAcctNo) {
            this.txAcctNo = txAcctNo;
        }

        public String getRepurchaseType() {
            return repurchaseType;
        }

        public void setRepurchaseType(String repurchaseType) {
            this.repurchaseType = repurchaseType;
        }

        public String getFundCode() {
            return fundCode;
        }

        public void setFundCode(String fundCode) {
            this.fundCode = fundCode;
        }

        public String getFundName() {
            return fundName;
        }

        public void setFundName(String fundName) {
            this.fundName = fundName;
        }

        public String getRepurchaseProtocolNo() {
            return repurchaseProtocolNo;
        }

        public void setRepurchaseProtocolNo(String repurchaseProtocolNo) {
            this.repurchaseProtocolNo = repurchaseProtocolNo;
        }

        public BigDecimal getRepurchaseVol() {
            return repurchaseVol;
        }

        public void setRepurchaseVol(BigDecimal repurchaseVol) {
            this.repurchaseVol = repurchaseVol;
        }

        public String getEndModifyDt() {
            return endModifyDt;
        }

        public void setEndModifyDt(String endModifyDt) {
            this.endModifyDt = endModifyDt;
        }

        public String getCanModify() {
            return canModify;
        }

        public void setCanModify(String canModify) {
            this.canModify = canModify;
        }

        public String getProductRepurchaseFlag() {
            return productRepurchaseFlag;
        }

        public void setProductRepurchaseFlag(String productRepurchaseFlag) {
            this.productRepurchaseFlag = productRepurchaseFlag;
        }

        public BigDecimal getBalanceVol() {
            return balanceVol;
        }

        public void setBalanceVol(BigDecimal balanceVol) {
            this.balanceVol = balanceVol;
        }

        public Date getUpdateDtm() {
            return updateDtm;
        }

        public void setUpdateDtm(Date updateDtm) {
            this.updateDtm = updateDtm;
        }
    }
    /**
     * 直销高端产品持仓:
    * <AUTHOR>
    * @date 2022/4/26
    */
    @ResponseBody
    @RequestMapping("/queryZxAcctBalanceList.do")
    public Map<String, Object> queryZxAcctBalanceList(String conscustno, HttpServletRequest request) throws Exception {
    	ZxPrivateTradeFundVo vo = new ZxPrivateTradeFundVo();
    	vo.setConscustno(conscustno);
        // 查询CRM的直销数据，过滤掉[香港产品]。（香港产品数据都已 迁移到 海外中台，该页面不再展示香港产品的数据）
    	PageData<ZxPrivateTradeFund> pageData = zxPrivateTradeFundService.listZxPrivateTradeFundWithoutXgByPage(vo);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<ZxPrivateTradeFund> listdata= pageData.getListData();
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        for(ZxPrivateTradeFund info : listdata){
            JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
            info.setConsname(consOrgCache.getAllUserMap().get(info.getConscode()));
            if(jjxx1 != null  ){
                info.setFundname(jjxx1.getJjjc());
                if(StringUtil.isNotNullStr(jjxx1.getHbzl())){
                	info.setCurrency(ConstantCache.getInstance().getVal("currencys", jjxx1.getHbzl()));
                }
            }
            if(info.getBalancevol() != null && info.getNav() != null){
            	info.setBalanceamt(info.getBalancevol().multiply(info.getNav()));
            	if(info.getBalancefactor() != null){
            		info.setBalanceamt(info.getBalanceamt().add(info.getBalancefactor()));
            	}
            }
        }
        resultMap.put("rows", listdata);
        return resultMap;
    }
    
    /**
     * 直销高端产品交易:
    * <AUTHOR>
    * @date 2022/4/26
    */
    @ResponseBody
    @RequestMapping("/queryZxAcctBalanceTradeList.do")
    public Map<String, Object> queryZxAcctBalanceTradeList(HttpServletRequest request) throws Exception {
    	ZxPrivateTradeFundVo vo = new ZxPrivateTradeFundVo();
    	String conscustno = request.getParameter("conscustno");
    	String fundcode = request.getParameter("fundcode");
    	String busicode = request.getParameter("busicode");
    	if(StringUtil.isNotNullStr(conscustno)){
    		vo.setConscustno(conscustno);
    	}
    	if(StringUtil.isNotNullStr(fundcode)){
    		vo.setFundcode(fundcode);
    	}
    	if(StringUtil.isNotNullStr(busicode)){
    		vo.setBusicode(busicode);
    	}
        // 查询CRM的直销数据，过滤掉[香港产品]。（香港产品数据都已 迁移到 海外中台，该页面不再展示香港产品的数据）
    	PageData<Custprivatefundtrade> pageData = custprivatefundtradeService.listZxPrivateTradedWithoutXgByPage(vo);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<Custprivatefundtrade> listdata= pageData.getListData();
        for(Custprivatefundtrade info : listdata){
            JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
            if(jjxx1 != null  ){
                info.setFundName(jjxx1.getJjjc());
            }
            info.setBusivar(ConstantCache.getInstance().getVal("zxBusiCode", info.getBusicode()));

            // 根据基金代码，调用私募接口拼接字段：母基金代码、是否分期成立
            String[] fundcodes = new String[]{info.getFundcode()};
            List<SmcccpJbxxDto> listDto = smccProductJbxxService.getSmccFromCacheByCodes(fundcodes);
            if (CollectionUtils.isNotEmpty(listDto)) {
                String mjjdm = listDto.get(0).getMjjdm();
                String sffqcl = listDto.get(0).getSffqcl();
                info.setMjjdm(mjjdm);
                info.setSffqcl(sffqcl);
            }
        }
        resultMap.put("rows", listdata);
        return resultMap;
    }


}
