package com.howbuy.crm.hb.web.controller.custinfo;

import com.google.common.collect.Maps;
import com.howbuy.crm.consultant.request.TransferRepeatCustRequest;
import com.howbuy.crm.consultant.response.TransferInsertCustResponse;
import com.howbuy.crm.consultant.response.TransferRepeatCustResponse;
import com.howbuy.crm.consultant.service.TransferRepeatCustService;
import com.howbuy.crm.hb.domain.custinfo.CmTransfLog;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.custinfo.ConscustOperation;
import com.howbuy.crm.hb.service.custinfo.*;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.util.RMIConstant;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * @version 1.0
 * @Description: 客户管理 Controller
 */
@Slf4j
@Controller
@RequestMapping(value = "/potentialcust")
public class CmPotentialCustController extends BaseController {

    @Autowired
    private ConscustService conscustService;

    @Autowired
    private ConscustIcService conscustIcService;

    @Autowired
    private CmCustfamilyService cmCustfamilyService;

    @Autowired
    private ConscustOperationService conscustOperationService;

    @Autowired
    private CmTransfLogService cmTransfLogService;

    @Autowired
    private TransferRepeatCustService transferRepeatCustService;

    //申请划转
    private String APPLAY_TRANSFER = "1";

    @RequestMapping("/listAuditPotentialClient")
    public String listAuditPotentialClient(HttpServletRequest request) {
    	User userlogin = (User)request.getSession().getAttribute("loginUser");
		String outletcode = ConsOrgCache.getInstance().getUser2OutletMap().get(userlogin.getUserId());
		String tearmcode = ConsOrgCache.getInstance().getCons2TeamMap().get(userlogin.getUserId());
		String userLevel="";	// 登陆用户级别字段：0代表CEO，1代表部门或团队，2代表投顾
		String groupOrgCode = "";	// 登陆用户所属的部门编码
		String loginOrgCode = StringUtils.isNoneBlank(tearmcode) ? tearmcode : outletcode;	// 登陆用户所属的部门编码
		String topgd = (String)request.getSession().getAttribute("topgddata");
    	if(StaticVar.DATARANGE_GD_ALL.equals(topgd)||StaticVar.DATARANGE_GD_ALL_NOWFP.equals(topgd)){
			userLevel="0";
			groupOrgCode = "0";
		}else if(StaticVar.DATARANGE_GD_OUTLET.equals(topgd)){
			userLevel="1";
			groupOrgCode = outletcode;
		}else if(StaticVar.DATARANGE_GD_TEARM.equals(topgd)){
			userLevel="1";
			groupOrgCode = tearmcode;
		}else{
			userLevel="2";
			groupOrgCode = loginOrgCode;
		}
    	
    	request.getSession().setAttribute("userLevel", userLevel);
		request.getSession().setAttribute("groupOrgCode", groupOrgCode);
        return "/custinfo/iccustAuditList";
    }

    //投顾客户查询
    @ResponseBody
    @RequestMapping("/potentialclientList")
    public Map<String, Object> potentialclientList(HttpServletRequest request) throws Exception{
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();
        String userLevel = (String)request.getSession().getAttribute("userLevel");
		String groupOrgCode = (String)request.getSession().getAttribute("groupOrgCode");
		// 查询条件中投顾级别判断，投顾单独分一组，其他归为一组（包括CEO、部门组和团队组）
		param.put("userLevel", userLevel);
        if(StringUtils.isNotBlank(userLevel) && ("0".equals(userLevel) || "1".equals(userLevel))){
			// 如果客户对应部门（机构编码）不为空，则增加客户组机构编码查询参数
			if(StringUtils.isNotBlank(groupOrgCode)){
				param.put("orgCode2", groupOrgCode);
			}else{
				param.put("orgCode2", null);
			}
		}else{
			param.put("orgCode2", null);
		}

//        User userlogin = (User) request.getSession().getAttribute("loginUser");
        String loginUserId=getLoginUserId(request);
        param.put("loginUser", loginUserId);

        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        if(StringUtil.isNotNullStr(consCode)){
            param.put("creator", consCode);
        }else{
            param.put("creators", Util.getSubQueryByOrgCode(orgCode));
        }
        param.put("conscuststatus", StaticVar.CONSCUST_STATUS_NORMAL);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        PageData<ConscustOperation> pd = conscustIcService.listConscustOperationByPage(param);
        request.getSession().setAttribute("searchMap", param);
        resultMap.put("total", pd.getPageBean().getTotalNum());
        List<ConscustOperation> listtemp = pd.getListData();
        for(ConscustOperation v:listtemp){

            //v.setMobile(Util.getEncryptValue(request, StaticVar.ENCRYPT_MOBILE, v.getMobile(), null, null));

            String sourcenew = v.getSource();
            v.setCitycode(ConstantCache.getInstance().getProvCityMap().get(v.getCitycode()));

            ConsOrgCache orgcache = ConsOrgCache.getInstance();
            //String i = orgcache.getCons2OutletMap().get(v.getCreator());

            v.setOutletname(ConsOrgCache.getInstance().getAllOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(v.getCreator())));

            String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(v.getCreator()));
            if("0".equals(uporgcode)){
                v.setUporgname(v.getOutletname());
            }else{
                v.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
            }

            String checkflag = v.getCheckflag();
            //客户状态
            String custStatus=getCustStatus(v.getConscustno(), checkflag,v.getOperationid());
            if(loginUserId.equals(v.getCreator()) && "潜客".equals(custStatus)){
                //mobile的  canSeeMobileInfo :当 客户状态 = 潜客（当前时间点）且 当前用户 = 创建者 时，可查看客户手机明文
                v.setCanSeeMobileInfo(true);
            }

            if(StringUtil.isNotNullStr(v.getCreator())){

                if(loginUserId.equals(v.getCreator())){
                    v.setCanedit("1");
                }
                v.setCreator(ConsOrgCache.getInstance().getAllConsMap().get(v.getCreator()));
            }

            v.setOpstatus(ConstantCache.getInstance().getVal("transfopstatus", v.getOpstatus()));
            v.setConscustlvl(ConstantCache.getInstance().getVal("custlevel", v.getConscustlvl()));

            // 生成客户状态
            v.setCustStatus(custStatus);

            v.setCheckflag(ConstantCache.getInstance().getVal("potentialclientcheckflag", checkflag));

            if(StringUtil.isNotNullStr(v.getSource())){
                v.setSource(v.getNewsourcename());
            }

            if(StringUtil.isNotNullStr(v.getCustSourceRemarkno()) && "B".equals(sourcenew)){
                param.put("conscustno", StringUtil.replaceNull(v.getCustSourceRemarkno()));
                Conscust conscust = conscustService.getConscust(StringUtil.replaceNull(v.getCustSourceRemarkno()));
                if(conscust != null){
                    StringBuffer sb = new StringBuffer();
                    int familycount = cmCustfamilyService.getIsHasFamilyCustCount(param);
                    sb.append("客户来源："+conscust.getNewsourcename());
                    sb.append("\n\n");
                    sb.append("最近购买高净值日期："+StringUtil.replaceNullStr(conscust.getLatesttradedt()));
                    sb.append("\n\n");
                    if(familycount > 0){
                        sb.append("是否有家庭账户：是");
                    }else{
                        sb.append("是否有家庭账户：否");
                    }
                    v.setCustSourceRemarkno(sb.toString());
                }

            }else if(StringUtil.isNullStr(v.getCustSourceRemarkno()) && "B".equals(sourcenew)){
                StringBuffer sb = new StringBuffer();
                sb.append("没找到对应客户!");
                v.setCustSourceRemarkno(sb.toString());
            }
        }
        resultMap.put("rows", listtemp);
        return resultMap;
    }



    /**
     * 客户状态
     * @param conscustno
     * @param checkflag
     */
    private String getCustStatus(String conscustno, String checkflag,String operationid){
        if("2".equals(checkflag)){ //审核不通过
            Map<String,String> paramMap= Maps.newHashMap();
            paramMap.put("operationid", operationid);
            List<CmTransfLog> cmTransfLogs = cmTransfLogService.listCmTransfLog(paramMap);
            if(CollectionUtils.isEmpty(cmTransfLogs)){
                return "潜客";
            }

            //全部都为潜客，则显示“潜客”   潜在/成交客户 0-潜在客户；1-成交客户'
            long potentialCount=cmTransfLogs.stream().filter(bean->"0".equals(bean.getCuststatus())).count();
            if(cmTransfLogs.size()==potentialCount){
                return "潜客";
            }

            //其中有一个或多个为成交，则显示“成交”
            long dealCount=cmTransfLogs.stream().filter(bean->"1".equals(bean.getCuststatus())).count();
            if(dealCount>0){
                return "成交";
            }

        }else{
          return conscustService.hasGdcjlabel(conscustno)?"成交":"潜客";
        }
        return  "潜客";
    }

    //删除客户
    @ResponseBody
    @RequestMapping("/delPotentialclient")
    public String delPotentialclient(HttpServletRequest request) throws Exception{
        String result = "";
        String conscustno = request.getParameter("conscustno");
        String operationid = request.getParameter("operationid");
        if(StringUtil.isNotNullStr(conscustno)){
            //首先查看该客户是否还持有基金
            Map<String,String> param = new HashMap<String,String>();
            param.put("conscustno", conscustno);
            conscustIcService.deleteConscustIcByConscustno(param);
            conscustOperationService.delConscustOperation(operationid);
            result = "success";
        }else{
            result = "paramError";
        }
        return result;
    }

    //删除客户
    @ResponseBody
    @RequestMapping("/checkCmTransf")
    public Map<String, Object> checkCmTransf(HttpServletRequest request) throws Exception{

        Map<String, Object> resultMap = new HashMap<String, Object>();
        String result = "success";
        Map param = new HashMap(2);
        String operationid = request.getParameter("operationid");
        param.put("operationid", operationid);
        List<CmTransfLog> cmTransfLogs = cmTransfLogService.listCmTransfLog(param);
        if(cmTransfLogs != null && cmTransfLogs.size() == 1){
            User userlogin = (User)request.getSession().getAttribute("loginUser");
            if(userlogin.getUserId().equals(cmTransfLogs.get(0).getCreator())){
                Conscust conscust = conscustService.getConscust(StringUtil.replaceNull(cmTransfLogs.get(0).getConscustno()));
                if(conscust != null){
                    resultMap.put("conscustno",conscust.getConscustno());
                }else{
                    result = "nocust";
                }
            }else{
                result = "notcreater";
            }
        }else{
            result = "error";
        }
        resultMap.put("result",result);

        return resultMap;
    }

    /**
     * @description 二次校验确认是否允许处理
     * <AUTHOR>
     * @updateTime 2021/8/31 16:03
     * @throws
     */
    @ResponseBody
    @RequestMapping("/checkCmTransfLog")
    public Map<String, Object> checkCmTransfLog(HttpServletRequest request) {
        String status = "false";
        Map<String, Object> resultTaskDisposeMap = new HashMap<String, Object>(2);
        User user = (User) request.getSession().getAttribute("loginUser");

        TransferRepeatCustRequest transfer = new TransferRepeatCustRequest();
        String[] conscustnos = request.getParameterValues("custData[]");
        String operationid = request.getParameter("operationid");
        List<String> conscustnolist = Arrays.asList(conscustnos);

        transfer.setConscode(user.getUserId());
        transfer.setOperationid(operationid);
        transfer.setConscustnolist(conscustnolist);
        TransferRepeatCustResponse transfresponse = transferRepeatCustService.checktransferRepeatCust(transfer);

        if(RMIConstant.RMISucc.equals(transfresponse.getReturnCode())){

            List<String> highConsCustList = transfresponse.getHighConsCustList();
            List<String> highZeroConsCustList = transfresponse.getHighZeroConsCustList();
            List<String> samelurkingConsCustList = transfresponse.getSamelurkingConsCustList();
            List<String> diflurkingConsCustList = transfresponse.getDiflurkingConsCustList();
            List<String> same6lurkingConsCustList = transfresponse.getSame6lurkingConsCustList();
            List<String> dif6lurkingConsCustList = transfresponse.getDif6lurkingConsCustList();
            List<String> transferCustList = transfresponse.getTransferCustList();

            if(checkConsCustList(highConsCustList) || checkConsCustList(highZeroConsCustList) || checkConsCustList(samelurkingConsCustList)
                    || checkConsCustList(diflurkingConsCustList) || checkConsCustList(same6lurkingConsCustList)
                    || checkConsCustList(dif6lurkingConsCustList)){
                resultTaskDisposeMap.put("highConsCustList",highConsCustList);
                resultTaskDisposeMap.put("highZeroConsCustList",highZeroConsCustList);
                resultTaskDisposeMap.put("samelurkingConsCustList",samelurkingConsCustList);
                resultTaskDisposeMap.put("diflurkingConsCustList",diflurkingConsCustList);
                resultTaskDisposeMap.put("same6lurkingConsCustList",same6lurkingConsCustList);
                resultTaskDisposeMap.put("dif6lurkingConsCustList",dif6lurkingConsCustList);
                resultTaskDisposeMap.put("flag","hightrans");
            }else if(checkConsCustList(transferCustList)){
                resultTaskDisposeMap.put("transferCustList",transferCustList);
                resultTaskDisposeMap.put("flag","transfer");
            }else{
                resultTaskDisposeMap.put("flag","success");
            }
        }else{
            resultTaskDisposeMap.put("transfresponse",transfresponse);
            resultTaskDisposeMap.put("flag","fail");
        }
        //整合重复客户提示

        return resultTaskDisposeMap;
    }

    /**
     * 插入重复客户划转信息
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/modifyCmTransfLog")
    public Map<String, Object> modifyCmTransfLog(HttpServletRequest request) {
        String status = "false";
        Map<String, Object> resultTaskDisposeMap = new HashMap<String, Object>(2);
        User user = (User) request.getSession().getAttribute("loginUser");

        TransferRepeatCustRequest transfer = new TransferRepeatCustRequest();
        String[] conscustnos = request.getParameterValues("custData[]");
        String operationid = request.getParameter("operationid");
        String[] check = request.getParameterValues("check[]");

        if(conscustnos != null && conscustnos.length > 0){
            List<String> conscustnolist = Arrays.asList(conscustnos);
            transfer.setConscustnolist(conscustnolist);
        }
        if(check != null && check.length > 0){
            List<String> checklist = Arrays.asList(check);
            transfer.setChecklist(checklist);
        }

        transfer.setConscode(user.getUserId());
        transfer.setOperationid(operationid);
        transfer.setTransfalg(StaticVar.CRM_TRANSF_YES);
        TransferInsertCustResponse transfresponse = transferRepeatCustService.modifyTransferRepeatCust(transfer);

        if(RMIConstant.RMISucc.equals(transfresponse.getReturnCode())){

            List<String> transferCustList = transfresponse.getTransferCustList();
            List<String> noTransferCustList = transfresponse.getNoTransferCustList();
            List<String> processTransferCustList = transfresponse.getProcessTransferCustList();
            List<String> waittransferlist = transfresponse.getWaitTransferList();

            String allstr = "";

            if(checkConsCustList(transferCustList) || checkConsCustList(processTransferCustList) ||
                    checkConsCustList(noTransferCustList) || checkConsCustList(waittransferlist)){
                allstr += "客户号：";

                if(checkConsCustList(noTransferCustList)){
                    allstr += this.returnmsg(noTransferCustList)+"已在你名下，无需申请划转<br>";
                }

                if(transferCustList != null && transferCustList.size() > 0){
                    allstr += this.returnmsg(transferCustList)+"已划转至你名下<br>";
                }

                if(processTransferCustList != null && processTransferCustList.size() > 0){
                    allstr += this.returnmsg(processTransferCustList)+"已有申请，请勿重复申请<br>";
                }

                if(checkConsCustList(waittransferlist)){
                    allstr += this.returnmsg(waittransferlist)+"申请成功<br>";
                }
            }
            resultTaskDisposeMap.put("allstr",allstr);
            resultTaskDisposeMap.put("flag","success");
        }else{
            resultTaskDisposeMap.put("transfresponse",transfresponse);
            resultTaskDisposeMap.put("flag","fail");
        }
        return resultTaskDisposeMap;
    }

    public String returnmsg(List<String> transferCustList){

        StringBuilder sb = new StringBuilder();
        sb.append("");
        if(transferCustList != null){
            for(String conscustno:transferCustList){
                sb.append(conscustno+"，");
            }
        }

        return sb.toString();
    }
    /**
     * 划转信息关闭
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/cancelCmTransfLog")
    public void cancelCmTransfLog(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute("loginUser");
        String operationid = request.getParameter("operationid");
        String[] conscustnos = request.getParameterValues("custData[]");
        List<String> conscustnolist = Arrays.asList(conscustnos);

        TransferRepeatCustRequest transfer = new TransferRepeatCustRequest();
        transfer.setConscode(user.getUserId());
        transfer.setOperationid(operationid);
        transfer.setConscustnolist(conscustnolist);
        transfer.setTransfalg(StaticVar.CRM_TRANSF_NO);
        TransferInsertCustResponse transfresponse = transferRepeatCustService.modifyTransferRepeatCust(transfer);

        conscustIcService.updateConscustOperation(operationid);
    }

    public boolean checkConsCustList(List<String> consCustList){

        boolean checkflag = false;
        if(consCustList != null && consCustList.size() > 0){
            checkflag = true;
        }
        return checkflag;
    }

    @ResponseBody
    @RequestMapping("/viewRepeatCust")
    public Object viewRepeatCust(String conscustno, String operationid, String transferapply){
        List<Map> mapList = new ArrayList<>();
        Map param = new HashMap(2);
        Map user2OutletMap = ConsOrgCache.getInstance().getUser2OutletMap();
        Map orgMap = ConsOrgCache.getInstance().getOrgMap();
        /*if(APPLAY_TRANSFER.equals(transferapply)){*/
        param.put("operationid", operationid);
        List<CmTransfLog> cmTransfLogs = cmTransfLogService.listCmTransfLog(param);
        ConstantCache constantCache = ConstantCache.getInstance();
        for(CmTransfLog cmTransfLog : cmTransfLogs){
            Map map = new HashMap(6);
            map.put("conscustno", cmTransfLog.getConscustno());
            if(StaticVar.NO.equals(cmTransfLog.getIsdel())){
                map.put("transfopstatus", "已删除/二次确认未申请");
            }else{
                map.put("transfopstatus", constantCache.getVal("transfopstatus", cmTransfLog.getOpstatus()));
            }
            map.put("remark", cmTransfLog.getMemo());
            map.put("outletName", orgMap.get(user2OutletMap.get(cmTransfLog.getOldconscode())));
            mapList.add(map);
        }
        /*}else {
            param.put("conscustno", conscustno);
            ConscustRepeat cust = conscustIcService.getConscustIcByRepeat(param);
            QueryRepeatCustRequest queryRepeatCustRequest = new QueryRepeatCustRequest();
            ConscustInfoDomain conscustInfoDomain = new ConscustInfoDomain();
            conscustInfoDomain.setCustname(cust.getCustname());
            conscustInfoDomain.setIdtype(cust.getIdtype());
            conscustInfoDomain.setIdnoDigest(cust.getIdnoDigest());
            conscustInfoDomain.setMobileDigest(cust.getMobileDigest());
            conscustInfoDomain.setInvsttype(cust.getInvsttype());
            queryRepeatCustRequest.setConscustInfoDomain(conscustInfoDomain);
            QueryRepeatCustResponse repeatCustResponse = queryRepeatCustService.queryRepeatCust(queryRepeatCustRequest);
            if(repeatCustResponse.isSuccessful()) {
                for (ConscustInfoDomain infoDomain : repeatCustResponse.getConscustInfoDomains()) {
                    Map map = new HashMap(3);
                    QueryCustconstantInfoRequest queryConsultantInfoRequest = new QueryCustconstantInfoRequest();
                    queryConsultantInfoRequest.setCustNo(infoDomain.getConscustno());
                    QueryCustconstantInfoResponse queryCustconstantInfoResponse = queryCustconstantInfoService.queryCustconstantInfo(queryConsultantInfoRequest);
                    map.put("conscustno", infoDomain.getConscustno());
                    if (queryCustconstantInfoResponse.getCustconstantInfo() != null) {
                        map.put("outletName", orgMap.get(user2OutletMap.get(queryCustconstantInfoResponse.getCustconstantInfo().getConscode())));
                    } else {
                        map.put("outletName", null);
                    }
                    mapList.add(map);
                }
            }
        }*/
        return mapList;
    }
}
