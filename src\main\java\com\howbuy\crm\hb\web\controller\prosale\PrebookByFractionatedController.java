package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.base.PreBookLegalDocUploadMethodEnum;
import com.howbuy.crm.base.YesOrNoEnum;
import com.howbuy.crm.fractionatedcall.service.NoticeAmtToZtService;
import com.howbuy.crm.hb.domain.prosale.Prebookproductinfo;
import com.howbuy.crm.hb.service.prosale.CustprivatefundService;
import com.howbuy.crm.hb.service.prosale.ManyCallPreInfoService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.service.system.CmOptLogService;
import com.howbuy.crm.hb.web.controller.counter.BaseCounterController;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prebook.service.PrebookBusinessService;
import com.howbuy.crm.prosale.dto.Prebookmanycallinfo;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping(value = "/prosale/fractionated")
public class PrebookByFractionatedController  extends BaseCounterController {

	@Autowired
    private CustprivatefundService custprivatefundService;
	

	
	@Autowired
	private ManyCallPreInfoService manyCallPreInfoService;
	
	@Autowired
	private PrebookproductinfoService prebookproductinfoService;

	@Autowired
	private PrebookBusinessService prebookBusinessService;

	
	@Autowired
	private CmOptLogService cmOptLogService;

	@Autowired
	private NoticeAmtToZtService noticeAmtToZtService;
	


	@Autowired
	private JjxxInfoService jjxxInfoService;


	/**
	 * (分次call预约列表界面)
	 *
	 * @return String
	 */
	@RequestMapping("/listPreByFractionatedCall.do")
	public String listPreByOther() {
		return "prosale/fractionatedcall/listPreByFractionatedCall";
	}


	/**
	 * (分次call主预约单列表)
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/listMainPreByPage_json.do")
	public Map<String, Object> listMainPreByPage_json(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
	    // 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String custname = request.getParameter("custname");
		String credt = request.getParameter("credt");
		String fundcode = request.getParameter("fundcode");
		String prebookStates = request.getParameter("prebookStates");
		String tradeStates = request.getParameter("tradeStates");
		String conscode = request.getParameter("conscode");
		String orgcode = request.getParameter("orgCode");
		String transferfalg = request.getParameter("transferfalg");
		
		// 查询条件（客户名）不为空，增增加客户名参数
		if (StringUtils.isNotBlank(custname)) {
			param.put("custname", custname);
		} else {
			param.put("custname", null);
		}

		// 如果查询条件（录入日期）不为空，则增加录入日期查询参数
		if (StringUtils.isNotBlank(credt)) {
			param.put("credt", credt);
		} else {
			param.put("credt", null);
		}

		// 如果查询条件（产品代码）不为空，则增加产品代码查询参数
		if (StringUtils.isNotBlank(fundcode)) {
			param.put("pcode", fundcode);
		} else {
			param.put("pcode", null);
		}
		// 如果查询条件（预约状态）不为空，则增加预约状态查询参数
		if (StringUtils.isNotBlank(prebookStates)) {
			param.put("prebookStates", prebookStates);
		} else {
			param.put("prebookStates", null);
		}
		// 如果查询条件（交易确认状态）不为空，则增加交易确认状态查询参数
		if (StringUtils.isNotBlank(tradeStates)) {
			param.put("tradeStates", tradeStates);
		} else {
			param.put("tradeStates", null);
		}

		// 设置查询条件（投顾编码）
		if (StringUtils.isNotBlank(conscode)) {
			param.put("conscode", conscode);
		} else {
			param.put("orgcode", orgcode);
		}
		
		// 查询条件是否份额转让生成
		if (StringUtils.isNotBlank(transferfalg)) {
			param.put("transferfalg", transferfalg);
		} else {
			param.put("transferfalg", null);
		}

		param.put("legaldocStat",request.getParameter("legaldocStat"));
		param.put("legalDocUploadMethod",request.getParameter("legalDocUploadMethod"));

		// 判断常量表中合规标识：true启用，false停用
 		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
 		boolean roleCPFlag = false;
 		if (cacheMap != null && !cacheMap.isEmpty()) {
 			roleCPFlag = JSON.toJSONString(cacheMap).contains("true");
 		}

 		// 判断登录人员的角色中是否包括“合规人员”角色
 		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
 		if (roleCPFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
			param.put("hascp", "true");
		}
		PageData<Prebookproductinfo> pageData = manyCallPreInfoService.listManyCallPreInfoByPage(param);
		List<Prebookproductinfo> listdata = pageData.getListData();

		ConstantCache constantCache = ConstantCache.getInstance();
		for (Prebookproductinfo info : listdata) {
			ConsOrgCache orgcache = ConsOrgCache.getInstance();
			if(StringUtil.isNotNullStr(info.getCalmdatetime())){
				info.setCalmdatetime(DateUtil.StrToDateStr(info.getCalmdatetime()));
			}
			//info.setCredt(DateUtil.transformDateFormat(info.getCredt(), "yyyyMMddHHmmss","yyyyMMdd HH:mm:ss"));
			info.setPrebookstateval(constantCache.getConstantKeyVal("prebookStates").get(info.getPrebookstate())); 
			//info.setPaymenttype(constantCache.getConstantKeyVal("paymenttype").get(info.getPaymenttype()));
			info.setConsname(orgcache.getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
			info.setOutletName(orgcache.getOrgMap().get(orgcache.getCons2OutletMap().get(Util.ObjectToString(info.getCreator()))));

			String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(Util.ObjectToString(info.getCreator())));
			if("0".equals(uporgcode)){
				info.setUporgname(info.getOutletName());
			}else{
				info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
			}

			info.setPaystateval(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));
			info.setZczmstate(prebookproductinfoService.getZczmstate(info.getHboneno(), info.getFundcode(), info.getConscustno()));

			Map<String,Object> map = new HashMap<>();
			map.put("mainid", info.getMainid());
			Prebookmanycallinfo domain = manyCallPreInfoService.getmanycallhasbuy(info.getMainid());
			info.setBuyamt(domain == null ? BigDecimal.ZERO : domain.getHasbuyamt());//累计实缴金额
			
			String isshow = "1";//默认展示
			//是否展示“新增实缴预约”
			List<Prebookproductinfo> list = prebookproductinfoService.listPrebookproductinfoByMainid(info.getMainid().toString());
			for (Prebookproductinfo prebookproductinfo : list){
				if(prebookproductinfo != null){
					if(StaticVar.PREBOOK_STATES_HAS_CONFIRM.equals(prebookproductinfo.getPrebookstate()) ){
						if(!StaticVar.TRADE_STATE_YES.equals(prebookproductinfo.getTradestate())){
							isshow = "0";
							break;
						}
						
					}
				}
			}
			
			if("1".equals(isshow)){
				BigDecimal total = info.getTotalamt();
				if (Objects.nonNull(total) && info.getBuyamt().compareTo(total) >= 0) {
					isshow = "0";
				}
			}
			
			info.setIsshow(isshow);
			info.setLegaldocStatVal(constantCache.getConstantKeyVal("legaldocstat").get(info.getLegaldocStat()));
			info.setLegalDocUploadMethodVal(PreBookLegalDocUploadMethodEnum.getDescription(info.getLegalDocUploadMethod()));
		}

		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<Prebookproductinfo> list = pageData.getListData();

		resultMap.put("rows", list);
		return resultMap;
	}
	
	
	
	
  
	/**
	 * (分次call下与主预约关联的所有子单)
	 * 
	 * @param PrebookByFractionatedController
	 * @param Map<String,Object>
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listPreFractionatedCall_json.do")
	public Map<String, Object> listPreFractionatedCall_json(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		// 设置查询参数
		Map<String, Object> param = new HashMap<String, Object>();
		String id = request.getParameter("id");
		// 查询条件（客户名）不为空，增增加客户名参数
		if (StringUtil.isNotNullStr(id)) {
			param.put("id", id);
		}

		List<Prebookproductinfo> list = manyCallPreInfoService.listManyCallPreInfo(param);
		ConstantCache constantCache = ConstantCache.getInstance();
		for (Prebookproductinfo info : list) {
			ConsOrgCache orgcache = ConsOrgCache.getInstance();
			info.setConsname(orgcache.getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
			info.setOutletName(orgcache.getOrgMap().get(orgcache.getCons2OutletMap().get(Util.ObjectToString(info.getCreator()))));

			String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(Util.ObjectToString(info.getCreator())));
			if("0".equals(uporgcode)){
				info.setUporgname(info.getOutletName());
			}else{
				info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
			}

			info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));
			info.setPretypeval(constantCache.getConstantKeyVal("pretype").get(info.getPretype()));
			info.setPrebookstateval(constantCache.getConstantKeyVal("prebookStates").get(info.getPrebookstate())); 
			info.setOrderstateval(constantCache.getConstantKeyVal("dealorderStatus").get(info.getOrderstate()));
			info.setPaystateval(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));
			info.setTradestateval(constantCache.getConstantKeyVal("tradestate").get(info.getTradestate()));
			String cretm = info.getCredt();
			if(StringUtils.isNotBlank(cretm) && cretm.length() == 14){
				info.setCredt(cretm.substring(0, 8));
			}
			info.setOrdertime(DateUtil.StrToDateStr(info.getOrdertime()));
			if(StringUtils.isNotBlank(info.getDealno())){
				info.setBuyamt(info.getZtbuyamt());
			}
			if (null != info.getSourcetype()) {
				info.setSourcetypeVal(constantCache.getConstantKeyVal("custrestype").get(info.getSourcetype()));
			}
			info.setFirstsourceVal(constantCache.getConstantKeyVal("firstrestype").get(info.getFirstsource()));
			//资产证明状态
			info.setZczmstate(prebookproductinfoService.getZczmstate(info.getHboneno(), info.getFundcode(), info.getConscustno()));
			if(StringUtils.isEmpty(info.getDiscountstate())){
				info.setDiscountstate("1");
			}
			info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates")
					.get(info.getDiscountstate()));//折扣状态
			info.setTradestateval(constantCache.getConstantKeyVal("tradestate").get(info.getTradestate()));//交易确认状态
			String restype = info.getRestype();//资源类型
			if("0".equals(restype)){
				restype = "公司资源";
       	    }else if ("1".equals(restype)){
       	    	restype = "投顾资源(无划转)";
       	    }else if ("2".equals(restype)){
       	    	restype = "投顾资源(划转-潜在)";
       	    }else if ("3".equals(restype)){
       	    	restype = "投顾资源(划转-成交)";
       	    }
			info.setRestype(restype);
			//千禧年产品标志
			info.setQianXiFlag(checkQianXiFlag(info.getFundcode()));
			// 售前留痕材料状态
			info.setLegaldocStatVal(constantCache.getConstantKeyVal("legaldocstat").get(info.getLegaldocStat()));
			// 售前留痕材料上传方式
			info.setLegalDocUploadMethodVal(PreBookLegalDocUploadMethodEnum.getDescription(info.getLegalDocUploadMethod()));
		}

		// [是否可线上下单]
		setOnlineOrder(list);

		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("rows", list);
		return resultMap;
	}



	/**
	 * 设置是否可线上下单
	 * @param listdata
	 */
	private void setOnlineOrder(List<Prebookproductinfo> listdata){
		//预约Id列表
		List<BigDecimal> preIdList = listdata.stream().map(Prebookproductinfo::getId).collect(Collectors.toList());
		Map<String,String> onlineInfoMap = prebookBusinessService.getOnLineOrderInfoMap(preIdList);
		for(Prebookproductinfo info : listdata){
			// [是否可线上下单]
			String inlineOrder = onlineInfoMap.get(info.getId().toString());
			info.setIslineorder(YesOrNoEnum.YES.getCode().equals(inlineOrder)?YesOrNoEnum.YES.getDescription():YesOrNoEnum.NO.getDescription());
		}
	}
	
	
	
	/**
	 * 获取 是否进入op
	 * 
	 * author: fangyuan.wu
	 * date: 2020年12月29日 下午6:27:38 
	 * @param request
	 * @return
	 */
	private String getProducttype(String pcode ,String conscustno){
		//查询产品是直销还是代销，代销的话就是进入OP，直销和直转代黑名单就是不进入op
        String producttype = StaticVar.COUNTERISOP_NO;//默认不进入OP
        if (StringUtils.isNotBlank(pcode)) {
			JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(pcode, false);
            if (StaticVar.SFMSJG_DX.equals(jjxx1.getSfmsjg())) {
                producttype = StaticVar.COUNTERISOP_YES;
            } else if (StaticVar.SFMSJG_ZZD.equals(jjxx1.getSfmsjg())) {
                Map<String, String> paramzzd = new HashMap<String, String>();
                paramzzd.put("custno", conscustno);
                paramzzd.put("fundcode", pcode);
                List<Map<String, Object>> listzzd = custprivatefundService.listgetZtInfoFundcode(paramzzd);
                if (listzzd == null || (listzzd != null && listzzd.size() == 0)) {
                    producttype = StaticVar.COUNTERISOP_YES;
                }
            }
        }
        
        return producttype;
	}


	
	@ResponseBody
	@RequestMapping("/updatetotalamt.do")
	public String updatetotalamt(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String id = request.getParameter("mainid");
		String totalamt = request.getParameter("totalamt");
		Map<String,String> param = new HashMap<String,String>();
		param.put("mainid", id);
		Prebookmanycallinfo obj = manyCallPreInfoService.getPrebookmanycallinfo(param);
		if(obj != null){
			String oldtotalamt = obj.getTotalamt().toPlainString();
			obj.setId(new BigDecimal(id));
			BigDecimal modifyAmt=new BigDecimal(totalamt).multiply(new BigDecimal(10000));
			obj.setTotalamt(modifyAmt);
			obj.setUpddt(new Date());
			obj.setCurTotalAmt(modifyAmt);
			manyCallPreInfoService.updatePrebookmanycallinfo(obj);
			//修改认缴金额通知中台
			noticeAmtToZtService.sendMq(prebookproductinfoService.selectPrebookproductinfoById(obj.getFirstpreid().toPlainString()).getConscustno()
					, obj.getPcode(), obj.getTotalamt(), DateTimeUtil.getCurDateTime());
			//日志
			User user = (User) request.getSession().getAttribute("loginUser");
			cmOptLogService.insertLog(user.getUserId(), StaticVar.OPT_MANYCALL_UPT_TOTALAMT, "将原认缴金额:"+oldtotalamt+"改成了:"+obj.getTotalamt().toPlainString(), id);
		}
		return "success";
	}
    

}
