package com.howbuy.crm.hb.web.controller.hbone;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.conscust.service.QueryConscustListService;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.web.util.ParamUtil;
import com.howbuy.crm.page.cache.ConstantCache;

import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2020/8/25 10:14
 * @Description 根据条件查询投顾顾客信息
 * @Version 1.0
 */

@Controller
@RequestMapping(value = "/custRelation")
public class CustRelationController {
    private static Logger LOG = LoggerFactory.getLogger(CustRelationController.class);

    @Autowired
    QueryConscustListService queryConscustListService;

    @Autowired
    private ConscustService conscustService;

    @ResponseBody
    @RequestMapping("/listConscustByPage")
    public Map<String, Object> listConscustByPage(HttpServletRequest request) throws Exception {
        Map<String, String> param = new HashMap<String, String>(16);
        param = new ParamUtil(request).getParamMap();

        String mobile = request.getParameter("mobile");
        String email = request.getParameter("email");
        if(StringUtils.isNotBlank(mobile)){
        	param.put("mobile", DigestUtil.digest(mobile.trim()));
        }
        if(StringUtils.isNotBlank(email)){
        	param.put("email", DigestUtil.digest(email.trim().toLowerCase()));
        }
        
        PageData<Conscust> pd = conscustService.selectConscustByPage(param);

        List<Conscust> list = pd.getListData();
        Map<String, Object> resultMap = new HashMap<String, Object>(16);
        if (list != null && list.size() > 0) {
            /*if (RMIConstant.RMISucc.equals(queryResponse.getReturnCode())) {
                if (queryResponse.getConscustlist() != null && queryResponse.getConscustlist().size() > 0) {*/
                    //3.5.1 敏感数据隐藏
                    for (Conscust conscust : list) {
                        /*conscust.setEmail(Util.getEncryptValue(request, StaticVar.ENCRYPT_EMAIL, conscust.getEmail(), null, null));
                        conscust.setEmail2(Util.getEncryptValue(request, StaticVar.ENCRYPT_EMAIL, conscust.getEmail2(), null, null));
                        conscust.setTelno(Util.getEncryptValue(request, StaticVar.ENCRYPT_PHONE, conscust.getMobile(), null, null));
                        conscust.setMobile(Util.getEncryptValue(request, StaticVar.ENCRYPT_MOBILE, conscust.getMobile(), null, null));
                        conscust.setMobile2(Util.getEncryptValue(request, StaticVar.ENCRYPT_MOBILE, conscust.getMobile2(), null, null));
                        conscust.setIdno(Util.getEncryptValue(request, StaticVar.ENCRYPT_BANKNO, conscust.getIdno(), null, null));*/
                    	conscust.setEmail(conscust.getEmailMask());
                    	conscust.setEmail2(conscust.getEmail2Mask());
                        conscust.setTelno(conscust.getMobileMask());
                        conscust.setMobile(conscust.getMobileMask());
                        conscust.setMobile2(conscust.getMobile2Mask());
                        conscust.setIdno(conscust.getIdnoMask());

                        // 根据投资者类型不同，展示不同的证件类型
                        if (StaticVar.INVST_TYPE_ORG.equals(conscust.getInvsttype())) {
                            conscust.setIdtype(ConstantCache.getInstance().getVal("IDTypesOfInstNew", conscust.getIdtype()));
                        } else if (StaticVar.INVST_TYPE_FUND.equals(conscust.getInvsttype())) {
                            conscust.setIdtype(ConstantCache.getInstance().getVal("IDTypesOfFund", conscust.getIdtype()));
                        } else if (StaticVar.INVST_TYPE_PRODUCT.equals(conscust.getInvsttype())) {
                            conscust.setIdtype(ConstantCache.getInstance().getVal("IDTypesOfProduct", conscust.getIdtype()));
                        } else {
                            conscust.setIdtype(ConstantCache.getInstance().getVal("idtype", conscust.getIdtype()));
                        }

                        conscust.setInvsttype(ConstantCache.getInstance().getVal("InvstTypes", conscust.getInvsttype()));
                    }
                }

        resultMap.put("total", pd.getPageBean().getTotalNum());
        resultMap.put("rows", pd.getListData());
        return resultMap;
    }
}
