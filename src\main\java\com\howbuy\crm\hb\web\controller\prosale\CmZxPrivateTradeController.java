package com.howbuy.crm.hb.web.controller.prosale;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.howbuy.crm.hb.domain.prosale.ZxPrivateTradeFund;
import com.howbuy.crm.hb.domain.prosale.ZxPrivateTradeFundVo;
import com.howbuy.crm.hb.service.prosale.CustprivatefundService;
import com.howbuy.crm.hb.service.prosale.ZxPrivateTradeFundService;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.prosale.dto.Custprivatefund;
import com.howbuy.simu.dto.business.product.SmjzAndHbDto;
import com.howbuy.simu.service.business.product.SmjzAndHbService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 
 * @reason:
 * @Date: 2020/4/21 16:13
 */
@Controller
@RequestMapping("/prosale")
@Slf4j
public class CmZxPrivateTradeController {
	
	private static Logger logger = LoggerFactory.getLogger(CmZxPrivateTradeController.class);

    @Autowired
    private CustprivatefundService custprivatefundService;
    
    @Autowired
    private ZxPrivateTradeFundService zxPrivateTradeFundService;
    
    @Autowired
    private SmjzAndHbService smjzAndHbService;

    @Autowired
    private JjxxInfoService jjxxInfoService;

    /**
     * 跳转到listZxPrivateTrade页面方法
     * @return String
     */
    @RequestMapping(value="/listZxPrivateTrade.do",method=RequestMethod.GET)
    public String listZxPrivateTrade() {
        return "prosale/listZxPrivateTrade";
    }

    /**
     * 加载页面数据方法
     * @param request
     * @return Map<String, Object>
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listZxPrivateTradeByPage_json.do")
    public Map<String, Object> listTransferVolByPageJson(HttpServletRequest request) throws Exception {
        // 设置查询参数
        ZxPrivateTradeFundVo vo = buildParam(request);
       
        PageData<ZxPrivateTradeFund> pageData = zxPrivateTradeFundService.listZxPrivateTradeFundByPage(vo);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<ZxPrivateTradeFund> listdata= pageData.getListData();
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        for(ZxPrivateTradeFund info : listdata){
            JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
            info.setOutletName(consOrgCache.getAllOrgMap().get(info.getOutletcode()));
            String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(info.getConscode()));
            if("0".equals(uporgcode)){
                info.setUporgname(info.getOutletName());
            }else{
                info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
            }
            info.setConsname(consOrgCache.getAllUserMap().get(info.getConscode()));
            if(jjxx1 != null){
                info.setFundname(jjxx1.getJjjc());
            }
            if(info.getBalancevol() != null && info.getNav() != null){
            	info.setBalanceamt(info.getBalancevol().multiply(info.getNav()));
            	BigDecimal factor = BigDecimal.ZERO;
            	if(info.getBalancefactor() != null){
            		factor = info.getBalancefactor();
            	}
            	info.setBalanceamt(info.getBalancevol().multiply(info.getNav()).add(factor).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }
        resultMap.put("rows", listdata);
        return resultMap;
    }

    /**
     * 处理强赎
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/dealSignQs.do")
    public ModelAndView dealSignQs(HttpServletRequest request){
        Map<String,Object> map = new HashMap<>();
        String custno = request.getParameter("custno");
        String fundcode = request.getParameter("fundcode");
        Map<String,Object> param = new HashMap<>();
        param.put("custno", custno);
        param.put("fundcode", fundcode);
        List<Custprivatefund> funds = custprivatefundService.listCustprivatefundByConditon(param);
        map.put("custno", custno);
        map.put("fundcode", fundcode);
        if(funds != null && funds.size() > 0){
        	Custprivatefund fund = funds.get(0);
        	map.put("custname", fund.getCustname());
        	map.put("vol", fund.getBalancevol());
        }
        JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(fundcode, false);
        if(jjxx1 != null){
        	map.put("fundname", jjxx1.getJjjc());
        }else{
        	map.put("fundname", "");
        }
        return new ModelAndView("prosale/dealqs","info",map);
    }

    /**
     * 强赎处理
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/qsDeal.do")
    public String qsDeal(HttpServletRequest request) {
        User userlogin = (User)request.getSession().getAttribute("loginUser");
        String custno = request.getParameter("custno");
        String fundcode = request.getParameter("fundcode");
        String tradedt=request.getParameter("tradedt");
        String ishaiwai = request.getParameter("ishaiwai");
        String nav =request.getParameter("nav");
        String discsummary =request.getParameter("discsummary");
        String result = "";
        if(StringUtil.isNotNullStr(nav) && new BigDecimal(nav).compareTo(new BigDecimal("0")) > 0){
        	result = zxPrivateTradeFundService.dealQs(custno, fundcode, tradedt, new BigDecimal(nav), userlogin.getUserId(), ishaiwai, discsummary);
        }else{
        	result = "没有获取到净值！";
        }
        return result;
    }
    
    /**
     * 批量处理强赎
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/batchdealQs.do")
    public ModelAndView batchdealQs(HttpServletRequest request){
        Map<String,Object> map = new HashMap<>();
        String custnos = request.getParameter("custnos");
        String fundcode = request.getParameter("fundcode");
        map.put("custnos",custnos);
        map.put("fundcode", fundcode);
        JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(fundcode, false);
        if(jjxx1 != null){
        	map.put("fundname", jjxx1.getJjjc());
        }else{
        	map.put("fundname", "");
        }
        return new ModelAndView("prosale/batchdealqs","info",map);
    }

    /**
     * 批量强赎处理
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/batchqsDeal.do")
    public String batchqsDeal(HttpServletRequest request) {
        User userlogin = (User)request.getSession().getAttribute("loginUser");
        String custnos = request.getParameter("custnos");
        String fundcode = request.getParameter("fundcode");
        String tradedt=request.getParameter("tradedt");
        String ishaiwai = request.getParameter("ishaiwai");
        String nav =request.getParameter("nav");
        String discsummary =request.getParameter("discsummary");
        String result = "";
        List<String> listcustno = new ArrayList<>();
        if(StringUtil.isNotNullStr(custnos)){
        	if(custnos.indexOf(",") == -1){
        		listcustno.add(custnos);
        	}else{
        		for(String custno : custnos.split(",")){
        			listcustno.add(custno);
        		}
        	}
        	if(StringUtil.isNotNullStr(nav) && new BigDecimal(nav).compareTo(new BigDecimal("0")) > 0){
            	result = zxPrivateTradeFundService.batchdealQs(listcustno, fundcode, tradedt, new BigDecimal(nav), userlogin.getUserId(), ishaiwai, discsummary);
            }else{
            	result = "没有获取到净值！";
            }
        }else{
        	result = "客户号必传";
        }
        return result;
    }
    
    /**
	 * 构建 查询vo
	 * @param request
	 * @return
	 * @throws Exception
	 */
	private ZxPrivateTradeFundVo buildParam(HttpServletRequest request) throws Exception{
		ZxPrivateTradeFundVo returnVo=new ZxPrivateTradeFundVo();
		returnVo.setCustname(getSqlParam(request.getParameter("custname")));
		returnVo.setHboneno(getSqlParam(request.getParameter("hboneno")));
		returnVo.setConscustno(getSqlParam(request.getParameter("conscustno")));
		returnVo.setFundcode(getSqlParam(request.getParameter("fundcode")));
		// 设置查询条件（投顾编码）
		String conscode=request.getParameter("conscode");
		String orgcode=request.getParameter("orgcode");
		if (StringUtils.isNotBlank(conscode)) {
			returnVo.setConscode(conscode);
		} else {
			returnVo.setOrgcode(orgcode);
		}
		//排序和页数
        String page=getSqlParam(request.getParameter("page"));
        String rows=getSqlParam(request.getParameter("rows"));
        String sort=getSqlParam(request.getParameter("sort"));
        String order=getSqlParam(request.getParameter("order"));
        if(page!=null){
            returnVo.setPage(Integer.valueOf(page));
        }
        if(rows!=null){
            returnVo.setRows(Integer.valueOf(rows));
        }
        if(sort!=null){
            returnVo.setSort(sort);
        }
        if(order!=null){
            returnVo.setOrder(order);
        }
		return returnVo;
	}
	
	private String getSqlParam(String str){
		if (StringUtils.isNotBlank(str)) {
			return str;
		} else {
			return null;
		}
	}
	
	/**
	 * 根据产品代码和日期查询基金净值
	 * @param request
	 * @return
	 */
    @ResponseBody
    @RequestMapping("/getnavbyfundandtd.do")
    public String getnavbyfundandtd(HttpServletRequest request) {
        String fundcode = request.getParameter("fundcode");
        String tradedt = request.getParameter("tradedt");
        Double amount = null;
        JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(fundcode, false);
        if(jjxx != null){
	        List<SmjzAndHbDto> smjzAndHbDtos = getsmjzAndHbDtos(jjxx.getHmcpx(),fundcode,tradedt);
	        if(CollectionUtils.isNotEmpty(smjzAndHbDtos)){
	            amount = smjzAndHbDtos.get(0).getJjjz();
	        }
	        if(amount == null){
	            amount = 0D;
	        }
        }
        String result = new BigDecimal(amount).setScale(4,BigDecimal.ROUND_HALF_UP).toPlainString();
        return result;
    }

    private List<SmjzAndHbDto> getsmjzAndHbDtos(String hmcpx,String fundCode,String tradeDt){
    	List<SmjzAndHbDto> smjzAndHbDtos;
        //调用DB接口获取净值
        if(StaticVar.HMCPX_GD.equals(hmcpx) || StaticVar.HMCPX_PEVC.equals(hmcpx)){
            logger.info("--------------查询净值接口参数：jjdm:{} startDate:{} endDate:{}",fundCode,null,tradeDt);
            smjzAndHbDtos = smjzAndHbService.getByJjdm(fundCode,null,tradeDt);
        }else {
            logger.info("--------------查询净值接口参数：jjdm:{} startDate:{} endDate:{}",fundCode,tradeDt,tradeDt);
            smjzAndHbDtos = smjzAndHbService.getByJjdm(fundCode,tradeDt,tradeDt);
        }
        logger.info("--------------查询净值接口返回：{}",smjzAndHbDtos == null ? null : JSON.toJSONString(smjzAndHbDtos));
        return smjzAndHbDtos;
    }
    
}
