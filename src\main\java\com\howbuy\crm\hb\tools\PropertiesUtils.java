package com.howbuy.crm.hb.tools;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;


public class PropertiesUtils {

    private static Logger LOGGER = LoggerFactory.getLogger(PropertiesUtils.class);

    public static Map<String, String> loadGlobalProperties(final String globalPath) throws Exception {
        return loadPathProperties(globalPath + "/app-global.properties");
    }


    public static Map<String, String> loadPathProperties(final String path) throws Exception {
        Properties properties = new Properties();
        Map<String, String> map = new HashMap<String, String>();
        InputStream in = null;
        try {
            File file = new File(path);
            in = new FileInputStream(file);
            properties.load(in);
            Set keyValue = properties.keySet();
            for (Iterator it = keyValue.iterator(); it.hasNext(); ) {
                String key = (String) it.next();
                map.put(key, properties.getProperty(key));
                System.setProperty(key, properties.getProperty(key));
            }
        } finally {
        	try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
            	LOGGER.error(e.getMessage(), e);
            }
        }
        return map;
    }

}
