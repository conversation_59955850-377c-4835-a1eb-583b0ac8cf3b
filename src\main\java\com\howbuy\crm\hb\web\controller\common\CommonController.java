package com.howbuy.crm.hb.web.controller.common;


import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModule;
import com.howbuy.crm.nt.uploadmodule.service.UploadModuleService;

import crm.howbuy.base.enums.CrmUploadModuleEnum;
import crm.howbuy.base.utils.StringUtil;


@Controller
@RequestMapping(value = "/common")
public class CommonController{
	@Autowired
	private UploadModuleService ntUploadModuleService;
	
	/**
	 * 加载上传文件验证弹出页面
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/viewUploadFileValidate.do")
	public ModelAndView viewUploadFileValidate(HttpServletRequest request){
		String type = request.getParameter("type");
		Assert.notNull(type);
		return new ModelAndView("/common/uploadfilevalidate", "type", type);
	}
	
	@ResponseBody
	@RequestMapping("/getCommUploadModule.do")
	public Object getCommUploadModule(HttpServletRequest request){
		String mod = request.getParameter("mod");
		if(StringUtil.isNotNullStr(mod) && CrmUploadModuleEnum.getEnum(mod) != null){
			NtCmUploadModule uploadmodule = ntUploadModuleService.getCmUploadModule(CrmUploadModuleEnum.getEnum(mod));
			StringBuilder sb = new StringBuilder();
			if(uploadmodule.getTypeSuffixsMap() != null){
				sb.append("仅支持以下类型的格式文件！</br></br><table border='1' cellpadding='0' cellspacing='0' ><tr bgcolor='#F5F5F5'><td style='width: 80px;' align='center'>类型</td><td style='width: 400px;' align='center'>支持格式</td></tr>");
				uploadmodule.getTypeSuffixsMap().forEach((key,value)->{
					sb.append("<tr><td align='center'>"+key+"</td><td>"+value+"</td></tr>");
				});
				sb.append("</table>");
			}
			uploadmodule.setTypeSuffixResult(sb.toString());
			return uploadmodule;
		}else{
			return new NtCmUploadModule();
		}
	}
}

