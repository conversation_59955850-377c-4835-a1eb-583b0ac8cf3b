package com.howbuy.crm.hb.tools;

import java.io.File;
import java.util.Map;
import java.util.LinkedHashMap;

public class ContextManager {
	// 网点状态：0暂停，1正常使用，2预备关闭，3欠费关闭
	public static final Map<String, String> ORG_STATUS = new LinkedHashMap<String, String>();
	static {
		ORG_STATUS.put("0", "暂停");
		ORG_STATUS.put("1", "正常使用");
		ORG_STATUS.put("2", "预备关闭");
		ORG_STATUS.put("3", "欠费关闭");	
	}
	
	// 处理类型：0忽略，1取消发布
	public static final Map<Integer, String> DEAL_TYPE = new LinkedHashMap<Integer, String>();
	static {
		DEAL_TYPE.put(0, "忽略");
		DEAL_TYPE.put(1, "取消发布");
	}
	
	// 登录异常信息：0验证码错误，1用户名不存在，2密码错误,3登录成功,9无访问权限，（请联系客服开通）
	public static final String LOGIN_STATUS_ZERO = "0";
	public static final String LOGIN_STATUS_ONE = "1";
	public static final String LOGIN_STATUS_TWO = "2";
	public static final String LOGIN_STATUS_THREE = "3";
	public static final String LOGIN_STATUS_OTHER = "9";
	public static final Map<String, String> LOGIN_STATUS = new LinkedHashMap<String, String>();
	static {
		LOGIN_STATUS.put("0", "验证码错误！");
		LOGIN_STATUS.put("1", "用户名不存在！");
		LOGIN_STATUS.put("2", "密码错误！");	
		LOGIN_STATUS.put("3", "登录成功！");	
		LOGIN_STATUS.put("9", "无访问权限，请联系客服开通！");	
	}
	
	//文件上传与下载路径
	public static final String SAVE_PATH="StaticResource"+File.separator+"files"+File.separator+"upload"+File.separator;
 
	// 帮助说明类型：0帮助说明
	public static final Map<String, String> DOCUMENT_TYPE = new LinkedHashMap<String, String>();
	static {
		DOCUMENT_TYPE.put("0", "帮助说明");
		DOCUMENT_TYPE.put("1", "需求说明");
		DOCUMENT_TYPE.put("2", "设计说明");
	}
	
	// 客户级别：1普通,2重要,3非常重要
	public static final Map<String, String> CUST_GLEVELS = new LinkedHashMap<String, String>();
	static {
		CUST_GLEVELS.put("1","普通");
		CUST_GLEVELS.put("2","重要");
		CUST_GLEVELS.put("3","非常重要");
	}
	
	// 基金分类：0股票型，1混合型，2债券型，3货币型，4QDII，5封闭式，6结构型，7一对多
	public static final Map<String, String> FUND_TYPE = new LinkedHashMap<String, String>();
	static {
		FUND_TYPE.put("0", "股票型");
		FUND_TYPE.put("1", "混合型");
		FUND_TYPE.put("2", "债券型");
		FUND_TYPE.put("3", "货币型");
		FUND_TYPE.put("4", "QDII");
		FUND_TYPE.put("5", "封闭式");
		FUND_TYPE.put("6", "结构型");
		FUND_TYPE.put("7", "一对多");
		FUND_TYPE.put("8", "储蓄罐");
	}
	
	// 基金二级分类： 01指数型，21理财产品，71股票型一对多，72债券型一对多，73货币型一对多
	public static final Map<String, String> FUND_SUB_TYPE = new LinkedHashMap<String, String>();
	static {
		FUND_SUB_TYPE.put("01", "指数型");
		FUND_SUB_TYPE.put("21", "理财产品");
		FUND_SUB_TYPE.put("71", "股票型一对多");
		FUND_SUB_TYPE.put("72", "债券型一对多");
		FUND_SUB_TYPE.put("73", "货币型一对多");
	}
	
	// 收费类型：A前收费，B后收费
	public static final Map<String, String> SHARE_CLASSES = new LinkedHashMap<String, String>();
	static {
		SHARE_CLASSES.put("A", "前收费");
		SHARE_CLASSES.put("B", "后收费");
	}
	
	// 定期定额状态：0未开通,1开通
	public static final Map<String, String> PD_SUBS_STATS = new LinkedHashMap<String, String>();
	static {
		PD_SUBS_STATS.put("0","未开通");
		PD_SUBS_STATS.put("1","开通");
	}
	
	// 交易状态： 0可申购赎回，1发行，4停止申购赎回，5停止申购，6停止赎回，8基金终止，9基金封闭
	public static final Map<String, String> FUND_STATUSES = new LinkedHashMap<String, String>();
	static {
		FUND_STATUSES.put("0","可申购赎回");
		FUND_STATUSES.put("1","发行");
		FUND_STATUSES.put("4","停止申购赎回");
		FUND_STATUSES.put("5","停止申购");
		FUND_STATUSES.put("6","停止赎回");
		FUND_STATUSES.put("8","基金终止");
		FUND_STATUSES.put("9","基金封闭");
	}
	
	// 基金状态：0交易，1发行，2发行成功，3发行失败，4停止交易，5停止申购，6停止赎回，7权益登记，8红利发放，9基金封闭，a基金终止
	public static final Map<String, String> FUND_STATE = new LinkedHashMap<String, String>();
	static {
		FUND_STATE.put("0","交易");
		FUND_STATE.put("1","发行");
		FUND_STATE.put("2","发行成功");
		FUND_STATE.put("3","发行失败");
		FUND_STATE.put("4","停止交易");
		FUND_STATE.put("5","停止申购");
		FUND_STATE.put("6","停止赎回");
		FUND_STATE.put("7","权益登记");
		FUND_STATE.put("8","红利发放");
		FUND_STATE.put("9","基金封闭");
		FUND_STATE.put("a","基金终止");
	}
	
	// 基金风险等级，好买自己定义的：1低风险,2中风险,3高风险
	public static final Map<String, String> FUND_RISK_LEVELS = new LinkedHashMap<String, String>();
	static {
		FUND_RISK_LEVELS.put("1","低风险");
		FUND_RISK_LEVELS.put("2","中风险");
		FUND_RISK_LEVELS.put("3","高风险");
	}
	
	// 基金风险等级，好买自己定义的：1低风险,2中风险,3高风险
	public static final Map<String, String> FUND_RISK_LEVELS2 = new LinkedHashMap<String, String>();
	static {
		FUND_RISK_LEVELS2.put("1","低");
		FUND_RISK_LEVELS2.put("2","中");
		FUND_RISK_LEVELS2.put("3","高");
	}
	
	// 基金风险等级，基金公司定义的：1低风险，2低中风险，3中风险，4高中风险，5高风险，9其他
	public static final Map<String, String> FUND_RISK_LEVELS_FROM_MAN = new LinkedHashMap<String, String>();
	static {
		FUND_RISK_LEVELS_FROM_MAN.put("1","低风险");
		FUND_RISK_LEVELS_FROM_MAN.put("2","低中风险");
		FUND_RISK_LEVELS_FROM_MAN.put("3","中风险");
		FUND_RISK_LEVELS_FROM_MAN.put("4","高中风险");
		FUND_RISK_LEVELS_FROM_MAN.put("5","高风险");
		FUND_RISK_LEVELS_FROM_MAN.put("9","其他");
	}
	
	// 业务编码
	public static final Map<String, String> BUSI_CODES = new LinkedHashMap<String, String>();
	static {
		BUSI_CODES.put("301", "基金交易账户开户");
		BUSI_CODES.put("302", "基金交易账户销户");
		BUSI_CODES.put("303", "客户资料修改");
		BUSI_CODES.put("304", "基金交易账户挂失");
		BUSI_CODES.put("305", "基金交易账户解挂");
		BUSI_CODES.put("308", "客户重要资料修改");
		BUSI_CODES.put("309", "修改客户补充资料");
		BUSI_CODES.put("311", "开通代扣款支付方式");
		BUSI_CODES.put("312", "关闭代扣款支付方式");
		BUSI_CODES.put("313", "增加客户经办人");
		BUSI_CODES.put("314", "修改客户经办人");
		BUSI_CODES.put("315", "删除客户经办人");
		BUSI_CODES.put("316", "客户风险承受能力测试");
		BUSI_CODES.put("320", "客户银行账号维护");
		BUSI_CODES.put("321", "客户银行账户信息变更");
		BUSI_CODES.put("322", "身份及卡验证维护");
		BUSI_CODES.put("323", "支付申请启动");
		BUSI_CODES.put("324", "支付结果记录");
		BUSI_CODES.put("001", "新开基金账户");
		BUSI_CODES.put("008", "开通已有基金账户");
		BUSI_CODES.put("002", "基金账户销户");
		BUSI_CODES.put("020", "认购");
		BUSI_CODES.put("022", "申购");
		BUSI_CODES.put("024", "赎回");
		BUSI_CODES.put("020_1", "存钱");
		BUSI_CODES.put("024_1", "取现");
		BUSI_CODES.put("024_2", "快速取现");
		BUSI_CODES.put("026", "一次转托管");
		BUSI_CODES.put("027", "转托管转入");
		BUSI_CODES.put("028", "转托管转出");
		BUSI_CODES.put("029", "更改基金分红选择");
		BUSI_CODES.put("036", "基金转换");
		BUSI_CODES.put("352", "撤消交易");
		BUSI_CODES.put("325", "设置默认风险等级");
		BUSI_CODES.put("326", "开户银行卡更改");
		BUSI_CODES.put("327", "账户验证扣款申请");
		BUSI_CODES.put("328", "账户验证扣款结果记录");
		BUSI_CODES.put("329", "代扣款");
		BUSI_CODES.put("510", "定期定额申购维护");
		BUSI_CODES.put("511", "定时定额申购协议开通");
		BUSI_CODES.put("512", "定时定额申购协议维护");
		BUSI_CODES.put("513", "支付协议签约");
		BUSI_CODES.put("514", "支付协议解约");
		BUSI_CODES.put("515", "定期定额申购继续");
		BUSI_CODES.put("351", "强制取消");
		BUSI_CODES.put("396", "定期赎回申请");
		BUSI_CODES.put("391", "定期赎回修改");
		BUSI_CODES.put("392", "定期赎回撤销");
		BUSI_CODES.put("393", "定期赎回协议查询");
		BUSI_CODES.put("394", "定期赎回情况查询");
		BUSI_CODES.put("397", "预约赎回开通");
		BUSI_CODES.put("398", "预约赎回修改");
		BUSI_CODES.put("399", "预约赎回关闭");
		BUSI_CODES.put("003", "基金账户修改");
		BUSI_CODES.put("009", "撤销交易账户");
		BUSI_CODES.put("039", "定时定额投资");
		BUSI_CODES.put("101", "基金账户开户");
		BUSI_CODES.put("102", "基金账户销户");
		BUSI_CODES.put("103", "基金账户修改");
		BUSI_CODES.put("104", "基金账户冻结");
		BUSI_CODES.put("105", "基金账户解冻");
		BUSI_CODES.put("106", "基金账户挂失");
		BUSI_CODES.put("107", "基金账户解挂");
		BUSI_CODES.put("108", "增加交易账户");
		BUSI_CODES.put("109", "撤销交易账户");
		BUSI_CODES.put("120", "认购");
		BUSI_CODES.put("122", "申购");
		BUSI_CODES.put("124", "赎回");
		BUSI_CODES.put("126", "一次转托管");
		BUSI_CODES.put("127", "转托管转入");
		BUSI_CODES.put("128", "转托管转出");
		BUSI_CODES.put("129", "设置自动再投资");
		BUSI_CODES.put("130", "认购结果");
		BUSI_CODES.put("131", "基金份额冻结");
		BUSI_CODES.put("132", "基金份额解冻");
		BUSI_CODES.put("133", "非交易过户");
		BUSI_CODES.put("134", "非交易过户转入");
		BUSI_CODES.put("135", "非交易过户转出");
		BUSI_CODES.put("136", "基金转换");
		BUSI_CODES.put("137", "基金转换转入");
		BUSI_CODES.put("138", "基金转换转出");
		BUSI_CODES.put("139", "定时定额投资");
		BUSI_CODES.put("142", "强制赎回");
		BUSI_CODES.put("143", "红利发放");
		BUSI_CODES.put("144", "强行调增");
		BUSI_CODES.put("145", "强行调减");
		BUSI_CODES.put("146", "配号");
		BUSI_CODES.put("149", "募集失败");
		BUSI_CODES.put("150", "基金清盘");
		BUSI_CODES.put("151", "基金终止");
		BUSI_CODES.put("198", "快速赎回");
		BUSI_CODES.put("601", "微信账户绑定");
		BUSI_CODES.put("602", "微信账户解除绑定");
		BUSI_CODES.put("603", "微信鉴权");
		BUSI_CODES.put("604", "微信鉴权");
		BUSI_CODES.put("161", "基金降级");
		BUSI_CODES.put("162", "基金升级");
		BUSI_CODES.put("701", "易宝一键迁移失败用户更换银行卡");
		BUSI_CODES.put("702", "易宝无密验证");
		BUSI_CODES.put("703", "易宝一键迁移");
		BUSI_CODES.put("359", "账户验证扣款申请");
		BUSI_CODES.put("360", "账户验证扣款结果记录");
	}
	
	// 来源细分（渠道信息）
	public static final Map<String, String> SUB_SOURCE = new LinkedHashMap<String, String>();
	static {
		SUB_SOURCE.put("41", "非注册客户");
		SUB_SOURCE.put("42", "注册客户");
		SUB_SOURCE.put("43", "线上活动");
		SUB_SOURCE.put("44", "客户端");
		SUB_SOURCE.put("31", "线下活动");
		SUB_SOURCE.put("32", "短信营销");
		SUB_SOURCE.put("33", "EDM营销");
		SUB_SOURCE.put("34", "陌生电话");
		SUB_SOURCE.put("35", "陌生DM");
		SUB_SOURCE.put("22", "员工介绍");
		SUB_SOURCE.put("23", "自主开发");
		SUB_SOURCE.put("24", "电话营销");
		SUB_SOURCE.put("61", "一财");
		SUB_SOURCE.put("62", "MYSTEEL");
		SUB_SOURCE.put("63", "我爱卡");
		SUB_SOURCE.put("71", "MGM");
		SUB_SOURCE.put("W20130801", "联想无线");
		SUB_SOURCE.put("W20130802", "UCWEB");
		SUB_SOURCE.put("A20131202", "掌-25pp");
		SUB_SOURCE.put("A20131203", "掌-安智网");
		SUB_SOURCE.put("A20140501", "储-百度投放");
		SUB_SOURCE.put("A20140502", "储-谷歌投放");
		SUB_SOURCE.put("H20121201", "掌上基金用户");
		SUB_SOURCE.put("W20130110", "搜狐腾信");
		SUB_SOURCE.put("W20120706", "掌上网");
		SUB_SOURCE.put("O20121101", "掌-手机拉卡拉");
		SUB_SOURCE.put("H20130301", "掌-好买WAP站");
		SUB_SOURCE.put("M20120703", "掌-电信号百");
		SUB_SOURCE.put("I20120801", "掌-北京掌上乐");
		SUB_SOURCE.put("O20121102", "掌-读览天下");
		SUB_SOURCE.put("M20120701", "掌-联通");
		SUB_SOURCE.put("M20120702", "掌-移动梦网");
		SUB_SOURCE.put("W20120705", "掌-百度移动");
		SUB_SOURCE.put("W20120702", "掌-google market");
		SUB_SOURCE.put("W20120703", "腾讯");
		SUB_SOURCE.put("W20120701", "掌");
		SUB_SOURCE.put("H20120701", "掌-好买网安卓下载");
		SUB_SOURCE.put("A20120701", "掌-APP store");
		SUB_SOURCE.put("A20120702", "掌-联通WO商店");
		SUB_SOURCE.put("A20120703", "掌-91平台");
		SUB_SOURCE.put("A20120704", "掌-weiphone");
		SUB_SOURCE.put("A20120705", "掌-无差别第三方");
		SUB_SOURCE.put("W20120704", "中金在线");
		SUB_SOURCE.put("W20130702", "腾讯财经");
		SUB_SOURCE.put("W20130903", "全景网");
		SUB_SOURCE.put("A20140201", "掌-豌豆荚");
		SUB_SOURCE.put("A20140202", "掌-N多网");
		SUB_SOURCE.put("W20140203", "凤凰财经");
		SUB_SOURCE.put("A20140306", "储-木蚂蚁");
		SUB_SOURCE.put("A20140315", "储-乐商店");
		SUB_SOURCE.put("A20140316", "储-豌豆荚");
		SUB_SOURCE.put("A20140317", "储-N多网");
		SUB_SOURCE.put("W20130703", "微信平台");
		SUB_SOURCE.put("O20130901", "掌-HiKe手机");
		SUB_SOURCE.put("W20130803", "天一星辰");
		SUB_SOURCE.put("O20131001", "怡安翰威特");
		SUB_SOURCE.put("A20131201", "掌-木蚂蚁");
		SUB_SOURCE.put("A20131204", "掌-360");
		SUB_SOURCE.put("A20140308", "储-91平台");
		SUB_SOURCE.put("W20140401", "储-UCWEB");
		SUB_SOURCE.put("W20130701", "盈盈理财");
		SUB_SOURCE.put("H20131104", "储-howbuy_ipa");
		SUB_SOURCE.put("A20140310", "储-移动MM");
		SUB_SOURCE.put("W20130601", "搜狐2013");
		SUB_SOURCE.put("W20130602", "腾讯内部");
		SUB_SOURCE.put("A20131205", "掌-小米");
		SUB_SOURCE.put("A20140301", "储-app store");
		SUB_SOURCE.put("A20140302", "储-25PP");
		SUB_SOURCE.put("A20140303", "储-同步推");
		SUB_SOURCE.put("A20140304", "储-腾讯");
		SUB_SOURCE.put("A20140305", "储-baidu");
		SUB_SOURCE.put("A20140307", "储-联通");
		SUB_SOURCE.put("A20140309", "储-google market");
		SUB_SOURCE.put("A20140311", "储-安智网");
		SUB_SOURCE.put("A20140312", "储-360");
		SUB_SOURCE.put("A20140313", "储-小米");
		SUB_SOURCE.put("A20140314", "储-无区分第三方");
		SUB_SOURCE.put("W20140701", "百度财富");
	}
	
	// 基金对象：1个人,0机构
	public static final Map<String, String> INVST_TYPES = new LinkedHashMap<String, String>();
	static {
		INVST_TYPES.put("0", "0-机构");
		INVST_TYPES.put("1", "1-个人");
	}
	
	// 取费率方式：0固定费用，1按金额，2按天数，3按份额
	public static final Map<String, String> GET_FEE_RATE_METHODS = new LinkedHashMap<String, String>();
	static {
		GET_FEE_RATE_METHODS.put("0", "固定费用");
		GET_FEE_RATE_METHODS.put("1", "按金额");
		GET_FEE_RATE_METHODS.put("2", "按天数");
		GET_FEE_RATE_METHODS.put("3", "按份额");
	}
	
	// 收益分配频率：1按月，2按季，3按半年，4按年，5到期分配，6其他（自加字段）
	public static final Map<String, String> SYFPPL_STATUS = new LinkedHashMap<String, String>();
	static {
		SYFPPL_STATUS.put("1", "按月");
		SYFPPL_STATUS.put("2", "按季");
		SYFPPL_STATUS.put("3", "按半年");
		SYFPPL_STATUS.put("4", "按年");
		SYFPPL_STATUS.put("5", "到期分配");
		SYFPPL_STATUS.put("6", "其他");
	}
	
	// 好买产品线：1现金管理,2债券,3固定收益,4对冲,5股票,6股权,7房地产,8其他
	public static final Map<String, String> HMCPXS_TYPE = new LinkedHashMap<String, String>();
	static {
		HMCPXS_TYPE.put("1", "现金管理");
		HMCPXS_TYPE.put("2", "债券");
		HMCPXS_TYPE.put("3", "固定收益");
		HMCPXS_TYPE.put("4", "对冲");
		HMCPXS_TYPE.put("5", "股票");
		HMCPXS_TYPE.put("6", "股权");
		HMCPXS_TYPE.put("7", "房地产");
		HMCPXS_TYPE.put("8", "其他");
	}
	
	// 产品状态：1发行,2发行结束,3成立,4交易,5结束
	public static final Map<String, String> CPZTS_STATUS = new LinkedHashMap<String, String>();
	static {
		CPZTS_STATUS.put("1", "发行");
		CPZTS_STATUS.put("2", "发行结束");
		CPZTS_STATUS.put("3", "成立");
		CPZTS_STATUS.put("4", "交易");
		CPZTS_STATUS.put("5", "结束");
	}
	
	// 产品预约状态：1尚未开始,2正在预约,3结束预约
	public static final Map<String, String> CPYYZTS_STATUS = new LinkedHashMap<String, String>();
	static {
		CPYYZTS_STATUS.put("1", "尚未开始");
		CPYYZTS_STATUS.put("2", "正在预约");
		CPYYZTS_STATUS.put("3", "结束预约");
	}
	
	// 销售状态：1待上线,2上线,3下线
	public static final Map<String, String> XSZTS_STATUS = new LinkedHashMap<String, String>();
	static {
		XSZTS_STATUS.put("1", "待上线");
		XSZTS_STATUS.put("2", "上线");
		XSZTS_STATUS.put("3", "下线");
	}
	
	// 组织形式：1公募,2信托,3有限合伙,4公募专户,5公募子公司专项,6管理计划,7券商资管,8期货资管,9复合型,10单账户证券,11单账户期货,12保险,13其他,14私募通道
	public static final Map<String, String> ZZXSS_TYPE = new LinkedHashMap<String, String>();
	static {
		ZZXSS_TYPE.put("1", "公募");
		ZZXSS_TYPE.put("2", "信托");
		ZZXSS_TYPE.put("3", "有限合伙");
		ZZXSS_TYPE.put("4", "公募专户");
		ZZXSS_TYPE.put("5", "公募子公司专项");
		ZZXSS_TYPE.put("6", "管理计划");
		ZZXSS_TYPE.put("7", "券商资管");
		ZZXSS_TYPE.put("8", "期货资管");
		ZZXSS_TYPE.put("9", "复合型");
		ZZXSS_TYPE.put("10", "单账户证券");
		ZZXSS_TYPE.put("11", "单账户期货");
		ZZXSS_TYPE.put("12", "保险");
		ZZXSS_TYPE.put("13", "其他");
		ZZXSS_TYPE.put("14", "私募通道");
	}
	
	// 策略类型：1主动股票型,2指数型,3纯债型,4偏债型,5短期理财型,6基础份额,7优先份额,8进取份额,
	// 9偏股型,10债券型,11其他,12标准混合型,13保本型,14保守混合型,15偏股型,16债券型,17劣后份额
	public static final Map<String, String> CLLXS_TYPE = new LinkedHashMap<String, String>();
	static {
		CLLXS_TYPE.put("1", "主动股票型");
		CLLXS_TYPE.put("2", "指数型");
		CLLXS_TYPE.put("3", "纯债型");
		CLLXS_TYPE.put("4", "偏债型");
		CLLXS_TYPE.put("5", "短期理财型");
		CLLXS_TYPE.put("6", "基础份额");
		CLLXS_TYPE.put("7", "优先份额");
		CLLXS_TYPE.put("8", "进取份额");
		CLLXS_TYPE.put("9", "偏股型");
		CLLXS_TYPE.put("10", "债券型");
		CLLXS_TYPE.put("11", "其他");
		CLLXS_TYPE.put("12", "标准混合型");
		CLLXS_TYPE.put("13", "保本型");
		CLLXS_TYPE.put("14", "保守混合型");
		CLLXS_TYPE.put("15", "偏股型");
		CLLXS_TYPE.put("16", "债券型");
		CLLXS_TYPE.put("17", "劣后份额");
	}
	
	// 投资类型：1股票型,2债券型,3结构型,4QDII,5混合型,6封闭式,7货币型,8普通股票型,9债券型,10多空仓型,11货币型,
	// 12宏观策略型,13市场中性型,14定向增发型,15套利型,16管理期货型,17多策略,18其他,19固定收益,20房地产基金,21PE/VC,22其他
	public static final Map<String, String> TZLXS_TYPE = new LinkedHashMap<String, String>();
	static {
		TZLXS_TYPE.put("1", "股票型");
		TZLXS_TYPE.put("2", "债券型");
		TZLXS_TYPE.put("3", "结构型");
		TZLXS_TYPE.put("4", "QDII");
		TZLXS_TYPE.put("5", "混合型");
		TZLXS_TYPE.put("6", "封闭式");
		TZLXS_TYPE.put("7", "货币型");
		TZLXS_TYPE.put("8", "普通股票型");
		TZLXS_TYPE.put("9", "债券型");
		TZLXS_TYPE.put("10", "多空仓型");
		TZLXS_TYPE.put("11", "货币型");
		TZLXS_TYPE.put("12", "宏观策略型");
		TZLXS_TYPE.put("13", "市场中性型");
		TZLXS_TYPE.put("14", "定向增发型");
		TZLXS_TYPE.put("15", "套利型");
		TZLXS_TYPE.put("16", "管理期货型");
		TZLXS_TYPE.put("17", "多策略");
		TZLXS_TYPE.put("18", "其他");
		TZLXS_TYPE.put("19", "固定收益");
		TZLXS_TYPE.put("20", "房地产基金");
		TZLXS_TYPE.put("21", "PE/VC");
		TZLXS_TYPE.put("22", "其他");
	}
	
	// 组织形式：1分销,2包销
	public static final Map<String, String> FXBXS_TYPE = new LinkedHashMap<String, String>();
	static {
		FXBXS_TYPE.put("1", "分销");
		FXBXS_TYPE.put("2", "包销");
	}
	
	// 组织形式：1政信类,2基础设施建设,3房地产,4股票质押,5其他
	public static final Map<String, String> XMLXS_TYPE = new LinkedHashMap<String, String>();
	static {
		XMLXS_TYPE.put("1", "政信类");
		XMLXS_TYPE.put("2", "基础设施建设");
		XMLXS_TYPE.put("3", "房地产");
		XMLXS_TYPE.put("4", "股票质押");
		XMLXS_TYPE.put("5", "其他");
	}
	
	// 利息分配：0无,1按月,2按季,3按半年,4按年,5到期一次性支付
	public static final Map<String, String> LXFPS_TYPE = new LinkedHashMap<String, String>();
	static {
		LXFPS_TYPE.put("0", "无");
		LXFPS_TYPE.put("1", "按月");
		LXFPS_TYPE.put("2", "按季");
		LXFPS_TYPE.put("3", "按半年");
		LXFPS_TYPE.put("4", "按年");
		LXFPS_TYPE.put("5", "到期一次性支付");
	}
	
	// 倒追形式：1是,2否
	public static final Map<String, String> DZS_TYPE = new LinkedHashMap<String, String>();
	static {
		DZS_TYPE.put("1", "是");
		DZS_TYPE.put("2", "否");
	}
	
	// 重点产品：1是,2否
	public static final Map<String, String> ZDCPS_TYPE = new LinkedHashMap<String, String>();
	static {
		ZDCPS_TYPE.put("1", "是");
		ZDCPS_TYPE.put("2", "否");
	}
	
	// 是否提前结束：1是,2否
	public static final Map<String, String> SFTQJSS_TYPE = new LinkedHashMap<String, String>();
	static {
		SFTQJSS_TYPE.put("1", "是");
		SFTQJSS_TYPE.put("2", "否");
	}
	
	// 产品是否计算存量：1是,2否
	public static final Map<String, String> ISCALBALS_TYPE = new LinkedHashMap<String, String>();
	static {
		ISCALBALS_TYPE.put("1", "是");
		ISCALBALS_TYPE.put("2", "否");
	}
	
	// 币种：1人民币,2外币                                                                      
	public static final Map<String, String> BZS_TYPE = new LinkedHashMap<String, String>();
	static {                                                                                 
		BZS_TYPE.put("1", "人民币");
		BZS_TYPE.put("2", "外币");
	}
	
	// 账户类型：1集合类,2账户类                                                                           
	public static final Map<String, String> ZHLXS_TYPE = new LinkedHashMap<String, String>();
	static {                                                                                 
		ZHLXS_TYPE.put("1", "集合类");
		ZHLXS_TYPE.put("2", "账户类");
	}
	
	// 投资区域：1集合类,2账户类                                                                           
	public static final Map<String, String> TZQYS_TYPE = new LinkedHashMap<String, String>();
	static {                                                                                 
		TZQYS_TYPE.put("1", "境内");
		TZQYS_TYPE.put("2", "境外");
		TZQYS_TYPE.put("3", "全球");
	}
	
	// 组合类型：0组合,1非组合                                                                           
	public static final Map<String, String> ZHS_TYPE = new LinkedHashMap<String, String>();
	static {                                                                                 
		ZHS_TYPE.put("0", "组合");
		ZHS_TYPE.put("1", "非组合");
	}
	
	// 结构化 类型：1结构化,2非结构化                                                                           
	public static final Map<String, String> JGHS_TYPE = new LinkedHashMap<String, String>();
	static {                                                                                 
		JGHS_TYPE.put("1", "结构化");
		JGHS_TYPE.put("2", "非结构化");
	}
	
	// 产品预约状态 ：1尚未开始,2正在预约,3结束预约                                                                        
	public static final Map<String, String> CPYYZTS_TYPE = new LinkedHashMap<String, String>();
	static {                                                                                 
		CPYYZTS_TYPE.put("1", "尚未开始");
		CPYYZTS_TYPE.put("2", "正在预约");
		CPYYZTS_TYPE.put("3", "结束预约");
	}
	
	// 发行方式 ：1公募,2私募                                                                         
		public static final Map<String, String> FXFSS_TYPE = new LinkedHashMap<String, String>();
		static {                                                                                 
			FXFSS_TYPE.put("1", "公募");
			FXFSS_TYPE.put("2", "私募");
		}
	
	// 产品结构(股权/房地产)类型：1纯股权,2股加债,3夹层,4纯债券                                                                         
	public static final Map<String, String> CPJGHS_TYPE = new LinkedHashMap<String, String>();
	static {                                                                                 
		CPJGHS_TYPE.put("1", "纯股权");
		CPJGHS_TYPE.put("2", "股加债");
		CPJGHS_TYPE.put("3", "夹层");
		CPJGHS_TYPE.put("4", "纯债券");
	}
		
	// 预约状态：0未完成，1已完成。2已取消
	public static final Map<String, String> BOOKING_STATUS = new LinkedHashMap<String, String>();
	static {
		BOOKING_STATUS.put("0", "未完成");
		BOOKING_STATUS.put("1", "已完成");
		BOOKING_STATUS.put("2", "已取消");
	}
	
	// 订阅服务类型
	public static final Map<String, String> SUBING_TYPE = new LinkedHashMap<String, String>();
	static {
		SUBING_TYPE.put("1000", "好买市场策略");
		SUBING_TYPE.put("1001", "好买精选基金");
		SUBING_TYPE.put("1002", "好买精选理财产品");
		SUBING_TYPE.put("1003", "好买精选信托");
		SUBING_TYPE.put("1223", "精选产品推荐");
		SUBING_TYPE.put("4501", "好买精选私募");
		SUBING_TYPE.put("1221", "每周基金动态");
		SUBING_TYPE.put("1224", "资产配置建议");
	}
	
	// 预约类型
	public static final Map<String, String> BOOKING_TYPE = new LinkedHashMap<String, String>();
	static {
		BOOKING_TYPE.put("1", "私募预约");
		BOOKING_TYPE.put("2", "合作预约");
	}
	
	// 活动类型
	public static final Map<String, String> ACTIVITY_TYPE = new LinkedHashMap<String, String>();
	static {
		ACTIVITY_TYPE.put("1", "我爱卡");
		ACTIVITY_TYPE.put("2", "我要牛基");
		ACTIVITY_TYPE.put("3", "新闻收集");
		ACTIVITY_TYPE.put("4", "基金诊断");
		ACTIVITY_TYPE.put("5", "货币基金导购");
		ACTIVITY_TYPE.put("6", "2012投资指南");
		ACTIVITY_TYPE.put("7", "掌上网预约");
		ACTIVITY_TYPE.put("8", "专题预约");
		ACTIVITY_TYPE.put("9", "UCWEB");
		ACTIVITY_TYPE.put("10", "联想无线");
		ACTIVITY_TYPE.put("11", "腾讯财经");
		ACTIVITY_TYPE.put("12", "微信产品预约");
		ACTIVITY_TYPE.put("13", "人物专访预约");
		ACTIVITY_TYPE.put("14", "凤凰网预约");
		ACTIVITY_TYPE.put("15", "aon");
		ACTIVITY_TYPE.put("16", "百度预约");
		ACTIVITY_TYPE.put("17", "私募活动讲座列表");
		ACTIVITY_TYPE.put("18", "掌机预约");
		ACTIVITY_TYPE.put("19", "sem专题预约");
		ACTIVITY_TYPE.put("20", "sem无线预约");
		ACTIVITY_TYPE.put("21", "新浪微财富");
		ACTIVITY_TYPE.put("22", "好买高端400(掌基)");
		ACTIVITY_TYPE.put("23", "臻财VIP");
		ACTIVITY_TYPE.put("24", "自选股预约");
		ACTIVITY_TYPE.put("25", "无线特定对象确认");
		ACTIVITY_TYPE.put("26", "华尔街见闻");
		ACTIVITY_TYPE.put("27", "华瑞银行");
		ACTIVITY_TYPE.put("28", "PC特定对象确认");
		ACTIVITY_TYPE.put("29", "线下-小区电梯屏幕");
		ACTIVITY_TYPE.put("30", "m站路演视频");
		ACTIVITY_TYPE.put("31", "小程序预约");
		ACTIVITY_TYPE.put("34", "私募资讯研报");
		ACTIVITY_TYPE.put("101", "私募基金类");
		ACTIVITY_TYPE.put("102", "公募基金类");
		ACTIVITY_TYPE.put("103", "固定收益类");
		ACTIVITY_TYPE.put("104", "私募股权类");
		ACTIVITY_TYPE.put("105", "其它");
		ACTIVITY_TYPE.put("106", "掌上基金预约");
		ACTIVITY_TYPE.put("107", "--");
		ACTIVITY_TYPE.put("108", "wap产品预约");
		ACTIVITY_TYPE.put("109", "信托大全预约");
		ACTIVITY_TYPE.put("110", "新固收一期");
		ACTIVITY_TYPE.put("111", "掌基音频预约");
		ACTIVITY_TYPE.put("112", "固收新品订阅通知");
		ACTIVITY_TYPE.put("2468", "今日头条");
	}
	
	
	// ivr类型Code
	public static final Map<String, String> IVR_TYPE = new LinkedHashMap<String, String>();
	static {
//		IVR_TYPE.put("百度低端", "3223");
//		IVR_TYPE.put("凤凰低端", "3231");
		IVR_TYPE.put("AON", "3320");// 先做兼容，后面删除此枚举，用下面的替换
		IVR_TYPE.put("同程产品合作", "3320");//原来是AON，现在调整为：同程产品合作，modify 2018-06-07
		IVR_TYPE.put("SEM", "3240");
		IVR_TYPE.put("凤凰高端", "3230");
		IVR_TYPE.put("腾讯高端", "3300");
//		IVR_TYPE.put("好买电子成单", "3300");
		IVR_TYPE.put("好买电子成单", "3406");//业务调整：将之前来源暂停，改对应关系为howbuy400-好买呼入-高端
		IVR_TYPE.put("腾讯400高端", "3310");
		IVR_TYPE.put("腾讯400低端", "3310");
		IVR_TYPE.put("腾讯", "3310");
		IVR_TYPE.put("百度高端", "3222");
//		IVR_TYPE.put("易天富", "3402");
//		IVR_TYPE.put("新浪微财富", "21");
		IVR_TYPE.put("华尔街见闻", "26");//业务调整：将之前的新浪微财富，改名为华尔街见闻modify 2016-06-21
		IVR_TYPE.put("好买高端掌基", "4023");		
//		IVR_TYPE.put("好买咨询高端产品", "3400");//业务调整：区分高端产品咨询到归为 howbuy400-好买呼入-高端 来源modify 2017-06-30
		IVR_TYPE.put("好买咨询高端产品", "3406");
		IVR_TYPE.put("好买咨询公墓基金", "3400");	
		IVR_TYPE.put("好买咨询公募基金", "3400");	
		IVR_TYPE.put("好买咨询交易及业务", "3400");
		IVR_TYPE.put("好买咨询活动", "3400");
		IVR_TYPE.put("好买投诉建议", "3400");
		IVR_TYPE.put("好买人工服务", "3400");
		IVR_TYPE.put("好买咨询储蓄罐", "3400");
//		IVR_TYPE.put("百度理财", "3224");
		IVR_TYPE.put("百度低端", "3224");//业务调整：调整为百度低端，沿用之前百度理财IVR modify 2016-07-14
		IVR_TYPE.put("换卡", "3403");//业务调整：新增换卡线路 add 2016-08-17
		IVR_TYPE.put("盗卡", "3404");//业务调整：新增盗卡线路 add 2016-08-17
		IVR_TYPE.put("腾讯理财通", "3405");//业务调整：易天富线路调整为腾讯理财通 modify 2016-09-27
	}
	
	// 商路通呼入客户来源Code
	public static final Map<String, String> CUST_SOURCE_TYPE = new LinkedHashMap<String, String>();
	static {
		CUST_SOURCE_TYPE.put("电话", "1");
		CUST_SOURCE_TYPE.put("微信", "1");
	}
	
	/**
	 * 转义产品销售方向方法
	 * @param typeNum 16位数字形式字符串
	 * @return String 转义后的字符串信息
	 */
	public static String convertProductType(String typeNum) {
		StringBuilder oldBuilder = new StringBuilder(typeNum);
		StringBuilder newBuilder = new StringBuilder();
		oldBuilder = oldBuilder.reverse();
		for (int i = 0; i < oldBuilder.length(); i++) {
			String temp = i + "_" + oldBuilder.charAt(i);
			if ("0_1".equals(temp)) {
				newBuilder.append("私募").append(" ");
			} else if ("1_1".equals(temp)) {
				newBuilder.append("公募专户").append(" ");
			} else if ("2_1".equals(temp)) {
				newBuilder.append("其他").append(" ");
			} else if ("3_1".equals(temp)) {
				newBuilder.append("一对多").append(" ");
			} else if ("4_1".equals(temp)) {
				newBuilder.append("VC/PE").append(" ");
			} else if ("5_1".equals(temp)) {
				newBuilder.append("TOT/FOF").append(" ");
			} else if ("6_1".equals(temp)) {
				newBuilder.append("固定收益").append(" ");
			} else if ("7_1".equals(temp)) {
				// newBuilder.append("").append(" ");
			} else if ("8_1".equals(temp)) {
				newBuilder.append("券商集合").append(" ");
			} else if ("9_1".equals(temp)) {
				// newBuilder.append("").append(" ");
			} else if ("10_1".equals(temp)) {
				// newBuilder.append("").append(" ");
			} else if ("11_1".equals(temp)) {
				newBuilder.append("公募").append(" ");
			} else if ("12_1".equals(temp)) {
				// newBuilder.append("").append(" ");
			} else if ("13_1".equals(temp)) {
				// newBuilder.append("").append(" ");
			} else if ("14_1".equals(temp)) {
				// newBuilder.append("").append(" ");
			} else if ("15_1".equals(temp)) {
				// newBuilder.append("").append(" ");
			}

		}
		return newBuilder.toString();
	}

	
}