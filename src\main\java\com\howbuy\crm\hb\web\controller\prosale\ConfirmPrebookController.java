package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.fastjson.JSON;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.prebook.dto.PreBookProdRelatedDto;
import com.howbuy.crm.prebook.request.PrebookTradeDealRequest;
import com.howbuy.crm.prebook.service.PrebookBusinessService;
import com.howbuy.crm.prebook.service.PrebookTradeDealService;
import com.howbuy.crm.prebook.vo.CmPreBookBankVo;
import com.howbuy.crm.prebook.vo.PreBookUpdateVo;
import com.howbuy.crm.prosale.dto.BankInfo;
import com.howbuy.crm.prosale.dto.CmPrebookExtend;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import com.howbuy.crm.prosale.request.GetInfoByParamRequest;
import com.howbuy.crm.prosale.response.GetCustBankInfoResponse;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import com.howbuy.simu.dto.business.product.RmbhlzjjDto;
import com.howbuy.simu.service.business.product.ComprehensiveService;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2019/8/6 13:19
 */
@Slf4j
@Controller
@RequestMapping("/prosale")
public class ConfirmPrebookController extends BaseController {



    @Autowired
    private PrebookproductinfoService prebookproductinfoService;
    @Autowired
    private QueryPreBookService queryPreBookService;
    @Autowired
    private QueryConscustInfoService queryConscustInfoService;
    @Autowired
    private ComprehensiveService comprehensiveService;

    @Autowired
	private DecryptSingleFacade decryptSingleFacade;
    @Autowired
    private JjxxInfoService jjxxInfoService;

    @Autowired
    private PrebookTradeDealService prebookTradeDealService;

    @Autowired
    private PrebookBusinessService prebookBusinessService;


    /**
     * @description:(构建 页面下拉框 map)
     * @param value	
     * @param text
     * @return java.util.Map<java.lang.String,java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/11/19 17:07
     * @since JDK 1.8
     */
    private static Map<String,String> buildMap (String value,String text){
        Map<String,String> map1 = new HashMap<>();
        map1.put("value",value);
        map1.put("text",text);
        return map1;
    }



    /**
     * 预约确认页面:
     * @param request
     * <AUTHOR>
     * @date 2019/8/7
     */
    @ResponseBody
    @RequestMapping("/confirmPrebook.do")
    public ModelAndView confirmPrebook(HttpServletRequest request){
        Map<String,Object> map = new HashMap<>();
        String id = request.getParameter("id");
        CmPrebookproductinfo info = prebookproductinfoService.selectPrebookproductinfoById(id);
        JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(info.getPcode(), false);
        map.put("concustname",info.getConscustname());
        map.put("concustno",info.getConscustno());
        map.put("pname",jjxx.getJjjc());
        map.put("currency",info.getCurrency());
        map.put("buyamt",info.getBuyamt());
        map.put("realbuyman",info.getRealbuyman());
        map.put("expecttradedt",info.getExpecttradedt());
        map.put("remarks",info.getRemarks());
        map.put("expectpayamtdt",info.getExpectpayamtdt());
        map.put("rateDt",info.getRatedt());
        if(info.getCurrency() != null && !"156".equals(info.getCurrency())) {
            RmbhlzjjDto rmbhlzjjDto = comprehensiveService.getRmbhlzjj(null, info.getCurrency());
            map.put("exchangerate", rmbhlzjjDto != null ? new BigDecimal(rmbhlzjjDto.getZjj()).multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_DOWN) : null);
        }else {
            map.put("exchangerate", 100);
        }
        map.put("tradeType",info.getTradeType());
        map.put("id",info.getId());
        map.put("prebookstate",info.getPrebookstate());
        map.put("sellvol",info.getSellvol());
        boolean dxflag = queryPreBookService.isDxflag(info.getConscustno(),info.getPcode());
        map.put("dxflag",dxflag);
        CmPrebookExtend cmPrebookExtend = null;
        if(!dxflag){
            cmPrebookExtend = prebookproductinfoService.getCmPrebookExtend(id);
        }
        if(cmPrebookExtend != null){
        	if(StringUtil.isNotNullStr(cmPrebookExtend.getBankacctCipher())){
    			CodecSingleResponse  resbank = decryptSingleFacade.decrypt(cmPrebookExtend.getBankacctCipher());
    			String bankacct = resbank.getCodecText();
    			map.put("bankacct", bankacct);
        	}
        	map.put("bankcode",cmPrebookExtend.getBankcode());
        	map.put("bankprov", cmPrebookExtend.getBankprov());
        	map.put("bankcity", cmPrebookExtend.getBankcity());
        	map.put("bankaddr", cmPrebookExtend.getBankaddr());
        }else{
        	map.put("bankacct", "");
        	map.put("bankcode","");
        	map.put("bankprov", "");
        	map.put("bankcity", "");
        	map.put("bankaddr", "");
        }

        List<Map<String, String>> preTypeList = Lists.newArrayList();
        ReturnMessageDto<PreBookProdRelatedDto>  relatedInfoResp=prebookBusinessService.getProdRelatedInfo(info.getConscustno(),
                info.getPcode(), info.getTradeType(),getLoginUserId());
        PreBookProdRelatedDto relatedDto= relatedInfoResp.getReturnObject();
        if(relatedDto!=null){
            if(CollectionUtils.isNotEmpty(relatedDto.getPreTypeEnumList())){
                relatedDto.getPreTypeEnumList().forEach(preTypeEnum -> {
                    preTypeList.add(buildMap(preTypeEnum.getCode(),preTypeEnum.getDescription()));
                });
            }
        }

        map.put("typeList",preTypeList);

        map.put("pretype",info.getPretype());
        QueryConscustInfoRequest conscustInfoRequest = new QueryConscustInfoRequest();
        conscustInfoRequest.setConscustno(info.getConscustno());
        QueryConscustInfoResponse conscustInfoResponse = queryConscustInfoService.queryConscustInfo(conscustInfoRequest);
        ConscustInfoDomain cust = conscustInfoResponse.getConscustinfo();
        log.info("conscustInfoDao is " + JSON.toJSONString(cust));
        if(cust != null && StringUtil.isNotNullStr(cust.getIdnoCipher())){
        	CodecSingleResponse  res = decryptSingleFacade.decrypt(cust.getIdnoCipher());
        	String idno = res.getCodecText();
			cust.setIdno(idno);
        }
        map.put("conscust",cust);
        GetInfoByParamRequest bankreq = new GetInfoByParamRequest();
        bankreq.setConscustno(info.getConscustno());
        GetCustBankInfoResponse bankresponse = queryPreBookService.getCustBankInfo(bankreq);
        List<Map<String,Object>> bankList = new ArrayList<>();
        if(bankresponse.isSuccessful()) {
            for (BankInfo bankInfo : bankresponse.getBankInfoList()) {
                Map<String, Object> bank = new HashMap<>();
                bank.put("id", bankInfo.getBankacct());
                bank.put("text", bankInfo.getBankacct() + ' ' + bankInfo.getBankname());
                bank.put("bankprov", bankInfo.getBankprov());
                bank.put("bankcity", bankInfo.getBankcity());
                bank.put("bankaddr", bankInfo.getBankaddr());
                bank.put("bankcode", bankInfo.getBankcode());
                bank.put("bankname", bankInfo.getBankname());
                bankList.add(bank);
            }
            map.put("bankSize",bankList.size());
        }else {
            map.put("bankSize",0);
        }
        map.put("bankList",bankList);
        return new ModelAndView("prosale/confirmPrebookProduct","info",map);
    }


    /**
     * 币种改变后获取DB汇率:
     * @param currency
     * <AUTHOR>
     * @date 2019/8/8
     */
    @RequestMapping("/getDBRateByCurAndDt")
    @ResponseBody
    public Object getDBRate(String currency){
        Map<String,Object> result = new HashMap<>();
        RmbhlzjjDto rmbhlzjjDto = comprehensiveService.getRmbhlzjj(null,currency);
        if(rmbhlzjjDto != null){
            result.put("code",0);
            result.put("zjj",new BigDecimal(rmbhlzjjDto.getZjj()).multiply(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_DOWN));
        }else {
            result.put("code",1);
        }
        return result;
    }


    /**
     * 预约确认保存:
     * @param request
     * <AUTHOR>
     * @date 2019/8/7
     */
    @RequestMapping("/saveConfirmPrebook.do")
    @ResponseBody
    public ReturnMessageDto<String> saveConfirmPrebook(HttpServletRequest request){
        String id = request.getParameter("id");
        String exchangerate = request.getParameter("exchangerate");
        String amt = request.getParameter("amt");
        String sellvol = request.getParameter("sellvol");
        String prepaydt = request.getParameter("prepaydt");
        String pretrddt = request.getParameter("pretrddt");
        String remarks = request.getParameter("remarks");
        String currency = request.getParameter("currency");
        String linkman = request.getParameter("linkman");
        String bankno = request.getParameter("bankno");
        String branchaccount = request.getParameter("branchaccount");
        String depositbank = request.getParameter("depositbank");
        String provCode = request.getParameter("provCode");
        String cityCode = request.getParameter("cityCode");
        String pretype = request.getParameter("pretype");

        //-----------------------------------------
        //构建 请求参数
        PrebookTradeDealRequest dealRequest = new PrebookTradeDealRequest();
        dealRequest.setPreId(new BigDecimal(id));
        dealRequest.setOperator(getLoginUserId());

        //预约属性更新 相关
        PreBookUpdateVo preBookUpdateVo= new PreBookUpdateVo();
        preBookUpdateVo.setExpectPayAmtDt(prepaydt);
        preBookUpdateVo.setExpectTradeDt(pretrddt);
        preBookUpdateVo.setRemarks(remarks);
        preBookUpdateVo.setPreType(pretype);
        if(StringUtil.isNotNullStr(sellvol)){
            preBookUpdateVo.setSellVol(new BigDecimal(sellvol));
        }
        if(StringUtil.isNotNullStr(amt)){
            preBookUpdateVo.setBuyAmt(new BigDecimal(amt).multiply(new BigDecimal(10000)).setScale(6));
        }
        if(StringUtil.isNotNullStr(currency)) {
            preBookUpdateVo.setCurrency(currency);
        }
        if(StringUtil.isNotNullStr(linkman)) {
            preBookUpdateVo.setRealBuyMan(linkman);
        }


         //银行卡信息
        CmPreBookBankVo bankVo = new CmPreBookBankVo();
        if(StringUtil.isNotNullStr(bankno)) {
            bankVo.setBankAcct(bankno);
        }
        if(StringUtil.isNotNullStr(depositbank)) {
            bankVo.setBankCode(depositbank);
        }
        if(StringUtil.isNotNullStr(provCode)) {
            bankVo.setBankProv(provCode);
        }
        if(StringUtil.isNotNullStr(cityCode)) {
            bankVo.setBankCity(cityCode);
        }
        if(StringUtil.isNotNullStr(branchaccount)) {
            bankVo.setBankAddr(branchaccount);
        }

        dealRequest.setPreBookUpdateVo(preBookUpdateVo);
        dealRequest.setBankVo(bankVo);
        return prebookTradeDealService.executeConfirmPrebook(dealRequest);
    }

    @RequestMapping("/batchConfirmPreBook")
    @ResponseBody
    public ReturnMessageDto<String> batchConfirm(HttpServletRequest request,String ids){
        if(ids == null){
            return ReturnMessageDto.fail("请选择预约数据！");
        }
        String[] idArr = ids.split(",");
        StringBuilder sb=new StringBuilder();
        for(String id : idArr){
            try {
                PrebookTradeDealRequest dealRequest = new PrebookTradeDealRequest();
                dealRequest.setPreId(new BigDecimal(id));
                dealRequest.setOperator(getLoginUserId());
                ReturnMessageDto<String>  dealResp= prebookTradeDealService.executeConfirmPrebook(dealRequest);
                if(!dealResp.isSuccess()){
                    sb.append(String.format("预约编号：%s,失败原因：%s",id,dealResp.getReturnMsg())).append(HTML_BREAK_LINE);
                }
            }catch (Exception e){
                log.error(e.getMessage(), e);
                sb.append(String.format("预约编号：%s,失败原因：系统异常",id)).append(HTML_BREAK_LINE);
            }
        }

        //包含错误信息
        if(sb.length()>0){
            return ReturnMessageDto.fail(sb.toString());
        }else{
            return ReturnMessageDto.ok();
        }
    }
}
