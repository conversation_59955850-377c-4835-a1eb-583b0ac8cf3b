package com.howbuy.crm.hb.web.controller.pushmsg;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import com.howbuy.crm.hb.domain.pushmsg.CmMsgBusinessTemplate;
import com.howbuy.crm.hb.domain.pushmsg.CmMsgWeixinTemplate;
import com.howbuy.crm.hb.domain.pushmsg.CmMsgWeixinTemplateParam;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.pushmsg.CmMsgBusinessTemplateService;
import com.howbuy.crm.hb.service.pushmsg.CmMsgWeixinTemplateParamService;
import com.howbuy.crm.hb.service.pushmsg.CmMsgWeixinTemplateService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.db.PageData;

/**
 * @Description: Controller
 * @version 1.0
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/cmMsgWeixinTemplate")
public class CmMsgWeixinTemplateController {
	
	@Autowired
	private CmMsgWeixinTemplateService cmMsgWeixinTemplateService;
	
	@Autowired
	private CmMsgWeixinTemplateParamService cmMsgWeixinTemplateParamService;
	
	@Autowired
	private CmMsgBusinessTemplateService cmMsgBusinessTemplateService;
	
	@Autowired
	private CommonService commonService;
	
	@RequestMapping("/listCmMsgWeixinTemplate.do")
	public ModelAndView listCmMsgWeixinTemplate(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();        
        modelAndView.setViewName("/pushmsg/listCmMsgWeixinTemplate");
        return modelAndView;
	}
	
	
	/**
	 * 企业微信模板查询
	 * @param request
	 * @return
	 * @throws Exception
	 */
    @ResponseBody
    @RequestMapping("/queryCmMsgWeixinTemplateList")
    public Map<String, Object> queryCmMsgWeixinTemplateList(HttpServletRequest request) throws Exception {
    	// 设置查询分页参数
    	Map<String, Object> resultMap = new HashMap<String, Object>(8);
    	Map<String, String> paramMap = new ParamUtil(request).getParamMap();
    	String title = request.getParameter("title");
    	paramMap.put("title", StringUtils.isNotBlank(title) ? title : null);
        PageData<CmMsgWeixinTemplate> pd = cmMsgWeixinTemplateService.listCmMsgWeixinTemplateByPage(paramMap);
        List<CmMsgWeixinTemplate> listCmMsgWeixinTemplate =  pd.getListData();
        if(CollectionUtils.isNotEmpty(listCmMsgWeixinTemplate)){
        	Map<String,String> param = new HashMap<String,String>(8);
        	for(CmMsgWeixinTemplate cmMsgWeixinTemplate : listCmMsgWeixinTemplate){
        		BigDecimal id = cmMsgWeixinTemplate.getId();
        		param.clear();
                param.put("weixinTemplateId", id.toString());
                param.put("isWeixinTemplate", "1");
                List<CmMsgBusinessTemplate> listCmMsgBusinessTemplate = cmMsgBusinessTemplateService.listBusinessTemplate(param);
                if(CollectionUtils.isNotEmpty(listCmMsgBusinessTemplate)){
                	cmMsgWeixinTemplate.setIsBusinessTemplate("1");
                }
        	}
        }
        resultMap.put("total", pd.getPageBean().getTotalNum());
        resultMap.put("rows", pd.getListData());
        
        return resultMap;
    }
    
    /**
     * 跳转到addCmMsgWeixinTemplate页面方法
     * @return String
     */
    @ResponseBody
    @RequestMapping("/addCmMsgWeixinTemplate")
    public ModelAndView addCmMsgWeixinTemplate(HttpServletRequest request) throws Exception {
        return new ModelAndView("pushmsg/addCmMsgWeixinTemplate");
    }
    
    /**
	 * 新增企业微信模板
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/saveCmMsgWeixinTemplate.do", method = RequestMethod.POST)
	@ResponseBody
    public Map<String, Object> saveCmMsgWeixinTemplate(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>(8);
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        
        
		String title = request.getParameter("title");
		Map<String,String> param = new HashMap<String,String>(8);
		param.put("title", title);
		CmMsgWeixinTemplate template = cmMsgWeixinTemplateService.getCmMsgWeixinTemplate(param);
		if(template != null){
			 resultMap.put("errorMsg", "模板标题重复！");
		     resultMap.put("errorCode", "9999");
		     return resultMap;
		}
		
		User user = (User) request.getSession().getAttribute("loginUser");
		
		BigDecimal id = new BigDecimal(commonService.getSeqValue("SEQ_CM_MSG_MANAGE"));
		CmMsgWeixinTemplate cmMsgWeixinTemplate = new CmMsgWeixinTemplate();
		cmMsgWeixinTemplate.setId(id);
		cmMsgWeixinTemplate.setCreator(user.getUserId());
		cmMsgWeixinTemplate.setCreateTime(new Date());
		cmMsgWeixinTemplate.setUpdateTime(cmMsgWeixinTemplate.getCreateTime());
		cmMsgWeixinTemplate.setTitle(title);
		
		List<CmMsgWeixinTemplateParam> listCmMsgWeixinTemplateParam = new ArrayList<CmMsgWeixinTemplateParam>();
		String cmMsgWeixinTemplateParamStr = request.getParameter("cmMsgWeixinTemplateParamStr");
		if(StringUtils.isNotBlank(cmMsgWeixinTemplateParamStr)){
			cmMsgWeixinTemplateParamStr = cmMsgWeixinTemplateParamStr.substring(0, cmMsgWeixinTemplateParamStr.length()-1);
			String[]  arr = cmMsgWeixinTemplateParamStr.split(";");
			for(String str : arr){
				String[] paramStrArr = str.split(":");
				CmMsgWeixinTemplateParam cmMsgWeixinTemplateParam = new CmMsgWeixinTemplateParam();
				cmMsgWeixinTemplateParam.setTemplateId(id);
				cmMsgWeixinTemplateParam.setCode(paramStrArr[0]);
				cmMsgWeixinTemplateParam.setName(paramStrArr[1]);
				cmMsgWeixinTemplateParam.setCreateTime(new Date());
				cmMsgWeixinTemplateParam.setCreator(user.getUserId());
				listCmMsgWeixinTemplateParam.add(cmMsgWeixinTemplateParam);
			}
		}
		
		cmMsgWeixinTemplateService.insertCmMsgWeixinTemplate(cmMsgWeixinTemplate, listCmMsgWeixinTemplateParam);

        return resultMap;
    }
    
    
    
    /**
	 * 删除
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/delCmMsgWeixinTemplate.do", method = RequestMethod.POST)
	@ResponseBody
    public Map<String, Object> delCmBxChannel(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>(8);
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");

        String id = request.getParameter("id");
        if (StringUtils.isNotBlank(id)) {
        	cmMsgWeixinTemplateService.delCmMsgWeixinTemplate(id);
        }
        return resultMap;
    }
	
	
	/**
     * 跳转到修改页面方法
     * @return String
     */
    @ResponseBody
    @RequestMapping("/viewUpdateCmMsgWeixinTemplate")
    public ModelAndView viewUpdateCmMsgWeixinTemplate(HttpServletRequest request) throws Exception {
    	String id = request.getParameter("id");
    	Map<String, Object> map = new HashMap<String, Object>(8);
    	
    	Map<String,String> param = new HashMap<String,String>(8);
        param.put("id", id);
        CmMsgWeixinTemplate cmMsgWeixinTemplate = cmMsgWeixinTemplateService.getCmMsgWeixinTemplate(param);
        
        param.clear();
        param.put("templateId", id);
        List<CmMsgWeixinTemplateParam> listCmMsgWeixinTemplateParam = cmMsgWeixinTemplateParamService.listCmMsgWeixinTemplateParam(param);
        map.put("cmMsgWeixinTemplate", cmMsgWeixinTemplate);
        map.put("listCmMsgWeixinTemplateParam", listCmMsgWeixinTemplateParam);
        map.put("upid",id);
        
        return new ModelAndView("pushmsg/updateCmMsgWeixinTemplate","map", map);
    }
	
	
	/**
	 * 修改企业微信模板
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/updateCmMsgWeixinTemplate.do", method = RequestMethod.POST)
	@ResponseBody
    public Map<String, Object> updateCmMsgWeixinTemplate(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>(8);
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        
        String id = request.getParameter("id");
		String title = request.getParameter("title");
		Map<String,String> param = new HashMap<String,String>(8);
		param.put("title", title);
		CmMsgWeixinTemplate template = cmMsgWeixinTemplateService.getCmMsgWeixinTemplate(param);
		if(template != null && !id.equals(template.getId().toString())){
			 resultMap.put("errorMsg", "模板标题重复！");
		     resultMap.put("errorCode", "9999");
		     return resultMap;
		}
		
		User user = (User) request.getSession().getAttribute("loginUser");

		CmMsgWeixinTemplate cmMsgWeixinTemplate = new CmMsgWeixinTemplate();
		cmMsgWeixinTemplate.setId(new BigDecimal(id));
		cmMsgWeixinTemplate.setUpdateMan(user.getUserId());
		cmMsgWeixinTemplate.setUpdateTime(new Date());
		cmMsgWeixinTemplate.setTitle(title);
		
		List<CmMsgWeixinTemplateParam> listCmMsgWeixinTemplateParam = new ArrayList<CmMsgWeixinTemplateParam>();
		
		String cmMsgWeixinTemplateParamStr = request.getParameter("cmMsgWeixinTemplateParamStr");
		if(StringUtils.isNotBlank(cmMsgWeixinTemplateParamStr)){
			cmMsgWeixinTemplateParamStr = cmMsgWeixinTemplateParamStr.substring(0, cmMsgWeixinTemplateParamStr.length()-1);
			String[]  arr = cmMsgWeixinTemplateParamStr.split(";");
			for(String str : arr){
				String[] paramStrArr = str.split(":");
				CmMsgWeixinTemplateParam cmMsgWeixinTemplateParam = new CmMsgWeixinTemplateParam();
				cmMsgWeixinTemplateParam.setTemplateId(new BigDecimal(id));
				cmMsgWeixinTemplateParam.setCode(paramStrArr[0]);
				cmMsgWeixinTemplateParam.setName(paramStrArr[1]);
				cmMsgWeixinTemplateParam.setCreateTime(new Date());
				cmMsgWeixinTemplateParam.setCreator(user.getUserId());
				listCmMsgWeixinTemplateParam.add(cmMsgWeixinTemplateParam);
			}
		}

		cmMsgWeixinTemplateService.updateCmMsgWeixinTemplate(cmMsgWeixinTemplate, listCmMsgWeixinTemplateParam);

        return resultMap;
    }
	
	
	/**
     * 跳转到详情页面方法
     * @return String
     */
    @ResponseBody
    @RequestMapping("/viewCmMsgWeixinTemplate")
    public ModelAndView viewCmMsgWeixinTemplate(HttpServletRequest request) throws Exception {
    	String id = request.getParameter("id");
    	Map<String, Object> map = new HashMap<String, Object>(8);
    	
    	Map<String,String> param = new HashMap<String,String>(8);
        param.put("id", id);
        CmMsgWeixinTemplate cmMsgWeixinTemplate = cmMsgWeixinTemplateService.getCmMsgWeixinTemplate(param);
        
        param.clear();
        param.put("templateId", id);
        List<CmMsgWeixinTemplateParam> listCmMsgWeixinTemplateParam = cmMsgWeixinTemplateParamService.listCmMsgWeixinTemplateParam(param);
        
        param.clear();
        param.put("weixinTemplateId", id);
        param.put("isWeixinTemplate", "1");
        List<CmMsgBusinessTemplate> listCmMsgBusinessTemplate = cmMsgBusinessTemplateService.listBusinessTemplate(param);
        
        
        map.put("cmMsgWeixinTemplate", cmMsgWeixinTemplate);
        map.put("listCmMsgWeixinTemplateParam", listCmMsgWeixinTemplateParam);
        map.put("listCmMsgBusinessTemplate", listCmMsgBusinessTemplate);
        
        
        return new ModelAndView("pushmsg/viewCmMsgWeixinTemplate","map", map);
    }

    @RequestMapping("/listWeixinTemplateById")
	@ResponseBody
    public Object listWeixinTemplateById(String templateId){
    	Map param = new HashMap(2);
    	param.put("templateId", templateId);
    	return cmMsgWeixinTemplateParamService.listCmMsgWeixinTemplateParam(param);
	}
}