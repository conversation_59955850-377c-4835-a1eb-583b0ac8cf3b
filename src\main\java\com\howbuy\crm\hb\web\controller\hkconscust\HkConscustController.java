package com.howbuy.crm.hb.web.controller.hkconscust;

import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.domain.hkconscust.HkConscust;
import com.howbuy.crm.hb.domain.hkconscust.SearchHkConscustVo;
import com.howbuy.crm.hb.service.hkconscust.HkConscustService;
import com.howbuy.crm.hb.web.controller.BaseController;
import crm.howbuy.base.db.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

/**
 * 香港客户维护
 * <AUTHOR>
 * @date 2022/4/20 13:42
 */
@Slf4j
@RequestMapping("/hkconscust")
@Controller
public class HkConscustController  extends BaseController {

    @Autowired
    private HkConscustService hkConscustService;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;


    @RequestMapping("/listHkConscust.do")
    public ModelAndView listAssociationMail(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/hkconscust/listHkConscust");
        return modelAndView;
    }

    @GetMapping("/getconscustbycustno")
    @ResponseBody
    public String getconscustbycustno(HttpServletRequest request) {
        String conscustno = request.getParameter("conscode");
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(conscustno);
        QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        if (null != queryConscustInfoResponse.getConscustinfo()) {
            return queryConscustInfoResponse.getConscustinfo().getConscode();
        } else {
            return "";
        }
    }

    @RequestMapping("/listHkConscustByPage.do")
    @ResponseBody
    public PageResult<HkConscust> listHkConscustByPage(SearchHkConscustVo searchHkConscustVo) {
        // 返回查询结果
        PageResult<HkConscust> pageData = new PageResult<>();
        try {
            pageData = hkConscustService.listHkConscustByPage(searchHkConscustVo);
        } catch (Exception e) {
            log.error("查询出错", e);
        }
        return pageData;
    }

    /**
     * 显示新增或修改页面
     * @return
     */
    @RequestMapping("/showAddOrEdit.do")
    public ModelAndView showAddOrEdit(@RequestParam(required = false) String id) {
        ModelAndView modelAndView = new ModelAndView();
        if (StringUtils.isNotEmpty(id)) {
            HkConscust hkConscust = hkConscustService.findHkConscustById(id);
            modelAndView.addObject("hkConscust", hkConscust);
            modelAndView.setViewName("/hkconscust/editHkConscust");
        } else {
            modelAndView.setViewName("/hkconscust/addHkConscust");
        }
        return modelAndView;
    }

    @RequestMapping("/updateHkConscust.do")
    @ResponseBody
    public ReturnMessageDto<Integer> updateHkConscust(HkConscust hkConscust) {
        return ReturnMessageDto.fail("暂不支持业务处理！");
    }

    @RequestMapping("/deleteHkConscust.do")
    @ResponseBody
    public ReturnMessageDto<Integer> deleteHkConscust(String id) {
        return ReturnMessageDto.fail("暂不支持业务处理！");
    }
}
