package com.howbuy.crm.hb.web.controller.prosale;

import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.domain.prosale.zxtraderecordaudit.BatchUpdatePrivatefundtradeAuditStatusVo;
import com.howbuy.crm.hb.domain.prosale.zxtraderecordaudit.CmPrivatefundtradeAudit;
import com.howbuy.crm.hb.domain.prosale.zxtraderecordaudit.SearchCmPrivatefundtradeAuditVo;
import com.howbuy.crm.hb.service.prosale.CmPrivatefundtradeAuditService;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import crm.howbuy.base.db.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;

/**
 * 直销交易记录审核
 * <AUTHOR>
 * @date 2022/6/2 11:26
 */
@Slf4j
@RequestMapping("/zxtraderecordaudit")
@Controller
public class CmPrivatefundtradeAuditController {

    @Autowired
    private CmPrivatefundtradeAuditService cmPrivatefundtradeAuditService;

    /**
     * 初始化直销交易记录审核列表
     * @param request
     * @return
     */
    @RequestMapping("/listPrivatefundtradeAudit.do")
    public ModelAndView listAssociationMail(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/prosale/zxtraderecordaudit/listPrivatefundtradeAudit");
        return modelAndView;
    }

    /**
     * 分页查询直销交易记录审核列表
     * @param searchCmPrivatefundtradeAuditVo
     * @return
     */
    @RequestMapping("/listPrivatefundtradeAuditByPage.do")
    @ResponseBody
    public PageResult<CmPrivatefundtradeAudit> listHkConscustByPage(HttpServletRequest request, SearchCmPrivatefundtradeAuditVo searchCmPrivatefundtradeAuditVo) {
        // 返回查询结果
        PageResult<CmPrivatefundtradeAudit> pageData = new PageResult<>();
        try {
            List<String> gdfxcplist = (List<String>) request.getSession().getAttribute("gdfxcplist");
            if (CollectionUtils.isNotEmpty(gdfxcplist) && gdfxcplist.size() == 1 && gdfxcplist.contains("4")) {
                searchCmPrivatefundtradeAuditVo.setSfxg("1");
            }
            pageData = cmPrivatefundtradeAuditService.listCmPrivatefundtradeAuditByPage(searchCmPrivatefundtradeAuditVo);
        } catch (Exception e) {
            log.error("查询出错", e);
        }
        return pageData;
    }

    /**
     * 新增直销交易记录审核记录
     * @param cmPrivatefundtradeAudit
     * @return
     */
    @RequestMapping("/insertCmPrivatefundtradeAudit.do")
    @ResponseBody
    public ReturnMessageDto<Integer> insertCmPrivatefundtradeAudit(CmPrivatefundtradeAudit cmPrivatefundtradeAudit) {
        try {
            return cmPrivatefundtradeAuditService.insertCmPrivatefundtradeAudit(cmPrivatefundtradeAudit);
        } catch (Exception e) {
            log.error("新增数据异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }

    /**
     * 修改直销交易记录审核记录
     * @param cmPrivatefundtradeAudit
     * @return
     */
    @RequestMapping("/updateCmPrivatefundtradeAudit.do")
    @ResponseBody
    public ReturnMessageDto<Integer> updateCmPrivatefundtradeAudit(CmPrivatefundtradeAudit cmPrivatefundtradeAudit) {
        try {
            return cmPrivatefundtradeAuditService.updateCmPrivatefundtradeAudit(cmPrivatefundtradeAudit);
        } catch (Exception e) {
            log.error("修改数据异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }

    /**
     * 修改直销交易记录审核记录的审核状态
     * @param cmPrivatefundtradeAudit
     * @return
     */
    @RequestMapping("/updateAuditStatus.do")
    @ResponseBody
    public ReturnMessageDto<Integer> updateAuditStatus(CmPrivatefundtradeAudit cmPrivatefundtradeAudit) {
        try {
            return cmPrivatefundtradeAuditService.updateAuditStatus(cmPrivatefundtradeAudit);
        } catch (Exception e) {
            log.error("审核异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }

    /**
     * 批量修改直销交易记录审核记录的审核状态
     * @param batchUpdateVo
     * @return
     */
    @RequestMapping("/batchUpdateAuditStatus.do")
    @ResponseBody
    public ReturnMessageDto<Integer> batchUpdateAuditStatus(@RequestBody BatchUpdatePrivatefundtradeAuditStatusVo batchUpdateVo) {
        try {
            return cmPrivatefundtradeAuditService.batchUpdateAuditStatus(batchUpdateVo);
        } catch (Exception e) {
            log.error("批量审核异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }


    /**
     * 直销交易记录审核记录-作废
     * @param auditid
     * @return
     */
    @RequestMapping("/invalidate.do")
    @ResponseBody
    public ReturnMessageDto<Integer> invalidate(String auditid) {
        try {
            return cmPrivatefundtradeAuditService.invalidate(auditid);
        } catch (Exception e) {
            log.error("作废操作异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }

    /**
     * 批量作废前的验证
     * @param auditidList
     * @return
     */
    @RequestMapping("/beforeBatchInvalidateCheck.do")
    @ResponseBody
    public ReturnMessageDto<Void> beforeInvalidateCheck(@RequestBody List<String> auditidList) {
        try {
            return cmPrivatefundtradeAuditService.beforeBatchInvalidateCheck(auditidList);
        } catch (Exception e) {
            log.error("beforeInvalidateCheck调用异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }

    /**
     * 直销交易记录审核记录-批量作废
     * @param auditidList
     * @return
     */
    @RequestMapping("/batchInvalidate.do")
    @ResponseBody
    public ReturnMessageDto<Integer> batchInvalidate(@RequestBody List<String> auditidList) {
        try {
            return cmPrivatefundtradeAuditService.batchInvalidate(auditidList);
        } catch (Exception e) {
            log.error("批量作废操作异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }

    /**
     * 导出数据
     * @return
     */
    @RequestMapping("/export.do")
    @ResponseBody
    public ReturnMessageDto<Integer> export(SearchCmPrivatefundtradeAuditVo searchCmPrivatefundtradeAuditVo, HttpServletResponse response) {
        OutputStream os = null;
        try {
            List<CmPrivatefundtradeAudit> exportList = cmPrivatefundtradeAuditService.listCmPrivatefundtradeAudit(searchCmPrivatefundtradeAuditVo);

            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition", "attachment;fileName=" + new String("直销操作记录.xls".getBytes("gb2312"), "ISO8859-1"));
            os = response.getOutputStream();
            ExcelWriter.writeExcel(os, "直销操作记录", 0, exportList, new String[][]{
                    {"交易类别", "tradetypeName"},
                    {"客户姓名", "custname"},
                    {"投顾客户号", "conscustno"},
                    {"产品代码", "fundcode"},
                    {"产品名称", "fundname"},
                    {"确认日期", "tradedt"},
                    {"确认净值", "nav"},
                    {"确认份额", "ackvol"},
                    {"确认金额", "ackamt"},
                    {"转让份额", "transferprice"},
                    {"是否海外特殊处理", "ishaiwai"},
                    {"是否发送短信", "ismessage"},
                    {"备注", "discsummary"},
                    {"修改人", "modifierName"},
                    {"修改时间", "moddt"},
                    {"审核状态", "auditstatusName"},
                    {"审核人", "auditorName"},
                    {"审核时间", "auditdt"},
            });
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return ReturnMessageDto.ok();
    }

    /**
     * 显示审核页面前的校验
     * @return
     */
    @RequestMapping("/beforeShowAuditPageCheck.do")
    @ResponseBody
    public ReturnMessageDto<Void> beforeShowAuditPageCheck(String auditid) {
        try {
            return cmPrivatefundtradeAuditService.beforeShowAuditPageCheck(auditid);
        } catch (Exception e) {
            log.error("beforeShowEditPageCheck调用异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }

    /**
     * 显示批量审核弹窗前的验证
     * @param auditidList
     * @return
     */
    @RequestMapping("/beforeShowBatchAuditWinCheck.do")
    @ResponseBody
    public ReturnMessageDto<Void> beforeShowBatchAuditWinCheck(@RequestBody List<String> auditidList) {
        try {
            return cmPrivatefundtradeAuditService.beforeShowBatchAuditWinCheck(auditidList);
        } catch (Exception e) {
            log.error("beforeShowBatchAuditWinCheck调用异常", e);
            return ReturnMessageDto.fail("系统异常！");
        }
    }

    /**
     * 显示审核页面
     * @return
     */
    @RequestMapping("/showAuditPage.do")
    public ModelAndView showAuditPage(String auditid) {
        ModelAndView modelAndView = new ModelAndView();
        CmPrivatefundtradeAudit privatefundtradeAudit = cmPrivatefundtradeAuditService.findCmPrivatefundtradeAuditById(auditid);
        modelAndView.addObject("privatefundtradeAudit", privatefundtradeAudit);
        modelAndView.setViewName("/prosale/zxtraderecordaudit/privatefundtradeAudit");
        return modelAndView;
    }

    /**
     * 显示编辑页面
     * @param auditid
     * @return
     */
    @RequestMapping("/showEditPage.do")
    public ModelAndView showEditPage(String auditid) {
        ModelAndView modelAndView = new ModelAndView();
        CmPrivatefundtradeAudit privatefundtradeAudit = cmPrivatefundtradeAuditService.findCmPrivatefundtradeAuditById(auditid);
        modelAndView.addObject("privatefundtradeAudit", privatefundtradeAudit);
        modelAndView.setViewName("/prosale/zxtraderecordaudit/editPrivatefundtradeAudit");
        return modelAndView;
    }
}
