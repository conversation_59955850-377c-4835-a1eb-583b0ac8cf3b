package com.howbuy.crm.hb.web.controller.custinfo;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.crm.hb.domain.callout.CsCalloutRec;
import com.howbuy.crm.hb.domain.custinfo.*;
import com.howbuy.crm.hb.persistence.common.CommonMapper;
import com.howbuy.crm.hb.service.callout.CsCalloutRecService;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.*;
import com.howbuy.crm.hb.service.system.HbUserroleService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.reader.ExcelReader;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import jxl.Sheet;
import jxl.Workbook;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @version 1.0
 * @Description: 客户管理 Controller
 */
@Slf4j
@Controller
@RequestMapping(value = "/custreview")
public class CmHighCustReviewController  extends BaseController {

    @Autowired
    private ConscustService conscustService;

    @Autowired
    private ConscustIcService conscustIcService;

	@Autowired
	private CmRealConsultantService cmRealConsultantService;

    @Autowired
    private CmCustfamilyService cmCustfamilyService;
    
    @Autowired
    private HbUserroleService hbUserroleService;
    
    @Autowired
    private CommonService commonService;
    
    @Autowired
    private CmNormalCustReviewService cmNormalCustReviewService;
    
    @Autowired
    private CmReviewRecordCountService cmReviewRecordCountService;
     
    @Autowired
    private CmNormalCustReviewLogService cmNormalCustReviewLogService;
    
    @Autowired
	private CsCalloutRecService csCalloutRecService;
    
    @Autowired
	private CommonMapper commonMapper;
    
    @Autowired
	private DecryptSingleFacade decryptSingleFacade;
    
    private final String DOWNLOAD_FILE_NAME="常规回访生成任务导入模板.xls";
    
    private final String MODEL_FILE_NAME="normalcustreview.xls";
    
    @RequestMapping("/listHighCustReview.do")
    public ModelAndView listHighCustReview(HttpServletRequest request) {
    	ModelAndView modelAndView = new ModelAndView();   
    	modelAndView.setViewName("/custinfo/highcustreviewList");
    	//查看有没有生成任务的权限
    	List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
    	String candeal = "";
    	for (String role : roles) {
    	List<String> temp = AuthCache.getInstance().getOperListStr(role, "050302");
	        if (temp != null && temp.contains("2")) {
	        	candeal = "true";
	            break;
	        }
    	}
    	Map<String,String> mapuser = new HashMap<String,String>();
    	//查询050302-3的人员
    	if("true".equals(candeal)){
    		Map<String,String> param = new HashMap<String,String>();
    		param.put("isvalid", "0");//不包括已离职用户
    		List<Map<String,String>> listmap = hbUserroleService.listUserByCustReview(param);
    		for(Map<String,String> map : listmap){
    			mapuser.put(map.get("CONSCODE"), map.get("CONSNAME"));
    		}
    	}
    	modelAndView.addObject("mapuser", mapuser);
        return modelAndView;
    }

    //投顾客户查询
    @ResponseBody
    @RequestMapping("/highcustreviewList")
    public Map<String, Object> highcustreviewList(HttpServletRequest request) throws Exception{
    	// 设置查询分页参数
    	Map<String, String> params = buildParam(request);
    	String email = params.get("email");
    	String mobile = params.get("mobile");
    	if(StringUtils.isNotBlank(email)){
    		params.put("email", DigestUtil.digest(email.trim()));
    	}
        if(StringUtils.isNotBlank(mobile)){
        	params.put("mobile", DigestUtil.digest(mobile.trim()));
    	}
        Map<String, Object> resultMap = new HashMap<String, Object>();
        PageData<Conscust> pd = conscustService.selectReviewConscustByPage(params);
        List<Conscust> list = pd.getListData();
        for (Conscust v : list) {
            v.setCityname(ConstantCache.getInstance().getProvCityMap().get(v.getCitycode()));
            if (StringUtils.isNotBlank(v.getVisittime())) {
                v.setVisittime(v.getVisittime().substring(0, 10).replaceAll("-", ""));
            }
            if (StringUtils.isNotBlank(v.getConscode())) {
				ConsOrgCache orgcache = ConsOrgCache.getInstance();
                v.setConsname(orgcache.getAllConsMap().get(v.getConscode()));
                v.setOrgname(orgcache.getAllOrgMap().get(orgcache.getCons2OutletMap().get(v.getConscode())));

				String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(v.getConscode()));
				if("0".equals(uporgcode)){
					v.setUporgname(v.getOrgname());
				}else{
					v.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
				}
            }
            v.setConscustlvl(ConstantCache.getInstance().getVal("custlevel", v.getConscustlvl()));
        }
        resultMap.put("total", pd.getPageBean().getTotalNum());
        resultMap.put("rows", pd.getListData());
        return resultMap;
    }

    //导出操作
    @RequestMapping("/exportCust.do")
    public void exportCust(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
    	String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
    	if (StringUtils.isNotBlank(ids)) {
            String[] custs = ids.split(",");
            //传过来的客户号
            List<String> list = new ArrayList<String>();
            //将数组转list
            CollectionUtils.addAll(list, custs);
            // 检查打标的客户是否有在申请划转的客户中
            Map<String,String> paramsql = new HashMap<String,String>();
            String sqlins = Util.getOracleSQLIn(list,999,"t.conscustno");
            paramsql.put("sqlins", sqlins);
            List<Conscust> exportList = conscustService.selectExportReviewCust(paramsql);
            if(CollectionUtils.isNotEmpty(exportList)){
            	for(Conscust v : exportList){
            		v.setCityname(ConstantCache.getInstance().getProvCityMap().get(v.getCitycode()));
                    if (StringUtils.isNotBlank(v.getVisittime())) {
                        v.setVisittime(v.getVisittime().substring(0, 10).replaceAll("-", ""));
                    }
                    if (StringUtils.isNotBlank(v.getConscode())) {
						ConsOrgCache orgcache = ConsOrgCache.getInstance();
						v.setConsname(orgcache.getAllConsMap().get(v.getConscode()));
						v.setOrgname(orgcache.getAllOrgMap().get(orgcache.getCons2OutletMap().get(v.getConscode())));
						String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(v.getConscode()));
						if("0".equals(uporgcode)){
							v.setUporgname(v.getOrgname());
						}else{
							v.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
						}
                    }
            	}
            }
            try {
                // 清空输出流
                response.reset();
                // 设置文件格式和名字
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition",
                        "attachment;fileName=" + new String("客户回访.xls".getBytes("gb2312"), "ISO8859-1"));
                ServletOutputStream os = response.getOutputStream();

                String[] columnName = {"客户名称", "投顾客户号", "所在地区", "最近拜访日期", "拜访摘要", "首次分配日期", "所属投顾", "所属部门","所属区域", "客户来源"};

                String[] beanProperty = {"custname", "conscustno", "cityname", "visittime","visitsummary","assignconsdt","consname","orgname","uporgname","sourcename"};
                ExcelWriter.writeExcel(os, "客户", 0, exportList, columnName, beanProperty);
                os.close(); // 关闭流
            } catch (Exception e) {
                log.error("文件导出异常", e);
            }
    	}
    }
    
    public Map<String, String> buildParam(HttpServletRequest request) throws Exception {
        Map<String, String> pmap = new ParamUtil(request).getParamMap();
        String email = request.getParameter("email");
        if (StringUtils.isNotBlank(email)) {
            pmap.put("email", email.toLowerCase());
        }
        String custsource = request.getParameter("source");
        String isnotleaf = request.getParameter("isnotleaf");
        pmap.put("isnotleaf", isnotleaf);
        if (StringUtils.isNotBlank(custsource) && "false".equals(isnotleaf)) {
            pmap.put("newsourceno", custsource);
        } else if (StringUtils.isNotBlank(custsource) && "true".equals(isnotleaf)) {
            pmap.put("newsourceno", ObjectUtils.getCurrentSource(custsource));
        }
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        if (StringUtils.isNotBlank(consCode)) {
            pmap.put("conscode", consCode);
        } else {
            //选择了未分配组
            if (orgCode.startsWith("other")) {
                pmap.put("othertearm", orgCode.replaceFirst("other", ""));
            } else {
                String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
                //选择了团队
                if (StaticVar.ORGTYPE_TEAM.equals(orgType)) {
                    pmap.put("teamcode", orgCode);
                } else {
                    if (!"0".equals(orgCode)) {
                        List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                        pmap.put("outletcodes", ObjectUtils.getSqlInStr(suborgs));
                    }
                }
            }
        }
        String hasprodtype = request.getParameter("hasprodtype");
        String ishw = request.getParameter("ishw");
        if (StringUtils.isNotBlank(hasprodtype)) {
        	String[] arrtype = hasprodtype.split(",");
            pmap.put("hasprodtype", ObjectUtils.getSqlHasProdType(ishw, arrtype));
        }
        String specialcustflag = request.getParameter("specialcustflag");
        if (StringUtils.isNotBlank(specialcustflag)) {
        	String[] arrtype = specialcustflag.split(",");
            pmap.put("specialcustflag", ObjectUtils.getSqlSpecCustType(arrtype));
        }
        return pmap;
    }
    
    @RequestMapping("/batchCreatTask.do")
    @ResponseBody
    public String batchCreatTask(HttpServletRequest request){
    	User user = (User)request.getSession().getAttribute("loginUser");
    	String result="";
        String ids = request.getParameter("ids");
        String checkusers = request.getParameter("checkusers");
        String rviewtype = request.getParameter("viewtype");
        List<CmNormalCustReview> insertlist = new ArrayList<CmNormalCustReview>();
        String[] users = checkusers.split(",");
        int usercount = users.length;
        String[] custs = ids.split(",");
        //传过来的客户号
        List<String> list = new ArrayList<String>();
        //将数组转list
        CollectionUtils.addAll(list, custs);
        // 检查打标的客户是否有在申请划转的客户中
        Map<String,String> paramsql = new HashMap<String,String>();
        String sqlins = Util.getOracleSQLIn(list,999,"t.conscustno");
        paramsql.put("sqlins", sqlins);
        List<Map<String,String>> listcustlabel = conscustService.selectHightCustinfoByReview(paramsql);
        int userindex = 0;
        
        if(listcustlabel != null && listcustlabel.size() > 0){
        	Map<String,String> param = null;
            String dealstate = null;
        	for(Map<String,String> map : listcustlabel){
        		boolean status = true;
        		CmNormalCustReview review = new CmNormalCustReview();
        		review.setId(new BigDecimal(commonService.getSeqValue("SEQ_CS_COMMUNICATE_VISIT_ID")));
        		review.setConscustno(map.get("CONSCUSTNO"));
        		review.setConscode(map.get("CONSCODE"));
        		review.setReviewtype(rviewtype);
        		review.setDealstate(StaticVar.REVIEW_DEALSTATE_WAIT);
        		
        		//客户回访页生成回访任务时，判断常规回访任务列表中是否已存在同一客户同一回访类型且任务状态 = 待处理/再处理 的任务，若已存在，则跳过，不用提示
        		param = new HashMap<String,String>();
        		param.put("conscustno", map.get("CONSCUSTNO"));
        		param.put("reviewtype", rviewtype);
        		List<CmNormalCustReview>  listNormalCustReview = cmNormalCustReviewService.listCmNormalCustReviewByCondition(param);
        		if(CollectionUtils.isNotEmpty(listNormalCustReview)){
        			for(CmNormalCustReview cmNormalCustReview : listNormalCustReview){
        				if(cmNormalCustReview != null){
        					dealstate = cmNormalCustReview.getDealstate();
        					if("1".equals(dealstate) || "2".equals(dealstate)){
        						status = false;
        						break;
        					}
        				}
        			}
        		}
        		
        		if(status){
        			//如果是高端零存量客户或者高端0存量（3个月内）客户
            		if("1".equals(map.get("GDLCLLABEL")) || "1".equals(map.get("GDLCL3MLABEL"))){
            			//常规客户回访中的客户类型是零存量客户客户
            			review.setCusttype(StaticVar.REVIEW_CUSTTYPE_LCL);
            		//非高端零存量客户或者高端0存量（3个月内）客户，并且是高端成交客户
            		}else if("1".equals(map.get("GDCJLABEL"))){
            			//常规客户回访中的客户类型是高端持仓客户
            			review.setCusttype(StaticVar.REVIEW_CUSTTYPE_GDCL);
            		}else{
            			//其他情况常规客户回访中的客户类型是潜在客户
            			review.setCusttype(StaticVar.REVIEW_CUSTTYPE_QZ);
            		}
            		review.setCreator(user.getUserId());
            		//按顺序分配坐席处理
            		if(userindex >= usercount){
            			userindex = 0;
            		}
            		review.setOptor(users[userindex]);
            		userindex++;
            		insertlist.add(review);
        		}
        	}
        	cmNormalCustReviewService.batchinsertCmNormalCustReview(insertlist);
        }
		result = "success";
        return result;
    }
    
    /**
     * 常规回访任务
     * @param request
     * @return
     */
    @RequestMapping("/listCmNormalCustReview.do")
    public ModelAndView listCmNormalCustReview(HttpServletRequest request) {
    	ModelAndView modelAndView = new ModelAndView();   
    	modelAndView.setViewName("/custinfo/listCmNormalCustReview");
    	Map<String,Object> map = new HashMap<String,Object>();
    	User user = (User) request.getSession().getAttribute("loginUser");
    	//查看有没有生成任务的权限
    	List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
    	String isAlloptor = "";//处理人权限
    	String opt = "";//处理人
    	String upAll = "";//修改(全部)
    	String upFk = "";//修改(反馈)
    	
    	for (String role : roles) {
    		List<String> temp = AuthCache.getInstance().getOperListStr(role, "050303");
	        if (temp != null && temp.contains("1")) {
	        	isAlloptor = "true";
	        }
	        if (temp != null && temp.contains("4")) {
	        	opt = "true";
	        }
	        if (temp != null && temp.contains("5")) {
	        	upFk = "true";
	        }
	        if (temp != null && temp.contains("6")) {
	        	upAll = "true";
	        }
    	}

    	List<Map<String,Object>> optorList = new ArrayList<>();
    	Map<String, Object>  optormap = new HashMap<>();
    	if("true".equals(isAlloptor)){
    		Map<String,String> param = new HashMap<String,String>();
    		param.put("isvalid", "1");//包括已离职用户
    		List<Map<String,String>> optorlistmap = hbUserroleService.listUserByCustReview(param);
    		optormap = new HashMap<>();
			optormap.put("id", "");
			optormap.put("text", "全部");
			optorList.add(optormap);
    		for(Map<String,String> retMap : optorlistmap){
    			optormap = new HashMap<>();
    			optormap.put("id", retMap.get("CONSCODE"));
    			optormap.put("text", retMap.get("CONSNAME"));
    			optorList.add(optormap);
    		}
    	}else{
    		optormap = new HashMap<>();
			optormap.put("id", user.getUserId());
			optormap.put("text", user.getUserName());
			optorList.add(optormap);
    	}
    	map.put("optorList",optorList);
    	


    	List<Map<String,Object>> newoptorList = new ArrayList<>();
    	Map<String, Object>  newoptormap = null;
    	if("true".equals(opt)){
    		Map<String,String> param = new HashMap<String,String>();
    		param.put("isvalid", "0");//不包括已离职用户
    		List<Map<String,String>> optorlistmap = hbUserroleService.listUserByCustReview(param);
    		newoptormap = new HashMap<>();
    		newoptormap.put("id", "");
    		newoptormap.put("text", "全部");
    		newoptorList.add(newoptormap);
    		for(Map<String,String> retMap : optorlistmap){
    			newoptormap = new HashMap<>();
    			newoptormap.put("id", retMap.get("CONSCODE"));
    			newoptormap.put("text", retMap.get("CONSNAME"));
    			newoptorList.add(newoptormap);
    		}
    	}
    	map.put("newoptorList",newoptorList);
    	map.put("upAll",upAll);
    	map.put("upFk",upFk);
    	map.put("opt",opt);
    	map.put("userId",user.getUserId());
    	
    	
    	modelAndView.addObject("map", map);
        return modelAndView;
    }

    //常规回访任务查询
    @ResponseBody
    @RequestMapping("/cmNormalCustReviewList")
    public Map<String, Object> cmNormalCustReviewList(HttpServletRequest request) throws Exception{
    	// 设置查询分页参数
    	Map<String, String> params = buildNormalCustReviewParam(request);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        PageData<CmNormalCustReview> pd = cmNormalCustReviewService.selectCmNormalCustReviewByPage(params);
        List<CmNormalCustReview> list = pd.getListData();
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		ConstantCache constantCache= ConstantCache.getInstance();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (CmNormalCustReview v : list) {
        	v.setCreatdtstr(v.getCreatdt() == null ?  "" : sdf.format(v.getCreatdt()));
        	v.setOrgname(consOrgCache.getAllOrgMap().get(consOrgCache.getCons2OutletMap().get(v.getConscode())));
			String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(v.getConscode()));
			if("0".equals(uporgcode)){
				v.setUporgname(v.getOrgname());
			}else{
				v.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
			}
			String conscode = v.getConscode();
			Map<String, Object> param = new HashMap<>();
			param.put("conscustno", v.getConscustno());
			CmRealConsultant cmRealConsultant = cmRealConsultantService.getCmRealConsultant(param);
			if (null != cmRealConsultant && !conscode.equals(cmRealConsultant.getRealConscode())) {
				v.setIsrealconscode("0");
			}

        	v.setConscode(consOrgCache.getAllUserMap().get(v.getConscode()));
        	v.setCusttype(constantCache.getVal("custReviewCusttype", v.getCusttype()));
        	v.setReviewtype(constantCache.getVal("reviewtype", v.getReviewtype()));
        	v.setIswrong(constantCache.getVal("custReviewIswrong", v.getIswrong()));
        	v.setWrongdes(constantCache.getVal("custReviewWrongdes", v.getWrongdes()));
        	v.setIsconnect(constantCache.getVal("custReviewIsconnect", v.getIsconnect()));
        	v.setReviewdt(StringUtils.isEmpty(v.getReviewdt()) ? "" : DateTimeUtil.fmtDate(DateTimeUtil.strToDate(v.getReviewdt(),"yyyyMMddHHmmss"), "yyyy-MM-dd HH:mm:ss" ));
        	v.setDreviewdt2nd(StringUtils.isEmpty(v.getDreviewdt2nd()) ? "" : DateTimeUtil.fmtDate(DateTimeUtil.strToDate(v.getDreviewdt2nd(),"yyyyMMddHHmmss"), "yyyy-MM-dd HH:mm:ss" ));
        	v.setDreviewdt3rd(StringUtils.isEmpty(v.getDreviewdt3rd()) ? "" : DateTimeUtil.fmtDate(DateTimeUtil.strToDate(v.getDreviewdt3rd(),"yyyyMMddHHmmss"), "yyyy-MM-dd HH:mm:ss" ));
        }
        resultMap.put("total", pd.getPageBean().getTotalNum());
        resultMap.put("rows", pd.getListData());
        return resultMap;
    }
    
    
    
    public Map<String, String> buildNormalCustReviewParam(HttpServletRequest request) throws Exception {
        Map<String, String> param = new ParamUtil(request).getParamMap();
        String endReviewdt = request.getParameter("endReviewdt");
        if(StringUtils.isNotBlank(endReviewdt)){
        	endReviewdt = DateTimeUtil.getNextDayByStr(endReviewdt, "yyyyMMdd");
        	param.put("endReviewdt", endReviewdt);
        }
        String orgcode = request.getParameter("orgcode");
        String conscode = request.getParameter("conscode");
        // 设置查询条件（所属投顾-部门）
        if (StringUtils.isNotBlank(conscode)) {
        	param.put("conscode", conscode);
		} else {
			// 选择了未分配组
			if (orgcode.startsWith("other")) {
				param.put("othertearm", orgcode.replaceFirst("other", ""));
			} else {
				String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgcode);
				// 选择了团队
				if (StaticVar.ORGTYPE_TEAM.equals(orgType)) {
					param.put("teamcode", orgcode);
				} else {
					if (!StaticVar.ORGTYPE_OUTLET.equals(orgcode)) {
						List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgcode);
						param.put("outletcodes", Util.getSqlInStr(suborgs));
					}
				}
			}
		}

        return param;
    }
    
    
    
    //任务划转
	@ResponseBody
	@RequestMapping("/taskTransfer.do")
	public String taskTransfer(HttpServletRequest request) throws Exception {
		String result = "success";
		String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
		String newoptor = request.getParameter("newoptor");
		if (StringUtils.isNotBlank(ids) || StringUtils.isNotBlank(newoptor)) {
			ids = ids.replaceFirst(",", "");
			ids = "("+ids+")";
        	
        	// 设置查询参数
			Map<String, String> param = new HashMap<String, String>();
			param.put("ids", ids);
			param.put("optor", newoptor);
			cmNormalCustReviewService.updateCmNormalCustReviewByIds(param);	
		}else{
			result = "参数错误，请选择记录和新处理人！";
		}
		
	    return result;
	}
	
	//删除常规回访任务
	@ResponseBody
	@RequestMapping("/delNormalCustReview.do")
	public String delNormalCustReview(HttpServletRequest request) throws Exception {
		String result = "success";
		String id = request.getParameter("id");
		if (StringUtils.isNotBlank(id)) {
			cmNormalCustReviewService.delCmNormalCustReview(id);	
		}
		
	    return result;
	}
	
	//删除常规回访任务
	@ResponseBody
	@RequestMapping("/batchDelNormalCustReview.do")
	public String batchDelNormalCustReview(HttpServletRequest request) throws Exception {
		String result = "success";
		String ids = request.getParameter("ids");
		if (StringUtils.isNotBlank(ids)) {
			ids = ids.replaceFirst(",", "");
			ids = "("+ids+")";
			Map<String, String> param = new HashMap<>();
			param.put("ids", ids);
			cmNormalCustReviewService.batchDelCmNormalCustReview(param);	
		}
		
	    return result;
	}
	
	
	/**
	 * (跳转到常规回访任务修改界面)
	 */
	@ResponseBody
	@RequestMapping("/upCmNormalCustReviewView.do")
	public ModelAndView upCmNormalCustReviewView(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		String id = request.getParameter("id");
		String upAll = request.getParameter("upAll");
		String upFk = request.getParameter("upFk");
		CmNormalCustReview cmNormalCustReview = cmNormalCustReviewService.getCmNormalCustReviewMsgById(id);
		String conscode = cmNormalCustReview.getConscode();
		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		if(StringUtils.isNotBlank(conscode)){
			cmNormalCustReview.setOrgname(consOrgCache.getAllOrgMap().get(consOrgCache.getCons2OutletMap().get(conscode)));
			cmNormalCustReview.setConscode(consOrgCache.getAllUserMap().get(conscode));
		}
		

		Map<String,Object> map = new HashMap<String,Object>();
		map.put("cmNormalCustReview", cmNormalCustReview);
  		map.put("upAll", upAll);
  		map.put("upFk", upFk);
  		return new ModelAndView("custinfo/upCmNormalCustReview", "map", map);
	}
	
	
	/**
     * 保存常规回访任务
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveCmNormalCustReview.do", method = RequestMethod.POST)
    public Map<String, Object> saveCmNormalCustReview(HttpServletRequest request) throws Exception{
        Map<String, Object> resultMap = new HashMap<String, Object>();
        User user = (User) request.getSession().getAttribute("loginUser");
        String id = handStr(request.getParameter("id"));
        String custtype = handStr(request.getParameter("custtype"));
        String iswrong = handStr(request.getParameter("iswrong"));
        String wrongdes = handStr(request.getParameter("wrongdes"));
        String isfeedback = handStr(request.getParameter("isfeedback"));
        String feedbackcontent = handStr(request.getParameter("feedbackcontent"));

        CmNormalCustReview oldCustReview = cmNormalCustReviewService.getCmNormalCustReviewMsgById(id);
        
        CmNormalCustReview cmNormalCustReview = new CmNormalCustReview();
        cmNormalCustReview.setId(new BigDecimal(id));
        cmNormalCustReview.setCusttype(custtype);
        cmNormalCustReview.setIswrong(iswrong);
        cmNormalCustReview.setWrongdes(wrongdes);
        cmNormalCustReview.setIsfeedback(isfeedback);
        cmNormalCustReview.setFeedbackcontent(feedbackcontent);
        cmNormalCustReview.setIsconnect(oldCustReview.getIsconnect());
        cmNormalCustReview.setNotifymessagedt(oldCustReview.getNotifymessagedt());
        cmNormalCustReview.setCommunicate(oldCustReview.getCommunicate());
        cmNormalCustReview.setModifier(user.getUserId());
        cmNormalCustReview.setModifydt(new Date());
        
        cmNormalCustReview.setReviewdt(oldCustReview.getReviewdt());
        cmNormalCustReview.setDreviewdt2nd(oldCustReview.getDreviewdt2nd());
        cmNormalCustReview.setCommunicate2nd(oldCustReview.getCommunicate2nd());
        cmNormalCustReview.setDreviewdt3rd(oldCustReview.getDreviewdt3rd());
        cmNormalCustReview.setCommunicate3rd(oldCustReview.getCommunicate3rd());
        cmNormalCustReview.setNotconnectnum(oldCustReview.getNotconnectnum());
        cmNormalCustReview.setDealstate(oldCustReview.getDealstate());
       
        cmNormalCustReviewService.updatePartMsgById(cmNormalCustReview);
        resultMap.put("flag", "success");
        resultMap.put("errorMsg", "保持常规回访任务成功");
        
        return resultMap;
    }
    
    
    
    /**
	 * 导出列表数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/exportCmNormalCustReview.do")
	public Map<String, Object> exportCmNormalCustReview(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		// 获取选中的编号
		String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
		List<CmNormalCustReview> exportList = null;
		if (StringUtils.isNotBlank(ids)) {
			String[] idArr = ids.split(",");
			
			// 临时存储订单编号
			List<String> tempList = new ArrayList<String>();

			// 将数组转List
			CollectionUtils.addAll(tempList, idArr);

			// 查询用户选中的记录
			Map<String, String> param = new HashMap<String, String>();
			String sqlins = Util.getOracleSQLIn(tempList, 999, "T1.ID");
			param.put("sqlins", sqlins);
			exportList = cmNormalCustReviewService.listCmNormalCustReview(param);
		}

		// 判断导出List是否为空
		if (exportList != null) {
			ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
			ConstantCache constantCache = ConstantCache.getInstance();
			for (CmNormalCustReview info : exportList) {
				info.setCreatdtstr(info.getCreatdt() == null ? "" : DateTimeUtil.fmtDate(info.getCreatdt(), "yyyy-MM-dd HH:mm:ss"));
				info.setOrgname(consOrgCache.getAllOrgMap().get(consOrgCache.getCons2OutletMap().get(info.getConscode())));

				String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(info.getConscode()));
				if("0".equals(uporgcode)){
					info.setUporgname(info.getOrgname());
				}else{
					info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
				}

				info.setConscode(consOrgCache.getAllUserMap().get(info.getConscode()));
				info.setCusttype(constantCache.getVal("custReviewCusttype", info.getCusttype()));
				info.setReviewtype(constantCache.getVal("reviewtype", info.getReviewtype()));
				info.setIswrong(constantCache.getVal("custReviewIswrong", info.getIswrong()));
				info.setWrongdes(constantCache.getVal("custReviewWrongdes", info.getWrongdes()));
				info.setIsconnect(constantCache.getVal("custReviewIsconnect", info.getIsconnect()));
				info.setIsfeedback(constantCache.getVal("custReviewIsfeedback", info.getIsfeedback()));
				info.setReviewdt(StringUtils.isEmpty(info.getReviewdt()) ? "" : DateTimeUtil.fmtDate(DateTimeUtil.strToDate(info.getReviewdt(),"yyyyMMddHHmmss"), "yyyy-MM-dd HH:mm:ss" ));
				info.setDreviewdt2nd(StringUtils.isEmpty(info.getDreviewdt2nd()) ? "" : DateTimeUtil.fmtDate(DateTimeUtil.strToDate(info.getDreviewdt2nd(),"yyyyMMddHHmmss"), "yyyy-MM-dd HH:mm:ss" ));
				info.setDreviewdt3rd(StringUtils.isEmpty(info.getDreviewdt3rd()) ? "" : DateTimeUtil.fmtDate(DateTimeUtil.strToDate(info.getDreviewdt3rd(),"yyyyMMddHHmmss"), "yyyy-MM-dd HH:mm:ss" ));
				if("1".equals(info.getDealstate())){
					info.setDealstate("待处理");
				}else if("2".equals(info.getDealstate())){
					info.setDealstate("再处理");
				}else if("3".equals(info.getDealstate())){
					info.setDealstate("已处理");
				}
			}

			try {
				// 清空输出流
				response.reset();

				// 设置文件格式和名字
				response.setContentType("multipart/form-data");
				response.setHeader("Content-Disposition", "attachment;fileName=" + new String("常规回访任务.xls".getBytes("gb2312"), "ISO8859-1"));
				ServletOutputStream os = response.getOutputStream();

				String[] columnName = { "任务生成时间","客户姓名","投顾客户号", "所属投顾","所属部门","所属区域","所属中心","客户类型","回访类型","是否问题件", "问题件描述","是否接通","未接通次数",
						"通知短信","回访日期","沟通记录","第二次时间", "第二次沟通记录", "第三次时间", "第三次沟通记录", "是否反馈", "反馈内容", "处理人","处理状态"};
				String[] beanProperty = { "creatdtstr","custname","conscustno","conscode","orgname","uporgname","sszxname","custtype","reviewtype", "iswrong","wrongdes","isconnect","notconnectnum",
						"notifymessagedt", "reviewdt","communicate","dreviewdt2nd","communicate2nd","dreviewdt3rd","communicate3rd","isfeedback", "feedbackcontent","optorname","dealstate"};
				ExcelWriter.writeExcel(os, "常规回访任务", 0, exportList, columnName, beanProperty);
				os.close(); // 关闭流
			} catch (Exception e) {
				log.error("文件导出异常", e);
			}

			resultMap.put("msg", "success");
		} else {
			resultMap.put("msg", "noData");
		}

		return resultMap;
	}
	
	
	//检查全量导出的数据符不符合要求
	@ResponseBody
	@RequestMapping("/checkExportAllCmNormalCustReview.do")
	public String checkExportAllCmNormalCustReview(HttpServletRequest request) throws Exception {
		String result = "success";
		// 获取选中的编号
		Map<String, String> params = buildNormalCustReviewParam(request);
		List<CmNormalCustReview> exportList = cmNormalCustReviewService.listCmNormalCustReview(params);
		if(exportList == null ){
			result = "请先查询出需导出的数据";
		}else if(exportList != null && exportList.size() == 0){
			result = "请先查询出需导出的数据";
		}else if(exportList != null && exportList.size() > 10000){
			result = "数据量过大，请重新查询数据导出";
		}
	    return result;
	}
	
	/**
	 * 导出列表数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/exportAllCmNormalCustReview.do")
	public Map<String, Object> exportAllCmNormalCustReview(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		// 获取选中的编号
		Map<String, String> params = buildNormalCustReviewParam(request);
		List<CmNormalCustReview> exportList = cmNormalCustReviewService.listCmNormalCustReview(params);
		// 判断导出List是否为空
		if (exportList != null) {
			ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
			ConstantCache constantCache = ConstantCache.getInstance();
			for (CmNormalCustReview info : exportList) {
				info.setCreatdtstr(info.getCreatdt() == null ? "" : DateTimeUtil.fmtDate(info.getCreatdt(), "yyyy-MM-dd HH:mm:ss"));
				info.setOrgname(consOrgCache.getAllOrgMap().get(consOrgCache.getCons2OutletMap().get(info.getConscode())));

				String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(info.getConscode()));
				if("0".equals(uporgcode)){
					info.setUporgname(info.getOrgname());
				}else{
					info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
				}

				info.setConscode(consOrgCache.getAllUserMap().get(info.getConscode()));
				info.setCusttype(constantCache.getVal("custReviewCusttype", info.getCusttype()));
				info.setReviewtype(constantCache.getVal("custReviewReviewtype", info.getReviewtype()));
				info.setIswrong(constantCache.getVal("custReviewIswrong", info.getIswrong()));
				info.setWrongdes(constantCache.getVal("custReviewWrongdes", info.getWrongdes()));
				info.setIsconnect(constantCache.getVal("custReviewIsconnect", info.getIsconnect()));
				info.setIsfeedback(constantCache.getVal("custReviewIsfeedback", info.getIsfeedback()));
				info.setReviewdt(StringUtils.isEmpty(info.getReviewdt()) ? "" : DateTimeUtil.fmtDate(DateTimeUtil.strToDate(info.getReviewdt(),"yyyyMMddHHmmss"), "yyyy-MM-dd HH:mm:ss" ));
				info.setDreviewdt2nd(StringUtils.isEmpty(info.getDreviewdt2nd()) ? "" : DateTimeUtil.fmtDate(DateTimeUtil.strToDate(info.getDreviewdt2nd(),"yyyyMMddHHmmss"), "yyyy-MM-dd HH:mm:ss" ));
				info.setDreviewdt3rd(StringUtils.isEmpty(info.getDreviewdt3rd()) ? "" : DateTimeUtil.fmtDate(DateTimeUtil.strToDate(info.getDreviewdt3rd(),"yyyyMMddHHmmss"), "yyyy-MM-dd HH:mm:ss" ));
				if("1".equals(info.getDealstate())){
					info.setDealstate("待处理");
				}else if("2".equals(info.getDealstate())){
					info.setDealstate("再处理");
				}else if("3".equals(info.getDealstate())){
					info.setDealstate("已处理");
				}
			}

			try {
				// 清空输出流
				response.reset();

				// 设置文件格式和名字
				response.setContentType("multipart/form-data");
				response.setHeader("Content-Disposition", "attachment;fileName=" + new String("常规回访任务.xls".getBytes("gb2312"), "ISO8859-1"));
				ServletOutputStream os = response.getOutputStream();

				String[] columnName = { "任务生成时间","客户姓名","投顾客户号", "所属投顾","所属部门","所属区域","所属中心","客户类型","回访类型","是否问题件", "问题件描述","是否接通","未接通次数",
						"通知短信","回访日期","沟通记录","第二次时间", "第二次沟通记录", "第三次时间", "第三次沟通记录", "是否反馈", "反馈内容", "处理人","处理状态"};
				String[] beanProperty = { "creatdtstr","custname","conscustno","conscode","orgname","uporgname","sszxname","custtype","reviewtype", "iswrong","wrongdes","isconnect","notconnectnum",
						"notifymessagedt", "reviewdt","communicate","dreviewdt2nd","communicate2nd","dreviewdt3rd","communicate3rd","isfeedback", "feedbackcontent","optorname","dealstate"};
				ExcelWriter.writeExcel(os, "常规回访任务", 0, exportList, columnName, beanProperty);
				os.close(); // 关闭流
			} catch (Exception e) {
				log.error("文件导出异常", e);
			}

			resultMap.put("msg", "success");
		} else {
			resultMap.put("msg", "noData");
		}

		return resultMap;
	}
	
	
	/**
	 * (跳转到常规回访任务处理界面)
	 */
	@ResponseBody
	@RequestMapping("/handleCmNormalCustReviewView.do")
	public ModelAndView handleCmNormalCustReviewView(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		String id = request.getParameter("id");
		CmNormalCustReview cmNormalCustReview = cmNormalCustReviewService.getCmNormalCustReviewMsgById(id);
		String conscode = cmNormalCustReview.getConscode();
		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		if(StringUtils.isNotBlank(conscode)){
			cmNormalCustReview.setConscode(consOrgCache.getAllUserMap().get(conscode));
		}
		String encryptionMobile = cmNormalCustReview.getMobileMask();
		String encryptionLinkmobile = cmNormalCustReview.getLinkmobileMask();
		
		cmNormalCustReview.setEncryptionMobile(encryptionMobile);
		cmNormalCustReview.setEncryptionLinkmobile(encryptionLinkmobile);
		if(StringUtil.isNotNullStr(cmNormalCustReview.getMobileCipher())){
			cmNormalCustReview.setMobile(decryptSingleFacade.decrypt(cmNormalCustReview.getMobileCipher()).getCodecText());
		}
		
		if(StringUtil.isNotNullStr(cmNormalCustReview.getLinkmobileCipher())){
			cmNormalCustReview.setLinkmobile(decryptSingleFacade.decrypt(cmNormalCustReview.getLinkmobileCipher()).getCodecText());
		}

		Map<String,Object> map = new HashMap<String,Object>();
		map.put("cmNormalCustReview", cmNormalCustReview);
		
		map.put("iswrong", cmNormalCustReview.getIswrong());
		ConstantCache constantCache = ConstantCache.getInstance();
		map.put("reviewtypeValue", constantCache.getVal("custReviewReviewtype", cmNormalCustReview.getReviewtype()));
		map.put("startTime", DateTimeUtil.getCurDateTime());
  		return new ModelAndView("custinfo/handleCmNormalCustReviewView", "map", map);
	}
	
	
	/**
     * 保存常规回访任务
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/handleCmNormalCustReview.do", method = RequestMethod.POST)
    public Map<String, Object> handleCmNormalCustReview(HttpServletRequest request) throws Exception{
        Map<String, Object> resultMap = new HashMap<String, Object>();
        User user = (User) request.getSession().getAttribute("loginUser");
        String hisId = commonService.getSeqValue("SEQ_PCUSTREC");
        String conscustno = handStr(request.getParameter("conscustno"));
        
        String id = handStr(request.getParameter("id"));
        String custtype = handStr(request.getParameter("custtype"));
    	String visitType = handStr(request.getParameter("visitType"));
    	String visitclassify = handStr(request.getParameter("visitclassify"));
    	String iswrong = handStr(request.getParameter("iswrong"));
    	String wrongdes = handStr(request.getParameter("wrongdes"));
    	String isconnect = handStr(request.getParameter("isconnect"));
    	String notifymessagedt = handStr(request.getParameter("notifymessagedt"));
    	String communicate = handStr(request.getParameter("communicate"));

		String startTime = handStr(request.getParameter("startTime"));
    	//保存沟通记录
    	cmNormalCustReviewService.saveCsCommunicateVisit(id,visitclassify,visitType,user.getUserId(),conscustno,communicate,hisId);
    	String orderId = csCalloutRecService.getLastRecByTidAndTime(id, startTime);
		if(orderId != null) {
			//更新拨号记录以便客户信息沟通记录列表能查到录音
			CsCalloutRec csCalloutRec = new CsCalloutRec();
			csCalloutRec.setAppSerialNo(hisId);
			csCalloutRec.setId(orderId);
			csCalloutRecService.updateRecAppSerialNo(csCalloutRec);
		}

    	CmNormalCustReview oldCustReview = cmNormalCustReviewService.getCmNormalCustReviewMsgById(id);
    	BigDecimal notconnectnum = oldCustReview.getNotconnectnum();
    	CmNormalCustReview cmNormalCustReview = new CmNormalCustReview();
    	cmNormalCustReview.setId(new BigDecimal(id));
        cmNormalCustReview.setCusttype(custtype);
        cmNormalCustReview.setIswrong(iswrong);
        cmNormalCustReview.setWrongdes(wrongdes);
        cmNormalCustReview.setIsconnect(isconnect);
        cmNormalCustReview.setNotifymessagedt(notifymessagedt);
        cmNormalCustReview.setIsfeedback(oldCustReview.getIsfeedback());
        cmNormalCustReview.setFeedbackcontent(oldCustReview.getFeedbackcontent());
        cmNormalCustReview.setModifier(user.getUserId());
        cmNormalCustReview.setModifydt(new Date());
        
    	String dealstate = oldCustReview.getDealstate();
    	if(StaticVar.REVIEW_DEALSTATE_WAIT.equals(dealstate)){
    		cmNormalCustReview.setCommunicate(communicate);
    		cmNormalCustReview.setReviewdt(DateTimeUtil.fmtDate(new Date(), "yyyyMMddHHmmss"));
    		cmNormalCustReview.setDreviewdt2nd(oldCustReview.getDreviewdt2nd());
			cmNormalCustReview.setCommunicate2nd(oldCustReview.getCommunicate2nd());
			cmNormalCustReview.setDreviewdt3rd(oldCustReview.getDreviewdt3rd());
			cmNormalCustReview.setCommunicate3rd(oldCustReview.getCommunicate3rd());
    	}else if(StaticVar.REVIEW_DEALSTATE_REPEAT.equals(dealstate)){
    		cmNormalCustReview.setCommunicate(oldCustReview.getCommunicate());
    		cmNormalCustReview.setReviewdt(oldCustReview.getReviewdt());
    		if(notconnectnum != null && (new BigDecimal(1)).compareTo(notconnectnum) == 0){
    			cmNormalCustReview.setDreviewdt2nd(DateTimeUtil.fmtDate(new Date(), "yyyyMMddHHmmss"));
    			cmNormalCustReview.setCommunicate2nd(communicate);
    			cmNormalCustReview.setDreviewdt3rd(oldCustReview.getDreviewdt3rd());
    			cmNormalCustReview.setCommunicate3rd(oldCustReview.getCommunicate3rd());
    		}else{
    			cmNormalCustReview.setDreviewdt2nd(oldCustReview.getDreviewdt2nd());
    			cmNormalCustReview.setCommunicate2nd(oldCustReview.getCommunicate2nd());
    			cmNormalCustReview.setDreviewdt3rd(DateTimeUtil.fmtDate(new Date(), "yyyyMMddHHmmss"));
    			cmNormalCustReview.setCommunicate3rd(communicate);
    		}
    	}
    	
    	BigDecimal tempnotconnectnum = notconnectnum;
    	if(StaticVar.REVIEW_ISCONNECT_Y.equals(isconnect)){
    		dealstate = "3";
    	}else{
    		tempnotconnectnum = notconnectnum == null ? new BigDecimal(1) : (new BigDecimal(1)).add(notconnectnum);
    		CmReviewRecordCount cmReviewRecordCount = new CmReviewRecordCount();
			cmReviewRecordCount.setCusttype(custtype);
			cmReviewRecordCount.setReviewtype(oldCustReview.getReviewtype());
			CmReviewRecordCount model = cmReviewRecordCountService.getCmReviewRecordCount(cmReviewRecordCount);
			if(model != null){
				BigDecimal recordcount = model.getRecordcount();
				if(tempnotconnectnum.compareTo(recordcount) >= 0){
					dealstate = "3";
				}else{
					dealstate = "2";
				}
			}else{
				dealstate = "2";
			}
    	}
    	
    	cmNormalCustReview.setNotconnectnum(tempnotconnectnum);
    	cmNormalCustReview.setDealstate(dealstate);
    	if(StaticVar.REVIEW_ISWRONG_YES_CLASSSOP.equals(cmNormalCustReview.getIswrong()) || StaticVar.REVIEW_ISWRONG_YES_NOTSOP.equals(cmNormalCustReview.getIswrong())){
    		if(StringUtil.isNullStr(cmNormalCustReview.getIsfeedback())){
    			cmNormalCustReview.setIsfeedback(StaticVar.REVIEW_ISFEEDBACK_NO);
    		}
    	}
        cmNormalCustReviewService.updatePartMsgById(cmNormalCustReview);
        
        
        //保存操作日志
        CmNormalCustReviewLog cmNormalCustReviewLog = new CmNormalCustReviewLog();
        String logid = commonMapper.getSeqValue("SEQ_NORMAL_CUST_REVIEW_LOG_ID ").toString();
        cmNormalCustReviewLog.setId(new BigDecimal(logid));
        cmNormalCustReviewLog.setReviewid(new BigDecimal(id));
        cmNormalCustReviewLog.setConscustno(oldCustReview.getConscustno());
        cmNormalCustReviewLog.setIswrong(iswrong);
        cmNormalCustReviewLog.setWrongdes(wrongdes);
        cmNormalCustReviewLog.setIsconnect(isconnect);
        cmNormalCustReviewLog.setNotifymessagedt(notifymessagedt);
        cmNormalCustReviewLog.setCommunicate(communicate);
        cmNormalCustReviewLog.setOptor(user.getUserId());
        cmNormalCustReviewLog.setOptd(new Date());
        cmNormalCustReviewLog.setCreator(oldCustReview.getCreator());
        cmNormalCustReviewLog.setCreatdt(oldCustReview.getCreatdt());
        cmNormalCustReviewLogService.insertCmNormalCustReviewLog(cmNormalCustReviewLog);
        resultMap.put("flag", "success");
        resultMap.put("errorMsg", "处理常规回访任务成功");
        
        return resultMap;
    }
    
    private String handStr(String str){
    	if(StringUtils.isEmpty(str)){
    		str = "";
    	}
    	if("null".equals(str)){
    		str = "";
    	}
    	return str;
    }
    
    @RequestMapping("/dowLoadModelNormalCustReview.do")
	public String dowLoadModelNormalCustReview( HttpServletRequest request,
			HttpServletResponse response) {
		return dowmloadTemplate(MODEL_FILE_NAME,DOWNLOAD_FILE_NAME,request,response);
	}
    
    @RequestMapping(value="/importNormalCustReview.do",method=RequestMethod.POST)
	public  @ResponseBody Map<String, Object> importNormalCustReview(HttpServletRequest request){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		User user = (User)request.getSession().getAttribute("loginUser");
		
		InputStream input = null;
		Workbook workBook = null;
		
		String errorMsg = "";
		String uploadFlag = "success";
		
		try {  
			// 转型为MultipartHttpRequest：  
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;  
			// 获得文件：  
			MultipartFile file = multipartRequest.getFile("file");   
			// 获得输入流：  
			input = file.getInputStream();  
			workBook = Workbook.getWorkbook(input);
			List<CmNormalCustReview> list = new ArrayList<CmNormalCustReview>();
			for (Sheet sheet : workBook.getSheets()) {
				//在第二行开始遍历
				for(int i = 1;i < sheet.getRows();i++){
					CmNormalCustReview vo = new CmNormalCustReview();
					vo.setConscustno(ExcelReader.getValue(sheet.getCell(0, i)).trim());
					vo.setReviewtype(ExcelReader.getValue(sheet.getCell(1, i)).trim());
					vo.setOptor(ExcelReader.getValue(sheet.getCell(2, i)).trim());
					list.add(vo);
				}
			}
			StringBuilder  sb = new StringBuilder();
			if(list != null && list.size() > 0){
				//判断回访类型、客户号、操作人必填项
				for(CmNormalCustReview vo : list){
					if(StringUtil.isNullStr(vo.getConscustno()) || StringUtil.isNullStr(vo.getReviewtype()) || StringUtil.isNullStr(vo.getOptor())){
						sb.append("投顾客户号、回访类型、操作人为必填项！");
						break;
					}
				}
				if(StringUtil.isNotNullStr(sb.toString())){
					resultMap.put("uploadFlag", "error");  
					resultMap.put("errorMsg", sb.toString());
					return resultMap;
				}
				List<String> reviewtypes = new ArrayList<String>();
				reviewtypes.add(StaticVar.REVIEW_TYPE_RCHF);
				reviewtypes.add(StaticVar.REVIEW_TYPE_KHGS);
				reviewtypes.add(StaticVar.REVIEW_TYPE_KHLY);
				reviewtypes.add(StaticVar.REVIEW_TYPE_TGLZHF);
				reviewtypes.add(StaticVar.REVIEW_TYPE_ZXHF);
				reviewtypes.add(StaticVar.REVIEW_TYPE_LEADS);
				reviewtypes.add(StaticVar.REVIEW_TYPE_20);
				List<CmNormalCustReview> insertreview = new ArrayList<>();
				List<String> repeatlist = new ArrayList<>();
				//判断每条记录的完整性
				for(CmNormalCustReview vo : list){
					String conscustno = vo.getConscustno();
					StringBuilder sb1 = new StringBuilder();
					//查询投顾客户号是否存在
					// 检查打标的客户是否有在申请划转的客户中
			        Map<String,String> paramsql = new HashMap<String,String>();
			        paramsql.put("sqlins", " t.conscustno='"+conscustno+"' ");
			        List<Map<String,String>> listcustlabel = conscustService.selectHightCustinfoByReview(paramsql);
			        if (listcustlabel == null || (listcustlabel != null && listcustlabel.size()==0)){
			        	addmesg(conscustno,sb1,"客户号不存在");
			        }
					//判断回访类型是否超出范围
					if(!reviewtypes.contains(vo.getReviewtype())){
						addmesg(conscustno,sb1,"回访类型不在范围之类");
					}
					//判断操作人是否投顾
					if(!StringUtil.isNotNullStr(ConsOrgCache.getInstance().getAllUserMap().get(vo.getOptor()))){
						addmesg(conscustno,sb1,"处理人"+vo.getOptor()+"不存在");
					}
					if(StringUtil.isNotNullStr(sb1.toString())){
						sb.append(sb1.toString()+".</br>");
					}else{
						Map<String,String> map = listcustlabel.get(0);
						vo.setId(new BigDecimal(commonService.getSeqValue("SEQ_CS_COMMUNICATE_VISIT_ID")));
						vo.setConscode(map.get("CONSCODE"));
						vo.setDealstate(StaticVar.REVIEW_DEALSTATE_WAIT);
		        		//如果是高端0存量客户NEW标签（1：是）客户
		        		if("1".equals(map.get("GDLCLNEWLABEL"))){
		        			//常规客户回访中的客户类型是零存量客户客户
		        			vo.setCusttype(StaticVar.REVIEW_CUSTTYPE_LCL);
		        		//高端存量客户标签（1：是）客户
		        		}else if("1".equals(map.get("GDCLLABEL"))){
		        			//常规客户回访中的客户类型是高端持仓客户
		        			vo.setCusttype(StaticVar.REVIEW_CUSTTYPE_GDCL);
		        		}else{
		        			//其他情况常规客户回访中的客户类型是潜在客户
		        			vo.setCusttype(StaticVar.REVIEW_CUSTTYPE_QZ);
		        		}
		        		vo.setCreator(user.getUserId());
		        		//去除重复的数据，客户号和回访类型
						if(!repeatlist.contains(vo.getConscustno()+vo.getReviewtype())){
							repeatlist.add(vo.getConscustno()+vo.getReviewtype());
							//查询是否存在相同客户号和回访类，并且处理状态是待处理和再处理的，如果存在就不做任何操作
							Map<String,String> paramreapet = new HashMap<>();
							paramreapet.put("conscustno", vo.getConscustno());
							paramreapet.put("reviewtype", vo.getReviewtype());
							if(cmNormalCustReviewService.getIsHasCustReviewCount(paramreapet) == 0){
								insertreview.add(vo);
							}
						}
					}
				}
				if(StringUtil.isNotNullStr(sb.toString())){
					resultMap.put("uploadFlag", "error");  
					resultMap.put("errorMsg", sb.toString());
					return resultMap;
				}else{
					cmNormalCustReviewService.batchinsertCmNormalCustReview(insertreview);
					log.info(user.getUserId()+"：导入成功！");
				}
				
			}else{
				resultMap.put("uploadFlag", "error");  
				resultMap.put("errorMsg", "请输入导入的内容！");
				return resultMap;
			}
			resultMap.put("uploadFlag", uploadFlag);  
			resultMap.put("errorMsg", errorMsg);  
			
	     } catch (Exception e) {   
	            e.printStackTrace();  
	            resultMap.put("uploadFlag", "error");  
	            resultMap.put("errorMsg", "请检查模板是否正确");  
	     }finally{
	    	try {
	    		if(input != null){
	    			input.close();
	    		}
			} catch (IOException e) {
				e.printStackTrace();
			}
	     }
		 
		return resultMap;
	}
    
    private void addmesg(String conscustno,StringBuilder sb1,String msg){
		if(StringUtil.isNotNullStr(sb1.toString())){
			sb1.append("、"+msg);
		}else{
			sb1.append("客户号"+conscustno+"："+msg);
		}
	}
}
