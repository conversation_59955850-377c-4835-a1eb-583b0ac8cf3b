package com.howbuy.crm.hb.web.controller.conference;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.crm.hb.domain.conference.CmConference;
import com.howbuy.crm.hb.domain.conference.CmConferenceConscust;
import com.howbuy.crm.hb.domain.conference.ConferenceConscustImportEntity;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.enums.ConferenceTypeEnum;
import com.howbuy.crm.hb.service.associationmail.AssociationMailService;
import com.howbuy.crm.hb.service.callout.CsCommunicateVisitService;
import com.howbuy.crm.hb.service.conference.CmConferenceConscustService;
import com.howbuy.crm.hb.service.conference.CmConferenceService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.controller.cache.CacheCode;
import com.howbuy.crm.hb.web.controller.cache.CacheUtil;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.request.ConferenceCustCancelVo;
import com.howbuy.crm.nt.conference.service.CmConferenceCustService;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ExcelUtils;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.page.framework.utils.ValidateUtil;
import com.howbuy.crm.trade.common.response.BaseResponse;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;
import crm.howbuy.base.validate.NumberValidator;
import crm.howbuy.base.validate.VarChar2Validator;
import jxl.Sheet;
import jxl.Workbook;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 路演会议处理
 * <AUTHOR>
 *
 */
@Slf4j
@Controller
@RequestMapping(value = "/conferencecust")
public class CmConferenceCustController  extends BaseController {

	private static Logger LOG = LoggerFactory.getLogger(CmConferenceCustController.class);
	
	private final String DOWNLOAD_FILE_NAME="会议名单导入模板.xls";
	
	private final String MODEL_FILE_NAME="ConferenceConscust.xls";

    private final String ATTEND_DOWNLOAD_FILE_NAME="导入参会模板.xls";

    private final String ATTEND_MODEL_FILE_NAME="conferenceAttend.xls";
	
	private final String StrAll="ALL";
	
	private final String strNull="null";
	
	private final String strSuccess="success";
	
	@Autowired
	private CmConferenceService cmConferenceService;
	
	@Autowired
	private CmConferenceConscustService cmConferenceConscustService;

	@Autowired
	private CmConferenceCustService ntConferenceCustService;

	@Autowired
	private ConscustService custService;


	@Autowired
	private CsCommunicateVisitService csCommunicateVisitService;

	@Autowired
	private AssociationMailService associationMailService;

	@RequestMapping(value = "/conferencecustList.do")
	public String conferenceList(HttpServletRequest request) {
		// 获取当前是否有导入全部的权限
		boolean canImport = isCanImportAll(request);
		request.setAttribute("canImport", canImport);
		return "/conference/conferencecustList";
	}
	
	@ResponseBody
	@RequestMapping("/queryConferenceCustList.do")
	public Map<String, Object> queryConferenceCustList(HttpServletRequest request) throws Exception{
		// 设置查询参数
		Map<String, Object> param = new HashMap<String, Object>(8);
		String[] conferencetype = request.getParameterValues("conferencetype[]");
		// 获取分页参数
		param = new ParamUtil(request).getParamObjMap();
		param.put("conferencetype", conferencetype);

		String paramConferenceName = (String) param.get("conferenceName");
		if(StringUtil.isNotNullStr(paramConferenceName)){
			param.put("conferenceName",URLDecoder.decode(paramConferenceName,"UTF-8"));
		}
		
		String consCode = request.getParameter("consCode");
		String orgCode = request.getParameter("orgCode");
		
		if(StringUtils.isNotEmpty(consCode) && !strNull.equals(consCode) && !StrAll.equals(consCode)) {
			param.put("consCode", consCode);
		}else {
			param.put("consCode", null);
			//param.put("teamCode", Util.getSubQueryByOrgCode(orgCode));
		}
		
		PageData<CmConferenceConscust> cmConferenceConscustPage = cmConferenceConscustService.listCmConferenceConscustByPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>(2);

		resultMap.put("total", cmConferenceConscustPage.getPageBean().getTotalNum());
		List<CmConferenceConscust> listtemp = cmConferenceConscustPage.getListData();
		if(listtemp != null && listtemp.size() > 1){
			for(CmConferenceConscust v:listtemp){
	
				ConsOrgCache orgcache = ConsOrgCache.getInstance();
				String uporgcode = orgcache.getUpOrgMapCache().get(v.getOrgcode());
				if("0".equals(uporgcode)){
					v.setUporgname(orgcache.getOrgMap().get(v.getOrgcode()));
				}else{
					v.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
				}
	
				v.setOrgcode(orgcache.getOrgMap().get(v.getOrgcode()));
				
				if(StringUtils.isNotEmpty(v.getCutoffdt())) {
					
					long now = Long.parseLong(DateUtil.getDateFormat(new Date(),"yyyyMMddHHmmss"));
					long cutoffdt = Long.parseLong(v.getCutoffdt());
					if (cutoffdt < now) {
						v.setCutoffdt("0");
					}
				}
			}
		}else{
			resultMap.put("total", 0);
			listtemp = new ArrayList<CmConferenceConscust>();
		}
		
		resultMap.put("rows", listtemp);
		return resultMap;
	}
	
	@RequestMapping("/queryConferenceConscust.do")
	public String queryConferenceConscust(HttpServletRequest request){
		return "/conference/addConferenceCust";
	}
	
	@ResponseBody
	@RequestMapping("/delConferenceCustInfo.do")
	public String delConferenceCustInfo(HttpServletRequest request) throws Exception{

		User user = getUser(request);

		String result = "success";
		String conferenceid = request.getParameter("conferenceid");
		String conscustno = request.getParameter("conscustno");
		String conferencename = URLDecoder.decode(request.getParameter("conferencename"),"UTF-8");
		Map<String, String> paramMap = new HashMap<String, String>(2);
		paramMap.put("conferenceid", conferenceid);
		paramMap.put("conscustno", conscustno);
		
		cmConferenceConscustService.deleteConferenceConscust(paramMap);

		String commContent = "取消预约参会："+conferencename;
		// 添加投顾预约及沟通拜访记录
		log.info("CmConferenceCustController.delConferenceCustInfo:" + conscustno);
		csCommunicateVisitService.insertCsCommunicateVisitByConfere(conscustno,user.getUserId(),commContent);

		return result;
	}

	@ResponseBody
	@RequestMapping("/crtRandomForCust.do")
	public String crtRandomForCust(HttpServletRequest request) throws Exception{
		String result = "success";
		String conferenceid = request.getParameter("conferenceid");

		Map<String, String> paramMap = new HashMap<String, String>(1);
		paramMap.put("conferenceid", conferenceid);

		CmConference cmConference = cmConferenceService.queryCmConferenceInfo(conferenceid);

		if(cmConference != null){
			if(StringUtils.isNotEmpty(cmConference.getCutoffdt())){
				long now = Long.valueOf(DateUtil.getDateFormat(new Date(),"yyyyMMddHHmmss"));
				long cutoffdt = Long.valueOf(cmConference.getCutoffdt());
				if (cutoffdt > now) {
					result = "outoffdt";
				}else{
					//查询会议下所有客户
					List<CmConferenceConscust> conferenceConscustList = cmConferenceConscustService.listCmConferenceConscustById(paramMap);
					List<CmConferenceConscust> newconferenceConscustList = new ArrayList<CmConferenceConscust>();
					//存储此会议中所有已存在预约码
					List<String> numRandom = new ArrayList<String>();

					if(conferenceConscustList != null && conferenceConscustList.size() > 0){
						//记录没有预约码的记录条数
						int listnum = conferenceConscustList.size();
						for(CmConferenceConscust conferenceConscust:conferenceConscustList){

							if(StringUtils.isNotEmpty(conferenceConscust.getAppointmentscode())){
								numRandom.add(conferenceConscust.getAppointmentscode());
								listnum -= 1;
							}else{
								newconferenceConscustList.add(conferenceConscust);
							}
						}

						if(newconferenceConscustList != null && newconferenceConscustList.size() >0){
							//生成全部未使用新预约码
							List<String> newnumRandom = StringUtil.getAllRandom(numRandom,listnum,4);

							for(int i = 0 ; i < newconferenceConscustList.size() ; i++){
								newconferenceConscustList.get(i).setAppointmentscode(newnumRandom.get(i));
								cmConferenceConscustService.updateCmConferenceConscust(newconferenceConscustList.get(i));
							}
						}else{
							result = "nonewcust";
						}
					}else{
						result = "nocust";
					}
				}
			}else{
				result = "nooffdt";
			}
		}else{
			result = "noConference";
		}

		return result;
	}


	@ResponseBody
	@RequestMapping("/saveConferenceConscust.do")
	public String saveConferenceConscust(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		String result = "success";
		User user = getUser(request);
		String conferenceid = request.getParameter("conferenceid");
		String conscustno = request.getParameter("conscustno");
		String maxnumber = request.getParameter("maxnumber");
		int conferencesub = Integer.parseInt(maxnumber.toString());

		Conscust cust = null;
		Map<String,String> param = new HashMap<String,String>(1);
		if(StringUtil.isNotNullStr(conscustno)){
			param.put("conscustno", conscustno);
			cust = custService.getConscust(conscustno);
		}

		if(cust !=null ){	
			Map<String, String> paramMap = new HashMap<String, String>(2);
			paramMap.put("conferenceid", conferenceid);
			paramMap.put("conscustno", conscustno);		
			CmConferenceConscust cmConfConscust = cmConferenceConscustService.getConferenceConscustinfo(paramMap);
			if(cmConfConscust == null){		
				String outletcode = ConsOrgCache.getInstance().getCons2OutletMap().get(cust.getConscode());		
				if(StringUtil.isNotNullStr(outletcode)){
					param.put("conferenceId", conferenceid);
					param.put("orgcode", outletcode);
					//查询当前组织允许报名人数
					Map<String, Object> allMap = cmConferenceService.getCmConferenceOrgNum(param);
					//获取截止时间
					long cutoffdt = Long.parseLong(allMap.get("CUTOFFDT") == null ? "0" : allMap.get("CUTOFFDT").toString());
					if (cutoffdt != 0) {
						long now = Long.parseLong(DateUtil.getDateFormat(new Date(),"yyyyMMddHHmmss"));
						if (cutoffdt < now) {
							result = "custoff";
							return result;
						}
					}
					if (CacheUtil.lock(CacheCode.CMCONFERENCE, conferenceid)){
						try {
							result = cmConferenceConscustService.checkConferenceConscust(conferenceid,outletcode,conferencesub);
							if(!strSuccess.equals(result)){
								return result;
							}
							CmConferenceConscust cmConferenceConscust = new CmConferenceConscust();
							cmConferenceConscust.setConferenceid(conferenceid);
							cmConferenceConscust.setConscode(cust.getConscode());
							cmConferenceConscust.setConscustno(conscustno);
							cmConferenceConscust.setOrgcode(outletcode);
							cmConferenceConscust.setCreatdt(DateTime.now().toString("yyyyMMdd"));
							cmConferenceConscust.setCreater(user.getUserId());
							cmConferenceConscust.setAppointmentsnub(conferencesub);
							// 查询客户状态
							String gdcjlabel = cmConferenceConscustService.queryGdcjlabelByConsCustNo(conscustno);
							if (StringUtils.isBlank(gdcjlabel)) {
								gdcjlabel = "0";
							}
							cmConferenceConscust.setGdcjlabel(gdcjlabel);
							cmConferenceConscustService.insertCmConferenceConscust(cmConferenceConscust);
							CmConference cmConference = cmConferenceService.queryCmConferenceInfo(conferenceid);
							String commContent = "预约参会："+cmConference.getConferencename()+"，预约参会人数："+conferencesub;
							// 添加投顾预约及沟通拜访记录
							log.info("CmConferenceCustController.saveConferenceConscust:" + conscustno);
							csCommunicateVisitService.insertCsCommunicateVisitByConfere(conscustno,user.getUserId(),commContent);
						}catch (Exception e) {
							LOG.error(e.getMessage(),e);
						}finally {
							CacheUtil.unlock(CacheCode.CMCONFERENCE, conferenceid);
						}
					}else {
						result = "wait";
					}
					
				}else{
					result = "noconscode";
				}
			}else{
				result = "haveinfo";
			}
		}else{
			result = "nocust";
		}
		
		return result;	
	}

	@RequestMapping("/exportConferenceCust.do")
	public void exportConferenceCust(HttpServletRequest request, @RequestParam Map<String, String> params,
			HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(8);
		
		Map<String, Object> param = new HashMap<String, Object>(5);
		// 获取分页参数
		String orgCode = request.getParameter("orgCode");
		
		String conferenceName = request.getParameter("conferenceName");
		String custName = request.getParameter("custName");
		String startDt = request.getParameter("startDt");
		String endDt = request.getParameter("endDt");
		String conferencetype = request.getParameter("conferencetype");
		//导出需求，1姓名正常导出；2姓名加密导出
		String type = request.getParameter("type");
		custName = URLDecoder.decode(custName.trim(), "UTF-8");		
		param.put("conferenceName", conferenceName);
		param.put("custName", custName);
		param.put("startDt", startDt);
		param.put("endDt", endDt);
		param.put("orgCode", orgCode);
		if(StringUtil.isNotNullStr(conferencetype)){
			String [] arrtype = conferencetype.split(",");
			param.put("conferencetype", arrtype);
		}
		
		String consCode = request.getParameter("consCode");
		
		if(StringUtils.isNotEmpty(consCode) && !strNull.equals(consCode) && !StrAll.equals(consCode)) {
			param.put("consCode", consCode);
		}else {
			param.put("consCode", null);
			//param.put("teamCode", Util.getSubQueryByOrgCode(orgCode));
		}
		

		List<CmConferenceConscust> cmConferenceConscustlist = cmConferenceConscustService.listCmConferenceConscust(param);
		if(cmConferenceConscustlist != null && cmConferenceConscustlist.size() > 1){
			for(CmConferenceConscust v:cmConferenceConscustlist){

				ConsOrgCache orgcache = ConsOrgCache.getInstance();
				String uporgcode = orgcache.getUpOrgMapCache().get(v.getOrgcode());
				if("0".equals(uporgcode)){
					v.setUporgname(orgcache.getOrgMap().get(v.getOrgcode()));
				}else{
					v.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
				}

				v.setOrgcode(orgcache.getOrgMap().get(v.getOrgcode()));
				if(v.getActualnubdt() == null){
					v.setActualnubdtstr("");
				}else{
					v.setActualnubdtstr(DateUtil.getDateFormat(v.getActualnubdt(),null));
				}
				//需求加密情况
				if("2".equals(type) && StringUtil.isNotNullStr(v.getCustname())){
					String name = v.getCustname().trim();
					if(StringUtil.isPattern(name, ".*[a-zA-Z]+.*")){
						v.setCustname(MaskUtil.maskNameEn(name));
					}else{
						v.setCustname(MaskUtil.maskNameCn(name));
					}
				}
				if (Objects.equals(v.getGdcjlabel(), "0")) {
					v.setFirtrdt(null);
				}
				// 报名时客户状态
				String gdcjlabel = v.getGdcjlabel();
				if ("1".equals(gdcjlabel)) {
					v.setGdcjlabelname("成交");
				} else if ("0".equals(gdcjlabel)) {
					v.setGdcjlabelname("潜客");
				}
			}
			
			resultMap.put("rows", cmConferenceConscustlist);
		}else{
			LOG.error("查询对应会议信息失败！");
		}
		
		try {
			// 清空输出流
			response.reset();
			// 设置文件格式和名字
			response.setContentType("multipart/form-data");
			response.setHeader("Content-Disposition",
					"attachment;fileName=" + new String("参会成员记录.xls".getBytes("gb2312"), "ISO8859-1"));
			ServletOutputStream os = response.getOutputStream();
			
			String [] columnName = new String []{ 
			"会议ID","参会区域","参会部门","参会时所属投顾","会议名称","客户号","客户姓名","报名时客户状态","高端首次交易日期","预约参会人数","预约码",
			"实到人数","签到时间","预约创建人","会议ID"
			};

			String[] beanProperty = new String[]{
					"conferenceid", "uporgname", "orgcode", "consname", "conferencename", "conscustno", "custname", "gdcjlabelname", "firtrdt",
					"appointmentsnub", "appointmentscode", "actualnub", "actualnubdtstr", "createrName", "courseid"
			};
			ExcelWriter.writeExcel(os, "参会成员记录", 0, (List<?>)resultMap.get("rows"), columnName, beanProperty);
			os.close(); // 关闭流
		} catch (Exception e) {
			LOG.error("文件导出异常", e);
		}

	}

	@RequestMapping(value="/impConferenceCustReport.do",method=RequestMethod.POST)
	public  @ResponseBody Map<String, Object> inputPcust(HttpServletRequest request){
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		User user = getUser(request);
		InputStream input = null;
		Workbook workBook = null;
		String errorMsg = "";
		String uploadFlag = "success";
		try {  
			// 转型为MultipartHttpRequest：  
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;  
			// 获得文件：  
			MultipartFile file = multipartRequest.getFile("file");  
			// 获得输入流：  
			input = file.getInputStream();  
			workBook = Workbook.getWorkbook(input);
			// 去掉之前导入的一二级旧来源，统一成现在的新来源编码
			String[] colPropertity = {"conferenceid", "conscustno", "actualnub"};
			Sheet sheet = workBook.getSheet(0);
			List<CmConferenceConscust> addconfconscustList = new ArrayList<CmConferenceConscust>();
			// 将之前获取Excel的13列数据改为12列
			List<CmConferenceConscust> postList = ExcelUtils.getListByReadShell(sheet, 1, 0, 3, colPropertity,CmConferenceConscust.class);
				//--------------update by tjy end(version 3.5.3)--------------------------------
			if (null == postList || postList.isEmpty()) {
				errorMsg = "没有上传记录";
				uploadFlag = "error";
			} else {
				int line = 2;
				for (CmConferenceConscust importParam : postList) {
					String validateMsg = this.checkAdd(importParam);
						
					if (!StringUtil.isEmpty(validateMsg)) {
						errorMsg = "第 " + line + " 行错误是：" + validateMsg;
						uploadFlag = "error";	
						break;
					}else{
						Map<String, String> conferenceparam = new HashMap<String, String>(2);
						conferenceparam.put("conferenceid", importParam.getConferenceid());
						conferenceparam.put("conscustno", importParam.getConscustno());
						CmConferenceConscust confconscust = cmConferenceConscustService.getConferenceConscustinfo(conferenceparam);
						if(confconscust == null){
							errorMsg = "第 " + line + " 行错误是："+importParam.getConscustno()+"此客户没有预约参会记录，请补全信息";
							uploadFlag = "error";
							break;
						}else{
							confconscust.setActualnub(importParam.getActualnub());
							confconscust.setModifier(user.getUserId());
							addconfconscustList.add(confconscust);
						}
					}
						
					line++;
				}
				//符合条件
				if(strSuccess.equals(uploadFlag)){
					for(CmConferenceConscust importConscust:addconfconscustList){
						// 查询客户状态
						String gdcjlabel = cmConferenceConscustService.queryGdcjlabelByConsCustNo(importConscust.getConscustno());
						if (StringUtils.isBlank(gdcjlabel)) {
							gdcjlabel = "0";
						}
						importConscust.setGdcjlabel(gdcjlabel);
						cmConferenceConscustService.updateCmConferenceConscust(importConscust);
					}
				}
			}
			resultMap.put("uploadFlag", uploadFlag);  
			resultMap.put("errorMsg", errorMsg);  
	     } catch (Exception e) {   
	            e.printStackTrace();  
	            resultMap.put("uploadFlag", "error");  
	            resultMap.put("errorMsg", "请检查模板是否正确");  
	     }finally{
	    	try {
	    		if(input != null){
	    			input.close();
	    		}
			} catch (IOException e) {
				e.printStackTrace();
			}
	     }
		return resultMap;
	}

	/**
	 * 导入参会
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/impConferenceAttend.do",method=RequestMethod.POST)
	public  @ResponseBody Map<String, Object> impConferenceAttend(HttpServletRequest request){
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		User user = getUser(request);
		InputStream input = null;
		String errorMsg = "";
		String uploadFlag = "success";
		try {
			// 转型为MultipartHttpRequest：
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
			// 获得文件：
			MultipartFile file = multipartRequest.getFile("file");
			input = file.getInputStream();
			StringBuilder result = new StringBuilder();
			//读取excel
			EasyExcel.read(input, ConferenceConscustImportEntity.class, new ReadListener<ConferenceConscustImportEntity>() {

				/**
				 * 单次缓存的数据量
				 */
				public static final int BATCH_COUNT = 3000;
				/**
				 * 临时缓存
				 */
				private List<ConferenceConscustImportEntity> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

				@Override
				public void invoke(ConferenceConscustImportEntity data, AnalysisContext context) {
					cachedDataList.add(data);
				}

				@Override
				public void doAfterAllAnalysed(AnalysisContext context) {
					try {

						result.append(cmConferenceConscustService.importAttendData(cachedDataList, user.getUserId()));
					}catch (Exception e){
						log.error(e.getMessage(), e);
						result.append(BaseConstantEnum.SYS_ERROR.getDescription());
					}
					log.info("存储数据库成功！");
				}
			}).sheet().doRead();

			resultMap.put("result", result);
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("result", "请检查模板是否正确");
		}finally{
			try {
				if(input != null){
					input.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return resultMap;
	}

	/**
	 * @api {POST} /conferencecust/importlcjzconfencecust importLcjzConferenceCust()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceCustController
	 * @apiName importLcjzConferenceCust()
	 * @apiDescription 导入参会---理财九章
	 * @apiSuccess (响应结果) {Boolean} success
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"8ZKixudzn","returnMsg":"fLDLJhuT","data":"7i","success":true}
	 */
	@PostMapping(value = "/importlcjzconfencecust")
	@ResponseBody
	public BaseResponse<String> importLcjzConferenceCust(HttpServletRequest request) throws IOException {

		// 获取当前登陆用户
		User user = getUser(request);
		// 当前登陆人的所属中心
		String outletcode = associationMailService.getBelongCenter(user.getUserId());
		// 获取当前是否有导入全部的权限
		boolean canImport = isCanImportAll(request);
		//通过easyExcel 读取所有数据
		// 转型为MultipartHttpRequest：
		InputStream input = null;
		try {
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
			MultipartFile file = multipartRequest.getFile("file");
			input = file.getInputStream();
			List<ConferenceConscustImportEntity> conferenceCustImportDTOList = EasyExcel.read(input).head(ConferenceConscustImportEntity.class).sheet().headRowNumber(1).doReadSync();
			if (canImport) {
				String result = cmConferenceConscustService.importAttendData(conferenceCustImportDTOList, user.getUserId(), canImport);
				if (result != "success") {
					return BaseResponse.fail(result);
				} else {
					return BaseResponse.ok();
				}
			} else {
				BaseResponse<String> notLcjzConferenceIdList = getStringBaseResponse(outletcode, conferenceCustImportDTOList);
				if (notLcjzConferenceIdList != null){
					return notLcjzConferenceIdList;
				}
				String result = cmConferenceConscustService.importAttendData(conferenceCustImportDTOList, user.getUserId(), canImport);
				if (result != "success") {
					return BaseResponse.fail(result);
				} else {
					return BaseResponse.ok();
				}
			}
			// 获取导入数据
		} catch (IOException e) {
			log.error("error in importlcjzconfencecust", e);
			return BaseResponse.fail("文件读取失败");
		} finally {
			if (input != null) {
				input.close();
			}
		}
	}


	/**
	 * @description:(特殊检验)
	 * @param outletcode
	 * @param conferenceCustImportDTOList
	 * @return com.howbuy.crm.trade.common.response.BaseResponse<java.lang.String>
	 * @author: xufanchao
	 * @date: 2023/11/14 11:04
	 * @since JDK 1.8
	 */
	public BaseResponse<String> getStringBaseResponse(String outletcode, List<ConferenceConscustImportEntity> conferenceCustImportDTOList) {
		// 获取上传的导入数据的会议ID
		List<String> conferenceIds = conferenceCustImportDTOList.stream().map(it -> it.getConferenceId()).collect(Collectors.toList());
		List<CmConferenceConscust> cmConferenceConscusts = cmConferenceConscustService.listCmConferenceByIds(conferenceIds);
		List<String> notLcjzConferenceIdList = isNotLcjzConference(cmConferenceConscusts);
		// 判断当前会议是不是均为理财九章会议
		if (!CollectionUtils.isEmpty(notLcjzConferenceIdList)) {
			return BaseResponse.fail("导入会议：" + notLcjzConferenceIdList + "不是理财九章会议，请使用【导入参会-非理财九章】功能导入");
		}
		// 判断当前用户是否所属同一个中心
		List<String> notSameConference = isNotSameConference(cmConferenceConscusts, outletcode);
		if (!CollectionUtils.isEmpty(notSameConference)) {
			return BaseResponse.fail("您所属中心与会议：" + notSameConference + "创建人不相同，无权限导入该会议参会信息");
		}
		// 判断当前时间和会议的截止日期时间先后
		List<String> dealyCurrent = isDealyCurrent(cmConferenceConscusts);
		if (!CollectionUtils.isEmpty(dealyCurrent)) {
			return BaseResponse.fail("该会议已过截止时间：" + dealyCurrent + "如需导入名单，请走线下申请");
		}
		// 获取当前会议导入的数量
		List<CmConferenceConscust> conferenceBySource = cmConferenceConscustService.getConferenceBySource(conferenceIds);
		List<String> countList = hasImportConference(conferenceBySource);
		if (!CollectionUtils.isEmpty(countList)) {
			return BaseResponse.fail("会议：" + countList + "导入客户参会信息已超限额，如需导入，请走线下申请");
		}
		// 判断当前数据是否在限度以内
		Map<String, Long> collect = conferenceIds.stream()
				.collect(Collectors.groupingBy(item -> item, Collectors.counting()));
		List<CmConferenceConscust> maxNumByConferenceId = cmConferenceConscustService.getMaxNumByConferenceId(conferenceIds);
		List<String> notExceed = isNotExceed(collect, maxNumByConferenceId);
		List<ConferenceConscustImportEntity> maxActNum = conferenceCustImportDTOList.stream().filter(it -> {
			if (it.getActualNum() != null && it.getAppointNum() != null && (Integer.parseInt(it.getActualNum()) <= Integer.parseInt(it.getAppointNum()))) {
				return false;
			} else {
				return true;
			}
		}).collect(Collectors.toList());
		if (!CollectionUtils.isEmpty(notExceed) || !maxActNum.isEmpty()) {
			return BaseResponse.fail("导入条数超限，请修改后重新上传");
		}
		return null;
	}


	/**
	 * @api {POST} /conferencecust/importnolcjzconfencecust importLcjzConferenceCust()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceCustController
	 * @apiName importLcjzConferenceCust()
	 * @apiDescription 导入参会---非理财九章
	 * @apiSuccess (响应结果) {Boolean} success
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"8ZKixudzn","returnMsg":"fLDLJhuT","data":"7i","success":true}
	 */
	@PostMapping(value = "/importnolcjzconfencecust")
	@ResponseBody
	public BaseResponse<String> importnoLcjzConferenceCust(HttpServletRequest request) throws IOException {

		// 获取当前登陆用户
		User user = getUser(request);
		// 当前登陆人的所属中心
		String outletcode = associationMailService.getBelongCenter(user.getUserId());
		// 获取当前是否有导入全部的权限
		boolean canImport = isCanImportAll(request);
		//通过easyExcel 读取所有数据
		// 转型为MultipartHttpRequest：
		InputStream input = null;
		try {
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
			MultipartFile file = multipartRequest.getFile("file");
			input = file.getInputStream();
			List<ConferenceConscustImportEntity> conferenceCustImportDTOList = EasyExcel.read(input).head(ConferenceConscustImportEntity.class).sheet().headRowNumber(1).doReadSync();
			if (canImport) {
				String result = cmConferenceConscustService.importAttendData(conferenceCustImportDTOList, user.getUserId(), canImport);
				if (result != "success") {
					return BaseResponse.fail(result);
				} else {
					return BaseResponse.ok();
				}
			} else {
				// 获取上传的导入数据的会议ID
				List<String> conferenceIds = conferenceCustImportDTOList.stream().map(it -> it.getConferenceId()).collect(Collectors.toList());
				List<CmConferenceConscust> cmConferenceConscusts = cmConferenceConscustService.listCmConferenceByIds(conferenceIds);
				List<String> notLcjzConferenceIdList = isLcjzConference(cmConferenceConscusts);
				// 判断当前会议是不是均为理财九章会议
				if (!CollectionUtils.isEmpty(notLcjzConferenceIdList)) {
					return BaseResponse.fail("导入会议：" + notLcjzConferenceIdList + "是理财九章会议，请使用【导入参会-理财九章】功能导入");
				}
				// 判断当前用户是否所属同一个中心
				List<String> notSameConference = isNotSameConference(cmConferenceConscusts, outletcode);
				if (!CollectionUtils.isEmpty(notSameConference)) {
					return BaseResponse.fail("您所属中心与会议：" + notSameConference + "创建人不相同，无权限导入该会议参会信息");
				}
				String result = cmConferenceConscustService.importAttendData(conferenceCustImportDTOList, user.getUserId(), canImport);
				if (result != "success") {
					return BaseResponse.fail(result);
				} else {
					return BaseResponse.ok();
				}
			}
			// 获取导入数据
		} catch (IOException e) {
			log.error("error in importlcjzconfencecust", e);
			return BaseResponse.fail("文件读取失败");
		} finally {
			if (input != null) {
				input.close();
			}
		}
	}

	/**
	 * @param collect
	 * @param cmConferenceConscusts
	 * @return java.util.List<java.lang.String>
	 * @description:(判断导入数据是否超限)
	 * @author: xufanchao
	 * @date: 2023/11/13 15:19
	 * @since JDK 1.8
	 */
	private List<String> isNotExceed(Map<String, Long> collect, List<CmConferenceConscust> cmConferenceConscusts) {
		return cmConferenceConscusts.stream().filter(it -> {
			if (collect.containsKey(it.getConferenceid()) && it.getCount() != null && ((Double.parseDouble(it.getCount()) * 0.08 +1) > collect.get(it.getConferenceid()))) {
				return false;
			} else {
				return true;
			}
		}).map(CmConferenceConscust::getConferenceid).collect(Collectors.toList());

	}

	/**
	 * @description:(获取到非理财九章的会议id数据)
	 * @param cmConferenceConscusts
	 * @return java.util.List<java.lang.String>
	 * @author: xufanchao
	 * @date: 2023/11/13 11:25
	 * @since JDK 1.8
	 */
	private List<String> isNotLcjzConference(List<CmConferenceConscust> cmConferenceConscusts) {
		// 根据会议id查询会议类型
		List<CmConferenceConscust> conferenceConscusts = cmConferenceConscusts.stream()
				.filter(conference ->
						{
							if (conference.getConferenceid() != null && conference.getConferencetype() != null && !conference.getConferencetype().contains(ConferenceTypeEnum.LCJZ_BIG_JB.getValue())) {
								return true;
							} else {
								return false;
							}
						}
				)
				.collect(Collectors.toList());
		return conferenceConscusts.stream().map(CmConferenceConscust::getConferenceid).distinct().collect(Collectors.toList());
	}


	/**
	 * @description:(获取到理财九章的会议id数据)
	 * @param cmConferenceConscusts
	 * @return java.util.List<java.lang.String>
	 * @author: xufanchao
	 * @date: 2023/11/13 11:25
	 * @since JDK 1.8
	 */
	private List<String> isLcjzConference(List<CmConferenceConscust> cmConferenceConscusts) {
		// 根据会议id查询会议类型
		List<CmConferenceConscust> conferenceConscusts = cmConferenceConscusts.stream()
				.filter(conference ->
						{
							if (conference.getConferenceid() != null && conference.getConferencetype() != null && conference.getConferencetype().contains(ConferenceTypeEnum.LCJZ_BIG_JB.getValue())) {
								return true;
							} else {
								return false;
							}
						}
				)
				.collect(Collectors.toList());
		return conferenceConscusts.stream().map(CmConferenceConscust::getConferenceid).distinct().collect(Collectors.toList());
	}


	/**
	 * @description:(获取当前类型的会议数量)
	 * @param cmConferenceConscusts
	 * @return java.util.List<java.lang.String>
	 * @author: xufanchao
	 * @date: 2023/11/13 14:54
	 * @since JDK 1.8
	 */
	private List<String> hasImportConference(List<CmConferenceConscust> cmConferenceConscusts) {
		return cmConferenceConscusts.stream().filter(it -> Integer.parseInt(it.getCount()) > 0).map(CmConferenceConscust::getConferenceid).collect(Collectors.toList());
	}

	/**
	 * @param cmConferenceConscusts
	 * @param orgCode
	 * @return java.util.List<java.lang.String>
	 * @description:(判断是否在统一的部门下)
	 * @author: xufanchao
	 * @date: 2023/11/13 13:38
	 * @since JDK 1.8
	 */
	private List<String> isNotSameConference(List<CmConferenceConscust> cmConferenceConscusts, String orgCode) {
		return cmConferenceConscusts.stream().filter(it ->
		{
			if (it.getConscode() != null) {
				String belongCenter = associationMailService.getBelongCenter(it.getConscode());
				if (!Objects.equals(belongCenter, orgCode)) {
					return true;
				} else {
					return false;
				}
			} else {
				return true;
			}
		}).map(CmConferenceConscust::getConferenceid).distinct().collect(Collectors.toList());
	}

	/**
	 * @description:(判断当前时间和会议时间是否先后)
	 * @param cmConferenceConscusts
	 * @return java.util.List<java.lang.String>
	 * @author: xufanchao
	 * @date: 2023/11/13 14:08
	 * @since JDK 1.8
	 */
	private List<String> isDealyCurrent(List<CmConferenceConscust> cmConferenceConscusts) {
		Date current = new Date();
		List<CmConferenceConscust> conferenceConscusts = cmConferenceConscusts.stream().filter(it -> {
			if (it.getCutoffdt() != null) {
				Date cutOffDt = DateUtil.getFormatTimeStr(it.getCutoffdt(), "yyyy-MM-dd HH:mm:ss");
				return cutOffDt.before(current);
			} else {
				return true;
			}
		}).collect(Collectors.toList());
		return conferenceConscusts.stream().map(CmConferenceConscust::getConferenceid).distinct().collect(Collectors.toList());
	}

	/**
	 * @description:(判断当前用户是否有导入数据的全部权限)
	 * @param request
	 * @return boolean
	 * @author: xufanchao
	 * @date: 2023/11/10 16:22
	 * @since JDK 1.8
	 */
	private static boolean isCanImportAll(HttpServletRequest request) {
		List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
		//是否有分配权限
		boolean canImport = false;
		for (String role : roles) {
			List<String> temp = AuthCache.getInstance().getOperListStr(role, "02050402");
			if (temp != null && (temp.contains("8"))) {
				canImport = true;
				break;
			}
		}
		return canImport;
	}


	/**
	 * @api {POST} /conferencecust/updateconscode updateConscode()
	 * @apiVersion 1.0.0
	 * @apiGroup 修改客户投顾
	 * @apiName updateConscode()
	 * @apiDescription 修改客户投顾
	 * @apiSuccess (响应结果) {Boolean} success
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"00ilLcGrA","returnMsg":"5p","data":{},"success":true}
	 */
	@ResponseBody
	@RequestMapping(value = "/updateconscode",method = RequestMethod.POST)
	public BaseResponse<String> updateConscode(HttpServletRequest request) throws Exception {
		BaseResponse<String> baseReturn = BaseResponse.ok();
		// 投顾编码
		String conscode = request.getParameter("conscode");
		/// 投顾客户号
		String conscustno = request.getParameter("conscustno");
		String conferenceId = request.getParameter("conferenceId");
		String orgcode = request.getParameter("orgCode");
		// 修改路演会议的投顾以及部门数据
		try {
			cmConferenceConscustService.updateCmConferenceConscustByConcustnos(conferenceId, conscode, orgcode, conscustno);
			return baseReturn;
		} catch (Exception e) {
			log.error("修改路演会议投顾及部门数据报错", e);
		}
		return BaseResponse.fail("修改失败");
	}


	/**
	 * @api {POST} /conferencecust/batchcancelconferencecust.do batchCancelConferenceCust()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceCustController
	 * @apiName batchCancelConferenceCust()
	 * @apiDescription 批量取消参会
	 * @apiParam (请求体) {Array} requestBody
	 * @apiParam (请求体) {String} requestBody.conferenceId 会议id
	 * @apiParam (请求体) {String} requestBody.custNo 客户号
	 * @apiParam (请求体) {String} requestBody.operator 操作人
	 * @apiParamExample 请求体示例
	 * [{"custNo":"Rnj0l","conferenceId":"NX94rFt3iP","operator":"jI2xTN"}]
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"FaIQzWLi","returnMsg":"5Ke0IrKI3","returnObject":"W","returnList":["whUY"]}
	 */
	@ResponseBody
	@PostMapping("/batchcancelconferencecust.do")
	public NtReturnMessageDto<String> batchCancelConferenceCust(@RequestBody List< ConferenceCustCancelVo > cancelList){
//		List< ConferenceCustCancelVo > cancelList = JSON.parseArray(cancelString, ConferenceCustCancelVo.class);
		String userId=getLoginUserId();
		cancelList.forEach(cancelVo -> cancelVo.setOperator(userId));
		return  ntConferenceCustService.batchCancel( cancelList);
	}

	/**
	 * @Title: checkAdd 
	 * @Description: TODO(方法说明描述)
	 *
	 * @param addRequest
	 * @return String
	 * @throws
	 */
	protected String checkAdd(CmConferenceConscust addRequest) {
		Map comonMap = ValidateUtil.commonMap;
		String validateCode = null;
		try{
			validateCode = NumberValidator.validate(String.valueOf(addRequest.getActualnub()), 1,
					100, true, "实际到场人数", comonMap);
			if (!StringUtil.isEmpty(validateCode)) {
				return validateCode;
			}
			validateCode = VarChar2Validator.validate(addRequest.getConferenceid(), 1,
					180, true, "参与会议", comonMap);
			if (!StringUtil.isEmpty(validateCode)) {
				return validateCode;
			}
			validateCode = VarChar2Validator.validate(addRequest.getConscustno(), 1,
					180, true, "投顾客户号", comonMap);
			if (!StringUtil.isEmpty(validateCode)) {
				return validateCode;
			}
		}catch(Exception e){
			validateCode = "信息检查不通过";
		}
		return validateCode;
	}
	
	@RequestMapping("/modelConferenceConscustReport.do")
	public String downloadModelDoc( HttpServletRequest request,
			HttpServletResponse response) {
		String type = request.getParameter("type");
		String downloadFileName = DOWNLOAD_FILE_NAME;
		String modelFileName = MODEL_FILE_NAME;
		if("attend".equals(type)){
			downloadFileName = ATTEND_DOWNLOAD_FILE_NAME;
			modelFileName = ATTEND_MODEL_FILE_NAME;
		}
		return dowmloadTemplate(modelFileName,downloadFileName,request,response);
	}
	
	
	/**
	 * 跳转分页查询会议报名签到统计页面
	 * @return
	 */
	@RequestMapping(value="/conferenceCustStatList.do")
	public String conferenceCustStatList(){
		return "/conference/conferenceCustStatList";
	}
	
	/**
	 * 分页查询会议报名签到统计
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/queryConferenceCustStatList.do")
	public Map<String, Object> queryConferenceCustStatList(HttpServletRequest request) throws Exception{
		String orgCode = request.getParameter("orgCode");
		// 设置查询参数
		Map<String, Object> param = new HashMap<String, Object>(8);
		String[] conferencetype = request.getParameterValues("conferencetype[]");
		// 获取分页参数
		param = new ParamUtil(request).getParamObjMap();
		param.put("conferencetype", conferencetype);
		param.put("orgCode", null);
		if(StringUtils.isNotBlank(orgCode)){
			param.put("orgCode", orgCode);
		}else{
			param.put("orgCode", null);
			param.put("orgCodeCfAndGd", "1");
		}
		
		PageData<CmConferenceConscust> cmConferenceConscustPage = cmConferenceConscustService.listCmConferenceConscustStatByPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		
		
		resultMap.put("total", cmConferenceConscustPage.getPageBean().getTotalNum());
		List<CmConferenceConscust> listtemp = cmConferenceConscustPage.getListData();
		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		ConstantCache constantCache = ConstantCache.getInstance();
		Map<String,String> orgMap = consOrgCache.getOrgMap();
		Map<String,String> provCityMap = constantCache.getProvCityMap();
		Map<String,String> conferenceTypeMap = constantCache.getConstantKeyVal("conferenceType");
		for(CmConferenceConscust v:listtemp){
			v.setOrgcode(orgMap.get(v.getOrgcode()));
			v.setCitycode(provCityMap.get(v.getCitycode()));
			v.setProvcode(provCityMap.get(v.getProvcode()));
			if(StringUtil.isNotNullStr(v.getConferencetype())){
				String [] listtypes = v.getConferencetype().split(",");
				StringBuilder sb = new StringBuilder();
				for(String type : listtypes){
					if (StringUtils.isNotBlank(type)) {
						sb.append(","+conferenceTypeMap.get(type));
					}
				}
				v.setConferencetype(sb.toString().replaceFirst(",", ""));
			}
		}
		resultMap.put("rows", listtemp);
		return resultMap;
	}
	
	
	@RequestMapping("/exportConferenceCustStatList.do")
	public void exportConference(HttpServletRequest request,HttpServletResponse response)  throws Exception{
		String orgCode = request.getParameter("orgCode");
		// 设置查询参数
		Map<String, Object> param = new HashMap<String, Object>(8);
		String conferencetype = request.getParameter("conferencetype");
		// 获取分页参数
		param = new ParamUtil(request).getParamObjMap();
		param.put("orgCode", null);
		if(StringUtils.isNotEmpty(conferencetype)){
			param.put("conferencetype", conferencetype.split(","));
		}
		if(StringUtils.isNotBlank(orgCode)){
			param.put("orgCode", orgCode);
		}else{
			param.put("orgCode", null);
			param.put("orgCodeCfAndGd", "1");
		}
		
		List<CmConferenceConscust> cmConferenceConscustlist = cmConferenceConscustService.listCmConferenceConscustStat(param);
		if(cmConferenceConscustlist != null && cmConferenceConscustlist.size() > 0){
			ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
			ConstantCache constantCache = ConstantCache.getInstance();
			Map<String,String> orgMap = consOrgCache.getOrgMap();
			Map<String,String> provCityMap = constantCache.getProvCityMap();
			Map<String,String> conferenceTypeMap = constantCache.getConstantKeyVal("conferenceType");
			String procode = null;
			String citycode = null;
			for(CmConferenceConscust v:cmConferenceConscustlist){
				v.setOrgcode(orgMap.get(v.getOrgcode()));
				citycode = StringUtils.isBlank(provCityMap.get(v.getCitycode())) ? "" : provCityMap.get(v.getCitycode());
				procode = StringUtils.isBlank(provCityMap.get(v.getProvcode())) ? "" : provCityMap.get(v.getProvcode());
				v.setCitycode(citycode);
				v.setProvcode(procode);
				v.setProvcode(procode + "  "  + citycode);
				//v.setConferencetype(conferenceTypeMap.get(v.getConferencetype()));
				if(StringUtil.isNotNullStr(v.getConferencetype())){
					String [] listtypes = v.getConferencetype().split(",");
					StringBuilder sb = new StringBuilder();
					for(String type : listtypes){
						sb.append(","+conferenceTypeMap.get(type));
					}
					v.setConferencetype(sb.toString().replaceFirst(",", ""));
				}

			}
		}
		
		try {
			// 清空输出流
			response.reset();
			// 设置文件格式和名字
			response.setContentType("multipart/form-data");
			response.setHeader("Content-Disposition",
					"attachment;fileName=" + new String("会议报名签到统计记录.xls".getBytes("gb2312"), "ISO8859-1"));
			ServletOutputStream os = response.getOutputStream();
			
			String [] columnName = {"会议id","会议日期","所属中心","会议类型","会议名称","会议地址","预约参会人数","实际到场人数"};
			String [] beanProperty = {"conferenceid","conferencedt","orgcode","conferencetype","conferencename","provcode","appointmentsnub","actualnub"};
			ExcelWriter.writeExcel(os, "会议报名签到统计记录", 0, cmConferenceConscustlist, columnName, beanProperty);
			os.close(); // 关闭流
		} catch (Exception e) {
			LOG.error("文件导出异常", e);
		}

	}


	/**
	 * @description:(获取当前登陆用户)
	 * @param request
	 * @return com.howbuy.crm.page.framework.domain.User
	 * @author: xufanchao
	 * @date: 2023/11/10 13:58
	 * @since JDK 1.8
	 */
	private static User getUser(HttpServletRequest request) {
		User user = (User) request.getSession().getAttribute("loginUser");
		return user;
	}
}