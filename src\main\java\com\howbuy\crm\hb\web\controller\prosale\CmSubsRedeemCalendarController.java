package com.howbuy.crm.hb.web.controller.prosale;

import com.howbuy.crm.hb.domain.prosale.CmSubsRedeemCalendar;
import com.howbuy.crm.hb.service.prosale.CmSubsRedeemCalendarService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ExcelUtils;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import jxl.Sheet;
import jxl.Workbook;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 申购赎回日历controller层
 * 
 * author: wu.long
 * date: 2019年8月2日 上午9:52:04
 * version: V1.0
 * since: jdk 1.8,tomcat 8.0
 */
@Slf4j
@Controller
@RequestMapping(value = "/cmSubsRedeemCalendar")
public class CmSubsRedeemCalendarController  extends BaseController {
	private final String APPLY_REDEEM_CALENDAR_TEMPLATE_NAME = "申购赎回导入模板.xls";
	private final String APPLY_REDEEM_CALENDAR_TEMPLATE_FILE = "applyRedeemCalendarTemplate.xls";
	private final String APPLY_REDEEM_CALENDAR_INSTRUCTIONS_NAME = "申购赎回说明.pdf";
	private final String APPLY_REDEEM_CALENDAR_INSTRUCTIONS_FILE = "applyRedeemCalendarInstructions.pdf";
	
	@Autowired
	private CmSubsRedeemCalendarService cmSubsRedeemCalendarService;

	/**
	 * 加载申购赎回日历页面
	 * 
	 * author: wu.long
	 * date: 2019年8月1日 下午4:36:45 
	 * @param request
	 * @return
	 */
	@RequestMapping("/showCmSubsRedeemCalendar")
	public String showCmSubsRedeemCalendar(HttpServletRequest request) throws UnsupportedEncodingException{
		return "/prosale/listCmSubsRedeemCalendar";
	}
	
	/**
	 * 根据条件查询申购赎回日历信息
	 * 
	 * author: wu.long
	 * date: 2019年8月1日 下午7:55:10 
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listCmSubsRedeemCalendarByPage")
	public Map<String, Object> listCmSubsRedeemCalendarByPage(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, String> paramMap = new HashMap<String, String>();
		try {
			//获取分页参数
			paramMap = new ParamUtil(request).getParamMap();
			paramMap.put("fundcode", request.getParameter("fundcode"));
			paramMap.put("bBeginDate", request.getParameter("bBeginDate"));
			paramMap.put("bEndDate", request.getParameter("bEndDate"));
			paramMap.put("calendarType", "3".equals(request.getParameter("calendarType"))?null:request.getParameter("calendarType"));
			paramMap.put("eBeginDate", request.getParameter("eBeginDate"));
			paramMap.put("eEndDate", request.getParameter("eEndDate"));
			resultMap = cmSubsRedeemCalendarService.listCmSubsRedeemCalendarByPage(paramMap);
		} catch (Exception e) {
			log.error("根据条件查询申购赎回日历信息异常：", e);
		}
		return resultMap;
	}
	
	/**
	 * 新增或修改申购赎回日历信息
	 * 
	 * author: wu.long
	 * date: 2019年8月1日 下午7:59:58 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/addOrUpdateRedeemCalendar")
	public String addOrUpdateRedeemCalendar(HttpServletRequest request){
		String result = "";
		try {
			User user = (User)request.getSession().getAttribute("loginUser");
			String id = request.getParameter("id");
			String calendartype = request.getParameter("calendartype");
			String openenddt = request.getParameter("openenddt");
			String fundcode = request.getParameter("fundcode");
			String funddt = request.getParameter("funddt");
			String opendtdesc = request.getParameter("opendtdesc");
			String prestartdt = request.getParameter("prestartdt");
			String preenddt = request.getParameter("preenddt");
			String preenddtdisplace = request.getParameter("preenddtdisplace");
			String enddtdesc = request.getParameter("enddtdesc");
			String remark = request.getParameter("remark");
			result = cmSubsRedeemCalendarService.addOrUpdateRedeemCalendar(id, calendartype, openenddt, fundcode, funddt, 
					opendtdesc, prestartdt, preenddt, preenddtdisplace, user.getUserId(), enddtdesc, remark);
		} catch (Exception e) {
			log.error((StringUtils.isNotBlank(request.getParameter("id"))?"修改":"新增")+"申购赎回日历信息异常：", e);
		}
		return result;
	}
	
	/**
	 * 删除申购赎回日历信息
	 * 
	 * author: wu.long
	 * date: 2019年8月1日 下午8:00:19 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/deleteRedeemCalendarById")
	public String deleteRedeemCalendarById(HttpServletRequest request){
		String result = "";
		try {
			User user = (User)request.getSession().getAttribute("loginUser");
			String id = request.getParameter("id");
			result = cmSubsRedeemCalendarService.deleteRedeemCalendarById(id, user.getUserId());
		} catch (Exception e) {
			log.error("删除申购赎回日历信息异常：", e);
		}
		return result;
	}
	
	/**
	 * 获取申购赎回日历编辑信息
	 * 
	 * author: wu.long
	 * date: 2019年8月2日 下午4:05:50 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/selectRedeemCalendarByParams")
	public Map<String, Object> selectRedeemCalendarByParams(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		CmSubsRedeemCalendar subsRedeemCalendar = new CmSubsRedeemCalendar();
		try {
			subsRedeemCalendar.setId(new BigDecimal(request.getParameter("id")));
			resultMap = cmSubsRedeemCalendarService.selectRedeemCalendarByParams(subsRedeemCalendar);
		} catch (Exception e) {
			log.error("获取申购赎回日历编辑信息异常：", e);
		}
		return resultMap;
	}
	
	/**
	 * 下载申购赎回日历模板
	 * 
	 * author: wu.long
	 * date: 2019年8月5日 下午4:23:09 
	 * @param request
	 */
	@RequestMapping("/downloadTemplate")
	public void downloadTemplate(HttpServletRequest request, HttpServletResponse response){
		dowmloadTemplate(APPLY_REDEEM_CALENDAR_TEMPLATE_FILE,APPLY_REDEEM_CALENDAR_TEMPLATE_NAME,request,response);
	}
	
	/**
	 * 下载申购赎回日历说明
	 * 
	 * author: wu.long
	 * date: 2019年8月5日 下午4:23:09 
	 * @param request
	 */
	@RequestMapping("/downloadInstructions")
	public void downloadInstructions(HttpServletRequest request, HttpServletResponse response){
		dowmloadTemplate(APPLY_REDEEM_CALENDAR_INSTRUCTIONS_FILE,APPLY_REDEEM_CALENDAR_INSTRUCTIONS_NAME,request,response);
	}
	
	/**
	 * 导入申购赎回日历信息
	 * 
	 * author: wu.long
	 * date: 2019年8月5日 下午5:24:58 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/importRedeemCalendar")
	@SuppressWarnings("all")
	public Map<String, Object> importRedeemCalendar(HttpServletRequest request){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Pattern pattern = Pattern.compile("^-?\\d+$");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		User user = (User)request.getSession().getAttribute("loginUser");
		String errorMsg = "", uploadFlag = "success";
		Workbook workBook;
		InputStream input = null;
        try {
            // 转型为MultipartHttpRequest：
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            // 获得文件：
            MultipartFile file = multipartRequest.getFile("file");
            // 获得输入流：
            input = file.getInputStream();
            workBook = Workbook.getWorkbook(input);
            // 去掉之前导入的一二级旧来源，统一成现在的新来源编码
            String[] colPropertity = {"calendartype", "openenddt", "fundcode", "fundname", "funddt", 
            		"prestartdt", "preenddt", "opendtdesc", "enddtdesc", "preenddtdisplaceStr", "remark"};
            Sheet sheet = workBook.getSheet(0);
            List<CmSubsRedeemCalendar> subsRedeemCalendarList = new ArrayList<CmSubsRedeemCalendar>();
            List<CmSubsRedeemCalendar> rCalendarList = ExcelUtils.getListByReadShell(sheet, 1, 0, 11, colPropertity, CmSubsRedeemCalendar.class);
            if (null == rCalendarList || rCalendarList.isEmpty()) {
                errorMsg = "没有上传记录";
                uploadFlag = "error";
            } else {
                for (CmSubsRedeemCalendar subsRedeemCalendar : rCalendarList) {
                	String calendartypeValue = subsRedeemCalendar.getCalendartype();
                	String openenddtValue = subsRedeemCalendar.getOpenenddt();
                	String fundcodeValue = subsRedeemCalendar.getFundcode();
                	String funddtValue = subsRedeemCalendar.getFunddt();
                	String opendtdescValue = subsRedeemCalendar.getOpendtdesc();
                	String prestartdtValue = subsRedeemCalendar.getPrestartdt();
                	String preenddt = subsRedeemCalendar.getPreenddt();
                	String enddtdesc = subsRedeemCalendar.getEnddtdesc();
                	String remark = subsRedeemCalendar.getRemark();
                	String calendarType = subsRedeemCalendar.getCalendartype();
                	String preenddtdisplaceStr = subsRedeemCalendar.getPreenddtdisplaceStr();
//                	BigDecimal preenddtdisplaceValue = subsRedeemCalendar.getPreenddtdisplace();
                	Matcher matcher = pattern.matcher(preenddtdisplaceStr);
                	if(StringUtils.isBlank(calendartypeValue) || (!"0".equals(calendartypeValue) && !"1".equals(calendartypeValue) && !"2".equals(calendartypeValue)) 
                			|| (StringUtils.isNotEmpty(openenddtValue) && false == isValidDate(openenddtValue))
                			|| (("0".equals(calendartypeValue) || "1".equals(calendartypeValue)) && (StringUtils.isBlank(openenddtValue))) 
                			|| (StringUtils.isBlank(fundcodeValue)) || (StringUtils.isNotEmpty(funddtValue) && false == isValidDate(funddtValue)) 
                			|| (StringUtils.isBlank(opendtdescValue) || 500 < opendtdescValue.length()) 
                			|| (500 < enddtdesc.length()) || (500 < remark.length()) 
                			|| (("0".equals(calendartypeValue) || "1".equals(calendartypeValue)) && (StringUtils.isBlank(prestartdtValue)))     
                			|| (("0".equals(calendartypeValue) || "1".equals(calendartypeValue)) && (StringUtils.isBlank(preenddt)))  
                			|| (StringUtils.isNotEmpty(prestartdtValue) && false == isValidDate(prestartdtValue))
                			|| (StringUtils.isNotEmpty(preenddt) && false == isValidDate(preenddt))
                			|| (("0".equals(calendartypeValue) || "1".equals(calendartypeValue)) && StringUtils.isBlank(preenddtdisplaceStr))
                			|| (StringUtils.isNotEmpty(preenddtdisplaceStr) && !matcher.matches())){
                		errorMsg = "文本数据不符合要求，请修改后重新上传";
						uploadFlag = "error";
						break;
                	}
                	subsRedeemCalendar.setCalendartype(calendartypeValue);
                	subsRedeemCalendar.setPreenddtdisplace(StringUtils.isEmpty(preenddtdisplaceStr)?null:new BigDecimal(preenddtdisplaceStr));
                	//重复则更新操作
                	CmSubsRedeemCalendar rcalendarParam = new CmSubsRedeemCalendar();
                	if("0".equals(calendarType) || "1".equals(calendarType)){
                		rcalendarParam.setCalendartype(calendartypeValue);
                    	rcalendarParam.setOpenenddt(openenddtValue);
                    	rcalendarParam.setFundcode(fundcodeValue);
                	}
                	if("2".equals(calendarType)){
                		rcalendarParam.setCalendartype(calendartypeValue);
                		rcalendarParam.setFundcode(fundcodeValue);
                	}
        			List<CmSubsRedeemCalendar> list = cmSubsRedeemCalendarService.selectRedeemCalendarListByParams(rcalendarParam);
        			if(null != list && 0 < list.size()){
        				subsRedeemCalendar.setId(list.get(0).getId());
        				subsRedeemCalendar.setUpdater(user.getUserId());
        				subsRedeemCalendar.setModdate(sdf.format(System.currentTimeMillis()));
        				cmSubsRedeemCalendarService.updateRedeemCalendar(subsRedeemCalendar);
        			}else{
        				subsRedeemCalendar.setStat("0");
        				subsRedeemCalendar.setCreater(user.getUserId());
        				subsRedeemCalendar.setCredate(sdf.format(System.currentTimeMillis()));
        				cmSubsRedeemCalendarService.addRedeemCalendar(subsRedeemCalendar);
        			}
                }
            }
            resultMap.put("uploadFlag", uploadFlag);
            resultMap.put("errorMsg", errorMsg);
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "请检查模板是否正确");
        } finally {
            try {
            	if(input != null){
            		input.close();
            	}
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
		return resultMap;
	}
	
	private static boolean isValidDate(String str) {
		boolean convertSuccess = true;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		try {
			sdf.setLenient(false);
			sdf.parse(str);
		} catch (ParseException e) {
			convertSuccess=false;
		} 
		return convertSuccess;
	}
	
	/**
	 * 导出申购赎回日历信息
	 * 
	 * author: wu.long
	 * date: 2019年8月5日 下午7:19:38 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/exportRedeemCalendar")
	@SuppressWarnings("all")
	public void exportRedeemCalendar(HttpServletRequest request, HttpServletResponse response){
		CmSubsRedeemCalendar subsRedeemCalendar = new CmSubsRedeemCalendar();
		subsRedeemCalendar.setFundcode(request.getParameter("fundcode"));
		subsRedeemCalendar.setBBeginDate(request.getParameter("bBeginDate"));
		subsRedeemCalendar.setBEndDate(request.getParameter("bEndDate"));
		subsRedeemCalendar.setCalendartype("3".equals(request.getParameter("calendarType"))?null:request.getParameter("calendarType"));
		subsRedeemCalendar.setEBeginDate(request.getParameter("eBeginDate"));
		subsRedeemCalendar.setEEndDate(request.getParameter("eEndDate"));
		List<CmSubsRedeemCalendar> resultList = cmSubsRedeemCalendarService.selectRedeemCalendarListByParams(subsRedeemCalendar);
		try {
			if(null != resultList && 0 < resultList.size()){
				for (CmSubsRedeemCalendar cmSubsRedeemCalendar : resultList) {
					switch (cmSubsRedeemCalendar.getCalendartype()) {
					case "0":
						cmSubsRedeemCalendar.setCalendartype("申购日历");
						break;
					case "1":
						cmSubsRedeemCalendar.setCalendartype("赎回日历");
						break;
					case "2":
						cmSubsRedeemCalendar.setCalendartype("外部产品赎回日历");
						break;
					}
				}
				// 清空输出流
				response.reset();
				// 设置文件格式和名字
				response.setContentType("multipart/form-data");
				response.setHeader("Content-Disposition", "attachment;fileName=" + new String("申购赎回日历信息.xls".getBytes("gb2312"), "ISO8859-1"));
				ServletOutputStream os = response.getOutputStream();
				String columnName[] = { 
						"日历类型", "开放结束日", "基金代码", "基金名称", "基金成立日期", "预约开始日期", "预约结束日期", "开放日描述", "截止日描述", "预约结束日期位移量", "备注" };
				String beanProperty[] = {"calendartype","openenddt","fundcode","fundname","funddt","prestartdt","preenddt","opendtdesc","enddtdesc","preenddtdisplace","remark"};
				ExcelWriter.writeExcel(os, "申购赎回日历信息", 0, resultList, columnName, beanProperty);
				os.close(); // 关闭流
			}
		} catch (Exception e) {
			log.error("导出申购赎回日历信息异常：", e);
		}
	}
	
	
}
