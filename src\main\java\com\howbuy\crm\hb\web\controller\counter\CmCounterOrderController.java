package com.howbuy.crm.hb.web.controller.counter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acccenter.facade.query.idcert.QueryDealImageDataInfoFacade;
import com.howbuy.acccenter.facade.query.idcert.QueryDealImageDataInfoRequest;
import com.howbuy.acccenter.facade.query.idcert.QueryDealImageDataInfoResponse;
import com.howbuy.acccenter.facade.query.idcert.bean.TpDealImageDataFileBean;
import com.howbuy.acccenter.facade.query.querykycexamdtl.QueryKycExamDtlFacade;
import com.howbuy.acccenter.facade.query.querykycexamdtl.QueryKycExamDtlRequest;
import com.howbuy.acccenter.facade.query.querykycexamdtl.QueryKycExamDtlResponse;
import com.howbuy.acccenter.facade.query.querykycexamdtl.bean.ExamInfoBean;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.cc.center.feature.kycinfo.domain.UserTypeEnum;
import com.howbuy.cc.center.feature.question.domain.ExamType;
import com.howbuy.common.date.DateUtil;
import com.howbuy.crm.account.client.enums.custinfo.HkAcctCustStatusEnum;
import com.howbuy.crm.base.*;
import com.howbuy.crm.base.response.CoreReturnMessageDto;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.dto.DisAcTxAcctBeanDto;
import com.howbuy.crm.conscust.dto.HkCustInfoDto;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.ConscustAcctInfoService;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.constants.CrmTradeServerPathConstant;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.fixed.CmFixedIntention;
import com.howbuy.crm.hb.domain.hkconscust.HkConscust;
import com.howbuy.crm.hb.domain.joinclub.CmConscustsurveyanswer;
import com.howbuy.crm.hb.domain.joinclub.CmConscustsurveyrec;
import com.howbuy.crm.hb.domain.prosale.Prebookproductinfo;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.fixed.CmFixedIntentionService;
import com.howbuy.crm.hb.service.hkconscust.HkConscustService;
import com.howbuy.crm.hb.service.joinclub.CmConscustsurveyanswerService;
import com.howbuy.crm.hb.service.joinclub.CmConscustsurveyrecService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.web.dto.counter.CmCounterOrderVo;
import com.howbuy.crm.hb.web.dto.counter.CmCounterOrderfileFileMsg;
import com.howbuy.crm.hb.web.dto.counter.CmCounterOuterFileDto;
import com.howbuy.crm.hb.web.util.ListPageUtil;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.hkcust.service.HkAcctInfoService;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.nt.accountrelation.dto.CmRelationAccountSub;
import com.howbuy.crm.nt.accountrelation.service.CmRelationAccountSubService;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.core.webapp.util.SessionUserManager;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.prebook.dto.CmPreBookProductInfo;
import com.howbuy.crm.prebook.dto.CmPrebookBankInfo;
import com.howbuy.crm.prebook.dto.CxgFundInfoDto;
import com.howbuy.crm.prebook.dto.PreRelatedOrderDto;
import com.howbuy.crm.prebook.response.PiggyFundDetailInfoVO;
import com.howbuy.crm.prebook.service.HkCxgService;
import com.howbuy.crm.prebook.service.HkPrebookService;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.prebook.service.PrebookBusinessService;
import com.howbuy.crm.prosale.dto.BankInfo;
import com.howbuy.crm.prosale.request.GetInfoByParamRequest;
import com.howbuy.crm.prosale.response.GetCustBankInfoResponse;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import com.howbuy.crm.trade.common.constant.DataConstants;
import com.howbuy.crm.trade.common.constant.DfileConstants;
import com.howbuy.crm.trade.common.enums.counter.*;
import com.howbuy.crm.trade.common.response.BaseResponse;
import com.howbuy.crm.trade.common.util.CounterUtil;
import com.howbuy.crm.trade.model.counter.busiparam.HkCxgParamDto;
import com.howbuy.crm.trade.model.counter.busiparam.HkDepositParamDto;
import com.howbuy.crm.trade.model.counter.busiparam.HkVoucherParamDto;
import com.howbuy.crm.trade.model.counter.dto.*;
import com.howbuy.crm.trade.model.counter.po.CmCounterBusiness;
import com.howbuy.crm.trade.model.counter.po.CmCounterFiletype;
import com.howbuy.crm.trade.model.counter.po.CmCounterOrderSignfile;
import com.howbuy.crm.trade.model.counter.po.CmCounterOrderfileFile;
import com.howbuy.crm.trade.model.counter.request.SaveCmCounterOrderSignfileRequest;
import com.howbuy.crm.trade.model.counter.vo.CmCounterOrderSearchVo;
import com.howbuy.crm.transfervol.dto.CmCustTransferVol;
import com.howbuy.crm.transfervol.service.CustTransferVolService;
import com.howbuy.crm.util.exception.BusinessException;
import com.howbuy.dfile.HFileService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.enums.CrmCustInvestTypeEnum;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.enums.DisCodeEnum;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @Description: Controller
 * @version 1.0
 */
@Slf4j
@Controller
@RequestMapping(value = "/counter")
public class CmCounterOrderController  extends BaseCounterController{

	/**
	 * 300  M。
	 */
	public static final int MAX_SIZE_MB = 300;
	/**
	 * 下载文件限制 ：300M
	 */
	public static final long MAX_DOWNLOAD_SIZE = MAX_SIZE_MB * 1024 * 1024;
	@Autowired
	private QueryConscustInfoService queryConscustInfoService;

	@Resource(name="taskExecutor")
	private ThreadPoolTaskExecutor taskExecutor;

	@Autowired
	private QueryPreBookService queryPreBookService;

	@Autowired
	private DecryptSingleFacade decryptSingleFacade;

	@Autowired
	private PageVisitLogService pageVisitLogService;

	@Autowired
	private CmRelationAccountSubService cmRelationAccountSubService;

    @Autowired
	private CmFixedIntentionService cmFixedIntentionService;

    @Autowired
	private QueryDealImageDataInfoFacade queryDealImageDataInfoFacade;

    @Autowired
    private PrebookproductinfoService prebookproductinfoService;

	@Autowired
	private PrebookBasicInfoService prebookBasicInfoService;

    @Autowired
    private ConscustService conscustService;

	@Autowired
	private CustTransferVolService custTransferVolService;
	@Autowired
	private JjxxInfoService jjxxInfoService;
	@Autowired
	private CmConscustsurveyrecService cmConscustsurveyrecService;

	@Autowired
	private CmConscustsurveyanswerService cmConscustsurveyanswerService;
	@Autowired
	private QueryKycExamDtlFacade queryKycExamDtlFacade;
	@Autowired
	private ConscustAcctInfoService conscustAcctInfoService;
	@Autowired
	private HkAcctInfoService hkAcctInfoService;

	@Autowired
	private HkPrebookService hkPrebookService;
	@Autowired
	private HkCxgService hkCxgService;

	@Autowired
	private HkConscustService hkConscustService;

	@Autowired
	private PrebookBusinessService prebookBusinessService;

	@Value("${COUNTER_FILE_VISIT_PATH}")
    private String counterFileVisitPath;



	/**
	 * @description:(资料管理 进入页面 默认查询条件状态)
	 * @param loginRoles
	 * @return java.lang.String
	 * @author: haoran.zhang
	 * @date: 2024/11/26 17:59
	 * @since JDK 1.8
	 */
	private String getDefaultCurState(List<String> loginRoles){
		//注意 ： 以下策略是覆盖式
		String returnState=null;
//		[1] -待分部审核 、[5] -分部已审核 、[7] - OP初审通过、[-3] 审核通过、[-2] 待持牌人回访、[-1] 已退回、[14]-已撤回、[10] -作废
		//地区投顾助理
		if (loginRoles.contains("ROLE_SIC_ASSISTANT")) {
			returnState= StaticVar.CURSTAT_WAIT_CHECK;
		}
		//OP业务，则审核状态为“分部已审核”（高端柜台业务处理）
		//2024年8月1日  新增逻辑 ： 如果同时有[ROLE_OP_BM-资管管理岗]角色和 [ROLE_IC_ASSISTANT_II-高端柜台运营]的，高端柜台运角色优先 .
		//2024年8月1日   角色调整： 【ROLE_OP_BS】 业务处理 ，调整为 【ROLE_IC_ASSISTANT_II】
		if (loginRoles.contains("ROLE_OP_BM")) {
			returnState= "-3";
		}
		if (loginRoles.contains("ROLE_IC_ASSISTANT_II")) {
			returnState= StaticVar.CURSTAT_DEPT_PASS;
		}
		return returnState;
	}


    /**
	 * 跳转到柜台订单主列表页面
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listCounterOrder.do")
	public ModelAndView listCmCounterOrder(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/counter/listCounterOrder");

		List<String> loginRoles =getLonginRoles(request);

		//OP复审大陆业务,OP复审香港业务
		String opDlfs = null;
		//OP复审香港业务
		String opHkfs = null;
		//香港op
		String isHkop = null;
		for(String role : loginRoles){
			List<String> temp = AuthCache.getInstance().getOperListStr(role, "B020601");
			//香港op
			if(temp != null && temp.contains("20")){
				isHkop = "true";
			}
			//OP复审大陆业务,OP复审香港业务
			if(temp != null && temp.contains("6")){
				opDlfs = "true";
			}
			//OP复审香港业务
			if(temp != null && temp.contains("19")){
				opHkfs = "true";
			}

		}
		modelAndView.addObject("defaultCurStat", getDefaultCurState(loginRoles));
		modelAndView.addObject("opDLFS", opDlfs);
		modelAndView.addObject("opHKFS", opHkfs);
		modelAndView.addObject("isHKOP", isHkop);
		modelAndView.addObject("defaultBegUploadDt", DateTimeUtil.fmtDate(DateTimeUtil.addDaysFromNow(-30),"yyyyMMdd"));
		modelAndView.addObject("defaultEndUploadDt", DateTimeUtil.fmtDate(DateTimeUtil.getCurDate(),"yyyyMMdd"));
		//modelAndView.addObject("isOP", map.get("isOP"));

		// 当前用户是否有“投顾客户-客户姓名链接”权限
		modelAndView.addObject("hasCustLinkAuth", SessionUserManager.hasAuth("020107_1"));
		return modelAndView;
	}


    /**
     * @description:(客户是否签署 香港-储蓄罐代扣协议 且有效)
     * @param custNo
     * @return com.howbuy.crm.base.ReturnMessageDto<java.lang.String>
     * @author: haoran.zhang
     * @date: 2023/6/13 14:49
     * @since JDK 1.8
     */
	@RequestMapping("/signcxghkprotocol.do")
	@ResponseBody
	public  ReturnMessageDto<String> signCxgHkProtocol(String custNo){
		if(StringUtil.isEmpty(custNo)){
			return ReturnMessageDto.fail("客户号不能为空！");
		}
		boolean hasSign=hkCxgService.isSignCxgHkProtocol(custNo,DateUtil.formateDate(new Date(),DateUtil.YYYYMMDD));
		if(hasSign){
			return ReturnMessageDto.ok("");
		}
		return ReturnMessageDto.fail("");
	}


	/**
	 * @description:(客户是否签署 香港-储蓄罐代扣协议 且有效)
	 * @param
	 * @return com.howbuy.crm.base.ReturnMessageDto<java.lang.String>
	 * @author: haoran.zhang
	 * @date: 2023/6/13 14:49
	 * @since JDK 1.8
	 */
	@RequestMapping("/getconfigedcxgprod.do")
	@ResponseBody
	public CxgFundInfoDto getConfigedCxgProd(){
		return getConfigedCxg();
	}

	/**
	 * @description:(获取储蓄罐配置 产品信息)
	 * @param
	 * @return com.howbuy.crm.prebook.dto.CxgFundInfoDto
	 * @author: haoran.zhang
	 * @date: 2024/7/31 9:10
	 * @since JDK 1.8
	 */
	private CxgFundInfoDto getConfigedCxg(){
		List<PiggyFundDetailInfoVO>  configedList=hkCxgService.getConfigedHkPiggyFund();
		if(CollectionUtils.isNotEmpty(configedList)){

			PiggyFundDetailInfoVO detailInfo=configedList.get(0);

			CxgFundInfoDto fundInfoDto=new CxgFundInfoDto();
			fundInfoDto.setPiggyFundCode(detailInfo.getFundCode());
			fundInfoDto.setPiggyFundName(detailInfo.getFundName());
			return fundInfoDto;
		}
		return null;
	}

	@RequestMapping("/getsignedcxgprod.do")
	@ResponseBody
	public  CxgFundInfoDto getSignedCxgProd(String custNo){
		return hkCxgService.getHkCustPiggySignedFundInfo(custNo);
	}

	/**
	 * @description:(选择不同的业务，返回对应的储蓄罐产品信息)
	 * @param custNo 客户号
	 * @param busiId 58-好买香港-储蓄罐协议签署  59-好买香港-储蓄罐底层变更   60-好买香港-储蓄罐协议终止
	 * @return com.howbuy.crm.prebook.dto.CxgFundInfoDto
	 * @author: haoran.zhang
	 * @date: 2024/7/31 8:34
	 * @since JDK 1.8
	 */
	@RequestMapping("/getcxgprodbybusiid.do")
	@ResponseBody
	public  ReturnMessageDto<CxgFundInfoDto> getCxgProdByBusiId(String custNo,
																String busiId){
		CounterBusiEnum busiEnum=CounterBusiEnum.getEnum(busiId);
		if(busiEnum==null){
            return ReturnMessageDto.fail("业务类型错误！");
        }
		if(!CounterUtil.CXG_RELATED_BUSI_LIST.contains(busiEnum)){
            return ReturnMessageDto.fail(String.format("不支持的业务类型：%s！",busiEnum.getDesc()));
        }

		//58-好买香港-储蓄罐协议签署  59-好买香港-储蓄罐底层变更  查询当前参数配置
		if(CounterBusiEnum.CXG_PROTOCAL_SIGN_HK==busiEnum  ||
				CounterBusiEnum.CXG_PROTOCAL_CHANGE_HK==busiEnum){
			return ReturnMessageDto.ok("",getConfigedCxg());
		}
		// 60-好买香港-储蓄罐协议终止  查询当前签署的储蓄罐产品信息
		if(CounterBusiEnum.CXG_PROTOCAL_TERMINATE_HK==busiEnum){
			CxgFundInfoDto fundInfoDto= hkCxgService.getHkCustPiggySignedFundInfo(custNo);
			return ReturnMessageDto.ok("",fundInfoDto);
		}
		return ReturnMessageDto.ok("",null);
	}


	/**
	 * 审核前置 操作。 替换 [/getOutUrlInfo.do]
	 * @param request
	 * @return Map<String, Object>
	 */
	@ResponseBody
	@RequestMapping("/executebeforecheck.do")
	public BaseResponse<CmCounterPreCheckDto> executeBeforeCheck(HttpServletRequest request) {
		Map<String,String> paramMap= Maps.newHashMap();
		paramMap.put("orderId",request.getParameter("orderId"));
		paramMap.put("checkLevel",request.getParameter("checkLevel"));
		paramMap.put("operatorNo",getLoginUserId(request));

//		String orderId, CounterCheckLevelEnum checkLevelEnum, String operatorNo
		return getPostEntityByMap(CrmTradeServerPathConstant.EXECUTE_BEFORE_CHECK,
				paramMap,
				new ParameterizedTypeReference<BaseResponse<CmCounterPreCheckDto>>() {});


	}


	/**
	 *
	 * @param configedUrl
	 * @param paramsMap
	 * @return
	 */
	private String joinRedirectUrlLink(String configedUrl,Map<String,String> paramsMap){
		if(paramsMap==null|| paramsMap.isEmpty()){
			return configedUrl;
		}
		StringBuilder sb=new StringBuilder(configedUrl);
		sb.append(sb.indexOf("?")>=0?"&":"?");

		paramsMap.forEach((k,v)->{
			sb.append(k).append("=").append(v).append("&");
		});
		String returnString=sb.toString();
		return returnString.substring(0,returnString.length()-1);
	}


	/**
	 * 更新订单的卡信息。 非常规逻辑。待重构
	 * @param orderId
	 * @param inBankAcct
	 * @param outBankAcct
	 * @param afterCardType 变更后银行卡类型：1：新卡；2：当前客户已绑定的银行卡
	 * @return
	 */
	@RequestMapping("/updateOrderBankInfo.do")
	@ResponseBody
	public BaseResponse<Integer> orderCheckPage(String orderId,String inBankAcct,String outBankAcct,String afterCardType ){

		Map<String,String> postParam = new HashMap<String,String>();
		postParam.put("orderId", orderId);
		postParam.put("inBankAcct", inBankAcct);
		postParam.put("outBankAcct", outBankAcct);
		postParam.put("afterCardType", afterCardType);
		log.info("订单id:{}.[06-卡信息变更业务]更新订单的卡信息为：{}",orderId, JSONObject.toJSONString(postParam));
		return getPostEntityByMap(CrmTradeServerPathConstant.UPDATE_ORDER_BANK_INFO,
				postParam,new ParameterizedTypeReference<BaseResponse<Integer>>(){});

	}

	/**
	 * 加载订单审核页面[替换 /orderCheck.do]
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/orderCheckPage.do")
	public ModelAndView orderCheckPage(HttpServletRequest request){
		String id = request.getParameter("id");
		String checkLevel = request.getParameter("checkLevel");
		String menucode = request.getParameter("menucode");
		String optcode = request.getParameter("optcode");

		Assert.notNull(id);

		Map<String, Object> map=Maps.newHashMap();
		map.put("id", id);
		map.put("checkLevel", checkLevel);
		map.put("menucode", menucode);
		map.put("optcode", optcode);

		//订单信息
		CmCounterOrderDto orderInfo=getOrderById(id);
		map.put("orderInfo", orderInfo);
		CounterBusiEnum busiEnum=CounterBusiEnum.getEnum(orderInfo.getBusiId());
		CounterBusiProidEnum busiProidEnum=CounterBusiProidEnum.getEnum(orderInfo.getBusiProId());
		String forId=orderInfo.getForId();

		//部分业务 禁止crm做审核操作
        if(CounterUtil.forbiddenCrmCheck().contains(busiEnum.getKey())){
            throw new BusinessException("该业务禁止crm做审核操作");
        }


		//查询文件列表  /querycounterorderfiledtolist  CrmTradeServerPathConstant.GET_ORDER_FILE_LIST;
		//参数：List<String> orderIdList,String checkLevel
		List<CmCounterOrderFileDto> list=listCmCounterOrderFileDto(id, checkLevel);
        //crm 配置的文件类型列表
		map.put("list", list);

		//外部获取的业务文件类型列表 Eg： 海外签约线上签约，客户签署的协议列表
		List<CmCounterOuterFileDto> outerFileList=Lists.newArrayList();
		if(CounterUtil.PREBOOK_HK_TRADELIST_ONLINE.contains(busiEnum)){
			outerFileList=getHkSignFileListByPreId(new BigDecimal(forId));
		}
		map.put("outerFileList", outerFileList);



		//客户信息
		QueryConscustInfoRequest queryCustRequest=new QueryConscustInfoRequest();
		queryCustRequest.setConscustno(orderInfo.getConscustno());
		QueryConscustInfoResponse response=queryConscustInfoService.queryConscustInfo(queryCustRequest);
		ConscustInfoDomain custInfo=response.getConscustinfo();

		//操作人员
		String  operatorNo=getLoginUserId(request);
		map.put("userId",operatorNo);
		//不同业务存储的 特殊字段 json字符串
		String specialBusiParam=orderInfo.getSpecialBusiParam();

		// 获取常量缓存
		ConstantCache constantCache = ConstantCache.getInstance();
		// 设置页面基金编码和基金名称
		JjxxInfo  jjxxInfo=null;
		if (StringUtils.isNotBlank(orderInfo.getFundCode())) {
			jjxxInfo=jjxxInfoService.getJjxxByJjdm(orderInfo.getFundCode(),false);
			map.put("fundCode", jjxxInfo.getJjdm());
			map.put("fundName", jjxxInfo.getJjjc());
		}
		map.put("conscustno", custInfo.getConscustno()); // 设置投顾客户号（跳转客户基本信息页）
		map.put("custName", custInfo.getCustname());// 设置页面客户姓名
		// 设置页面客户类型
		map.put("invstType", orderInfo.getCustType());
		map.put("invstTypeName", orderInfo.getCustTypeName());


		// 设置页面业务类型
		map.put("counterBusiId", orderInfo.getBusiId());
		map.put("counterBusiName", orderInfo.getBusiIdName());
		// 设置业务属性
		map.put("counterBusiProId", orderInfo.getBusiProId());
		// 设置是否进入OP：1-是；0-否
		if (StringUtils.isNotBlank(orderInfo.getProductType())) {
			map.put("counterProductType", orderInfo.getProductType());
		}

		String optUrl = "";

		//定投意向单 业务
		if(CounterUtil.FIXED_AIP_BUSILIST.contains(busiEnum)){
			// 获取定投意向单的信息
			Map<String, Object> paramMap = new HashMap<>();
			paramMap.put("planid", forId);
			CmFixedIntention cmFixedIntention = cmFixedIntentionService.getCmFixedIntention(paramMap);//TODO: 意向单逻辑待迁移
			String planrate = cmFixedIntention.getPlanrate();
			// 定投周期
			map.put("planrateName", constantCache.getVal("fixedPlanRate", planrate));
			// 定投期数
			map.put("planTotalNum", cmFixedIntention.getPlantotalnum());
			// 定投金额/期
			map.put("planamount", cmFixedIntention.getPlanamount());
			// 支付方式
			map.put("planPaymentTypeName", constantCache.getVal("fixedpaytype", cmFixedIntention.getPaytype()));

			return new ModelAndView("/counter/checkFixedInvest", "map", map);
		}

        //换卡业务     op初审  页面 .
		//特殊处理
		if(CounterBusiEnum.CARD_CHANGE.equals(busiEnum)){
			//分布审核 跳转到 crm通用页面
			//机构的 跳转到  crm通用页面
			if(CounterCheckLevelEnum.FBSH.getKey().equals(checkLevel)){
				map.put("idtype", custInfo.getIdtype());
				map.put("idnoCipher", custInfo.getIdnoCipher());
				map.put("idnoMask", custInfo.getIdnoMask());
				return new ModelAndView("/counter/checkNT", "map", map);
			}else if(!CrmCustInvestTypeEnum.PERSONAL.getCode().equals(custInfo.getInvsttype())){
				//机构的 仍然走 正常crm 审核页面
				map.put("idtype", custInfo.getIdtype());
				map.put("idnoCipher", custInfo.getIdnoCipher());
				map.put("idnoMask", custInfo.getIdnoMask());
				return new ModelAndView("/counter/checkNT", "map", map);
			}else{
				//初审 跳转到 特殊页面，再决定跳转到 外部链接
				map.put("inbankacct", orderInfo.getInBankAcct());
				map.put("outbankacct", orderInfo.getOutBankAcct());
				//卡类型有值取值，没有值默认取新卡
				map.put("aftercardtype",
						StringUtil.isNullStr(orderInfo.getAfterCardType())?StaticVar.COUNTER_CHANGE_BANK_NEW  //1
								:orderInfo.getAfterCardType());

				//获取下拉框 卡号列表
				GetInfoByParamRequest req = new GetInfoByParamRequest();
				req.setConscustno(orderInfo.getConscustno());
				GetCustBankInfoResponse res = queryPreBookService.getCustBankInfo(req);
				List<BankInfo> banks = Lists.newArrayList();
				if(res.isSuccessful()){
					banks.addAll(res.getBankInfoList());
				}
				List<Map<String,Object>> bankList = new ArrayList<>();
				for(BankInfo bankinfo : banks){
					Map<String, Object> bankmap = new HashMap<>(2);
					bankmap.put("id", bankinfo.getBankacct());
					bankmap.put("text", bankinfo.getBankacct()+" "+bankinfo.getBankname());
					bankList.add(bankmap);
				}
				map.put("bankList",bankList);


				CmCounterBusiness  businessInfo=getBusinessById(orderInfo.getBdid());

				Map<String,String> paramMap=Maps.newHashMap();
				paramMap.put("orderId",orderInfo.getId());
				paramMap.put("busiId",businessInfo.getBusiId());
				paramMap.put("operatorNo",operatorNo );
				paramMap.put("source","crm" );
				paramMap.put("forId",orderInfo.getForId());

				//变更后银行卡1 != 新卡，则跳转[中台页面]进行份额迁移
//				参数：资料ID，变更前银行卡，变更后银行卡，预申请单号，客户姓名
//				OPERATORNO,SOURCE,FORID,BUSIID,HBONENO,ORDERID,INBANKACCT,OUTBANKACCT
				Map<String,String> diffParamMap=Maps.newHashMap(paramMap);
				diffParamMap.put("hboneNo",custInfo.getHboneno());
//				paramMap.put("inBankAcct", orderInfo.getInBankAcct());
//				paramMap.put("outBankAcct", orderInfo.getOutBankAcct());
				map.put("oldBankUrl",joinRedirectUrlLink(businessInfo.getOutInitUrlBank(),diffParamMap));


//				变更后银行卡1 = 新卡，则跳转后台页面
//				参数：资料ID，变更前银行卡号，合同号，客户姓名，手机号，身份证号
//				OPERATORNO,ORDERID,CUSTNAME,IDNO,MOBILE,FORID
				Map<String,String> sameParamMap=Maps.newHashMap(paramMap);
				sameParamMap.put("custName",custInfo.getCustname());
				if(StringUtils.isNotBlank(custInfo.getIdnoCipher())){
					String idno = decryptSingleFacade.decrypt(custInfo.getIdnoCipher()).getCodecText();
					sameParamMap.put("idNo",idno);
				}
				if(StringUtils.isNotBlank(custInfo.getMobileCipher())){
					String mobile = decryptSingleFacade.decrypt(custInfo.getMobileCipher()).getCodecText();
					sameParamMap.put("mobile",mobile);
				}
				map.put("newBankUrl",joinRedirectUrlLink(businessInfo.getOutInitUrl(),sameParamMap));
				map.put("userId",getLoginUserId());

				return new ModelAndView("/counter/checkBankCard", "map", map);

			}

		}

		//换卡

		//份额转让
		if(CounterBusiEnum.TRADE_TRANS_VOL.equals(busiEnum)){
			// 获取份额转让信息
			CmCustTransferVol transferVol=custTransferVolService.getTransferVolById(new BigDecimal(forId));
			// 设置页面转让人
			if (StringUtils.isNotBlank(transferVol.getTransferor())) {
				map.put("transferorname", transferVol.getTransferorname());
			}
			// 设置页面受让人
			if (StringUtils.isNotBlank(transferVol.getAssignee())) {
				map.put("assigneename", transferVol.getAssigneename());
			}
			// 设置页面转让份额
			if (transferVol.getTransfervol()!=null) {
				map.put("transfervol", transferVol.getTransfervol());
			}
			return new ModelAndView("/counter/checkST", "map", map);
		}

		//预约相关
		if (CounterUtil.PREBOOK_BUSILIST.contains(busiEnum)) {
			return toPreBookCheckHtml(map, orderInfo, checkLevel, custInfo, jjxxInfo);
		}

		//好买好臻入会 审核   单独页面
		if(CounterBusiEnum.RISK_ANALYSE_HZ.equals(busiEnum)
		|| CounterBusiEnum.RISK_ANALYSE_HOWBUY.equals(busiEnum)){
			ModelAndView mv = dealJoinClubCheck(forId,map,busiEnum);
			if(null!=mv){
				return mv;
			}
		}

		//业务属性：非交易类、
		//业务属性：交易类中：  交易-修改分红方式
		if (CounterBusiProidEnum.NON_TRADE.equals(busiProidEnum)
				|| CounterBusiProidEnum.NON_TRADE_RELATION.equals(busiProidEnum)
				|| CounterBusiEnum.BONUS_TYPE_MODIFY.equals(busiEnum)) {

			//是否为 管理账户相关业务
			boolean isRelatedAccount=CounterUtil.getRelationRelatedBusiList().contains(busiEnum);

			Map<String, Object> newparam = new HashMap<String, Object>();
			newparam.put("orderid", orderInfo.getForId());

			map.put("busid", orderInfo.getBusiId());
			List<CmRelationAccountSub> relationsublist = cmRelationAccountSubService.listRelationAccountSub(newparam);
			for (CmRelationAccountSub relationaccountsub:relationsublist){
				relationaccountsub.setRelationstr(constantCache.getVal("relatedAccountRole", relationaccountsub.getRelation()));
				relationaccountsub.setRelationstatestr(constantCache.getVal("relationstate", relationaccountsub.getRelationstate()));
			}

			if(CounterBusiEnum.OPEN_ACCT_HZ.equals(busiEnum) || CounterBusiEnum.OPEN_ACCT_HOWBUY.equals(busiEnum)){
				map.put("idtype", custInfo.getIdtype());
		        map.put("idnoCipher", custInfo.getIdnoCipher());
		        map.put("idnoMask", custInfo.getIdnoMask());
			}

			map.put("relationsublist", relationsublist);
			map.put("isRelatedAccount", isRelatedAccount);

            // busiCode= 54- 好买香港-开户入金 57-好买香港-现金存入   存储特殊属性
			// 历史逻辑  设计问题。此处应该以特殊业务来区分 是否赋值， 而不是直接判断非空。 2024年4月16日重构
			if (CounterBusiEnum.INCOME_PAYMENT_HK==busiEnum || CounterBusiEnum.CASH_DEPOSIT_HK==busiEnum) {
				Map<String, Object> depositMap=Maps.newHashMap();
				if(StringUtils.isNotBlank(specialBusiParam)){
					HkDepositParamDto depositDto=JSON.parseObject(specialBusiParam,HkDepositParamDto.class);
					//银行卡 资金账号
					depositMap.put("hkCpAcctNo", depositDto.getHkCpAcctNo());
					//银行卡 展示名称
					depositMap.put("bankAcctMask",depositDto.getBankAcctMask());
					depositMap.put("curCode", depositDto.getCurCode());
					depositMap.put("curCodeDesc", constantCache.getVal("currencys", depositDto.getCurCode()));
					depositMap.put("amount", depositDto.getAmount());
					depositMap.put("refNo", depositDto.getRefNo());
					depositMap.put("tradeChannel", depositDto.getTradeChannel());
					depositMap.put("hkCustNo", depositDto.getHkCustNo());
					depositMap.put("memo", depositDto.getMemo());
				}
				map.put("depositInfo", depositMap);
			}

			//58-好买香港-储蓄罐协议签署  59-好买香港-储蓄罐底层变更   60-好买香港-储蓄罐协议终止  字段使用
			if(CounterUtil.CXG_RELATED_BUSI_LIST.contains(busiEnum)){
				Map<String, Object> cxgMap=Maps.newHashMap();
				if(StringUtils.isNotBlank(specialBusiParam)){
					HkCxgParamDto paramDto=JSON.parseObject(specialBusiParam,HkCxgParamDto.class);
					//前端只展示 1 个产品
					if(CollectionUtils.isNotEmpty(paramDto.getCxgFundList())){
						cxgMap.put("piggyFundCode", paramDto.getCxgFundList().get(0).getPiggyFundCode());
						cxgMap.put("piggyFundName", paramDto.getCxgFundList().get(0).getPiggyFundName());
					}
				}
				map.put("cxgInfo", cxgMap);
			}
			return new ModelAndView("/counter/checkNT", "map", map);
			// 业务属性：交易类，包含“交易-购买”、“交易-赎回”、“交易-撤单”
		}

		//其他交易类的大类
		List<CounterBusiProidEnum> tradeBusiProdList= Lists.newArrayList(CounterBusiProidEnum.TRADE_BUY,CounterBusiProidEnum.TRADE_REDEEM,CounterBusiProidEnum.TRADE_RECALL);
		if(tradeBusiProdList.contains(busiProidEnum)){
			log.error("类型：{} 未做处理！ 订单id为 ：{} ",busiProidEnum.getDesc(),orderInfo.getId());
		}

		return new ModelAndView(optUrl, "map", map);
	}


	/**
	 * @description: 资料审核页面：【预约相关】
	 * @param map 页面参数
	 * @param orderInfo 资料信息
	 * @param checkLevel 审核级别
	 * @param custInfo 客户信息
	 * @param jjxxInfo 基金信息
	 * @return org.springframework.web.servlet.ModelAndView 页面
	 * @author: jin.wang03
	 * @date: 2024/4/26 13:16
	 * @since JDK 1.8
	 */
	private ModelAndView toPreBookCheckHtml(Map<String, Object> map, CmCounterOrderDto orderInfo, String checkLevel,
											ConscustInfoDomain custInfo, JjxxInfo jjxxInfo) {
		String forId = orderInfo.getForId();
		CounterBusiEnum busiEnum = CounterBusiEnum.getEnum(orderInfo.getBusiId());

		BigDecimal preId = new BigDecimal(forId);
		CmPreBookProductInfo preBookInfo = prebookBasicInfoService.getPreBookById(preId);
		if (preBookInfo == null) {
			throw new BusinessException(String.format("根据预约Id:%s 查询不到预约信息！", forId));
		}
		String specialBusiParam=orderInfo.getSpecialBusiParam();

		//预约
		map.put("preBookInfo", preBookInfo);

		//基金为必输项
		assert jjxxInfo != null;
		map.put("sfxg", jjxxInfo.getSfxg());
		map.put("zzxs", jjxxInfo.getZzxs()); //组织形式   2-有限合伙  其他 非有限合伙
		map.put("preId", forId);

		// 设置页面预约类型
		if (StringUtils.isNotBlank(preBookInfo.getPretype())) {
			map.put("preType", preBookInfo.getPretype());
			map.put("preTypeName", CounterPreTypeEnum.getDesc(preBookInfo.getPretype()));
		}

		map.put("tradetype", preBookInfo.getTradeType());
		map.put("buyamt", preBookInfo.getBuyamt());
		map.put("sellvol", preBookInfo.getSellvol()==null?"":String.format("%.6f", preBookInfo.getSellvol()));
		map.put("sellamt", preBookInfo.getSellAmt()==null?"":String.format("%.4f", preBookInfo.getSellAmt()));
		if (StringUtils.isNotBlank(preBookInfo.getRedeemMode())) {
			map.put("redeemMode", RedeemModeEnum.getDescription(preBookInfo.getRedeemMode()));
			map.put("redeemModeCode", preBookInfo.getRedeemMode());
		}
		//货币
		map.put("currencyDesc",CurrencyEnum.getDescription(preBookInfo.getCurrency()));

		//香港产品
		if(YesOrNoEnum.YES.getCode().equals(preBookInfo.getSfxg())) {
			//支付方式
			List<String> paymentList= PaymentModeEnum.getPaymentEnumListByValue(preBookInfo.getPaymentMode())
					.stream().map(PaymentModeEnum::getDesc).collect(Collectors.toList());

			//赎回方向
			List<String> redeemList= RedeemDirectEnum.getRedeemEnumListByValue(preBookInfo.getRedeemDirection())
					.stream().map(RedeemDirectEnum::getDesc).collect(Collectors.toList());

			map.put("paymentDesc", String.join(",",paymentList));
			map.put("redeemDesc", String.join(",",redeemList));
			map.put("divModeDesc", DivModeEnum.getName(preBookInfo.getDivMode()));
			//卡信息列表
			List<CmPrebookBankInfo>  hkBankList=prebookBasicInfoService.getHkBankAcctInfoList(preId);
			map.put("hkBankList", hkBankList);

			//56-香港-交易打款凭证
			if(CounterBusiEnum.TRADE_HK_PAYMENT_VOUCHER==busiEnum){
				if (StringUtils.isNotBlank(specialBusiParam)) {
					HkVoucherParamDto paramDto= JSON.parseObject(specialBusiParam,HkVoucherParamDto.class);
					// 【汇款金额】 备注：客户APP下单时支持传打款凭证，将触发CRM生成一条业务类型为“好买香港-交易打款凭证”的审核流水
					map.put("hkCustUploadRemitAmt",
							paramDto.getRemitAmt() == null ?
									"" : String.format("%.4f", paramDto.getRemitAmt()));
					// 【汇款币种】 备注：客户APP下单时支持传打款凭证，将触发CRM生成一条业务类型为“好买香港-交易打款凭证”的审核流水
					map.put("hkCustUploadCurrencyDesc", paramDto.getCurrency() == null ?
							"" : CurrencyEnum.getDescription(paramDto.getCurrency()));
					// 【凭证备注】
					map.put("hkUploadRemark", paramDto.getRemark() == null ?
							"" : paramDto.getRemark());

				}
			}





		}

		// 好买-售前留痕材料、好臻售前留痕材料
		if (CounterBusiEnum.LEGAL_DOCUMENT.equals(busiEnum) || CounterBusiEnum.HZ_LEGAL_DOCUMENT.equals(busiEnum)) {
			if (StringUtils.isNotBlank(specialBusiParam)) {
				JSONObject jsonObject = JSON.parseObject(specialBusiParam);
				map.put("legalDocSendCustomerDt", jsonObject.getString("legalDocSendCustomerDt"));
			}
		}

		//【OP初审】 业务类型 = 非有限合伙类产品认申购/有限合伙类产品认申购/追加 且 产品分销 [好臻|好买]未分销开户， 禁止【审核通过】
		boolean forbiddenPass=false;
		//业务类型 = 非有限合伙类产品认申购/有限合伙类产品认申购 且 OP初审
		List<CounterBusiEnum> lpList=Lists.newArrayList(CounterBusiEnum.TRADE_BUY_NON_LP,CounterBusiEnum.TRADE_BUY_LP);
		if(lpList.contains(busiEnum) && CounterCheckLevelEnum.OPCS.getKey().equals(checkLevel)){
			DisChannelCodeEnum disChannelCodeEnum=jjxxInfoService.getDisCodeEnumByJjdm(preBookInfo.getPcode());
			if(disChannelCodeEnum .equals(DisChannelCodeEnum.HZ)){
				boolean isExistDisAccount=isExistsDisAccount(custInfo.getHboneno(), DisCodeEnum.HZ);
				log.info("资料编号:{} 对应预约preId:{},一账通号：{}，产品对应的分销渠道：{}，是否存在分销账户：{}", orderInfo.getId(), forId, custInfo.getHboneno(),disChannelCodeEnum.getDescription(),isExistDisAccount);
				if(!isExistDisAccount){
					forbiddenPass=true;
				}
			}
		}
		//禁止审核通过按钮
		map.put("forbiddenPass", forbiddenPass);
		return new ModelAndView("/counter/checkTD", "map", map);
	}

	/**
	 * 是否 已开分销账户  true标识开户
	 * @return
	 */
	private boolean isExistsDisAccount(String hboneNo,DisCodeEnum disCodeEnum){
		CoreReturnMessageDto<DisAcTxAcctBeanDto> disAcTxAcctResp=conscustAcctInfoService.getDisAcTxAcctInfo(hboneNo, disCodeEnum);
		if(disAcTxAcctResp!=null &&
				disAcTxAcctResp.getReturnObject()!=null &&
				disAcTxAcctResp.getReturnObject().isValid() ){
			//是否对应分销  未开户
			return true;
		}
		return false;
	}

	/**
	 * 处理资料菜单入会审核相关信息
	 * @param forId
	 * @param map
	 * @param busiEnum
	 * @return
	 */
	private ModelAndView dealJoinClubCheck(String forId, Map<String, Object> map, CounterBusiEnum busiEnum) {
		map.put("appserialno",forId);
		map.put("userId",getLoginUserId());
		Map<String, String> param = new HashMap<String, String>();
		param.put("appserialno", forId);
		CmConscustsurveyrec rec = cmConscustsurveyrecService.getCmConscustsurveyrec(param);
		String conscustno = "";
		String investtype = "";
		//问卷信息
		if (rec != null) {
			conscustno = rec.getConscustno();
			//map.put("checkadvice", rec.getCheckadvice());
			map.put("singdate", rec.getSingdate());
		}
		if (StringUtil.isNotNullStr(conscustno)) {
			Conscust cust = conscustService.getConscust(conscustno);

			//拼接客户信息
			if (cust != null) {
				investtype = cust.getInvsttype();

				if(StringUtil.isNotNullStr(cust.getIdnoCipher())){
					cust.setIdno(decryptSingleFacade.decrypt(cust.getIdnoCipher()).getCodecText());
				}
				if(StringUtil.isNotNullStr(cust.getAddrCipher())){
					cust.setAddr(decryptSingleFacade.decrypt(cust.getAddrCipher()).getCodecText());
				}
				if(StringUtil.isNotNullStr(cust.getEmailCipher())){
					cust.setEmail(decryptSingleFacade.decrypt(cust.getEmailCipher()).getCodecText());
				}

				//是否好臻好买已入会
				if (StringUtils.isNotBlank(cust.getIdtype()) && StringUtils.isNotBlank(cust.getIdno())) {
					if (ObjectUtils.ObjectToInteger(cust.getIsjoinclub()) > 0) {
						map.put("hasjoinclub", "true");
					} else {
						map.put("hasjoinclub", "false");
					}
					if (ObjectUtils.ObjectToInteger(cust.getIshzjoinclub()) > 0) {
						map.put("hashzjoinclub", "true");
					} else {
						map.put("hashzjoinclub", "false");
					}
				}
				map.put("cust", cust);
				param.clear();
				param.put("surveyserialno", forId);
				//获取题目和已选择选项
				List<CmConscustsurveyanswer> answerList = cmConscustsurveyanswerService.listCmConscustsurveyanswer(param);
				Map<String, String> ansmap = new HashMap<String, String>();
				Map<String, String> erroransmap = new HashMap<String, String>();
				for (CmConscustsurveyanswer obj : answerList) {
					ansmap.put(obj.getQuestionid(), obj.getAcode());
					if ("0".equals(obj.getErrorFlag())) {
						erroransmap.put(obj.getQuestionid(), obj.getErrorFlag());
					}
				}
				map.put("ansmap", ansmap);
				map.put("erroransmap", erroransmap);

				QueryKycExamDtlRequest req = new QueryKycExamDtlRequest();
				req.setOutletCode("CRM");
				//好臻 或  好买
				QueryKycExamDtlResponse res = new QueryKycExamDtlResponse();
				ModelAndView modelAndView = new ModelAndView();
				if (StaticVar.INVST_TYPE_PERSONAL.equals(investtype)) {
					//个人好臻
					if(CounterBusiEnum.RISK_ANALYSE_HZ.equals(busiEnum)){
						req.setDisCode(DisChannelCodeEnum.HZ.getCode());
					}else if (CounterBusiEnum.RISK_ANALYSE_HOWBUY.equals(busiEnum)){
						//个人好买
						req.setDisCode(DisChannelCodeEnum.HOWBUY.getCode());
					}
					req.setExamType(ExamType.HIGH_END.getValue());
					req.setInvstType(UserTypeEnum.PERSONAL.getValue());
					log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(req));
					res = queryKycExamDtlFacade.execute(req);
					log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(res));
					ExamInfoBean domain = res.getExamInfoBean();
					if (domain != null) {
						map.put("domain", domain);
						map.put("idtypeval", ConstantCache.getInstance().getVal("idtype", cust.getIdtype()));
						map.put("qcount", domain.getQuestions().size());
						String viewName = "";
						if(CounterBusiEnum.RISK_ANALYSE_HZ.equals(busiEnum)){
							viewName="joinclub/checkHzJoinClub";
						}else if (CounterBusiEnum.RISK_ANALYSE_HOWBUY.equals(busiEnum)){
							viewName="joinclub/checkHowbuyJoinClub";
						}

						modelAndView.addAllObjects(map);
						modelAndView.setViewName(viewName);
						return modelAndView;
					} else {
						log.info("queryKycExamDtlFacade.execute(req)接口未返回任何个人问卷信息！");
					}
				} else {
					//获取机构问卷
					req.setExamType(ExamType.INSTITUTION.getValue());
					req.setInvstType(UserTypeEnum.INSTITUTION.getValue());
					//机构好臻
					if(CounterBusiEnum.RISK_ANALYSE_HZ.equals(busiEnum)){
						req.setDisCode(DisChannelCodeEnum.HZ.getCode());
					}else if (CounterBusiEnum.RISK_ANALYSE_HOWBUY.equals(busiEnum)){
						//机构好买
						req.setDisCode(DisCodeEnum.FOF.getCode());
					}
					log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(req));
					res = queryKycExamDtlFacade.execute(req);
					log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(res));
					ExamInfoBean domain = res.getExamInfoBean();
					if (domain != null) {
						map.put("domain", domain);

						// 根据投资者类型不同，展示不同的证件类型
						if (StaticVar.INVST_TYPE_ORG.equals(cust.getInvsttype())) {
							map.put("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfInstNew", cust.getIdtype()));
						} else if (StaticVar.INVST_TYPE_FUND.equals(cust.getInvsttype())) {
							map.put("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfFund", cust.getIdtype()));
						}else if (StaticVar.INVST_TYPE_PRODUCT.equals(investtype)) {
							map.put("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfProduct", cust.getIdtype()));
						}
						// 设置题目总数
						map.put("qcount", domain.getQuestions().size());
						String viewName = "";
						if(CounterBusiEnum.RISK_ANALYSE_HZ.equals(busiEnum)){
							viewName="joinclub/checkHzJoinOrgClub";
						}else if (CounterBusiEnum.RISK_ANALYSE_HOWBUY.equals(busiEnum)){
							viewName="joinclub/checkHowbuyJoinOrgClub";
						}
						modelAndView.addAllObjects(map);
						modelAndView.setViewName(viewName);
						return modelAndView;

					} else {
						log.info("queryKycExamDtlFacade.execute(req)接口未返回任何机构问卷信息！");
					}
				}
			}
		}
		return null;
	}


	/**
	 * @api {GET} /counter/getconfirmbeforecheck.do getConfirmBeforeCheck()
	 * @apiVersion 1.0.0
	 * @apiGroup CmCounterOrderController
	 * @apiName getConfirmBeforeCheck()
	 * @apiDescription 审核资料之前的 confirm信息 汇总在这里: 注意：不是业务校验，只是confirm提示信息
	 * @apiParam (请求参数) {String} orderId
	 * @apiParam (请求参数) {String} passResult
	 * @apiParam (请求参数) {String} checkLevel
	 * @apiParamExample 请求参数示例
	 * checkLevel=X6ksBnCBr&orderId=vVxlL3y&passResult=I9KB7M5DL2
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"I","returnMsg":"AV","returnObject":"xfIiJWO0","returnList":["QA2LqJ"]}
	 */
	@RequestMapping("/getconfirmbeforecheck.do")
	@ResponseBody
	public ReturnMessageDto<String> getConfirmBeforeCheck(@RequestParam(value = "orderId") String orderId,
														  @RequestParam(value = "passResult") String passResult,
														  @RequestParam(value = "checkLevel") String checkLevel){
		StringBuilder sb = new StringBuilder();

		//校验预约
		CmCounterOrderDto orderDto=getOrderById(orderId);
        //是否为 审核通过
		boolean isPass=passResult.equals(YesNoEnum.Y.getCode());

		if (isPass) {
			sb.append("是否确认审核通过？");
		} else {
			sb.append("是否确认审核退回？");
		}
		//实际业务场景： 一定返回 二次确认信息
		return ReturnMessageDto.fail(sb.toString());
	}


	/**
	 * 保存订单审核结果
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/saveCheck.do")
	public BaseResponse<String> saveCheck(HttpServletRequest request) {

		String passResult = request.getParameter("passResult"); // 1-确认审核通过   0-审核退回
		String orderId = request.getParameter("id");
		String checkAdvice = request.getParameter("checkAdvice");
		String checkLevel = request.getParameter("checkLevel");

		CounterCheckFileParamVo checkVo=new CounterCheckFileParamVo();

		checkVo.setOperatorNo(getLoginUserId(request));
		checkVo.setCheckAdvice(checkAdvice);
		checkVo.setCheckLevelEnum(CounterCheckLevelEnum.getEnum(checkLevel));
		checkVo.setCheckPass("1".equals(passResult));
		checkVo.setOrderId(orderId);
		// ！！根据具体的业务类型，赋值特有的业务字段
		fillBusinessField(request, checkVo);

		//文件列表 ：
		String ids = request.getParameter("ids");
		if(StringUtil.isEmpty(ids)){
			return BaseResponse.fail("文件审核列表为空！");
		}
		List<CounterCheckFileParamVo.FileCheckResultVo> fileResultList =Lists.newArrayList();

		ids = ids.replaceFirst("\\|", "");
		String[] resultArray = ids.split("\\|");
		Map<String, String> param = new HashMap<String, String>(1);
		for (String resultSingle : resultArray) {
			String[]  fileCheckArray=resultSingle.split("`");
			CounterCheckFileParamVo.FileCheckResultVo fileCheckVo=new CounterCheckFileParamVo.FileCheckResultVo();
			fileCheckVo.setFileId(fileCheckArray[0]);
			//1-通过  0-退回
			fileCheckVo.setQualify("1".equals(fileCheckArray[1]));
			if(fileCheckArray.length>2){
				fileCheckVo.setDesc(fileCheckArray[2]);
			}
			fileResultList.add(fileCheckVo);
		}
		checkVo.setFileResultList(fileResultList);

		///executecheck
//		CrmTradeServerPathConstant.COUNTER_CHECK;
		return getPostEntityByJsonObject(CrmTradeServerPathConstant.COUNTER_CHECK,
				checkVo,
				new ParameterizedTypeReference<BaseResponse<String>>(){});
	}

	/**
	 * @description: 资料审核中，根据具体的业务类型，赋值特有的业务字段
	 * @param request 资料审核入参
	 * @param checkVo 资料审核VO
	 * @author: jin.wang03
	 * @date: 2024/4/22 17:33
	 * @since JDK 1.8
	 */
	private void fillBusinessField(HttpServletRequest request, CounterCheckFileParamVo checkVo) {
		String orderId = request.getParameter("id");
		String checkLevel = request.getParameter("checkLevel");
		String conscustNo = request.getParameter("conscustNo");

		CmCounterOrderDto orderDto = getOrderById(orderId);
		String busiId = orderDto.getBusiId();
		CounterBusiEnum busiEnum = CounterBusiEnum.getEnum(busiId);

		// 业务类型为：“好买-售前留痕材料”、“好臻-售前留痕材料”，页面会传值
		if (CounterBusiEnum.LEGAL_DOCUMENT.equals(busiEnum) || CounterBusiEnum.HZ_LEGAL_DOCUMENT.equals(busiEnum)) {
			String legalDocSendCustomerDt = request.getParameter("legalDocSendCustomerDt");
			// 特殊处理：将秒置为00，示例：20250319 14:45:34
			if (StringUtils.isNotEmpty(legalDocSendCustomerDt)) {
				legalDocSendCustomerDt = legalDocSendCustomerDt.substring(0, 15) + "00";
			}
			checkVo.setLegalDocSendCustomerDt(legalDocSendCustomerDt);
		}

	}

	/**
     * 封装分页查询列表请求对象
     * @param param
	 * @param topCpData
     * @return
     */
	private  CmCounterOrderSearchVo getCmCounterOrderSearchVo(HttpServletRequest request,Map<String, Object> param, String topCpData){
		CmCounterOrderSearchVo searchVo = new CmCounterOrderSearchVo();
		searchVo.setSort((String)param.get("sort"));
		searchVo.setOrder((String)param.get("order"));
		searchVo.setBusiProId((String)param.get("busiProId"));
		searchVo.setBusiId((String)param.get("busiId"));
		searchVo.setBusiIdList((List<String>)param.get("busiIdList"));
		searchVo.setConscustno((String)param.get("conscustno"));
		searchVo.setForId((String)param.get("forId"));
		searchVo.setFundCode((String)param.get("fundCode"));
		searchVo.setCurStat((String)param.get("curStat"));
		searchVo.setComplexStateList( (List<String>)param.get("complexStateList"));
		searchVo.setCurStatList((List<String>)param.get("curStatList"));
		searchVo.setSignSmopStat((String)param.get("signSmopStat"));
		searchVo.setArchSmopStat((String)param.get("archSmopStat"));
		searchVo.setProductType((String)param.get("productType"));
		searchVo.setProductTypeIsNull((String)param.get("productTypeIsNull"));
		searchVo.setBegUploadDt((String)param.get("begUploadDt"));
		searchVo.setEndUploadDt((String)param.get("endUploadDt"));
		searchVo.setManageMailStat((String)param.get("manageMailStat"));
		searchVo.setDeptMailStat((String)param.get("deptMailStat"));
		searchVo.setBegCurcheckDt((String)param.get("begCurcheckDt"));
		searchVo.setEndCurcheckDt((String)param.get("endCurcheckDt"));
		searchVo.setBusiArea((String)param.get("busiArea"));
		searchVo.setCustType((String)param.get("custType"));
		searchVo.setDeptMailDateStart((String)param.get("deptMailDateStart"));
		searchVo.setDeptMailDateEnd((String)param.get("deptMailDateEnd"));


		searchVo.setBegModDt((String)param.get("begModDt"));
		searchVo.setEndModDt((String)param.get("endModDt"));

		if(StaticVar.CP_TOP_DATA_CG.equals(topCpData)){
			List<CounterBusiEnum> listBusiIdEnum = CounterUtil.REJECT_BUSI_ID;
			List<String> busiCodeList = listBusiIdEnum.stream().map(CounterBusiEnum::getKey).collect(Collectors.toList());
			searchVo.setNotBusiIdList(busiCodeList);
			searchVo.setBusiArea(DataConstants.COUNTER_BUSIAREA_DL);
		}

		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
		// 是否包含香港角色
		if (CollectionUtils.isNotEmpty(loginRoles) && loginRoles.contains("ROLE_HK_CP")) {
			searchVo.setBusiArea(DataConstants.COUNTER_BUSIAREA_HK);
		}
		return searchVo;
	}

	/**
	 * 处理接口返回对象：填充属性值
	 * @param list
	 * @return
	 */
	private List<CmCounterOrderVo> listCmCounterOrderVo(List<CmCounterOrderDto> list,List<CmCounterOrderReturnDetailDto> listReturnDetailDto){
		List<CmCounterOrderVo> listVo = new ArrayList<CmCounterOrderVo>();
        CmCounterOrderVo vo = null;
        // 对枚举字段进行转义
		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		QueryConscustInfoRequest queryConscustInfoRequest = null;
		ConscustInfoDomain conscust = null;
		if(CollectionUtils.isEmpty(list) && CollectionUtils.isEmpty(listReturnDetailDto)){
			return listVo;
		}
		Map<String, String> allUserMap=consOrgCache.getAllUserMap();
		Map<String, String> allOrgMap=consOrgCache.getAllOrgMap();
		Map<String, String> upOrgMap=consOrgCache.getUpOrgMapCache();
		Map<String, String> cons2OutletMap=consOrgCache.getCons2OutletMap();
		if(CollectionUtils.isNotEmpty(list)){
			for(CmCounterOrderDto dto : list){
	        	vo = new CmCounterOrderVo();
	        	BeanUtils.copyProperties(dto, vo);
	        	queryConscustInfoRequest = new QueryConscustInfoRequest();
	        	queryConscustInfoRequest.setConscustno(dto.getConscustno());
	            QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);
	            conscust = queryConscustInfoResponse.getConscustinfo();
	            if(conscust != null){
	            	vo.setCustName(conscust.getCustname());
		            vo.setHboneNo(conscust.getHboneno());
		            vo.setIdnoMask(conscust.getIdnoMask());
		            vo.setIdnoCipher(conscust.getIdnoCipher());
		            //所属部门
		            vo.setOrgName(allOrgMap.get(cons2OutletMap.get(conscust.getConscode())));
		            //所属投顾
		            vo.setConsName(allUserMap.get(conscust.getConscode()));
		            //所属区域
		            String regionCode = upOrgMap.get(cons2OutletMap.get(conscust.getConscode()));
					if("0".equals(regionCode)){
						vo.setRegionName(vo.getOrgName());
					}else{
						vo.setRegionName(allOrgMap.get(regionCode));
					}
	            }


	            vo.setCredateStr(vo.getCredate() == null ? "" : DateTimeUtil.fmtDate(vo.getCredate(), "yyyy-MM-dd HH:mm:ss"));

	            vo.setModDateStr(vo.getModdate() == null ? "" : DateTimeUtil.fmtDate(vo.getModdate(), "yyyy-MM-dd HH:mm:ss"));
	            //最新审核日期
	            vo.setCurCheckdateStr(vo.getCurCheckdate()== null ? "" : DateTimeUtil.fmtDate(vo.getCurCheckdate(), "yyyyMMdd"));


	        	if(StringUtils.isNotBlank(vo.getFundCode())){
					JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(vo.getFundCode(), false);
	        		vo.setFundName(jjxx1 == null ? null : jjxx1.getJjjc());
	        	}

				String uploadorName = allUserMap.get(vo.getUploador());
				vo.setUploadorName(StringUtils.isNotBlank(uploadorName) ? uploadorName : vo.getUploador());
				String curCheckerName = allUserMap.get(vo.getCurChecker());
				vo.setCurCheckerName(StringUtils.isNotBlank(curCheckerName) ? curCheckerName : vo.getCurChecker());
	        	CounterBusiEnum busiEnum=CounterBusiEnum.getEnum(vo.getBusiId());
	        	//查询预约信息
	        	if(CounterUtil.PREBOOK_BUSILIST.contains(busiEnum) && StringUtils.isNotBlank(vo.getForId())){
	        		Prebookproductinfo prebookproductinfo = prebookproductinfoService.getPrebookproductinfoById(vo.getForId());
	        		vo.setExpectTradeDt(prebookproductinfo == null ? "" : prebookproductinfo.getExpecttradedt());
	        		vo.setPreId(vo.getForId());
					// 是否后补：如果 存在 和预约单关联的[订单]，则是，否则不是
					vo.setIsLater(isLater(prebookproductinfo));
	        	}
				vo.setSpeicalBusiParam(dto.getSpecialBusiParam());
	        	listVo.add(vo);
	        }
		}else{
			for(CmCounterOrderReturnDetailDto dto : listReturnDetailDto){
	        	vo = new CmCounterOrderVo();
	        	BeanUtils.copyProperties(dto, vo);
	        	queryConscustInfoRequest = new QueryConscustInfoRequest();
	        	queryConscustInfoRequest.setConscustno(dto.getConscustno());
	            QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);
	            conscust = queryConscustInfoResponse.getConscustinfo();
	            if(conscust != null){
	            	vo.setCustName(conscust.getCustname());
		            vo.setHboneNo(conscust.getHboneno());
		            vo.setIdnoMask(conscust.getIdnoMask());
		            vo.setIdnoCipher(conscust.getIdnoCipher());
		            //所属部门
		            vo.setOrgName(allOrgMap.get(cons2OutletMap.get(conscust.getConscode())));
		            //所属投顾
		            vo.setConsName(allUserMap.get(conscust.getConscode()));
		            //所属区域
		            String regionCode = upOrgMap.get(cons2OutletMap.get(conscust.getConscode()));
					if("0".equals(regionCode)){
						vo.setRegionName(vo.getOrgName());
					}else{
						vo.setRegionName(allOrgMap.get(regionCode));
					}
	            }


	            vo.setCredateStr(vo.getCredate() == null ? "" : DateTimeUtil.fmtDate(vo.getCredate(), "yyyy-MM-dd HH:mm:ss"));

				vo.setModDateStr(vo.getModdate() == null ? "" : DateTimeUtil.fmtDate(vo.getModdate(), "yyyy-MM-dd HH:mm:ss"));

	            //最新审核日期
	            vo.setCurCheckdateStr(vo.getCurCheckdate()== null ? "" : DateTimeUtil.fmtDate(vo.getCurCheckdate(), "yyyyMMdd"));
	        	if(StringUtils.isNotBlank(vo.getFundCode())){
					JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(vo.getFundCode(), false);
	        		vo.setFundName(jjxx1 == null ? null : jjxx1.getJjjc());
	        	}

				String uploadorName = allUserMap.get(vo.getUploador());
				vo.setUploadorName(StringUtils.isNotBlank(uploadorName) ? uploadorName : vo.getUploador());
				String curCheckerName = allUserMap.get(vo.getCurChecker());
				vo.setCurCheckerName(StringUtils.isNotBlank(curCheckerName) ? curCheckerName : vo.getCurChecker());

	        	CounterBusiEnum busiEnum=CounterBusiEnum.getEnum(vo.getBusiId());
	        	//查询预约信息
	        	if(CounterUtil.PREBOOK_BUSILIST.contains(busiEnum) && StringUtils.isNotBlank(vo.getForId())){
	        		Prebookproductinfo prebookproductinfo = prebookproductinfoService.getPrebookproductinfoById(vo.getForId());
	        		vo.setExpectTradeDt(prebookproductinfo == null ? "" : prebookproductinfo.getExpecttradedt());
	        		vo.setPreId(vo.getForId());
					// 是否后补：如果 存在 和预约单关联的[订单]，则是，否则不是
					vo.setIsLater(isLater(prebookproductinfo));
	        	}
	        	//流水审核人名称
	        	vo.setOrderFlowCurCheckerName(allUserMap.get(vo.getOrderFlowCurChecker()));
	        	//流水审核时间
	        	vo.setOrderFlowCurCheckdateStr(vo.getOrderFlowCurCheckdate() == null ? "" : DateTimeUtil.fmtDate(vo.getOrderFlowCurCheckdate(), "yyyyMMdd HH:mm:ss"));
	        	listVo.add(vo);
	        }
		}


        return listVo;
	}

	/**
	 * @description: 是否后补：如果 存在 和预约单关联的[订单]，则是，否则不是
	 * @param prebookproductinfo 预约信息
	 * @return java.lang.String 是/否
	 * @author: jin.wang03
	 * @date: 2024/4/18 15:03
	 * @since JDK 1.8
	 */
	private String isLater(Prebookproductinfo prebookproductinfo) {
		if(prebookproductinfo ==null){
			return "";
		}
		//外部订单 存在。 是 后补
		PreRelatedOrderDto outerOrderInfo=prebookBusinessService.getOuterOrderInfo(prebookproductinfo.getId());
		return Objects.nonNull(outerOrderInfo)  ? "是" : "否";
	}

	/**
	 * 加载列表页面数据
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listCounterOrderByPage.do")
	public Map<String, Object> listCounterOrderByPage(HttpServletRequest request)	throws Exception {
		long starttime = System.currentTimeMillis();
		String topCpData = (String) request.getSession().getAttribute("topcpdata");
		String pageStr = request.getParameter("page");
    	String rowsStr = request.getParameter("rows");
    	int page = StringUtils.isBlank(pageStr) ? 1 : Integer.parseInt(pageStr);
    	int rows = StringUtils.isBlank(rowsStr) ? 10 : Integer.parseInt(rowsStr);

		Map<String, Object> param = getRequestParam(request);
		CmCounterOrderSearchVo cmCounterOrderSearchVo = getCmCounterOrderSearchVo(request, param, topCpData);
		log.info("/counterorder/querycounterorderdtolist请求:{}",JSON.toJSONString(cmCounterOrderSearchVo));

		BaseResponse<List<CmCounterOrderDto>> httpRsp = getPostEntityByJsonObject(CrmTradeServerPathConstant.QUERY_COUNTER_ORDR_DTO_LIST,  cmCounterOrderSearchVo, new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderDto>>>() {});
		log.info("/counterorder/querycounterorderdtolist返回:{}", (httpRsp!=null && CollectionUtils.isNotEmpty(httpRsp.getData()) ) ? httpRsp.getData().size():0);
		List<CmCounterOrderDto> list = new ArrayList<CmCounterOrderDto>();
		if (httpRsp!=null && httpRsp.isSuccess()) {
			List<CmCounterOrderDto> listRsp = httpRsp.getData();
			if(CollectionUtils.isNotEmpty(listRsp)){
				List<String> conscustnoList = null;
				List<Map<String,String>> preIdAndConscustno = null;
				for(CmCounterOrderDto dto : listRsp){
					//产品广度为常规产品的，非常规产品数据不让看
				    if(StaticVar.CP_TOP_DATA_CG.equals(topCpData) && StringUtil.isNotNullStr(dto.getFundCode())){
				    	Integer count = prebookproductinfoService.checkNormalProduct(dto.getFundCode());
				    	if(count == 0){
				    		continue;
						}
					}

					param.put("conscustno", dto.getConscustno());
					CounterBusiEnum busiEnum=CounterBusiEnum.getEnum(dto.getBusiId());
					//验证预约是否存在
					if(CounterUtil.PREBOOK_BUSILIST.contains(busiEnum)){
						param.put("forId", dto.getForId());
						preIdAndConscustno = prebookproductinfoService.queryPreIdAndConscustnoForCounter(param);
						if(CollectionUtils.isNotEmpty(preIdAndConscustno) && preIdAndConscustno.size() == 1){
							list.add(dto);
						}
						continue;
					}
					//验证客户号是否存在
					conscustnoList = conscustService.queryConscustnoForCounter(param);
					if(CollectionUtils.isNotEmpty(conscustnoList) && conscustnoList.size() == 1){
						list.add(dto);
					}

				}
			}
		}

        //循环遍历赋值属性值
		ListPageUtil<CmCounterOrderDto> orderPageUtil= new ListPageUtil<>(list, rows);
		List<CmCounterOrderDto>  dislpayList=orderPageUtil.getPagedList(page);
		log.info("返回到页面list size :{}",dislpayList.size());
        // 返回查询结果
     	Map<String, Object> resultMap = new HashMap<String, Object>(8);
		resultMap.put("total", list.size());
		log.info("资料管理页面耗时:{}",System.currentTimeMillis() - starttime);

		List<CmCounterOrderVo> pageList=listCmCounterOrderVo(dislpayList,null);
		//补充其他业务属性
		// busiId=='22' 香港开户，  是否存在香港客户信息
		pageList.forEach(pageDto -> {
			if (CounterBusiEnum.OPEN_ACCT_HK.getKey().equals(pageDto.getBusiId())) {
				String consCustNo = pageDto.getConscustno();
				CoreReturnMessageDto<HkCustInfoDto> hkAcctInfoByCustNo = hkAcctInfoService.getHkAcctInfoByCustNo(consCustNo);
				log.info("查询香港客户信息，conscustNo为：{}，返回结果:{}", consCustNo, JSON.toJSONString(hkAcctInfoByCustNo));
				if (hkAcctInfoByCustNo != null && com.howbuy.crm.base.model.BaseConstantEnum.SUCCESS.getCode()
						.equals(hkAcctInfoByCustNo.getReturnCode())) {
					HkCustInfoDto hkCustInfoDto = hkAcctInfoByCustNo.getReturnObject();
					if (hkCustInfoDto != null && StringUtils.isNotEmpty(hkCustInfoDto.getHkTxAcctNo())) {
						pageDto.setHkTxAcctNo(hkCustInfoDto.getHkTxAcctNo());
						// 香港客户号是否开户：香港客户号不为空 且 (客户状态为“正常” 或 客户状态为“休眠”)
						pageDto.setHkTxAcctNoIsOpen((HkAcctCustStatusEnum.NORMAL.getCode().equals(hkCustInfoDto.getHkCustStatus()) ||
								HkAcctCustStatusEnum.DORMANT.getCode().equals(hkCustInfoDto.getHkCustStatus())));
					}
				}
			}
		});
		resultMap.put("rows", pageList);

		return resultMap;
	}


	/**
	 * 签字文件查询
	 * @param request
	 * @return
	 * @throws Exception
	 */
    @ResponseBody
    @RequestMapping("/qryCmCounterOrderSignfile")
    public Map<String, Object> qryCmCounterOrderSignfile(HttpServletRequest request) throws Exception {
    	// 设置查询分页参数
    	Map<String, Object> resultMap = new HashMap<String, Object>(2);

    	// 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("orderId", request.getParameter("id"));
    	List<CmCounterOrderSignfile> list = null;
		BaseResponse<List<CmCounterOrderSignfile>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_CM_COUNTER_ORDER_SIGN_FILE_LIST, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderSignfile>>>(){});
		if (httpRsp.isSuccess()) {
			list = httpRsp.getData();
			StringBuilder signfileStr = new StringBuilder();
			if(CollectionUtils.isNotEmpty(list)){
	        	for(CmCounterOrderSignfile cmCounterOrderSignfile : list){
	        		signfileStr.append(cmCounterOrderSignfile.getFileName() + "." + cmCounterOrderSignfile.getFileSuffix() + ";");
	        	}
	        }
	        if(signfileStr != null && StringUtil.isNotNullStr(signfileStr.toString())){
	        	resultMap.put("signfileStr", signfileStr.toString());
	        }else{
	        	resultMap.put("signfileStr", "");
	        }
	        resultMap.put("rows", list);
		}

        return resultMap;
    }

    @ResponseBody
    @RequestMapping(value = "/uploadSignfile", method = RequestMethod.POST)
    public Map<String, Object> uploadSignfile(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>(8);
        //默认上传成功
        resultMap.put("uploadFlag", "success");
        resultMap.put("errorMsg", "上传成功");

        String id = request.getParameter("id");
        //校验是否已经存在此文件名称
        if(StringUtils.isNotBlank(id)){
        	User user = getLoginUser();
			//删除更新过程中去除的countOrderSignfile
			String delfileids = request.getParameter("delfileids").replaceFirst("#", "");
			List<String> deleteList=Arrays.asList(delfileids.split("#"));

            // 设置参数
			SaveCmCounterOrderSignfileRequest orderRequest=new SaveCmCounterOrderSignfileRequest();
			orderRequest.setOrderId(id);
			orderRequest.setOperatorNo(user.getUserId());
			orderRequest.setDelFileIdList(deleteList);

			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
			List<MultipartFile> fileList = multipartRequest.getFiles("filelist[]");
			orderRequest.setFilePathList(processCounterTempFile(fileList));

            BaseResponse<String> httpRsp = getPostEntityByJsonObject(
					CrmTradeServerPathConstant.UPLOAD_COUNTER_ORDER_SIGN_FILE,
					orderRequest,
					new ParameterizedTypeReference<BaseResponse<String>>(){});
            resultMap.put("uploadFlag", httpRsp.isSuccess());
            resultMap.put("errorMsg", httpRsp.getReturnMsg());
        }else{
        	resultMap.put("uploadFlag", "allerror");
            resultMap.put("errorMsg", "参数错误，提交失败");
        }
        return resultMap;
    }

	@RequestMapping(value = "/previewSignImg")
	public String previewCounterImg(HttpServletRequest request) {
		String docId = request.getParameter("docId");
		if (StringUtils.isNotBlank(docId)) {
			request.setAttribute("docid", docId);
			try {
				//插入日志
//                this.creatLog(user.getUserId(), docfile.getFid(), "'" + docfile.getName() + "." + docfile.getSufname() + "'", StaticVar.OPT_TYPE_FILE_VIEW);

			} catch (Exception e) {
				log.error("previewSignImg error", e);
			}
		}

		return "/counter/previewSignImg";
	}

	@ResponseBody
	@RequestMapping("/getSignFileStream")
	public void getModelFileStream(HttpServletRequest request, HttpServletResponse response) {
		String docId = request.getParameter("docId");
		String userId=getLoginUserId();
		Map<String, String> param = new HashMap<String, String>();
		param.put("id", docId);
		CmCounterSignfileStream fileStreamInfo = null;
		BaseResponse<CmCounterSignfileStream> httpRsp =
				getPostEntityByMap(CrmTradeServerPathConstant.GET_COUNTER_SIGN_FILE_STREAM,
						param,
						new ParameterizedTypeReference<BaseResponse<CmCounterSignfileStream>>() {
						});
		if (httpRsp.isSuccess()) {
			fileStreamInfo = httpRsp.getData();
		}else{
			fileStreamInfo=new CmCounterSignfileStream();
		}

		byte[] bytes= fileStreamInfo.getFilebyte();
		ServletOutputStream outputStream = null;
		try {
			response.setContentType("application/octet-stream");
			//输出文件流
			outputStream = response.getOutputStream();
			outputStream.write(bytes);
		}catch (Exception e){
			log.error("文档预览失败，fileName：{}, useId：{}", fileStreamInfo.getFileName(), userId, e);
		}finally {
			if (null != outputStream) {
				try {
					outputStream.close();
				} catch (IOException e) {
					log.error("CrmCounterFileOuterService>>>downloadPdf 关闭文件流异常", e);
				}
			}
		}

	}


	/**
	 * request参数处理
	 * @param request
	 * @return
	 * @throws Exception
	 */
	public Map<String, Object> getRequestParam(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String, Object> param = new HashMap<String, Object>(8);
		String page = request.getParameter("page");
    	String rows = request.getParameter("rows");
    	param.put("page", StringUtils.isBlank(page) ? 1 : Integer.parseInt(page) );
    	param.put("rows", StringUtils.isBlank(rows) ? 10 : Integer.parseInt(rows) );
    	param.put("order", request.getParameter("order") );
    	String sort = request.getParameter("sort");
    	//转换排序列名
    	translateSort(param,sort,"moddate","MODDATE");
    	translateSort(param,sort,"curStatName","CUR_STAT");
    	translateSort(param,sort,"archSmopStatName","ARCH_SMOP_STAT");
    	translateSort(param,sort,"archSmopDate","ARCH_SMOP_DATE");
    	translateSort(param,sort,"docSmopNo","DOC_SMOP_NO");
    	translateSort(param,sort,"manageMailStat","MANAGE_MAIL_STAT");
    	translateSort(param,sort,"manageMailDate","MANAGE_MAIL_DATE");
    	translateSort(param,sort,"managemMailNo","MANAGE_MAIL_NO");
    	translateSort(param,sort,"deptMailStat","DEPT_MAIL_STAT");
    	translateSort(param,sort,"deptMailDate","DEPT_MAIL_DATE");
    	translateSort(param,sort,"deptMailNo","DEPT_MAIL_NO");

    	String busiProId = request.getParameter("busiProId");
    	String[] busiIds = request.getParameterValues("busiIds[]");
    	String invstType = request.getParameter("invstType");
    	String custName = request.getParameter("custName");
    	String orgCode = request.getParameter("orgCode");
		String consCode = request.getParameter("consCode");
		String conscustno = request.getParameter("conscustno");
		String idNo = request.getParameter("idNo");
		String forId = request.getParameter("forId");
		String fundCode = request.getParameter("fundCode");
		String expectTradeDtStart = request.getParameter("expectTradeDtStart");
		String expectTradeDtEnd = request.getParameter("expectTradeDtEnd");
		String[] curStats = request.getParameterValues("curStats[]");
		String signSmopStat = request.getParameter("signSmopStat");
		String archSmopStat = request.getParameter("archSmopStat");
		String productType = request.getParameter("productType");
		String begUploadDt = request.getParameter("begUploadDt");
		String endUploadDt = request.getParameter("endUploadDt");
		String mgrCode = request.getParameter("mgrCode");
		String manageMailStat = request.getParameter("manageMailStat");
		String deptMailStat = request.getParameter("deptMailStat");
		String hboneNo = request.getParameter("hboneNo");
		String begCurcheckDt = request.getParameter("begCurcheckDt");
		String endCurcheckDt = request.getParameter("endCurcheckDt");
		String busiArea = request.getParameter("busiArea");
		String deptMailDateStart = request.getParameter("deptMailDateStart");
		String deptMailDateEnd = request.getParameter("deptMailDateEnd");

		//资料管理 审核状态  复合查询
		String[] complexStateArray = request.getParameterValues("complexState[]");

    	// 业务属性
		param.put("busiProId", StringUtil.getStr(busiProId));
    	if (null != busiIds && busiIds.length > 0 && !"".equals(busiIds[0])) {
			param.put("busiIdList", Lists.newArrayList(busiIds));
		} else {
			param.put("busiIdList", null);
		}
    	// 设置查询参数（ 客户类型）
		param.put("custType", StringUtil.getStr(invstType));
		// 设置查询参数（客户名）
		param.put("custName", StringUtil.getStr(custName));
		// 设置查询条件（所属投顾-部门）
		if (StringUtils.isNotBlank(consCode)) {
			param.put("conscode", consCode);
		} else {
			if (!StringUtil.isEmpty(orgCode)) {
				// 选择了未分配组
				if (orgCode.startsWith("other")) {
					param.put("othertearm", orgCode.replaceFirst("other", ""));
				} else {
					String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
					// 选择了团队
					if (StaticVar.ORGTYPE_TEAM.equals(orgType)) {
						param.put("teamcode", orgCode);
					} else {
						if (!StaticVar.ORGTYPE_OUTLET.equals(orgCode)) {
							List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
							param.put("outletcodes", Util.getSqlInStr(suborgs));
						}
					}
				}
			}
		}
		// 设置查询参数（ 投顾客户号）
		param.put("conscustno", StringUtil.getStr(conscustno));
		// 设置查询参数（ 证件号）
		if (StringUtils.isNotBlank(idNo)) {
			param.put("idNo", DigestUtil.digest(idNo.trim()));
		} else {
			param.put("idNo", null);
		}
		// 设置查询参数（ 预约单号）
		param.put("forId", StringUtil.getStr(forId));
		// 设置查询参数（ 基金代码）
		param.put("fundCode", StringUtil.getStr(fundCode));
		//预计交易日期
		param.put("expectTradeDtStart", StringUtil.getStr(expectTradeDtStart));
		param.put("expectTradeDtEnd", StringUtil.getStr(expectTradeDtEnd));

		//Crocodile's TODO : 状态查询条件 复核查询

		if (null != curStats && curStats.length > 0) {
			boolean flag = false;
			StringBuilder sb = new StringBuilder();
			for (String str : curStats) {
				if("0".equals(str)){
					param.put("curStat", null);
					flag = true;
					break;
				}
				if("-1".equals(str)){
					sb.append("2");
					sb.append(",");
					sb.append("6");
					sb.append(",");
					sb.append("8");
					sb.append(",");
				}else{
					sb.append(str);
					sb.append(",");
				}
			}
			String curStatsStr = flag ? null : sb.substring(0, sb.length()-1).toString();
			param.put("curStatList", StringUtils.isBlank(curStatsStr) ? null : Lists.newArrayList(curStatsStr.split(",")));
		} else {
			param.put("curStatList", null);
		}

		//审核状态：复合查询处理
		List<String> complexStateList =null;
		if(complexStateArray!=null &&  complexStateArray.length>0){
			complexStateList =Lists.newArrayList(complexStateArray);
		}
		//包含 0-全部 。 则忽略该条件
		if(CollectionUtils.isNotEmpty(complexStateList) &&  !complexStateList.contains("0")){
			param.put("complexStateList", complexStateList);
		}

		// 设置查询参数（ 签收状态(SMOP)）
		param.put("signSmopStat", StringUtil.getStr(signSmopStat));
		// 设置查询参数（归档状态(SMOP)）
		param.put("archSmopStat", StringUtil.getStr(archSmopStat));
		if (StringUtils.isNotBlank(productType)) {
			//全部
			if("2".equals(productType)){
				param.put("productTypeIsNull", "1");
				param.put("productType", null);
			//注意：2 代表查询空值
			}else{
				param.put("productType", productType);
			}
		} else {
			param.put("productType", null);
		}
		param.put("begUploadDt", StringUtil.getStr(begUploadDt));
		param.put("endUploadDt", StringUtil.getStr(endUploadDt));

		param.put("begModDt", request.getParameter("begModDt"));
		param.put("endModDt", request.getParameter("endModDt"));



		// 设置查询参数（ 基金管理人）
		param.put("mgrCode", StringUtil.getStr(mgrCode));
		param.put("manageMailStat", StringUtil.getStr(manageMailStat));
		param.put("deptMailStat", StringUtil.getStr(deptMailStat));
		param.put("hboneNo", StringUtil.getStr(hboneNo));
		param.put("begCurcheckDt", StringUtil.getStr(begCurcheckDt));
		param.put("endCurcheckDt", StringUtil.getStr(endCurcheckDt));
		if(StringUtils.isNotBlank(begCurcheckDt) || StringUtils.isNotBlank(endCurcheckDt)){
			param.put("curStat", "9");
		}else{
			param.put("curStat", null);
		}
		// 设置查询参数（ 业务地区）
		param.put("busiArea", StringUtil.getStr(busiArea));
		//邮寄日期（分部）
		param.put("deptMailDateStart", StringUtil.getStr(deptMailDateStart));
		param.put("deptMailDateEnd", StringUtil.getStr(deptMailDateEnd));

		return param;
	}

	/**
	 * 转换排序列名
	 * @param param
	 * @param sort
	 */
	private void translateSort(Map<String, Object> param, String sort,String psort,String dbSort) {
		if(psort.equals(sort)){
			param.put("sort",dbSort);
		}
	}

	/**
     * 查看证件号明文
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/showIdnoByCipher.do")
    public String showIdnoByCipher(HttpServletRequest request) throws Exception {
        User user = getLoginUser();
        String idnoCipher = request.getParameter("idnoCipher");
        String conscustno = request.getParameter("conscustno");
        String id = request.getParameter("id");

        String idno = decryptSingleFacade.decrypt(idnoCipher).getCodecText();

        String ip = getIpAddr(request);
        //记录访问日志
        PageVisitLog pageVisitLog = new PageVisitLog();
        pageVisitLog.setConscustno(conscustno);
        pageVisitLog.setUserId(user.getUserId());
        pageVisitLog.setVisitUrl(request.getRequestURI());
        pageVisitLog.setOperation("查看证件号明文"+id);
        pageVisitLog.setVisitTime(new Date());
        pageVisitLog.setIp(ip);
        pageVisitLogService.recordLog(pageVisitLog);

        return idno;

    }

	/**
	 * 订单作废
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/orderInvalid.do")
	public String orderInvalid(HttpServletRequest request) {
		User user = getLoginUser();
		String result = null;
		String id = request.getParameter("id");
		String busiId = request.getParameter("busiId");
		if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(busiId)) {
			Map<String,String> postParam = new HashMap<String,String>();
	        postParam.put("id", id);
	        postParam.put("operatorNo", user.getUserId());
			BaseResponse<String> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.INVALID_COUNTER_ORDER, postParam,new ParameterizedTypeReference<BaseResponse<String>>(){});
			//关联账户对应的busiId
			result = httpRsp.isSuccess() ? "success" : httpRsp.getReturnMsg();
		} else {
			result = "paramError";
		}
		return result;
	}

	/**
	 * 订单撤回
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/orderWithdraw.do")
	public Map<String, Object> orderWithdraw(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<>();

		User user = getLoginUser();
		Map<String,String> postParam = new HashMap<String,String>();
        postParam.put("orderId", request.getParameter("id"));
    	postParam.put("operatorNo", user.getUserId());
		BaseResponse<String> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.WITHDRAW_COUNTER_ORDER, postParam,new ParameterizedTypeReference<BaseResponse<String>>(){});
		resultMap.put("errorCode", httpRsp.isSuccess());
		resultMap.put("errorMsg", httpRsp.getReturnMsg());

		return resultMap;
	}

	private List<CmCounterOrderfileFileMsg> handleFileBytesByDtoList (List<CmCounterOrderfileFileDto> fileDtoList){
		log.info("handleFileBytesByDtoList 方法fileList：{}", JSON.toJSON(fileDtoList));
		if(CollectionUtils.isEmpty(fileDtoList) ){
			return Lists.newArrayList();
		}
		List<CmCounterOrderfileFileMsg> returnList = new ArrayList<CmCounterOrderfileFileMsg>();

		if(CollectionUtils.isNotEmpty(fileDtoList)){
			for(CmCounterOrderfileFileDto cmCounterOrderfileFile : fileDtoList){
				if(cmCounterOrderfileFile == null){
					continue;
				}
				CmCounterOrderfileFileMsg cmCounterOrderfileFileMsg = new CmCounterOrderfileFileMsg();
				BeanUtils.copyProperties(cmCounterOrderfileFile, cmCounterOrderfileFileMsg);
				returnList.add(cmCounterOrderfileFileMsg);

			}
		}
		handleFileBytes(returnList);
		return returnList;
	}

	private List<CmCounterOrderfileFileMsg> handleFileBytesByFileList (List<CmCounterOrderfileFile> fileList){
		log.info("handleFileBytesByFileList 方法fileList：{}", JSON.toJSON(fileList));
		if(CollectionUtils.isEmpty(fileList)){
			return Lists.newArrayList();
		}
		List<CmCounterOrderfileFileMsg> returnList = new ArrayList<CmCounterOrderfileFileMsg>();
		for(CmCounterOrderfileFile cmCounterOrderfileFile : fileList){
			if(cmCounterOrderfileFile == null){
				continue;
			}
			CmCounterOrderfileFileMsg cmCounterOrderfileFileMsg = new CmCounterOrderfileFileMsg();
			BeanUtils.copyProperties(cmCounterOrderfileFile, cmCounterOrderfileFileMsg);
			returnList.add(cmCounterOrderfileFileMsg);
		}
		handleFileBytes(returnList);
		return returnList;
	}

	/**
	 * 赋值：账户中心文件流
	 * @param returnList
	 * return
	 */
	private void handleFileBytes (List<CmCounterOrderfileFileMsg> returnList){
        //循环：查询需要查询账户中心的外部订单
		Set<String> forIdSet = new HashSet<String>(16);
		for(CmCounterOrderfileFileMsg cmCounterOrderfileFileMsg : returnList){
			//审核通过、ftid等于1、文件已删除：取账户中心
			if(StaticVar.CURSTAT_ORDER_PASS.equals(cmCounterOrderfileFileMsg.getCurStat()) &&
            		StaticVar.COUNTER_FILE_TYPE_ID_STRING.equals(cmCounterOrderfileFileMsg.getFtid()) &&
            		StaticVar.IS_MOVE.equals(cmCounterOrderfileFileMsg.getIsMove()) &&
            		StringUtils.isNotBlank(cmCounterOrderfileFileMsg.getForId())){
				forIdSet.add(cmCounterOrderfileFileMsg.getForId());
			}
		}

		//查询账户中心文件流接口
		List<TpDealImageDataFileBean> listBeans = new ArrayList<TpDealImageDataFileBean>();
		QueryDealImageDataInfoRequest request = null;
		QueryDealImageDataInfoResponse response = null;
		for(String forId : forIdSet){
			request = new QueryDealImageDataInfoRequest();
			request.setContractNo(forId);
			List<String> fileTypes = new ArrayList<String>(8);
			fileTypes.add(StaticVar.COUNTER_FILE_TYPE_ID_STRING);
			request.setFileTypes(fileTypes);
			log.info("queryDealImageDataInfoFacade.execute(request)请求：{}", JSON.toJSON(request));
			response = queryDealImageDataInfoFacade.execute(request);
			log.info("queryDealImageDataInfoFacade.execute(request)返回：{}", JSON.toJSON(response));
			if(response != null && CollectionUtils.isNotEmpty(response.getTpDealImageDataFileList())){
				listBeans.addAll(response.getTpDealImageDataFileList());
			}
		}

		//赋值文件流
        for(TpDealImageDataFileBean tpDealImageDataFileBean : listBeans){
        	for(CmCounterOrderfileFileMsg cmCounterOrderfileFile : returnList){
    			if(StaticVar.CURSTAT_ORDER_PASS.equals(cmCounterOrderfileFile.getCurStat()) &&
    					StaticVar.IS_MOVE.equals(cmCounterOrderfileFile.getIsMove()) &&
    					StaticVar.COUNTER_FILE_TYPE_ID_STRING.equals(cmCounterOrderfileFile.getFtid()) &&
    					cmCounterOrderfileFile.getFtid().equals(tpDealImageDataFileBean.getFileType()) &&
    					cmCounterOrderfileFile.getForId().equals(tpDealImageDataFileBean.getContractNo())
                		)
    			{
    				String fileName = cmCounterOrderfileFile.getFileName() + "." + cmCounterOrderfileFile.getFileSuffix();
    				//账户中心文件格式：hhmmss_原文件名  20240318日确认账户中心文件格式有修改，改成取第一个下划线后的
    				String zhzxFileName = StringUtils.isBlank(tpDealImageDataFileBean.getFileName()) ?
    						null : tpDealImageDataFileBean.getFileName().substring(tpDealImageDataFileBean.getFileName().indexOf("_") + 1);
    				if(StringUtils.isNotBlank(zhzxFileName) && fileName.equals(zhzxFileName)){
    					cmCounterOrderfileFile.setFileBytes(tpDealImageDataFileBean.getFileBytes());
    				}
    			}
    		}
		}
	}

	/**
	 * 导出列表数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/orderExport.do")
	public Map<String, Object> orderExport(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		// 获取选中的订单编号
		String orderIds = ObjectUtils.replaceNull(request.getParameter("orderIds"));
		orderIds = orderIds.substring(0, orderIds.length() -1);

		// 设置默认参数
		CmCounterOrderSearchVo vo = new CmCounterOrderSearchVo();
		vo.setOrderIdList(Arrays.asList(orderIds.split(",")));
    	List<CmCounterOrderDto> list = null;
		BaseResponse<List<CmCounterOrderDto>> httpRsp = getPostEntityByJsonObject(CrmTradeServerPathConstant.QUERY_COUNTER_ORDR_DTO_LIST,  vo, new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderDto>>>() {});
		if (httpRsp.isSuccess()) {
			list = httpRsp.getData();
		}

		//处理接口返回：赋值字段
		List<CmCounterOrderVo> exportList = listCmCounterOrderVo(list,null);
		// 给特殊的业务字段赋值
		fillBusinessFields(exportList);

		// 判断导出List是否为空
		if (CollectionUtils.isNotEmpty(exportList)) {
			List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
			User user = getLoginUser();
			String ip = getIpAddr(request);
			for(CmCounterOrderVo cmCounterOrderVo : exportList){
				//导出日志
	            PageVisitLog pageVisitLog = new PageVisitLog();
	            pageVisitLog.setConscustno(cmCounterOrderVo.getConscustno());
	            pageVisitLog.setUserId(user.getUserId());
	            pageVisitLog.setVisitUrl(request.getRequestURI());
	            pageVisitLog.setOperation("资料管理导出");
	            pageVisitLog.setVisitTime(new Date());
	            pageVisitLog.setPreid(new BigDecimal(cmCounterOrderVo.getId()));
	            pageVisitLog.setIp(ip);
	            listlog.add(pageVisitLog);
			}

			try {
				// 清空输出流
				response.reset();
				// 设置文件格式和名字
				response.setContentType("multipart/form-data");
				response.setHeader("Content-Disposition", "attachment;fileName=" + new String("资料管理.xls".getBytes("gb2312"), "ISO8859-1"));
				ServletOutputStream os = response.getOutputStream();
				ExcelWriter.writeExcel(os, "资料管理", 0, exportList, new String[][]{
						{"首次上传日期", "credateStr"},
						{"预计交易日期", "expectTradeDt"},
						{"产品代码", "fundCode"},
						{"产品名称", "fundName"},
						{"业务类型", "busiIdName"},
						{"客户姓名", "custName"},
						{"投顾客户号", "conscustno"},
						{"一账通号", "hboneNo"},
						{"证件号", "idnoMask"},
						{"所属区域", "regionName"},
						{"所属部门", "orgName"},
						{"所属投顾", "consName"},
						{"业务属性", "busiProIdName"},
						{"客户类型", "custTypeName"},
						{"预约单号", "preId"},
						{"预约类型", "preTypeName"},
						{"是否进入OP", "productTypeName"},
						{"上传人", "uploadorName"},
						{"最新上传日期", "modDateStr"},
						{"最新审核日期", "curCheckdateStr"},
						{"最新审核人", "curCheckerName"},
						{"最新审核状态", "curStatName"},
						{"是否后补", "isLater"},
						{"签收状态（smop）", "signSmopStatName"},
						{"归档状态（smop）", "archSmopStatName"},
						{"归档日期（smop）", "archSmopDate"},
						{"归档编号（smop）", "docSmopNo"},
						{"邮寄状态（管理人）", "manageMailStatName"},
						{"邮寄日期（管理人）", "manageMailDate"},
						{"邮寄单号（管理人）", "manageMailNo"},
						{"邮寄状态（分部）", "deptMailStatName"},
						{"邮寄日期（分部）", "deptMailDate"},
						{"邮寄单号（分部）", "deptMailNo"},
						{"审核意见", "curCheckdes"},
						{"退回原因", "fileFlowReturnDesSummary"},
						{"发送客户时间", "legalDocSendCustomerDt"}
				});
				// 关闭流
				os.close();
				for(PageVisitLog log : listlog){
	            	pageVisitLogService.recordLog(log);
	            }
			} catch (Exception e) {
				log.error("文件导出异常", e);
			}
			resultMap.put("msg", "success");
		} else {
			resultMap.put("msg", "noData");
		}
		return resultMap;
	}

	/**
	 * @description:(请在此添加描述)
	 * @param exportList
	 * @return void
	 * @author: jin.wang03
	 * @date: 2025/2/26 16:08
	 * @since JDK 1.8
	 */
	private void fillBusinessFields(List<CmCounterOrderVo> exportList) {
		if (CollectionUtils.isEmpty(exportList)) {
			return;
		}
		// 赋值：退回原因汇总
		fillReturnDesc(exportList);
		// 赋值：发送客户时间(仅 好臻-售前留痕材料、好买-售前留痕材料 有值)
		fillLegalDocSendCustomerDt(exportList);
	}

	/**
	 * @description: 赋值：发送客户时间
	 * @param exportList
	 * @return void
	 * @author: jin.wang03
	 * @date: 2025/2/26 16:09
	 * @since JDK 1.8
	 */
	private static void fillLegalDocSendCustomerDt(List<CmCounterOrderVo> exportList) {
		for (CmCounterOrderVo cmCounterOrderVo : exportList) {
			if (CounterBusiEnum.HZ_LEGAL_DOCUMENT.getKey().equals(cmCounterOrderVo.getBusiId())
					|| CounterBusiEnum.LEGAL_DOCUMENT.getKey().equals(cmCounterOrderVo.getBusiId())) {
				if (StringUtils.isNotBlank(cmCounterOrderVo.getSpeicalBusiParam())) {
					try {
						JSONObject jsonObject = JSON.parseObject(cmCounterOrderVo.getSpeicalBusiParam());
						cmCounterOrderVo.setLegalDocSendCustomerDt(jsonObject.getString("legalDocSendCustomerDt"));
					} catch (Exception e) {
						log.error("赋值：发送客户时间出错！orderId: {}", cmCounterOrderVo.getId(), e);
					}
				}
			}
		}
	}

	/**
	 * @description: 填充退回描述信息
	 * 	 * 对于状态为分部退回、OP初审退回、OP复审退回、作废的订单,需要填充退回原因
	 * @param exportList
	 * @return void
	 * @author: jin.wang03
	 * @date: 2025/2/26 16:10
	 * @since JDK 1.8
	 */
	private void fillReturnDesc(List<CmCounterOrderVo> exportList) {
		if (CollectionUtils.isEmpty(exportList)) {
			return;
		}

		// 获取需要填充退回描述的订单ID列表
		List<String> failOrderIds = getFailOrderIds(exportList);
		if (CollectionUtils.isEmpty(failOrderIds)) {
			return;
		}

		// 查询退回详情
		List<CmCounterOrderReturnDetailDto> returnDetails = queryReturnDetails(failOrderIds);
		if (CollectionUtils.isEmpty(returnDetails)) {
			return;
		}

		// 构建退回描述Map
		Map<String, String> returnDescMap = buildReturnDescMap(returnDetails);

		// 填充退回描述
		fillReturnDescToOrders(exportList, failOrderIds, returnDescMap);
	}

	/**
	 * @description: 获取需要填充退回描述的订单ID列表
	 * @param exportList
	 * @return java.util.List<java.lang.String>
	 * @author: jin.wang03
	 * @date: 2025/2/26 17:07
	 * @since JDK 1.8
	 */
	private List<String> getFailOrderIds(List<CmCounterOrderVo> exportList) {
		return exportList.stream()
				.filter(this::isFailOrder)
				.map(CmCounterOrderVo::getId)
				.collect(Collectors.toList());
	}


	/**
	 * @description: 判断是否为退回/作废订单
	 * @param order
	 * @return boolean
	 * @author: jin.wang03
	 * @date: 2025/2/26 17:07
	 * @since JDK 1.8
	 */
	private boolean isFailOrder(CmCounterOrderVo order) {
		String curStat = order.getCurStat();
		return CounterStateEnum.FAIL_FBSH.getKey().equals(curStat)
				|| CounterStateEnum.FAIL_OPCS.getKey().equals(curStat)
				|| CounterStateEnum.FAIL_OPFS.getKey().equals(curStat)
				|| CounterStateEnum.CANCEL.getKey().equals(curStat);
	}

	/**
	 * @description: 查询退回详情
	 * @param failOrderIds
	 * @return java.util.List<com.howbuy.crm.trade.model.counter.dto.CmCounterOrderReturnDetailDto>
	 * @author: jin.wang03
	 * @date: 2025/2/26 17:07
	 * @since JDK 1.8
	 */
	private List<CmCounterOrderReturnDetailDto> queryReturnDetails(List<String> failOrderIds) {
		Map<String, String> postParam = new HashMap<>(1);
		postParam.put("orderIdList", StringUtils.join(failOrderIds, ","));

		BaseResponse<List<CmCounterOrderReturnDetailDto>> response = getPostEntityByMap(
				CrmTradeServerPathConstant.QUERY_COUNTER_ORDER_LATEST_RETURN_DETAIL,
				postParam,
				new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderReturnDetailDto>>>() {}
		);

		return Optional.ofNullable(response)
				.filter(BaseResponse::isSuccess)
				.map(BaseResponse::getData)
				.orElse(Collections.emptyList());
	}


	/**
	 * @description: 构建退回描述Map
	 * @param returnDetails
	 * @return java.util.Map<java.lang.String,java.lang.String>
	 * @author: jin.wang03
	 * @date: 2025/2/26 17:08
	 * @since JDK 1.8
	 */
	private Map<String, String> buildReturnDescMap(List<CmCounterOrderReturnDetailDto> returnDetails) {
		return returnDetails.stream()
				.collect(Collectors.groupingBy(
						CmCounterOrderReturnDetailDto::getId,
						Collectors.mapping(
								detail -> detail.getFileTypeName() + "-" + detail.getFileFlowReturnDes(),
								Collectors.joining(",")
						)
				));
	}

	/**
	 * @description: 填充退回描述到订单信息中
	 * @param exportList
	 * @param failOrderIds
	 * @param returnDescMap
	 * @return void
	 * @author: jin.wang03
	 * @date: 2025/2/26 17:08
	 * @since JDK 1.8
	 */
	private void fillReturnDescToOrders(List<CmCounterOrderVo> exportList,
										List<String> failOrderIds,
										Map<String, String> returnDescMap) {
		exportList.stream()
				.filter(order -> failOrderIds.contains(order.getId()))
				.forEach(order -> order.setFileFlowReturnDesSummary(
						returnDescMap.get(order.getId())
				));
	}


	/**
	 * 海外app2.2 数据清洗 需 导出列表数据
	 * 工具类
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/orderExporTool.do")
	public Map<String, Object> orderExporTool(HttpServletRequest request, HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		String orderIds=request.getParameter("orderIds");
		String busiIds=request.getParameter("busiIds");

		if(StringUtil.isEmpty(orderIds) && StringUtil.isEmpty(busiIds) ){
			return  Maps.newHashMap();
		}

		// 设置默认参数
		CmCounterOrderSearchVo vo = new CmCounterOrderSearchVo();

        if(StringUtil.isNotNullStr(orderIds)){
			List<String> idList=Arrays.asList(orderIds.split(SEPARATOR_COMMA));
			vo.setOrderIdList(idList);
		}
		if(StringUtil.isNotNullStr(busiIds)){
			List<String> busiIdList=Arrays.asList(busiIds.split(SEPARATOR_COMMA));
			vo.setBusiIdList(busiIdList);
		}

		List<CmCounterOrderDto> list = null;
		BaseResponse<List<CmCounterOrderDto>> httpRsp = getPostEntityByJsonObject(CrmTradeServerPathConstant.QUERY_COUNTER_ORDR_DTO_LIST,  vo, new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderDto>>>() {});
		if (httpRsp.isSuccess()) {
			list = httpRsp.getData();
		}

		//处理接口返回：赋值字段
		List<CmCounterOrderVo> exportList = listCmCounterOrderVo(list,null);
		//准备 客户号  列表
		List<String> custNoList=exportList.stream().map(CmCounterOrderVo::getConscustno).collect(Collectors.toList());
		//赋值 香港客户信息
		List<HkConscust>  hkCustList=hkConscustService.listHkConscustByCustNoList(custNoList);
       // Map key:客户号  value:香港客户号
		Map<String,String> hkAcctInfoMap=Maps.newHashMap();
		hkCustList.forEach(hkConscust -> {
			hkAcctInfoMap.put(hkConscust.getConscustno(),hkConscust.getHkTxAcctNo());
		});

		//exportList 赋值
		exportList.forEach(exportInfo->{
			exportInfo.setHkTxAcctNo(hkAcctInfoMap.get(exportInfo.getConscustno()));
		});

		ServletOutputStream os =null;
		try {
			// 清空输出流
			response.reset();
			// 设置文件格式和名字
			response.setContentType("multipart/form-data");
			response.setHeader("Content-Disposition", "attachment;fileName=" + new String("资料管理.xls".getBytes("gb2312"), "ISO8859-1"));
			os = response.getOutputStream();
			ExcelWriter.writeExcel(os, "资料管理", 0, exportList, new String[][]{
					{"资料ID", "id"},
					{"香港客户号", "hkTxAcctNo"},
					{"首次上传日期", "credateStr"},
					{"预计交易日期", "expectTradeDt"},
					{"产品代码", "fundCode"},
					{"产品名称", "fundName"},
					{"业务类型", "busiIdName"},
					{"客户姓名", "custName"},
					{"投顾客户号", "conscustno"},
					{"一账通号", "hboneNo"},
					{"证件号", "idnoMask"},
					{"所属区域", "regionName"},
					{"所属部门", "orgName"},
					{"所属投顾", "consName"},
					{"业务属性", "busiProIdName"},
					{"客户类型", "custTypeName"},
					{"预约单号", "preId"},
					{"预约类型", "preTypeName"},
					{"是否进入OP", "productTypeName"},
					{"上传人", "uploadorName"},
					{"最新上传日期", "modDateStr"},
					{"最新审核日期", "curCheckdateStr"},
					{"最新审核人", "curCheckerName"},
					{"最新审核状态", "curStatName"},
					{"是否后补", "isLater"},
					{"签收状态（smop）", "signSmopStatName"},
					{"归档状态（smop）", "archSmopStatName"},
					{"归档日期（smop）", "archSmopDate"},
					{"归档编号（smop）", "docSmopNo"},
					{"邮寄状态（管理人）", "manageMailStatName"},
					{"邮寄日期（管理人）", "manageMailDate"},
					{"邮寄单号（管理人）", "manageMailNo"},
					{"邮寄状态（分部）", "deptMailStatName"},
					{"邮寄日期（分部）", "deptMailDate"},
					{"邮寄单号（分部）", "deptMailNo"},
					{"审核意见", "curCheckdes"}
			});
		} catch (Exception e) {
			log.error("文件导出异常", e);
		}finally {
			try {
				if(os != null){
					os.close();
				}
			} catch (IOException eo) {
				log.error("close stream 异常！",eo);
			}
		}
		return resultMap;
	}



	/**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }


	/**
	 * 导出退回明细
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/exportReturnDetail.do")
	public Map<String, Object> exportReturnDetail(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		// 获取选中的订单编号
		String orderIds = ObjectUtils.replaceNull(request.getParameter("orderIds"));
		orderIds = orderIds.substring(0, orderIds.length() -1);
		List<CmCounterOrderReturnDetailDto> list = null;
		// 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("orderIdList", orderIds);
		BaseResponse<List<CmCounterOrderReturnDetailDto>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_ORDER_RETURN_DETAIL, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderReturnDetailDto>>>(){});
		if (httpRsp.isSuccess()) {
			list = httpRsp.getData();
		}
		//处理接口返回：赋值字段
		List<CmCounterOrderVo> exportList = listCmCounterOrderVo(null,list);

		// 判断导出List是否为空
		if (exportList != null) {
			List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
			User user = getLoginUser();
			String ip = getIpAddr(request);
			for (CmCounterOrderVo info : exportList) {
				//导出日志
	            PageVisitLog pageVisitLog = new PageVisitLog();
	            pageVisitLog.setConscustno(info.getConscustno());
	            pageVisitLog.setUserId(user.getUserId());
	            pageVisitLog.setVisitUrl(request.getRequestURI());
	            pageVisitLog.setOperation("资料管理导出退回明细");
	            pageVisitLog.setVisitTime(new Date());
	            pageVisitLog.setPreid(new BigDecimal(info.getId()));
	            pageVisitLog.setIp(ip);
	            listlog.add(pageVisitLog);
			}
			try {
				// 清空输出流
				response.reset();
				// 设置文件格式和名字
				response.setContentType("multipart/form-data");
				response.setHeader("Content-Disposition", "attachment;fileName=" + new String("资料管理.xls".getBytes("gb2312"), "ISO8859-1"));
				ServletOutputStream os = response.getOutputStream();
				ExcelWriter.writeExcel(os, "退回明细", 0, exportList, new String[][]{
						{"上传日期", "credateStr"},
						{"预计交易日期", "expectTradeDt"},
						{"产品代码", "fundCode"},
						{"产品名称", "fundName"},
						{"业务类型", "busiIdName"},
						{"客户姓名", "custName"},
						{"投顾客户号", "conscustno"},
						{"一账通号", "hboneNo"},
						{"证件号", "idnoMask"},
						{"所属区域", "regionName"},
						{"所属部门", "orgName"},
						{"所属投顾", "consName"},
						{"业务属性", "busiProIdName"},
						{"客户类型", "custTypeName"},
						{"预约单号", "preId"},
						{"预约类型", "preTypeName"},
						{"是否进入OP", "productTypeName"},
						{"上传人", "uploadorName"},
						{"最新审核日期", "curCheckdateStr"},
						{"最新审核人", "curCheckerName"},
						{"最新审核状态", "curStatName"},
						{"是否后补", "isLater"},
						{"签收状态（smop）", "signSmopStatName"},
						{"归档状态（smop）", "archSmopStatName"},
						{"归档日期（smop）", "archSmopDate"},
						{"归档编号（smop）", "docSmopNo"},
						{"邮寄状态（管理人）", "manageMailStatName"},
						{"邮寄日期（管理人）", "manageMailDate"},
						{"邮寄单号（管理人）", "manageMailNo"},
						{"邮寄状态（分部）", "deptMailStatName"},
						{"邮寄日期（分部）", "deptMailDate"},
						{"邮寄单号（分部）", "deptMailNo"},
						{"审核人", "orderFlowCurCheckerName"},
						{"审核时间", "orderFlowCurCheckdateStr"},
						{"审核状态", "orderFlowCurStatName"},
						{"审核意见", "orderFlowCurCheckdes"},
						{"材料名称", "fileTypeName"},
						{"退回原因", "fileFlowReturnDes"}
				});
				// 关闭流
				os.close();
				for(PageVisitLog log : listlog){
	            	pageVisitLogService.recordLog(log);
	            }
			} catch (Exception e) {
				log.error("文件导出异常", e);
			}

			resultMap.put("msg", "success");
		} else {
			resultMap.put("msg", "noData");
		}
		return resultMap;
	}




    /**
	 * 单个订单中附件打包下载
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/orderDownload.do")
	public void orderDownload(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String orderId = request.getParameter("orderId");
		if (StringUtils.isNotBlank(orderId)) {
	    	List<CmCounterOrderFileDto> listFile = querycounterorderfiledtolist(orderId,null,null);

			log.info("订单附件开始打包下载！");
			if (listFile != null && listFile.size() > 0) {
				response.setContentType("application/force-download");
				response.setHeader("Content-disposition", "attachment;filename=" + new String("资料附件.zip".getBytes("gb2312"), "ISO8859-1"));
				OutputStream fos = response.getOutputStream();
				final ZipOutputStream zipOutputStream = new ZipOutputStream(fos);
				// 获取附件文件
				List<CmCounterOrderfileFileDto> fileList = new ArrayList<CmCounterOrderfileFileDto>();
				for (int i = 0; i < listFile.size(); i++) {
					CmCounterOrderFileDto orderFile = listFile.get(i);
					List<CmCounterOrderfileFileDto> orderfileFileList = orderFile.getListCmCounterOrderfileFileDto();
					fileList.addAll(orderfileFileList);
				}
				//给文件流赋值：查询账户中心
				List<CmCounterOrderfileFileMsg> attachFileList = handleFileBytesByDtoList(fileList);
				// 打印订单关联到的附件个数
				log.info("已找到附件文件个数:{} " , attachFileList.size());
				final CountDownLatch countDownLatch = new CountDownLatch(attachFileList.size());
				// 打包下载附件
				for (int j = 0; j < attachFileList.size(); j++) {
					CmCounterOrderfileFileMsg attachFile = attachFileList.get(j);
					final String fileName = attachFile.getFileName();
					final String fileSuffix = attachFile.getFileSuffix();
					taskExecutor.execute(new Runnable() {
						@Override
						public void run() {
							BufferedInputStream bis = null;
							InputStream inputStream = null;
							try {
								byte[] usedBytes = getBytesByFileMsg(attachFile);
								inputStream = new ByteArrayInputStream(usedBytes);
								bis = new BufferedInputStream(inputStream);
								byte[] buf = new byte[8096];
								int size = 0;
								synchronized (zipOutputStream) {
									ZipEntry ze = new ZipEntry(fileName + "." + fileSuffix);
									zipOutputStream.putNextEntry(ze);
									while ((size = bis.read(buf)) != -1) {
										zipOutputStream.write(buf, 0, size);
										zipOutputStream.flush();
									}
								}
							} catch (Exception e) {
								log.error("订单附件下载异常:{}" + e.getMessage(),e);
							} finally {
								if (bis != null) {
									try {
										bis.close();
									} catch (IOException e) {
										log.error("bis数据流出现异常:{}" + e.getMessage(),e);
									}
								}

								if (inputStream != null) {
									try {
										inputStream.close();
									} catch (IOException e) {
										log.error("inputStream数据流出现异常:{}" + e.getMessage(),e);
									}
								}
							}
							countDownLatch.countDown();
						}
					});
				}
				countDownLatch.await();
				if (zipOutputStream != null) {
					zipOutputStream.close();
				}
				log.info("订单附件下载成功！");
			} else {
				log.info("未找到要下载的附件文件！");
			}
		}
	}


    /**
	 * 批量订单附件打包下载
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/counterOrderfileFileListDownload.do")
	 public void counterOrderfileFileListDownload(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String orderIds = ObjectUtils.replaceNull(request.getParameter("orderIds"));
        if (StringUtils.isNotBlank(orderIds)) {
        	//将用|符合隔开的每条记录分离开来
        	orderIds = orderIds.replaceFirst(",", "");
	    	List<CmCounterOrderFileDto> listFile = querycounterorderfiledtolist(null,orderIds,null);

			log.info("批量订单附件开始打包下载！");
			if (CollectionUtils.isNotEmpty(listFile)) {
				response.setContentType("application/force-download");
				response.setHeader("Content-disposition", "attachment;filename=" + new String("资料附件.zip".getBytes("gb2312"), "ISO8859-1"));
				OutputStream fos = response.getOutputStream();
				final ZipOutputStream zipOutputStream = new ZipOutputStream(fos);
				// 获取附件文件
				List<CmCounterOrderfileFileDto> fileList = new ArrayList<CmCounterOrderfileFileDto>();
				for (int i = 0; i < listFile.size(); i++) {
					CmCounterOrderFileDto orderFile = listFile.get(i);
					List<CmCounterOrderfileFileDto> listCmCounterOrderfileFileDto = orderFile.getListCmCounterOrderfileFileDto();
					fileList.addAll(listCmCounterOrderfileFileDto);
				}
				//给文件流赋值：查询账户中心
				List<CmCounterOrderfileFileMsg> attachFileList = handleFileBytesByDtoList(fileList);
				// 打印订单关联到的附件个数
				log.info("已找到附件文件个数:{} ", attachFileList.size() );
				final CountDownLatch countDownLatch = new CountDownLatch(attachFileList.size());
				HFileService instance = HFileService.getInstance();
				// 打包下载附件
				for (int j = 0; j < attachFileList.size(); j++) {
					CmCounterOrderfileFileMsg attachFile = attachFileList.get(j);
					final String filePath = attachFile.getFilePath();
					final String relativeFilePath=attachFile.getRelativeFilePath();
					final String fileId = attachFile.getId();
					final String fileName = attachFile.getFileName();
					final String fileSuffix = attachFile.getFileSuffix();
					final String isMove = attachFile.getIsMove();
					final byte[] fileBytes = attachFile.getFileBytes();
					taskExecutor.execute(new Runnable() {
						@Override
						public void run() {
							BufferedInputStream bis = null;
							InputStream inputStream = null;
							try {
								log.info("处理文件。fileId：{},isMove{},fileBytes.length:{}", fileId,isMove, fileBytes == null ? 0 : fileBytes.length);
								//取账户中心文件
								if(StaticVar.IS_MOVE.equals(isMove)){
									inputStream = new ByteArrayInputStream(fileBytes);
									bis = new BufferedInputStream(inputStream);
								//取本地文件
								}else{
									byte[]  fileBytes= new byte[0];
									try {
										fileBytes = instance.read2Bytes(DfileConstants.COUNTER_FILE_PATH,
												relativeFilePath,
												String.join(".", fileName, fileSuffix)
										);
									} catch (Exception e) {
										log.error("文件ID：{}，文件名称：{}，读取异常！",fileId,fileName);
										log.error("读取文件异常！",e);
									}
									inputStream = new ByteArrayInputStream(fileBytes);
									bis = new BufferedInputStream(inputStream);
								}

								byte[] buf = new byte[8096];
								int size = 0;
								synchronized (zipOutputStream) {
									ZipEntry ze = new ZipEntry(fileName + "." + fileSuffix);
									zipOutputStream.putNextEntry(ze);
									while ((size = bis.read(buf)) != -1) {
										zipOutputStream.write(buf, 0, size);
										zipOutputStream.flush();
									}
								}
							} catch (Exception e) {
								log.error("批量订单附件下载异常:{}" + e.getMessage(),e);
							} finally {
								if (bis != null) {
									try {
										bis.close();
									} catch (IOException e) {
										log.error("bis数据流出现异常:{}" + e.getMessage(),e);
									}
								}
								if (inputStream != null) {
									try {
										inputStream.close();
									} catch (IOException e) {
										log.error("inputStream数据流出现异常:{}" + e.getMessage(),e);
									}
								}
							}
							countDownLatch.countDown();
						}
					});
				}
				countDownLatch.await();
				if (zipOutputStream != null) {
					zipOutputStream.close();
				}
				log.info("批量订单附件下载成功！");
			} else {
				log.info("未找到要下载的附件文件！");
			}
        }
	}


	/**
     * 跳转到分类选择下载材料页面
     *
     */
    @RequestMapping("/viewDownOrderFileFiles.do")
    public ModelAndView viewDownOrderFileFiles(HttpServletRequest request,HttpServletResponse response) throws Exception {
    	String orderIds = ObjectUtils.replaceNull(request.getParameter("orderIds"));
    	String busiId = request.getParameter("busiId");
//    	List<String> orderIdList = null;
        if (StringUtils.isNotBlank(orderIds)) {
        	//将用|符合隔开的每条记录分离开来
        	orderIds = orderIds.replaceFirst(",", "");
//        	orderIdList = Arrays.asList(orderIds.split(","));
        }

        // 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("orderIdList", orderIds);
    	postParam.put("busiId", busiId);
    	List<CmCounterFiletype> list = null;
		BaseResponse<List<CmCounterFiletype>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_FILE_TYPE_LIST_BY_CONDITION, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterFiletype>>>(){});
		if (httpRsp.isSuccess()) {
			list = httpRsp.getData();
		}

        // 设置查询参数
		Map<String, Object> map = new HashMap<String, Object>(3);
		map.put("list", list);
		map.put("orderIds",  ObjectUtils.replaceNull(request.getParameter("orderIds")));
		map.put("busiId", busiId);

        return new ModelAndView("/counter/viewDownOrderFileFiles", "map", map);
    }

	/**
	 * 分类下载前验证
	 * @param request
	 * @return
	 * @throws Exception
	 */
    @ResponseBody
    @RequestMapping("/validateDownOrderfileFiles.do")
    public String validateDownOrderfileFIles(HttpServletRequest request) throws Exception {
    	String result = "success";
    	String orderIds = ObjectUtils.replaceNull(request.getParameter("orderIds"));
		String busiId = request.getParameter("busiId");
		String fileTypeIds = ObjectUtils.replaceNull(request.getParameter("fileTypeIds"));
		List<CmCounterOrderfileFile> newListFile = new ArrayList<CmCounterOrderfileFile>(16);
		if (StringUtils.isNotBlank(orderIds) && StringUtils.isNotBlank(fileTypeIds)) {
	        	//将用|符合隔开的每条记录分离开来
			    orderIds = orderIds.replaceFirst(",", "");
//			    List<String> orderIdList = Arrays.asList(orderIds.split(","));
			    fileTypeIds = fileTypeIds.replaceFirst(",", "");
//			    List<String> fileTypeIdList = Arrays.asList(fileTypeIds.split(","));

			    List<CmCounterOrderfileFile> listFile = queryCounterOrderfileFileListByCondition( orderIds, fileTypeIds, busiId);
				
				if (CollectionUtils.isNotEmpty(listFile)) {
					//判断大小是否超过300M
					long filesize = 0;
	                for (CmCounterOrderfileFile orderfileFile : listFile) {
	                	if(orderfileFile == null){
	                		continue;
	                	}
						newListFile.add(orderfileFile);
	                	filesize += (orderfileFile.getFileSize()==null?0:orderfileFile.getFileSize());
	                	if(filesize > MAX_DOWNLOAD_SIZE){
	                		return String.format("所选择下载的文件大小不能超过%sM！",MAX_SIZE_MB);
	                	}
					}
				}
				
				if(CollectionUtils.isEmpty(newListFile)){
					result = "所选资料中无满足条件的文件！";
				}
		}
		
        return result;
    }

	/**
	 * 订单文件分类打包下载
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/orderfileFilestDownloadByFl.do")
	public void  orderfileFilestDownloadByFl(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String orderIds = ObjectUtils.replaceNull(request.getParameter("orderIds"));
		String busiId = request.getParameter("busiId");
		String fileTypeIds = ObjectUtils.replaceNull(request.getParameter("fileTypeIds"));
        if (StringUtils.isNotBlank(orderIds) && StringUtils.isNotBlank(fileTypeIds)) {
        	//将用|符合隔开的每条记录分离开来
        	orderIds = orderIds.replaceFirst(",", "");
//        	List<String> orderIdList = Arrays.asList(orderIds.split(","));
		    fileTypeIds = fileTypeIds.replaceFirst(",", "");
//		    List<String> fileTypeIdList = Arrays.asList(fileTypeIds.split(","));
	    	List<CmCounterOrderfileFile> listFile = queryCounterOrderfileFileListByCondition( orderIds, fileTypeIds, busiId);

			log.info("订单指定业务类型附件开始打包下载！busiId:{}",busiId);
			if (listFile != null && listFile.size() > 0) {
				response.setContentType("application/force-download");
				response.setHeader("Content-disposition", "attachment;filename=" + new String("资料附件.zip".getBytes("gb2312"), "ISO8859-1"));
				OutputStream fos = response.getOutputStream();
				final ZipOutputStream zipOutputStream = new ZipOutputStream(fos);
				//给文件流赋值：查询账户中心
				List<CmCounterOrderfileFileMsg> attachFileList = handleFileBytesByFileList(listFile);
				// 打印订单关联到的附件个数
				log.info("已找到 附件文件个数:{}" , attachFileList.size());
				log.info("已找到 附件文件list:{}" , JSON.toJSONString(attachFileList));
				if(CollectionUtils.isEmpty(attachFileList)){
					return;
				}
				final CountDownLatch countDownLatch = new CountDownLatch(attachFileList.size());
				HFileService instance = HFileService.getInstance();
				// 打包下载附件
				for (int j = 0; j < attachFileList.size(); j++) {
					CmCounterOrderfileFileMsg attachFile = attachFileList.get(j);
					final String filePath = attachFile.getFilePath();
					final String relativeFilePath= attachFile.getRelativeFilePath();
					final String fileId = attachFile.getId();
					final String fileName = attachFile.getFileName();
					final String fileSuffix = attachFile.getFileSuffix();
					final String isMove = attachFile.getIsMove();
					final byte[] fileBytes = attachFile.getFileBytes();
					taskExecutor.execute(new Runnable() {
						@Override
						public void run() {
							BufferedInputStream bis = null;
							InputStream inputStream = null;
							try {
								log.info("处理文件。fileId：{},isMove{},fileBytes.length:{}", fileId,isMove, fileBytes == null ? 0 : fileBytes.length);
								//取账户中心文件
								if(StaticVar.IS_MOVE.equals(isMove)){
									inputStream = new ByteArrayInputStream(fileBytes);
									bis = new BufferedInputStream(inputStream);
								//取本地文件
								}else{
									byte[]  fileBytes= new byte[0];
									try {
										fileBytes = instance.read2Bytes(DfileConstants.COUNTER_FILE_PATH,
												relativeFilePath,
												String.join(".", fileName, fileSuffix)
										);
									} catch (Exception e) {
										log.error("文件ID：{}，文件名称：{}，读取异常！",fileId,fileName);
										log.error("读取文件异常！",e);
									}
									inputStream = new ByteArrayInputStream(fileBytes);
									bis = new BufferedInputStream(inputStream);
								}

								byte[] buf = new byte[8096];
								int size = 0;
								synchronized (zipOutputStream) {
									ZipEntry ze = new ZipEntry(fileName + "." + fileSuffix);
									zipOutputStream.putNextEntry(ze);
									while ((size = bis.read(buf)) != -1) {
										zipOutputStream.write(buf, 0, size);
										zipOutputStream.flush();
									}
								}
							} catch (Exception e) {
								log.error("指定业务类型附件下载异常:{}" + e.getMessage(),e);
							} finally {
								if (bis != null) {
									try {
										bis.close();
									} catch (IOException e) {
										log.error("bis数据流出现异常:{}" + e.getMessage(),e);
									}
								}
								if (inputStream != null) {
									try {
										inputStream.close();
									} catch (IOException e) {
										log.error("inputStream数据流出现异常:{}" + e.getMessage(),e);
									}
								}
								countDownLatch.countDown();
							}
						}
					});
				}
				countDownLatch.await();
				if (zipOutputStream != null) {
					zipOutputStream.close();
				}
				log.info("订单指定业务类型附件下载成功！busiId:{}",busiId);
			} else {
				log.info("未找到指定业务类型要下载的附件文件！busiId:{}",busiId);
			}
        }
	}

	/**
	 * 根据条件查询List<CmCounterOrderFileDto>
	 * @param orderIds
	 * @param orderId
	 * @return
	 */
	private List<CmCounterOrderFileDto> querycounterorderfiledtolist(String orderId,String orderIds,String checkLevel){
    	// 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("orderId", orderId);
    	postParam.put("orderIdList", orderIds);
    	postParam.put("checkLevel", checkLevel);

    	List<CmCounterOrderFileDto> listFile = null;
		BaseResponse<List<CmCounterOrderFileDto>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_ORDER_FILE_DTO_LIST, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderFileDto>>>(){});
		if (httpRsp.isSuccess()) {
			listFile = httpRsp.getData();
		}
        return listFile;
	}

	/**
	 * 根据条件查询List<CmCounterOrderfileFile>
	 * @param orderIds
	 * @param fileTypeIds
	 * @param busiId
	 * @return
	 */
	private List<CmCounterOrderfileFile> queryCounterOrderfileFileListByCondition(String orderIds,String fileTypeIds,String busiId){
		// 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("orderIdList", orderIds);
    	postParam.put("fileTypeIdList", fileTypeIds);
    	postParam.put("busiId", busiId);
    	List<CmCounterOrderfileFile> listFile = null;
		BaseResponse<List<CmCounterOrderfileFile>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_ORDER_FILE_FILE_LIST_BY_CONDITION, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderfileFile>>>(){});
		if (httpRsp.isSuccess()) {
			listFile = httpRsp.getData();
		}

        return listFile;
	}



	/**
	 * 单个类型中单个文件下载
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/toDownloadOrderfileFile.do")
	public void toDownloadOrderfileFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String id = request.getParameter("id");
		if (StringUtils.isNotBlank(id)) {
            // 设置参数
        	Map<String,String> postParam = new HashMap<String,String>();
        	postParam.put("id", id);
        	CmCounterOrderfileFileMsg file = null;
			BaseResponse<CmCounterOrderfileFile> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.GET_CM_COUNTER_ORDER_FILE_FILE, postParam,new ParameterizedTypeReference<BaseResponse<CmCounterOrderfileFile>>(){});
			if (httpRsp.isSuccess()) {
				CmCounterOrderfileFile cmCounterOrderfileFile = httpRsp.getData();
				//给文件流赋值：查询账户中心
				List<CmCounterOrderfileFileMsg> attachFileList = null;
				if(cmCounterOrderfileFile != null){
					attachFileList = handleFileBytesByFileList(Lists.newArrayList(cmCounterOrderfileFile));
				}
				file = CollectionUtils.isEmpty(attachFileList) ? null : attachFileList.get(0);
			}

			log.info("附件开始打包下载！");
			if (file != null) {
				response.setContentType("application/force-download");
				response.setHeader("Content-disposition", "attachment;filename=" + new String("附件.zip".getBytes("gb2312"), "ISO8859-1"));
				OutputStream fos = response.getOutputStream();
				final ZipOutputStream zipOutputStream = new ZipOutputStream(fos);

				final String fileName = file.getFileName();
				final String fileSuffix = file.getFileSuffix();
				BufferedInputStream bis = null;
				InputStream inputStream = null;
				try {
					byte[] usedBytes=getBytesByFileMsg(file);
					inputStream = new ByteArrayInputStream(usedBytes);
					bis = new BufferedInputStream(inputStream);
					byte[] buf = new byte[8096];
					int size = 0;
					synchronized (zipOutputStream) {
						ZipEntry ze = new ZipEntry(fileName + "." + fileSuffix);
						zipOutputStream.putNextEntry(ze);
						while ((size = bis.read(buf)) != -1) {
							zipOutputStream.write(buf, 0, size);
							zipOutputStream.flush();
						}
					}
				} catch (Exception e) {
					log.error("附件下载异常:{}" + e.getMessage(),e);
				} finally {
					if (bis != null) {
						try {
							bis.close();
						} catch (IOException e) {
							log.error("bis数据流出现异常:{}" + e.getMessage(),e);
						}
					}

					if (inputStream != null) {
						try {
							inputStream.close();
						} catch (IOException e) {
							log.error("inputStream数据流出现异常:{}" + e.getMessage(),e);
						}
					}
				}

				if (zipOutputStream != null) {
					zipOutputStream.close();
				}
				log.info("附件下载成功！");
			} else {
				log.info("未找到要下载的附件文件！");
			}
		}

	}


	/**
	 * 组装文件类型下拉框
	 * @param request
	 * @param response
	 * @return
	 */
	@ResponseBody
    @RequestMapping("/getBusiIdList")
    public List<Map<String,Object>> getBusiIdList(HttpServletRequest request, HttpServletResponse response) {
        List<Map<String,Object>> busiIdList = new ArrayList<Map<String,Object>>(200);

        String busiProId = request.getParameter("busiProId");
        Map<String, Object> busiIdMap = new HashMap<>(2);
    	busiIdMap.put("id", "");
    	busiIdMap.put("text", "全部");
    	busiIdList.add(busiIdMap);

		// 通过Session获取产品广度信息
		HttpSession session = request.getSession();
		String topcpdata = (String) session.getAttribute("topcpdata");

		// 需要对以下业务进行屏蔽：香港-开户、香港-认申购、香港-赎回、香港-撤单、香港-追加、香港-身份信息变更
		// 香港-银行卡信息变更、香港-其它信息变更、香港-份额转换、定投新增、定投终止、好臻开户
		List<CounterBusiEnum> listBusiIdEnum = CounterUtil.REJECT_BUSI_ID;
		if(StringUtils.isNotBlank(busiProId)){
        	Map<String,String> busiidMap = new HashMap<String,String>(200);

        	// 设置参数
        	Map<String,String> postParam = new HashMap<String,String>();
        	postParam.put("busiProId", busiProId);
        	postParam.put("stat", "1");
			String busiArea = request.getParameter("busiArea");
			postParam.put("busiArea", busiArea);
			BaseResponse<List<CmCounterBusiness>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_BUSINESS_LIST, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterBusiness>>>(){});
			List<CmCounterBusiness> businessList = httpRsp.isSuccess() ? httpRsp.getData() : null;
        	if(CollectionUtils.isNotEmpty(businessList)){
        		for(CmCounterBusiness cmCounterBusiness : businessList){
        			if(!busiidMap.containsKey(cmCounterBusiness.getBusiId())){
        				CounterBusiEnum busiEnum=CounterBusiEnum.getEnum(cmCounterBusiness.getBusiId());
						// 只显示常规产品相关业务（过滤掉其他业务）
						if ("32".equals(topcpdata) && listBusiIdEnum.contains(busiEnum)) {
							continue;
						}

        				busiidMap.put(cmCounterBusiness.getBusiId(), cmCounterBusiness.getBusiId());
        				busiIdMap = new HashMap<String, Object>(2);
                    	busiIdMap.put("id", cmCounterBusiness.getBusiId());
                    	busiIdMap.put("text", StringUtils.isBlank(cmCounterBusiness.getBusiId()) ? "" : CounterBusiEnum.getDesc(cmCounterBusiness.getBusiId()));
                    	busiIdList.add(busiIdMap);
        			}
        		}
        	}
        }else{
        	Iterator<CounterBusiEnum> iterator = Arrays.stream(CounterBusiEnum.values()).iterator();
            while (iterator.hasNext()){
            	CounterBusiEnum next = iterator.next();
            	if ("32".equals(topcpdata) && listBusiIdEnum.contains(next)) {
					continue;
				}

            	busiIdMap = new HashMap<>(2);
            	busiIdMap.put("id", next.getKey());
            	busiIdMap.put("text", next.getDesc());
            	busiIdList.add(busiIdMap);
            }
        }

        return busiIdList;
    }


	/**
	 * 根据客户号和业务ID校验是否有在途业务(审核状态 != 审核通过/作废)
	 * TODO: 未知使用路径， 该业务不合业务规范 待删除
	 * @param conscustno
	 * @param busiId 业务typeID
	 * @return  ReturnMessageDto<Boolean>
	 */
	@ResponseBody
	@RequestMapping("/validateOrderByConscustnoAndBusiId.do")
	public ReturnMessageDto<String> validateOrderByConscustnoAndBusiId(String conscustno, String busiId){
		if(StringUtils.isEmpty(conscustno) || StringUtils.isEmpty(busiId)){
			ReturnMessageDto returnMessageDto = new ReturnMessageDto();
			returnMessageDto.setReturnCode(com.howbuy.crm.base.BaseConstantEnum.SYS_ERROR.getCode());
			returnMessageDto.setReturnMsg("校验查询订单参数信息异常!");
			return  returnMessageDto;
		}

		//点击按钮时校验是否配置文件
		GetCounterBusinessParamVo paramVo=new GetCounterBusinessParamVo();
		paramVo.setConscustNo(conscustno);
		paramVo.setBusiType(busiId);

		BaseResponse<CmCounterBusinessMsgDto>  typeDto= getPostEntityByJsonObject(CrmTradeServerPathConstant.GET_BUSSINESS_BY_BUSITYPE,
				paramVo,
				new ParameterizedTypeReference<BaseResponse<CmCounterBusinessMsgDto>>(){});

		if(null==typeDto|| (!typeDto.isSuccess()) || StringUtils.isEmpty(typeDto.getData().getId())){
			ReturnMessageDto returnMessageDto = new ReturnMessageDto();
			returnMessageDto.setReturnCode(com.howbuy.crm.base.BaseConstantEnum.SYS_ERROR.getCode());
			returnMessageDto.setReturnMsg("该类型业务被禁用或未配置，如需上传资料，请联系总部销助！");
			return  returnMessageDto;
		}

		CmCounterOrderSearchVo searchVo = new CmCounterOrderSearchVo();
		searchVo.setConscustno(conscustno);
		searchVo.setBusiId(busiId);
		log.info("查询订单数据接口调用参数:{}" , JSON.toJSONString(searchVo));
		BaseResponse<List<CmCounterOrderDto>> httpRsp =
				getPostEntityByJsonObject(CrmTradeServerPathConstant.QUERY_COUNTER_ORDR_DTO_LIST,
						searchVo,new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderDto>>>(){});
		if(null==httpRsp|| (!httpRsp.isSuccess())){
			ReturnMessageDto returnMessageDto = new ReturnMessageDto();
			returnMessageDto.setReturnCode(com.howbuy.crm.base.BaseConstantEnum.SYS_ERROR.getCode());
			returnMessageDto.setReturnMsg("校验查询订单信息异常!");
			return  returnMessageDto;
		}
		//没查询到单子
		List<CmCounterOrderDto> data = httpRsp.getData();
		if(CollectionUtils.isEmpty(data) ){
			return  ReturnMessageDto.ok("");
		}else{
			//查询到单子
			boolean flag = false;
			for(CmCounterOrderDto dto: data){
				if(!CounterStateEnum.ORDER_PASS.getKey().equals(dto.getCurStat())
						&& !CounterStateEnum.CANCEL.getKey().equals(dto.getCurStat())){
					flag = true;
					break;
				}
			}
			//有审核状态 != 审核通过/作废
			if(flag){
				return  ReturnMessageDto.fail("");
			}else{
				return  ReturnMessageDto.ok("");
			}
		}
	}

	/**
	 * @description:(预览播放 视频)
	 * @param request
	 * @return org.springframework.web.servlet.ModelAndView
	 * @author: haoran.zhang
	 * @date: 2024/6/11 16:10
	 * @since JDK 1.8
	 */
	@RequestMapping("/showCounterView.do")
	public ModelAndView showDocView(HttpServletRequest request) {
		String id = request.getParameter("id");
		Map<String, String> param = new HashMap<String, String>();
		param.put("id", id);
		BaseResponse<CmCounterOrderfileFile> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.GET_CM_COUNTER_ORDER_FILE_FILE,
				param,
				new ParameterizedTypeReference<BaseResponse<CmCounterOrderfileFile>>(){});
		String fp = null;
		CmCounterOrderfileFile orderFile=null;
		if (httpRsp.isSuccess()) {
			orderFile = httpRsp.getData();
		}
		if(orderFile!=null){
			fp=String.join("",
					counterFileVisitPath,
					orderFile.getRelativeFilePath(),File.separator,
					orderFile.getFileName(),".",
					orderFile.getFileSuffix());
		}
		//[http://crm.intelnal.howbuy.com/crm-doc/] [20240830/] [1076821038-周虹霞-051811-双录视频-1394187].[MP4]
		//http://crm.intelnal.howbuy.com/crm-doc/20240830/1076821038-xxx.MP4
		log.info("broadcast view .counterFileId:{}, view path:{}",id,fp);
		request.setAttribute("fp", fp);
		return new ModelAndView("/counter/BroadcastView");
	}



}

