package com.howbuy.crm.hb.web.controller.callout;

import com.howbuy.crm.hb.domain.callout.CsTaskSourceConfig;
import com.howbuy.crm.hb.service.callout.ManAssignTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 外呼任务手动分配处理类
 */
@Controller
@RequestMapping("/callout/manual")
public class ManAssignTaskController {
    @Autowired
    private ManAssignTaskService manAssignTaskService;

    /**
     * 获取指定任务下拉框数据
     * @param request
     * @return Object
     */
    @ResponseBody
    @RequestMapping("/listTaskSourceConfig")
    public List<Map<String, Object>> listTaskSource(HttpServletRequest request) {
        Map<String, String> map = new HashMap<String, String>();
        if (request.getParameter("parentId") != null) {
            map.put("parentId", request.getParameter("parentId"));
            map.put("sourceLevel", request.getParameter("sourceLevel"));
        } else {
            map.put("parentId", "0");// 默认查询任务来源一级菜单
        }
        List<CsTaskSourceConfig> list = manAssignTaskService.listCsTaskSourceConfig(map);
        List<Map<String, Object>> mapList = new LinkedList<Map<String, Object>>();

        for (CsTaskSourceConfig c : list) {
            Map<String, Object> map1 = new HashMap<String, Object>();
            map1.put("id", c.getSourceId());
            map1.put("text", c.getSourceName());
            mapList.add(map1);
        }
        return mapList;
    }
}
