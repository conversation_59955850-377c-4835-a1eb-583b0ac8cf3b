package com.howbuy.crm.hb.web.controller.doubletrade;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acccenter.facade.trade.kycinfo.KycInfoResponse;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.account.client.response.consultantinfo.CmCustBindRelationVO;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.base.doubletrade.DoubeHandleFlagEnum;
import com.howbuy.crm.base.doubletrade.DoubleTaskTypeEnum;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.doubletrade.dto.CmFeedBackTradeRelation;
import com.howbuy.crm.doubletrade.dto.DoubleTradeInfoDomain;
import com.howbuy.crm.doubletrade.request.CmFeedBackTradeRelationVo;
import com.howbuy.crm.doubletrade.service.DoubleTradeRelatedService;
import com.howbuy.crm.doubletrade.service.FeedBackRelationService;
import com.howbuy.crm.doubletrade.service.FeedBackTaskService;
import com.howbuy.crm.doubletrade.service.VideoDoubleTradeService;
import com.howbuy.crm.hb.constants.DfileConstants;
import com.howbuy.crm.hb.domain.callout.CsCalloutRec;
import com.howbuy.crm.hb.domain.callout.CsCommunicateVisit;
import com.howbuy.crm.hb.domain.custinfo.CmRealConsultant;
import com.howbuy.crm.hb.domain.doubletrade.*;
import com.howbuy.crm.hb.domain.system.HbUserrole;
import com.howbuy.crm.hb.domain.webservice.PhoneCallRecord;
import com.howbuy.crm.hb.outersevice.CmWechatOuterService;
import com.howbuy.crm.hb.persistence.doubletrade.CmVisitRecordMapper;
import com.howbuy.crm.hb.service.callout.CsCalloutRecService;
import com.howbuy.crm.hb.service.callout.CsCommunicateVisitService;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.CmRealConsultantService;
import com.howbuy.crm.hb.service.doubletrade.*;
import com.howbuy.crm.hb.service.external.AcctRelatedService;
import com.howbuy.crm.hb.service.system.CmOptLogService;
import com.howbuy.crm.hb.service.system.HbUserroleService;
import com.howbuy.crm.hb.service.webservice.TeleSaleCountStatisticsServiceImpl;
import com.howbuy.crm.hb.tools.excel.bean.ExcelDataResult;
import com.howbuy.crm.hb.tools.excel.conf.ExcelImpTableConf;
import com.howbuy.crm.hb.tools.excel.reader.ExcelReader;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.doubletrade.DoubleFileUpdateVo;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.nt.base.model.RemindEnum;
import com.howbuy.crm.nt.pushmsg.request.CmPushMsgRequest;
import com.howbuy.crm.nt.pushmsg.service.CmPushMsgService;
import com.howbuy.crm.nt.remind.request.QueryRemindRequest;
import com.howbuy.crm.nt.remind.service.RemindWithdrawMessageService;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import com.howbuy.crm.util.FileUtil;
import com.howbuy.dfile.HFileService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.dubbo.response.BaseResponse;
import crm.howbuy.base.enums.CrmCustInvestTypeEnum;
import crm.howbuy.base.enums.CrmUserRoleEnum;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.HttpUtils;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: Controller
 * @version 1.0
 */
@Slf4j
@Controller
@RequestMapping("/doubletrade")
public class CmDoubleRecordController  extends BaseController {
	@Value("${FILE_VISIT_PATH}")
	private String crmFilePath;

	@Autowired
	private CmDoubleTradeService cmDoubleTradeService;

	@Autowired
	private CmDoubleFileService cmDoubleFileService;

	@Autowired
	private CmDoubleTradeRsService cmDoubleTradeRsService;

	@Autowired
	private HbUserroleService hbUserRoleService;

	@Autowired
	private DoubleTradeRelatedService doubleTradeRelatedService;

	@Autowired
	private QueryConscustInfoService queryConscustInfoService;

	@Autowired
	private CmVisitRecordMapper cmVisitRecordMapper;
	@Autowired
	private CsCalloutRecService csCalloutRecService;
	@Autowired
	private CsCommunicateVisitService csCommunicateVisitService;
	@Autowired
	private CommonService commonService;
	@Autowired
	private TeleSaleCountStatisticsServiceImpl teleSaleCountStatisticsService;
	@Autowired
	private CmOptLogService cmOptLogService;
	@Autowired
	private CmDoubleRemarkService cmDoubleRemarkService;
	@Autowired
	private VideoDoubleTradeService videoDoubleTradeService;
	@Autowired
	private RemindWithdrawMessageService remindWithdrawMessageService;
	@Autowired
	private PageVisitLogService pageVisitLogService;
	@Autowired
	private DecryptSingleFacade decryptSingleFacade;
	@Autowired
	private CmDoubleTradeChecklogService cmDoubleTradeChecklogService;
	@Autowired
	private CmPushMsgService cmPushMsgService;

	@Autowired
	private CmWechatOuterService cmWechatOuterService;

	@Autowired
	private AcctRelatedService acctRelatedService;

	@Autowired
	private JjxxInfoService jjxxInfoService;
	@Autowired
	private FeedBackTaskService feedBackTaskService;

	@Autowired
	private CmRealConsultantService cmRealConsultantService;

	@Autowired
	private FeedBackRelationService feedBackRelationService;


	@Value("${UPLOAD_WAIT_MINUTE}")
	private int UPLOAD_WAIT_MINUTE;


	@RequestMapping("/generatefeedback.do")
	@ResponseBody
	public ReturnMessageDto<String> generateFeedBack(String sids,HttpServletRequest request){

		List<String> tradeIdList = Arrays.asList(sids.split(SEPARATOR_COMMA));
		return feedBackTaskService.executeGenerateTask(tradeIdList,getLoginUserId(request));
	}


	/**
	 * 校验已存在 且未作答完成提交  的问卷
	 * @param sids
	 * @param request
	 * @return
	 */
	@RequestMapping("/validateExistfeedback.do")
	@ResponseBody
	public List<CmFeedBackTradeRelation> validateExistfeedback(String sids,HttpServletRequest request){
		List<String> tradeIdList = Arrays.asList(sids.split(SEPARATOR_COMMA));

		CmFeedBackTradeRelationVo vo=new CmFeedBackTradeRelationVo();

		vo.setTradeIdList(tradeIdList);
//		1-答案已提交回执，0-未作答
		vo.setFeedbackStatusList(Lists.newArrayList("0"));
		List<CmFeedBackTradeRelation> relationList=feedBackRelationService.selectListByVo( vo);

		//去重， 同一个tradeId 只保留一条，
		return relationList.stream()
				.distinct()
				.collect(Collectors.collectingAndThen(Collectors.toCollection(
						() -> new TreeSet<>(Comparator.comparing(
								relation -> relation.getTradeId()
						))
				), ArrayList::new));

	}

	/**
	 * 校验已存在 且未作答完成提交  的问卷
	 * @param sids
	 * @param request
	 * @return
	 */
	@GetMapping("/validatetradedt")
	@ResponseBody
	public List<CmFeedBackTradeRelation> validateTradeDtIsNull(String sids,HttpServletRequest request){
		List<String> tradeIdList = Arrays.asList(sids.split(SEPARATOR_COMMA));
		List<CmDoubleTrade> cmDoubleTradeList = cmDoubleTradeService.getCmDoubleTradeList(tradeIdList);

		// 查询出来打款确认日期为空的数据
		List<String> collect = cmDoubleTradeList.stream().filter(it -> it.getPmtDt() == null).map(CmDoubleTrade::getHboneNo).collect(Collectors.toList());

		CmFeedBackTradeRelationVo vo=new CmFeedBackTradeRelationVo();

		vo.setTradeIdList(tradeIdList);
        // 1-答案已提交回执，0-未作答
		vo.setFeedbackStatusList(Lists.newArrayList("0"));
		List<CmFeedBackTradeRelation> cmFeedBackTradeRelations = feedBackRelationService.selectListByVo(vo);
		List<CmFeedBackTradeRelation> cmFeedBackTradeRelationList = cmFeedBackTradeRelations.stream().filter(it -> collect.contains(it.getHboneNo())).collect(Collectors.collectingAndThen(
				Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(CmFeedBackTradeRelation::getHboneNo))),
				ArrayList::new
		));

		return cmFeedBackTradeRelationList;
	}

	/**
	 * 跳转到双录EC页面
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping("/listEcDoubleRecord.do")
	public String listEcDoubleRecord(HttpServletRequest request, HttpServletResponse response, ModelMap model) {
		List<String> roles = (List<String>)request.getSession().getAttribute("loginRoles");
		response.setContentType("text/html; charset=utf-8");
		String menuCode = "070105";
		//修改需要处理状态
		String operCode = "01";
		//查看理财通回访数据
		String operLctCode = "02";
		// 任务分配
		String operAssignTaskCode = "14";
		boolean hasAuth = false;
		boolean seeLctVisit = false;
		boolean hasAssignTaskAuth = false;
		//改为待处理
		boolean updcl = false;
		//不需要处理
		boolean bxycl = false;
		//拨号
		boolean bh = false;
		//修改为已处理
		boolean upycl = false;
		//本地上传
		boolean bdscStatus = false;
		//CRM上传
		boolean crmscStatus = false;
		//查询
		boolean cxStatus = false;
		//查询-删除
		boolean cxscStatus = false;
		//处理人（全部）
		boolean conductorAll = false; 
		//反馈日期默认当天
		boolean feedbackDateToday = false; 

		for (String role : roles) {
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menuCode);
			if (temp != null && temp.contains(operCode) && !hasAuth) {
				hasAuth = true;
			}
			if (temp != null && temp.contains(operLctCode) && !seeLctVisit) {
				seeLctVisit = true;
			}
			if (temp != null && temp.contains(operAssignTaskCode) && !hasAssignTaskAuth) {
				hasAssignTaskAuth = true;
			}
			if (temp != null && temp.contains("03") && !updcl) {
				updcl = true;
			}
			if (temp != null && temp.contains("04") && !bxycl) {
				bxycl = true;
			}
			if (temp != null && temp.contains("05") && !bh) {
				bh = true;
			}
			if (temp != null && temp.contains("06") && !upycl) {
				upycl = true;
			}

			if (temp != null && temp.contains("07") && !bdscStatus) {
				bdscStatus = true;
			}
			if (temp != null && temp.contains("08") && !crmscStatus) {
				crmscStatus = true;
			}
			if (temp != null && temp.contains("09") && !cxStatus) {
				cxStatus = true;
			}
			if (temp != null && temp.contains("10") && !cxscStatus) {
				cxscStatus = true;
			}
			// 配置了“处理人（全部）”权限时，处理人选择“全部”
			if (temp != null && temp.contains("12") && !conductorAll) {
				conductorAll = true;
			}
			if (temp != null && temp.contains("13") && !feedbackDateToday) {
				feedbackDateToday = true;
			}
		}

		// 当前用户所属角色不包含“回访客服”时，处理人选择“全部”
		if (CollectionUtils.isNotEmpty(roles) && !roles.contains(CrmUserRoleEnum.ROLE_PS.getCode()) && !conductorAll) {
			conductorAll = true;
		}

		model.put("hasAuth", hasAuth);
		model.put("seeLctVisit", seeLctVisit);
		model.put("hasAssignTaskAuth", hasAssignTaskAuth);

		model.put("updcl", updcl);
		model.put("bxycl", bxycl);
		model.put("bh", bh);
		model.put("upycl", upycl);

		model.put("bdscStatus", bdscStatus);
		model.put("crmscStatus", crmscStatus);
		model.put("cxStatus", cxStatus);
		model.put("cxscStatus", cxscStatus);
		model.put("crmFilePath", crmFilePath);
		model.put("conductorAll", conductorAll);
		model.put("feedbackDateToday", feedbackDateToday);
		Map<String,String> statusKeyVal = ConstantCache.getInstance().getConstantKeyVal("DoubleTradeRe");
		model.put("statusKeyVal", statusKeyVal);

		// 存放下拉选项的键值对
		Map<String,Object> map = new HashMap<String,Object>(2);

		// 处理人
		List<Map<String, String>> conductorList = cmDoubleTradeService.getConductorList(new HashMap<>(1));
		Map<String, String> conductorMap = new HashMap<>(2);
		conductorMap.put("ID", "");
		conductorMap.put("TEXT", "全部");
		conductorList.add(0, conductorMap);

		conductorMap = new HashMap<>(2);
		conductorMap.put("ID", "0");
		conductorMap.put("TEXT", "空");
		conductorList.add(1, conductorMap);
		map.put("conductorList", conductorList);

		// 新处理人
		Map<String, String> paramMap = new HashMap<>(1);
		paramMap.put("consstatus", "1");
		map.put("newConductorList", cmDoubleTradeService.getConductorList(paramMap));
		model.put("map", map);

		HttpSession session = request.getSession();
		String userId = (String) session.getAttribute("userId");
		model.put("userId", userId);

		return "doubletrade/listEcDoubleRecord";
	}

	/**
	 * 分配任务处理人
	 * @param tradeIds 客服回访任务id（如果有多个，则以逗号分隔）
	 * @param conductor 处理人id
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/assignTask.do")
	public Map<String, Object> assignTask(String tradeIds, String conductor) {
		Map<String, Object> resultMap = new HashMap<>(2);
		try {
			//存非分配人待处理的任务id
			List<String> listtradids = new ArrayList<String>();
			//存在非分配人待处理的客户
			List<String> listcustnos = new ArrayList<String>();
			//存在非分配人待处理的处理人
			List<String> listconductors = new ArrayList<String>();
			StringBuilder dealIds = new StringBuilder();
			String[] tradeIdArr = tradeIds.split(",");
			for (String tradeId : tradeIdArr) {
				//根据任务id和处理人查询回访状态 = 待投顾处理/待客服处理 且 处理人 != 空 且 处理人 != 当前选择分配客服
				Map<String,String> param = new HashMap<>();
				param.put("tradeId", tradeId);
				param.put("conductor", conductor);
				List<Map<String,String>> list = cmDoubleTradeService.getNeedHandTradeId(param);
				if(list != null && list.size() > 0){
					listtradids.add(tradeId);
					for(Map<String,String> map :list){
						String mapcustno = map.get("CONSCUSTNO");
						String mapconductor = map.get("CONDUCTOR");
						if(!listcustnos.contains(mapcustno)){
							listcustnos.add(mapcustno);
						}
						if(!listconductors.contains(mapconductor)){
							listconductors.add(mapconductor);
						}
					}
				}else{
					dealIds.append(","+tradeId);
				}
			}
			
			if(StringUtil.isNotNullStr(dealIds.toString())){
				cmDoubleTradeService.assignTask(dealIds.toString().replaceFirst(",", ""), conductor);
			}
			if(listconductors.size() == 0 && StringUtil.isNotNullStr(dealIds.toString())){
				resultMap.put("errorCode", "0000");
			}else{
				ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
				//其他待处理的任务的客户只属于一个处理的人情况
				if(listconductors.size() == 1 ){
					resultMap.put("errorCode", "1111");
					StringBuilder sb = new StringBuilder();
					sb.append("<font color='red'>部分分配成功！</font></br>其中客户号");
					String custnostr = "";
					for(String custno : listcustnos){
						custnostr = ","+custno;
					}
					sb.append(custnostr.replaceFirst(",", ""));
					sb.append("存在新规任务在客服"+consOrgCache.getAllUserMap().get(listconductors.get(0))+"名下，是否确认将列表中勾选的该客户对应任务分配至"+consOrgCache.getAllUserMap().get(listconductors.get(0))+"？");
					resultMap.put("errorMsg", sb.toString());
					resultMap.put("dealTradeids", StringUtils.join(listtradids.toArray(),","));
					resultMap.put("dealConductor", listconductors.get(0));
				}else{
					resultMap.put("errorCode", "2222");
					StringBuilder sb = new StringBuilder();
					sb.append("<font color='red'>部分分配成功！</font></br>其中客户号");
					String custnostr = "";
					for(String custno : listcustnos){
						custnostr = ","+custno;
					}
					sb.append(custnostr.replaceFirst(",", ""));
					sb.append("存在新规任务在客服");
					String ctorstr = "";
					for(String ctor:listconductors){
						ctorstr = ","+consOrgCache.getAllUserMap().get(ctor);
					}
					sb.append(ctorstr.replaceFirst(",", ""));
					sb.append("名下，请单独处理!");
					resultMap.put("errorMsg", sb.toString());
				}
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			resultMap.put("errorCode", "9999");
			resultMap.put("errorMsg", e.getMessage());
		}
		return resultMap;
	}
	
	/**
	 * 分配任务处理人
	 * @param tradeIds 客服回访任务id（如果有多个，则以逗号分隔）
	 * @param conductor 处理人id
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/assignTaskSpec.do")
	public Map<String, Object> assignTaskSpec(String tradeIds, String conductor) {
		Map<String, Object> resultMap = new HashMap<>(2);
		try {
			cmDoubleTradeService.assignTask(tradeIds, conductor);
			resultMap.put("errorCode", "0000");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			resultMap.put("errorCode", "9999");
			resultMap.put("errorMsg", e.getMessage());
		}
		return resultMap;
	}

	/**
	 * 跳转到双录IC页面
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping("/listIcDoubleRecord.do")
	public String listIcDoubleRecord(HttpServletRequest request, HttpServletResponse response,ModelMap model) {
		List<String> roles = (List<String>)request.getSession().getAttribute("loginRoles");
		response.setContentType("text/html; charset=utf-8");
		String menuCode = "070106";
		String operCode = "01";
		boolean hasAuth = false;
		for (String role : roles) {
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menuCode);
			if (temp != null && temp.contains(operCode)) {
				hasAuth = true;
				break;
			}
		}
		model.put("hasAuth", hasAuth);
		model.put("crmFilePath", crmFilePath);
		return "doubletrade/listIcDoubleRecord";
	}

	/**
	 * 跳转到双录审核页面
	 */
	@SuppressWarnings("unchecked")
	@RequestMapping("/listAuditDoubleRecord.do")
	public String listAuditDoubleRecord(HttpServletRequest request, HttpServletResponse response,ModelMap model) {
		System.out.println(System.currentTimeMillis());
		List<String> roles = (List<String>)request.getSession().getAttribute("loginRoles");
		response.setContentType("text/html; charset=utf-8");
		String check = "0";
		String menuCode = "B020604";
		String operCode = "1";
		boolean hasAuth = false;
		for (String role : roles) {
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menuCode);
			if (temp != null && temp.contains(operCode)) {
				hasAuth = true;
				break;
			}
		}

		String menuCode2 = "B020604";
		String operCode2 = "2";
		boolean hasAuth2 = false;
		for (String role : roles) {
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menuCode2);
			if (temp != null && temp.contains(operCode2)) {
				hasAuth2 = true;
				break;
			}
		}
		
		String menuCode3 = "B020604";
		String operCode3 = "4";
		boolean hasAuth3 = false;
		for (String role : roles) {
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menuCode3);
			if (temp != null && temp.contains(operCode3)) {
				hasAuth3 = true;
				break;
			}
		}
		
		String menuCode4 = "B020604";
		String operCode4 = "5";
		boolean hasAuth4 = false;
		for (String role : roles) {
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menuCode4);
			if (temp != null && temp.contains(operCode4)) {
				hasAuth4 = true;
				break;
			}
		}

		if(hasAuth && !hasAuth2){
			check = "1";
		}else if(hasAuth2 && !hasAuth){
			check = "2";
		}else if(hasAuth2 && hasAuth){
			check = "3";
		}
		model.put("hasAuth", check);
		model.put("hasAuth3", hasAuth3);
		model.put("hasAuth4", hasAuth4);
		model.put("crmFilePath", crmFilePath);
		// 无需双录
		String operNotNeedDoubleTrade = "3";
		boolean hasNotNeedDoubleTradeAuth = false;
		for (String role : roles) {
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menuCode);
			if (temp != null && temp.contains(operNotNeedDoubleTrade)) {
				hasNotNeedDoubleTradeAuth = true;
				break;
			}
		}
		model.put("hasNotNeedDoubleTradeAuth", hasNotNeedDoubleTradeAuth);

		System.out.println(System.currentTimeMillis());
		return "doubletrade/listAuditDoubleRecord";
	}

	/**
	 * 跳转到双录RS页面
	 */
	@RequestMapping("/listRsDoubleRecord.do")
	public String listRsDoubleRecord(ModelMap model) {
		model.put("crmFilePath", crmFilePath);
		return "doubletrade/listRsDoubleRecord";
	}

	/**
	 * 加载双录列表数据方法
	 * modified by haoran.zhang  2022-03-16  investorType调整为，产品对应的分销-[投资者类型]。 riskLevel产品对应的分销-[投资者风险等级]
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/listDoubleRecord_json.do")
	public Map<String, Object> listDoubleRecordjson(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String curPage = request.getParameter("page");
		String roleFlag = request.getParameter("roleFlag");
		String startDate = request.getParameter("startDate");
		String endDate = request.getParameter("endDate");
		String handleFlag = request.getParameter("handleFlag");
		//String priority = request.getParameter("priority");
		String custName = request.getParameter("custName");
		String mobile = request.getParameter("mobile");
		String fundName = request.getParameter("fundName");
		String fundCode = request.getParameter("fundCode");
		String hboneno = request.getParameter("hboneno");
		String conscustno = request.getParameter("conscustno");
		String searchFlag = request.getParameter("searchFlag");
		String recState = request.getParameter("recState");
		String orgCode2 = request.getParameter("orgCode");
		String consCode2 = request.getParameter("consCode");
		String syncDateBegin = request.getParameter("syncDateBegin");
		String syncDateEnd = request.getParameter("syncDateEnd");
		String seeLctVisit = request.getParameter("seeLctVisit");
		String idno = request.getParameter("idno");
		String hasAuth = request.getParameter("hasAuth");
		String isopcode = request.getParameter("isopcode");
		String doubleHandleFlag = request.getParameter("doubleHandleFlag");
		String hasVisitStatus = request.getParameter("hasVisitStatus");
		String queryVisitProblem = request.getParameter("query_visitProblem");
		String queryIsLegal = request.getParameter("query_isLegal");
		String conductor = request.getParameter("conductor");

		//排序相关 2024年9月14日 历史逻辑有bug .不支持排序。未赋值 sort
		String sort = request.getParameter("sort");
		String order = request.getParameter("order");


//		NOTICE: 历史设计问题标记： roleFlag== IC | EC  。 标记查询不同的业务数据。sql逻辑其实为：
//		roleFlag== IC --> CM_DOUBLE_TRADE.TASKTYPE  任务单类型：1-双录;
//		roleFlag== EC --> CM_DOUBLE_TRADE.TASKTYPE  任务单类型：2-回访;
//		此处定义 taskType .  业务逻辑 使用方便， 未完全重构
		DoubleTaskTypeEnum taskTypeEnum=null;
		if(IC.equals(roleFlag)){
			taskTypeEnum=DoubleTaskTypeEnum.TASK_DOUBLE_TRADE;
		}
		if(EC.equals(roleFlag)){
			taskTypeEnum=DoubleTaskTypeEnum.TASK_FEED_BACK;
		}

		//预约状态
		String prebookstate = request.getParameter("prebookstate");
		if (StringUtils.isNotBlank(prebookstate)) {
			param.put("prebookstate",prebookstate);
		}else {
			param.put("prebookstate",null );
		}
		//投顾反馈回访日期
		String feedbackStartDate = request.getParameter("feedbackStartDate");
		String feedbackEndDate = request.getParameter("feedbackEndDate");
		if (StringUtils.isNotBlank(feedbackStartDate)) {
			param.put("feedbackStartDate",feedbackStartDate);
		}else {
			param.put("feedbackStartDate",null );
		}
		if (StringUtils.isNotBlank(feedbackEndDate)) {
			param.put("feedbackEndDate",feedbackEndDate);
		}else {
			param.put("feedbackEndDate",null );
		}

		if(StringUtils.isNotBlank(isopcode)){
			param.put("hasAuth", isopcode);
		}else{
			param.put("hasAuth", hasAuth);
		}
		// 增加查看理财通数据参数
		param.put("seeLctVisit", seeLctVisit);



		// 如果列头点了排序，则按所在列排序，否则那默认规则排序
		//【A0165】新规回访任务提醒  回访任务列表，依次按以下两种规则排序；
		//（1）优先1：【上报截止日期】倒序，日期大的在上面；
		//（2）优先2：若（1）一致，则按【数据入库时间】倒序排列。 NOTICE: 历史逻辑在sql中已保证 ：第二排序规则 按照 创建日期 降序排列
		if(DoubleTaskTypeEnum.TASK_FEED_BACK==taskTypeEnum){
			//taTradeDt : 上报截止日期
			param.put("sort", "taTradeDt");
			param.put("order", SORT_DESC);
		}else{
			param.put("sort", StringUtils.isNotBlank(sort)?sort:null);
			param.put("order", StringUtils.isNotBlank(order)?order:null);
		}


		if (StringUtils.isNotBlank(idno)) {
			param.put("idno", DigestUtil.digest(idno.trim()));
		} else {
			param.put("idno", null);
		}

		// 如果查询条件（角色标识）不为空，则增加角色标识查询参数
		if (StringUtils.isNotBlank(roleFlag)) {
			param.put("roleFlag", roleFlag);
		} else {
			param.put("roleFlag", null);
		}
		// 判断是否为指定角色|售后部门负责人  售后负责人  售后作业岗 销售助理主管  总部投顾助理 IC数据分析经理 总部投顾总监
		HttpSession session = request.getSession();
		String userId = (String) session.getAttribute("userId");
		// 设置默认查询条件（双录回访状态）
		if (StringUtils.isNotBlank(searchFlag) && "true".equals(searchFlag)) {

			// 如果查询条件（双录回访状态）不为空，则增加双录回访状态查询参数
			if (StringUtils.isNotBlank(handleFlag)) {
				param.put("handleFlag", handleFlag);
			} else {
				if(StringUtils.isNotBlank(doubleHandleFlag)){
					param.put("handleFlag", doubleHandleFlag);
				}else{
					param.put("handleFlag", null);
				}
			}

			// 如果查询条件（开始日期）不为空，则增加开始日期查询参数
			if (StringUtils.isNotBlank(startDate)) {
				param.put("startDate", startDate);
			} else {
				param.put("startDate", null);
			}

			// 如果查询条件（结束日期）不为空，则增加结束日期查询参数
			if (StringUtils.isNotBlank(endDate)) {
				param.put("endDate", endDate);
			} else {
				param.put("endDate", null);
			}

			// 如果查询条件（入库开始时间）不为空，则增加数据入库开始时间查询参数
			if (StringUtils.isNotBlank(syncDateBegin)) {
				param.put("syncDateBegin", syncDateBegin);
			} else {
				param.put("syncDateBegin", null);
			}

			// 如果查询条件（入库结束时间）不为空，则增加数据入库结束时间查询参数
			if (StringUtils.isNotBlank(syncDateEnd)) {
				param.put("syncDateEnd", syncDateEnd);
			} else {
				param.put("syncDateEnd", null);
			}

			// 如果查询条件（是否需要处理）不为空，则增加是否需要处理查询参数
			if (StringUtils.isNotBlank(recState)) {
				param.put("recState", recState);
			} else {
				param.put("recState", "0");
			}

			// 判断所属投顾字段
			if (StringUtils.isNotBlank(consCode2) && !"ALL".equals(consCode2)) {
				if (consCode2.equals(userId)) {
					// 如果不为空，并且投顾编号等于登录用户，则用登录用户权限模型

					// 判断是否为指定角色|售后部门负责人 售后负责人 售后作业岗 销售助理主管 总部投顾助理 IC数据分析经理
					Map<String, String> roleParam = new ParamUtil(request).getParamMap();
					roleParam.put("consCode", userId);
					List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
					if (CollectionUtils.isNotEmpty(listRoles)) {
						// 总部投顾总监
						param.put("roleLevel", "leader");
					} else {
						// 获取默认登陆用户下属投顾信息
						param.put("roleLevel", "consultant");
						param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
					}
				} else {
					// 如果所属投顾和登录用户不是一个，则直接用所属投顾进行查询
					param.put("roleLevel", "consultant");
                    param.put("consCode", "'" + consCode2 + "'");
				}
			} else if (StringUtils.isNotBlank(orgCode2) && !"0".equals(orgCode2)) {
				// 如果投顾编码为空，所属部门编码不为空，则用所属部门编码查询
				param.put("roleLevel", "consultant");
				param.put("consCode", ObjectUtils.getSubQueryByOrgCode(orgCode2));
			} else {// 获取默认登陆用户下属投顾信息
				Map<String, String> roleParam = new ParamUtil(request).getParamMap();
				roleParam.put("consCode", userId);
				List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
				if (listRoles != null && listRoles.size() > 0) {
					param.put("roleLevel", "leader");
				} else {
					// 获取默认登陆用户下属投顾信息
					param.put("roleLevel", "consultant");
					param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
				}
			}
		} else {

			// 设置默认查询状态
			if(StringUtils.isNotBlank(doubleHandleFlag)){
				param.put("handleFlag", doubleHandleFlag);
			}else{
				if("EC".equals(roleFlag)) {
					param.put("handleFlag", "10");
				}else {
					param.put("handleFlag", "1");
				}
			}

			// 设置默认查询日期
			param.put("startDate", null);
			param.put("endDate", null);
			param.put("recState", "0");

			Map<String, String> roleParam = new ParamUtil(request).getParamMap();
			roleParam.put("consCode", userId);
			List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
			if (listRoles != null && listRoles.size() > 0) {
				param.put("roleLevel", "leader");
			} else {
				// 获取默认登陆用户下属投顾信息
				param.put("roleLevel", "consultant");
				param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
			}
		}

		// 如果查询条件（优先级）不为空，则增加优先级查询参数
		/*if (StringUtils.isNotBlank(priority)) {
			param.put("priority", priority);
		} else {
			param.put("priority", null);
		}*/

		// 如果查询条件（客户名称）不为空，则增加客户名称查询参数
		if (StringUtils.isNotBlank(custName)) {
			param.put("custName", custName);
		} else {
			param.put("custName", null);
		}

		// 如果查询条件（联系方式）不为空，则增加联系方式查询参数
		if (StringUtils.isNotBlank(mobile)) {
			param.put("mobile", DigestUtil.digest(mobile.trim()));
		} else {
			param.put("mobile", null);
		}

		// 如果查询条件（产品名称）不为空，则增加产品名称查询参数
		if (StringUtils.isNotBlank(fundName)) {
			param.put("fundName", fundName);
		} else {
			param.put("fundName", null);
		}

		if (StringUtils.isNotBlank(fundCode)) {
			param.put("fundCode", fundCode);
		} else {
			param.put("fundCode", null);
		}

		if(StringUtils.isNoneBlank(hboneno)){
			param.put("hboneno", hboneno);
		}else{
			param.put("hboneno", null);
		}

		if(StringUtils.isNoneBlank(conscustno)){
			param.put("conscustno", conscustno);
		}else{
			param.put("conscustno", null);
		}

		// 如果查询条件（处理人）不为空，则增加处理人查询参数
		if (StringUtils.isNotBlank(conductor)) {
			param.put("conductor", conductor);
		} else {
			param.put("conductor", null);
		}

		// 如果查询条件（存在问题）不为空，则增加存在问题查询参数
		if (StringUtils.isNotBlank(queryVisitProblem)) {
			param.put("query_visitProblem", queryVisitProblem);
		} else {
			param.put("query_visitProblem", null);
		}
		// 如果查询条件（质检结果）不为空，则增加存在问题查询参数
		if (StringUtils.isNotBlank(queryIsLegal)) {
			param.put("query_isLegal", queryIsLegal);
		} else {
			param.put("query_isLegal", null);
		}

		// 如果查询条件（已访状态）不为空，则增加已访状态查询参数
		if (StringUtils.isNotBlank(hasVisitStatus)) {
			param.put("hasVisitStatus", hasVisitStatus);
		} else {
			param.put("hasVisitStatus", null);
		}

		// 判断常量表中合规标识：true启用，false停用
		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
		boolean roleCpFlag = false;
		if (cacheMap != null && !cacheMap.isEmpty()) {
			roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
		}

		// 判断登录人员的角色中是否包括“合规人员”角色
		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
		if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
			param.put("hascp", "true");
		}
		if("undefined".equals(param.get("havingRetail"))){
			param.remove("havingRetail");
		}
		// 通过Session获取产品广度信息
		String topcpdata = (String) session.getAttribute("topcpdata");
		param.put("topcpdata", topcpdata);

		//2023年3月3日  新增前端查询属性： existedVisit：0-不存在回访记录
		param.put("existedVisit",request.getParameter("existedVisit") );
		//2023年3月3日  新增前端查询属性： feedBackType：回访类型
		param.put("feedBackType",request.getParameter("feedBackType") );
		//2024年6月21日 新增前端查询属性： doubleType: 双录方式
        param.put("doubleType",request.getParameter("doubleType") );
		//2024年9月13日 新增前端查询属性：visitForceFlag：上报强控回访
		param.put("visitForceFlag",request.getParameter("visitForceFlag") );
		PageData<CmDoubleTrade> pData = cmDoubleTradeService.listCmDoubleTradeByPage(param);
		log.info("查询双录页面数据，查询参数vo:{}",JSON.toJSONString(param));
		if(taskTypeEnum!=null){
			cmDoubleTradeService.setVisitQuality(pData.getListData(),taskTypeEnum.getCode() );
		}
		// 对列表数据字段进行转义
		//2024年9月14日 17:42:08   for循环遍历中，获取远端redis大Map .性能问题 fix . 不允许for-each中 getMap.
		ConsOrgCache orgcache = ConsOrgCache.getInstance();
		Map<String, String> upOrgMap= orgcache.getUpOrgMapCache();
		Map<String, String> allOrgMap=orgcache.getAllOrgMap();
		Map<String, String> allUserMap=orgcache.getAllUserMap();

		for (CmDoubleTrade cmDoubleTrade : pData.getListData()) {



			// 根据权限模型对购买人电话进行加密
			if (StringUtils.isNotBlank(cmDoubleTrade.getMobileCipher())) {
				cmDoubleTrade.setMobile(cmDoubleTrade.getMobileCipher());
				CodecSingleResponse codecSingleResponse = decryptSingleFacade.decrypt(cmDoubleTrade.getMobileCipher());
				if (codecSingleResponse.getCodecText() != null) {
					cmDoubleTrade.setMobile2(cmDoubleTrade.getMobileMask());
				}
			}
			String conscustNo = cmDoubleTrade.getConscustNo();
			Map<String, Object> params = new HashMap<>();
			params.put("conscustno", conscustNo);
			CmRealConsultant cmRealConsultant = cmRealConsultantService.getCmRealConsultant(params);
			if (null != cmRealConsultant && !cmRealConsultant.getRealConscode().equals(cmDoubleTrade.getConsCode())) {
				cmDoubleTrade.setIsrealconscode("0");
			}

			String uporgcode = upOrgMap.get(cmDoubleTrade.getOrgCode());
			if("0".equals(uporgcode)){
				cmDoubleTrade.setUpOrgName(cmDoubleTrade.getOrgName());
			}else{
				cmDoubleTrade.setUpOrgName(allOrgMap.get(uporgcode));
			}

			// 根据权限模型对购买人电话进行加密
			if (cmDoubleTrade.getPremiumAmount() != null) {
				cmDoubleTrade.setPremiumAmount(cmDoubleTrade.getPremiumAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
			}

			// 根据权限模型对联系人电话进行加密
			if (StringUtils.isNotBlank(cmDoubleTrade.getLinkMobileCipher())) {
				cmDoubleTrade.setLinkMobile(cmDoubleTrade.getLinkMobileCipher());
				CodecSingleResponse codecSingleResponse = decryptSingleFacade.decrypt(cmDoubleTrade.getLinkMobileCipher());
				if (codecSingleResponse.getCodecText() != null) {
					cmDoubleTrade.setLinkMobile2(cmDoubleTrade.getLinkMobileMask());
				}
			}

			if (StringUtils.isNotBlank(cmDoubleTrade.getChecker()) && !"sys".equals(cmDoubleTrade.getChecker())) {
				cmDoubleTrade.setChecker(allUserMap.get(cmDoubleTrade.getChecker()));
			}

			// 获取处理人名称
			String conductorCode = cmDoubleTrade.getConductor();
			if (StringUtils.isNotBlank(conductorCode)) {
				cmDoubleTrade.setConductorName(allUserMap.get(conductorCode));
			}

			// 存在问题
			String visitProblemCode = cmDoubleTrade.getVisitProblem();
			if (StringUtils.isNotBlank(visitProblemCode)) {
				cmDoubleTrade.setVisitProblemVal(ConstantCache.getInstance().getVal("visitProblem", visitProblemCode));
			}

			// 已访状态
			String hasVisitStatusCode = cmDoubleTrade.getHasVisitStatus();
			if (StringUtils.isNotBlank(hasVisitStatus)) {
				cmDoubleTrade.setHasVisitStatusVal(ConstantCache.getInstance().getVal("hasVisitStatus", hasVisitStatusCode));
			}

            //addWechatAccount 是否添加企微且绑定  1-是 0-否
			CmCustBindRelationVO cmCustBindRelationVO = cmWechatOuterService.queryBindRelation(cmDoubleTrade.getHboneNo(), cmDoubleTrade.getConsCode());
            cmDoubleTrade.setAddWechatAccount(cmCustBindRelationVO.getIsAddQiWeiAndBind());

			try {
				DisChannelCodeEnum disChannelCodeEnum=jjxxInfoService.getDisCodeEnumByJjdm(cmDoubleTrade.getFundCode());
				log.info("根据产品代码：{} 获取分销渠道：{}",cmDoubleTrade.getFundCode(),disChannelCodeEnum);
				cmDoubleTrade.setDisChannelCode(disChannelCodeEnum.getCode());
			} catch (Exception e) {
				log.error("jjxxInfoService.getDisCodeEnumByJjdm 接口出现异常!", e);
			}
		}
		Map<String, Object> resultMap = new HashMap<String, Object>(3);
		resultMap.put("total", pData.getPageBean().getTotalNum());
		resultMap.put("page", curPage);
		resultMap.put("rows", pData.getListData());
		return resultMap;
	}

	@RequestMapping("/newOrdVisit")
	public String newOrdVisit(HttpServletRequest request, String id, String conscustno, Map map){
		Map param = new HashMap(1);
		param.put("id", id);
		CmDoubleTrade cmDoubleTrade = cmDoubleTradeService.getCmDoubleTrade(param);
		map.put("cmDoubleTrade", cmDoubleTrade);
		QueryConscustInfoRequest queryConscustInfoRequest = new QueryConscustInfoRequest();
		queryConscustInfoRequest.setConscustno(conscustno);
		QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);
		ConscustInfoDomain conscustInfoDomain = queryConscustInfoResponse.getConscustinfo();
		conscustInfoDomain.setIdtype(ConstantCache.getInstance().getVal("idtype", conscustInfoDomain.getIdtype()));
		map.put("conscust", conscustInfoDomain);
		// 格式化打款日期
		String pmtDt = cmDoubleTrade.getPmtDt();
		if (null != pmtDt) {
			cmDoubleTrade.setPmtDt(pmtDt.substring(0, 8));
		}
		// 根据权限模型对购买人电话进行加密
		if (StringUtils.isNotBlank(conscustInfoDomain.getMobileCipher())) {
			cmDoubleTrade.setMobile(conscustInfoDomain.getMobileCipher());
			CodecSingleResponse codecSingleResponse = decryptSingleFacade.decrypt(conscustInfoDomain.getMobileCipher());
			if (codecSingleResponse.getCodecText() != null) {
				cmDoubleTrade.setMobile2(conscustInfoDomain.getMobileMask());
			}
		}

		// 根据权限模型对联系人电话进行加密
		if (StringUtils.isNotBlank(conscustInfoDomain.getLinkmobileCipher())) {
			cmDoubleTrade.setLinkMobile(conscustInfoDomain.getLinkmobileCipher());
			CodecSingleResponse codecSingleResponse = decryptSingleFacade.decrypt(conscustInfoDomain.getLinkmobileCipher());
			if(codecSingleResponse.getCodecText() != null) {
				cmDoubleTrade.setLinkMobile2(conscustInfoDomain.getLinkmobileMask());
			}
		}
		if(conscustInfoDomain.getIdnoCipher() != null) {
			CodecSingleResponse codecSingleResponse = decryptSingleFacade.decrypt(conscustInfoDomain.getIdnoCipher());
			if (codecSingleResponse.getCodecText() != null) {
				cmDoubleTrade.setIdNo(codecSingleResponse.getCodecText());
			}
		}
		CmVisitRecord cmVisitRecord = cmVisitRecordMapper.getRecordByTradeId(cmDoubleTrade.getId());
		map.put("visitProblem", cmVisitRecord == null ? null : cmVisitRecord.getVisitProblem());
		map.put("recordId", cmVisitRecord == null ? null : cmVisitRecord.getId());
		//为了容错提前2秒
		map.put("startTime", DateTimeUtil.fmtDate(DateUtils.addSeconds(new Date(), -2), "yyyyMMddHHmmss"));
		log.info((String) map.get("startTime"));

		DisChannelCodeEnum disChannelCodeEnum=jjxxInfoService.getDisCodeEnumByJjdm(cmDoubleTrade.getFundCode());
		log.info("根据产品代码：{} 获取分销渠道：{}",cmDoubleTrade.getFundCode(),disChannelCodeEnum);
		map.put("disChannelCode", disChannelCodeEnum==null?"":disChannelCodeEnum.getCode());
		return "/doubletrade/newOrdVisit";
	}

	@RequestMapping("/saveVisitRecord.do")
	@ResponseBody
	public Object saveVisitRecord(HttpServletRequest request,String id,String conscustno,String visitType,String visitClassify,String visitResultDt
			,String visitResult,String visitProblem,String feedBackType,String commRecord,String remark,String orgName
			,String consName,String tid,String contractNo,String startTime, String fundName){
		CmVisitRecord cmVisitRecord;
		String hasVisitStatus = null;
		try {
			// id 回访记录id；tid：回访任务id
			if(StringUtil.isNullStr(id)){
				cmVisitRecord = new CmVisitRecord();
				cmVisitRecord.setTradeId(tid);
				cmVisitRecord.setConscustno(conscustno);
				cmVisitRecord.setVisitDt(new Date());
				hasVisitStatus = StaticVar.HAS_VISIT_STATUS_FIRST_VISIT;
			}else {
				cmVisitRecord = cmVisitRecordMapper.getRecordByTradeId(tid);
				if(cmVisitRecord == null){
					return "参数错误";
				}
				if(cmVisitRecord.getSecondDt() == null){
					cmVisitRecord.setSecondDt(new Date());
					hasVisitStatus = StaticVar.HAS_VISIT_STATUS_SECOND_VISIT;
				} else if(cmVisitRecord.getThirdDt() == null){
					cmVisitRecord.setThirdDt(new Date());
					hasVisitStatus = StaticVar.HAS_VISIT_STATUS_THIRD_VISIT;
				}
			}
			cmVisitRecord.setVisitResult(visitResult);
			cmVisitRecord.setVisitProblem(visitProblem);
			cmVisitRecord.setFeedBackType(feedBackType);
			cmVisitRecord.setCommRecord(commRecord);
			cmVisitRecord.setRemark(remark);
			if(StaticVar.VISIT_RESULT_COMPLET.equals(visitResult)) {
				cmVisitRecord.setVisitResultDt(visitResultDt);
			}else {
				cmVisitRecord.setVisitResultDt(null);
			}
			cmVisitRecord.setConsName(consName);
			cmVisitRecord.setOrgName(orgName);
			String userId = (String) request.getSession().getAttribute("userId");
			cmVisitRecord.setVisitMan(userId);
			User loginUser=(User)request.getSession().getAttribute("loginUser");
			//保存
			log.info("CmDoubleRecordController.saveVisitRecord:"+JSON.toJSONString(cmVisitRecord));
			insertVisitRecord(tid,visitType,visitClassify, userId, cmVisitRecord, id, loginUser.getUserName(), startTime, hasVisitStatus, fundName);
			//客服备注
			CmDoubleRemark cmDoubleRemark = new CmDoubleRemark();
			cmDoubleRemark.setCreator(userId);
			cmDoubleRemark.setTid(tid);
			cmDoubleRemark.setDeptFlag("EC");
			cmDoubleRemark.setCreDt(DateTimeUtil.getCurDateTime());
			cmDoubleRemark.setRemark(commRecord);
			cmDoubleRemarkService.insertCmDoubleRemark(cmDoubleRemark);
			//日志
			cmOptLogService.insertLog(userId, StaticVar.OPT_VISIT_RECORD, cmVisitRecord, contractNo);
		}catch (Exception e){
			log.error(e.getMessage(), e);
			throw new RuntimeException();
		}
		return "success";
	}

	private void insertVisitRecord(String tid,String visitType,String visitClassify,String userId,CmVisitRecord cmVisitRecord
			, String id, String userName, String startTime, String hasVisitStatus, String fundName){
		//沟通记录
		CsCommunicateVisit csCommunicateVisit = new CsCommunicateVisit();
		String communicateVisitId = commonService.getSeqValue("SEQ_CS_COMMUNICATE_VISIT_ID").toString();
		String hisId = commonService.getSeqValue("SEQ_PCUSTREC");
		csCommunicateVisit.setId(communicateVisitId);
		csCommunicateVisit.setVisitType(visitType);
		csCommunicateVisit.setVisitClassify(visitClassify);
		csCommunicateVisit.setModifyFlag("1");
		csCommunicateVisit.setCreator(userId);
		csCommunicateVisit.setConscustNo(cmVisitRecord.getConscustno());
		csCommunicateVisit.setHisId(hisId);
		csCommunicateVisit.setHisFlag("0");
		csCommunicateVisit.setCommContent(cmVisitRecord.getCommRecord());
		log.info("CmDoubleRecordController.insertVisitRecord.csCommunicateVisit:"+JSON.toJSONString(csCommunicateVisit));
		csCommunicateVisitService.insertCsCommunicateVisit(csCommunicateVisit);

		//保存回访记录
		if(StringUtil.isNullStr(id)){
			cmVisitRecord.setCommRecordIds(communicateVisitId);
			cmVisitRecordMapper.insert(cmVisitRecord);
		}else {
			String recordIds = cmVisitRecord.getCommRecordIds() + "," + communicateVisitId;
			if(recordIds.length() <= 1000) {
				cmVisitRecord.setCommRecordIds(recordIds);
			}
			cmVisitRecordMapper.update(cmVisitRecord);
		}
		log.info("保存记录成功 tid:" + tid);

		/**
		 * 若存在问题 = 3次及以上未接听/不配合回访/手机号码错误（非本人、空号）/已发邮件未回复/对产品有疑义时
		 * 且 该新规任务的回访状态 = 待客服处理，则同步更新相应新规任务的回访状态为“待投顾处理
		 */
		Map<String, String> paramMap = new HashMap<>(1);
		paramMap.put("id", tid);
		CmDoubleTrade cmDoubleTrade = cmDoubleTradeService.getCmDoubleTrade(paramMap);
		log.info("tid为{}时的数据 {}", tid, JSON.toJSONString(cmDoubleTrade));
		// 存在问题
		String visitProblem = cmVisitRecord.getVisitProblem(); 
		boolean needUpdateHandleFlag = ("1".equals(visitProblem) || "2".equals(visitProblem) || "3".equals(visitProblem) || "4".equals(visitProblem) || "6".equals(visitProblem))
				&& StaticVar.HANDLE_FLAG_WAIT_DEAL.equals(cmDoubleTrade.getHandleFlag());
		log.info("是否需要修改handleFlag{}", needUpdateHandleFlag);
		CmDoubleTrade updateCmDoubleTrade = new CmDoubleTrade();
		updateCmDoubleTrade.setId(tid);
		// 已访状态：0 未回访;1 已一访;2 已二访;3 已三访
		updateCmDoubleTrade.setHasVisitStatus(hasVisitStatus); 
		if (needUpdateHandleFlag) {
			// 新规任务的回访状态
			updateCmDoubleTrade.setHandleFlag(StaticVar.HANDLE_FLAG_WAIT_CONSULT_DEAL); 

			log.info("cmDoubleTradeService.updateCmDoubleTrade即将调用");
			cmDoubleTradeService.updateCmDoubleTrade(updateCmDoubleTrade);
			log.info("cmDoubleTradeService.updateCmDoubleTrade调用成功");

			cmDoubleTradeService.sendHandleFlagMessage(tid);
		} else {
			log.info("cmDoubleTradeService.updateCmDoubleTrade即将调用");
			cmDoubleTradeService.updateCmDoubleTrade(updateCmDoubleTrade);
			log.info("cmDoubleTradeService.updateCmDoubleTrade调用成功");
		}

		//查询最近的一次拨号记录
		String orderId = csCalloutRecService.getLastRecByTidAndTime(tid, startTime);
		log.info(startTime);
		if(orderId != null) {
			//更新拨号记录以便客户信息沟通记录列表能查到录音
			CsCalloutRec csCalloutRec = new CsCalloutRec();
			csCalloutRec.setAppSerialNo(hisId);
			csCalloutRec.setId(orderId);
			csCalloutRecService.updateRecAppSerialNo(csCalloutRec);

			//回访结果是完成上传录音文件
			if (StaticVar.VISIT_RESULT_COMPLET.equals(cmVisitRecord.getVisitResult())) {
				uploadRecFile(orderId, userId, tid, userName, cmVisitRecord.getConscustno(), fundName);
			}
		}
	}

	public void uploadRecFile(String orderId, String userId, String tid, String userName, String conscustno, String fundName){
		long start = System.currentTimeMillis();
		VisitRecUploadExecutors.getExecutors().execute(() -> {
			Map<String,String> paramMap = new HashMap(3);
			paramMap.put("orderId", orderId);
			int uploadWaitMinute = UPLOAD_WAIT_MINUTE * 60 * 1000;
			int i = 0;
			//循环查录音是否生成
			while (true) {
				i++;
				//非第一次且超时
				if(System.currentTimeMillis() - start > uploadWaitMinute && i > 1){
					//超时未查到发送消息提醒
					pushFailMsg(conscustno, fundName, userId);
					break;
				}
				/***********调取录音文件********/
				Map<String, List<PhoneCallRecord>> resultMap = teleSaleCountStatisticsService.getStaffCallCountStatisticsFields(paramMap);
				log.info("TID:" + tid + " orderId:" + orderId + " resultMap:" + (resultMap == null ? "" : JSON.toJSONString(resultMap)));

				HFileService instance = HFileService.getInstance();
				// 呼出
				List<PhoneCallRecord> list1 = resultMap.get("list1");
				if (CollectionUtils.isNotEmpty(list1)) {
					log.info("TID:" + tid + "查到耗时" + (System.currentTimeMillis() - start));
					PhoneCallRecord entity = list1.get(0);
					try {

						String fileId =entity.getFileName();
						String fileName = getStoreFileName(tid, ".wav");
						String remoteUrl = entity.getFileUrl();
						DoubleFileUpdateVo updateVo = new DoubleFileUpdateVo();
						updateVo.setFileId(fileId);
						updateVo.setFileName(fileName);
//						updateVo.setUploadType("0");
						updateVo.setTradeId(tid);
						updateVo.setRemoteUrl(remoteUrl);
						updateVo.setStoreFileConfig(DfileConstants.DOUBLE_TRADE_STORE_CONFIG);
						ReturnMessageDto<String> updateResp=uploadFileByRemoteUrl(updateVo);

						//保存数据
						CmDoubleFile cmDoubleFile = new CmDoubleFile();
						// 往文件表插入记录
						cmDoubleFile.setFileId(fileId);
						cmDoubleFile.setFilePath(remoteUrl);
						cmDoubleFile.setFileName(fileName);
						cmDoubleFile.setRecStat("0");
						cmDoubleFile.setUploader(userName);
						cmDoubleFile.setUploadDt(DateTimeUtil.getCurDateTime());

						// 如果文件上传成功，则往文件表和关系表插入数据
						//上传状态：0上传成功；1上传失败；
						String uploadType  =updateResp.isSuccess()?"0":"1";
						cmDoubleFile.setUploadType(uploadType);
						// 关联订单和文件表记录
						CmDoubleTradeRFile cmDoubleTradeRfile = new CmDoubleTradeRFile();
						cmDoubleTradeRfile.setTid(tid);
						cmDoubleTradeRfile.setFileId(entity.getFileName());
						cmDoubleTradeRfile.setRecStat("0");
						cmDoubleTradeRfile.setCreator(userId);
						cmDoubleTradeRfile.setCreDt(DateTimeUtil.getCurDateTime());
						cmDoubleFileService.insertCmDoubleFileAndRFFile(cmDoubleFile, cmDoubleTradeRfile);

						//上传成功更新任务状态为已处理
						if (updateResp.isSuccess()) {
							CmDoubleTrade cmDoubleTrade = new CmDoubleTrade();
							cmDoubleTrade.setId(tid);
							cmDoubleTrade.setHandleFlag(DoubeHandleFlagEnum.AUDIT_PASS.getCode());
							cmDoubleTradeService.updateCmDoubleTrade(cmDoubleTrade);
							try {
								//同步中台
								syncStatusToZt(tid);
							} catch (Exception e) {
								log.error("同步中台失败", e);
								log.error(e.getMessage(), e);
							}
						}
					} catch (Exception e) {
						log.error(e.getMessage(), Throwables.getStackTraceAsString(e));
					}
					break;
				}else {
					try {
						//每次间隔半分钟再查
						Thread.sleep(29900);
					} catch (InterruptedException e) {
						log.error(e.getMessage(), e);
					}
				}
			}
		});
	}

	private void pushFailMsg(String conscustno, String fundName, String userId){
		QueryConscustInfoRequest queryConscustInfoRequest = new QueryConscustInfoRequest();
		queryConscustInfoRequest.setConscustno(conscustno);
		QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);
		if(queryConscustInfoResponse.getConscustinfo() != null) {
			CmPushMsgRequest request = new CmPushMsgRequest();
			request.setBusinessId("200581");
			//1、PC端；2、微信
			request.setPushChannel("1");
			//1、一账通号；2、投顾号
			request.setAccountType("2");
			request.setAccount(userId);
			Map paramMap = new HashMap();
			paramMap.put("custname", queryConscustInfoResponse.getConscustinfo().getCustname());
			paramMap.put("pname", fundName);
			request.setParamJson(JSON.toJSONString(paramMap));
			BaseResponse response = cmPushMsgService.pushMsg(request);
			if (response != null && BaseConstantEnum.SUCCESS.getCode().equals(response.getReturnCode())) {
				log.info("发送新规上传超时提醒成功:" + JSON.toJSONString(request));
			} else {
				log.info("发送新规上传超时提醒失败，失败原因:{}", response == null ? "返回值为null" : response.getDescription());
			}
		}
	}



	private String getStoreFileName(String tid, String extName){
		String fileName;
		CmPrebookproductinfo cmPrebookproductinfo = cmDoubleTradeService.getPreBookByTradeId(tid);
		if (cmPrebookproductinfo == null) {
			Map map = new HashMap(2);
			map.put("id", tid);
			fileName = cmDoubleTradeService.getCmDoubleTrade(map).getHboneNo();
		} else {
			//双录日期时间到秒_投顾客户号 _客户姓名_  购买产品代码 _   预约ID _  随机数 2位
			fileName = DateTimeUtil.getCurDateTime() + "_" + cmPrebookproductinfo.getConscustno() + "_" + cmPrebookproductinfo.getConscustname() + "_"
					+ cmPrebookproductinfo.getPcode() + "_" + cmPrebookproductinfo.getId();
		}
		fileName += "_" + (new Random().nextInt(90) + 10) + extName;
		return fileName;
	}

	private void syncStatusToZt(String tid){
		Map param = new HashMap(2);
		param.put("id", tid);
		CmDoubleTrade cmDoubleTrade = cmDoubleTradeService.getCmDoubleTrade(param);
		if(cmDoubleTrade != null) {
			log.info(tid + "同步中台");
			DoubleTradeInfoDomain syncDoubleTrade = new DoubleTradeInfoDomain();
			syncDoubleTrade.setId(tid);
			syncDoubleTrade.setTxAcctNo(cmDoubleTrade.getPubcustNo());
			syncDoubleTrade.setContractNo(cmDoubleTrade.getContractNo());
			syncDoubleTrade.setFundCode(cmDoubleTrade.getFundCode());
			syncDoubleTrade.setSystemFlag(cmDoubleTrade.getSystemFlag());
			syncDoubleTrade.setTaskType(cmDoubleTrade.getTaskType());
			List<DoubleTradeInfoDomain> syncList = new ArrayList<DoubleTradeInfoDomain>();
			syncList.add(syncDoubleTrade);
			PushCmDoubleDataUtil c = new PushCmDoubleDataUtil();
			c.setSystemFlag("ZT");
			c.setSyncList(syncList);
			// 异步同步双录状态给ZT
			Thread syncThread = new Thread(c);
			syncThread.start();
		}
	}

	/**
	 * 加载零售双录列表数据方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/listRsDoubleRecord_json.do")
	public Map<String, Object> listRsDoubleRecordJson(HttpServletRequest request,
													   HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String curPage = request.getParameter("page");
		String tradeStartDate = request.getParameter("tradeStartDate");
		String tradeEndDate = request.getParameter("tradeEndDate");
		String uploadStartDate = request.getParameter("uploadStartDate");
		String uploadEndDate = request.getParameter("uploadEndDate");
		String handleFlag = request.getParameter("handleFlag");
		String uploader = request.getParameter("uploader");
		String searchFlag = request.getParameter("searchFlag");
		String handleCount = request.getParameter("handleCount");

		// 设置默认查询条件（双录回访状态）
		if (StringUtils.isNotBlank(searchFlag) && "true".equals(searchFlag)) {
			// 如果查询条件（结束日期）不为空，则增加结束日期查询参数
			if (StringUtils.isNotBlank(tradeEndDate)) {
				param.put("tradeEndDate", tradeEndDate);
			} else {
				param.put("tradeEndDate", null);
			}

			// 如果查询条件（优先级）不为空，则增加优先级查询参数
			if (StringUtils.isNotBlank(uploadStartDate)) {
				param.put("uploadStartDate", uploadStartDate);
			} else {
				param.put("uploadStartDate", null);
			}

			// 如果查询条件（双录回访状态）不为空，则增加双录回访状态查询参数
			if (StringUtils.isNotBlank(handleFlag)) {
				param.put("handleFlag", handleFlag);
			} else {
				param.put("handleFlag", null);
			}

			// 如果查询条件（开始日期）不为空，则增加开始日期查询参数
			if (StringUtils.isNotBlank(tradeStartDate)) {
				param.put("tradeStartDate", tradeStartDate);
			} else {
				param.put("tradeStartDate", null);
			}

			// 如果查询条件（优先级）不为空，则增加优先级查询参数
			if (StringUtils.isNotBlank(uploadEndDate)) {
				param.put("uploadEndDate", uploadEndDate);
			} else {
				param.put("uploadEndDate", null);
			}
		} else {
			// 设置默认查询状态
			param.put("handleFlag", "1");

			// 设置默认查询日期
			Map<String, String> startAndEndDayMap = DateTimeUtil.getFiveDayStartAndEnd();
			param.put("tradeStartDate", startAndEndDayMap.get("beginDate"));
			param.put("tradeEndDate", startAndEndDayMap.get("endDate"));
			param.put("uploadStartDate", startAndEndDayMap.get("beginDate"));
			param.put("uploadEndDate", startAndEndDayMap.get("endDate"));
		}

		// 如果查询条件（联系方式）不为空，则增加联系方式查询参数
		if (StringUtils.isNotBlank(uploader)) {
			param.put("uploader", uploader);
		} else {
			param.put("uploader", null);
		}

		// 如果查询条件（已访状态）不为空，则增加已访状态查询参数
		if (StringUtils.isNotBlank(handleCount)) {
			param.put("handleCount", handleCount);
		} else {
			param.put("handleCount", null);
		}

		// 判断常量表中合规标识：true启用，false停用
		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
		boolean roleCpFlag = false;
		if (cacheMap != null && !cacheMap.isEmpty()) {
			roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
		}

		// 判断登录人员的角色中是否包括“合规人员”角色
		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
		if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
			param.put("hascp", "true");
		}
		PageData<CmDoubleTradeRs> pData = cmDoubleTradeRsService.listCmDoubleTradeRsByPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>(3);
		resultMap.put("total", pData.getPageBean().getTotalNum());
		resultMap.put("page", curPage);
		List<CmDoubleTradeRs> list = pData.getListData();
		for (CmDoubleTradeRs rs : list) {
			if (StringUtil.isNotNullStr(rs.getMobileCipher())) {
				rs.setMobile(decryptSingleFacade.decrypt(rs.getMobileCipher()).getCodecText());
			}
			try {
				DisChannelCodeEnum disChannelCodeEnum = jjxxInfoService.getDisCodeEnumByJjdm(rs.getFundCode());
				log.info("根据产品代码：{} 获取分销渠道：{}", rs.getFundCode(), disChannelCodeEnum);
				// 通过调用接口获取投资者类型
				String hboneNo = rs.getHboneNo();
				if (StringUtils.isNotBlank(hboneNo)) {
					ReturnMessageDto<KycInfoResponse> kycInfoDto = acctRelatedService.queryKycInfoByInvestType(hboneNo,
							disChannelCodeEnum,
							// 投资者类型为：1-个人
							CrmCustInvestTypeEnum.getEnum("1"));
					if (kycInfoDto.isSuccess() && kycInfoDto.getReturnObject() != null) {
						KycInfoResponse kycInfoResponse = kycInfoDto.getReturnObject();
						rs.setRiskLevel(kycInfoResponse.getRiskToleranceLevel());
					}
				}
			} catch (Exception e) {
				log.error("queryKycInfoService接口出现异常", e.getMessage());
			}
		}
		resultMap.put("rows", list);
		return resultMap;
	}

	/**
	 * 修改交易记录状态
	 * @param request
	 * @param response
	 * @return String
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/modifyStatus.do")
	public String modifyStatus(HttpServletRequest request, HttpServletResponse response) {
		// 保存
		String result;
		String tid = request.getParameter("tid");
		String pubcustNo = request.getParameter("pubcustNo");
		String contractNo = request.getParameter("contractNo");
		String handleFlag = request.getParameter("handleFlag");
		String fundCode = request.getParameter("fundCode");
		String systemFlag = request.getParameter("systemFlag");
		String taskType = request.getParameter("taskType");
		String urge = request.getParameter("urge");
		String urgeReason = request.getParameter("urgeReason");

		try {
			//默认为客服回访，如果是IC进来的，则状态应该为已处理待审核
			String checkflag = "2";
			if("1".equals(taskType)){
				checkflag = "3";
			}

			// 如果状态改为已处理待审核，则先判断是否已上传录音文件
			if (checkflag.equals(handleFlag)) {
				boolean isUploaded = isUploadedFile(tid);
				if (!isUploaded) {
					result = "noUploadedFile";
					return result;
				}
			}

			// 获取登陆用户信息
			HttpSession session = request.getSession();
			String userId = (String) session.getAttribute("userId");
			CmDoubleTrade cmDoubleTrade = new CmDoubleTrade();
			cmDoubleTrade.setUrge(urge);
			cmDoubleTrade.setUrgeReason(urgeReason);
			cmDoubleTrade.setId(tid);
			cmDoubleTrade.setHandleFlag(handleFlag);
			cmDoubleTrade.setHandleDt(new Date());
			cmDoubleTrade.setModifier(userId);
			cmDoubleTrade.setModDt(new Date());
			// 更新表中数据
			cmDoubleTradeService.updateCmDoubleTrade(cmDoubleTrade);
			// 如果处理状态变为“已处理”，则同步该条记录状态到中台
			if ("2".equals(handleFlag)) {
				if (StringUtils.isNotBlank(pubcustNo)) {
					DoubleTradeInfoDomain syncDoubleTrade = new DoubleTradeInfoDomain();
					syncDoubleTrade.setId(tid);
					syncDoubleTrade.setTxAcctNo(pubcustNo);
					syncDoubleTrade.setContractNo(contractNo);
					syncDoubleTrade.setFundCode(fundCode);
					syncDoubleTrade.setSystemFlag(systemFlag);
					syncDoubleTrade.setTaskType(taskType);
					List<DoubleTradeInfoDomain> syncList = new ArrayList<DoubleTradeInfoDomain>();
					syncList.add(syncDoubleTrade);
					PushCmDoubleDataUtil c = new PushCmDoubleDataUtil();
					c.setSystemFlag("ZT");
					c.setSyncList(syncList);
					// 异步同步双录状态给ZT
					Thread syncThread = new Thread(c);
					syncThread.start();
				}
			}
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 修改交易记录状态
	 * @param request
	 * @param response
	 * @return String
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/modifyAuditStatus.do")
	public String modifyAuditStatus(HttpServletRequest request, HttpServletResponse response) {
		// 保存
		String result;
		String tid = request.getParameter("tid");
		String audit = request.getParameter("audit");
		String handleFlag = request.getParameter("handleFlag");
		String pubcustNo = request.getParameter("pubcustNo");
		String contractNo = request.getParameter("contractNo");
		String fundCode = request.getParameter("fundCode");
		String systemFlag = request.getParameter("systemFlag");
		String taskType = request.getParameter("taskType");

		try {
			// 获取登陆用户信息
			HttpSession session = request.getSession();
			String userId = (String) session.getAttribute("userId");
			CmDoubleTrade cmDoubleTrade = new CmDoubleTrade();
			cmDoubleTrade.setAuditmind(audit);
			cmDoubleTrade.setId(tid);
			cmDoubleTrade.setHandleFlag(handleFlag);
			cmDoubleTrade.setHandleDt(new Date());
			cmDoubleTrade.setModifier(userId);
			cmDoubleTrade.setModDt(new Date());
			cmDoubleTrade.setChecker(userId);
			cmDoubleTrade.setCheckdt(new Date());

			// 更新表中数据
			cmDoubleTradeService.updateCmDoubleTrade(cmDoubleTrade);
			//插入审核日志
			CmDoubleTradeChecklog cmDoubleTradeChecklog = new CmDoubleTradeChecklog();
			cmDoubleTradeChecklog.setCheckdt(new Date());
			cmDoubleTradeChecklog.setChecker(userId);
			cmDoubleTradeChecklog.setAuditmind(audit);
			cmDoubleTradeChecklog.setHandleflag(handleFlag);
			cmDoubleTradeChecklog.setTradeid(tid);
			cmDoubleTradeChecklogService.insertCmDoubleTradeChecklog(cmDoubleTradeChecklog);
			
			// 如果处理状态变为“已处理”，则同步该条记录状态到中台
			if ("2".equals(handleFlag)) {
				if(StaticVar.YES.equals(cmDoubleTrade.getIsFixed())){
					updateFixedDoubleTrade(cmDoubleTrade.getContractNo(), userId, tid, systemFlag);
				}else if (StringUtils.isNotBlank(pubcustNo)) {
					DoubleTradeInfoDomain syncDoubleTrade = new DoubleTradeInfoDomain();
					syncDoubleTrade.setId(tid);
					syncDoubleTrade.setTxAcctNo(pubcustNo);
					syncDoubleTrade.setContractNo(contractNo);
					syncDoubleTrade.setFundCode(fundCode);
					syncDoubleTrade.setSystemFlag(systemFlag);
					syncDoubleTrade.setTaskType(taskType);
					List<DoubleTradeInfoDomain> syncList = new ArrayList<DoubleTradeInfoDomain>();
					syncList.add(syncDoubleTrade);
					PushCmDoubleDataUtil c = new PushCmDoubleDataUtil();
					c.setSystemFlag("ZT");
					c.setSyncList(syncList);
					// 异步同步双录状态给ZT
					Thread syncThread = new Thread(c);
					syncThread.start();
				}
			}else{
				QueryRemindRequest queryRemindRequest = new QueryRemindRequest();
				queryRemindRequest.setId(tid);
				queryRemindRequest.setRemindType(RemindEnum.MSG_TYPE_SLTH.getCode());
				remindWithdrawMessageService.withdrawMessage(queryRemindRequest);
			}
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 意向单审核更新并同步首笔预约的双录单
	 * @param planId
	 * @param userId
	 * @param tid
	 * @param systemFlag
	 */
	private void updateFixedDoubleTrade(String planId, String userId, String tid, String systemFlag){
		Map<String, String> param = new HashMap<>();
		param.put("planId", planId);
		param.put("HandleFlag", "2");
		CmDoubleTrade cmDoubleTrade = cmDoubleTradeService.getCmDoubleTrade(param);
		if(cmDoubleTrade != null){
			cmDoubleTrade.setHandleFlag("2");
			cmDoubleTrade.setHandleDt(new Date());
			cmDoubleTrade.setModifier(userId);
			cmDoubleTrade.setModDt(new Date());
			cmDoubleTrade.setChecker(userId);
			cmDoubleTrade.setCheckdt(new Date());
			// 更新表中数据
			cmDoubleTradeService.updateCmDoubleTrade(cmDoubleTrade);
			DoubleTradeInfoDomain syncDoubleTrade = new DoubleTradeInfoDomain();
			syncDoubleTrade.setId(tid);
			syncDoubleTrade.setTxAcctNo(cmDoubleTrade.getPubcustNo());
			syncDoubleTrade.setContractNo(cmDoubleTrade.getContractNo());
			syncDoubleTrade.setFundCode(cmDoubleTrade.getFundCode());
			syncDoubleTrade.setSystemFlag(systemFlag);
			syncDoubleTrade.setTaskType(cmDoubleTrade.getTaskType());
			List<DoubleTradeInfoDomain> syncList = new ArrayList<DoubleTradeInfoDomain>();
			syncList.add(syncDoubleTrade);
			PushCmDoubleDataUtil c = new PushCmDoubleDataUtil();
			c.setSystemFlag("ZT");
			c.setSyncList(syncList);
			// 异步同步双录状态给ZT
			Thread syncThread = new Thread(c);
			syncThread.start();
		}
	}
	/**
	 * 修改交易记录状态
	 * @param request
	 * @param response
	 * @return String
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/batchUpdateRemark.do")
	public String batchUpdateRemark(HttpServletRequest request,	HttpServletResponse response) {
		// 保存
		String result;
		try {
			String handleFlag = request.getParameter("handleFlag");
			String remark = request.getParameter("remark");
			String tids = request.getParameter("tids");
			String pushIds = request.getParameter("pushIds");
			String operator = request.getParameter("operator");
			if (StringUtil.isNullStr(tids) || "null".equals(tids)) {
				return "paramError";
			}
			//默认为客服回访，如果是IC进来的，则状态应该为已处理待审核
			String checkflag = "2";
			if("IC".equals(operator)){
				checkflag = "3";
			}

			if (checkflag.equals(handleFlag)) {
				String[] arrayTid = tids.split(",");
				for (String tid : arrayTid) {
					boolean isUploaded = isUploadedFile(tid);
					if (!isUploaded) {
						result = "noUploadedFile";
						return result;
					}
				}
			}
			// 获取登陆用户信息
			HttpSession session = request.getSession();
			String userId = (String) session.getAttribute("userId");
			CmDoubleTrade cmDoubleTrade = new CmDoubleTrade();
			cmDoubleTrade.setModifier(userId);
			cmDoubleTrade.setModDt(new Date());
			cmDoubleTrade.setHandleFlag(handleFlag);

			// 如果备注信息不为空，则新增一条备注信息
			CmDoubleRemark cmDoubleRemark = null;
			if (StringUtils.isNotBlank(remark) && !"null".equals(remark)) {
				cmDoubleRemark = new CmDoubleRemark();
				cmDoubleRemark.setTid(tids);
				cmDoubleRemark.setRemark(remark);
				cmDoubleRemark.setCreator(userId);
				cmDoubleRemark.setCreDt(DateTimeUtil.getCurDateTime());
				if (StringUtils.isNotBlank(operator) && "EC".equals(operator)) {
					cmDoubleRemark.setDeptFlag("EC");
				} else if (StringUtils.isNotBlank(operator)	&& "IC".equals(operator)) {
					cmDoubleRemark.setDeptFlag("IC");
				}
			}

			if (StringUtils.isNotBlank(pushIds) && !"null".equals(pushIds)) {
				String[] rowData = pushIds.split(",");

				// 需要同步记录状态到ZT的List
				List<DoubleTradeInfoDomain> syncZtList = new ArrayList<DoubleTradeInfoDomain>();

				for (int i = 0; i < rowData.length; i++) {
					String row = rowData[i];
					String[] rowArray = row.split("_");
					String tid = rowArray[0];
					String pubcustNo = rowArray[1];
					String contractNo = rowArray[2];
					String fundCode = rowArray[3];
					String systemFlag = rowArray[4];
					String taskType = rowArray[5];

					// 将数据来源是“ZT”的数据保存到syncZTList中
					if (checkflag.equals(handleFlag) && !"4".equals(handleFlag) && StringUtils.isNotBlank(pushIds)) {
						if (StringUtils.isNotBlank(pubcustNo)) {
							DoubleTradeInfoDomain syncDoubleTrade = new DoubleTradeInfoDomain();
							syncDoubleTrade.setId(tid);
							syncDoubleTrade.setTxAcctNo(pubcustNo);
							syncDoubleTrade.setContractNo(contractNo);
							syncDoubleTrade.setFundCode(fundCode);
							syncDoubleTrade.setSystemFlag(systemFlag);
							syncDoubleTrade.setTaskType(taskType);
							syncZtList.add(syncDoubleTrade);
						}

					}

					// 更新表中数据
					cmDoubleTradeService.updateBatchCmDoubleTrade(cmDoubleTrade, cmDoubleRemark);

				}

				// 如果syncZTList中有值，则进行同步数据操作
				if (syncZtList != null && syncZtList.size() > 0) {
					PushCmDoubleDataUtil c = new PushCmDoubleDataUtil();
					c.setSystemFlag("ZT");
					c.setSyncList(syncZtList);
					// 异步同步双录状态给ZT
					Thread syncThread = new Thread(c);
					syncThread.start();
				}
			}

			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 电话录音调取和上传操作
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/uploadUrlFile.do")
	public Map<String, Object> uploadUrlFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(1);
		String filePaths = request.getParameter("filePath");
		String fileIds = request.getParameter("fileName");
		String tid = request.getParameter("tid");
		String[] array = filePaths.split(",");
		String[] arrayFileid  =fileIds.split(",");
		try{
			// 获取登陆用户信息
			HttpSession session=request.getSession();
			User loginUser=(User)session.getAttribute("loginUser");

			final List<CmDoubleFile> insertFileList = new ArrayList<CmDoubleFile>();
			final List<CmDoubleTradeRFile> insertRFileList = new ArrayList<CmDoubleTradeRFile>();
			String result = "success";
			//已生成的随机数集合，防止短时间内生成的随机数重复
			Map<String, String> randomMap = new HashMap<>(1);
			for (int i = 0; i < array.length; i++) {
				CmDoubleFile cmDoubleFile = new CmDoubleFile();
				final String remoteUrl = array[i];
//				String remoteUrl = array[i];
				final String fileId =arrayFileid[i];

				// 如果录音文件不存在，返回错误
				if(StringUtil.isNullStr(fileId)){
					resultMap.put("msg", "nodata");
					return resultMap;
				}

				Random random = new Random();
				String ran;
				String curTime = DateTimeUtil.getCurDateTime();
				do {
					ran = random.nextInt(90) + 10 + "";
				}while (curTime.equals(randomMap.get(ran)));
				randomMap.put(ran,curTime);
				String fileName = getStoreFileName(tid, ".wav");


				DoubleFileUpdateVo updateVo = new DoubleFileUpdateVo();
				updateVo.setFileId(fileId);
				updateVo.setFileName(fileName);
//						updateVo.setUploadType("0");
				updateVo.setTradeId(tid);
				updateVo.setRemoteUrl(remoteUrl);
				updateVo.setStoreFileConfig(DfileConstants.DOUBLE_TRADE_STORE_CONFIG);
				ReturnMessageDto<String> updateResp=uploadFileByRemoteUrl(updateVo);

				// 上传失败判断
				if(!updateResp.isSuccess()){
					result = "上传失败！";
				}

				// 往文件表插入记录
				cmDoubleFile.setFileId(fileId);
				cmDoubleFile.setFilePath(remoteUrl);
				cmDoubleFile.setFileName(fileName);
				cmDoubleFile.setRecStat("0");
				cmDoubleFile.setUploader(loginUser.getUserName());
				cmDoubleFile.setUploadDt(DateTimeUtil.getCurDateTime());

				// 如果文件上传成功，则往文件表和关系表插入数据
				if(updateResp.isSuccess()) {
					//上传状态：0上传成功；1上传失败；
					cmDoubleFile.setUploadType("0");
				} else {
					// 对上传失败文件信息进行保存//上传状态：0上传成功；1上传失败；
					cmDoubleFile.setUploadType("1");
					resultMap.put("msg", String.format("上传失败！文件地址：%s",remoteUrl));
				}

				insertFileList.add(cmDoubleFile);

				// 关联订单和文件表记录
				CmDoubleTradeRFile r = new CmDoubleTradeRFile();
				r.setTid(tid);
				r.setFileId(fileId);
				r.setRecStat("0");
				r.setCreator(loginUser.getUserId());
				r.setCreDt(DateTimeUtil.getCurDateTime());
				insertRFileList.add(r);

			}
			cmDoubleFileService.insertBatchCmDoubleFile(insertFileList, insertRFileList);
			resultMap.put("msg", result);
		}catch(Exception e){
			resultMap.put("msg", "error");
			log.error("上传数据出现异常",e);
		}
		return resultMap;
	}




	/**
	 * @description:(路演会议通用文件处理 ， 1-请求远端url获取源 2-删除历史文件 3-上传新的文件)
	 * @param updateVo
	 * @return com.howbuy.crm.base.ReturnMessageDto<java.lang.String>
	 * @author: haoran.zhang
	 * @date: 2025/2/18 10:44
	 * @since JDK 1.8
	 */
	private ReturnMessageDto<String> uploadFileByRemoteUrl(DoubleFileUpdateVo updateVo){
		HFileService instance = HFileService.getInstance();

		String fileId=updateVo.getFileId();
		String storeFileConfig=updateVo.getStoreFileConfig();

		// 如果文件表中已存在该记录，并且上传状态是已上传的，则先删除服务器上文件
		List<CmDoubleFile> existsFileList = cmDoubleFileService.getSuccessFileListByFileId(fileId);
		log.info("双录回访文件处理，tradeId:{},fileId:{},查找到的存量已上传的文件条数：{}",
				updateVo.getTradeId(),fileId,existsFileList.size());

		//历史逻辑： 删除存量文件。
		//NOTICE : 历史逻辑无法保证 删除正常操作。 但是删除失败，不影响后续上传逻辑。  此处保持逻辑不变
		existsFileList.stream().forEach(existFile->{
			Boolean delateFlag = false;
			try {
				delateFlag = instance.deleteFile(storeFileConfig, existFile.getRelativeFilePath(), existFile.getFileName());
			} catch (Exception e) {
				log.error("删除双录回访文件异常",e);
			}
			log.info("双录回访文件处理，tradeId:{},fileId:{},查找到的文件ID:{},删除文件信息：{}，删除状态：{}",
					updateVo.getTradeId(),fileId,existFile.getId(),JSON.toJSONString(existFile),delateFlag);
		});
		String remoteUrl=updateVo.getRemoteUrl();
		String fileName = updateVo.getFileName();
		String relativeFilePath = updateVo.getRelativeFilePath();

		ReturnMessageDto<String> resp;
		try {
			InputStream inputStream=FileUtil.getInpuStreamByRemoteUrl(remoteUrl);
			instance.write(storeFileConfig, relativeFilePath,fileName, inputStream);
			resp = ReturnMessageDto.ok();
		} catch (Exception e) {
			log.error("上传双录回访文件异常",e);
			return ReturnMessageDto.fail("上传文件异常！");
		}
		log.info("上传双录回访文件处理，文件信息：{}，上传结果：{}",JSON.toJSONString(updateVo),JSON.toJSONString(resp));
		return resp;
	}

	/**
	 * 电话录音调取和上传操作
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/rsuploadUrlFile.do")
	public Map<String, Object> rsuploadUrlFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(1);
		String filePaths = request.getParameter("filePath");
		String fileIds = request.getParameter("fileName");
		String tid = request.getParameter("tid");
		String[] array = filePaths.split(",");
		String[] arrayFileid  =fileIds.split(",");
		String custname = request.getParameter("custname");
		String custno = request.getParameter("custno");
		try{
			// 获取登陆用户信息
			HttpSession session=request.getSession();
			User loginUser=(User)session.getAttribute("loginUser");

			final List<CmDoubleFile> insertFileList = new ArrayList<CmDoubleFile>();
			final List<CmDoubleTradeRFile> insertRFileList = new ArrayList<CmDoubleTradeRFile>();
			String result = "success";
			//已生成的随机数集合，防止短时间内生成的随机数重复
			Map<String, String> randomMap = new HashMap<>(1);
			for (int i = 0; i < array.length; i++) {
				CmDoubleFile cmDoubleFile = new CmDoubleFile();
				final String remoteUrl = array[i];
				final String fileId =arrayFileid[i];

				// 如果录音文件不存在，返回错误
				if(StringUtil.isNullStr(fileId)){
					resultMap.put("msg", "nodata");
					return resultMap;
				}

				Random random = new Random();
				String ran;
				String curTime = DateTimeUtil.getCurDateTime();
				do {
					ran = random.nextInt(90) + 10 + "";
				}while (curTime.equals(randomMap.get(ran)));
				randomMap.put(ran,curTime);
				//双录日期时间到秒_投顾客户号 _客户姓名_  购买产品代码 _   预约ID _  随机数 2位
				String fileName = String.join("_", curTime,custno, custname, ran)+ ".wav";
				log.info("上传零售抽样回访文件路径：" + fileName);

				DoubleFileUpdateVo updateVo = new DoubleFileUpdateVo();
				updateVo.setFileId(fileId);
				updateVo.setFileName(fileName);
//						updateVo.setUploadType("0");
				updateVo.setTradeId(tid);
				updateVo.setRemoteUrl(remoteUrl);
				updateVo.setStoreFileConfig(DfileConstants.DOUBLE_TRADE_STORE_CONFIG);
				ReturnMessageDto<String> updateResp=uploadFileByRemoteUrl(updateVo);

				// 如果不存在，则上传文件，再进行关联
				// 上传失败判断
				if(!updateResp.isSuccess()){
					result = "上传失败！";
					resultMap.put("msg", result);
				}

				// 往文件表插入记录
				cmDoubleFile.setFileId(fileId);
				cmDoubleFile.setFilePath(remoteUrl);
				cmDoubleFile.setFileName(fileName);
				cmDoubleFile.setRecStat("0");
				cmDoubleFile.setUploader(loginUser.getUserName());
				cmDoubleFile.setUploadDt(DateTimeUtil.getCurDateTime());

				// 如果文件上传成功，则往文件表和关系表插入数据
				//上传状态：0上传成功；1上传失败；
				String uploadType  =updateResp.isSuccess()?"0":"1";
				cmDoubleFile.setUploadType(uploadType);
				insertFileList.add(cmDoubleFile);

				// 关联订单和文件表记录
				CmDoubleTradeRFile r = new CmDoubleTradeRFile();
				r.setTid(tid);
				r.setFileId(fileId);
				r.setRecStat("0");
				r.setCreator(loginUser.getUserId());
				r.setCreDt(DateTimeUtil.getCurDateTime());
				insertRFileList.add(r);
			}
			cmDoubleFileService.insertBatchCmDoubleFile(insertFileList, insertRFileList);
			resultMap.put("msg", result);
		}catch(Exception e){
			resultMap.put("msg", "error");
			log.error("上传数据出现异常",e);
		}
		return resultMap;
	}


	/**
	 * 随机生成文件编号：年月日+6位随机数
	 */
	public String getRandomFileId() {
		int randomNum=(int)(Math.random()*900000)+100000;
		return DateTimeUtil.getCurDate()+randomNum;
	}


	/**
	 * 对本地语音文件进行上传
	 * @param request
	 * @return Map<String, Object>
	 */
	@ResponseBody
	@RequestMapping(value="/uploadVoiceFile.do",method=RequestMethod.POST)
	public Map<String, Object> uploadVoiceFile(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>(3);
		InputStream inputStream = null;
		String tid = request.getParameter("tid");
		try {
			// 转型为MultipartHttpRequest：
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;

			// 获得文件：
			List<MultipartFile> files = multipartRequest.getFiles("filelist[]");

			if (files != null && files.size() > 0) {

				// 记录文件上传信息
				final List<CmDoubleFile> listFile = new ArrayList<CmDoubleFile>();
				final List<CmDoubleTradeRFile> listTradeRfile = new ArrayList<CmDoubleTradeRFile>();

				List<Map<String, String>> msglist = new ArrayList<Map<String, String>>();
				//成功数
				Integer succnum = 0;
				//循环获取file数组中得文件
				for (int i = 0; i < files.size(); i++) {
					Map<String, String> msgmap = new HashMap<String, String>(2);
					MultipartFile file = files.get(i);
					// 获得输入流：
					inputStream = file.getInputStream();
					// 获取原始文件名称
					String originalFileName = file.getOriginalFilename();

					// 如果录音文件不存在，返回错误
					if (StringUtil.isNullStr(originalFileName)) {
						resultMap.put("msg", "nodata");
						return resultMap;
					}
					// 获取文件后缀名
					String suffixName = originalFileName.substring(originalFileName.lastIndexOf('.'));
					// 生成新的文件编号
					String fileId = getRandomFileId();
					String newFileName = getStoreFileName(tid, suffixName);
					// 如果不存在，则上传文件，再进行关联
					String relativeFilePath = DateUtil.date2String(new Date(), FileUtil.FILE_PATH_DATE_FORMAT);
					HFileService instance = HFileService.getInstance();
					boolean uploadResp=false;
					try {
						// 尝试写入文件
						instance.write(DfileConstants.DOUBLE_TRADE_STORE_CONFIG, relativeFilePath, newFileName, inputStream);
						uploadResp=true;
					} catch (Exception e) {
						// 处理异常情况
						log.error("上传双录回访文件异常", e);
					}
					log.info("上传双录回访文件处理，上传文件名称：{}，fileId:{}, 存储文件名称：{}，上传结果：{}",
							originalFileName,fileId,newFileName, uploadResp);

					// 获取登陆用户信息
					HttpSession session = request.getSession();
					User loginUser = (User) session.getAttribute("loginUser");
					CmDoubleFile cmDoubleFile = new CmDoubleFile();
					msgmap.put("filename", originalFileName);
					// 往文件表插入记录
					cmDoubleFile.setFileId(fileId);
					cmDoubleFile.setFilePath("");
					cmDoubleFile.setFileName(newFileName);
					cmDoubleFile.setUploader(loginUser.getUserName());
					cmDoubleFile.setRecStat("0");
					cmDoubleFile.setUploadDt(DateTimeUtil.getCurDateTime());

					// 如果文件上传成功，则往文件表和关系表插入数据
					if (uploadResp) {
						// 上传状态：0上传成功；1上传失败；
						cmDoubleFile.setUploadType("0");
						succnum++;
						msgmap.put("msg", "上传成功");
						msglist.add(msgmap);
					} else {
						// 对上传失败文件信息进行保存
						//上传状态：0上传成功；1上传失败；
						cmDoubleFile.setUploadType("1");
						msgmap.put("msg", "上传失败，系统问题");
						msglist.add(msgmap);
						//抛出异常，全部失败处理
						//TODO: 震惊， 判断了总条数，成功条数，失败条数，话术都区分开。结果这里 直接throw Exception.  暂时不改变历史逻辑！
						throw new Exception();
					}
					listFile.add(cmDoubleFile);

					// 关联订单和文件表记录
					CmDoubleTradeRFile r = new CmDoubleTradeRFile();
					r.setTid(tid);
					r.setFileId(fileId);
					r.setCreDt(DateTimeUtil.getCurDateTime());
					r.setCreator(loginUser.getUserId());
					r.setRecStat("0");
					listTradeRfile.add(r);

					//resultMap.put("msg", uploadResult);
				}

				cmDoubleFileService.insertBatchCmDoubleFile(listFile, listTradeRfile);
				JSONArray array = JSONArray.fromObject(msglist);
				if (succnum == files.size()) {
					resultMap.put("uploadFlag", "allsuccess");
					resultMap.put("errorMsg", "上传成功");
				}

				resultMap.put("array", array.toString());
			}

		} catch (Exception e) {
			log.error("上传数据出现异常", e);
			resultMap.put("uploadFlag", "error");
			resultMap.put("errorMsg", "上传失败");
		}
		return resultMap;
	}

	@RequestMapping("switchDoubleType.do")
	@ResponseBody
	public ReturnMessageDto<String> switchDoubleType(String doubleTradeId, String assignDoubleType){
		return doubleTradeRelatedService.switchDoubleType(doubleTradeId, assignDoubleType,getLoginUserId());
	}


	/**
	 * 查看双录文件方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listViewRecord_json.do")
	public Map<String, Object> listViewRecordJson(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		String tid = request.getParameter("tid");
		Map<String, String> param = new HashMap<String, String>(1);
		// 如果查询条件（预约类型）不为空，则增加预约类型查询参数
		if (StringUtils.isNotBlank(tid)) {
			param.put("tid", tid);
		} else {
			resultMap.put("msg", "error");
			return resultMap;
		}

		List<CmDoubleTradeRFile> listFile = cmDoubleFileService.listCmDoubleTradeRFile(param);
		resultMap.put("msg", "success");
		resultMap.put("rows", listFile);
		return resultMap;
	}

	@RequestMapping("checkVideo")
	@ResponseBody
	public String checkVideo(String roomId, String tid, String fileId){
		String filePath = videoDoubleTradeService.getVideoUrl(roomId, tid);
		if(filePath != null) {
			CmDoubleFile cmDoubleFile = new CmDoubleFile();
			cmDoubleFile.setFileId(fileId);
			cmDoubleFile.setFilePath(filePath);
			cmDoubleFileService.updateCmDoubleFile(cmDoubleFile);
		}
		return filePath;
	}

	/**
	 * 删除录音文件方法（逻辑删除）
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 */
	@ResponseBody
	@RequestMapping("/removeTradeRFile.do")
	public Map<String, Object> removeTradeRfile(HttpServletRequest request,	HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>(1);
		String id = request.getParameter("id");
		try {
			if (StringUtils.isNotBlank(id)) {
				HttpSession session = request.getSession();
				String userId = (String) session.getAttribute("userId");
				CmDoubleTradeRFile cmDoubleTradeRfile = new CmDoubleTradeRFile();
				cmDoubleTradeRfile.setId(id);
				cmDoubleTradeRfile.setRecStat("1");
				cmDoubleTradeRfile.setModifier(userId);
				cmDoubleTradeRfile.setModDt(DateTimeUtil.getCurDateTime());
				cmDoubleFileService.updateCmDoubleTradeRFile(cmDoubleTradeRfile);
				resultMap.put("msg", "success");
			} else {
				resultMap.put("msg", "paramError");
			}
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("msg", "error");
		}
		return resultMap;
	}

	/**
	 * 对零售Excel文件进行上传
	 * @param request
	 * @return Map<String, Object>
	 */
	@ResponseBody
	@RequestMapping(value = "/importExcel.do", method = RequestMethod.POST)
	public Map<String, Object> importExcel(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		InputStream inputStream = null;
		String errorMsg = "";
		try {
			// 转型为MultipartHttpRequest：
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;

			// 获得文件：
			MultipartFile file = multipartRequest.getFile("file");

			// 获得输入流：
			inputStream = file.getInputStream();

			// 读取文件
			ExcelDataResult excelDataResult = ExcelReader.readExcel(inputStream, ExcelImpTableConf.confMap.get("publicDoubleRs"));
			if (!excelDataResult.isCheakExcelFlag()) {
				resultMap.put("msg", "hadError");
				resultMap.put("errorMsg", "数据格式不正确");
				return resultMap;
			}

			// 判断导入数据
			if (null == excelDataResult || null == excelDataResult.getLists() || excelDataResult.getLists().size() == 0) {
				errorMsg = "没有上传记录";
				resultMap.put("msg", "noData");
			} else {
				int line = 1;
				List<?> excelList = excelDataResult.getLists();
				boolean existFlag = false;
				List<CmDoubleTradeRs> cmDoubleTradeRsList = new ArrayList<CmDoubleTradeRs>();
				HttpSession session = request.getSession();
				String userId = (String) session.getAttribute("userId");
				// 加载缓存数据
				for (int i = 0; i < excelList.size(); i++) {
					AddDoubleRsInfo addDoubleRsInfo = (AddDoubleRsInfo) excelList.get(i);
					Map<String, String> param = new HashMap<String, String>(1);

					param.put("contractNo", addDoubleRsInfo.getContractNo());

					// 判断是否已存在相同记录
					List<CmDoubleTradeRs> list = cmDoubleTradeRsService.listCmDoubleTradeRs(param);
					if (list != null && list.size() > 0) {
						existFlag = true;
						errorMsg = "第 " + line + "行，交易单号为：" + addDoubleRsInfo.getContractNo() + "的数据已存在！";
						break;
					}

					// 组装插入数据库实体类
					CmDoubleTradeRs cmDoubleTradeRs = new CmDoubleTradeRs();
					cmDoubleTradeRs.setContractNo(addDoubleRsInfo.getContractNo());
					// 待处理
					cmDoubleTradeRs.setHandleFlag("1");
					cmDoubleTradeRs.setCreator(userId);
					cmDoubleTradeRs.setCreDt(DateTimeUtil.getCurDateTime());
					cmDoubleTradeRsList.add(cmDoubleTradeRs);
					line++;
				}

				if (!existFlag) {
					cmDoubleTradeRsService.addBatchCmDoubleTradeRs(cmDoubleTradeRsList);
					resultMap.put("msg", "success");
				} else {
					resultMap.put("msg", "hadError");
					resultMap.put("errorMsg", errorMsg);
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("msg", "hadError");
			if ("".equals(errorMsg)) {
				resultMap.put("errorMsg", "数据字段为空或格式不正确，请检查数据！");
			}
		}

		return resultMap;
	}

	/**
	 * 导出零售列表数据方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/exportExcel.do")
	public Map<String, Object> exportExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(1);

		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String tradeStartDate = request.getParameter("tradeStartDate");
		String tradeEndDate = request.getParameter("tradeEndDate");
		String uploadStartDate = request.getParameter("uploadStartDate");
		String uploadEndDate = request.getParameter("uploadEndDate");
		String handleFlag = request.getParameter("handleFlag");
		String uploader = request.getParameter("uploader");
		String searchFlag = request.getParameter("searchFlag");
		String handleCount = request.getParameter("handleCount");

		// 设置默认查询条件（双录回访状态）
		if (StringUtils.isNotBlank(searchFlag) && "true".equals(searchFlag)) {
			// 如果查询条件（结束日期）不为空，则增加结束日期查询参数
			if (StringUtils.isNotBlank(tradeEndDate)) {
				param.put("tradeEndDate", tradeEndDate);
			} else {
				param.put("tradeEndDate", null);
			}

			// 如果查询条件（优先级）不为空，则增加优先级查询参数
			if (StringUtils.isNotBlank(uploadStartDate)) {
				param.put("uploadStartDate", uploadStartDate);
			} else {
				param.put("uploadStartDate", null);
			}

			// 如果查询条件（双录回访状态）不为空，则增加双录回访状态查询参数
			if (StringUtils.isNotBlank(handleFlag)) {
				param.put("handleFlag", handleFlag);
			} else {
				param.put("handleFlag", null);
			}

			// 如果查询条件（开始日期）不为空，则增加开始日期查询参数
			if (StringUtils.isNotBlank(tradeStartDate)) {
				param.put("tradeStartDate", tradeStartDate);
			} else {
				param.put("tradeStartDate", null);
			}

			// 如果查询条件（优先级）不为空，则增加优先级查询参数
			if (StringUtils.isNotBlank(uploadEndDate)) {
				param.put("uploadEndDate", uploadEndDate);
			} else {
				param.put("uploadEndDate", null);
			}
		} else {
			// 设置默认查询状态
			param.put("handleFlag", "1");

			// 设置默认查询日期
			Map<String, String> startAndEndDayMap = DateTimeUtil.getFiveDayStartAndEnd();
			param.put("tradeStartDate", startAndEndDayMap.get("beginDate"));
			param.put("tradeEndDate", startAndEndDayMap.get("endDate"));
			param.put("uploadStartDate", startAndEndDayMap.get("beginDate"));
			param.put("uploadEndDate", startAndEndDayMap.get("endDate"));
		}

		// 如果查询条件（联系方式）不为空，则增加联系方式查询参数
		if (StringUtils.isNotBlank(uploader)) {
			param.put("uploader", uploader);
		} else {
			param.put("uploader", null);
		}

		// 如果查询条件（已访状态）不为空，则增加已访状态查询参数
		if (StringUtils.isNotBlank(handleCount)) {
			param.put("handleCount", handleCount);
		} else {
			param.put("handleCount", null);
		}

		// 判断常量表中合规标识：true启用，false停用
		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
		boolean roleCpFlag = false;
		if (cacheMap != null && !cacheMap.isEmpty()) {
			roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
		}

		// 判断登录人员的角色中是否包括“合规人员”角色
		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
		if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
			param.put("hascp", "true");
		}
		List<CmDoubleTradeRs> listRs = cmDoubleTradeRsService.listCmDoubleTradeRs(param);
		if (listRs != null && listRs.size() > 0) {
			for (CmDoubleTradeRs rs : listRs) {
				try {
					DisChannelCodeEnum disChannelCodeEnum = jjxxInfoService.getDisCodeEnumByJjdm(rs.getFundCode());
					log.info("根据产品代码：{} 获取分销渠道：{}", rs.getFundCode(), disChannelCodeEnum);
					// 通过调用接口获取投资者类型
					String hboneNo = rs.getHboneNo();
					if (StringUtils.isNotBlank(hboneNo)) {
						ReturnMessageDto<KycInfoResponse> kycInfoDto = acctRelatedService.queryKycInfoByInvestType(hboneNo,
								disChannelCodeEnum,
								// 投资者类型为：1-个人
								CrmCustInvestTypeEnum.getEnum("1"));
						if (kycInfoDto.isSuccess() && kycInfoDto.getReturnObject() != null) {
							KycInfoResponse kycInfoResponse = kycInfoDto.getReturnObject();
							rs.setRiskLevel(kycInfoResponse.getRiskToleranceLevel());
						}
					}
				} catch (Exception e) {
					log.error("queryKycInfoService接口出现异常", e.getMessage());
				}
			}

			DoubleTradeExportService manager = new DoubleTradeExportService();
			manager.exportDoubleRsByList(response, listRs);
		} else {
			resultMap.put("msg", "dataError");
			return resultMap;
		}

		resultMap.put("msg", "success");
		return resultMap;
	}

	/**
	 * 导出客户和投顾双录列表数据方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/exportDoubleExcel.do")
	public Map<String, Object> exportDoubleExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(1);

		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String roleFlag = request.getParameter("roleFlag");
		String startDate = request.getParameter("startDate");
		String endDate = request.getParameter("endDate");
		String handleFlag = request.getParameter("handleFlag");
		String custName = request.getParameter("custName");
		String mobile = request.getParameter("mobile");
		String fundName = request.getParameter("fundName");
		String fundCode = request.getParameter("fundCode");
		String conscustno = request.getParameter("conscustno");
		String searchFlag = request.getParameter("searchFlag");
		String order = request.getParameter("order");
		String recState = request.getParameter("recState");
		String orgCode2 = request.getParameter("orgCode");
		String consCode2 = request.getParameter("consCode");
		String syncDateBegin = request.getParameter("syncDateBegin");
		String syncDateEnd = request.getParameter("syncDateEnd");
		String seeLctVisit = request.getParameter("seeLctVisit");
		//投顾反馈回访日期
		String feedbackStartDate = request.getParameter("feedbackStartDate");
		String feedbackEndDate = request.getParameter("feedbackEndDate");

		String hasVisitStatus = request.getParameter("hasVisitStatus");
		String queryVisitProblem = request.getParameter("query_visitProblem");
		String queryIsLegal = request.getParameter("query_isLegal");
		String conductor = request.getParameter("conductor");

		String visitDtBegin = request.getParameter("visitDtBegin");
		String visitDtEnd = request.getParameter("visitDtEnd");
		String assignTimeBegin = request.getParameter("assignTimeBegin");
		String assignTimeEnd = request.getParameter("assignTimeEnd");

		if (StringUtils.isNotBlank(feedbackStartDate)) {
			param.put("feedbackStartDate",feedbackStartDate);
		}else {
			param.put("feedbackStartDate",null );
		}
		if (StringUtils.isNotBlank(feedbackEndDate)) {
			param.put("feedbackEndDate",feedbackEndDate);
		}else {
			param.put("feedbackEndDate",null );
		}
		// 增加查看理财通数据参数
		param.put("seeLctVisit", seeLctVisit);

		// 如果列头点了排序，则按所在列排序，否则那默认规则排序
		if (StringUtils.isNotBlank(order)) {
			param.put("order", order);
		} else {
			param.put("order", null);
		}

		// 如果查询条件（角色标识）不为空，则增加角色标识查询参数
		if (StringUtils.isNotBlank(roleFlag)) {
			param.put("roleFlag", roleFlag);
		} else {
			param.put("roleFlag", null);
		}
		// 判断是否为指定角色|售后部门负责人  售后负责人  售后作业岗 销售助理主管  总部投顾助理 IC数据分析经理 总部投顾总监
		HttpSession session = request.getSession();
		String userId = (String) session.getAttribute("userId");
		// 设置默认查询条件（双录回访状态）
		if (StringUtils.isNotBlank(searchFlag) && "true".equals(searchFlag)) {
			//判断反馈开始结束日期是否为空，不为空则为当天时间
			if (StringUtils.isNotBlank(feedbackStartDate)) {
				param.put("feedbackStartDate",feedbackStartDate);
			}else {
				param.put("feedbackStartDate",null );
			}
			if (StringUtils.isNotBlank(feedbackEndDate)) {
				param.put("feedbackEndDate",feedbackEndDate);
			}else {
				param.put("feedbackEndDate",null );
			}
			// 如果查询条件（双录回访状态）不为空，则增加双录回访状态查询参数
			if (StringUtils.isNotBlank(handleFlag)) {
				param.put("handleFlag", handleFlag);
			} else {
				param.put("handleFlag", null);
			}

			// 如果查询条件（开始日期）不为空，则增加开始日期查询参数
			if (StringUtils.isNotBlank(startDate)) {
				param.put("startDate", startDate);
			} else {
				param.put("startDate", null);
			}

			// 如果查询条件（结束日期）不为空，则增加结束日期查询参数
			if (StringUtils.isNotBlank(endDate)) {
				param.put("endDate", endDate);
			} else {
				param.put("endDate", null);
			}

			// 如果查询条件（入库开始时间）不为空，则增加数据入库开始时间查询参数
			if (StringUtils.isNotBlank(syncDateBegin)) {
				param.put("syncDateBegin", syncDateBegin);
			} else {
				param.put("syncDateBegin", null);
			}

			// 如果查询条件（入库结束时间）不为空，则增加数据入库结束时间查询参数
			if (StringUtils.isNotBlank(syncDateEnd)) {
				param.put("syncDateEnd", syncDateEnd);
			} else {
				param.put("syncDateEnd", null);
			}

			// 如果查询条件（是否需要处理）不为空，则增加是否需要处理查询参数
			if (StringUtils.isNotBlank(recState)) {
				param.put("recState", recState);
			} else {
				param.put("recState", "0");
			}

			// 判断所属投顾字段
			if (StringUtils.isNotBlank(consCode2) && !"ALL".equals(consCode2)) {
				// 如果不为空，并且投顾编号等于登录用户，则用登录用户权限模型
				if (consCode2.equals(userId)) {
					// 判断是否为指定角色|售后部门负责人 售后负责人 售后作业岗 销售助理主管 总部投顾助理 IC数据分析经理
					// 总部投顾总监
					Map<String, String> roleParam = new ParamUtil(request).getParamMap();
					roleParam.put("consCode", userId);
					List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
					// 如果所属投顾和登录用户不是一个，则按所属投顾进行查询
					if (listRoles != null && listRoles.size() > 0) {
						param.put("roleLevel", "leader");
					} else {
						// 获取默认登陆用户下属投顾信息
						param.put("roleLevel", "consultant");
						param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
					}
				} else {// 如果所属投顾和登录用户不是一个，则直接用所属投顾进行查询
					param.put("roleLevel", "consultant");
					StringBuffer sb = new StringBuffer();
					sb.append("'" + consCode2 + "'");
					param.put("consCode", sb.toString());
				}
				// 如果投顾编码为空，所属部门编码不为空，则用所属部门编码查询
			} else if (StringUtils.isNotBlank(orgCode2) && !"0".equals(orgCode2)) {
				param.put("roleLevel", "consultant");
				param.put("consCode", ObjectUtils.getSubQueryByOrgCode(orgCode2));
			} else {// 获取默认登陆用户下属投顾信息
				Map<String, String> roleParam = new ParamUtil(request).getParamMap();
				roleParam.put("consCode", userId);
				List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
				if (listRoles != null && listRoles.size() > 0) {
					param.put("roleLevel", "leader");
				} else {
					// 获取默认登陆用户下属投顾信息
					param.put("roleLevel", "consultant");
					param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
				}
			}
		} else {
			// 设置默认查询状态
			param.put("handleFlag", "10");

			// 设置默认查询日期
			param.put("startDate", null);
			param.put("endDate", null);
			param.put("recState", "0");

			Map<String, String> roleParam = new ParamUtil(request).getParamMap();
			roleParam.put("consCode", userId);
			List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
			if (listRoles != null && listRoles.size() > 0) {
				param.put("roleLevel", "leader");
			} else {
				// 获取默认登陆用户下属投顾信息
				param.put("roleLevel", "consultant");
				param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
			}
		}

		// 如果查询条件（客户名称）不为空，则增加客户名称查询参数
		if (StringUtils.isNotBlank(custName)) {
			param.put("custName", custName);
		} else {
			param.put("custName", null);
		}

		// 如果查询条件（联系方式）不为空，则增加联系方式查询参数
		if (StringUtils.isNotBlank(mobile)) {
			param.put("mobile", DigestUtil.digest(mobile.trim()));
		} else {
			param.put("mobile", null);
		}

		// 如果查询条件（产品名称）不为空，则增加产品名称查询参数
		if (StringUtils.isNotBlank(fundName)) {
			param.put("fundName", fundName);
		} else {
			param.put("fundName", null);
		}

		if (StringUtils.isNotBlank(fundCode)) {
			param.put("fundCode", fundCode);
		} else {
			param.put("fundCode", null);
		}

		if (StringUtils.isNotBlank(conscustno)) {
			param.put("conscustno", conscustno);
		} else {
			param.put("conscustno", null);
		}

		// 如果查询条件（处理人）不为空，则增加处理人查询参数
		if (StringUtils.isNotBlank(conductor)) {
			param.put("conductor", conductor);
		} else {
			param.put("conductor", null);
		}

		// 如果查询条件（存在问题）不为空，则增加存在问题查询参数
		if (StringUtils.isNotBlank(queryVisitProblem)) {
			param.put("query_visitProblem", queryVisitProblem);
		} else {
			param.put("query_visitProblem", null);
		}
		// 如果查询条件（质检结果）不为空，则增加质检结果查询参数
		if (StringUtils.isNotBlank(queryIsLegal)) {
			param.put("query_isLegal", queryIsLegal);
		} else {
			param.put("query_isLegal", null);
		}
		// 如果查询条件（客服回访日期）不为空，则增加客服回访日期查询参数
		if (StringUtils.isNotBlank(visitDtBegin)) {
			param.put("visitDtBegin", visitDtBegin);
		} else {
			param.put("visitDtBegin", null);
		}
		if (StringUtils.isNotBlank(visitDtEnd)) {
			param.put("visitDtEnd", visitDtEnd);
		} else {
			param.put("visitDtEnd", null);
		}

		// 如果查询条件（任务分配日期）不为空，则增加任务分配日期查询参数
		if (StringUtils.isNotBlank(assignTimeBegin)) {
			param.put("assignTimeBegin", assignTimeBegin);
		} else {
			param.put("assignTimeBegin", null);
		}
		if (StringUtils.isNotBlank(assignTimeEnd)) {
			param.put("assignTimeEnd", assignTimeEnd);
		} else {
			param.put("assignTimeEnd", null);
		}

		// 判断常量表中合规标识：true启用，false停用
		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
		boolean roleCpFlag = false;
		if (cacheMap != null && !cacheMap.isEmpty()) {
			roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
		}

		// 判断登录人员的角色中是否包括“合规人员”角色
		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
		if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
			param.put("hascp", "true");
		}

		// 通过Session获取产品广度信息
		String topcpdata = (String) session.getAttribute("topcpdata");
		param.put("topcpdata", topcpdata);

		//2023年3月3日  新增前端查询属性： existedVisit：0-不存在回访记录
		param.put("existedVisit",request.getParameter("existedVisit") );
		//2023年3月3日  新增前端查询属性： feedBackType：回访类型
		param.put("feedBackType",request.getParameter("feedBackType") );
		//2024年9月13日 新增前端查询属性：visitForceFlag：上报强控回访
		param.put("visitForceFlag",request.getParameter("visitForceFlag") );

		List<CmDoubleTrade> listDoubleTrade = cmDoubleTradeService.listCmDoubleTrade(param);
		String taskType = "";
		if("IC".equals(roleFlag)){
			taskType="1";
		}
		if("EC".equals(roleFlag)){
			taskType="2";
		}
		if(StringUtil.isNotNullStr(taskType)){
			cmDoubleTradeService.setVisitQuality(listDoubleTrade,taskType );
		}
		// 通过接口获取投资者类型
		if (listDoubleTrade != null && listDoubleTrade.size() > 0) {
			try {
				for (CmDoubleTrade cmDoubleTrade : listDoubleTrade) {

					ConsOrgCache orgcache = ConsOrgCache.getInstance();
					String uporgcode = orgcache.getUpOrgMapCache().get(cmDoubleTrade.getOrgCode());
					if("0".equals(uporgcode)){
						cmDoubleTrade.setUpOrgName(cmDoubleTrade.getOrgName());
					}else{
						cmDoubleTrade.setUpOrgName(orgcache.getAllOrgMap().get(uporgcode));
					}
					// 获取处理人名称
					String conductorCode = cmDoubleTrade.getConductor();
					if (StringUtils.isNotBlank(conductorCode)) {
						cmDoubleTrade.setConductorName(ConsOrgCache.getInstance().getAllUserMap().get(conductorCode));
					}

					// 存在问题
					String visitProblemCode = cmDoubleTrade.getVisitProblem();
					if (StringUtils.isNotBlank(visitProblemCode)) {
						cmDoubleTrade.setVisitProblemVal(ConstantCache.getInstance().getVal("visitProblem", visitProblemCode));
					}

					// 已访状态
					String hasVisitStatusCode = cmDoubleTrade.getHasVisitStatus();
					if (StringUtils.isNotBlank(hasVisitStatus)) {
						cmDoubleTrade.setHasVisitStatusVal(ConstantCache.getInstance().getVal("hasVisitStatus", hasVisitStatusCode));
					}
				}
			} catch (Exception e) {
				log.error("queryKycInfoService接口出现异常", e.getMessage());
			}

			DoubleTradeExportService exportService = new DoubleTradeExportService();
			exportService.exportDoubleTradeByList(response, listDoubleTrade, roleFlag);
		} else {
			resultMap.put("msg", "dataError");
			return resultMap;
		}

		resultMap.put("msg", "success");
		return resultMap;
	}

	/**
	 * 修改Rs交易记录状态
	 * @param request
	 * @param response
	 * @return String
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/modifyRsStatus.do")
	public String modifyRsStatus(HttpServletRequest request, HttpServletResponse response) {
		// 保存
		String result = "error";
		String tid = request.getParameter("tid");
		String handleFlag = request.getParameter("handleFlag");
		try {
			// 如果状态改为已处理，则先判断是否已上传录音文件
			if ("2".equals(handleFlag)) {
				boolean isUploaded = isUploadedFile(tid);
				if (!isUploaded) {
					result = "noUploadedFile";
					return result;
				}
			}
			CmDoubleTradeRs cmDoubleTradeRs = new CmDoubleTradeRs();
			cmDoubleTradeRs.setId(tid);
			cmDoubleTradeRs.setHandleFlag(handleFlag);
			// 获取登陆用户信息
			HttpSession session = request.getSession();
			String userId = (String) session.getAttribute("userId");
			cmDoubleTradeRs.setModifier(userId);
			cmDoubleTradeRs.setModDt(DateTimeUtil.getCurDateTime());
			cmDoubleTradeRsService.updateCmDoubleTradeRs(cmDoubleTradeRs);
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 修改交易记录状态
	 * @param request
	 * @param response
	 * @return String
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/batchUpdateRsRemark.do")
	public String batchUpdateRsRemark(HttpServletRequest request, HttpServletResponse response) {
		//保存
		String result = "error";
		try {
			String handleFlag = request.getParameter("handleFlag");
			String remark = request.getParameter("remark");
			String tids = request.getParameter("tids");
			if (StringUtil.isNullStr(tids) || "null".equals(tids)) {
				return "paramError";
			}

			// 如果状态改为已处理，则先判断是否已上传录音文件
			if ("2".equals(handleFlag)) {
				String[] arrayTid = tids.split(",");
				for (String tid : arrayTid) {
					boolean isUploaded = isUploadedFile(tid);
					if (!isUploaded) {
						result = "noUploadedFile";
						return result;
					}
				}
			}

			// 获取登陆用户信息
			HttpSession session = request.getSession();
			String userId = (String) session.getAttribute("userId");
			CmDoubleTradeRs cmDoubleTradeRs = new CmDoubleTradeRs();
			cmDoubleTradeRs.setModifier(userId);
			cmDoubleTradeRs.setModDt(DateTimeUtil.getCurDateTime());
			cmDoubleTradeRs.setHandleFlag(handleFlag);

			// 如果备注信息不为空，则新增一条备注信息
			CmDoubleRemark cmDoubleRemark = null;
			if (StringUtils.isNotBlank(remark) && !"null".equals(remark)) {
				cmDoubleRemark = new CmDoubleRemark();
				cmDoubleRemark.setTid(tids);
				cmDoubleRemark.setRemark(remark);
				cmDoubleRemark.setCreator(userId);
				cmDoubleRemark.setCreDt(DateTimeUtil.getCurDateTime());
				cmDoubleRemark.setDeptFlag("RS");
			}
			cmDoubleTradeRsService.updateBatchCmDoubleTradeRs(cmDoubleTradeRs, cmDoubleRemark);
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 修改是否需要处理状态
	 * @param request
	 * @param response
	 * @return String
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/batchUpdateRecstate.do")
	public String batchUpdateRecstate(HttpServletRequest request, HttpServletResponse response) {
		//保存
		String result = "error";
		try {
			String id = request.getParameter("id");
			String recState = request.getParameter("recState");
			String handleFlag = request.getParameter("handleFlag");
			if (StringUtils.isBlank(id) || (StringUtils.isBlank(recState) && StringUtils.isBlank(handleFlag))) {
				return "paramError";
			}

			CmDoubleTrade cmDoubleTrade = new CmDoubleTrade();
			cmDoubleTrade.setId(id);

			// 设置记录删除状态
			if (StringUtils.isNotBlank(recState)) {
				cmDoubleTrade.setRecState(recState);
			}

			// 设置记录处理状态
			if (StringUtils.isNotBlank(handleFlag)) {
				cmDoubleTrade.setHandleFlag(handleFlag);
			}

			// 获取登陆用户信息
			HttpSession session = request.getSession();
			String userId = (String) session.getAttribute("userId");
			cmDoubleTrade.setModifier(userId);
			cmDoubleTrade.setModDt(new Date());

			cmDoubleTradeService.updateCmDoubleTrade(cmDoubleTrade);
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 判断订单是否已上传录音文件
	 * @param tid
	 * @return boolean
	 */
	public boolean isUploadedFile(String tid) {
		boolean isUploaded = false;
		// 查询录音文件是否已经存在
		Map<String, String> param = new HashMap<String, String>(1);
		// 文件编号
		param.put("tid", tid);
		List<CmDoubleTradeRFile> listExistFile = cmDoubleFileService.listCmDoubleTradeRFile(param);
		if (listExistFile != null && listExistFile.size() > 0) {
			isUploaded = true;
		}
		return isUploaded;
	}


	/**
	 * 添加拜访记录
	 * @param request
	 * @param response
	 * @return String
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/saveVisitRec.do")
	public String saveVisitRec(HttpServletRequest request, HttpServletResponse response) {
		// 保存
		String result = "error";
		try {
			String conscustNo = request.getParameter("conscustNo");
			String selectText = request.getParameter("selectText");
			String commContent = request.getParameter("commContent");
			String contractNo = request.getParameter("contractNo");

			// 获取登陆用户信息
			HttpSession session = request.getSession();
			String userId = (String) session.getAttribute("userId");
			cmDoubleTradeService.saveVisitrec(conscustNo, selectText, commContent, userId, contractNo);
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 导出投顾双录列表数据方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/exportIcDoubleExcel.do")
	public Map<String, Object> exportIcDoubleExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(1);

		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String roleFlag = request.getParameter("roleFlag");
		String startDate = request.getParameter("startDate");
		String endDate = request.getParameter("endDate");
		String handleFlag = request.getParameter("handleFlag");
		String priority = request.getParameter("priority");
		String custName = request.getParameter("custName");
		String fundName = request.getParameter("fundName");
		String fundCode = request.getParameter("fundCode");
		String conscustno = request.getParameter("conscustno");
		String searchFlag = request.getParameter("searchFlag");
		String order = request.getParameter("order");
		String recState = request.getParameter("recState");
		String orgCode2 = request.getParameter("orgCode");
		String consCode2 = request.getParameter("consCode");
		String syncDateBegin = request.getParameter("syncDateBegin");
		String syncDateEnd = request.getParameter("syncDateEnd");
		String seeLctVisit = request.getParameter("seeLctVisit");
		String isFixed = request.getParameter("isFixed");
		String queryIsLegal = request.getParameter("query_isLegal");

		// 增加查看理财通数据参数
		param.put("seeLctVisit", seeLctVisit);

		// 如果列头点了排序，则按所在列排序，否则那默认规则排序
		if (StringUtils.isNotBlank(order)) {
			param.put("order", order);
		} else {
			param.put("order", null);
		}

		// 如果查询条件（角色标识）不为空，则增加角色标识查询参数
		if (StringUtils.isNotBlank(roleFlag)) {
			param.put("roleFlag", roleFlag);
		} else {
			param.put("roleFlag", null);
		}
		// 判断是否为指定角色|售后部门负责人  售后负责人  售后作业岗 销售助理主管  总部投顾助理 IC数据分析经理 总部投顾总监
		HttpSession session = request.getSession();
		String userId = (String) session.getAttribute("userId");
		// 设置默认查询条件（双录回访状态）
		if (StringUtils.isNotBlank(searchFlag) && "true".equals(searchFlag)) {
			// 如果查询条件（双录回访状态）不为空，则增加双录回访状态查询参数
			if (StringUtils.isNotBlank(handleFlag)) {
				param.put("handleFlag", handleFlag);
			} else {
				param.put("handleFlag", null);
			}

			// 如果查询条件（开始日期）不为空，则增加开始日期查询参数
			if (StringUtils.isNotBlank(startDate)) {
				param.put("startDate", startDate);
			} else {
				param.put("startDate", null);
			}

			// 如果查询条件（结束日期）不为空，则增加结束日期查询参数
			if (StringUtils.isNotBlank(endDate)) {
				param.put("endDate", endDate);
			} else {
				param.put("endDate", null);
			}

			// 如果查询条件（入库开始时间）不为空，则增加数据入库开始时间查询参数
			if (StringUtils.isNotBlank(syncDateBegin)) {
				param.put("syncDateBegin", syncDateBegin);
			} else {
				param.put("syncDateBegin", null);
			}

			// 如果查询条件（入库结束时间）不为空，则增加数据入库结束时间查询参数
			if (StringUtils.isNotBlank(syncDateEnd)) {
				param.put("syncDateEnd", syncDateEnd);
			} else {
				param.put("syncDateEnd", null);
			}

			// 如果查询条件（是否需要处理）不为空，则增加是否需要处理查询参数
			if (StringUtils.isNotBlank(recState)) {
				param.put("recState", recState);
			} else {
				param.put("recState", "0");
			}

			// 判断所属投顾字段
			if (StringUtils.isNotBlank(consCode2) && !"ALL".equals(consCode2)) {
				// 如果不为空，并且投顾编号等于登录用户，则用登录用户权限模型
				if (consCode2.equals(userId)) {
					// 判断是否为指定角色|售后部门负责人 售后负责人 售后作业岗 销售助理主管 总部投顾助理 IC数据分析经理
					// 总部投顾总监
					Map<String, String> roleParam = new ParamUtil(request).getParamMap();
					roleParam.put("consCode", userId);
					List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
					// 如果所属投顾和登录用户不是一个，则按所属投顾进行查询
					if (listRoles != null && listRoles.size() > 0) {
						param.put("roleLevel", "leader");
					} else {
						// 获取默认登陆用户下属投顾信息
						param.put("roleLevel", "consultant");
						param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
					}
				} else {// 如果所属投顾和登录用户不是一个，则直接用所属投顾进行查询
					param.put("roleLevel", "consultant");
					StringBuffer sb = new StringBuffer();
					sb.append("'" + consCode2 + "'");
					param.put("consCode", sb.toString());
				}
				// 如果投顾编码为空，所属部门编码不为空，则用所属部门编码查询
			} else if (StringUtils.isNotBlank(orgCode2) && !"0".equals(orgCode2)) {
				param.put("roleLevel", "consultant");
				param.put("consCode", ObjectUtils.getSubQueryByOrgCode(orgCode2));
			} else {// 获取默认登陆用户下属投顾信息
				Map<String, String> roleParam = new ParamUtil(request).getParamMap();
				roleParam.put("consCode", userId);
				List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
				if (listRoles != null && listRoles.size() > 0) {
					param.put("roleLevel", "leader");
				} else {
					// 获取默认登陆用户下属投顾信息
					param.put("roleLevel", "consultant");
					param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
				}
			}
		} else {
			// 设置默认查询状态
			param.put("handleFlag", "1");

			// 设置默认查询日期
			param.put("startDate", null);
			param.put("endDate", null);
			param.put("recState", "0");

			Map<String, String> roleParam = new ParamUtil(request).getParamMap();
			roleParam.put("consCode", userId);
			List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
			if (listRoles != null && listRoles.size() > 0) {
				param.put("roleLevel", "leader");
			} else {
				// 获取默认登陆用户下属投顾信息
				param.put("roleLevel", "consultant");
				param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
			}
		}

		// 如果查询条件（优先级）不为空，则增加优先级查询参数
		if (StringUtils.isNotBlank(priority)) {
			param.put("priority", priority);
		} else {
			param.put("priority", null);
		}

		// 如果查询条件（客户名称）不为空，则增加客户名称查询参数
		if (StringUtils.isNotBlank(custName)) {
			param.put("custName", custName);
		} else {
			param.put("custName", null);
		}

		// 如果查询条件（产品名称）不为空，则增加产品名称查询参数
		if (StringUtils.isNotBlank(fundName)) {
			param.put("fundName", fundName);
		} else {
			param.put("fundName", null);
		}

		if (StringUtils.isNotBlank(fundCode)) {
			param.put("fundCode", fundCode);
		} else {
			param.put("fundCode", null);
		}

		if (StringUtils.isNotBlank(conscustno)) {
			param.put("conscustno", conscustno);
		} else {
			param.put("conscustno", null);
		}
		param.put("isFixed", isFixed);
		// 如果查询条件（质检结果）不为空，则增加质检结果查询参数
		if (StringUtils.isNotBlank(queryIsLegal)) {
			param.put("query_isLegal", queryIsLegal);
		} else {
			param.put("query_isLegal", null);
		}

		// 判断常量表中合规标识：true启用，false停用
		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
		boolean roleCpFlag = false;
		if (cacheMap != null && !cacheMap.isEmpty()) {
			roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
		}

		// 判断登录人员的角色中是否包括“合规人员”角色
		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
		if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
			param.put("hascp", "true");
		}
		String topcpdata = (String) session.getAttribute("topcpdata");
		param.put("topcpdata", topcpdata);
		param.put("doubleType",request.getParameter("doubleType") );
		List<CmDoubleTrade> listDoubleTrade = cmDoubleTradeService.listCmDoubleTrade(param);
		cmDoubleTradeService.setVisitQuality(listDoubleTrade,"1" );
		// 通过接口获取投资者类型
		Map<String, String> allUserMap=ConsOrgCache.getInstance().getAllUserMap();

		if (listDoubleTrade != null && listDoubleTrade.size() > 0) {
			for (CmDoubleTrade cmDoubleTrade : listDoubleTrade) {
				ConsOrgCache orgcache = ConsOrgCache.getInstance();
				String uporgcode = orgcache.getUpOrgMapCache().get(cmDoubleTrade.getOrgCode());
				if("0".equals(uporgcode)){
					cmDoubleTrade.setUpOrgName(cmDoubleTrade.getOrgName());
				}else{
					cmDoubleTrade.setUpOrgName(orgcache.getAllOrgMap().get(uporgcode));
				}

				if (StringUtils.isNotBlank(cmDoubleTrade.getChecker())) {
					cmDoubleTrade.setChecker(allUserMap.get(cmDoubleTrade.getChecker()));
				}
			}
			DoubleTradeExportService exportService = new DoubleTradeExportService();
			exportService.exportIcDoubleTradeByList(response, listDoubleTrade, roleFlag);
		} else {
			resultMap.put("msg", "dataError");
			return resultMap;
		}

		resultMap.put("msg", "success");
		return resultMap;
	}

	/**
	 * 导出客户和投顾双录列表数据方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/exportAuditDoubleExcel.do")
	public Map<String, Object> exportAuditDoubleExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(1);

		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String roleFlag = request.getParameter("roleFlag");
		String startDate = request.getParameter("startDate");
		String endDate = request.getParameter("endDate");
		String handleFlag = request.getParameter("handleFlag");
		//String priority = request.getParameter("priority");
		String custName = request.getParameter("custName");
		String mobile = request.getParameter("mobile");
		String fundName = request.getParameter("fundName");
		String fundCode = request.getParameter("fundCode");
		String hboneno = request.getParameter("hboneno");
		String conscustno = request.getParameter("conscustno");
		String searchFlag = request.getParameter("searchFlag");
		String order = request.getParameter("order");
		String recState = request.getParameter("recState");
		String orgCode2 = request.getParameter("orgCode");
		String consCode2 = request.getParameter("consCode");
		String syncDateBegin = request.getParameter("syncDateBegin");
		String syncDateEnd = request.getParameter("syncDateEnd");
		String seeLctVisit = request.getParameter("seeLctVisit");
		String idno = request.getParameter("idno");
		String hasAuth = request.getParameter("hasAuth");
		String isopcode = request.getParameter("isopcode");
		String doubleHandleFlag = request.getParameter("doubleHandleFlag");
		String hasVisitStatus = request.getParameter("hasVisitStatus");
		String queryVisitProblem = request.getParameter("query_visitProblem");
		String conductor = request.getParameter("conductor");
		//预约状态
		String prebookstate = request.getParameter("prebookstate");
		String queryIsLegal = request.getParameter("query_isLegal");
		if (StringUtils.isNotBlank(prebookstate)) {
			param.put("prebookstate",prebookstate);
		}else {
			param.put("prebookstate",null );
		}
		//投顾反馈回访日期
		String feedbackStartDate = request.getParameter("feedbackStartDate");
		String feedbackEndDate = request.getParameter("feedbackEndDate");
		if (StringUtils.isNotBlank(feedbackStartDate)) {
			param.put("feedbackStartDate",feedbackStartDate);
		}else {
			param.put("feedbackStartDate",null );
		}
		if (StringUtils.isNotBlank(feedbackEndDate)) {
			param.put("feedbackEndDate",feedbackEndDate);
		}else {
			param.put("feedbackEndDate",null );
		}

		if(StringUtils.isNotBlank(isopcode)){
			param.put("hasAuth", isopcode);
		}else{
			param.put("hasAuth", hasAuth);
		}
		// 增加查看理财通数据参数
		param.put("seeLctVisit", seeLctVisit);

		// 如果列头点了排序，则按所在列排序，否则那默认规则排序
		if (StringUtils.isNotBlank(order)) {
			param.put("order", order);
		} else {
			param.put("order", null);
		}

		if (StringUtils.isNotBlank(idno)) {
			param.put("idno", DigestUtil.digest(idno.trim()));
		} else {
			param.put("idno", null);
		}

		// 如果查询条件（角色标识）不为空，则增加角色标识查询参数
		if (StringUtils.isNotBlank(roleFlag)) {
			param.put("roleFlag", roleFlag);
		} else {
			param.put("roleFlag", null);
		}
		// 判断是否为指定角色|售后部门负责人  售后负责人  售后作业岗 销售助理主管  总部投顾助理 IC数据分析经理 总部投顾总监
		HttpSession session = request.getSession();
		String userId = (String) session.getAttribute("userId");
		// 设置默认查询条件（双录回访状态）
		if (StringUtils.isNotBlank(searchFlag) && "true".equals(searchFlag)) {

			// 如果查询条件（双录回访状态）不为空，则增加双录回访状态查询参数
			if (StringUtils.isNotBlank(handleFlag)) {
				param.put("handleFlag", handleFlag);
			} else {
				if(StringUtils.isNotBlank(doubleHandleFlag)){
					param.put("handleFlag", doubleHandleFlag);
				}else{
					param.put("handleFlag", null);
				}
			}

			// 如果查询条件（开始日期）不为空，则增加开始日期查询参数
			if (StringUtils.isNotBlank(startDate)) {
				param.put("startDate", startDate);
			} else {
				param.put("startDate", null);
			}

			// 如果查询条件（结束日期）不为空，则增加结束日期查询参数
			if (StringUtils.isNotBlank(endDate)) {
				param.put("endDate", endDate);
			} else {
				param.put("endDate", null);
			}

			// 如果查询条件（入库开始时间）不为空，则增加数据入库开始时间查询参数
			if (StringUtils.isNotBlank(syncDateBegin)) {
				param.put("syncDateBegin", syncDateBegin);
			} else {
				param.put("syncDateBegin", null);
			}

			// 如果查询条件（入库结束时间）不为空，则增加数据入库结束时间查询参数
			if (StringUtils.isNotBlank(syncDateEnd)) {
				param.put("syncDateEnd", syncDateEnd);
			} else {
				param.put("syncDateEnd", null);
			}

			// 如果查询条件（是否需要处理）不为空，则增加是否需要处理查询参数
			if (StringUtils.isNotBlank(recState)) {
				param.put("recState", recState);
			} else {
				param.put("recState", "0");
			}

			// 判断所属投顾字段
			if (StringUtils.isNotBlank(consCode2) && !"ALL".equals(consCode2)) {
				// 如果不为空，并且投顾编号等于登录用户，则用登录用户权限模型
				if (consCode2.equals(userId)) {
					// 判断是否为指定角色|售后部门负责人 售后负责人 售后作业岗 销售助理主管 总部投顾助理 IC数据分析经理
					// 总部投顾总监
					Map<String, String> roleParam = new ParamUtil(request).getParamMap();
					roleParam.put("consCode", userId);
					List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
					// 如果所属投顾和登录用户不是一个，则按所属投顾进行查询
					if (listRoles != null && listRoles.size() > 0) {
						param.put("roleLevel", "leader");
					} else {
						// 获取默认登陆用户下属投顾信息
						param.put("roleLevel", "consultant");
						param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
					}
				} else {// 如果所属投顾和登录用户不是一个，则直接用所属投顾进行查询
					param.put("roleLevel", "consultant");
					StringBuffer sb = new StringBuffer();
					sb.append("'" + consCode2 + "'");
					param.put("consCode", sb.toString());
				}
			// 如果投顾编码为空，所属部门编码不为空，则用所属部门编码查询
			} else if (StringUtils.isNotBlank(orgCode2) && !"0".equals(orgCode2)) {
				param.put("roleLevel", "consultant");
				param.put("consCode", ObjectUtils.getSubQueryByOrgCode(orgCode2));
			} else {// 获取默认登陆用户下属投顾信息
				Map<String, String> roleParam = new ParamUtil(request).getParamMap();
				roleParam.put("consCode", userId);
				List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
				if (listRoles != null && listRoles.size() > 0) {
					param.put("roleLevel", "leader");
				} else {
					// 获取默认登陆用户下属投顾信息
					param.put("roleLevel", "consultant");
					param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
				}
			}
		} else {

			// 设置默认查询状态
			if(StringUtils.isNotBlank(doubleHandleFlag)){
				param.put("handleFlag", doubleHandleFlag);
			}else{
				if("EC".equals(roleFlag)) {
					param.put("handleFlag", "10");
				}else {
					param.put("handleFlag", "1");
				}
			}
			param.put("recState", "0");

			Map<String, String> roleParam = new ParamUtil(request).getParamMap();
			roleParam.put("consCode", userId);
			List<HbUserrole> listRoles = hbUserRoleService.listHbRolecodeByConscode(roleParam);
			if (listRoles != null && listRoles.size() > 0) {
				param.put("roleLevel", "leader");
			} else {
				// 获取默认登陆用户下属投顾信息
				param.put("roleLevel", "consultant");
				param.put("consCode", ObjectUtils.getSubQueryByUserId(request));
			}
		}

		// 如果查询条件（客户名称）不为空，则增加客户名称查询参数
		if (StringUtils.isNotBlank(custName)) {
			param.put("custName", custName);
		} else {
			param.put("custName", null);
		}

		// 如果查询条件（联系方式）不为空，则增加联系方式查询参数
		if (StringUtils.isNotBlank(mobile)) {
			param.put("mobile", DigestUtil.digest(mobile.trim()));
		} else {
			param.put("mobile", null);
		}

		// 如果查询条件（产品名称）不为空，则增加产品名称查询参数
		if (StringUtils.isNotBlank(fundName)) {
			param.put("fundName", fundName);
		} else {
			param.put("fundName", null);
		}

		if (StringUtils.isNotBlank(fundCode)) {
			param.put("fundCode", fundCode);
		} else {
			param.put("fundCode", null);
		}

		if(StringUtils.isNoneBlank(hboneno)){
			param.put("hboneno", hboneno);
		}else{
			param.put("hboneno", null);
		}

		if(StringUtils.isNoneBlank(conscustno)){
			param.put("conscustno", conscustno);
		}else{
			param.put("conscustno", null);
		}

		// 如果查询条件（处理人）不为空，则增加处理人查询参数
		if (StringUtils.isNotBlank(conductor)) {
			param.put("conductor", conductor);
		} else {
			param.put("conductor", null);
		}

		// 如果查询条件（存在问题）不为空，则增加存在问题查询参数
		if (StringUtils.isNotBlank(queryVisitProblem)) {
			param.put("query_visitProblem", queryVisitProblem);
		} else {
			param.put("query_visitProblem", null);
		}

		// 如果查询条件（已访状态）不为空，则增加已访状态查询参数
		if (StringUtils.isNotBlank(hasVisitStatus)) {
			param.put("hasVisitStatus", hasVisitStatus);
		} else {
			param.put("hasVisitStatus", null);
		}

		// 判断常量表中合规标识：true启用，false停用
		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
		boolean roleCpFlag = false;
		if (cacheMap != null && !cacheMap.isEmpty()) {
			roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
		}

		// 判断登录人员的角色中是否包括“合规人员”角色
		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
		if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
			param.put("hascp", "true");
		}
		// 如果查询条件（质检结果）不为空，则增加质检结果查询参数
		if (StringUtils.isNotBlank(queryIsLegal)) {
			param.put("query_isLegal", queryIsLegal);
		} else {
			param.put("query_isLegal", null);
		}
		if("undefined".equals(param.get("havingRetail"))){
			param.remove("havingRetail");
		}
		String topcpdata = (String) session.getAttribute("topcpdata");
		param.put("topcpdata", topcpdata);

		param.put("doubleType",request.getParameter("doubleType") );
		List<CmDoubleTrade> listDoubleTrade = cmDoubleTradeService.exportCmDoubleTrade(param);
		cmDoubleTradeService.setVisitQuality(listDoubleTrade,"1" );
		List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
		User user = (User) request.getSession().getAttribute("loginUser");
		// 通过接口获取投资者类型
		if (listDoubleTrade != null && listDoubleTrade.size() > 0) {
			try {
				String ip = getIpAddr(request);

				Map<String, String> allUserMap=ConsOrgCache.getInstance().getAllUserMap();

				for (CmDoubleTrade cmDoubleTrade : listDoubleTrade) {
					// 通过调用接口获取投资者类型
					String hboneNo = cmDoubleTrade.getHboneNo();
					if (StringUtils.isNotBlank(hboneNo) && !"LCT".equals(cmDoubleTrade.getSystemFlag())) {
						if (StringUtils.isNotBlank(cmDoubleTrade.getChecker())) {
							cmDoubleTrade.setChecker(allUserMap.get(cmDoubleTrade.getChecker()));
						}
					}
					if(StringUtil.isNotNullStr(cmDoubleTrade.getIdNoCipher())){
						cmDoubleTrade.setIdNo(decryptSingleFacade.decrypt(cmDoubleTrade.getIdNoCipher()).getCodecText());
					}else{
						cmDoubleTrade.setIdNo("");
					}
					
					//导出日志
		            PageVisitLog pageVisitLog = new PageVisitLog();
		            pageVisitLog.setConscustno(cmDoubleTrade.getConscustNo());
		            pageVisitLog.setUserId(user.getUserId());
		            pageVisitLog.setVisitUrl(request.getRequestURI());
		            pageVisitLog.setOperation("双录审核导出");
		            pageVisitLog.setVisitTime(new Date());
		            pageVisitLog.setPreid(new BigDecimal(cmDoubleTrade.getId()));
		            pageVisitLog.setIp(ip);
		            listlog.add(pageVisitLog);
				}
				
			} catch (Exception e) {
				log.error("queryKycInfoService接口出现异常", e.getMessage());
			}

			DoubleTradeExportService exportService = new DoubleTradeExportService();
			exportService.exportAuditDoubleTradeByList(response, listDoubleTrade, roleFlag);
			for(PageVisitLog log : listlog){
            	pageVisitLogService.recordLog(log);
            }
		} else {
			resultMap.put("msg", "dataError");
			return resultMap;
		}

		resultMap.put("msg", "success");
		return resultMap;
	}
	
	/**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

	@RequestMapping("/showAuditView.do")
	public ModelAndView showAuditView(HttpServletRequest request){
		String fp = request.getParameter("fp");

		request.setAttribute("fp", fp);
		return new ModelAndView("doubletrade/AuditDoubleView");
	}

	/**
	 * 批量订单附件打包下载
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/fileDownload.do")
	public void fileDownload(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String videoDownStatus = request.getParameter("videoDownStatus");
		String fp = ObjectUtils.replaceNull(request.getParameter("fp"));
		if (StringUtils.isNotBlank(fp)) {
			if(fp.contains("%3A")){
				fp = URLDecoder.decode(fp, "utf-8");
			}
			//机构零售的可能
			fp = fp.replaceAll(" ","");
			String suffix = fp.substring(fp.lastIndexOf("."));
			if(suffix.contains("jpg") || suffix.contains("jpeg") || suffix.contains("png")){
				response.addHeader("Content-Disposition", "attachment;filename=pic" + suffix);
			}else {
				response.addHeader("Content-Disposition", "attachment;filename=video.mp4");
			}
			/*URL url = new URL(fp);
			HttpURLConnection conn = (HttpURLConnection)url.openConnection();*/
			/*//设置超时间为3秒
			conn.setConnectTimeout(3*1000);
			//防止屏蔽程序抓取而返回403错误
			conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

			//得到输入流
			InputStream inputStream = conn.getInputStream();*/

			Map<String, String> headerMap = new HashMap<>(1);
			headerMap.put("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
			//获取文件输入流
			InputStream inputStream;
			if("0".equals(videoDownStatus)){
				//云端未下载视频需要调专用方法禁止压缩
				inputStream = HttpUtils.sendHttpGetInputStream(fp);
			}else {
				inputStream = HttpUtils.getInputStream(fp, headerMap);
			}
			//获取自己数组
			byte[] getData = readInputStream(inputStream);

			//文件保存位置
			OutputStream fos = response.getOutputStream();
			if(fos!=null){
				fos.write(getData);
				fos.close();
			}
			if(inputStream!=null){
				inputStream.close();
			}

			System.out.println("info:"+fp+" download success");

		}
	}

	/**
	 * 从输入流中获取字节数组
	 * @param inputStream
	 * @return
	 * @throws IOException
	 */
	public static  byte[] readInputStream(InputStream inputStream) throws IOException {
		byte[] buffer = new byte[1024];
		int len = 0;
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		while((len = inputStream.read(buffer)) != -1) {
			bos.write(buffer, 0, len);
		}
		bos.close();
		return bos.toByteArray();
	}

	/**
	 * 功能描述: <br>
	 * <获取单个投顾回访详情>
	 * @Param: [request]
	 * @Return: java.util.Map<java.lang.String,java.lang.Object>
	 * @Author: pei.luo
	 * @Date: 2020/11/3 15:22
	 */
	@RequestMapping(value = "/getFeedbackRecord", method = RequestMethod.POST)
	@ResponseBody
	public Map<String, Object> getFeedbackRecord(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>(4);
		Map<String, String> param = new HashMap<>(4);
		String id = request.getParameter("id");
		if (StringUtils.isNotBlank(id)){
			param.put("id", id);
			CmDoubleTrade cmDoubleTrade = cmDoubleTradeService.getCmDoubleTrade(param);
			resultMap.put("cmDoubleTrade",cmDoubleTrade);
			resultMap.put("flag", Boolean.TRUE);
			resultMap.put("message", "操作成功");
		} else {
			resultMap.put("flag", Boolean.FALSE);
			resultMap.put("message", "操作失败");
		}
		return resultMap;
	}

	/**
	 * 功能描述: <br>
	 * <投顾回访>
	 * @Param: [request]
	 * @Return: java.util.Map<java.lang.String,java.lang.Object>
	 * @Author: pei.luo
	 * @Date: 2020/11/3 14:31
	 */
	@ResponseBody
	@RequestMapping(value = "/updateFeedbackRecord", method = RequestMethod.POST)
	public Map<String, Object> updateFeedbackRecord(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>(16);
		User loginUser = (User) request.getSession().getAttribute("loginUser");
		String visitStartDate = request.getParameter("visitStartDate");
		String visitEndDate = request.getParameter("visitEndDate");
		String editInvestRemark = request.getParameter("editInvestRemark");
		CmDoubleTrade cmDoubleTrade = new CmDoubleTrade();
		cmDoubleTrade.setId(request.getParameter("id"));
		cmDoubleTrade.setVisitStartDate(visitStartDate);
		cmDoubleTrade.setVisitEndDate(visitEndDate);
		cmDoubleTrade.setInvestRemark(editInvestRemark);
		cmDoubleTrade.setFeedback(loginUser.getUserId());
		cmDoubleTrade.setFeedbackTime(DateUtil.date2String(new Date(),"yyyyMMdd HH:mm:ss"));
		cmDoubleTradeService.updateCmDoubleTrade(cmDoubleTrade);
		cmOptLogService.insertLog(cmDoubleTrade.getFeedback(),StaticVar.OPT_CONS_FEDBACK,JSON.toJSON(cmDoubleTrade),cmDoubleTrade.getId());
		resultMap.put("flag", Boolean.TRUE);
		resultMap.put("message", "编辑成功");
		return resultMap;
	}

	/**
	 * 跳转到新规任务分配情况页面
	 */
	@RequestMapping("/listDoubleTradeDistribution.do")
	public ModelAndView listDoubleTradeDistribution(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/doubletrade/listDoubleTradeDistribution");
		return modelAndView;
	}


	/**
	 * 加载新规任务分配情况数据方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/listDoubleTradeDistribution_json.do")
	public Map<String, Object> listDoubleTradeDistribution(HttpServletRequest request,
														   HttpServletResponse response) throws Exception {
		int countToAssignedTasks = cmDoubleTradeService.countToAssignedTasks();
		List<String> listConductor = cmDoubleTradeService.getConductors();
		List<CmDoubleTrade> listCmDoubleTrade = new ArrayList<CmDoubleTrade>();
		int countWclCurrdayTotal = 0;
		int countWclAlldayTotal = 0;
		int countYclCurrdayTotal = 0;
		int countYclAlldayTotal = 0;
		CmDoubleTrade cmDoubleTrade = null;

		if(CollectionUtils.isNotEmpty(listConductor)){
			Map<String,String> param = new HashMap<String,String>(9);
			for(String conductor : listConductor){
				cmDoubleTrade = new CmDoubleTrade();
				cmDoubleTrade.setConductor(conductor);

				param.clear();
				param.put("conductor", conductor);
				//未处理（今）
				param.put("handleFlag", "0");
				param.put("currentDayFlag", "0");
				int countWclCurrday = cmDoubleTradeService.countDountTradeByContion(param);
				cmDoubleTrade.setCountWclCurrday(countWclCurrday);
				countWclCurrdayTotal += countWclCurrday;

				//未处理（所有）
				param.put("handleFlag", "0");
				param.put("currentDayFlag", "1");
				int countWclAllday = cmDoubleTradeService.countDountTradeByContion(param);
				cmDoubleTrade.setCountWclAllday(countWclAllday);
				countWclAlldayTotal += countWclAllday;

				//已处理（今）
				param.put("handleFlag", "1");
				param.put("currentDayFlag", "0");
				int countYclCurrday = cmDoubleTradeService.countDountTradeByContion(param);
				cmDoubleTrade.setCountYclCurrday(countYclCurrday);
				countYclCurrdayTotal += countYclCurrday;

				//已处理（所有）
				param.put("handleFlag", "1");
				param.put("currentDayFlag", "1");
				int countYclAllday = cmDoubleTradeService.countDountTradeByContion(param);
				cmDoubleTrade.setCountYclAllday(countYclAllday);
				countYclAlldayTotal += countYclAllday;

				listCmDoubleTrade.add(cmDoubleTrade);
			}
		}

		cmDoubleTrade = new CmDoubleTrade();
		cmDoubleTrade.setConductor("合计");
		cmDoubleTrade.setCountWclCurrday(countWclCurrdayTotal);
		cmDoubleTrade.setCountWclAllday(countWclAlldayTotal);
		cmDoubleTrade.setCountYclCurrday(countYclCurrdayTotal);
		cmDoubleTrade.setCountYclAllday(countYclAlldayTotal);
		listCmDoubleTrade.add(cmDoubleTrade);

		Map<String, Object> resultMap = new HashMap<String, Object>(3);
		resultMap.put("total", listCmDoubleTrade.size());
		resultMap.put("rows", listCmDoubleTrade);
		resultMap.put("countToAssignedTasks", countToAssignedTasks);
		return resultMap;
	}

	/**
	 * 修改为“无需双录”
	 * @param request
	 * @param tradeId 双录流水id
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/updateNotNeedDoubleTrade.do")
	public Map<String, Object> updateNotNeedDoubleTrade(HttpServletRequest request, String tradeId) {
		Map<String, Object> resultMap = new HashMap<>(2);
		try {
			cmDoubleTradeService.updateNotNeedDoubleTrade(tradeId);
			resultMap.put("errorCode", "0000");
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			resultMap.put("errorCode", "9999");
			resultMap.put("errorMsg", e.getMessage());
		}
		return resultMap;
	}

	/**
	 * 将双录、回访数据 置为 NEEDFLAG=0-无需处理、HANDLEFLAG=0-无需处理
	 * @param tradeId 双录流水id
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/updateToNoNeed.do")
	public ReturnMessageDto<String> updateToNoNeed(String tradeId) {
		try {
			cmDoubleTradeService.updateToNoNeed(tradeId);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return  ReturnMessageDto.fail("系统异常！");
		}
		return ReturnMessageDto.ok();
	}
	
	
	/**
	 * 查看双录审核记录方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listChecklog_json.do")
	public Map<String, Object> listChecklogJson(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		String tradeid = request.getParameter("id");
		Map<String, String> param = new HashMap<String, String>(1);
		if (StringUtils.isNotBlank(tradeid)) {
			param.put("tradeid", tradeid);
		} else {
			resultMap.put("msg", "error");
			return resultMap;
		}

		List<CmDoubleTradeChecklog> listFile = cmDoubleTradeChecklogService.listCmDoubleTradeChecklog(param);
		if(CollectionUtils.isNotEmpty(listFile)){
			ConsOrgCache orgcache = ConsOrgCache.getInstance();
			for(CmDoubleTradeChecklog cmDoubleTradeChecklog : listFile){
				if(StringUtils.isNotBlank(cmDoubleTradeChecklog.getChecker())){
					cmDoubleTradeChecklog.setChecker(orgcache.getAllConsMap().get(cmDoubleTradeChecklog.getChecker()));
				}
				if("2".equals(cmDoubleTradeChecklog.getHandleflag())){
					cmDoubleTradeChecklog.setHandleflag("审核通过");
				}
				if("4".equals(cmDoubleTradeChecklog.getHandleflag())){
					cmDoubleTradeChecklog.setHandleflag("审核不通过");
				}
			}
		}
		resultMap.put("msg", "success");
		resultMap.put("rows", listFile);
		return resultMap;
	}

	/**
	 * @api {POST} /doubletrade/batchUpdateToNoNeed.do batchUpdateToNoNeed()
	 * @apiVersion 1.0.0
	 * @apiGroup CmDoubleRecordController
	 * @apiName batchUpdateToNoNeed()
	 * @apiDescription 批量无需处理
	 * @apiParam (请求参数) {String} tradeIds
	 * @apiParamExample 请求参数示例
	 * tradeIds=RF7Kzgk1q
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"Etn","returnMsg":"dMXZtKDWaG","returnObject":"DoxWWVxEl9","returnList":["8b14OF"]}
	 */
	@ResponseBody
    @PostMapping("/batchUpdateToNoNeed.do")
	public ReturnMessageDto<String> batchUpdateToNoNeed(String tradeIds, HttpServletRequest request) {
		try {
			String userId = (String) request.getSession().getAttribute("userId");
			if (StringUtils.isBlank(tradeIds)) {
				return ReturnMessageDto.fail("参数不能为空");
			}

			String[] idArray = tradeIds.split(",");
			cmDoubleTradeService.batchUpdateToNoNeed(Arrays.asList(idArray), userId);

			return ReturnMessageDto.ok("处理成功");
		} catch (Exception e) {
			log.error("批量无需处理失败", e);
			return ReturnMessageDto.fail("系统异常！");
		}
	}

}