package com.howbuy.crm.hb.web.controller.cache;

import com.howbuy.cachemanagement.service.CacheService;
import com.howbuy.cachemanagement.service.CacheServiceImpl;
import com.howbuy.crm.hb.service.lock.LockService;
import com.howbuy.crm.page.framework.context.SpringBeanUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 缓存3.0
 */
public class CacheUtil {

	private static final Logger logger = LoggerFactory.getLogger(CacheUtil.class);

	private static final CacheService cacheService = CacheServiceImpl.getInstance();

	private static LockService lockService = SpringBeanUtil.getBean(LockService.class);

	/**
	 * @description: 获取缓存
	 * @param cacheCode	 缓存code
	 * @param key	 缓存key
	 * @return java.lang.Object
	 * @author: hongdong.xie
	 * @date: 2023/12/21 10:02
	 * @since JDK 1.8
	 */
	public static Object getObject(CacheCode cacheCode, String key) {
		Object object = null;
		try {
			object = cacheService.get(cacheCode.keyPrefix + key);
		} catch (Exception e) {
			logger.error("CacheGetException , " + e.getMessage(), e);
		}
		return object;
	}

	/**
	 * @description: 设置缓存
	 * @param cacheCode	 缓存code
	 * @param key	 缓存key
	 * @param value	 缓存value
	 * @return void
	 * @author: hongdong.xie
	 * @date: 2023/12/21 10:02
	 * @since JDK 1.8
	 */
	public static void setObject(CacheCode cacheCode, String key, Object value) {
		try {
			String k = cacheCode.keyPrefix + key;
			cacheService.put(k, value);
			cacheService.expires(k,cacheCode.timeOut);
		} catch (Exception e) {
			logger.error("CachePutException , " + e.getMessage(), e);
		}
	}

	/**
	 * @description: 删除缓存
	 * @param cacheCode	 缓存code
	 * @param key	 缓存key
	 * @return void
	 * @author: hongdong.xie
	 * @date: 2023/12/21 10:02
	 * @since JDK 1.8
	 */
	public static void deleteOne(CacheCode cacheCode, String key) {
		try {
			cacheService.remove(cacheCode.keyPrefix + key);
		} catch (Exception e) {
			logger.error("CacheDelException , " + e.getMessage(), e);
		}
	}

	/**
	 * @description: 获取锁
	 * @param cacheCode	 缓存code
	 * @param key	 锁的唯一标识
	 * @return boolean
	 * @author: hongdong.xie
	 * @date: 2023/12/21 10:01
	 * @since JDK 1.8
	 */
	public static boolean lock(CacheCode cacheCode, String key) {

		return getLockService().getLock(cacheCode.keyPrefix + key, cacheCode.timeOut);

	}

	/**
	 * @description: 释放锁
	 * @param cacheCode 缓存code
	 * @param key 锁的唯一标识
	 * @author: hongdong.xie
	 * @date: 2023/12/21 10:01
	 * @since JDK 1.8
	 */
	public static void unlock(CacheCode cacheCode, String key) {
		getLockService().releaseLock(cacheCode.keyPrefix + key);
	}

	/**
	 * @description: 获取锁服务类
	 * @return com.howbuy.crm.hb.service.lock.LockService
	 * @author: hongdong.xie
	 * @date: 2023/12/21 09:59
	 * @since JDK 1.8
	 */
	private static LockService getLockService(){
		if(lockService == null){
			lockService = SpringBeanUtil.getBean(LockService.class);
		}
		return lockService;
	}


}
