package com.howbuy.crm.hb.web.controller.custinfo;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.domain.system.HbOrganization;
import com.howbuy.crm.hb.domain.usergroup.CmCustomizegroup;
import com.howbuy.crm.hb.domain.usergroup.CmCustomizegroupUser;
import com.howbuy.crm.hb.domain.usergroup.CmCustomizegroupUserHis;
import com.howbuy.crm.hb.persistence.common.CommonMapper;
import com.howbuy.crm.hb.service.system.HbOrganizationService;
import com.howbuy.crm.hb.service.usergroup.CmCustomizegroupService;
import com.howbuy.crm.hb.service.usergroup.CmCustomizegroupUserHisService;
import com.howbuy.crm.hb.service.usergroup.CmCustomizegroupUserService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ExcelUtils;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import jxl.Sheet;
import jxl.Workbook;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * @Description: Controller
 * <AUTHOR> @version 1.0
 * @created 
 */
@Slf4j
@Controller
@RequestMapping("/custgroup")
public class CustgroupController extends BaseController {
	
	public static final String DELFLAG_ZERO = "0";	// 删除标识0：正常数据
	public static final String DELFLAG_ONE = "1";	// 删除标识1：删除数据
	@Autowired
	private CmCustomizegroupService cmCustomizegroupService;
	
	@Autowired
	private HbOrganizationService hbOrganizationService;
	
	@Autowired
	private CmCustomizegroupUserService cmCustomizegroupUserService;
	
	@Autowired
	private CmCustomizegroupUserHisService cmCustomizegroupUserHisService;
	
	@Autowired
	private CommonMapper commonMapper;
	
	@Autowired
    private QueryConscustInfoService queryConscustInfoService;
	
	private final String Cust_GROUP_IMPORT_TEMPLATE_NAME = "自定义组导入客户模板.xls";
	
	/**
	 * 跳转到自定义分组页面
	 */
	@RequestMapping("/listCmCustGroup.do")
	public String listCmCustGroup(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
		User userlogin = (User)request.getSession().getAttribute("loginUser");
		String outletcode = ConsOrgCache.getInstance().getUser2OutletMap().get(userlogin.getUserId());
		String tearmcode = ConsOrgCache.getInstance().getCons2TeamMap().get(userlogin.getUserId());
		String userLevel="";	// 登陆用户级别字段：0代表CEO，1代表部门或团队，2代表投顾
		String groupOrgCode = "";	// 登陆用户所属的部门编码
		String loginOrgCode = StringUtil.isNotBlank(tearmcode) ? tearmcode : outletcode;	// 登陆用户所属的部门编码
		String topgd = (String)request.getSession().getAttribute("topgddata");
		if(StaticVar.DATARANGE_GD_ALL.equals(topgd)||StaticVar.DATARANGE_GD_ALL_NOWFP.equals(topgd)){
			userLevel="0";
			groupOrgCode = "0";
		}else if(StaticVar.DATARANGE_GD_OUTLET.equals(topgd)){
			userLevel="1";
			groupOrgCode = outletcode;
		}else if(StaticVar.DATARANGE_GD_TEARM.equals(topgd)){
			userLevel="1";
			groupOrgCode = tearmcode;
		}else{
			userLevel="2";
			groupOrgCode = loginOrgCode;
		}
		
		
		String opDept = "false";// 判断是否是拥有创建修改部门组权限
		String opTeam = "false";// 判断是否是拥有创建修改团队组权限
		String opUpAndDel = "false";// 判断是否是拥有修改删除组权限
		List<String> userroles = (List<String>)request.getSession().getAttribute("loginRoles");
		String menucode = "020133";
		
		String opercode = "2";
		for(String role : userroles){
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menucode);
			if(temp != null && temp.contains(opercode)){
				opDept = "true";
				break;
			}
		}
		
		opercode = "3";
		for(String role : userroles){
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menucode);
			if(temp != null && temp.contains(opercode)){
				opTeam = "true";
				break;
			}
		}
		
		opercode = "4";
		for(String role : userroles){
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menucode);
			if(temp != null && temp.contains(opercode)){
				opUpAndDel = "true";
				break;
			}
		}
		request.setAttribute("opDept", opDept);
		request.setAttribute("opTeam", opTeam);
		request.setAttribute("opUpAndDel", opUpAndDel);
		
		request.setAttribute("userId", userlogin.getUserId());
		request.getSession().setAttribute("userLevel", userLevel);
		request.getSession().setAttribute("groupOrgCode", groupOrgCode);
		request.getSession().setAttribute("loginOrgCode", loginOrgCode);
		request.getSession().setAttribute("userlogin", userlogin);
		return "usergroup/listCmCustGroup";
	}
	
	
	/**
	 * 加载自定义分组页面数据方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listCmCustGroup_json.do")
	public Map<String, Object> listCmCustGroup_json(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		
		// 获取分页参数
		param = new ParamUtil(request).getParamMap();
		String groupname = request.getParameter("groupname");
		String grouplevel = request.getParameter("grouplevel");
		String userLevel = (String)request.getSession().getAttribute("userLevel");
		String groupOrgCode = (String)request.getSession().getAttribute("groupOrgCode");
		String loginOrgCode = (String)request.getSession().getAttribute("loginOrgCode");
		String orgcode = (String)request.getParameter("orgcode");

		User userlogin = (User)request.getSession().getAttribute("userlogin");
		if(StringUtil.isNotBlank(orgcode)){
			param.put("queryorgcode", orgcode);
		}else{
			param.put("queryorgcode", null);
		}
		// 如果查询条件（客户组名称）不为空，则增加客户组名称查询参数
		if(StringUtil.isNotBlank(groupname)){
			param.put("groupname", groupname);
		}else{
			param.put("groupname", null);
		}
		// 如果查询条件（客户组级别）不为空，则增加客户组级别查询参数
		if(StringUtil.isNotBlank(grouplevel)){
			param.put("grouplevel", grouplevel);
		}else{
			param.put("grouplevel", null);
		}
		
		// 获取登陆用户信息
		HttpSession session = request.getSession();
		String userId = (String)session.getAttribute("userId");
		param.put("loginUser", userId);
		if(StringUtil.isBlank(loginOrgCode)){
			loginOrgCode = userlogin.getOutletcode();
		}
		param.put("loginOrgCode", loginOrgCode);
		
		// 查询条件中投顾级别判断，投顾单独分一组，其他归为一组（包括CEO、部门组和团队组）
		param.put("userLevel", userLevel);
		if(StringUtil.isNotBlank(userLevel) && ("0".equals(userLevel) || "1".equals(userLevel))){
			// 如果客户对应部门（机构编码）不为空，则增加客户组机构编码查询参数
			if(StringUtil.isNotBlank(groupOrgCode)){
				param.put("orgCode", groupOrgCode);
			}else{
				param.put("orgCode", null);
			}
		}
		
		
		PageData<CmCustomizegroup> custData = cmCustomizegroupService.listCustgroupByPage(param);
		// 对列表数据字段进行转义
		ConstantCache constantCache= ConstantCache.getInstance();
		for(CmCustomizegroup custgroup : custData.getListData()){
			String org = custgroup.getOrgcode();
			// 转义部门名称字段
			if("0".equals(custgroup.getGrouplevel())){//个人组
				custgroup.setOrgcodez("-");
				custgroup.setTeamcodez("-");
			}else{
				if("1".equals(custgroup.getGrouplevel())){//部门组
					custgroup.setTeamcodez("-");
					if(StringUtil.isNotBlank(org)){
						custgroup.setOrgcodez(ConsOrgCache.getInstance().getAllOrgMap().get(org));
					}
				}else{//团队组
					if(StringUtil.isNotBlank(org)){
						custgroup.setTeamcodez(ConsOrgCache.getInstance().getAllOrgMap().get(org));
						param.clear();
						param.put("orgcode", org);
						HbOrganization hbOrganization = hbOrganizationService.getHbOrganization(param);
						if(hbOrganization != null && StringUtils.isNotBlank(hbOrganization.getParentorgcode())){
							custgroup.setOrgcodez(ConsOrgCache.getInstance().getAllOrgMap().get(hbOrganization.getParentorgcode()));
						}
					}
				}
			}
			// 转义创建人字段
			if(StringUtil.isNotBlank(custgroup.getCreator())){
				custgroup.setCreatorName(ConsOrgCache.getInstance().getAllUserMap().get(custgroup.getCreator()));
			}
			custgroup.setGrouplevelz(constantCache.getVal("grouplevel", custgroup.getGrouplevel()));//组类型
			//custgroup.setLoginUser(ConsOrgCache.getInstance().getAllUserMap().get(userId));
			//custgroup.setPublictypez("0".equals(custgroup.getGrouplevel()) ? "-" : constantCache.getVal("publictype", custgroup.getPublictype()));//公开类型
		}
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", custData.getPageBean().getTotalNum());
		resultMap.put("rows", custData.getListData());
		
		return resultMap;
	}
	
	
	/**
	 * 自定义分组保存方法
	 * @param request
	 * @param response
	 * @return String
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/saveCustGroup.do")
	public Map <String,Object> saveCustGroup(HttpServletRequest request, HttpServletResponse response) throws IOException {
		Map <String,Object> retmap = new HashMap<String,Object>();
    	retmap.put("errorMsg", "操作成功");
    	retmap.put("errorCode", "0000");

		String groupname = request.getParameter("groupname");
		String grouplevel = request.getParameter("grouplevel");//0:个人，1：部门，2：团队
		String orgcode = request.getParameter("orgcode");
		String publictype = request.getParameter("publictype");
		String memo = request.getParameter("memo");
		
		Map<String,String> param = new HashMap<String,String>();
		if(StringUtils.isNotEmpty(orgcode) && StringUtils.isNotEmpty(grouplevel)){
			param.put("orgcode", orgcode);
			HbOrganization hbOrganization = hbOrganizationService.getHbOrganization(param);
			if(hbOrganization != null){
				String orgtype = hbOrganization.getOrgtype();//部门级别0：部门/组织；1：团队（组）
				if(!"0".equals(orgtype) && "1".equals(grouplevel)){
					retmap.put("errorMsg", "请选择部门！");
			    	retmap.put("errorCode", "9999");
			    	return retmap;
				}else if(!"1".equals(orgtype) && "2".equals(grouplevel)){
					retmap.put("errorMsg", "请选择团队！");
			    	retmap.put("errorCode", "9999");
			    	return retmap;
				}
			}else{
				retmap.put("errorMsg", "未查询到所属部门");
		    	retmap.put("errorCode", "9999");
		    	return retmap;
			}
		}
		
		param.clear();
		param.put("groupname", groupname);
		param.put("delflag", "0");//有效
		List<CmCustomizegroup> usergroupList = cmCustomizegroupService.listCustgroup(param);
		if(usergroupList != null && usergroupList.size() != 0){
			retmap.put("errorMsg", "组名称重复！");
	    	retmap.put("errorCode", "9999");
	    	return retmap;
		}
		
		String groupid = commonMapper.getSeqValue("SEQ_CUSTGROUP_GROUPID").toString();// 从序列中获取分组编号
		try {
			if(StringUtil.isNotBlank(groupid) && StringUtil.isNotBlank(groupname)){
				CmCustomizegroup custgroup = new CmCustomizegroup();
				custgroup.setGroupid(groupid);
				custgroup.setGroupname(groupname);
				custgroup.setGrouplevel(grouplevel);
				if(StringUtil.isNotBlank(orgcode)){
					custgroup.setOrgcode(orgcode);
				}
				if(StringUtil.isNotBlank(publictype)){
					custgroup.setPublictype(publictype);
				}
				custgroup.setMemo(memo);
				custgroup.setDelflag(DELFLAG_ZERO);
				// 获取登陆用户信息
				HttpSession session = request.getSession();
				String userId = (String)session.getAttribute("userId");
				if (userId != null) {
					custgroup.setCreator(userId);
				}
				custgroup.setCredt(DateUtil.date2String(new Date(),DateUtil.SHORT_DATEPATTERN));
				cmCustomizegroupService.insertCustgroup(custgroup);
			}
		} catch (Exception e) {
			e.printStackTrace();
			retmap.put("errorMsg", "保存自定义组报错！");
	    	retmap.put("errorCode", "9999");
	    	return retmap;
		}
		return retmap;
	}

	
	/**
	 * 自定义分组删除方法（假删除，只修改删除标识）
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 */
	@ResponseBody
	@RequestMapping("/deleteCustGroup.do")
	public Map<String, Object> delete(HttpServletRequest request,
			HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("errorMsg", "操作成功");
		resultMap.put("errorCode", "0000");
		String groupid = request.getParameter("groupid");
		try {
			if(StringUtil.isNotBlank(groupid)){
				cmCustomizegroupService.delCustgroup(groupid);
			}else{
				resultMap.put("errorMsg", "参数错误");
				resultMap.put("errorCode", "9999");
			}
		} catch (Exception e) {
			resultMap.put("errorMsg", "删除操作异常");
			resultMap.put("errorCode", "9999");
		}
		return resultMap;
	}
	
	
	/**
	 * 展示修改页面
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/viewCmCustomizegroup", method = RequestMethod.POST)
	@ResponseBody
    public  Map<String, Object> viewCmCustomizegroup(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("errorMsg", "操作成功");
		resultMap.put("errorCode", "0000");
		String groupid = request.getParameter("groupid");	
		if(StringUtils.isNotBlank(groupid)){
			Map<String,String> param = new HashMap<String,String> ();
			param.put("groupid", groupid);
			CmCustomizegroup cmCustomizegroup = cmCustomizegroupService.getCustgroup(param);
			resultMap.put("domain", cmCustomizegroup);
			
		}else{
			resultMap.put("errorMsg", "操作失败：groupid不能为空");
			resultMap.put("errorCode", "9999");
		}
      
        return resultMap;
    }
	
	
	/**
	 * 自定义分组编辑方法
	 * @param request
	 * @param response
	 * @return String
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/editCustGroup.do")
	public Map<String, Object> editCustGroup(HttpServletRequest request, HttpServletResponse response) throws IOException {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("errorMsg", "操作成功");
		resultMap.put("errorCode", "0000");
		
		String groupid = request.getParameter("groupid");
		String groupname = request.getParameter("groupname");
		String grouplevel = request.getParameter("grouplevel");
		String orgcode = request.getParameter("orgcode");
		String publictype = request.getParameter("publictype");
		String memo = request.getParameter("memo");
		
		Map<String,String> param = new HashMap<String,String>();
		if(StringUtils.isNotEmpty(orgcode) && StringUtils.isNotEmpty(grouplevel)){
			param.put("orgcode", orgcode);
			HbOrganization hbOrganization = hbOrganizationService.getHbOrganization(param);
			if(hbOrganization != null){
				String orgtype = hbOrganization.getOrgtype();//部门级别0：部门/组织；1：团队（组）
				if(!"0".equals(orgtype) && "1".equals(grouplevel)){
					resultMap.put("errorMsg", "请选择部门！");
					resultMap.put("errorCode", "9999");
			    	return resultMap;
				}else if(!"1".equals(orgtype) && "2".equals(grouplevel)){
					resultMap.put("errorMsg", "请选择团队！");
					resultMap.put("errorCode", "9999");
			    	return resultMap;
				}
			}else{
				resultMap.put("errorMsg", "未查询到所属部门");
				resultMap.put("errorCode", "9999");
		    	return resultMap;
			}
			
		}
		
		param.clear();
		param.put("groupname", groupname);
		param.put("delflag", "0");//有效
		List<CmCustomizegroup> usergroupList = cmCustomizegroupService.listCustgroup(param);
		if(CollectionUtils.isNotEmpty(usergroupList) && !usergroupList.get(0).getGroupid().equals(groupid)){
			resultMap.put("errorMsg", "组名称重复！");
			resultMap.put("errorCode", "9999");
	    	return resultMap;
		}
		
		try {
			if(StringUtil.isNotBlank(groupid)){
				CmCustomizegroup custgroup = new CmCustomizegroup();
				custgroup.setGroupid(groupid);
				custgroup.setGroupname(groupname);
				custgroup.setGrouplevel(grouplevel);
				if(StringUtils.isNotBlank(publictype)) {
					custgroup.setPublictype(publictype);
				}
				if(StringUtil.isNotBlank(orgcode)){
					custgroup.setOrgcode(orgcode);
				}
				custgroup.setMemo(memo);
				custgroup.setModdt(DateUtil.date2String(new Date(),DateUtil.SHORT_DATEPATTERN));
				// 获取登陆用户信息
				HttpSession session = request.getSession();
				String userId = (String)session.getAttribute("userId");
				if (userId != null) {
					custgroup.setModifier(userId);
				}
				cmCustomizegroupService.updateByPrimaryKey(custgroup);
			}else{
				resultMap.put("errorMsg", "操作失败：groupid不能为空");
				resultMap.put("errorCode", "9999");
			}
		} catch (Exception e) {
			e.printStackTrace();
			resultMap.put("errorMsg", "修改自定义组报错！");
			resultMap.put("errorCode", "9999");
	    	return resultMap;
		}
		return resultMap;
	}
	
	
	/**
	 * 跳转到组成员列表页面
	 */
	@RequestMapping("/listCustGroupUser.do")
	public String listCustGroupUser(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
		String groupid = request.getParameter("groupid");
		String loginOrgCode = request.getParameter("loginOrgCode");
		String userLevel = request.getParameter("userLevel");
		String orgCode = request.getParameter("orgCode");
		String loginUser = request.getParameter("loginUser");

		request.setAttribute("groupid", groupid);
		request.setAttribute("loginOrgCode",loginOrgCode);
		request.setAttribute("userLevel", userLevel);
		request.setAttribute("orgCode",orgCode);
		request.setAttribute("loginUser",loginUser);
		
		return "usergroup/listCmCustGroupUser";
	}
	
	
	/**
	 * 加载组成员页面数据方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listCustGroupUser_json.do")
	public Map<String, Object> listCustGroupUser_json(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		// 获取分页参数
		param = new ParamUtil(request).getParamMap();
		if("consname".equals(param.get("sort"))){
			param.put("sort", "conscode");
		}

		String conscustno = request.getParameter("conscustno");
		String custname = request.getParameter("custname");
		String mobile = request.getParameter("mobile");
		String qryOrgCode = request.getParameter("qryOrgCode");
		String conscode = request.getParameter("conscode");
		
		String groupid = request.getParameter("groupid");
		String loginOrgCode = request.getParameter("loginOrgCode");
		String userLevel = request.getParameter("userLevel");
		String orgCode = request.getParameter("orgCode");
		String loginUser = request.getParameter("loginUser");
		param.put("loginOrgCode", "null".equals(loginOrgCode) ? null : loginOrgCode );
		param.put("userLevel", "null".equals(userLevel) ? null : userLevel );
		param.put("orgCode", "null".equals(orgCode) ? null : orgCode);
		param.put("loginUser", "null".equals(loginUser) ? null : loginUser );
		
		if(StringUtil.isNotBlank(groupid)){
			param.put("groupid", groupid);
		}else{
			param.put("groupid", null);
		}
		if(StringUtil.isNotBlank(conscustno)){
			param.put("conscustno", conscustno);
		}else{
			param.put("conscustno", null);
		}
		if(StringUtil.isNotBlank(custname)){
			param.put("custname", custname);
		}else{
			param.put("custname", null);
		}
		// 如果查询条件（客户手机）不为空，则增加客户手机查询参数
		if(StringUtil.isNotBlank(mobile)){
			param.put("mobile", DigestUtil.digest(mobile.trim()));
		}else{
			param.put("mobile", null);
		}
		if(StringUtil.isNotBlank(qryOrgCode)){
			param.put("qryOrgCode", qryOrgCode);
		}else{
			param.put("qryOrgCode", null);
		}
		if(StringUtil.isNotBlank(conscode)){
			param.put("conscode", conscode);
		}else{
			param.put("conscode", null);
		}
		
		PageData<CmCustomizegroupUser> custData = cmCustomizegroupUserService.listCustgroupUserByPage(param);
		Map<String, String> provCityMap = ConstantCache.getInstance().getProvCityMap();
		// 从缓存中获取所在地区信息
		for(CmCustomizegroupUser custgroupUser : custData.getListData()){
			String proCity = "";
			String provCode = custgroupUser.getProvcode();
			String cityCode = custgroupUser.getCitycode();
			if(StringUtil.isNotBlank(provCode) && provCityMap.containsKey(provCode) && !"10".equals(provCode) && !"20".equals(provCode) && !"30".equals(provCode) && !"63".equals(provCode)){
				proCity += provCityMap.get(provCode);
			}
			if(StringUtil.isNotBlank(cityCode) && provCityMap.containsKey(cityCode)){
				proCity += provCityMap.get(cityCode);
			}
			custgroupUser.setProvcode(proCity);
			
			// 对手机号码进行加密（根据拥有者权限）
			custgroupUser.setMobile(Util.getEncryptValue(request, StaticVar.ENCRYPT_MOBILE, custgroupUser.getMobile(), custgroupUser.getConscode(), custgroupUser.getSeniormgrcode()));
			
			// 对固话号码进行加密（根据拥有者权限）
			custgroupUser.setTelno(Util.getEncryptValue(request, StaticVar.ENCRYPT_PHONE, custgroupUser.getTelno(), custgroupUser.getConscode(), custgroupUser.getSeniormgrcode()));
			
			// 对电子邮件进行加密（根据拥有者权限）
			custgroupUser.setEmail(Util.getEncryptValue(request, StaticVar.ENCRYPT_EMAIL, custgroupUser.getEmail(), custgroupUser.getConscode(), custgroupUser.getSeniormgrcode()));
			
			// 转义投顾姓名字段
			if(StringUtil.isNotBlank(custgroupUser.getConscode())){
				custgroupUser.setConsname(ConsOrgCache.getInstance().getAllConsMap().get(custgroupUser.getConscode()));
				String outletcode = ConsOrgCache.getInstance().getCons2OutletMap().get(custgroupUser.getConscode());
				/*String tearmcode = ConsOrgCache.getInstance().getCons2TeamMap().get(custgroupUser.getConscode());
				custgroupUser.setOrgcode(ConsOrgCache.getInstance().getAllOrgMap().get(StringUtil.isNotBlank(tearmcode) ? tearmcode : outletcode));*/
				
				custgroupUser.setOrgcode(StringUtil.isNotBlank(outletcode) ? ConsOrgCache.getInstance().getAllOrgMap().get(outletcode) : null);
			}
		}
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", custData.getPageBean().getTotalNum());
		resultMap.put("rows", custData.getListData());
		
		return resultMap;
	}
	
	
	
	/**
	 * 分组用户删除方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 */
	@ResponseBody
	@RequestMapping("/deleteGroupUserByIds.do")
	public Map<String, Object> deleteGroupUserByIds(HttpServletRequest request,
			HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		String groupid = request.getParameter("groupid");
		String ids = request.getParameter("ids");
		try {
			if(StringUtil.isNotBlank(groupid)&&StringUtil.isNotBlank(ids)){
				Map<String, String> param = new HashMap<String, String>();
				param.put("groupid", groupid);
				param.put("ids", ids);
				param.put("enddt", DateTimeUtil.getCurDate());
				HttpSession session = request.getSession();// 获取登陆用户信息
				String userId = (String)session.getAttribute("userId");
				param.put("modifier", userId);
				cmCustomizegroupUserService.delListCustgroupUser(param);
				resultMap.put("msg", "success");
			}else{
				resultMap.put("msg", "paramError");
			}
		} catch (Exception e) {
			resultMap.put("msg", "error");
		}
		return resultMap;
	}
	
	/**
	 * 分组用户删除方法
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 */
	@ResponseBody
	@RequestMapping("/deleteGroupUserByAll.do")
	public Map<String, Object> deleteGroupUserByAll(HttpServletRequest request,
			HttpServletResponse response) {
		 // 设置查询参数
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Map<String, String> param = new HashMap<String, String>();
		String groupid = request.getParameter("groupid");
		String conscustno = request.getParameter("conscustno");
		String custname = request.getParameter("custname");
		String mobile = request.getParameter("mobile");
		String qryOrgCode = request.getParameter("qryOrgCode");
		String conscode = request.getParameter("conscode");
		
		String loginOrgCode = request.getParameter("loginOrgCode");
		String userLevel = request.getParameter("userLevel");
		String orgCode = request.getParameter("orgCode");
		String loginUser = request.getParameter("loginUser");
		
		if(StringUtil.isNotBlank(groupid)){
			param.put("groupid", groupid);
		}else{
			param.put("groupid", null);
		}
		if(StringUtil.isNotBlank(conscustno)){
			param.put("conscustno", conscustno);
		}else{
			param.put("conscustno", null);
		}
		if(StringUtil.isNotBlank(custname)){
			param.put("custname", custname);
		}else{
			param.put("custname", null);
		}
		// 如果查询条件（客户手机）不为空，则增加客户手机查询参数
		if(StringUtil.isNotBlank(mobile)){
			param.put("mobile", DigestUtil.digest(mobile));
		}else{
			param.put("mobile", null);
		}
		if(StringUtil.isNotBlank(qryOrgCode)){
			param.put("qryOrgCode", qryOrgCode);
		}else{
			param.put("qryOrgCode", null);
		}
		if(StringUtil.isNotBlank(conscode)){
			param.put("conscode", conscode);
		}else{
			param.put("conscode", null);
		}
		
		if(StringUtil.isNotBlank(loginOrgCode)){
			param.put("loginOrgCode", loginOrgCode);
		}else{
			param.put("loginOrgCode", null);
		}
		if(StringUtil.isNotBlank(userLevel)){
			param.put("userLevel", userLevel);
		}else{
			param.put("userLevel", null);
		}
		if(StringUtil.isNotBlank(orgCode)){
			param.put("orgCode", orgCode);
		}else{
			param.put("orgCode", null);
		}
		if(StringUtil.isNotBlank(loginUser)){
			param.put("loginUser", loginUser);
		}else{
			param.put("loginUser", null);
		}
		
		
		List<CmCustomizegroupUser> list = cmCustomizegroupUserService.listCustgroupUserByCondition(param);
		List<String> ids = new ArrayList<String> ();
		try {
			if(StringUtil.isNotBlank(groupid)){
				Map<String,Object> par = new HashMap<String,Object> ();
				par.put("groupid", groupid);
				if(list != null && list.size() != 0){
					HttpSession session = request.getSession();// 获取登陆用户信息
					String userId = (String)session.getAttribute("userId");
					for(CmCustomizegroupUser model : list){
						ids.add(model.getCustno());
					}
					
					//分批次删除：一次1000条
                    int count = 1000;
                	int length = ids.size();
                    int size = length % count;
                    if (size == 0) {
                        size = length / count;
                    } else {
                        size = (length / count) + 1;
                    }
                    for (int i = 0; i < size; i++) {
                        int fromIndex = i * count;
                        int toIndex = Math.min(fromIndex + count, length);
                        par.put("ids", ids.subList(fromIndex, toIndex));
    					par.put("enddt", DateTimeUtil.getCurDate());
    					par.put("modifier", userId);
    					cmCustomizegroupUserService.delListCustgroupUserAll(par);
                    }
                	
                }
				resultMap.put("msg", "success");
			}else{
				resultMap.put("msg", "paramError");
			}
		} catch (Exception e) {
			resultMap.put("msg", "error");
		}
		return resultMap;
	}
	
	
	/**
	 * 导入
	 * 
	 * author: fangyuan.wu
	 * date: 2019年12月16日 下午5:24:58 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/importCustgroupUser")
	@SuppressWarnings("all")
	public Map<String, Object> importCustgroupUser(HttpServletRequest request){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		Pattern pattern = Pattern.compile("^-?\\d+$");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		User user = (User)request.getSession().getAttribute("loginUser");
		String errorMsg = "导入成功！", uploadFlag = "success";
		Workbook workBook;
		InputStream input = null;
        try {
        	String groupid = request.getParameter("groupid");
        	if(StringUtils.isEmpty(groupid)){
        		resultMap.put("uploadFlag", "error");
                resultMap.put("errorMsg", "参数错误，groupid不能为空！");
                return resultMap;
        	}
            // 转型为MultipartHttpRequest：
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            // 获得文件：
            MultipartFile file = multipartRequest.getFile("file");
            // 获得输入流：
            input = file.getInputStream();
            workBook = Workbook.getWorkbook(input);
            String[] colPropertity = {"custno"};
            Sheet sheet = workBook.getSheet(0);
            List<CmCustomizegroupUser> cmCustomizegroupUserList = new ArrayList<CmCustomizegroupUser>();
            List<CmCustomizegroupUser> rCalendarList = ExcelUtils.getListByReadShell(sheet, 1, 0, 1, colPropertity, CmCustomizegroupUser.class);
            if (null == rCalendarList || rCalendarList.isEmpty()) {
                errorMsg = "没有上传记录！";
                uploadFlag = "error";
            }else if(rCalendarList.size() > 10000){
            	errorMsg = "导入数据不允许超过10000条！";
                uploadFlag = "error";
            } else {
            	//客户表中不存在的客户
        		List<String> notExistsCust = new ArrayList<String>();
        		//该组中已有的客户
        		List<String> listhascustnos = new ArrayList<String>();
        		//筛选后的客户
        		Set<String> listsets = new HashSet<String>();
        		
            	Map<String,String> param = new HashMap<String,String>();
        		param.put("groupid", groupid);
        		List<CmCustomizegroupUser> list = cmCustomizegroupUserService.listCustgroupUser(param);
        		for(CmCustomizegroupUser obj : list){
        			listhascustnos.add(obj.getCustno());
        		}
        		
        		QueryConscustInfoRequest req = new QueryConscustInfoRequest();
                for (CmCustomizegroupUser cmCustomizegroupUser : rCalendarList) {
                	String custno = cmCustomizegroupUser.getCustno();
                	if(StringUtils.isNotBlank(custno)){
                		req.setConscustno(custno);
                		QueryConscustInfoResponse res = queryConscustInfoService.queryConscustInfo(req);
                        if(res.getConscustinfo() != null){
                        	if(!listsets.contains(custno) && !listhascustnos.contains(custno)){
                        		listsets.add(custno);
                        	}
                        }else{
                        	notExistsCust.add(custno);
                        }
                	}
                }
                
                
                if(listsets.size() > 0){
                	List<CmCustomizegroupUser> listinsert = new ArrayList<CmCustomizegroupUser>();
            		List<CmCustomizegroupUserHis> listhisinsert = new ArrayList<CmCustomizegroupUserHis>();
                	for(String custno : listsets){
        				CmCustomizegroupUser obj1 = new CmCustomizegroupUser();
        				obj1.setGroupid(groupid);
        				obj1.setCustno(custno);
        				obj1.setCreator(user.getUserId());
        				obj1.setCredt(DateTimeUtil.fmtDate(new Date(), "yyyyMMdd"));
        				obj1.setModdt("");
        				obj1.setModifier("");
        				listinsert.add(obj1);
        				CmCustomizegroupUserHis obj2 = new CmCustomizegroupUserHis();
        				obj2.setGroupid(groupid);
        				obj2.setCustno(custno);
        				obj2.setStartdt(DateTimeUtil.fmtDate(new Date(), "yyyyMMdd"));
        				obj2.setEnddt("");
        				obj2.setCreator(user.getUserId());
        				obj2.setCredt(DateTimeUtil.fmtDate(new Date(), "yyyyMMdd"));
        				obj2.setModdt("");
        				obj2.setModifier("");
        				listhisinsert.add(obj2);
        			}
                	
                	//分批次插入：一次3000条插入
                    int count = 3000;
                	int length = listinsert.size();
                    int size = length % count;
                    if (size == 0) {
                        size = length / count;
                    } else {
                        size = (length / count) + 1;
                    }
                    for (int i = 0; i < size; i++) {
                        int fromIndex = i * count;
                        int toIndex = Math.min(fromIndex + count, length);
                        cmCustomizegroupUserService.insertCustgroupUserBatch(listinsert.subList(fromIndex, toIndex));
            			cmCustomizegroupUserHisService.insertCustgroupUserHisBatch(listhisinsert.subList(fromIndex, toIndex));
                    }
                	
                }
                if(notExistsCust.size() != 0){
                	StringBuilder sb = new StringBuilder();
                	sb.append("部分导入成功:");
                	for(String conscustno : notExistsCust){
                		sb.append(conscustno + ",");
                	}
                	sb.append(sb.toString().substring(0, errorMsg.length()-1) + "不存在！");
                	errorMsg = sb.toString();
                }
            }
            resultMap.put("uploadFlag", uploadFlag);
            resultMap.put("errorMsg", errorMsg);
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "请检查模板是否正确！");
        } finally {
            try {
            	if(input != null){
            		input.close();
            	}
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
		return resultMap;
	}
	
	/**
	 * 下载自定义组导入客户模板
	 * 
	 * author: fangyuan.wu
	 * date: 2019年12月16日 下午4:23:09 
	 * @param request
	 */
	@RequestMapping("/downloadTemplate")
	public void downloadTemplate(HttpServletRequest request, HttpServletResponse response){
		dowmloadTemplate(Cust_GROUP_IMPORT_TEMPLATE_NAME,Cust_GROUP_IMPORT_TEMPLATE_NAME,request,response);
	}
	
	
	@RequestMapping("/exportCustgroupUser.do")
    public void exportCustgroupUser(HttpServletRequest request, @RequestParam Map<String, String> params,
                                            HttpServletResponse response) throws Exception {
        // 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		String groupid = request.getParameter("groupid");
		String conscustno = request.getParameter("conscustno");
		String custname = request.getParameter("custname");
		String mobile = request.getParameter("mobile");
		String qryOrgCode = request.getParameter("qryOrgCode");
		String conscode = request.getParameter("conscode");
		
		String loginOrgCode = request.getParameter("loginOrgCode");
		String userLevel = request.getParameter("userLevel");
		String orgCode = request.getParameter("orgCode");
		String loginUser = request.getParameter("loginUser");
		
		if(StringUtil.isNotBlank(groupid)){
			param.put("groupid", groupid);
		}else{
			param.put("groupid", null);
		}
		if(StringUtil.isBlank(conscustno) || "null".equals(conscustno)){
			param.put("conscustno", null);
		}else{
			param.put("conscustno", conscustno);
		}
		if(StringUtil.isBlank(custname) || "null".equals(custname)){
			param.put("custname", null);
		}else{
			param.put("custname", custname);
		}
		// 如果查询条件（客户手机）不为空，则增加客户手机查询参数
		if(StringUtil.isBlank(mobile) || "null".equals(mobile)){
			param.put("mobile", null);
		}else{
			param.put("mobile", DigestUtil.digest(mobile));
		}
		if(StringUtil.isBlank(qryOrgCode) || "null".equals(qryOrgCode)){
			param.put("qryOrgCode", null);
		}else{
			param.put("qryOrgCode", qryOrgCode);
		}
		if(StringUtil.isBlank(conscode) || "null".equals(conscode)){
			param.put("conscode", null);
		}else{
			param.put("conscode", conscode);
		}
		
		if(StringUtil.isBlank(loginOrgCode) || "null".equals(loginOrgCode)){
			param.put("loginOrgCode", null);
		}else{
			param.put("loginOrgCode", loginOrgCode);
		}
		if(StringUtil.isBlank(userLevel) || "null".equals(userLevel)){
			param.put("userLevel", null);
		}else{
			param.put("userLevel", userLevel);
		}
		if(StringUtil.isBlank(orgCode) || "null".equals(orgCode)){
			param.put("orgCode", null);
		}else{
			param.put("orgCode", orgCode);
		}
		if(StringUtil.isBlank(loginUser) || "null".equals(loginUser)){
			param.put("loginUser", null);
		}else{
			param.put("loginUser", loginUser);
		}
		
		List<CmCustomizegroupUser> list = cmCustomizegroupUserService.exportCustgroupUser(param);
		Map<String, String> provCityMap = ConstantCache.getInstance().getProvCityMap();
		// 从缓存中获取所在地区信息
		for(CmCustomizegroupUser custgroupUser : list){
			String proCity = "";
			String provCode = custgroupUser.getProvcode();
			String cityCode = custgroupUser.getCitycode();
			if(StringUtil.isNotBlank(provCode) && provCityMap.containsKey(provCode) && !"10".equals(provCode) && !"20".equals(provCode) && !"30".equals(provCode) && !"63".equals(provCode)){
				proCity += provCityMap.get(provCode);
			}
			if(StringUtil.isNotBlank(cityCode) && provCityMap.containsKey(cityCode)){
				proCity += provCityMap.get(cityCode);
			}
			custgroupUser.setProvcode(proCity);
			// 对手机号码进行加密（根据拥有者权限）
			custgroupUser.setMobile(custgroupUser.getMobileMask());
			// 对固话号码进行加密（根据拥有者权限）
			custgroupUser.setTelno(custgroupUser.getTelnoMask());
			
			// 对电子邮件进行加密（根据拥有者权限）
			custgroupUser.setEmail(custgroupUser.getEmailMask());
			// 转义投顾姓名字段
			if(StringUtil.isNotBlank(custgroupUser.getConscode())){
				custgroupUser.setConsname(ConsOrgCache.getInstance().getAllConsMap().get(custgroupUser.getConscode()));
				String outletcode = ConsOrgCache.getInstance().getCons2OutletMap().get(custgroupUser.getConscode());
				//String tearmcode = ConsOrgCache.getInstance().getCons2TeamMap().get(custgroupUser.getConscode());
				//custgroupUser.setOrgcode(ConsOrgCache.getInstance().getAllOrgMap().get(StringUtil.isNotBlank(tearmcode) ? tearmcode : outletcode));
				custgroupUser.setOrgcode(StringUtil.isNotBlank(outletcode) ? ConsOrgCache.getInstance().getAllOrgMap().get(outletcode) : null);
			}
		}
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("downName", "自定义组成员.xls");
		map.put("rows", list);
		cmCustomizegroupUserService.exportCustgroupUser(response,map);
	}
}