package com.howbuy.crm.hb.web.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ContextLoader;

import javax.annotation.PostConstruct;
import javax.servlet.ServletContext;

/**
 * <AUTHOR>
 * @description: web基础配置，过滤器读不到的在这读
 * @date 2022/4/6 14:56
 */
@Component
public class NacosWebConfig {

    @Value("${SP_URL}")
    private String spUrl;
    @Value("${IDP_URL_CHECK_TICKET}")
    private String idpUrlCheckTicket;
    @Value("${IDP_URL_LOGIN}")
    private String idpUrlLogin;
    @Value("${IDP_URL_LOGOUT_STATUS}")
    private String idpUrlLogoutStatus;

    @Value("${GLOBAL_HB_CRM}")
    private String globalHbCrm;
    @Value("${GLOBAL_CRM_SYS}")
    private String globalCrmSys;
    @Value("${GLOBAL_HB_CS}")
    private String globalHbCs;
    @Value("${GLOBAL_HB_DOC}")
    private String globalHbDoc;
    @Value("${GLOBAL_HB_CRM}")
    private String globalHbWeChat;
    @Value("${GLOBAL_CRM_TRADE_SERVER}")
    private String globalCrmTradeServer;
    @Value("${GLOBAL_CRM_WEB}")
    private String globalCrmWebUrl;
    @Value("${GLOBAL_DS_REPORT_REMOTE}")
    private String globalDsReportRemoteUrl;

    @PostConstruct
    public void setConfig(){
        System.setProperty("spUrl", spUrl);
        System.setProperty("idpUrlCheckTicket", idpUrlCheckTicket);
        System.setProperty("idpUrlLogin", idpUrlLogin);
        System.setProperty("idpUrlLogoutStatus", idpUrlLogoutStatus);
        ServletContext servletContext = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        servletContext.setAttribute("GLOBAL_HB_CRM",globalHbCrm);
        servletContext.setAttribute("GLOBAL_CRM_SYS",globalCrmSys);
        servletContext.setAttribute("GLOBAL_HB_CS",globalHbCs);
        servletContext.setAttribute("GLOBAL_HB_DOC",globalHbDoc);
        servletContext.setAttribute("GLOBAL_HB_WEBCHAT",globalHbWeChat);
        servletContext.setAttribute("GLOBAL_CRM_TRADE_SERVER",globalCrmTradeServer);
        servletContext.setAttribute("GLOBAL_CRM_WEB",globalCrmWebUrl);
        servletContext.setAttribute("GLOBAL_DS_REPORT_REMOTE",globalDsReportRemoteUrl);
    }
}
