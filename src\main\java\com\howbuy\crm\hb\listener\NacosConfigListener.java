package com.howbuy.crm.hb.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Iterator;
import java.util.Properties;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description: 宙斯读取外移文件
 * @reason:
 * @Date: 2021/4/25 13:47
 */
public class NacosConfigListener extends PropertyPlaceholderConfigurer implements ServletContextListener {

    private static Logger logger = LoggerFactory.getLogger(NacosConfigListener.class);
    @Override
    public void contextInitialized(ServletContextEvent servletContextEvent) {
        ServletContext servletContext = servletContextEvent.getServletContext();
        String realPath = servletContext.getInitParameter("nacosConfigPath");
        InputStream in = null;
        try {
            Properties properties = new Properties();
            File file = new File(realPath);
            in = new FileInputStream(file);
            properties.load(in);
            Set keyValue = properties.keySet();
            for (Iterator it = keyValue.iterator(); it.hasNext(); ) {
                String key = (String) it.next();
                servletContext.setAttribute(key, properties.getProperty(key));
                System.setProperty(key, properties.getProperty(key));
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }finally {
            if(in != null){
                try {
                    in.close();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
    }

    @Override
    public void contextDestroyed(ServletContextEvent servletContextEvent) {

    }
}
