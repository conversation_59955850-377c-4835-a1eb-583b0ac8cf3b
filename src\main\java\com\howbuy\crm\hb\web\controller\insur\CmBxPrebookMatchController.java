package com.howbuy.crm.hb.web.controller.insur;

import com.howbuy.common.utils.NumberUtil;
import com.howbuy.crm.hb.domain.insur.CmBxPrebookMatch;
import com.howbuy.crm.hb.domain.insur.CmBxPrebookMatchExcl;
import com.howbuy.crm.hb.service.insur.CmBxPrebookMatchService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ExcelUtils;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
import jxl.Sheet;
import jxl.Workbook;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 核保数据核对页controller
 * <AUTHOR>
 *
 */
@Slf4j
@Controller
@RequestMapping("/insur")
public class CmBxPrebookMatchController  extends BaseController {

	@Autowired
	private CmBxPrebookMatchService cmBxPrebookMatchService;
	private final String DOWNLOAD_FILE_NAME="数据核对导入模板.xls";
	
	private final String MODEL_FILE_NAME="bxmatchmodel.xls";
	
	
	/**
	 * 跳转到核保数据对比
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping(value="/listCmBxPrebookMatch.do",method=RequestMethod.GET)
	public ModelAndView listCmBxPrebookMatch(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/insur/listCmBxPrebookMatch");
		return modelAndView;
	}

	/**
	 * 核保数据对比列表数据
	 * @param request
	 * @return
	 * @throws Exception
	 */
    @SuppressWarnings("unchecked")
	@ResponseBody
    @RequestMapping(value="/listCmBxPrebookMatch_json.do",method=RequestMethod.POST)
    public Map<String, Object> listCmBxPrebookMatch_json(HttpServletRequest request) throws Exception {
        // 设置查询参数
    	CmBxPrebookMatch vo = buildParam(request);
        PageData<CmBxPrebookMatch> pageData = cmBxPrebookMatchService.listCmBxPrebookMatchByPage(vo);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<CmBxPrebookMatch> listdata = pageData.getListData();
        resultMap.put("rows", listdata);
        vo.setPage(new Integer(1));
        vo.setRows(new Integer(100000));
        PageData<CmBxPrebookMatch> pageDataAll = cmBxPrebookMatchService.listCmBxPrebookMatchByPage(vo);
        int allNum = 0;
        int hasMatchNum = 0;
        int notMatchNum = 0;
        int same = 0;
        int notsame = 0;
        if(pageDataAll != null && pageDataAll.getListData() != null && pageDataAll.getListData().size() > 0){
        	allNum = pageDataAll.getListData().size();
        	for(CmBxPrebookMatch bean : pageDataAll.getListData()){
        		if("0".equals(bean.getMatchflag())){
        			hasMatchNum = hasMatchNum + 1;
        			if("1".equals(bean.getComparflag())){
        				same = same + 1;
        			}else if("0".equals(bean.getComparflag())){
        				notsame = notsame + 1;
        			}
        		}else{
        			notMatchNum = notMatchNum +1;
        		}
        	}
        }
        String resultStr = "按查询条件：共计"+allNum+"条数据，其中"+hasMatchNum+"条已匹配（"+same+"条核对一致，"+notsame+"条核对不一致），"+notMatchNum+"条未匹配";
        resultMap.put("hejiStr", resultStr);
        return resultMap;
    }
    
    /**
     * 封装查询参数
     * @param request
     * @return
     * @throws Exception
     */
    private CmBxPrebookMatch buildParam(HttpServletRequest request) throws Exception{
    	CmBxPrebookMatch returnVo = new CmBxPrebookMatch();
        
    	returnVo.setMatchflag(StringUtil.getStr(request.getParameter("matchflag")));
    	returnVo.setComparflag(StringUtil.getStr(request.getParameter("comparflag")));
    	returnVo.setFundname(StringUtil.getStr(request.getParameter("fundname")));
    	if(StringUtil.isNotNullStr(returnVo.getFundname())){
    		returnVo.setFundname(returnVo.getFundname().split(" ")[1]);
    	}
    	returnVo.setStartdt(StringUtil.getStr(request.getParameter("startdt")));
    	returnVo.setEnddt(StringUtil.getStr(request.getParameter("enddt")));
    	returnVo.setCustname(StringUtil.getStr(request.getParameter("custname")));
    	returnVo.setInsurid(StringUtil.getStr(request.getParameter("insurid")));
    	returnVo.setPrestate(StringUtil.getStr(request.getParameter("prestate")));
    	returnVo.setInsurstate(StringUtil.getStr(request.getParameter("insurstate")));
    	returnVo.setPaystate(StringUtil.getStr(request.getParameter("paystate")));
    	returnVo.setCheckstate(StringUtil.getStr(request.getParameter("checkstate")));
    	//只有冷静期截止日期查询的情况，可以查询导入的数据
    	if(StringUtil.isNull(returnVo.getMatchflag()) 
    			&& StringUtil.isNull(returnVo.getComparflag())
    			&& StringUtil.isNull(returnVo.getFundname())
    			&& StringUtil.isNull(returnVo.getCustname())
    			&& StringUtil.isNull(returnVo.getInsurid())
    			&& StringUtil.isNull(returnVo.getPrestate())
    			&& StringUtil.isNull(returnVo.getInsurstate())
    			&& StringUtil.isNull(returnVo.getPaystate())
    			&& StringUtil.isNull(returnVo.getCheckstate())
    			){
    		returnVo.setOnlydt("1");
    	}
    	
    	//排序和页数
        String page=StringUtil.replaceNullStr(request.getParameter("page"));
        String rows=StringUtil.replaceNullStr(request.getParameter("rows"));
        String sort=StringUtil.replaceNullStr(request.getParameter("sort"));
        String order=StringUtil.replaceNullStr(request.getParameter("order"));
    
        if(page!=null){
            returnVo.setPage(Integer.valueOf(page));
        }
        if(rows!=null){
            returnVo.setRows(Integer.valueOf(rows));
        }
        returnVo.setSort(sort);
        returnVo.setOrder(order);
        
        return returnVo;
    }
    
    /**
     * 查询列表
     *
     * @param cmBalanceFactor
     * @return
     */
    @RequestMapping(value="/getLoadBxPrebookMatch",method=RequestMethod.POST)
    @ResponseBody
    public Object getLoadBxPrebookMatch(HttpServletRequest request) throws Exception {
    	User user = (User)request.getSession().getAttribute("loginUser");
    	CmBxPrebookMatch cmBxPrebookMatch = new CmBxPrebookMatch();
    	String fundname = URLDecoder.decode(StringUtil.getStr(request.getParameter("fundname")), "UTF-8");
    	cmBxPrebookMatch.setFundname(fundname);
    	cmBxPrebookMatch.setInsurid(StringUtil.getStr(request.getParameter("insurid")));
    	//上传页的请求
    	if("load".equals(request.getParameter("opttype"))){
    		cmBxPrebookMatch.setCreator(user.getUserId());
    	}
    	return cmBxPrebookMatchService.getLoadBxPrebookMatch(cmBxPrebookMatch,request.getParameter("opttype"));
    }
    
    /**
	 * 根据id删除导入的核保数据
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping(value="/deleteBxMatch.do",method=RequestMethod.POST)
	public String deleteBxMatch(HttpServletRequest request) {
		String result = "success";
		CmBxPrebookMatch match = new CmBxPrebookMatch();
        String id = request.getParameter("id");
        match.setId(new BigDecimal(id));
        cmBxPrebookMatchService.deleteCmBxPrebookMatch(match);
		return result;
	}
	
	/**
	 * 判断导入的核保数据与已经归档的数据是否有相同保单号和产品的数据
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping(value="/changeBxSameMatch.do",method=RequestMethod.POST)
	public String changeBxSameMatch(HttpServletRequest request) {
		String result = "";
		User user = (User)request.getSession().getAttribute("loginUser");
		CmBxPrebookMatch match = new CmBxPrebookMatch();
        match.setCreator(user.getUserId());
        List<CmBxPrebookMatch> list = cmBxPrebookMatchService.listSameMatchOpt(match);
        StringBuilder sb = new StringBuilder();
        if(list != null && list.size() > 0){
        	for(CmBxPrebookMatch bean : list){
        		sb.append(","+bean.getInsurid()+":"+bean.getFundname());
        	}
        }
        if(StringUtil.isNotNullStr(sb.toString())){
        	result = "系统已存在"+sb.toString().replaceFirst(",", "")+"的保单，是否确定要删除原数据，以此次导入为准？";
        }
		return result;
	}
	
	/**
	 * 存档处理
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping(value="/dealSaveBxMatch.do",method=RequestMethod.POST)
	public String dealSaveBxMatch(HttpServletRequest request) {
		String result = "success";
		User user = (User)request.getSession().getAttribute("loginUser");
		CmBxPrebookMatch match = new CmBxPrebookMatch();
        match.setCreator(user.getUserId());
        result = cmBxPrebookMatchService.dealSaveBxMatch(match);
        return result;
	}
	
	@RequestMapping("/loadBxMatchData.do")
	public ModelAndView loadBxMatchData(HttpServletRequest request)	throws Exception {
		
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/insur/loadBxMatchData");
		return modelAndView;
	}
	
	@RequestMapping("/downloadMode.do")
	public String downloadModel( HttpServletRequest request,
			HttpServletResponse response) {
		return dowmloadTemplate(MODEL_FILE_NAME,DOWNLOAD_FILE_NAME,request,response);
	}
    
	/**
	 * 获取刚上传的数据
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
    @RequestMapping("/queryHasLoadDataList.do")
    public Map<String, Object> queryHasLoadDataList(HttpServletRequest request) throws Exception {
		User user = (User) request.getSession().getAttribute("loginUser");
		String startdt = request.getParameter("startdt");
		String enddt = request.getParameter("enddt");
		String matchflag = request.getParameter("matchflag");
		String comparflag = request.getParameter("comparflag");
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		CmBxPrebookMatch cmBxPrebookMatch = new CmBxPrebookMatch();
		cmBxPrebookMatch.setMatchflag(StringUtil.getStr(matchflag));
		cmBxPrebookMatch.setComparflag(StringUtil.getStr(comparflag));
		cmBxPrebookMatch.setStartdt(startdt);
		cmBxPrebookMatch.setEnddt(enddt);
		cmBxPrebookMatch.setCreator(user.getUserId());
		List<CmBxPrebookMatch> list = cmBxPrebookMatchService.listMatchBxPrebookOpt(cmBxPrebookMatch);
		int allNum = 0;
        int hasMatchNum = 0;
        int notMatchNum = 0;
        int same = 0;
        int notsame = 0;
        if(list != null && list.size() > 0){
        	allNum = list.size();
        	for(CmBxPrebookMatch bean : list){
        		if("0".equals(bean.getMatchflag())){
        			hasMatchNum = hasMatchNum + 1;
        			if("1".equals(bean.getComparflag())){
        				same = same + 1;
        			}else if("0".equals(bean.getComparflag())){
        				notsame = notsame + 1;
        			}
        		}else{
        			notMatchNum = notMatchNum +1;
        		}
        	}
        }
        String resultStr = "按本次上传的匹配结果：共计"+allNum+"条数据，其中"+hasMatchNum+"条已匹配（"+same+"条核对一致，"+notsame+"条核对不一致），"+notMatchNum+"条未匹配";
		resultMap.put("dataList", list);
		resultMap.put("hejiStr", resultStr);
        return resultMap;
    }
	
	/**
	 * 核保对比数据上传
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/impBxMatchData.do",method=RequestMethod.POST)
	public  @ResponseBody Map<String, Object> impBxMatchData(HttpServletRequest request){
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		
		User user = (User)request.getSession().getAttribute("loginUser");
		
		InputStream input = null;
		Workbook workBook = null;
		
		String errorMsg = "";
		String uploadFlag = "success";
		
		try {  
			// 转型为MultipartHttpRequest：  
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;  
			// 获得文件：  
			MultipartFile file = multipartRequest.getFile("file");  
			// 获得输入流：  
			input = file.getInputStream();  
			
			workBook = Workbook.getWorkbook(input);
				
			// 去掉之前导入的一二级旧来源，统一成现在的新来源编码
			String[] colPropertity = {"custname","insurname","relation","insuridno","insurage","compname","prodtype","fundname","yearamk","payyears","ensureyears","insurstate","insurid","passdt","caltime"};
				
			Sheet sheet = workBook.getSheet(0);
			
			// 将之前获取Excel的13列数据改为12列
			List<CmBxPrebookMatchExcl> postList = ExcelUtils.getListByReadShell(sheet, 1, 0, 15, colPropertity,CmBxPrebookMatchExcl.class);
				
			if (null == postList || postList.isEmpty()) {
				errorMsg = "没有上传记录";
				uploadFlag = "error";
			} else {
				int line = 2;
				for (CmBxPrebookMatchExcl info : postList) {
					String checkstr = checkPreInfo(info);
					if (StringUtil.isNotNullStr(checkstr)) {
						errorMsg += "第 " + line + " 行错误是：" + checkstr+"</br>";
						uploadFlag = "error";
					}
					line++;
				}
				//符合条件	
				if("success".equals(uploadFlag)){  
					List<CmBxPrebookMatch> insertList = new ArrayList<CmBxPrebookMatch>();
					List<String> reapdata = new ArrayList<>();
					boolean isrepart = false;
					for (CmBxPrebookMatchExcl info : postList) {
						insertList.add(changePreInfo(info));
						String idname = info.getInsurid()+info.getFundname();
						if(reapdata.contains(idname)){
							isrepart = true;
							break;
						}else{
							reapdata.add(idname);
						}
					}
					if(!isrepart){
						String result = cmBxPrebookMatchService.batchInsertCmBxPrebookMatchOpt(insertList,user.getUserId());
						if(!"success".equals(result)){
							uploadFlag = "error";
							errorMsg = result;
						}
					}else{
						uploadFlag = "error";
						errorMsg = "存在重复的保险单号和产品名称！";
					}
				}
			}
			resultMap.put("uploadFlag", uploadFlag);  
			resultMap.put("errorMsg", errorMsg);  
			
	     } catch (Exception e) {   
	            e.printStackTrace();  
	            resultMap.put("uploadFlag", "error");  
	            resultMap.put("errorMsg", "请检查模板是否正确");  
	     }finally{
	    	try {
	    		if(input != null){
	    			input.close();
	    		}
			} catch (IOException e) {
				e.printStackTrace();
			}
	     }
		 
		return resultMap;
	}
	
	private CmBxPrebookMatch changePreInfo(CmBxPrebookMatchExcl info){
		CmBxPrebookMatch match = new CmBxPrebookMatch();
		match.setInsurid(info.getInsurid().trim());
		match.setCustname(info.getCustname().trim());
		match.setInsurname(info.getInsurname().trim());
		match.setRelation(info.getRelation().trim());
		match.setInsuridno(info.getInsuridno().trim());
		match.setInsurage(new BigDecimal(info.getInsurage().trim()));
		match.setCompname(info.getCompname().trim());
		match.setFundname(info.getFundname().trim());
		match.setProdtype(info.getProdtype().trim());
		match.setYearamk(new BigDecimal(info.getYearamk().trim()));
		match.setPayyears(info.getPayyears().trim());
		match.setEnsureyears(info.getEnsureyears().trim());
		match.setInsurstate(info.getInsurstate().trim());
		match.setPassdt(info.getPassdt().trim());
		match.setCaltime(info.getCaltime().trim());
		return match;
	}
	
	private String checkPreInfo(CmBxPrebookMatchExcl info){
		StringBuilder sb = new StringBuilder();
    	
    	sb.append(changeNullStr(info.getCustname(),"投保人必填"));
    	sb.append(changeNullStr(info.getInsurname(),"受保人必填"));
    	sb.append(changeNullStr(info.getRelation(),"投保人与受保人关系必填"));
    	sb.append(changeNullStr(info.getInsuridno(),"受保人证件号必填"));
    	sb.append(changeNullStr(info.getInsurage(),"受保人年龄必填"));
    	if(!NumberUtil.isDouble(info.getInsurage().trim())){
    		sb.append(",受保人年龄须为数字");
    	}
    	sb.append(changeNullStr(info.getCompname(),"保险公司必填"));
    	sb.append(changeNullStr(info.getProdtype(),"产品类型必填"));
    	sb.append(changeNullStr(info.getFundname(),"产品名称必填"));
    	sb.append(changeNullStr(info.getYearamk(),"年缴保费/TP必填"));
    	if(!NumberUtil.isDouble(info.getYearamk().trim())){
    		sb.append(",年缴保费/TP须为数字");
    	}
    	sb.append(changeNullStr(info.getPayyears(),"缴费年限必填"));
    	sb.append(changeNullStr(info.getEnsureyears(),"保障年限必填"));
    	sb.append(changeNullStr(info.getInsurstate(),"保单状态必填"));
    	sb.append(changeNullStr(info.getInsurid(),"保单号必填"));
    	sb.append(changeNullStr(info.getPassdt(),"核保通过日期必填"));
    	sb.append(changeNullStr(info.getCaltime(),"冷静期截止日必填"));
    	if(StringUtil.isNotNullStr(sb.toString())){
    		return sb.toString().replaceFirst("，", "");
    	}else{
    		return sb.toString();
    	}
    	
    }
	
	private String changeNullStr(Object val,String resstr){
		String str = "";
		if(StringUtil.isNull(val)){
    		str ="，"+resstr;
    	}
		return str;
	}
}
