package com.howbuy.crm.hb.web.controller.counter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.constants.CrmTradeServerPathConstant;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.counter.CmCounterOrderfileFileMsg;
import com.howbuy.crm.hb.web.dto.counter.CmCounterOuterFileDto;
import com.howbuy.crm.prebook.dto.PreBookSignContractDetail;
import com.howbuy.crm.prebook.dto.PreBookSignContractDto;
import com.howbuy.crm.prebook.service.HkPrebookService;
import com.howbuy.crm.trade.common.constant.DfileConstants;
import com.howbuy.crm.trade.common.enums.counter.CounterBusiEnum;
import com.howbuy.crm.trade.common.enums.counter.CounterStateEnum;
import com.howbuy.crm.trade.common.response.BaseResponse;
import com.howbuy.crm.trade.model.counter.dto.CmCounterOrderDto;
import com.howbuy.crm.trade.model.counter.dto.CmCounterOrderFileDto;
import com.howbuy.crm.trade.model.counter.po.CmCounterBusiness;
import com.howbuy.crm.trade.model.counter.po.CmCounterOrder;
import com.howbuy.crm.trade.model.counter.vo.CmCounterOrderSearchVo;
import com.howbuy.crm.util.exception.BusinessException;
import com.howbuy.dfile.HFileService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Description //COUNTER 的 controller
 * <AUTHOR>
 * @Date 15点35分$ 2022年8月9日$
 */
@Slf4j
public class BaseCounterController  extends BaseController {


    private static RestTemplate restTemplate;
    
    @Value("${CRM_TRADE_SERVER_URL}")
	private String crmTradeServerUrl;


	@Value("${CRM_TRADE_SERVER_TIMEOUT}")
    private String crmTradeServerTimeout;


	@Autowired
	private HkPrebookService hkPrebookService;


	/**
	 * 资料刊文件上传， 页面定义的 文件前缀： 100001
	 */
	private static final String  COUNTER_FILE_NAME_PREFIX="100001";


	@PostConstruct
	public void init() {
		int timeout = Integer.parseInt(crmTradeServerTimeout); //50s
		restTemplate = new RestTemplate(getClientHttpRequestFactory(timeout));
	}

	private static ClientHttpRequestFactory getClientHttpRequestFactory(int timeout) {
		RequestConfig config = RequestConfig.custom()
				.setConnectTimeout(timeout)
				.setConnectionRequestTimeout(timeout)
				.setSocketTimeout(timeout)
				.build();
		CloseableHttpClient client = HttpClientBuilder
				.create()
				.setDefaultRequestConfig(config)
				.build();
		return new HttpComponentsClientHttpRequestFactory(client);
	}



	/**
	 * 根据业务类型 新增资料管理
	 * @param fileList 页面端请求 request 上传的文件
	 * @return
	 */
	public Map<String, List<String>>  processCounterFile (List<MultipartFile> fileList){
		Map<String, List<String>> addFileMap= Maps.newHashMap();
		//文件上传信息
		fileList.forEach(file->{
			//写入临时目录
            //相对目录  /20240415/160710/
			String relativePath = String.join(File.separator, DateTimeUtil.getCurrYMD(), DateTimeUtil.getCurTimeHHmmss());
			String fileName = file.getOriginalFilename();
			//基于 fileName  处理空格后的 使用的 fileName
			String  usedFileName=StringUtil.trim(fileName).replace(" ", "");
			//HdFile 写入
			HFileService instance = HFileService.getInstance();
			try {
				//webdav不支持参数 inputStream. 详见[WebDavHFileService]
				instance.write(DfileConstants.COUNTER_TEMP_PATH, relativePath,usedFileName, file.getBytes());
				String fileTypeId = fileName.substring(0, fileName.indexOf(COUNTER_FILE_NAME_PREFIX));
				if(!addFileMap.containsKey(fileTypeId)){
					addFileMap.put(fileTypeId, Lists.newArrayList());
				}
				addFileMap.get(fileTypeId).add(String.join(File.separator, relativePath, usedFileName));
				log.info("新增资料管理订单，文件信息fielTypeId：{}，新增文件名称：{},处理空格后的文件名称：{}",fileTypeId,fileName,usedFileName);
			}catch (Exception e){
				log.error("资料管理文件上传，配置：{}，相对路径：{}，文件名称：{}，处理空格后的文件名称：{},异常！",
						DfileConstants.COUNTER_TEMP_PATH, relativePath,fileName,usedFileName);
				log.error("文件上传异常",e);
                throw new BusinessException("文件上传异常");
			}
		});
		return addFileMap;
	}


	/**
	 * 文件类型配置 写入
	 * 将 MultipartFile 存储， 返回 相对路径。 通用方法
	 * @param fileList
	 * @return
	 */
	public List<String> processCounterTempFile(List<MultipartFile> fileList){
		List<String> addFileList=Lists.newArrayList();
		//文件上传信息
		fileList.forEach(file->{
			//写入临时目录
			//相对目录  /20240415/160710/
			String relativePath = String.join(File.separator, DateTimeUtil.getCurrYMD(), DateTimeUtil.getCurTimeHHmmss());
			String fileName = file.getOriginalFilename();
			//HdFile 写入
			HFileService instance = HFileService.getInstance();
			try {
				//webdav不支持参数 inputStream. 详见[WebDavHFileService]
				instance.write(DfileConstants.COUNTER_TEMP_PATH, relativePath,fileName, file.getBytes());
				addFileList.add(String.join(File.separator, relativePath, fileName));
				log.info("新增资料管理文件类型配置，，新增文件名称：{}",fileName);
			}catch (Exception e){
				log.error("资料管理类型配置上传，文件目录配置：{}，相对路径：{}，文件名称：{}，异常！",
						DfileConstants.COUNTER_TEMP_PATH, relativePath,fileName);
				log.error("文件上传异常",e);
				throw new BusinessException("文件上传异常");
			}
		});
		return addFileList;
	}


	/**
	 * 获取 counter 文件
	 * @param fileId
	 * @param relativeFilePath
	 * @param fileName
	 * @param fileSuffix
	 * @return
	 */
	public byte[] getCounterFile(String fileId, String relativeFilePath,String fileName,String fileSuffix){
		HFileService instance = HFileService.getInstance();
		byte[]  fileBytes= new byte[0];
		String fullName = null;
		try {
			fullName=String.join(".", fileName, fileSuffix);
			fileBytes = instance.read2Bytes(DfileConstants.COUNTER_FILE_PATH,
					relativeFilePath,
					fullName
			);
		} catch (Exception e) {
			log.error("文件名称：{}，读取异常！",fullName);
			log.error("读取文件异常！",e);
		}

		if(null != fileBytes && fileBytes.length > 0) {
			return fileBytes;
		}

		try {
			// 兼容历史文件【202003之前的文件是以ID命名的，在查询时兼容查询】
			fullName=String.join(".", fileId, fileSuffix);
			log.info("根据ID查询文件为空，ID：{}，文件名称：{}",fileId,fullName);
			fileBytes = instance.read2Bytes(DfileConstants.COUNTER_FILE_PATH,
					relativeFilePath,
					fullName
			);
		} catch (Exception e) {
			log.error("文件名称：{}，读取异常！",fullName);
			log.error("读取文件异常！",e);
		}
		return fileBytes;
	}


	/**
	 * 历史逻辑 迁移。 待重构
	 * 根据 fileMsg 对象， 动态确定 文件读取方式
	 * @param fileMsg
	 * @return
	 */
	public byte[] getBytesByFileMsg(CmCounterOrderfileFileMsg fileMsg){
		log.info("处理文件。fileId：{},fileName:{},fileSuffix:{},isMove{}",
				fileMsg.getId(),
				fileMsg.getFileName(),
				fileMsg.getFileSuffix(),
				fileMsg.getIsMove());
		//取账户中心文件
		//逻辑有问题： 此处 只适用 isMove .
		if(StaticVar.IS_MOVE.equals(fileMsg.getIsMove())){
			return fileMsg.getFileBytes();
		}else{
			//取 资料管理
			return getCounterFile(fileMsg.getId(),fileMsg.getRelativeFilePath(),fileMsg.getFileName(),fileMsg.getFileSuffix());
		}
	}


	/**
	 * application/json  post提交对象
	 * @param path
	 * @param jsonObject
	 * @param responseType
	 * @param <T>
	 * @return
	 */
    public  <T,V> T getPostEntityByJsonObject(String path, V jsonObject, ParameterizedTypeReference<T> responseType){
   	    String fullUrl=crmTradeServerUrl+path;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<V> httpEntity = new HttpEntity<>(jsonObject, headers);
        ResponseEntity<T> response=restTemplate.exchange(fullUrl, HttpMethod.POST, httpEntity,  responseType);
        log.info("POST请求crm_trade_server.url:{},param:{},response:{}",path, JSONObject.toJSONString(jsonObject), JSONObject.toJSONString(response));
        if(HttpStatus.OK.equals(response.getStatusCode())){
            return response.getBody();
        }
        return null;
   }

	/**
	 * post请求
	 * @param path  path路径
	 * @param paramMap
	 * @param responseType
	 * @return
	 */
	public <T> T getPostEntityByMap(String path , Map<String,String> paramMap, ParameterizedTypeReference<T> responseType) {
		String fullUrl = crmTradeServerUrl + path;
		//①：表单信息，需要放在MultiValueMap中，MultiValueMap相当于Map<String,List<String>>
		MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
		//调用add方法放入表单元素(表单名称:值)
		paramMap.forEach(body::add);
		//②：请求头
		HttpHeaders headers = new HttpHeaders();

		//调用set方法放入请求头
		headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
		//③：请求实体：包含了请求体和请求头
		HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(body, headers);

		log.info( "http请求：fullUrl->{},httpEntity->{}" ,fullUrl,JSON.toJSONString(httpEntity));
		ResponseEntity<T> response = restTemplate.exchange(fullUrl, HttpMethod.POST, httpEntity, responseType);
		log.info( "http返回：path->{},,postParam->{},response->{} " ,path,JSON.toJSONString(paramMap),JSON.toJSONString(response));
		if (HttpStatus.OK.equals(response.getStatusCode())) {
			return response.getBody();
		}
		return null;
	}



	/**
	 * 根据bdId获取业务配置对象
	 * @param bdId
	 * @return
	 */
	public CmCounterBusiness getBusinessById(String bdId){
		 //GET_BUSINESS_BYID;
//		BaseResponse<CmCounterBusiness>
          //TODO:
		Map<String,String> postParam = new HashMap<String,String>();
		postParam.put("id", bdId);
		BaseResponse<CmCounterBusiness> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.GET_CM_COUNTER_BUSSINESS,
				postParam,new ParameterizedTypeReference<BaseResponse<CmCounterBusiness>>(){});
		if (httpRsp.isSuccess()) {
			return httpRsp.getData();
		}
		return null;
	}

	/**
	 * 根据订单orderId获取订单对象
	 * @param orderId
	 * @return
	 */
	public CmCounterOrderDto getOrderById(String orderId){
    	///querycounterorderdto
		// GET_ORDER_BY_ID
		CmCounterOrderSearchVo vo = new CmCounterOrderSearchVo();
		vo.setId(orderId);
		BaseResponse<List<CmCounterOrderDto>> response=
				getPostEntityByJsonObject(CrmTradeServerPathConstant.QUERY_COUNTER_ORDR_DTO_LIST,  vo, new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderDto>>>() {});
		if(response.isSuccess() && response.getData().size()==1){
			return response.getData().get(0);
		}
		return null; //TODO:
	}


	/**
	 * 根据订单forId获取订单对象
	 * @param forId
	 * @return
	 */
	public CmCounterOrder getValidOrderByForId(String forId){
		///querycounterorderdto
		// QUERY_COUNTER_ORDR_BY_VO
		CmCounterOrderSearchVo searchVo = new CmCounterOrderSearchVo();
		searchVo.setForId(forId);
		// 10-作废 13-失效 不参与判断重复
		searchVo.setExcludeStateList(Lists.newArrayList(CounterStateEnum.CANCEL.getKey(),CounterStateEnum.EXPIRE.getKey()));
		BaseResponse<List<CmCounterOrder>> response=
				getPostEntityByJsonObject(CrmTradeServerPathConstant.QUERY_COUNTER_ORDR_BY_VO,
						searchVo,
						new ParameterizedTypeReference<BaseResponse<List<CmCounterOrder>>>() {});
		log.info("根据forId:{},获取到有效[非10-作废|13-失效]的资料管理订单：{}.",forId, JSONObject.toJSONString(response.getData()));
		if(response.isSuccess() && response.getData().size()==1){
			return response.getData().get(0);
		}
		return null;
	}

	/**
	 * 根据订单forId获取订单对象.
	 * 存在：一个forId 两种业务同事存在。比如：新增意向单  撤销意向单。两种业务使用同一个意向单业务Id 。
	 * * @param forId
	 * @return
	 */
	public CmCounterOrder getValidOrderByForId(String forId, CounterBusiEnum busiEnum){
		///querycounterorderdto
		// QUERY_COUNTER_ORDR_BY_VO
		CmCounterOrderSearchVo searchVo = new CmCounterOrderSearchVo();
		searchVo.setForId(forId);
		searchVo.setBusiId(busiEnum.getKey());
		// 10-作废 13-失效 不参与判断重复
		searchVo.setExcludeStateList(Lists.newArrayList(CounterStateEnum.CANCEL.getKey(),CounterStateEnum.EXPIRE.getKey()));
		BaseResponse<List<CmCounterOrder>> response=
				getPostEntityByJsonObject(CrmTradeServerPathConstant.QUERY_COUNTER_ORDR_BY_VO,
						searchVo,
						new ParameterizedTypeReference<BaseResponse<List<CmCounterOrder>>>() {});
		log.info("根据forId:{},订单业务类型：{} 获取到有效[非10-作废|13-失效]的资料管理订单：{}.",forId, busiEnum.getDesc(),JSONObject.toJSONString(response.getData()));
		if(response.isSuccess() && response.getData().size()==1){
			return response.getData().get(0);
		}
		return null;
	}


	public List<CmCounterOrderFileDto> listCmCounterOrderFileDto(String orderId, String checkLevel){
		// 设置默认参数
		Map<String,String> postParam = new HashMap<String,String>();
		postParam.put("orderId", orderId);
		postParam.put("checkLevel", checkLevel);

		List<CmCounterOrderFileDto> listFile = null;
		BaseResponse<List<CmCounterOrderFileDto>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_ORDER_FILE_DTO_LIST, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderFileDto>>>(){});
		if (httpRsp.isSuccess()) {
			listFile = httpRsp.getData();
		}

		return listFile;
	}


	/**
	 * 根据预约id 获取 海外产品电子签约 协议文件列表信息
	 * @param preId
	 * @return
	 */
	public List<CmCounterOuterFileDto>  getHkSignFileListByPreId(BigDecimal preId){
		List<PreBookSignContractDetail> signDetaiList=Lists.newArrayList();
		//获取协议信息列表
		ReturnMessageDto<PreBookSignContractDto> signResp=hkPrebookService.getSignContractInfo(preId);
		if(signResp.isSuccess()){
			PreBookSignContractDto signDto=signResp.getReturnObject();
			if(signDto!=null && CollectionUtils.isNotEmpty(signDto.getContracts())){
				signDetaiList.addAll(signDto.getContracts());
			}
		}

		List<CmCounterOuterFileDto> hkSignFileList=Lists.newArrayList();
		signDetaiList.stream().forEach(signDetail->{
			CmCounterOuterFileDto outerFileDto=new CmCounterOuterFileDto();
			outerFileDto.setFileBusiRemark(String.format("%s签约（签约时间：%s）","已",
					signDetail.getSignDate()==null?"": DateUtil.date2String(signDetail.getSignDate(),DateUtil.DEFAULT_DATESFM)));
			outerFileDto.setFileName(signDetail.getFileName());
			outerFileDto.setFilePathUrl(signDetail.getFilePath());
			outerFileDto.setFileType(signDetail.getFileType());
			outerFileDto.setFileTypeName(signDetail.getFileTypeName());
			outerFileDto.setRequired(true);
			hkSignFileList.add(outerFileDto);
		});

       return hkSignFileList;
	}
    

}
