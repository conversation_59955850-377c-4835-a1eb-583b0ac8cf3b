/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.web.controller.custinfo;

import com.howbuy.common.page.Page;
import com.howbuy.common.page.Pagination;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.domain.birthdaygift.CmBirthdayWhiteListInfoDTO;
import com.howbuy.crm.hb.enums.birthdaygift.WhiteListApplicantTypeEnum;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.web.dto.xlw.white.QueryUserBirthdayWhiteListDTO;
import com.howbuy.web.dto.xlw.white.UserBirthdayWhiteListInfoDTO;
import com.howbuy.web.service.business.UserBirthdayWhiteListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生日礼物白名单控制器
 * 用于管理生日礼物白名单的增删改查等操作
 * 白名单分为客户和理财顾问两种类型
 *
 * <AUTHOR>
 * @date 2025/2/21
 */
@Slf4j
@Controller
@RequestMapping("/birthdaygiftwhitelist")
public class BirthdayGiftWhiteListController {

    @Autowired
    private UserBirthdayWhiteListService userBirthdayWhiteListService;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    /**
     * 默认每页显示记录数
     */
    private static final String DEFAULT_PAGE_SIZE = "10";

    /**
     * @api {GET} /birthdaygiftwhitelist/list.do index()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftWhiteListController
     * @apiName index()
     * @apiDescription 生日礼品白名单首页
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":false,"status":"URI_TOO_LONG"}
     */
    @RequestMapping("/list.do")
    public ModelAndView index(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/custinfo/birthdayGiftWhiteList");

        return modelAndView;
    }

    /**
     * @api {POST} /birthdaygiftwhitelist/query query()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftWhiteListController
     * @apiName query()
     * @apiDescription 生日礼品白名单 查询
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * {}
     */
    @ResponseBody
    @PostMapping("/query")
    public Map<String, Object> query(HttpServletRequest request) {
        try {
            String applicantName = request.getParameter("applicantName");
            String hboneNo = request.getParameter("hboneNo");
            String applicantType = request.getParameter("applicantType");

            int page = Integer.parseInt(request.getParameter("page"));
            int rows = Optional.ofNullable(request.getParameter("rows"))
                    .map(Integer::parseInt)
                    .orElse(Integer.parseInt(DEFAULT_PAGE_SIZE));

            QueryUserBirthdayWhiteListDTO queryDTO = buildQueryDTO(applicantName, hboneNo, applicantType);
            Page pageInfo = buildPageInfo(page, rows);

            Pagination<UserBirthdayWhiteListInfoDTO> pagination =
                    userBirthdayWhiteListService.queryUserBirthdayWhiteListInfoDTOPage(queryDTO, pageInfo);

            return processQueryResult(pagination);
        } catch (Exception e) {
            log.error("查询白名单失败", e);
            Map<String, Object> resultMap = new HashMap<>(2);
            resultMap.put("total", 0);
            resultMap.put("rows", new ArrayList<>());

            return resultMap;
        }
    }

    /**
     * @api {POST} /birthdaygiftwhitelist/delete delete()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftWhiteListController
     * @apiName delete()
     * @apiDescription 生日礼品白名单 删除
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"fY","returnMsg":"hWmGCW","returnObject":"W","returnList":["VHVyQKX"]}
     */
    @ResponseBody
    @PostMapping("/delete")
    public ReturnMessageDto<String> delete(HttpServletRequest request) {
        String idStr = request.getParameter("idStr");
        if (StringUtils.isBlank(idStr)) {
            return ReturnMessageDto.fail("删除的白名单，id不存在！");
        }
        Long id = Long.valueOf(idStr);
        userBirthdayWhiteListService.deleteUserBirthdayWhiteListInfoDTO(id);

        return ReturnMessageDto.ok();
    }

    /**
     * @api {GET} /birthdaygiftwhitelist/initadd initAdd()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftWhiteListController
     * @apiName initAdd()
     * @apiDescription 生日礼品白名单 新增页面
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":false,"status":"UNAVAILABLE_FOR_LEGAL_REASONS"}
     */
    @RequestMapping("/initadd")
    public ModelAndView initAdd(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/custinfo/addBirthdayGiftWhiteList");

        return modelAndView;
    }

    /**
     * @api {POST} /birthdaygiftwhitelist/add add()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftWhiteListController
     * @apiName add()
     * @apiDescription 生日礼品白名单 新增
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"OLBT19O5RD","returnMsg":"MaBi3rcf","returnObject":"IlEECRDH5","returnList":["f4"]}
     */
    @ResponseBody
    @PostMapping("/add")
    public ReturnMessageDto<String> add(HttpServletRequest request) {
        try {
            String applicantType = request.getParameter("applicantType");
            String hboneNo = request.getParameter("hboneNo");
            String consCode = request.getParameter("consCode");
            String applicantSource = request.getParameter("applicantSource");

            // 参数校验
            if (!validateAddParameters(applicantType, hboneNo, consCode)) {
                return ReturnMessageDto.fail("参数不完整");
            }

            UserBirthdayWhiteListInfoDTO whiteListDTO = buildWhiteListDTO(
                    request, applicantType, hboneNo, consCode, applicantSource
            );

            int i = userBirthdayWhiteListService.addUserBirthdayWhiteListInfoDTO(whiteListDTO);
            if (i == 0) {
                String applicantName = WhiteListApplicantTypeEnum.CUST.getCode().equals(applicantType)
                        ? WhiteListApplicantTypeEnum.CUST.getDescription() : WhiteListApplicantTypeEnum.CONS.getDescription();
                return ReturnMessageDto.fail(applicantName + "已存在，请勿重复添加！");
            }
            return ReturnMessageDto.ok();
        } catch (Exception e) {
            log.error("添加白名单失败", e);
            return ReturnMessageDto.fail("添加失败：" + e.getMessage());
        }
    }

    /**
     * @api {POST} /birthdaygiftwhitelist/getcustinfobyhboneno getCustInfoByHboneNo()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftWhiteListController
     * @apiName getCustInfoByHboneNo()
     * @apiDescription 根据一账通号查询客户信息
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"8q83","returnMsg":"1yKI","returnObject":"k","returnList":["arNGaxvJD"]}
     */
    @ResponseBody
    @PostMapping("/getcustinfobyhboneno")
    public ReturnMessageDto<String> getCustInfoByHboneNo(HttpServletRequest request) {
        String hboneNo = request.getParameter("hboneNo");
        if (StringUtils.isBlank(hboneNo)) {
            return ReturnMessageDto.ok("", "");
        }
        ConscustInfoDomain hboneAcctCustDetailInfoVO = getConsCustInfoByHboneNo(hboneNo);

        if (Objects.isNull(hboneAcctCustDetailInfoVO) || StringUtils.isBlank(hboneAcctCustDetailInfoVO.getHboneno())) {
            return ReturnMessageDto.fail("查询失败");
        }
        return ReturnMessageDto.ok("", hboneAcctCustDetailInfoVO.getCustname());
    }

    /**
     * 构建查询条件DTO
     * 处理查询参数的空值情况
     *
     * @param applicantName 申请人姓名
     * @param hboneNo 一账通号
     * @param applicantType 申请人类型(CUST:客户/CONS:理财顾问)
     * @return 查询条件DTO
     */
    private QueryUserBirthdayWhiteListDTO buildQueryDTO(String applicantName, String hboneNo, String applicantType) {
        QueryUserBirthdayWhiteListDTO queryDTO = new QueryUserBirthdayWhiteListDTO();
        queryDTO.setApplicantName(StringUtils.isNotBlank(applicantName) ? applicantName : null);
        queryDTO.setHboneNo(StringUtils.isNotBlank(hboneNo) ? hboneNo : null);
        queryDTO.setApplicantType(StringUtils.isNotBlank(applicantType) ? applicantType : null);
        return queryDTO;
    }

    /**
     * 构建分页信息
     *
     * @param page 当前页码
     * @param rows 每页记录数
     * @return 分页对象
     */
    private Page buildPageInfo(int page, int rows) {
        Page pageInfo = new Page();
        pageInfo.setPage(page);
        pageInfo.setPerPage(rows);
        return pageInfo;
    }

    /**
     * 处理查询结果
     * 将服务层返回的分页数据转换为前端所需的格式
     *
     * @param pagination 分页查询结果
     * @return 包含total和rows的Map
     */
    private Map<String, Object> processQueryResult(Pagination<UserBirthdayWhiteListInfoDTO> pagination) {
        Map<String, Object> resultMap = new HashMap<>(2);

        if (Objects.isNull(pagination) || Objects.isNull(pagination.getPage())) {
            resultMap.put("total", 0);
            resultMap.put("rows", new ArrayList<>());
            return resultMap;
        }

        List<CmBirthdayWhiteListInfoDTO> resultList = pagination.getResultList().stream()
                .map(this::convertToWhiteListDTO)
                .collect(Collectors.toList());

        resultMap.put("total", pagination.getPage().getTotal());
        resultMap.put("rows", resultList);
        return resultMap;
    }

    /**
     * 将UserBirthdayWhiteListInfoDTO转换为CmBirthdayWhiteListInfoDTO
     * 主要用于查询结果的数据转换
     *
     * @param source 源对象
     * @return 目标对象
     */
    private CmBirthdayWhiteListInfoDTO convertToWhiteListDTO(UserBirthdayWhiteListInfoDTO source) {
        CmBirthdayWhiteListInfoDTO target = new CmBirthdayWhiteListInfoDTO();
        BeanUtils.copyProperties(source, target);
        target.setIdStr(Long.toString(target.getId()));
        return target;
    }

    /**
     * 验证添加白名单的参数
     * 客户类型必须提供一账通号
     * 理财顾问类型必须提供理财顾问编码
     *
     * @param applicantType 申请人类型
     * @param hboneNo 一账通号
     * @param consCode 理财顾问编码
     * @return 验证结果
     */
    private boolean validateAddParameters(String applicantType, String hboneNo, String consCode) {
        if (StringUtils.isBlank(applicantType)) {
            return false;
        }

        if (WhiteListApplicantTypeEnum.CUST.getCode().equals(applicantType)) {
            return StringUtils.isNotBlank(hboneNo);
        } else if (WhiteListApplicantTypeEnum.CONS.getCode().equals(applicantType)) {
            return StringUtils.isNotBlank(consCode);
        }

        return false;
    }

    /**
     * 构建白名单DTO
     * 根据申请人类型设置不同的信息
     *
     * @param request HTTP请求
     * @param applicantType 申请人类型
     * @param hboneNo 一账通号
     * @param consCode 理财顾问编码
     * @param applicantSource 申请来源
     * @return 白名单DTO
     */
    private UserBirthdayWhiteListInfoDTO buildWhiteListDTO(
            HttpServletRequest request,
            String applicantType,
            String hboneNo,
            String consCode,
            String applicantSource) {

        String applicantName = getApplicantName(applicantType, hboneNo, consCode);
        User user = (User) request.getSession().getAttribute("loginUser");

        UserBirthdayWhiteListInfoDTO whiteListDTO = new UserBirthdayWhiteListInfoDTO();
        whiteListDTO.setApplicantType(applicantType);
        whiteListDTO.setApplicantName(applicantName);
        whiteListDTO.setApplicantSource(applicantSource);
        whiteListDTO.setCreateUser(user.getUserId());

        if (WhiteListApplicantTypeEnum.CUST.getCode().equals(applicantType)) {
            whiteListDTO.setHboneNo(hboneNo);
        } else {
            whiteListDTO.setConsCode(consCode);
        }

        return whiteListDTO;
    }

    /**
     * 获取申请人姓名
     * 客户类型：通过一账通号查询客户信息获取姓名
     * 理财顾问类型：通过理财顾问编码从缓存中获取姓名
     *
     * @param applicantType 申请人类型
     * @param hboneNo 一账通号
     * @param consCode 理财顾问编码
     * @return 申请人姓名
     */
    private String getApplicantName(String applicantType, String hboneNo, String consCode) {
        if (WhiteListApplicantTypeEnum.CUST.getCode().equals(applicantType)) {
            ConscustInfoDomain custInfo = getConsCustInfoByHboneNo(hboneNo);
            return Objects.nonNull(custInfo) ? custInfo.getCustname() : "";
        } else if (WhiteListApplicantTypeEnum.CONS.getCode().equals(applicantType)) {
            ConsOrgCache orgcache = ConsOrgCache.getInstance();
            return orgcache.getAllUserMap().get(consCode);
        }
        return "";
    }

    /**
     * 根据一账通号查询客户信息
     * 调用客户信息查询服务获取客户详细信息
     *
     * @param hboneNo 一账通号
     * @return 客户信息对象，查询失败返回null
     */
    private ConscustInfoDomain getConsCustInfoByHboneNo(String hboneNo) {
        QueryConscustInfoRequest queryConscustInfoRequest = new QueryConscustInfoRequest();
        queryConscustInfoRequest.setHboneNo(hboneNo);

        QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);
        if (Objects.nonNull(queryConscustInfoResponse)) {
            return queryConscustInfoResponse.getConscustinfo();

        }
        return null;
    }
}