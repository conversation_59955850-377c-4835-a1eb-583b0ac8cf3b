package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.excel.EasyExcel;
import com.howbuy.crm.base.DivModeEnum;
import com.howbuy.crm.base.PreBookArchTypeEnum;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.domain.prosale.UploadZxTrade;
import com.howbuy.crm.hb.domain.prosale.ZxTradeRecordVo;
import com.howbuy.crm.hb.domain.prosale.zxtraderecordaudit.ZxTradeRecord;
import com.howbuy.crm.hb.enums.ZxTradeTypeEnum;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.prosale.CustprivatefundtradeService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.privatetrade.dto.CmCustprivatefundtrade;
import com.howbuy.crm.privatetrade.service.CustTradeBusinessService;
import com.howbuy.crm.prosale.dto.Custprivatefundtrade;
import com.howbuy.crm.util.UploadZxTradeListener;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageResult;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (直销交易记录处理)
 * @date 2023/2/28 17:38
 * @since JDK 1.8
 */
@Controller
@RequestMapping(value = "/prosale")
public class ZxTradeRecordController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(ZxTradeRecordController.class);

    private final String DOWNLOAD_FILE_NAME = "CRM直销交易记录模板.xls";

    private final String MODEL_FILE_NAME = "crmzxtraderecordmodel.xls";

    @Autowired
    private CustprivatefundtradeService custprivatefundtradeService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private CustTradeBusinessService custTradeBusinessService;

    @Autowired
    private PrebookBasicInfoService prebookBasicInfoService;

    private static final String ZERO = "0";

    @RequestMapping(value = "/zxTradeRecord.do", method = RequestMethod.GET)
    public String loadPrivateTradeXls() {
        return "/prosale/zxTradeRecord";
    }

    /**
     * @return java.lang.String
     * @description:(下载模板文件)
     * @author: xfc
     * @date: 2023/3/9 11:18
     * @since JDK 1.8
     */
    @RequestMapping(value = "/downloadZxMode.do", method = RequestMethod.GET)
    public String downloadModel(HttpServletRequest request, HttpServletResponse response) {
         return dowmloadTemplate(MODEL_FILE_NAME,DOWNLOAD_FILE_NAME,request,response);
    }

    /**
     * 分页查询直销交易记录列表
     *
     * @param zxTradeRecordVo
     * @return
     */
    @RequestMapping("/listZxTradeByPage.do")
    @ResponseBody
    public PageResult<ZxTradeRecord> listZxTradeByPage(ZxTradeRecordVo zxTradeRecordVo) {
        // 返回查询结果
        PageResult<ZxTradeRecord> pageData = new PageResult<>();
        try {
            pageData = custprivatefundtradeService.listCmCustPrivateFundTradeByPage(zxTradeRecordVo);
        } catch (Exception e) {
            log.error("查询出错", e);
        }
        return pageData;
    }

    @RequestMapping(value = "/updateZxTradeRecord.do", method = RequestMethod.GET)
    public String updateZxTradeRecordView(HttpServletRequest request) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("appserialno", request.getParameter("appserialno"));

        ZxTradeRecord zxTradeRecord = custprivatefundtradeService.getCmCustPrivateFundTrade(params);

        request.setAttribute("zxTradeRecord", zxTradeRecord);
        return "/prosale/updateZxTradeRecord";
    }

    @ResponseBody
    @RequestMapping(value = "/updateZxTrade.do", method = RequestMethod.POST)
    public String updateZxTradeRecord(HttpServletRequest request, HttpServletResponse response) throws Exception {
        User user = (User) request.getSession().getAttribute("loginUser");

        String appserialno = request.getParameter("appserialno");
        String tradeDt = request.getParameter("tradedt");
        String tradeType = request.getParameter("tradeType");
        BigDecimal appamt = new BigDecimal(request.getParameter("appamt"));
        BigDecimal ackvol = new BigDecimal(request.getParameter("ackvol"));
        BigDecimal ackamt = new BigDecimal(request.getParameter("ackamt"));
        BigDecimal fee = new BigDecimal(request.getParameter("fee"));
        BigDecimal nav = new BigDecimal(request.getParameter("nav"));

        Custprivatefundtrade custprivatefundtrade = new Custprivatefundtrade();
        custprivatefundtrade.setAppserialno(appserialno);
        custprivatefundtrade.setTradedt(tradeDt);
        custprivatefundtrade.setAppamt(appamt);
        custprivatefundtrade.setAckamt(ackamt);
        custprivatefundtrade.setAckvol(ackvol);
        custprivatefundtrade.setFee(fee);
        custprivatefundtrade.setNav(nav);
        custprivatefundtrade.setModifier(user.getUserId());
        custprivatefundtrade.setBusicode(tradeType);
        custprivatefundtrade.setModdt(DateUtil.getDateFormat(new Date(), "yyyyMMdd"));
        // 对于交易类型为分红,需要校验分红方式
        if (StaticVar.BUSINESS_CODE_FH.equals(tradeType)) {
            String divMode = request.getParameter("divMode");
            String checkErrorMsg = checkDivMode(divMode, custprivatefundtrade);
            if (StringUtil.isNotNullStr(checkErrorMsg)) {
                return checkErrorMsg;
            }
            custprivatefundtrade.setDivmode(divMode);
        } else {
            custprivatefundtrade.setDivmode("");
        }

        CmCustprivatefundtrade cmCustprivatefundtrade = new CmCustprivatefundtrade();
        BeanUtils.copyProperties(custprivatefundtrade, cmCustprivatefundtrade);
        ReturnMessageDto<String> resp=custTradeBusinessService.updateFundTrade(cmCustprivatefundtrade);
        if(resp.isSuccess()){
            return "success";
        }else{
            return resp.getReturnMsg();
        }
    }

    private static String checkDivMode(String divMode, Custprivatefundtrade custprivatefundtrade) {
        if (StringUtil.isNullStr(divMode)) {
            return "分红方式不能为空";
        }

        if (DivModeEnum.DIV_MODE_VOL.getCode().equals(divMode)) {
            if (isHavaAckVolAndNotAckAmt(custprivatefundtrade)) {
                return "分红方式为红利再投，未填写确认份额且无需写确认金额";
            } else if (custprivatefundtrade.getAckvol() == null || custprivatefundtrade.getAckvol().compareTo(Util.ObjectToBigDecimalNull(ZERO)) <= 0) {
                return "分红方式为红利再投，未填写确认份额";
            } else if (custprivatefundtrade.getAckamt() != null && custprivatefundtrade.getAckamt().compareTo(Util.ObjectToBigDecimalNull(ZERO)) != 0) {
                return "分红方式为红利再投，无需写确认金额";
            }
        }
        if (DivModeEnum.DIV_MODE_AMT.getCode().equals(divMode)) {
            if (isHavaAckAmtAndNotAckVol(custprivatefundtrade)) {
                return "分红方式为现金分红，未填写确认金额且无需写确认份额";
            } else if (custprivatefundtrade.getAckamt() == null || custprivatefundtrade.getAckamt().compareTo(Util.ObjectToBigDecimalNull(ZERO)) <= 0) {
                return "分红方式为现金分红，未填写确认金额";
            } else if (custprivatefundtrade.getAckvol() != null && custprivatefundtrade.getAckvol().compareTo(Util.ObjectToBigDecimalNull(ZERO)) != 0) {
                return "分红方式为现金分红，无需写确认份额";
            }
        }
        return null;
    }

    private static boolean isHavaAckAmtAndNotAckVol(Custprivatefundtrade custprivatefundtrade) {
        return (custprivatefundtrade.getAckamt() == null || custprivatefundtrade.getAckamt().compareTo(Util.ObjectToBigDecimalNull(ZERO)) <= 0) &&
                (custprivatefundtrade.getAckvol() != null && custprivatefundtrade.getAckvol().compareTo(Util.ObjectToBigDecimalNull(ZERO)) != 0);
    }

    private static boolean isHavaAckVolAndNotAckAmt(Custprivatefundtrade custprivatefundtrade) {
        return (custprivatefundtrade.getAckvol() == null || custprivatefundtrade.getAckvol().compareTo(Util.ObjectToBigDecimalNull(ZERO)) <= 0) &&
                (custprivatefundtrade.getAckamt() != null && custprivatefundtrade.getAckamt().compareTo(Util.ObjectToBigDecimalNull(ZERO)) != 0);
    }


    @ResponseBody
    @RequestMapping(value = "/deleteZxTrade.do", method = RequestMethod.POST)
    public ReturnMessageDto<String> deleteZxTrade(HttpServletRequest request) {
        String appserialno = request.getParameter("appserialno");
        return custTradeBusinessService.deleteFundTrade(appserialno,getLoginUserId());
    }


    /**
     * @param request request请求
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @description:(上传保存接口)
     * @author: xfc
     * @date: 2023/3/9 15:03
     * @since JDK 1.8
     */
    @RequestMapping(value = "/uploadZxTradeRecord.do", method = RequestMethod.POST)
    public @ResponseBody
    Map<String, Object> uploadZxTradeRecord(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute("loginUser");

        Map<String, Object> resultMap = new HashMap<String, Object>();
        InputStream input = null;
        StringBuffer errorMsg = new StringBuffer();
        String uploadFlag = "success";
        try {
            // 转型为MultipartHttpRequest：
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            // 获得文件：
            MultipartFile file = multipartRequest.getFile("file");
            if (StringUtil.isNull(file)) {
                resultMap.put("uploadFlag", "error");
                resultMap.put("errorMsg", "请上传文件");
                return resultMap;
            }
            // 获得输入流：
            InputStream headInput = file.getInputStream();
            input = file.getInputStream();
            // 判断表头模板格式与类型
            try {
                EasyExcel.read(headInput, UploadZxTrade.class, new UploadZxTradeListener()).sheet().headRowNumber(1).doRead();
            } catch (Exception e) {
                log.error("上传模板错误" + e.getMessage());
                resultMap.put("uploadFlag", "error");
                resultMap.put("errorMsg", "模板格式不匹配,请检查后重新上传");
                return resultMap;
            }

            //通过easyExcel 读取所有数据
            List<UploadZxTrade> uploadZxTradeList = EasyExcel.read(input).head(UploadZxTrade.class).sheet().headRowNumber(1).doReadSync();
            // 数据处理
            List<UploadZxTrade> zxTradeRecordList = new ArrayList<>();
            //该方法，内部拦截 香港产品
            dealZxTradeList(uploadZxTradeList, errorMsg, zxTradeRecordList);

            if(CollectionUtils.isNotEmpty(zxTradeRecordList)){
                //获取需要更新持仓的记录
                List<UploadZxTrade> recalculateFundTradeRecordList = zxTradeRecordList.stream()
                        .filter(uploadZxTrade-> Objects.equals(uploadZxTrade.getIsInfluence(),ZxTradeTypeEnum.INFLUENCE.getTradeTypecode())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(recalculateFundTradeRecordList)){
                    // 存交易记录数据
                    List<CmCustprivatefundtrade> needUpdateTradeList = convertVo(recalculateFundTradeRecordList, user);
                    custTradeBusinessService.insertListFundTrade(needUpdateTradeList, true);
                }

                //获取不需要更新持仓的记录
                List<UploadZxTrade> tradeRecordList = zxTradeRecordList.stream()
                        .filter(uploadZxTrade-> Objects.equals(uploadZxTrade.getIsInfluence(),ZxTradeTypeEnum.NOINFLUENCE.getTradeTypecode())).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(tradeRecordList)){
                    // 存交易记录数据
                    List<CmCustprivatefundtrade> tradeList = convertVo(tradeRecordList, user);
                    custTradeBusinessService.insertListFundTrade(tradeList, false);
                }
            }

            if (StringUtil.isNotNullStr(errorMsg.toString())) {
                resultMap.put("uploadFlag", "error");
                resultMap.put("errorMsg", errorMsg.toString());
                return resultMap;
            }

            resultMap.put("uploadFlag", uploadFlag);
            resultMap.put("errorMsg", errorMsg.toString());
        } catch (Exception e) {
            log.error("uploadZxTradeRecord：" + e.getMessage(), e);
            resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "上传失败");
        }
        return resultMap;
    }


    /**
     * @description:(上传数据校验)
     * @since JDK 1.8
     */
    private void dealZxTradeList(List<UploadZxTrade> uploadZxTradeList,
                                 StringBuffer errorMsg,
                                 List<UploadZxTrade> zxTradeRecordList) throws IllegalAccessException {
        for (UploadZxTrade uploadZxTrade : uploadZxTradeList) {
            String conScustNo = uploadZxTrade.getConscustNo();
            String fundCode = uploadZxTrade.getFundCode();
            validNullVo(uploadZxTrade, errorMsg);
            if (StringUtil.isNotNullStr(errorMsg.toString())) {
                break;
            }
            //拦截香港产品
            String archType =prebookBasicInfoService.getArchType(conScustNo,fundCode);
            if(PreBookArchTypeEnum.HW.getCode().equals(archType)){
                addMsg(conScustNo, fundCode, errorMsg, "不支持海外产品操作,请修改后重新上传");
            }

            // 判断 投顾客户号 产品代码是否存在  以及判断产品类型是否存在
            if (StringUtil.isNullStr(uploadZxTrade.getConscustNo())) {
                addMsg(conScustNo, fundCode, errorMsg, "投顾客户号不存在,请修改后重新上传");
            }
            if (StringUtil.isNullStr(uploadZxTrade.getFundCode())) {
                addMsg(conScustNo, fundCode, errorMsg, "产品代码不存在,请修改后重新上传");
            }
            // 判断 金额/份额/净值非正数
            if (!isSignum(uploadZxTrade)) {
                addMsg(conScustNo, fundCode, errorMsg, ",上传的数据为负数");
            }
            // 判断上传日期格式类型
            if (!Util.isValidDate(uploadZxTrade.getTradeDt())) {
                addMsg(conScustNo, fundCode, errorMsg, ",上传的交易日期不符合规范");
            }
            // 判断上传的交易类型是否存在
            if (null == ZxTradeTypeEnum.getEnum(uploadZxTrade.getTradeType())) {
                addMsg(conScustNo, fundCode, errorMsg, ",上传的交易类型不存在");
            }
            // 处理是否影响持仓
            if (null == ZxTradeTypeEnum.getEnum(uploadZxTrade.getIsInfluence())) {
                addMsg(conScustNo, fundCode, errorMsg, ",未填写是否影响持仓");
            }
            uploadZxTrade.setIsInfluence(ZxTradeTypeEnum.getEnum(uploadZxTrade.getIsInfluence()));
            uploadZxTrade.setTradeType(ZxTradeTypeEnum.getEnum(uploadZxTrade.getTradeType()));
            // 判断销售类型  直销 直转代。  设计修改 2023-05-17  进入交易记录，赋值为： 直销
            if ("直销".equals(uploadZxTrade.getSaleType()) || "直转代".equals(uploadZxTrade.getSaleType())) {
                zxTradeRecordList.add(uploadZxTrade);
            } else {
                addMsg(conScustNo, fundCode, errorMsg, ",销售类型填写不符合");
            }
            if (StringUtil.isNotNullStr(errorMsg.toString())) {
                break;
            }
        }
    }

    /**
     * @description:(增加错误日志)
     * @author: xfc
     * @date: 2023/3/9 15:41
     * @since JDK 1.8
     */
    public void addMsg(String concustno, String funcode, StringBuffer errorMsg, String msg) {
        errorMsg.append("投顾客户号: ").append(concustno).append(",").append("产品代码： ").append(funcode).append(msg);
    }

    /**
     * @description:(校验上传的数据是否填写)
     * @author: xfc
     * @date: 2023/3/9 15:41
     * @since JDK 1.8
     */
    public void validNullVo(UploadZxTrade uploadZxTrade, StringBuffer errorMsg) throws IllegalAccessException {
        String fundCode = uploadZxTrade.getFundCode();
        String conscustNo = uploadZxTrade.getConscustNo();
        for (Field f : uploadZxTrade.getClass().getDeclaredFields()) {
            f.setAccessible(true);
            if (f.get(uploadZxTrade) == null) {
                addMsg(conscustNo, fundCode, errorMsg, ",本行数据有存在未填项,请检查后重新上传");
            }
        }
    }

    /**
     * @return boolean
     * @description:(判断 金额/份额/净值非正数)
     * @author: xfc
     * @date: 2023/3/9 14:58
     * @since JDK 1.8
     */
    private boolean isSignum(UploadZxTrade uploadZxTrade) {
        BigDecimal ackvol = uploadZxTrade.getAckvol();
        BigDecimal ackamt = uploadZxTrade.getAckamt();
        BigDecimal appamt = uploadZxTrade.getAppamt();
        BigDecimal fee = uploadZxTrade.getFee();
        BigDecimal nav = uploadZxTrade.getNav();
        if (appamt.compareTo(BigDecimal.ZERO) < 0 || ackamt.compareTo(BigDecimal.ZERO) < 0 || ackvol.compareTo(BigDecimal.ZERO) < 0 || nav.compareTo(BigDecimal.ZERO) < 0
                || fee.compareTo(BigDecimal.ZERO) < 0) {
            return false;
        }
        return true;
    }


    /**
     * 转换上传的数据为实体类
     *
     * @param zxTradeRecordList
     * @param user
     * @return
     */
    private List<CmCustprivatefundtrade> convertVo(List<UploadZxTrade> zxTradeRecordList, User user) {
        List<CmCustprivatefundtrade> custprivatefundtradeList = new ArrayList<>();
        for (int i = 0; i < zxTradeRecordList.size(); i++) {
            UploadZxTrade uploadZxTrade = zxTradeRecordList.get(i);
            CmCustprivatefundtrade custprivatefundtrade = new CmCustprivatefundtrade();
            custprivatefundtrade.setAppserialno(String.valueOf(commonService.getSeqValue("SEQ_PCUSTREC")));
            custprivatefundtrade.setBusicode(uploadZxTrade.getTradeType());
            custprivatefundtrade.setCustno(uploadZxTrade.getConscustNo());
            custprivatefundtrade.setFundcode(uploadZxTrade.getFundCode());
            custprivatefundtrade.setTradedt(uploadZxTrade.getTradeDt());
            custprivatefundtrade.setAppamt(uploadZxTrade.getAppamt().setScale(2, RoundingMode.HALF_UP));
            custprivatefundtrade.setAckvol(uploadZxTrade.getAckvol().setScale(6, RoundingMode.HALF_UP));
            custprivatefundtrade.setAckamt(uploadZxTrade.getAckamt().setScale(2, RoundingMode.HALF_UP));
            custprivatefundtrade.setNav(uploadZxTrade.getNav().setScale(8, RoundingMode.HALF_UP));
            custprivatefundtrade.setFee(uploadZxTrade.getFee().setScale(2, RoundingMode.HALF_UP));
            custprivatefundtrade.setModdt(DateUtil.getDateFormat(new Date(), "yyyyMMdd"));
            custprivatefundtrade.setModifier(user.getUserId());
            custprivatefundtrade.setConscustrid("0");
            custprivatefundtrade.setCretime(new Date());
            custprivatefundtrade.setRecstat(StaticVar.CONSSTATUS_NORMAL);
            custprivatefundtrade.setCheckflag(StaticVar.TX_CHECK_FLAG_PASS);


            custprivatefundtrade.setFundTradeDt(uploadZxTrade.getTradeDt());
            custprivatefundtrade.setFundAckDt(uploadZxTrade.getTradeDt());

            custprivatefundtradeList.add(custprivatefundtrade);
        }
        return custprivatefundtradeList;
    }


}
