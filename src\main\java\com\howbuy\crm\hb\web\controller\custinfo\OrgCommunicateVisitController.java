package com.howbuy.crm.hb.web.controller.custinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.constants.DfileConstants;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.custinfo.OrgCommunicateAttend;
import com.howbuy.crm.hb.domain.custinfo.OrgCommunicateFile;
import com.howbuy.crm.hb.domain.custinfo.OrgCommunicateVisit;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.custinfo.OrgCommunicateVisitService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.trade.common.constant.MarkConstants;
import com.howbuy.crm.util.FileUtil;
import com.howbuy.dfile.HFileService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @Description: 机构客户
 * @reason:
 * @Date: 2020/6/1 10:24
 */
@Slf4j
@Controller
@RequestMapping("/orgCust")
public class OrgCommunicateVisitController {

    
    @Autowired
    private OrgCommunicateVisitService orgCommunicateVisitService;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;
    
    @Autowired
    private ConscustService conscustService;

    @Autowired
    private CommonService commonService;

    // 2025年4月28日 始产线路径定义 ： /data/files/communicateFilePath/
    //但 从未有数据记录。
    //历史功能也有许多bug  几近不可用


    @RequestMapping("/listCommunicateVisit")
    public String listCommunicateVisit(HttpServletRequest request) {
    	String menucode = request.getParameter("menucode");
        String custno = request.getParameter("conscustno");
        request.setAttribute("menucode", menucode);
        request.setAttribute("custno", custno);
        request.setAttribute("startdt", DateTimeUtil.addMonthsFromNow(-3));
        request.setAttribute("enddt", DateTimeUtil.getCurDate());
        return "/custinfo/listOrgCommunicateVisit";

    }

    @RequestMapping("/delOrgCommunicateVisit.do")
    @ResponseBody
    public Object delOrgCommunicateVisit(HttpServletRequest request){
        try {
            String id = request.getParameter("id");
            orgCommunicateVisitService.delOrgCommunicateVisit(Long.parseLong(id));
            return "success";
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return "fail";
        }
    }
    
    @ResponseBody
    @RequestMapping("/queryOrgCommunicateVisitList")
    public Map<String, Object> queryOrgCommunicateVisitList(HttpServletRequest request) throws Exception{
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        param = new ParamUtil(request).getParamMap();
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String topgd = (String) request.getSession().getAttribute("topgddata");
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        String conscode = userlogin.getUserId();
        String outletcode = ConsOrgCache.getInstance().getUser2OutletMap().get(userlogin.getUserId());
        String tearmcode = ConsOrgCache.getInstance().getUser2TeamMap().get(userlogin.getUserId());
        String orgCode = "";
        if (StaticVar.DATARANGE_GD_ALL.equals(topgd) || StaticVar.DATARANGE_GD_ALL_NOWFP.equals(topgd)) {
            orgCode = "0";
        } else if (StaticVar.DATARANGE_GD_OUTLET.equals(topgd)) {
            orgCode = outletcode;
        } else if (StaticVar.DATARANGE_GD_TEARM.equals(topgd)) {
            orgCode = tearmcode;
        }
        param.put("orgCode", orgCode);
        param.put("conscode", conscode);
        List<OrgCommunicateVisit> list = orgCommunicateVisitService.listOrgCommunicateVisit(param);
        for(OrgCommunicateVisit visit : list){
        	visit.setCreator(ConsOrgCache.getInstance().getAllUserMap().get(visit.getCreator()));
        	visit.setVisittype(ConstantCache.getInstance().getVal("orgVisitType", visit.getVisittype()));
        }
        resultMap.put("total", list.size());
        resultMap.put("rows", list);
        return resultMap;
    }
    
  //导出操作
    @ResponseBody
    @RequestMapping("/exportOrgCommunicateVisit.do")
    public void exportOrgCommunicateVisit(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
        Map<String, String> param = new ParamUtil(request).getParamMap();
        String topgd = (String) request.getSession().getAttribute("topgddata");
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        String conscode = userlogin.getUserId();
        String outletcode = ConsOrgCache.getInstance().getUser2OutletMap().get(userlogin.getUserId());
        String tearmcode = ConsOrgCache.getInstance().getUser2TeamMap().get(userlogin.getUserId());
        String orgCode = "";
        if (StaticVar.DATARANGE_GD_ALL.equals(topgd) || StaticVar.DATARANGE_GD_ALL_NOWFP.equals(topgd)) {
            orgCode = "0";
        } else if (StaticVar.DATARANGE_GD_OUTLET.equals(topgd)) {
            orgCode = outletcode;
        } else if (StaticVar.DATARANGE_GD_TEARM.equals(topgd)) {
            orgCode = tearmcode;
        }
        param.put("orgCode", orgCode);
        param.put("conscode", conscode);
        List<OrgCommunicateVisit> list = orgCommunicateVisitService.listOrgCommunicateVisit(param);
        for(OrgCommunicateVisit visit : list){
        	visit.setCreator(ConsOrgCache.getInstance().getAllUserMap().get(visit.getCreator()));
        	visit.setVisittype(ConstantCache.getInstance().getVal("orgVisitType", visit.getVisittype()));
        }
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("机构客户沟通记录导出.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String[] columnName = {"生成时间","拜访日期","拜访内容","拜访地址","会谈形式","对方参会人","我方参会人","创建人"};

            String[] beanProperty = {"cratedt","visitdt","commcontent","visitaddr","visittype","othersideattend","ourattend","creator"};
            ExcelWriter.writeExcel(os, "沟通记录", 0, list, columnName, beanProperty);
            os.close(); // 关闭流
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }

    }
    
    @RequestMapping("/orgVisitDetail.do")
    public ModelAndView orgVisitDetail(HttpServletRequest request){
        //查询沟通信息
    	String id = request.getParameter("id");
        Map<String,Object> param = new HashMap<String,Object>();
        param.put("id", Long.valueOf(id));
        OrgCommunicateVisit visit = orgCommunicateVisitService.getOrgCommunicateVisit(param);
        Map<String,Object> map = new HashMap<String,Object>();
        if(visit != null){

            Conscust cust = conscustService.getConscust(visit.getCustno());
        	visit.setVisittype(ConstantCache.getInstance().getVal("orgVisitType", visit.getVisittype()));
        	visit.setOrgtype(ConstantCache.getInstance().getVal("orgType", cust.getOrgtype()));
        }else{
        	visit = new OrgCommunicateVisit();
        }
        map.put("visit", visit);
        //查询参会人
        param.clear();
        param.put("communicateid", Long.valueOf(id));
        List<OrgCommunicateAttend> listattend = orgCommunicateVisitService.listOrgCommunicateAttend(param);
        if(listattend != null){
        	for(OrgCommunicateAttend attend : listattend){
        		//如果是我司的员工，需要转成姓名
        		if("2".equals(attend.getAttendManType())){
        			attend.setAttendMan(ConsOrgCache.getInstance().getAllUserMap().get(attend.getAttendMan()));
        		}
        		attend.setAttendManType(ConstantCache.getInstance().getVal("orgAttentType", attend.getAttendManType()));
        		
        	}
        }
        map.put("listattend", listattend);
        List<OrgCommunicateFile> listfile = orgCommunicateVisitService.listOrgCommunicateFile(param);
        map.put("listfile", listfile);
        return new ModelAndView("custinfo/orgVisitDetail","map",map);
    }

    @RequestMapping("/addOrgCommunicate.html")
    public String addOrgCommunicate(HttpServletRequest request, Map map){
        String id = request.getParameter("id");
        String custNo = request.getParameter("conscustno");
        if(StringUtil.isNotNullStr(id)){
            Map param = new HashMap();
            param.put("id", id);
            OrgCommunicateVisit orgCommunicateVisit= orgCommunicateVisitService.getOrgCommunicateVisit(param);
            if(StringUtil.isNullStr(custNo)){
                custNo = orgCommunicateVisit.getCustno();
            }
            param.put("communicateid", id);
            List<OrgCommunicateAttend> orgCommunicateAttends = orgCommunicateVisitService.listOrgCommunicateAttend(param);
            List<OrgCommunicateFile> orgCommunicateFiles = orgCommunicateVisitService.listOrgCommunicateFile(param);
            map.put("orgCommunicateVisit", orgCommunicateVisit);
            map.put("orgCommunicateAttends", orgCommunicateAttends);
            map.put("orgCommunicateFiles", orgCommunicateFiles);
        }
        QueryConscustInfoRequest queryConscustInfoRequest = new QueryConscustInfoRequest();
        queryConscustInfoRequest.setConscustno(custNo);
        QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);
        map.put("orgType", queryConscustInfoResponse.getConscustinfo().getOrgtype());
        List<Map> consMapList = orgCommunicateVisitService.listConsForAttend();
        map.put("consList", consMapList);
        LinkedHashMap<String, String> orgAttentTypeList = ConstantCache.getInstance().getConstantKeyVal("orgAttentType");
        List<Map> attentTypeConst = new ArrayList<>();
        for(Map.Entry<String, String> enty : orgAttentTypeList.entrySet()){
            Map map1 = new HashMap(3);
            map1.put("id", enty.getKey());
            map1.put("name", enty.getValue());
            attentTypeConst.add(map1);
        }
        map.put("orgAttentTypeList", attentTypeConst);
        map.put("conscustno", custNo);
        return "/custinfo/addOrgCommunicate";
    }

    @RequestMapping("/addOrgCommunicateDeal.do")
    @ResponseBody
   // @Transactional
    public String addOrgCommunicateDeal(HttpServletRequest request){
        String id = request.getParameter("id");
        String userId = (String) request.getSession().getAttribute("userId");
        String visitdt = request.getParameter("visitdt");
        String visitaddr = request.getParameter("visitaddr");
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String orgVisitType = request.getParameter("orgVisitType");
        String commcontent = request.getParameter("commcontent");
        String rowsStr = request.getParameter("rowsStr");
        String commintent = request.getParameter("commintent");
        String support = request.getParameter("support");
        String remark = request.getParameter("remark");
        String conscustno = request.getParameter("conscustno");
        String deleteIds = request.getParameter("deleteIds");
        String deleteFilePaths = request.getParameter("deleteFilePaths");
        OrgCommunicateVisit orgCommunicateVisit = new OrgCommunicateVisit();
        orgCommunicateVisit.setVisitdt(visitdt);
        orgCommunicateVisit.setStarttime(startTime);
        orgCommunicateVisit.setEndtime(endTime);
        orgCommunicateVisit.setVisitaddr(visitaddr);
        orgCommunicateVisit.setVisittype(orgVisitType);
        orgCommunicateVisit.setCommcontent(commcontent);
        orgCommunicateVisit.setCommintent(commintent);
        orgCommunicateVisit.setSupport(support);
        orgCommunicateVisit.setRemark(remark);
        try {
            JSONArray jsonArray = JSON.parseArray(rowsStr);

            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest)request;
            //所有上传文件集合
            Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

            //参会人列表
            List<OrgCommunicateAttend> orgCommunicateAttends = jsonArray.toJavaList(OrgCommunicateAttend.class);
            StringBuilder ourAttendStrBuilder = new StringBuilder();
            StringBuilder otherSideAttendStrBuilder = new StringBuilder();

            ConsOrgCache consOrgCache = ConsOrgCache.getInstance();

            HFileService instance = HFileService.getInstance();

            for(OrgCommunicateAttend communicateAttend : orgCommunicateAttends){
                if(StringUtil.isNotNullStr(id)) {
                    communicateAttend.setCommunicateId(Long.valueOf(id));
                }
                if("2".equals(communicateAttend.getAttendManType())){
                    ourAttendStrBuilder.append(",").append(consOrgCache.getAllUserMap().get(communicateAttend.getAttendMan()));
                    if(StringUtil.isNotNullStr(communicateAttend.getOtherAttendMan())){
                        ourAttendStrBuilder.append(",").append(communicateAttend.getOtherAttendMan());
                    }
                }else {
                    otherSideAttendStrBuilder.append(",").append(communicateAttend.getAttendMan());
                    if(StringUtil.isNotNullStr(communicateAttend.getOtherAttendMan())){
                        otherSideAttendStrBuilder.append(",").append(communicateAttend.getOtherAttendMan());
                    }
                }
            }
            String ourAttendStr = ourAttendStrBuilder.toString();
            String otherSideAttend = otherSideAttendStrBuilder.toString();
            //把参会人写入主表字段方便查询
            orgCommunicateVisit.setOurattend(ourAttendStr.substring(1));
            orgCommunicateVisit.setOthersideattend(otherSideAttend.substring(1));
            if(StringUtil.isNotNullStr(id)){
                orgCommunicateVisit.setId(Long.valueOf(id));
                orgCommunicateVisit.setModifier(userId);
                orgCommunicateVisitService.update(orgCommunicateVisit);
            }else {
                orgCommunicateVisit.setCustno(conscustno);
                orgCommunicateVisit.setCreator(userId);
                //插入主表
                orgCommunicateVisitService.insert(orgCommunicateVisit);
                //设主键id
                for(OrgCommunicateAttend communicateAttend : orgCommunicateAttends) {
                    communicateAttend.setCommunicateId(orgCommunicateVisit.getId());
                }
            }

            if(StringUtil.isNotNullStr(deleteIds)){
                //删除数据库记录
                orgCommunicateVisitService.deleteFileByIds(deleteIds);
                //删除服务器文件  不再做  实体文件  删除操作
//                deleteFile(deleteFilePaths);
            }
            //文件列表
            List<OrgCommunicateFile> communicateFiles = new ArrayList<>(fileMap.size());
            for(Map.Entry<String, MultipartFile> entry : fileMap.entrySet()){
                OrgCommunicateFile orgCommunicateFile = new OrgCommunicateFile();
                orgCommunicateFile.setCommunicateId(orgCommunicateVisit.getId());

                MultipartFile multipartFile = entry.getValue();
                try {
                    // 尝试写入文件
                    String relativeFilePath =  DateTimeUtil.getCurrYMD();
                    //上传文件 原始  text.pdf
                    String shortFileName = FileUtil.getFileShortName(multipartFile.getOriginalFilename());
                    //  PDF
                    String   suffixName =FileUtil.getUpperSuffixName(shortFileName);
                    // 202504280900_text.PDF
                    String storeFileName=String.join(MarkConstants.SEPARATOR_DOT,
                            DateTimeUtil.getCurDateTime() + "_" +FileUtil.removeSuffix(shortFileName),
                            suffixName);
                    instance.write(DfileConstants.ORG_COMMUNICATE_STORE_CONFIG, relativeFilePath, storeFileName, multipartFile.getBytes());

                    orgCommunicateFile.setFileName(storeFileName);
                    orgCommunicateFile.setFileSize((int)multipartFile.getSize());
                    //切换为 webdav .   不能再支持NFS全路径， 存放相对路径
                    orgCommunicateFile.setFileUrl(relativeFilePath);
                    orgCommunicateFile.setFileSuffix(suffixName);
                    orgCommunicateFile.setId(Long.valueOf(commonService.getSeqValue("seq_org_linkman")));
                    communicateFiles.add(orgCommunicateFile);
                    log.info("");
                }catch (Exception e){
                    log.error("机构沟通记录文件上传失败",e);
                }
            }
            //插入参会人及文件
            orgCommunicateVisitService.insertExtend(orgCommunicateAttends, communicateFiles, id);
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return "fail";
        }
        return "success";
    }


    @RequestMapping(value = "/preorgviewimg")
    public String preorgviewimg(HttpServletRequest request) {
        String id = request.getParameter("id");
        request.setAttribute("id", id);

        return "/custinfo/previeworgvisitimg";
    }
    
    @ResponseBody
    @RequestMapping("/getOrgCommunicateFileStream.do")
    public void getOrgCommunicateFileStream(HttpServletRequest request, HttpServletResponse response) {
        String id = request.getParameter("id");
        Map<String,Object> param = new HashMap<String,Object>();
        param.put("id", Long.valueOf(id));
        OrgCommunicateFile orgfiel = orgCommunicateVisitService.getOrgCommunicateFile(param);

        String fileName = orgfiel.getFileName();

        //读取
        HFileService instance = HFileService.getInstance();
        byte[]  fileBytes= new byte[0];
        try {
            log.info("文件file：{} 读取开始，relativePath:{},fileName:{}", JSON.toJSONString(orgfiel),orgfiel.getFileUrl(),fileName);
            fileBytes = instance.read2Bytes(DfileConstants.ORG_COMMUNICATE_STORE_CONFIG,
                    orgfiel.getFileUrl(),
                    fileName);
        } catch (Exception e) {
            log.error("文件file：{} ，读取异常！",JSON.toJSONString(orgfiel));
            log.error("读取文件异常！",e);
        }

        ServletOutputStream outputStream = null;
        try {
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;fileName=" + new String(fileName.getBytes("gb2312"), StandardCharsets.ISO_8859_1));
            //输出文件流
            outputStream = response.getOutputStream();
            outputStream.write(fileBytes);
        }catch (Exception e){
            log.error("预览失败，fileName：{}, useId：{}", fileName, e);
        }finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭文件流异常", e);
                }
            }
        }

    }

}
