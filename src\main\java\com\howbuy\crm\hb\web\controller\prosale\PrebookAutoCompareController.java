package com.howbuy.crm.hb.web.controller.prosale;

import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.domain.prosale.autocompare.SearchAutoCompareDetailVo;
import com.howbuy.crm.hb.domain.prosale.autocompare.SearchAutoCompareSummaryVo;
import com.howbuy.crm.hb.service.prosale.CmPrebookAutoCompareService;
import com.howbuy.crm.prosale.dto.AvailBalanceInfoLocal;
import com.howbuy.crm.prosale.dto.CmPrebookAutoCompareDetail;
import com.howbuy.crm.prosale.dto.CmPrebookAutoCompareSummary;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 数据自动核对Controller
 * <AUTHOR>
 * @date 2022/10/19 19:44
 */
@Controller
@RequestMapping("/prosale/autoCompare")
@Slf4j
public class PrebookAutoCompareController {

    @Autowired
    private CmPrebookAutoCompareService cmPrebookAutoCompareService;

    /**
     * 初始化 数据自动核对 页面
     * @return
     */
    @RequestMapping("/initAutoCompareSummary.do")
    public String initAutoCompareSummaryPage() {
        return "/prosale/autocompare/listAutoCompareSummary";
    }

    /**
     * 数据自动核对
     * @return
     */
    @RequestMapping("/listAutoCompareSummary.do")
    @ResponseBody
    public List<CmPrebookAutoCompareSummary> listAutoCompareSummary(SearchAutoCompareSummaryVo searchVo) {
        return cmPrebookAutoCompareService.listAutoCompareSummary(searchVo);
    }

    /**
     * 初始化 数据自动详情 页面
     * @return
     */
    @RequestMapping("/initAutoCompareDetail.do")
    public String initAutoCompareDetailPage() {
        return "/prosale/autocompare/listAutoCompareDetail";
    }

    /**
     * 数据自动详情
     * @return
     */
    @RequestMapping("/listAutoCompareDetail.do")
    @ResponseBody
    public List<CmPrebookAutoCompareDetail> listAutoCompareDetail(SearchAutoCompareDetailVo searchVo) {
        return cmPrebookAutoCompareService.listAutoCompareDetail(searchVo);
    }

    /**
     * 导出数据自动核对详情
     */
    @RequestMapping("/exportAutoCompareDetail.do")
    @ResponseBody
    public void exportAutoCompareDetail(HttpServletResponse response, SearchAutoCompareDetailVo searchVo) {
        try {
            cmPrebookAutoCompareService.exportAutoCompareDetail(response, searchVo);
        } catch (Exception e) {
            log.error("导出数据自动核对详情出错 " + e.getMessage(), e);
        }
    }

    /**
     * 显示银行卡明文
     * @param bankAcctDigest 银行卡号摘要
     * @param hboneNo 一账通号
     * @return
     */
    @RequestMapping("/showBankAcct")
    @ResponseBody
    public ReturnMessageDto<String> showBankAcct(String bankAcctDigest, String hboneNo) {
        return cmPrebookAutoCompareService.getBankAcct(bankAcctDigest, hboneNo);
    }

    /**
     * 查询可用余额列表
     * @param productCode
     * @return
     */
    @RequestMapping("/queryProdAvailBalanceList")
    @ResponseBody
    public ReturnMessageDto<AvailBalanceInfoLocal> queryProdAvailBalanceList(String productCode) {
        return cmPrebookAutoCompareService.queryProdAvailBalanceList(productCode);
    }
}
