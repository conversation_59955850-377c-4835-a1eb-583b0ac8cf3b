package com.howbuy.crm.hb.web.controller.custinfo;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.howbuy.common.page.Page;
import com.howbuy.common.page.Pagination;
import com.howbuy.content.service.interview.CompanyReportService;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.core.service.PageJjxxInfoService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.util.FileUtil;
import com.howbuy.persistence.content.interview.CompanyReport;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse.BalanceBean;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Controller
@RequestMapping(value = "/conscust")
public class DownloadFundReportController {
	
	@Autowired
	private CompanyReportService companyReportService;
	
	@Autowired
	private ConscustService custService;
	
	@Autowired
    private QueryConscustInfoService queryConscustInfoService;
	
	@Autowired
	private PageJjxxInfoService jjxxInfoService;

	@Autowired
	private JjxxInfoService jjxxService;
	
	@Autowired
    private QueryAcctBalanceFacade queryAcctBalanceFacade;

	@Autowired
	private QueryDealOrderListFacade queryDealOrderListFacade;
	
	@Value("${DOWNLOAD_FUNDREPORT_IP}")
	private String downloadfundreportip;
	
	//用户详情索引页
	@RequestMapping("/custHoldFundIndex.do")
	public ModelAndView forwardCustIndex(HttpServletRequest request){
		ModelAndView modelAndView = new ModelAndView();
		String  keyuser = request.getParameter("keyuser");
		User user = (User) request.getSession().getAttribute("loginUser");
		if(!keyuser.equals(Util.getkeyUser(user.getUserId()))){
			return null;
		}
		modelAndView.addObject("conscustno", request.getParameter("conscustno"));
		List<String> funds = new ArrayList<String>();
		// 查询换成调用core中接口
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(request.getParameter("conscustno"));
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();
		//查询客户持有的高净值产品代码
		List<BalanceBean> listbean = getBalanceListbean(request, conscust);
		for(BalanceBean bean : listbean){
			if(bean.getBalanceVol() != null && bean.getBalanceVol().compareTo(new BigDecimal(0)) > 0){
				if(StringUtils.isNotBlank(bean.getProductCode())){
					JjxxInfo jjxx1 = jjxxService.getJjxxByJjdm(bean.getProductCode(), false);
					if(jjxx1 != null){
						String fundCode = Util.ObjectToString(bean.getProductCode()+jjxx1.getJjjc());
						if(!funds.contains(fundCode)){
							funds.add(fundCode);
						}
					}
				}
			}
		}
		modelAndView.addObject("funds", funds);
        modelAndView.setViewName("/custinfo/fundReportIndex");
		return modelAndView;
	}
	
	public List<BalanceBean> getBalanceListbean(HttpServletRequest request, ConscustInfoDomain conscust) {
        QueryAcctBalanceRequest req = new QueryAcctBalanceRequest();
        req.setOutletCode("A20150120");
        req.setOperIp(getIpAddr(request));
        req.setTxChannel("1");
        req.setDataTrack("crmsale");
        if (conscust == null) {
            return new ArrayList<BalanceBean>();
        }
        req.setHbOneNo(conscust.getHboneno());
//        req.setDisCode(custService.getDiscodeByConsCustno(conscust.getConscustno()));
		req.setDisCodeList(jjxxService.getHbFullDisCodeList());
        List<BalanceBean> listbean = queryAcctBalanceFacade.execute(req).getBalanceList();
        return listbean;
    }
	
	/**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    public String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    //用户详情索引页
  	@RequestMapping("/loadfundreporthtm.do")
  	public ModelAndView loadfundreporthtm(HttpServletRequest request){
  		ModelAndView modelAndView = new ModelAndView();
  		modelAndView.addObject("fundcode", request.getParameter("fundcode"));
  		modelAndView.addObject("conscustno", request.getParameter("conscustno"));
        modelAndView.setViewName("/custinfo/listSignFundReport");
  		return modelAndView;
  	}

	//用户详情索引页
	@RequestMapping("/loaddocreporthtm.do")
	public ModelAndView loaddocreporthtm(HttpServletRequest request){
		ModelAndView modelAndView = new ModelAndView();
		String conscustno = request.getParameter("conscustno");
		String fundcode = request.getParameter("fundcode");
		modelAndView.addObject("fundcode", fundcode);
		modelAndView.addObject("conscustno", conscustno);
//		String disCode = custService.getDiscodeByConsCustno(conscustno);
		QueryConscustInfoRequest queryConscustInfoRequest = new QueryConscustInfoRequest();
		queryConscustInfoRequest.setConscustno(conscustno);
		QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);
		//取基金代码对应的最早确认日期
		String ackDt = getFundAckDt(request, queryConscustInfoResponse.getConscustinfo().getHboneno(),  fundcode);
		modelAndView.addObject("ackDt", ackDt);
		modelAndView.setViewName("/custinfo/listDocFundReport");
		return modelAndView;
	}
	
	@ResponseBody
	@RequestMapping(value="/showSignFund.do")
	public Map<String,Object> showSignFund(HttpServletRequest request) throws Exception{
		String fundcode = request.getParameter("fundcode");
		String conscustno = request.getParameter("conscustno");
		Map<String,Object> resultMap = new HashMap<String, Object>();
		List<String> fundCodes = new ArrayList<String>();
		String newfundcode = jjxxInfoService.getMpcode(fundcode);
		fundCodes.add(newfundcode);
		List<String> companyId = new ArrayList<String>();
		//查询产品的管理人码
		JjxxInfo jjxx1 = jjxxService.getJjxxByJjdm(newfundcode, false);
		if(jjxx1 != null){
			companyId.add(jjxx1.getGlrm());
		}
		String pageNo =request.getParameter("page");
		String pageSize = request.getParameter("rows");
		int page = Integer.parseInt(pageNo); // 当前页数
		int rows = Integer.parseInt(pageSize); // 每页显示记录数
		Page pageparam = new Page();
		pageparam.setPage(page);
		pageparam.setPerPage(rows);
	    Pagination<CompanyReport> list = companyReportService.getCompanyReportListByStatus(companyId,fundCodes,null,null, null, pageparam, 0);
		log.info("查询报告列表: {}", JSON.toJSONString(list));
	    if(list!=null && list.getResultList().size()>0){
			// 记录过滤掉的记录数
			int filterCount = 0;
//			String disCode = custService.getDiscodeByConsCustno(conscustno);
			QueryConscustInfoRequest queryConscustInfoRequest = new QueryConscustInfoRequest();
			queryConscustInfoRequest.setConscustno(conscustno);
			QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);
			// 该客户购买基金代码 最早的[上报日]，如果为空，则取基金代码对应的[最早确认日期]
			String appDtOrAckDt = getFundAppDtOrAckDt(request, queryConscustInfoResponse.getConscustinfo().getHboneno(), newfundcode);
			log.info("一账通号:{}，基金代码: {} 过滤最早日期: {}",
					queryConscustInfoResponse.getConscustinfo().getHboneno(), newfundcode, appDtOrAckDt);
			List<CompanyReport> newResultList = new ArrayList<CompanyReport>();
			for(int i = 0; i <list.getResultList().size(); i++){
				CompanyReport companyReport = list.getResultList().get(i);
				//最早交易日期之前的报告不显示
				if(appDtOrAckDt != null && companyReport.getReportDate() != null){
					String reportDate = companyReport.getReportDate().replace("-", "");
					if(appDtOrAckDt.compareTo(reportDate) > 0){
						log.info("最早交易日期之前的报告不显示，一账通号:{}，基金代码: {} 过滤最早日期: {}，报告日期: {}",
								queryConscustInfoResponse.getConscustinfo().getHboneno(), newfundcode, appDtOrAckDt, companyReport.getReportDate());
						filterCount++;
						continue;
					}
				}
				if(StringUtils.isBlank(companyReport.getRedirectUrl())){
					companyReport.setRedirectUrl("null");
				}
				// 增加根据类型对展示报告内容过滤方法（只展示报告类型reportType为：4、5、6、7、8、9的，为6时，判断status=0的前端不展示）
				String reportType = companyReport.getReportType();
				int status = companyReport.getStatus();
				if ("4".equals(reportType) || "5".equals(reportType) || ("6".equals(reportType) && status == 0) || "7".equals(reportType) || "8".equals(reportType) || "9".equals(reportType) || "13".equals(reportType)) {
					newResultList.add(companyReport);
				}else{
					log.info("根据类型对展示报告内容过滤方法，一账通号:{}，基金代码: {}，报告类型: {}，报告状态: {}，不展示",
							queryConscustInfoResponse.getConscustinfo().getHboneno(), newfundcode, reportType, status);
					filterCount++;
				}
			}
			resultMap.put("rows", newResultList);
			resultMap.put("total", list.getPage().getTotal() - filterCount);
		}else{
			resultMap.put("rows", 0);
			resultMap.put("total",0);
		}
		/*
		Map<String,Object> resultMap = new HashMap<String, Object>();
		List<CompanyReport> newResultList = new ArrayList<CompanyReport>();
		CompanyReport re = new CompanyReport();
		re.setFundCode("P23782");
		re.setId(*********);
		re.setTitle("【信息披露】新方程大岩定增3号尊享A基金2018年4季度报");
		re.setReportDate("2019-01-30");
		re.setRedirectUrl("https://static.howbuy.com/upload/cmsupload/8f1a34ab653327dda389d178897c104f.pdf");
		newResultList.add(re);
		resultMap.put("rows", newResultList);
		resultMap.put("total", 1);*/
		return resultMap;
	}

	/**
	 * 取基金代码对应的最早确认日期
	 * @param request
	 * @param hboneNo
	 * @param fundCode
	 * @return
	 */
	private String getFundAckDt(HttpServletRequest request, String hboneNo,String fundCode){
		QueryDealOrderListResponse queryDealOrderListResponse = djy(getIpAddr(request), hboneNo, null);
		List<QueryDealOrderListResponse.DealOrderBean> listData = queryDealOrderListResponse.getDealOrderList();
		String ackDt = null;
		if(CollectionUtils.isNotEmpty(listData)){
			List<QueryDealOrderListResponse.DealOrderBean> listData1 = listData.stream().filter(
					dealOrderBean -> dealOrderBean.getProductCode().equals(fundCode) && StringUtil.isNotNullStr(dealOrderBean.getAckDt()))
					.collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(listData1)) {
				Collections.sort(listData1, Comparator.comparing(QueryDealOrderListResponse.DealOrderBean::getAckDt));
				ackDt = listData1.get(0).getAckDt();
			}
		}
		return ackDt;
	}

	/**
	 * @description: 获取基金代码对应的[最早确认日期]或[上报日]
	 * @param request
	 * @param hboneNo
	 * @param fundCode
	 * @return java.lang.String
	 * @author: jin.wang03
	 * @date: 2025/5/12 19:21
	 * @since JDK 1.8
	 */
	private String getFundAppDtOrAckDt(HttpServletRequest request, String hboneNo, String fundCode) {
		QueryDealOrderListResponse queryDealOrderListResponse = djy(getIpAddr(request), hboneNo, null);
		List<QueryDealOrderListResponse.DealOrderBean> listData = queryDealOrderListResponse.getDealOrderList();
		log.info("查询一账通号:{}，交易列表: {}", hboneNo, JSON.toJSONString(listData));

		if (CollectionUtils.isEmpty(listData)) {
			return null;
		}
		List<QueryDealOrderListResponse.DealOrderBean> listData1 = listData.stream().filter(
						dealOrderBean -> dealOrderBean.getProductCode().equals(fundCode) && StringUtil.isNotNullStr(dealOrderBean.getAckDt()))
				.collect(Collectors.toList());
		log.info("查询一账通号:{}，基金代码: {}，交易列表: {}", hboneNo, fundCode, JSON.toJSONString(listData1));
		if (CollectionUtils.isEmpty(listData1)) {
			return null;
		}

		Optional<String> earliestDate = listData1.stream()
				.map(order -> {
					// 尝试获取 submitTaDt 或 ackDt，如果都为 null，则返回 null
					String date = order.getSubmitTaDt();
					return date != null ? date : order.getAckDt();
				})
				// 过滤掉 null 或空白字符串
				.filter(StringUtils::isNotBlank)
				// 获取最小（最早）日期
				.min(Comparator.naturalOrder());

		earliestDate.ifPresent(date -> log.info("查询一账通号:{}，基金代码: {}，最早的日期: {}", hboneNo, fundCode, date));

		return earliestDate.orElse(null);
	}

	/**
	 * 如果discodeList 不为空，使用disCodeList.否则取好买平台下所有分销
	 * @param ip
	 * @param hboneno
	 * @param discodeList
	 * @return
	 */
	public QueryDealOrderListResponse djy(String ip, String hboneno, List<String> discodeList) {
		QueryDealOrderListRequest req = new QueryDealOrderListRequest();
		req.setOutletCode("A20150120");
		req.setOperIp(ip);
		req.setTxChannel("1");
		req.setDataTrack("crmsale");
		req.setPageSize(500);
		req.setHbOneNo(hboneno);
		if(CollectionUtils.isEmpty(discodeList)){
			discodeList=jjxxService.getHbFullDisCodeList();
		}
		req.setDisCodeList(discodeList);
		QueryDealOrderListResponse dealOrderlist = queryDealOrderListFacade.execute(req);
		return dealOrderlist;
	}
	
	@ResponseBody
	@RequestMapping("/batchDownloadReport.do")
	public void batchDownloadReport(HttpServletRequest request,HttpServletResponse response) {
		User user = (User) request.getSession().getAttribute("loginUser");
		String ids = StringUtil.getStr(request.getParameter("ids"));
		String consname = ConsOrgCache.getInstance().getAllConsMap().get(user.getUserId());
		String conscode = user.getUserId();
		String conscustno = StringUtil.getStr(request.getParameter("conscustno"));
		Map<String,String> param = new HashMap<String,String>();
		param.put("conscustno", request.getParameter("conscustno"));
		// 查询换成调用core中接口
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(request.getParameter("conscustno"));
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();
        String conscustname = conscust.getCustname();
		String fundcode = StringUtil.getStr(request.getParameter("fundcode"));
		List<Map<String,String>> list = new ArrayList<Map<String,String>>();
		if(ids != null){
			if(ids.indexOf(",") != -1){
				String [] idarr = ids.split(",");
				for(String id : idarr ){
					Map<String,String> map = new HashMap<String,String>();
					map.put("id", id.split("#")[0]);
					map.put("title", id.split("#")[1]);
					map.put("type", getFileType(id.split("#")[2]));
					map.put("redirectUrl", id.split("#")[3]);
					list.add(map);
				}
			}else{
				Map<String,String> map = new HashMap<String,String>();
				map.put("id", ids.split("#")[0]);
				map.put("title", ids.split("#")[1]);
				map.put("type", getFileType(ids.split("#")[2]));
				map.put("redirectUrl", ids.split("#")[3]);
				list.add(map);
			}
		}
		OutputStream fos = null;
		ZipOutputStream zipOutputStream = null;
		List <String> titles = new ArrayList<String>();
		try {
			response.setContentType("application/force-download");
			response.setHeader("Content-disposition","attachment;filename=" + new String("产品报告.zip".getBytes("gb2312"), "ISO8859-1"));
			fos = response.getOutputStream();
			zipOutputStream = new ZipOutputStream(fos);
			for(Map<String,String> map : list){
				ZipEntry ze = new ZipEntry(map.get("title")+"."+map.get("type"));// 这是压缩包名里的文件名
				titles.add(map.get("title")+"."+map.get("type"));
				zipOutputStream.putNextEntry(ze);// 写入新的 ZIP
				String pdfFilePath = downloadfundreportip+map.get("id")+".htm"; 
				if(!"null".equals(map.get("redirectUrl"))){
					pdfFilePath = StringUtil.replaceNull(map.get("redirectUrl"));
				}
				URL url = new URL(pdfFilePath);    
		        HttpURLConnection conn = (HttpURLConnection)url.openConnection();    
		        //设置超时间为3秒  
		        conn.setConnectTimeout(3*1000);  
		        //防止屏蔽程序抓取而返回403错误  
		        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
		        //得到输入流  
		        InputStream inputStream = conn.getInputStream(); 
		        byte[] bosbyte = FileUtil.readInputStream(pdfFilePath);
		        if(bosbyte.length > 0){
					if("pdf".equals(map.get("type"))){
						PDDocument pd = PDDocument.load(inputStream);
						if(pd != null){
							if(pd.isEncrypted()){
								zipOutputStream.write(FileUtil.readInputStream(pdfFilePath));
							}else{
								PdfReader pdfReader = new PdfReader(pdfFilePath);
								ByteArrayOutputStream bos = new ByteArrayOutputStream(); 
								PdfStamper pdfStamper = new PdfStamper(pdfReader, bos);
								if("pdf".equals(map.get("type"))){
									FileUtil.addWatermark(pdfStamper, "仅供高端客户 "+conscustno+" 使用","好买财富"+consname+"  "+Util.getDateYearMonthDay());
								}
								pdfStamper.close();
								bos.close();
								zipOutputStream.write(bos.toByteArray());
							}
							pd.close();
						}
					}else{
				        zipOutputStream.write(bosbyte);
					}
		        }
				if(inputStream!=null){  
		            inputStream.close();  
		        }  
			}
			if(zipOutputStream!=null){
				zipOutputStream.close();
			}
			for(String title : titles){
				Map<String,String> inparam = new HashMap<String,String>();
				inparam.put("conscode", conscode);
				inparam.put("fundcode", fundcode);
				inparam.put("conscustno", conscustno);
				inparam.put("custname", conscustname);
				inparam.put("opttype", StaticVar.REPORTFUND_OPT_DOWN);
				inparam.put("filename", title);
				custService.insertDownloadFundReport(inparam);
			}
		} catch (Exception e0) {
			log.error("批量下载报告执行失败", e0);
		}finally{
			if(fos != null){
				try {
					fos.close();
				} catch (IOException e) {
					log.error("批量下载报告执行失败", e);
				}
			}
			if(zipOutputStream != null){
				try {
					zipOutputStream.close();
				} catch (IOException e) {
					log.error("批量下载报告执行失败", e);
				}
			}
		}
	}
	
	@ResponseBody
	@RequestMapping("/batchDownloadReportpdf.do")
	public void batchDownloadReportpdf(HttpServletRequest request,HttpServletResponse response) {
		User user = (User) request.getSession().getAttribute("loginUser");
		String ids = StringUtil.getStr(request.getParameter("ids"));
		String consname = ConsOrgCache.getInstance().getAllConsMap().get(user.getUserId());
		String conscode = user.getUserId();
		String conscustno = StringUtil.getStr(request.getParameter("conscustno"));
		Map<String,String> param = new HashMap<String,String>();
		param.put("conscustno", request.getParameter("conscustno"));
		// 查询换成调用core中接口
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(request.getParameter("conscustno"));
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();
        String conscustname = conscust.getCustname();
		String fundcode = StringUtil.getStr(request.getParameter("fundcode"));
		//List<Map<String,String>> list = new ArrayList<Map<String,String>>();
		String title = "";
		String type = "";
		String redirectUrl = "";
		String filename = "";
		if(ids != null){
			filename = ids.split("#")[0];
			title = ids.split("#")[1];
			type = getFileType(ids.split("#")[2]);
			redirectUrl = ids.split("#")[3];
		}
		OutputStream fos = null;
		try {
			response.setContentType("application/force-download");
			response.setHeader("Content-disposition","attachment;filename=" + new String((title+"."+type).getBytes("gb2312"), "ISO8859-1"));
			fos = response.getOutputStream();
			
			String pdfFilePath = downloadfundreportip+filename+".htm"; 
			if(!"null".equals(redirectUrl)){
				pdfFilePath = StringUtil.replaceNull(redirectUrl);
			}
			URL url = new URL(pdfFilePath);    
	        HttpURLConnection conn = (HttpURLConnection)url.openConnection();    
	        //设置超时间为3秒  
	        conn.setConnectTimeout(3*1000);  
	        //防止屏蔽程序抓取而返回403错误  
	        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
	        //得到输入流  
	        InputStream inputStream = conn.getInputStream(); 
	        byte[] bosbyte = FileUtil.readInputStream(pdfFilePath);
	        if(bosbyte.length > 0){
				if("pdf".equals(type)){
					PDDocument pd = PDDocument.load(inputStream);
					if(pd != null){
						if(pd.isEncrypted()){
							fos.write(FileUtil.readInputStream(pdfFilePath));
						}else{
							PdfReader pdfReader = new PdfReader(pdfFilePath);
							ByteArrayOutputStream bos = new ByteArrayOutputStream(); 
							PdfStamper pdfStamper = new PdfStamper(pdfReader, bos);
							if("pdf".equals(type)){
								FileUtil.addWatermark(pdfStamper, "仅供高端客户 "+conscustno+" 使用","好买财富"+consname+"  "+Util.getDateYearMonthDay());
							}
							pdfStamper.close();
							bos.close();
							fos.write(bos.toByteArray());
						}
						pd.close();
					}
				}else{
					fos.write(bosbyte);
				}
	        }
				if(inputStream!=null){  
		            inputStream.close();  
		        }  
			
			if(fos!=null){
				fos.close();
			}
			Map<String,String> inparam = new HashMap<String,String>();
			inparam.put("conscode", conscode);
			inparam.put("fundcode", fundcode);
			inparam.put("conscustno", conscustno);
			inparam.put("custname", conscustname);
			inparam.put("opttype", StaticVar.REPORTFUND_OPT_DOWN);
			inparam.put("filename", title+"."+type);
			custService.insertDownloadFundReport(inparam);
		} catch (Exception e0) {
			log.error("下载报告执行失败", e0);
		}finally{
			if(fos != null){
				try {
					fos.close();
				} catch (IOException e) {
					log.error("下载报告执行失败", e);
				}
			}
		}
	}
	
	/**
     * 根据文件类型返回文件后缀
     * 支持 ms office 和 wps  pdf
     * @param type 文件类型
     * @return 文件后缀
     */
    private String getFileType(String type){
    	if(StringUtil.isEmpty(type)){
    		return "pdf";
    	}else if("application/pdf".equals(type)){
    		return "pdf";
    	}else if("application/msword".equals(type) || "application/kswps".equals(type)){
    		return "doc";
    	}else if("application/vnd.ms-powerpoint".equals(type) || "application/ksdps".equals(type)){
    		return "ppt";
    	}else if("application/vnd.ms-excel".equals(type) || "application/kset".equals(type)){
    		return "xls";
    	}
    	return "pdf";
    }
	
}
