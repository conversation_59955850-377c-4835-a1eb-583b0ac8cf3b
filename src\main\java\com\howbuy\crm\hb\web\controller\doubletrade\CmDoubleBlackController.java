package com.howbuy.crm.hb.web.controller.doubletrade;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.doubletrade.service.UpdateInitStatusToZtService;
import com.howbuy.crm.hb.domain.doubletrade.CmDoubleBlack;
import com.howbuy.crm.hb.domain.doubletrade.CmDoubleTrade;
import com.howbuy.crm.hb.service.doubletrade.CmDoubleBlackService;
import com.howbuy.crm.hb.service.doubletrade.CmDoubleTradeService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * @Description: Controller
 * @version 1.0
 */
@Controller
@RequestMapping(value = "/doubletrade")
public class CmDoubleBlackController {
	private Logger logger = LoggerFactory.getLogger(CmDoubleBlackController.class);

	@Autowired
	private CmDoubleBlackService cmDoubleBlackService;
	
	@Autowired
	private CmDoubleTradeService cmDoubleTradeCRMService;

	@Autowired
	private UpdateInitStatusToZtService updateInitStatusToZtService;

	/**
	 * 双录回访黑名单表
	 * 
	 * @return
	 */
	@RequestMapping("/listCmDoubleBlack.do")
	public String listCmDoubleBlack() {
		return "doubletrade/listCmDoubleBlack";
	}

	/**
	 * 加载数据方法
	 * 
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/listCmDoubleBlack_json.do")
	public Map<String, Object> listCmDoubleBlack_json(HttpServletRequest request, HttpServletResponse response)	throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		param = new ParamUtil(request).getParamMap();
		String curPage = request.getParameter("page");
		String fundCode = request.getParameter("fundCode");

		// 如果查询条件（基金代码）不为空，则增加基金代码查询参数
		if (StringUtil.isNotNullStr(fundCode)) {
			param.put("fundCode", fundCode);
		} else {
			param.put("fundCode", null);
		}

		// 判断常量表中合规标识：true启用，false停用
		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
		boolean roleCPFlag = false;
		if (cacheMap != null && !cacheMap.isEmpty()) {
			roleCPFlag = JSON.toJSONString(cacheMap).contains("true");
		}

		// 判断登录人员的角色中是否包括“合规人员”角色
		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
		if (roleCPFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
			param.put("hascp", "true");
		}
		PageData<CmDoubleBlack> loadData = cmDoubleBlackService.listCmDoubleBlackByPage(param);
		// 对枚举字段进行转义
		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		for (CmDoubleBlack info : loadData.getListData()) {
			info.setCreator(consOrgCache.getAllUserMap().get(info.getCreator()));
			info.setModifier(consOrgCache.getAllUserMap().get(info.getModifier()));
		}

		resultMap.put("total", loadData.getPageBean().getTotalNum());
		resultMap.put("page", curPage);
		resultMap.put("rows", loadData.getListData());

		return resultMap;
	}

	/**
	 * 新增数据方法
	 * 
	 * @return Map
	 */
	@ResponseBody
	@RequestMapping("/saveCmDoubleBlack.do")
	public Map<String, String> saveCmDoubleBlack(HttpServletRequest request, HttpServletResponse response) {
		Map<String, String> resultMap = new HashMap<String, String>();
		try {
			// 设置参数
			Map<String, String> param = new ParamUtil(request).getParamMap();
			String fundCode = param.get("fundCode");
			String needDouble = param.get("needDouble");
			String needVisit = param.get("needVisit");

			// 判断记录是否已存在
			Map<String, String> tempParam = new HashMap<String, String>();
			tempParam.put("fundCode", fundCode);
			List<CmDoubleBlack> list = cmDoubleBlackService.listCmDoubleBlack(tempParam);
			if (list != null && list.size() > 0) {
				resultMap.put("msg", "hadRecord");
				return resultMap;
			}

			// 获取登陆用户信息
			HttpSession session = request.getSession();
			String userId = (String) session.getAttribute("userId");

			// 组装记录所需数据
			CmDoubleBlack insertCmDoubleBlack = new CmDoubleBlack();
			insertCmDoubleBlack.setFundCode(fundCode);
			insertCmDoubleBlack.setRecState("0");
			insertCmDoubleBlack.setCreator(userId);
			if (StringUtils.isNotBlank(needDouble)) {
				insertCmDoubleBlack.setNeedDouble(needDouble);
			}

			if (StringUtils.isNotBlank(needVisit)) {
				insertCmDoubleBlack.setNeedVisit(needVisit);
			}

			// 保存数据数据库中
			cmDoubleBlackService.insertCmDoubleBlack(insertCmDoubleBlack);
			resultMap.put("msg", "addSuccess");
		} catch (Exception e) {
			logger.error("saveCmDoubleBlack异常",e);
			resultMap.put("msg", "hadError");
		}
		return resultMap;
	}

	/**
	 * 修改数据方法
	 * 
	 * @return Map
	 */
	@ResponseBody
	@RequestMapping("/editCmDoubleBlack.do")
	public Map<String, String> editCmDoubleBlack(HttpServletRequest request, HttpServletResponse response) {
		Map<String, String> resultMap = new HashMap<String, String>();
		try {
			// 设置查询参数
			Map<String, String> param = new ParamUtil(request).getParamMap();
			String id = param.get("id");
			String needDouble = param.get("needDouble");
			String needVisit = param.get("needVisit");

			// 获取登陆用户信息
			HttpSession session = request.getSession();
			String userId = (String) session.getAttribute("userId");

			// 组装记录所需数据
			CmDoubleBlack updateCmDoubleBlack = new CmDoubleBlack();
			updateCmDoubleBlack.setId(id);
			if (StringUtils.isNotBlank(needDouble)) {
				updateCmDoubleBlack.setNeedDouble(needDouble);
			}
			if (StringUtils.isNotBlank(needVisit)) {
				updateCmDoubleBlack.setNeedVisit(needVisit);
			}
			updateCmDoubleBlack.setModifier(userId);

			// 更新数据数据库中
			cmDoubleBlackService.updateCmDoubleBlack(updateCmDoubleBlack);
			resultMap.put("msg", "editSuccess");
		} catch (Exception e) {
			logger.error("editCmDoubleBlack异常",e);
			resultMap.put("msg", "hadError");
		}
		return resultMap;
	}

	/**
	 * 删除数据方法
	 * 
	 * @return Map
	 */
	@ResponseBody
	@RequestMapping("/delCmDoubleBlack.do")
	public Map<String, String> delCmDoubleBlack(HttpServletRequest request, HttpServletResponse response) {
		Map<String, String> resultMap = new HashMap<String, String>();
		try {
			// 设置查询参数
			Map<String, String> param = new ParamUtil(request).getParamMap();
			String id = param.get("id");
			String fundCode = param.get("fundCode");

			// 判断记录是否已生成双录单
			Map<String, String> tempParam = new HashMap<String, String>();
			tempParam.put("fundCode", fundCode);
			List<CmDoubleTrade> list = cmDoubleTradeCRMService.listCmDoubleTrade(tempParam);
			if (list != null && list.size() > 0) {
				resultMap.put("msg", "hadRecord");
				return resultMap;
			}

			// 对数据进行删除
			HttpSession session = request.getSession();
			String userId = (String) session.getAttribute("userId");

			// 组装记录所需数据
			CmDoubleBlack updateCmDoubleBlack = new CmDoubleBlack();
			updateCmDoubleBlack.setId(id);
			updateCmDoubleBlack.setModifier(userId);
			updateCmDoubleBlack.setRecState("1");

			// 更新数据数据库中
			cmDoubleBlackService.updateCmDoubleBlack(updateCmDoubleBlack);
			resultMap.put("msg", "delSuccess");
		} catch (Exception e) {
			logger.error("delCmDoubleBlack异常",e);
			resultMap.put("msg", "hadError");
		}
		return resultMap;
	}
	
	/**
	 * 批量修改双录回访数据方法
	 * @return Map
	 */
	@ResponseBody
	@RequestMapping("/updateDoubleTask.do")
	public Map<String, String> updateDoubleTask(HttpServletRequest request, HttpServletResponse response) {
		Map<String, String> resultMap = new HashMap<String, String>();
		try {
			// 设置查询参数
			Map<String, String> param = new ParamUtil(request).getParamMap();
			String fundJoin = param.get("fundJoin");
			String[] fundArray = fundJoin.split(",");

			// 存放需要处理的类型数据
			List<String> listNeedDouble = new ArrayList<String>();
			List<String> listNoNeedDouble = new ArrayList<String>();
			List<String> listNoNeedVisit = new ArrayList<String>();
			for (int i = 0; i < fundArray.length; i++) {
				String fundInfo = fundArray[i];
				String[] rowArray = fundInfo.split("_");
				String fundCode = rowArray[0];
				String needDouble = rowArray[1];
				String needVisit = rowArray[2];

				// 判断特殊产品设置属性
				if ("1".equals(needDouble)) {// 需双录
					listNeedDouble.add(fundCode);
				}

				if ("2".equals(needDouble)) {// 无需双录
					listNoNeedDouble.add(fundCode);
				}

				if ("2".equals(needVisit)) {// 无需回访
					listNoNeedVisit.add(fundCode);
				}

			}

			// 获取登陆用户信息
			HttpSession session = request.getSession();
			String userId = (String) session.getAttribute("userId");

			// 设置处理参数
			Map<String, String> exeParam = new HashMap<String, String>();
			exeParam.put("modifier", userId);

			// 修改产品任务为需双录
			if (listNeedDouble != null && listNeedDouble.size() > 0) {
				StringBuilder fundBuilder = new StringBuilder();
				fundBuilder.append("'").append(StringUtils.join(listNeedDouble, "','")).append("'");
				exeParam.put("fundCode", fundBuilder.toString());
				exeParam.put("taskType", "1");
				exeParam.put("needFlag", "1");
				exeParam.put("handleFlag", "1");
				cmDoubleTradeCRMService.updateDoubleTask(exeParam);
			}

			// 修改产品任务为无需双录
			if (listNoNeedDouble != null && listNoNeedDouble.size() > 0) {
				StringBuilder fundBuilder = new StringBuilder();
				fundBuilder.append("'").append(StringUtils.join(listNoNeedDouble, "','")).append("'");
				exeParam.put("fundCode", fundBuilder.toString());
				exeParam.put("taskType", "1");
				exeParam.put("needFlag", "0");
				exeParam.put("handleFlag", "0");
				cmDoubleTradeCRMService.updateDoubleTask(exeParam);
			}

			// 修改产品任务为无需回访
			if (listNoNeedVisit != null && listNoNeedVisit.size() > 0) {
				StringBuilder fundBuilder = new StringBuilder();
				fundBuilder.append("'").append(StringUtils.join(listNoNeedVisit, "','")).append("'");
				exeParam.put("fundCode", fundBuilder.toString());
				exeParam.put("taskType", "2");
				exeParam.put("needFlag", "0");
				exeParam.put("handleFlag", "0");
				cmDoubleTradeCRMService.updateDoubleTask(exeParam);
			}
			updateInitStatusToZtService.pushInitStatusToZt(listNeedDouble, listNoNeedDouble);
			resultMap.put("msg", "updateSuccess");
		} catch (Exception e) {
			logger.error("updateDoubleTask异常！",e);
			resultMap.put("msg", "hadError");
		}
		return resultMap;
	}

}