package com.howbuy.crm.hb.web.controller.custinfo;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.hb.constants.CommonConstant;
import com.howbuy.crm.hb.constants.HbConstant;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.custinfo.WebChatUserDto;
import com.howbuy.crm.hb.service.custinfo.CmConscustOtherService;
import com.howbuy.crm.hb.tools.ContextManager;
import com.howbuy.crm.hb.tools.excel.write.ExcelWriter;
import com.howbuy.crm.hb.web.util.ListPageUtil;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.util.MainLogUtils;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.HttpUtils;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description: 客户管理 Controller
 */
@Slf4j
@Controller
@RequestMapping(value = "/conscust")
public class CmConscustOtherController {

	@Autowired
	private CmConscustOtherService cmConscustOtherService;

	@Value("${GLOBAL_HB_WEBCHAT}")
	private String wewbchatUrl;

	/**
	 * 最近分配客户
	 *
	 * @return
	 */
	@RequestMapping("/showNewestAssignMore.do")
	public ModelAndView showNewestAssignMore(HttpServletRequest request) {
		Map<String, String> startAndEndWeekMap = DateTimeUtil.getWeekStartAndEnd();
		String beginDate = startAndEndWeekMap.get("beginDate");
		String endDate = startAndEndWeekMap.get("endDate");
		ModelAndView modelAndView = new ModelAndView();
		HttpSession session=request.getSession();
		String topcpdata = (String) session.getAttribute("topcpdata");
		modelAndView.addObject("topcpdata", topcpdata);
		modelAndView.addObject("beginDate", beginDate);
		modelAndView.addObject("endDate", endDate);
		modelAndView.setViewName("/custinfo/listNewestAssignMore");
		return modelAndView;
	}

	/**
	 * 最近分配客户列表
	 *
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/loadNewestAssignMoreJson.do")
	public Map<String, Object> loadNewestAssignMoreJson(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		// 获取登陆用户信息
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		param.put("consCode", userlogin.getUserId());

		// 获取页面查询参数
		String custname = request.getParameter("custname");
		String beginDate = request.getParameter("beginDate");
		String endDate = request.getParameter("endDate");
		String visitStatus = request.getParameter("visitStatus");

		// 如果查询条件（客户姓名）不为空，则增加客户姓名查询参数
		if (StringUtil.isNotNullStr(custname)) {
			param.put("custname", custname);
		} else {
			param.put("custname", null);
		}

		// 如果查询条件（起始日期）不为空，则增加起始日期查询参数
		if (StringUtil.isNotNullStr(beginDate)) {
			param.put("beginDate", beginDate);
		} else {
			param.put("beginDate", null);
		}

		// 如果查询条件（终止日期）不为空，则增加终止日期查询参数
		if (StringUtil.isNotNullStr(endDate)) {
			param.put("endDate", endDate);
		} else {
			param.put("endDate", null);
		}
		// 如果查询条件（拜访状态）不为空，则增加拜访状态查询参数
		if (StringUtil.isNotNullStr(visitStatus)) {
			param.put("visitStatus", visitStatus);
		} else {
			param.put("visitStatus", null);
		}

		Map<String, Object> resultMap = new HashMap<String, Object>();
		PageData<Conscust> custData = cmConscustOtherService.listNewestAssignCustByPage(param);
		// 对列表数据字段进行转义
		for (Conscust vconscust : custData.getListData()) {
			// 转义销售方向字段
			if (StringUtil.isNotNullStr(vconscust.getSaledirection())) {
				vconscust.setSaledirection(ContextManager.convertProductType(vconscust.getSaledirection()));
			}
			// 转义客户级别字段
			if (StringUtil.isNotNullStr(vconscust.getConscustlvl())) {
				vconscust.setConscustlvl(ConstantCache.getInstance().getVal("custlevel", vconscust.getConscustlvl()));
			}
		}

		resultMap.put("total", custData.getPageBean().getTotalNum());
		resultMap.put("rows", custData.getListData());
		return resultMap;
	}

	/**
	 * 最近来电客户
	 *
	 * @return
	 */
	@RequestMapping("/showNewestIncomingMore.do")
	public ModelAndView showNewestIncomingMore(HttpServletRequest request) {
		Map<String, String> startAndEndWeekMap = DateTimeUtil.getWeekStartAndEnd();
		String beginDate = startAndEndWeekMap.get("beginDate");
		String endDate = startAndEndWeekMap.get("endDate");
		ModelAndView modelAndView = new ModelAndView();
		HttpSession session=request.getSession();
		String topcpdata = (String) session.getAttribute("topcpdata");
		modelAndView.addObject("topcpdata", topcpdata);
		modelAndView.addObject("beginDate", beginDate);
		modelAndView.addObject("endDate", endDate);
		modelAndView.setViewName("/custinfo/listNewestIncomingMore");
		return modelAndView;
	}

	/**
	 * 最近来电客户列表
	 *
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/loadNewestIncomingJson.do")
	public Map<String, Object> loadNewestIncomingJson(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		// 获取页面查询参数
		String custname = request.getParameter("custname");
		String beginDate = request.getParameter("beginDate");
		String endDate = request.getParameter("endDate");
		// 获取登陆用户信息
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		param.put("consCode", userlogin.getUserId());
		if (StringUtil.isNotNullStr(custname)) {
			param.put("custname", custname);
		} else {
			param.put("custname", null);
		}
		// 如果查询条件（起始日期）不为空，则增加起始日期查询参数
		if (StringUtil.isNotNullStr(beginDate)) {
			param.put("beginDate", beginDate);
		} else {
			param.put("beginDate", null);
		}

		// 如果查询条件（终止日期）不为空，则增加终止日期查询参数
		if (StringUtil.isNotNullStr(endDate)) {
			param.put("endDate", endDate);
		} else {
			param.put("endDate", null);
		}
		Map<String, Object> resultMap = new HashMap<String, Object>();
		PageData<Conscust> loadVconscust = cmConscustOtherService.listNewestIncomingCallCustByPage(param);

		// 对列表数据字段进行转义
		for (Conscust vconscust : loadVconscust.getListData()) {
			// 转义销售方向字段
			if (StringUtil.isNotNullStr(vconscust.getSaledirection())) {
				vconscust.setSaledirection(ContextManager.convertProductType(vconscust.getSaledirection()));
			}
			// 转义客户级别字段
			if (StringUtil.isNotNullStr(vconscust.getConscustlvl())) {
				vconscust.setConscustlvl(ConstantCache.getInstance().getVal("custlevel", vconscust.getConscustlvl()));
			}
		}
		resultMap.put("total", loadVconscust.getPageBean().getTotalNum());
		resultMap.put("rows", loadVconscust.getListData());
		return resultMap;
	}

	/**
	 * 最近生日客户
	 *
	 * @return
	 */
	@RequestMapping("/showNearestBirthdayMore.do")
	public ModelAndView showNearestBirthdayMore() {
		SimpleDateFormat fmt = new SimpleDateFormat("yyyyMM");
		String curMonth = fmt.format(new Date());
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.addObject("curMonth", curMonth);
		modelAndView.setViewName("/custinfo/listNearestBirthdayMore");
		return modelAndView;
	}

	/**
	 * 最近生日客户列表
	 *
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/loadNearestBirthdayJson.do")
	public Map<String, Object> loadNearestBirthdayJson(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		// 获取页面查询参数
		String custname = request.getParameter("custname");
		String birthMonth = request.getParameter("birthMonth");

		// 获取登陆用户信息
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		param.put("consCode", userlogin.getUserId());
		if (StringUtil.isNotNullStr(custname)) {
			param.put("custname", custname);
		} else {
			param.put("custname", null);
		}
		// 如果查询条件（起始日期）不为空，则增加起始日期查询参数
		Map<String, String> dayMap = DateTimeUtil.getMonthFirstLastday(birthMonth);
		String beginDate = dayMap.get("beginDate");
		String endDate = dayMap.get("endDate");
		param.put("beginDate", beginDate.substring(4));
		param.put("endDate", endDate.substring(4));
		Map<String, Object> resultMap = new HashMap<String, Object>();
		PageData<Conscust> loadVconscust = cmConscustOtherService.listNewestBirthdayCustByPage(param);

		// 对列表数据字段进行转义
		for (Conscust vconscust : loadVconscust.getListData()) {
			// 转义销售方向字段
			if (StringUtil.isNotNullStr(vconscust.getSaledirection())) {
				vconscust.setSaledirection(ContextManager.convertProductType(vconscust.getSaledirection()));
			}
			// 转义客户级别字段
			if (StringUtil.isNotNullStr(vconscust.getConscustlvl())) {
				vconscust.setConscustlvl(ConstantCache.getInstance().getVal("custlevel", vconscust.getConscustlvl()));
			}
		}
		resultMap.put("total", loadVconscust.getPageBean().getTotalNum());
		resultMap.put("rows", loadVconscust.getListData());
		return resultMap;
	}

	/**
	 * 最近联系客户
	 *
	 * @return
	 */
	@RequestMapping("/showNewestContactMore.do")
	public ModelAndView showNewestContactMore(HttpServletRequest request) {
		Map<String, String> startAndEndWeekMap = DateTimeUtil.getWeekStartAndEnd();
		String beginDate = startAndEndWeekMap.get("beginDate");
		String endDate = startAndEndWeekMap.get("endDate");
		HttpSession session=request.getSession();
		String topcpdata = (String) session.getAttribute("topcpdata");


		ModelAndView modelAndView = new ModelAndView();
		modelAndView.addObject("topcpdata", topcpdata);
		modelAndView.addObject("beginDate", beginDate);
		modelAndView.addObject("endDate", endDate);
		modelAndView.setViewName("/custinfo/listNewestContactMore");
		return modelAndView;
	}

	/**
	 * 最近联系客户列表
	 *
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/loadNewestContactJson.do")
	public Map<String, Object> loadNewestContactJson(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		// 获取登陆用户信息
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		param.put("consCode", userlogin.getUserId());

		// 获取页面查询参数
		String custname = request.getParameter("custname");
		String beginDate = request.getParameter("beginDate");
		String endDate = request.getParameter("endDate");

		// 如果查询条件（客户姓名）不为空，则增加客户姓名查询参数
		if (StringUtil.isNotNullStr(custname)) {
			param.put("custname", custname);
		} else {
			param.put("custname", null);
		}

		// 如果查询条件（起始日期）不为空，则增加起始日期查询参数
		if (StringUtil.isNotNullStr(beginDate)) {
			param.put("beginDate", beginDate);
		} else {
			param.put("beginDate", null);
		}

		// 如果查询条件（终止日期）不为空，则增加终止日期查询参数
		if (StringUtil.isNotNullStr(endDate)) {
			param.put("endDate", endDate);
		} else {
			param.put("endDate", null);
		}

		Map<String, Object> resultMap = new HashMap<>();
		PageData<Conscust> loadVconscust = cmConscustOtherService.listNewestContactCustByPage(param);

		// 对列表数据字段进行转义
		for (Conscust vconscust : loadVconscust.getListData()) {
			// 转义销售方向字段
			if (StringUtil.isNotNullStr(vconscust.getSaledirection())) {
				vconscust.setSaledirection(ContextManager.convertProductType(vconscust.getSaledirection()));
			}
			// 转义客户级别字段
			if (StringUtil.isNotNullStr(vconscust.getConscustlvl())) {
				vconscust.setConscustlvl(ConstantCache.getInstance().getVal("custlevel", vconscust.getConscustlvl()));
			}
		}
		resultMap.put("total", loadVconscust.getPageBean().getTotalNum());
		resultMap.put("rows", loadVconscust.getListData());
		return resultMap;
	}

	/**
	 * 查询投顾 企业微信添加的客户微信信息
	 *
	 * @return
	 */
	@RequestMapping("/showWebChatInfo.do")
	public ModelAndView showWebChatInfo() {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/custinfo/listWebChatInfo");
		return modelAndView;
	}

	/**
	 * ajax请求  企业微信添加的客户微信信息
	 *
	 * @param conscustno
	 * @param hboneno
	 * @param conscode
	 * @param unionId
	 * @param page
	 * @param rows
	 * @return Map
	 */
	@ResponseBody
	@RequestMapping("/loadWebChatInfo.do")
	public Map loadWebChatInfo(String conscustno,
							   String hboneno,
							   String conscode,
							   String unionId,
							   int page, int rows) {
		List<WebChatUserDto> userList = Lists.newArrayList();
		Map returnMap = Maps.newHashMap();

		if (StringUtil.isEmpty(conscode)) {
			returnMap.put("total", userList.size());
			return returnMap;
		}
		//获取用户列表
		userList = getWebChatListByConsCode(conscode);
		//过滤查询条件
		for (Iterator<WebChatUserDto> iter = userList.iterator(); iter.hasNext(); ) {
			WebChatUserDto userDto = iter.next();
			//过滤逻辑;
			boolean filter = false;
			if (StringUtil.isNotNullStr(conscustno) && !conscustno.equals(userDto.getCustNo())) {
				filter = true;//标记为 过滤
			}
			if (StringUtil.isNotNullStr(hboneno) && !hboneno.equals(userDto.getHboneNo())) {
				filter = true;//标记为 过滤
			}
			if (StringUtil.isNotNullStr(unionId) && !unionId.equals(userDto.getUnionId())) {
				filter = true;//标记为 过滤
			}
			if (filter) {
				iter.remove();
			}
		}
		//内存分页
		ListPageUtil<WebChatUserDto> pageUtil = new ListPageUtil<>(userList, rows);

		returnMap.put("total", userList.size());
		returnMap.put("rows", pageUtil.getPagedList(page));
		return returnMap;
	}

	/**
	 * ajax请求  更新所有列表
	 *
	 * @return conscode
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/updateAllList.do")
	public String updateAllList() {
		String result = null;
		try {
			long startTime = System.currentTimeMillis();
			result = HttpUtils.get(wewbchatUrl + "/wechatuser/updateAllCustInfoList");
			long endTime = System.currentTimeMillis();
			MainLogUtils.httpCallOut(wewbchatUrl + "/wechatuser/updateAllCustInfoList", CommonConstant.HTTP_SUCCESS_CODE, endTime - startTime);
		} catch (Exception e) {
			log.error("http请求wechat更新所有列表,异常信息：{}", e);
		}
		if (StringUtil.isNotNullStr(result) && result.indexOf("success") != -1) {
			return "success";
		}
		return "";
	}

	/**
	 * 获取全量 外部客户列表
	 *
	 * @param conscode
	 * @return
	 */
	private List<WebChatUserDto> getWebChatListByConsCode(String conscode) {
		List<WebChatUserDto> userList = Lists.newArrayList();
		List<String> externalUserList = getExternalUserIdList(conscode);
		//根据 微信客户Id 获取客户详细信息
		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		externalUserList.forEach(externalUserId -> {
			WebChatUserDto userDto = getSingleWebChatCust(externalUserId);
			if (userDto != null) {
				//赋值 投顾信息
				String orgCode = consOrgCache.getCons2OutletMap().get(userDto.getConscode());
				userDto.setConsName(ConsOrgCache.getInstance().getAllUserMap().get(userDto.getConscode()));
				//部门
				userDto.setOrgname(consOrgCache.getAllOrgMap().get(orgCode));
				//区域
				String uporgcode = consOrgCache.getUpOrgMapCache().get(orgCode);
				if ("0".equals(uporgcode)) {
					userDto.setUporgname(userDto.getOrgname());
				} else {
					userDto.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
				}
				//中心
				String centerName = consOrgCache.getAllOrgMap().get(consOrgCache.getUpCenterMapCache().get(orgCode));
				userDto.setCenterName(centerName);
				//添加投顾 信息
				String addOrgCode = consOrgCache.getCons2OutletMap().get(conscode);
				//姓名
				userDto.setAddConsName(ConsOrgCache.getInstance().getAllUserMap().get(conscode));
				//部门
				userDto.setAddConsOrgName(consOrgCache.getAllOrgMap().get(addOrgCode));
				//区域
				String addConsUpOrgCode = consOrgCache.getUpOrgMapCache().get(addOrgCode);
				if ("0".equals(addConsUpOrgCode)) {
					userDto.setAddConsUpOrgName(userDto.getAddConsOrgName());
				} else {
					userDto.setAddConsUpOrgName(consOrgCache.getAllOrgMap().get(addConsUpOrgCode));
				}
				//中心
				String addCenterName = consOrgCache.getAllOrgMap().get(consOrgCache.getUpCenterMapCache().get(addOrgCode));
				userDto.setAddConsCenterName(addCenterName);
				userList.add(userDto);

			}
		});
		return userList;
	}

	/**
	 * 导出-微信联系人操作
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping("/exportWebChatList.do")
	public void exportWebChatList(HttpServletRequest request, HttpServletResponse response) {
		String conscode = request.getParameter("conscode");
		List<WebChatUserDto> exportList = getWebChatListByConsCode(conscode);

		List<Map> valueMapList = Lists.newArrayList();
		exportList.stream().forEach(dto -> valueMapList.add(new org.apache.commons.beanutils.BeanMap(dto)));

		try {
			// 清空输出流
			response.reset();
			String fileName = "客户微信查询_" + conscode + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".xls";
			// 设置文件格式和名字
			response.setContentType("multipart/form-data");
			response.setHeader("Content-Disposition",
					"attachment;fileName=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
			ServletOutputStream os = response.getOutputStream();

			String[] columnName = new String[]{"一账通号", "投顾客户号", "客户姓名", "所属投顾", "所属部门", "所属区域",
					"客户微信昵称", "企业微信投顾", "添加企业微信投顾数"};

			String[] beanProperty = new String[]{"hboneNo", "custNo", "custName", "conscode", "orgname", "uporgname",
					"webChatNickName", "consName", "followCount"};
			ExcelWriter.writeExcel(os, "客户微信列表", 0, valueMapList, columnName, beanProperty);
			os.close(); // 关闭流
		} catch (Exception e) {
			log.error("文件导出异常", e);
		}

	}

	/**
	 * 根据 投顾号 获取 微信客户列表
	 *
	 * @param conscode
	 * @return
	 */
	private List<String> getExternalUserIdList(String conscode) {
		//根据 投顾号 获取 微信客户列表
		Map<String, String> map = Maps.newHashMap();
		map.put("userId", conscode);
		String result = null;
		try {
			long startTime = System.currentTimeMillis();
			result = HttpUtils.post(wewbchatUrl + "/wechatuser/getexternaluseridlist", map);
			long endTime = System.currentTimeMillis();
			MainLogUtils.httpCallOut(wewbchatUrl + "/wechatuser/getexternaluseridlist", CommonConstant.HTTP_SUCCESS_CODE, endTime - startTime);
		} catch (Exception e) {
			log.error("根据投顾号：{} http请求wechat获取归属的外部客户列表,异常信息：{}", conscode, e);
		}
		if (StringUtil.isNotNullStr(result)) {
			return JSONArray.parseArray(result, String.class);
		}
		return Lists.newArrayList();
	}


	/**
	 * 外部客户详情
	 *
	 * @param externalUserId
	 */
	private WebChatUserDto getSingleWebChatCust(String externalUserId) {
		WebChatUserDto dto = null;
		//根据 投顾号 获取 微信客户列表
		Map<String, String> map = Maps.newHashMap();
		map.put("externalUserId", externalUserId);
		try {
			long startTime = System.currentTimeMillis();
			String result = HttpUtils.post(wewbchatUrl + "/wechatuser/getexternalusercrminfo", map);
			long endTime = System.currentTimeMillis();
			MainLogUtils.httpCallOut(wewbchatUrl + "/wechatuser/getexternalusercrminfo", CommonConstant.HTTP_SUCCESS_CODE, endTime - startTime);
			log.info("根据externalUserId：{} http请求wechat获取客户信息:{}", externalUserId, result);
			if (StringUtil.isNotNullStr(result)) {
				dto = JSONObject.parseObject(result, WebChatUserDto.class);
			}
		} catch (Exception e) {
			log.error("根据externalUserId：{} http请求wechat获取客户,异常信息：{}", externalUserId, e);
		}
		return dto;
	}

	/**
	 * 查询全部
	 */
	@ResponseBody
	@RequestMapping("/loadAllWeChatInfo.do")
	public Map<String, Object> loadAllWeChatInfo(HttpServletRequest request) throws Exception {
		Map<String, Object> param = new ParamUtil(request).getParamObjMap();
		String conscustno = request.getParameter("conscustno");
		String hboneno = request.getParameter("hboneno");
		String conscode = request.getParameter("conscode");
		String orgcode = request.getParameter("orgcode");
		String wechatrelstatus = request.getParameter("wechatrelstatus");
		String invstType = request.getParameter("invstType");
		String unionId = request.getParameter("unionId");
		String unionIdSource  = request.getParameter("unionIdSource");
		String hkCustNo = request.getParameter("hkCustNo");
		param.put("conscustno", StringUtil.isEmpty(conscustno) ? null : conscustno);
		param.put("hboneno", StringUtil.isEmpty(hboneno) ? null : hboneno);
		param.put("conscode", StringUtil.isEmpty(conscode) ? null : conscode);
		if (null != orgcode && orgcode.startsWith(StaticVar.STR_OTHER)) {
			orgcode = orgcode.replaceFirst("other", "");
		}
		if (null != orgcode && orgcode.equals(StaticVar.NO)) {
			orgcode = null;
		}
		param.put("orgcode", StringUtil.isEmpty(orgcode) ? null : orgcode);
		param.put("wechatrelstatus", StringUtil.isEmpty(wechatrelstatus) ? null : wechatrelstatus);
		param.put("invstType", StringUtil.isEmpty(invstType) ? null : invstType);
		param.put("unionId", StringUtil.isEmpty(unionId) ? null : unionId);
		param.put("unionIdSource", StringUtil.isEmpty(unionIdSource) ? null : unionIdSource);
		param.put("hkCustNo", StringUtil.isEmpty(hkCustNo) ? null : hkCustNo);
		Map<String, Object> resultMap = new HashMap<>(2);
		PageData<WebChatUserDto> pd = cmConscustOtherService.loadAllWeChatInfoByPage(param);
		List<WebChatUserDto> list = pd.getListData();

		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		Map<String, String> user2OutletMap = consOrgCache.getUser2OutletMap();
		Map<String, String> allUserMap = consOrgCache.getAllUserMap();
		Map<String, String> allOrgMap = consOrgCache.getAllOrgMap();
		Map<String, String> upOrgMapCache = consOrgCache.getUpOrgMapCache();
		Map<String, String> upCenterMapCache = consOrgCache.getUpCenterMapCache();
		Map<String, String> wechatrelstatusMap = ConstantCache.getInstance().getConstantKeyVal("wechatrelstatus");
		Map<String, String> wechataddwayMap = ConstantCache.getInstance().getConstantKeyVal("wechataddway");
		Map<String, String> invstTypesMap = ConstantCache.getInstance().getConstantKeyVal("InvstTypes");
		for (WebChatUserDto userDto : list) {
			userDto.setInvstType(null == userDto.getInvstType() ? "" : invstTypesMap.get(userDto.getInvstType()));
			userDto.setStatus(null == userDto.getStatus() ? "" : wechatrelstatusMap.get(userDto.getStatus()));
			userDto.setAddWay(null == userDto.getAddWay() ? "" : wechataddwayMap.get(userDto.getAddWay()));
			//赋值 投顾信息
			String orgCode = user2OutletMap.get(userDto.getConscode());
			userDto.setConsName(allUserMap.get(userDto.getConscode()));
			//部门
			userDto.setOrgname(allOrgMap.get(orgCode));
			//区域
			String uporgcode = upOrgMapCache.get(orgCode);
			if ("0".equals(uporgcode)) {
				userDto.setUporgname(userDto.getOrgname());
			} else {
				userDto.setUporgname(allOrgMap.get(uporgcode));
			}
			//中心
			String centerName = allOrgMap.get(upCenterMapCache.get(orgCode));
			userDto.setCenterName(centerName);
			//添加投顾 信息
			String addOrgCode = user2OutletMap.get(userDto.getAddConsCode());
			//姓名
			userDto.setAddConsName(allUserMap.get(userDto.getAddConsCode()));
			//部门
			userDto.setAddConsOrgName(allOrgMap.get(addOrgCode));
			//区域
			String addConsUpOrgCode = upOrgMapCache.get(addOrgCode);
			if ("0".equals(addConsUpOrgCode)) {
				userDto.setAddConsUpOrgName(userDto.getAddConsOrgName());
			} else {
				userDto.setAddConsUpOrgName(allOrgMap.get(addConsUpOrgCode));
			}
			//中心
			String addCenterName = allOrgMap.get(upCenterMapCache.get(addOrgCode));
			userDto.setAddConsCenterName(addCenterName);
		}
		// 中心过滤
		list = list.stream().filter(it -> HbConstant.CENTER_ORG_LIST.contains(it.getAddConsCenterName())).collect(Collectors.toList());
		pd.setListData(list);
		resultMap.put("total", pd.getPageBean().getTotalNum());
		resultMap.put("rows", pd.getListData());
		return resultMap;
	}

	/**
	 * 全部导出-微信联系人操作
	 *
	 * @param request
	 * @param response
	 */
	@RequestMapping("/exportAllWeChatInfo.do")
	public void exportAllWeChatInfo(HttpServletRequest request, HttpServletResponse response) {
		Map<String, String> param = Maps.newHashMap();
		String conscustno = request.getParameter("conscustno");
		String hboneno = request.getParameter("hboneno");
		String conscode = request.getParameter("conscode");
		String orgcode = request.getParameter("orgcode");
		String wechatrelstatus = request.getParameter("wechatrelstatus");
		String invstType = request.getParameter("invstType");
		String unionId = request.getParameter("unionId");
		String unionIdSource  = request.getParameter("unionIdSource");
		String hkCustNo = request.getParameter("hkCustNo");
		param.put("conscustno", StringUtil.isEmpty(conscustno) ? null : conscustno);
		param.put("hboneno", StringUtil.isEmpty(hboneno) ? null : hboneno);
		param.put("conscode", StringUtil.isEmpty(conscode) ? null : conscode);
		if (null != orgcode && orgcode.startsWith(StaticVar.STR_OTHER)) {
			orgcode = orgcode.replaceFirst("other", "");
		}
		if (null != orgcode && orgcode.equals(StaticVar.NO)) {
			orgcode = null;
		}
		param.put("orgcode", StringUtil.isEmpty(orgcode) ? null : orgcode);
		param.put("wechatrelstatus", StringUtil.isEmpty(wechatrelstatus) ? null : wechatrelstatus);
		param.put("invstType", StringUtil.isEmpty(invstType) ? null : invstType);
		param.put("unionId", StringUtil.isEmpty(unionId) ? null : unionId);
		param.put("unionIdSource", StringUtil.isEmpty(unionIdSource) ? null : unionIdSource);
		param.put("hkCustNo", StringUtil.isEmpty(hkCustNo) ? null : hkCustNo);
		List<WebChatUserDto> exportList = cmConscustOtherService.loadAllWeChatInfoList(param);
		exportList.stream().forEach(it -> {
			if (StringUtils.isNotBlank(it.getGnHboneNo()) && StringUtils.isNotBlank(it.getHkHboneNo())) {
				it.setUnionIdSource("GN、GJ");
			}
			if (StringUtils.isNotBlank(it.getGnHboneNo()) && StringUtils.isBlank(it.getHkHboneNo())) {
				it.setUnionIdSource("GN");
			}
			if (StringUtils.isBlank(it.getGnHboneNo()) && StringUtils.isNotBlank(it.getHkHboneNo())) {
				it.setUnionIdSource("GJ");
			}
		});
		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		Map<String, String> user2OutletMap = consOrgCache.getUser2OutletMap();
		Map<String, String> allUserMap = consOrgCache.getAllUserMap();
		Map<String, String> allOrgMap = consOrgCache.getAllOrgMap();
		Map<String, String> upOrgMapCache = consOrgCache.getUpOrgMapCache();
		Map<String, String> upCenterMapCache = consOrgCache.getUpCenterMapCache();
		Map<String, String> wechatrelstatusMap = ConstantCache.getInstance().getConstantKeyVal("wechatrelstatus");
		Map<String, String> wechataddwayMap = ConstantCache.getInstance().getConstantKeyVal("wechataddway");
		Map<String, String> invstTypesMap = ConstantCache.getInstance().getConstantKeyVal("InvstTypes");
		for (WebChatUserDto userDto : exportList) {
			userDto.setInvstType(null == userDto.getInvstType() ? "" : invstTypesMap.get(userDto.getInvstType()));
			userDto.setStatus(null == userDto.getStatus() ? "" : wechatrelstatusMap.get(userDto.getStatus()));
			userDto.setAddWay(null == userDto.getAddWay() ? "" : wechataddwayMap.get(userDto.getAddWay()));
			//赋值 投顾信息
			String orgCode = user2OutletMap.get(userDto.getConscode());
			userDto.setConsName(allUserMap.get(userDto.getConscode()));
			//部门
			userDto.setOrgname(allOrgMap.get(orgCode));
			//区域
			String uporgcode = upOrgMapCache.get(orgCode);
			if ("0".equals(uporgcode)) {
				userDto.setUporgname(userDto.getOrgname());
			} else {
				userDto.setUporgname(allOrgMap.get(uporgcode));
			}
			//中心
			String centerName = allOrgMap.get(upCenterMapCache.get(orgCode));
			userDto.setCenterName(centerName);
			//添加投顾 信息
			String addOrgCode = user2OutletMap.get(userDto.getAddConsCode());
			//姓名
			userDto.setAddConsName(allUserMap.get(userDto.getAddConsCode()));
			//部门
			userDto.setAddConsOrgName(allOrgMap.get(addOrgCode));
			//区域
			String addConsUpOrgCode = upOrgMapCache.get(addOrgCode);
			if ("0".equals(addConsUpOrgCode)) {
				userDto.setAddConsUpOrgName(userDto.getAddConsOrgName());
			} else {
				userDto.setAddConsUpOrgName(allOrgMap.get(addConsUpOrgCode));
			}
			//中心
			String addCenterName = allOrgMap.get(upCenterMapCache.get(addOrgCode));
			userDto.setAddConsCenterName(addCenterName);
		}

		try {
			// 清空输出流
			response.reset();
			String fileName = "客户微信查询查询并导出全部_" + conscode + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".xls";
			// 设置文件格式和名字
			response.setContentType("multipart/form-data");
			response.setHeader("Content-Disposition",
					"attachment;fileName=" + new String(fileName.getBytes("gb2312"), "ISO8859-1"));
			ServletOutputStream os = response.getOutputStream();

			String[] columnName = new String[]{"一账通号", "投顾客户号", "香港客户号", "客户姓名", "客户状态", "客户类型", "所属投顾", "所属中心",
					"所属区域", "所属部门", "客户微信昵称", "添加状态", "unionId", "unionId来源", "企业微信投顾", "企微投顾所属中心",
					"企微投顾所属区域", "企微投顾所属部门", "添加渠道", "添加企业微信投顾数", "添加时间", "流失时间"};

			String[] beanProperty = new String[]{"hboneNo", "custNo", "hkCustNo", "custName", "custLabelStat", "invstType", "consName", "centerName",
					"uporgname", "orgname", "webChatNickName", "status", "unionId", "unionIdSource", "addConsName", "addConsCenterName",
					"addConsUpOrgName", "addConsOrgName", "addWay", "followCount", "addTime", "delTime"};
			ExcelWriter.writeExcel(os, "客户微信列表", 0, exportList, columnName, beanProperty);
			os.close(); // 关闭流
		} catch (Exception e) {
			log.error("文件导出异常", e);
		}

	}

	/**
	 * 查询全部企业微信添加该客户的投顾
	 */
	@RequestMapping("/followCountDetail.do")
	public String followCountDetail(HttpServletRequest request, Map<String, Object> map) {
		String externalUserId = request.getParameter("externalUserId");
		//用户名称、所属中心、所属区域、所属部门
		List<String> list = cmConscustOtherService.getAllFollowCons(externalUserId);
		ArrayList<WebChatUserDto> retList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(list)) {
			ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
			Map<String, String> user2OutletMap = consOrgCache.getUser2OutletMap();
			Map<String, String> allUserMap = consOrgCache.getAllUserMap();
			Map<String, String> allOrgMap = consOrgCache.getAllOrgMap();
			Map<String, String> upOrgMapCache = consOrgCache.getUpOrgMapCache();
			Map<String, String> upCenterMapCache = consOrgCache.getUpCenterMapCache();
			for (String addConsCode : list) {
				WebChatUserDto userDto = new WebChatUserDto();
				userDto.setAddConsCode(addConsCode);
				//添加投顾 信息
				String addOrgCode = user2OutletMap.get(userDto.getAddConsCode());
				//姓名
				userDto.setAddConsName(allUserMap.get(userDto.getAddConsCode()));
				//部门
				userDto.setAddConsOrgName(allOrgMap.get(addOrgCode));
				//区域
				String addConsUpOrgCode = upOrgMapCache.get(addOrgCode);
				if ("0".equals(addConsUpOrgCode)) {
					userDto.setAddConsUpOrgName(userDto.getAddConsOrgName());
				} else {
					userDto.setAddConsUpOrgName(allOrgMap.get(addConsUpOrgCode));
				}
				//中心
				String addCenterName = allOrgMap.get(upCenterMapCache.get(addOrgCode));
				userDto.setAddConsCenterName(addCenterName);
				retList.add(userDto);
			}
		}
		map.put("detailList", retList);
		return "custinfo/viewFollowCountDetail";
	}
	/**
	 * ajax请求  更新单条关系的状态
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/toSaveUpdateStatus.do")
	public String toSaveUpdateStatus(HttpServletRequest request) {
		String id = request.getParameter("id");
		String updateStatusCode = request.getParameter("updateStatusCode");
		if( (StringUtil.isNullStr(id)) || (StringUtil.isNullStr(updateStatusCode)) ){
			return "";
		}
		WebChatUserDto webChatUserDto = new WebChatUserDto();
		webChatUserDto.setId(id);
		webChatUserDto.setStatusCode(updateStatusCode);
		int count = cmConscustOtherService.saveUpdateStatus(webChatUserDto);
		if (count>0) {
			return "success";
		}
		return "";
	}
}
