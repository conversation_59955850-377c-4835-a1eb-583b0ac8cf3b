package com.howbuy.crm.hb.web.controller.insur;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.howbuy.crm.hb.domain.insur.CmBxChannel;
import com.howbuy.crm.hb.domain.insur.CmBxProduct;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.insur.CmBxChannelService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.db.PageData;

/**
 * @Description: Controller
 * @version 1.0
 */
@Slf4j
@Controller
@RequestMapping(value = "/cmBxChannel")
public class CmBxChannelController {
	@Autowired
	private CmBxChannelService cmBxChannelService;
	
	@Autowired
    private CommonService commonService;
	
	@RequestMapping("/listCmBxChannel.do")
	public ModelAndView listCmBxChannel(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();        
        modelAndView.setViewName("/insur/listCmBxChannel");
        return modelAndView;
	}
	
	/**
	 * 加载列表页面数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listCmBxChannelByPage.do")
	public Map<String, Object> listCmBxChannelByPage(HttpServletRequest request)	throws Exception {
		Map<String, String> param = new ParamUtil(request).getParamMap();
		param.put("isdel", "1");
		PageData<CmBxChannel> pageData = cmBxChannelService.listCmBxChannelByPage(param);

		// 返回查询结果
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmBxChannel> listdata = pageData.getListData();
		resultMap.put("rows", listdata);
		
		return resultMap;
	}
	
	
	
	/**
	 * 更新
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/updateCmBxChannel", method = RequestMethod.POST)
	@ResponseBody
    public Map<String, Object> updateCmBxChannel(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String id = request.getParameter("id");
		String channname = request.getParameter("channname");
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        User user = (User) request.getSession().getAttribute("loginUser");

        if (!StringUtils.isEmpty(id)) {
        	CmBxChannel cmBxChannel = new CmBxChannel();
        	cmBxChannel.setId(new BigDecimal(id));
        	cmBxChannel.setChannname(channname);
        	cmBxChannel.setModifier(user.getUserId());
        	cmBxChannel.setModifydt(new Date());
        	cmBxChannelService.updateCmBxChannel(cmBxChannel);
        }
        return resultMap;
    }
	
	
	/**
	 * 删除
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/delCmBxChannel", method = RequestMethod.POST)
	@ResponseBody
    public Map<String, Object> delCmBxChannel(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        User user = (User) request.getSession().getAttribute("loginUser");

        String id = request.getParameter("id");
        String channcode = request.getParameter("channcode");
        if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(channcode)) {
        	Map<String,String> param = new HashMap<String,String>();
        	param.put("channcode", channcode);
        	List<CmBxProduct>  listCmBxProduct = cmBxChannelService.listUsedCmBxProduct(param);
        	//校验产品信息是否已使用此数据:已经使用，不允许删除
        	if(CollectionUtils.isEmpty(listCmBxProduct)){
        		CmBxChannel cmBxChannel = new CmBxChannel();
            	cmBxChannel.setId(new BigDecimal(id));
            	cmBxChannel.setIsdel("0");//删除
            	cmBxChannel.setModifier(user.getUserId());
            	cmBxChannel.setModifydt(new Date());
            	cmBxChannelService.updateCmBxChannel(cmBxChannel);
        	}else{
        		resultMap.put("errorMsg", "数据已使用，不允许删除");
                resultMap.put("errorCode", "9999");
        	}
        }
        return resultMap;
    }
	
	
	/**
	 * 新增
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/insertCmBxChannel", method = RequestMethod.POST)
	@ResponseBody
    public  Map<String, Object> insertCmBxChannel(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
		String channname = request.getParameter("channname");
		String channcode = request.getParameter("channcode");
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        
        if(channcode != null && (!channcode.matches("^[0-9a-zA-Z]{1,15}$"))){
        	resultMap.put("errorMsg", "合作渠道代码必须为数字或字母！");
            resultMap.put("errorCode", "9999");
            return resultMap;
		}
        User user = (User) request.getSession().getAttribute("loginUser");
        Map<String,String> param = new HashMap<String,String> ();
        param.put("channcode", channcode);
        param.put("isdel", "1");
        CmBxChannel channel = cmBxChannelService.getCmBxChannel(param);
        if(channel != null){
        	resultMap.put("errorMsg", "当前合作渠道代码已存在，请重新输入");
        	resultMap.put("errorCode", "9999");
        	return resultMap;
        }

        CmBxChannel cmBxChannel = new CmBxChannel();
        cmBxChannel.setId(new BigDecimal(commonService.getSeqValue("SEQ_INSUR_ID")));
        cmBxChannel.setChannname(channname);
        cmBxChannel.setChanncode(channcode);
        cmBxChannel.setCreator(user.getUserId());
        cmBxChannel.setModifydt(new Date());
        cmBxChannel.setIsdel("1");
    	cmBxChannelService.insertCmBxChannel(cmBxChannel);
      
        return resultMap;
    }
	
	
	/**
	 * 展示修改页面
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/viewCmBxChannel", method = RequestMethod.POST)
	@ResponseBody
    public  Map<String, Object> viewCmBxChannel(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("errorMsg", "操作成功");
		resultMap.put("errorCode", "0000");
		String id = request.getParameter("id");
		if(StringUtils.isNotBlank(id)){
			Map<String,String> param = new HashMap<String,String> ();
			param.put("id", id);
			CmBxChannel cmBxChannel = cmBxChannelService.getCmBxChannel(param);
			resultMap.put("domain", cmBxChannel);
		}else{
			resultMap.put("errorMsg", "操作失败：id不能为空");
			resultMap.put("errorCode", "9999");
		}
      
        return resultMap;
    }
	
}
