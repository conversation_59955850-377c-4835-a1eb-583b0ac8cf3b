package com.howbuy.crm.hb.web.controller.pushmsg;

import com.howbuy.crm.hb.domain.pushmsg.CmMsgBlack;
import com.howbuy.crm.hb.domain.pushmsg.CmMsgType;
import com.howbuy.crm.hb.service.pushmsg.CmMsgBlackService;
import com.howbuy.crm.hb.service.pushmsg.CmMsgTypeService;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @classname: CmMsgBlackController
 * @author: yu.zhang
 * @description: TODO
 * @creatdate: 2021-01-13 9:55
 * @since: JDK1.8
 */
@Slf4j
@Controller
@RequestMapping("/msgblack")
public class CmMsgBlackController {
	
	@Autowired
	private CmMsgBlackService cmMsgBlackService;

    @Autowired
    private CmMsgTypeService cmMsgTypeService;

    /**
     * msgBlackList
     * @param request
     * @return java.lang.String
     * @Author: yu.zhang on 2021/1/14 14:41
     */
	@RequestMapping("/msgBlackList")
    public String msgBlackList(HttpServletRequest request){

        // 设置查询参数
        List<CmMsgType> listbx = cmMsgTypeService.listMasterTypeAll();
        List<CmMsgType> msgtypelist = this.getCmMsgTypeList();
        for(CmMsgType msgtype:listbx){
            msgtypelist.add(msgtype);
        }

        List<CmMsgType> listsubbx = cmMsgTypeService.listAllSubType();

        List<CmMsgType> msgsubtypelist = this.getCmMsgTypeList();
        for(CmMsgType msgsubtype:listsubbx){
            msgsubtypelist.add(msgsubtype);
        }
        request.setAttribute("msgtypelist",msgtypelist);
        request.setAttribute("msgsubtypelist",msgsubtypelist);
        request.setAttribute("userId", request.getSession().getAttribute("userId"));
        return "pushmsg/msgBlackList";
    }

    /**
     * queryMsgBlackList
     * @param request
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @Author: yu.zhang on 2021/1/15 14:08
     */
    @ResponseBody
    @RequestMapping("/queryMsgBlackList")
    public Map<String, Object> queryMsgBlackList(HttpServletRequest request) throws Exception{
        // 设置查询参数
        Map<String, String> param;
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();

        PageData<CmMsgBlack> cmConferencePage = cmMsgBlackService.listCmMsgBlackByPage(param);
        Map<String, Object> resultMap = new HashMap<String, Object>(16);
        resultMap.put("total", cmConferencePage.getPageBean().getTotalNum());
        List<CmMsgBlack> listtemp = cmConferencePage.getListData();

        resultMap.put("rows", listtemp);
        return resultMap;
    }
    /**
     * getCmMsgTypeList
     * @param
     * @return java.util.List<com.howbuy.crm.hb.domain.pushmsg.CmMsgType>
     * @Author: yu.zhang on 2021/1/14 13:28
     */
    public List<CmMsgType> getCmMsgTypeList(){
        List<CmMsgType> newlistbx = new ArrayList<>();
        CmMsgType msgtype = new CmMsgType();
        msgtype.setId("");
        msgtype.setMsgTypeName("全部");
        newlistbx.add(msgtype);
        return newlistbx;
    }

    /**
     * autoSubMsgTypeLoader
     * @param request
     * @param response
     * @param model
     * @return java.util.List<com.howbuy.crm.hb.domain.pushmsg.CmMsgType>
     * @Author: yu.zhang on 2021/1/15 9:56
     */
    @ResponseBody
    @RequestMapping("/autoSubMsgTypeLoader")
    public List<CmMsgType> autoSubMsgTypeLoader(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
        // 设置查询参数
        String msgtype = request.getParameter("msgtype");

        List<CmMsgType> msgtypelist = this.getCmMsgTypeList();

        if(StringUtils.isNotBlank(msgtype)){
            List<CmMsgType> listbx = cmMsgTypeService.listAllSubTypeByMasterId(msgtype);
            for(CmMsgType submsgtype:listbx){
                msgtypelist.add(submsgtype);
            }
        }else{
            List<CmMsgType> listsubbx = cmMsgTypeService.listAllSubType();
            for(CmMsgType msgsubtype:listsubbx){
                msgtypelist.add(msgsubtype);
            }
        }

        return msgtypelist;
    }

    /**
     * 组织架构页面的左边树形菜单的生成方法
     * @throws Exception
     */
    @RequestMapping("/msgtype_tree")
    public void getMsgTypeTree(HttpServletRequest request, HttpServletResponse response) throws Exception{

        List<CmMsgType> msgtypelist = cmMsgTypeService.listMsgTypeTree();
        String zNodes="";
        if (msgtypelist != null && msgtypelist.size() > 0) {
            zNodes +="{id:'0',pId:'-1',name:'全部',orgtype:'0',type:'0',isParent:true,open:true,nocheck:true},";

            for (CmMsgType cmmsgtype: msgtypelist) {
                if(StringUtils.isBlank(cmmsgtype.getMasterId()))
                {
                    zNodes +="{id:'"+cmmsgtype.getId()+"',pId:'0',name:'"+cmmsgtype.getMsgTypeName()+"',isParent:true,open:false},";
                }else
                {
                    zNodes +="{id:'"+cmmsgtype.getId()+"',pId:'"+cmmsgtype.getMasterId()+"',name:'"+cmmsgtype.getMsgTypeName()+"',isParent:false,open:false},";
                }
            }
        }

        zNodes = zNodes.substring(0, zNodes.length()-1);

        String jsonZnodes="["+zNodes+"]";
        response.setContentType("text/plain; charset=UTF-8");
        response.getOutputStream().write(jsonZnodes.getBytes("UTF-8"));
    }

    /**
     * saveMsgBlack
     * @param request
     * @param response
     * @return java.lang.String
     * @Author: yu.zhang on 2021/1/15 9:55
     */
    @ResponseBody
    @RequestMapping("/saveMsgBlack")
    public Map<String,String> saveMsgBlack(HttpServletRequest request,
                              HttpServletResponse response) throws Exception {
        Map<String,String> resultMap = new HashMap<>(16);
        User user = (User)request.getSession().getAttribute("loginUser");

        String conscode = request.getParameter("conscode");
        String remark = request.getParameter("remark");
        String[] msgtypeid = request.getParameterValues("msgtypeid[]");
        List<String> list = Arrays.asList(msgtypeid);

        Map<String, Object> param = new HashMap<String, Object>(16);
        param.put("conscode", conscode.trim());
        param.put("list", list);
        List<CmMsgBlack> repeatmsgblacklist = cmMsgBlackService.listCmMsgBlackRepeat(param);

        if(repeatmsgblacklist != null && repeatmsgblacklist.size() > 0){
            if(repeatmsgblacklist.size() == list.size()){
                resultMap.put("result","allrepeat");
                resultMap.put("msg","添加失败，所选用户及消息类型已在黑名单，无需重复添加");
            }else{
                String msg = "部分添加成功，其中";
                List<String> newlist = new ArrayList<>();
                for(CmMsgBlack msgblack:repeatmsgblacklist){
                    if(list.contains(msgblack.getMsgTypeId())){
                        msg += msgblack.getMsgTypeName()+",";
                    }

                    newlist.add(msgblack.getMsgTypeId());
                }

                msg = msg.substring(0,msg.length()-1)+"已存在，无需重复添加";
                this.insertSelectiveList(user.getUserId(),conscode, Util.getExceptList(list,newlist),remark);
                resultMap.put("result","partrepeat");
                resultMap.put("msg",msg);
            }
        }else{
            this.insertSelectiveList(user.getUserId(),conscode,list,remark);
            resultMap.put("result","success");
            resultMap.put("msg","添加成功");
        }

        return resultMap;
    }


    /**
     * insertSelectiveList
     * @param userid
     * @param conscode
     * @param msgtypelist
     * @param remark
     * @return void
     * @Author: yu.zhang on 2021/1/15 18:04
     */
    public void insertSelectiveList(String userid,String conscode,List<String> msgtypelist,String remark){
        List<CmMsgBlack> msgblacklist = new ArrayList<>();
        for(String msgtypeid:msgtypelist){
            CmMsgBlack msgblack = new CmMsgBlack();
            msgblack.setMsgTypeId(msgtypeid);
            msgblack.setCreator(userid);
            msgblack.setUserCode(conscode);
            msgblack.setRemark(remark);
            msgblacklist.add(msgblack);
        }

        cmMsgBlackService.insertSelectiveList(msgblacklist);
    }

    /**
     * delMsgBlack
     * @param request
     * @return java.lang.String
     * @Author: yu.zhang on 2021/1/15 9:55
     */
    @ResponseBody
    @RequestMapping("/delMsgBlack")
    public String delMsgBlack(HttpServletRequest request){

        String id = request.getParameter("id");
        cmMsgBlackService.deleteByPrimaryKey(id);
        String result = "success";

        return result;
    }

    /**
     * batchDelMsgBlack
     * @param request
     * @return java.lang.String
     * @Author: yu.zhang on 2021/1/15 9:55
     */
    @ResponseBody
    @RequestMapping("/batchDelMsgBlack")
    public String batchDelMsgBlack(HttpServletRequest request){

        String[] msgblackid = request.getParameterValues("msgblackid[]");
        List<String> list = Arrays.asList(msgblackid);
        cmMsgBlackService.batchDeleteByPrimaryKey(list);
        String result = "success";

        return result;
    }

    /**
     * exportMsgBlack
     * @param request
     * @param params
     * @param response
     * @return void
     * @Author: yu.zhang on 2021/1/15 10:09
     */
    @RequestMapping("/exportMsgBlack")
    public void exportMsgBlack(HttpServletRequest request, @RequestParam Map<String, String> params,
                                 HttpServletResponse response) {
        Map<String, Object> resultMap = new HashMap<String, Object>(16);

        Map<String, String> param = new HashMap<String, String>(16);
        // 获取分页参数
        String conscode = request.getParameter("conscode");
        String consname = request.getParameter("consname");
        String msgtype = request.getParameter("msgtype");
        String submsgtype = request.getParameter("submsgtype");

        param.put("conscode", conscode.trim());
        param.put("consname", consname.trim());
        param.put("msgtype", msgtype.trim());
        param.put("submsgtype", submsgtype.trim());

        List<CmMsgBlack> msgblacklist = cmMsgBlackService.listCmMsgBlack(param);

        if(msgblacklist != null && msgblacklist.size() > 0){

            for(CmMsgBlack cmmsgblack:msgblacklist){
                cmmsgblack.setCreateTimeStr(DateTimeUtil.convertDateToString("yyyy-MM-dd HH:mm:ss", cmmsgblack.getCreateTime()));
            }
            resultMap.put("rows", msgblacklist);
        }else{
            log.error("查询对应黑名单信息失败！");
        }

        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("消息黑名单.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String[] columnName = {
                    "员工编号","员工姓名","消息主类","消息子类","添加人","添加时间","备注"
            };

            String[] beanProperty = {
                    "userCode","consName","mainTypeName","msgTypeName","creator","createTimeStr","remark"
            };
            ExcelWriter.writeExcel(os, "消息黑名单", 0, (List<?>)resultMap.get("rows"), columnName, beanProperty);
            os.close(); // 关闭流
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }

    }

    @RequestMapping("/listBlackMsgTypeId")
    @ResponseBody
    public Object listBlackMsgTypeId(String userId){
        return cmMsgBlackService.listBlackMsgTypeId(userId);
    }

    @RequestMapping("/saveBlackForMsg")
    @ResponseBody
    public Object saveBlackForMsg(HttpServletRequest request,
                                  HttpServletResponse response) throws Exception{
        //添加
        String[] msgtypeid = request.getParameterValues("msgtypeid[]");
        if(msgtypeid != null && msgtypeid.length > 0) {
            saveMsgBlack(request, response);
        }
        String userId = (String) request.getSession().getAttribute("userId");
        String[] delMsgTypeId = request.getParameterValues("delMsgTypeId[]");
        if(delMsgTypeId != null && delMsgTypeId.length > 0) {
            //删除
            cmMsgBlackService.deleteByMsgTypeAndUser(userId, Arrays.asList(delMsgTypeId));
        }
        return "success";
    }
}
