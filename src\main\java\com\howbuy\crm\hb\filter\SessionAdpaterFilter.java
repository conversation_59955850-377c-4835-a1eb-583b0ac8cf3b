package com.howbuy.crm.hb.filter;

import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.sso.client.domain.UserSessionObject;
import com.howbuy.crm.sso.client.http.tools.SSOClientContants;
import com.howbuy.crm.util.LoggerUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Map;

public class SessionAdpaterFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        LoggerUtils.setUUID();
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpSession session = request.getSession();
        if (session.getAttribute(SSOClientContants.AUTHSESSION) != null) {
            UserSessionObject userSessionObject = (UserSessionObject) session.getAttribute(SSOClientContants.AUTHSESSION);
            User loginUser = new User();
            loginUser.setUserId(userSessionObject.getUserId());
            loginUser.setUserName(userSessionObject.getUserName());
            loginUser.setOutletcode(userSessionObject.getOrgCode());
            session.setAttribute("loginUser", loginUser);
            session.setAttribute("userId", userSessionObject.getUserId());
            session.setAttribute("loginRoles", userSessionObject.getLoginRoles());
            session.setAttribute("maxRoleLevel", userSessionObject.getMaxRoleLevel());
            session.setAttribute("listRoleLevel", userSessionObject.getListRoleLevel());
            session.setAttribute("topsddata", userSessionObject.getTopsddata());
            session.setAttribute("topsddatas", userSessionObject.getTopsddatas());
            session.setAttribute("topgddata", userSessionObject.getTopgddata());
            session.setAttribute("topcpdata", userSessionObject.getTopcpdata());
            session.setAttribute("gdfxcplist", userSessionObject.getGdfxcpList());
            LoggerUtils.setCfgValue(LoggerUtils.CUST_NO, loginUser.getUserId());
            Map<String, Object> sessionMap = userSessionObject.getOtherMap();
            if (sessionMap.get("canseeallreport") != null) {
                session.setAttribute("canseeallreport", sessionMap.get("canseeallreport"));
            }
            if (sessionMap.get("token") != null) {
                session.setAttribute("token", sessionMap.get("token"));
            }
            if (sessionMap.get("maxMenuGd") != null) {
            	session.setAttribute("maxMenuGd", sessionMap.get("maxMenuGd"));
            }
            if (sessionMap.get("maxMenuSd") != null) {
            	session.setAttribute("maxMenuSd", sessionMap.get("maxMenuSd"));
            }
            if (sessionMap.get("maxMenuCp") != null) {
            	session.setAttribute("maxMenuCp", sessionMap.get("maxMenuCp"));
            }
        }
        chain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public void destroy() {

    }
}
