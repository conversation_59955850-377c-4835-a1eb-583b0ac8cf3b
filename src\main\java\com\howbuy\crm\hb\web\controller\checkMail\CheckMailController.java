package com.howbuy.crm.hb.web.controller.checkMail;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.crm.hb.domain.checkmail.CmCheckMailConMain;
import com.howbuy.crm.hb.domain.checkmail.CmCheckMailConSub;
import com.howbuy.crm.hb.domain.checkmail.FundHisDto;
import com.howbuy.crm.hb.enums.HmcpxEnum;
import com.howbuy.crm.hb.service.checkmail.CheckMailService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.web.dto.custinfo.CrmBalanceProdDto;
import com.howbuy.crm.hb.web.dto.custinfo.CrmHighBalancePageDto;
import com.howbuy.crm.hb.web.dto.custinfo.CrmSmgqProdDto;
import com.howbuy.crm.hb.web.dto.custinfo.CrmYgsmProdDto;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.simu.dto.base.product.SmjzDto;
import com.howbuy.simu.service.base.product.SmjzService;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfWriter;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;

import static com.howbuy.crm.hb.enums.HmcpxEnum.SMGQ;

/**
 * 对账单controller
 */
@Slf4j
@Controller
@RequestMapping("/checkMail")
public class CheckMailController {

    @Autowired
    private CheckMailService checkMailService;

    @Autowired
    private ConscustService conscustService;
    @Autowired
    private QueryAcctBalanceFacade queryAcctBalanceFacade;
    @Autowired
    private SmjzService smjzService;
    @Autowired
    private JjxxInfoService jjxxInfoService;

    private static final String PE0053="PE0053";
    @RequestMapping("/listCheckMail.do")
    public ModelAndView listAssociationExpMail() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/checkmail/listCheckMail");
        return modelAndView;
    }

    @RequestMapping("/listCheckMailByPage.do")
    @ResponseBody
    public Map<String, Object> listCheckMailByPage(HttpServletRequest request) throws Exception {
        Map<String, String> param = new ParamUtil(request).getParamMap();
        PageData<CmCheckMailConMain> pageData = checkMailService.listCheckMailByPage(param);
        // 返回查询结果
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        resultMap.put("rows", pageData.getListData());
        return resultMap;
    }
    @RequestMapping("/toCheckMailYGSM.do")
    public ModelAndView toCheckMailYGSM(HttpServletRequest request) throws Exception {
        Map<String, String> param = new ParamUtil(request).getParamMap();
        String id = param.get("id");
        Map map = dealSendCheckMail(id,"4",request);
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addAllObjects(map);
        modelAndView.setViewName("/checkmail/YGSMTemplate");
        return modelAndView;
    }
    @RequestMapping("/toCheckMailSMGQ.do")
    public ModelAndView toCheckMailSMGQ(HttpServletRequest request) throws Exception {
        Map<String, String> param = new ParamUtil(request).getParamMap();
        String id = param.get("id");
        Map map = dealSendCheckMail(id,"5",request);
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addAllObjects(map);
        modelAndView.setViewName("/checkmail/SMGQTemplate");
        return modelAndView;
    }

    /**
     * 页面传过来的图片  打水印  盖章  转pdf   下载
     * @param request
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/savePicToPdfWithMark")
    public void savePicToPdfWithMark(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        //String assectid = request.getParameter("assectid");
        //String serialId = request.getParameter("serialId");
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        // 获得文件：
        MultipartFile file = multipartRequest.getFile("file");
        String fileName = file.getOriginalFilename();
        log.info(fileName);
        if (!file.isEmpty()) {
            BufferedImage backImage = ImageIO.read(file.getInputStream());
            int width = backImage.getWidth();
            int height = backImage.getHeight();
            // 水印图象的路径 水印一般为gif或者png的，这样可设置透明度
            //水印
            URL resource = Thread.currentThread().getContextClassLoader().getResource("Mark/mark.png");
            //好买章
            URL howbuyofficeMark = Thread.currentThread().getContextClassLoader().getResource("Mark/howbuyofficeMark.png");
            //好臻章
            URL haozhenofficeMark = Thread.currentThread().getContextClassLoader().getResource("Mark/haozhenofficeMark.png");
            ImageIcon imgIcon = new ImageIcon(resource);
            ImageIcon howbuyofficeMarkimgIcon = new ImageIcon(howbuyofficeMark);
            ImageIcon haozhenofficeMarkimgIcon = new ImageIcon(haozhenofficeMark);
            // 得到Image对象。
            Image img = imgIcon.getImage();
            Image howbuyofficeMarkimg = howbuyofficeMarkimgIcon.getImage();
            Image haozhenofficeMarkimg = haozhenofficeMarkimgIcon.getImage();

            //生产画板
            Graphics2D g = backImage.createGraphics();
            // 设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION,RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            float alpha = 1.0f; // 透明度
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP,   alpha));
            //旋转前盖章   根据前台传过来的文件名判断盖哪个章
            if(fileName.indexOf("私募股权")>0){
                g.drawImage(haozhenofficeMarkimg, width -270, height -250,150,150, null);
            }else{
                g.drawImage(howbuyofficeMarkimg, width -270, height -250,150,150, null);
            }
            // 设置水印旋转
            g.rotate(Math.toRadians(-45),(double) width / 2, (double) height / 2);

            for(int i=0;i<7;i++){
                for(int j = 0;j<4;j++){
                    // 表示水印图片的位置
                    g.drawImage(img, (int) (500*j), (int) (390*i), null);
                }
            }
            //g.drawImage(img, 500, 400, null);
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
            g.dispose();
            //ImageIO.write(backImage, "PNG", new File("D:/"+ DateUtil.getDateYYYYMMDD() +"/withMark" + fileName));
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            ImageIO.write(backImage, "png", os);
            
            OutputStream fos = null;
            Document doc = null;
            com.itextpdf.text.Image image = null;
            String outPdfName = fileName.replace("png", "pdf");
            ByteArrayOutputStream pdfos = new ByteArrayOutputStream();
            try {
                // 输出流
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition", "attachment;fileName="+ new String(outPdfName.getBytes("gb2312"), "ISO8859-1"));
                fos = response.getOutputStream();
                //FileOutputStream fos = new FileOutputStream("D:/"+ DateUtil.getDateYYYYMMDD() +"/withMarkPdf" + fileName.replace("png","pdf"));
                // 创建文档   页面有可能过来长图片   所以不再强制使用A4尺寸
                com.itextpdf.text.Rectangle rec = new com.itextpdf.text.Rectangle(width, height);
                doc = new Document(rec, 0, 0, 0, 0);
                // 写入PDF文档
                PdfWriter.getInstance(doc, pdfos);
                doc.open();
                // 根据图片大小设置文档大小
                //doc.setPageSize(PageSize.A4);
                // 实例化图片
                image = com.itextpdf.text.Image.getInstance(os.toByteArray());
                /* float scaler;
                if((842>height)&&(842>width)) {
                    scaler= 100;
                    image.scalePercent(scaler);
                }else{
                    if(height < width){
                        scaler = (float)595f/width*100;
                        image.scalePercent(scaler);
                    }
                    else {
                        scaler = 842f/height*100;
                        image.scalePercent(scaler);
                    }
                }*/
                image.scalePercent(100f);
                doc.add(image);
                doc.close();
                pdfos.writeTo(fos);
                fos.flush();
            } catch (IOException e) {
                e.printStackTrace();
            } catch (DocumentException e) {
                e.printStackTrace();
            } finally {
            	if(doc != null){
            		doc.close();   
            	}
            	if(pdfos != null) {
            		pdfos.close();
            	}
                if(fos != null){
                	fos.close();
                }
            }
        }
    }

    //private String json  = "[{\"balanceCostCurrency\":90000.00,\"dailyAsset\":-360.00,\"dailyAssetCurrency\":-360.00,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"淡水泉新方程基金1期\",\"currentAsset\":99468.00,\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210716\",\"nav\":2.1052,\"accumIncome\":322482.00,\"accumRealizedIncome\":223014.00,\"navDt\":\"20210716\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"1\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":90000.00,\"balanceCost\":90000.00,\"currencyMarketValue\":189468.00,\"currency\":\"156\",\"accumIncomeRmb\":322482.00,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"subProductCode\":\"PA1503\",\"marketValue\":189468.00,\"balanceIncomeNew\":0.00,\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":223014.00,\"productCode\":\"P02697\",\"currentAssetCurrency\":99468.00},{\"balanceCostCurrency\":600000.00,\"dailyAsset\":-1920.00,\"dailyAssetCurrency\":-1920.00,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"淡水泉新方程基金1期\",\"currentAsset\":392040.00,\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210716\",\"nav\":1.6534,\"accumIncome\":392040.00,\"accumRealizedIncome\":0.00,\"navDt\":\"20210716\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"1\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":600000.00,\"balanceCost\":600000.00,\"currencyMarketValue\":992040.00,\"currency\":\"156\",\"accumIncomeRmb\":392040.00,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"subProductCode\":\"PA1706\",\"marketValue\":992040.00,\"balanceIncomeNew\":0.00,\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"P02697\",\"currentAssetCurrency\":392040.00},{\"balanceCostCurrency\":400000.00,\"dailyAsset\":-1200.00,\"dailyAssetCurrency\":-1200.00,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"淡水泉新方程基金1期\",\"currentAsset\":232720.00,\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210716\",\"nav\":1.5818,\"accumIncome\":232720.00,\"accumRealizedIncome\":0.00,\"navDt\":\"20210716\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"1\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":400000.00,\"balanceCost\":400000.00,\"currencyMarketValue\":632720.00,\"currency\":\"156\",\"accumIncomeRmb\":232720.00,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"subProductCode\":\"PA1805\",\"marketValue\":632720.00,\"balanceIncomeNew\":0.00,\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"P02697\",\"currentAssetCurrency\":232720.00},{\"balanceCostCurrency\":450000.00,\"dailyAsset\":-1215.00,\"dailyAssetCurrency\":-1215.00,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"淡水泉新方程基金1期\",\"currentAsset\":187335.00,\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210716\",\"nav\":1.4163,\"accumIncome\":187335.00,\"accumRealizedIncome\":0.00,\"navDt\":\"20210716\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"1\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":450000.00,\"balanceCost\":450000.00,\"currencyMarketValue\":637335.00,\"currency\":\"156\",\"accumIncomeRmb\":187335.00,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"subProductCode\":\"PA1912\",\"marketValue\":637335.00,\"balanceIncomeNew\":0.00,\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"P02697\",\"currentAssetCurrency\":187335.00},{\"balanceCostCurrency\":1400000.00,\"dailyAsset\":-3920.00,\"dailyAssetCurrency\":-3920.00,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"淡水泉新方程基金1期\",\"currentAsset\":621180.00,\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210716\",\"nav\":1.4437,\"accumIncome\":621180.00,\"accumRealizedIncome\":0.00,\"navDt\":\"20210716\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"1\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":1400000.00,\"balanceCost\":1400000.00,\"currencyMarketValue\":2021180.00,\"currency\":\"156\",\"accumIncomeRmb\":621180.00,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"subProductCode\":\"PA2001\",\"marketValue\":2021180.00,\"balanceIncomeNew\":0.00,\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"P02697\",\"currentAssetCurrency\":621180.00},{\"balanceCostCurrency\":1000020.00,\"currencyCashCollection\":772242.67,\"disCode\":\"HB000A001\",\"productSubType\":\"5\",\"balanceIncomeNewRmb\":0,\"productName\":\"新方程启辰新三板母基金3期\",\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0,\"cashCollection\":772242.67,\"productSaleType\":\"0\",\"currencyNetBuyAmount\":1000020.00,\"scaleType\":\"2\",\"netBuyAmount\":1000020.00,\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0,\"fundCXQXStr\":\"2+2+0年\",\"balanceVol\":1000020.00,\"balanceCost\":1000020.00,\"currencyMarketValue\":1000020.00,\"currency\":\"156\",\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":1000020.00,\"balanceIncomeNew\":0,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"productCode\":\"PE0050\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0},{\"balanceCostCurrency\":1000000.00,\"dailyAsset\":-53846.45,\"dailyAssetCurrency\":-53846.45,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"景林全球基金专享子基金V期\",\"currentAsset\":593162.48,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210701\",\"nav\":1.7486,\"accumIncome\":593162.48,\"accumRealizedIncome\":0.00,\"navDt\":\"20210701\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":911107.45,\"balanceCost\":1000000.00,\"currencyMarketValue\":1593162.48,\"currency\":\"156\",\"accumIncomeRmb\":593162.48,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":1593162.48,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"SCK182\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":593162.48},{\"balanceCostCurrency\":1000000.00,\"dailyAsset\":-48754.27,\"dailyAssetCurrency\":-48754.27,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"景林全球基金专享私募子基金HM2期\",\"currentAsset\":444357.59,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210701\",\"nav\":1.4783,\"accumIncome\":444357.59,\"accumRealizedIncome\":0.00,\"navDt\":\"20210701\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":977039.57,\"balanceCost\":1000000.00,\"currencyMarketValue\":1444357.59,\"currency\":\"156\",\"accumIncomeRmb\":444357.59,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":1444357.59,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"SJA222\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":444357.59},{\"balanceCostCurrency\":1000000.00,\"dailyAsset\":-14197.82,\"dailyAssetCurrency\":-14197.82,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"丹羿精选1号1期\",\"currentAsset\":24704.20,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210716\",\"nav\":1.0826,\"accumIncome\":24704.20,\"accumRealizedIncome\":0.00,\"navDt\":\"20210716\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":946521.53,\"balanceCost\":1000000.00,\"currencyMarketValue\":1024704.20,\"currency\":\"156\",\"accumIncomeRmb\":24704.20,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":1024704.20,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"SLX243\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":24704.20},{\"balanceCostCurrency\":1000000.00,\"dailyAsset\":576.97,\"dailyAssetCurrency\":576.97,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"启林中证500指数增强8号\",\"currentAsset\":160407.24,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210716\",\"nav\":1.0056,\"accumIncome\":160407.24,\"accumRealizedIncome\":0.00,\"navDt\":\"20210716\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":1153945.15,\"balanceCost\":1000000.00,\"currencyMarketValue\":1160407.24,\"currency\":\"156\",\"accumIncomeRmb\":160407.24,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":1160407.24,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"SNL641\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":160407.24}]";
//    private String json  = "[{\"balanceCostCurrency\":1000000.00,\"dueDate\":\"20220410\",\"dailyAsset\":0.00,\"dailyAssetCurrency\":0.00,\"investmentHorizon\":\"24个月\",\"productSubType\":\"5\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"源码人民币五期-淄博昭游\",\"currentAsset\":0.00,\"accumIncomeNewRmb\":0.00,\"cooperation\":\"光大信托\",\"nav\":1.0000,\"accumIncome\":0.00,\"accumRealizedIncome\":0.00,\"navDt\":\"20200313\",\"benchmark\":\"8.0%\",\"rePurchaseFlag\":\"0\",\"scaleType\":\"1\",\"benchmarkType\":\"0\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"fundCXQXStr\":\"24.00个月\",\"balanceVol\":1000000.00,\"balanceCost\":1000000.00,\"currencyMarketValue\":1000000.00,\"currency\":\"156\",\"hwSaleFlag\":\"1\",\"productType\":\"5\",\"standardFixedIncomeFlag\":\"1\",\"marketValue\":1000000.00,\"valueDate\":\"20200410\",\"balanceIncomeNew\":0.00,\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"PE0266\",\"currentAssetCurrency\":0.00},{\"balanceCostCurrency\":1000000.00,\"disCode\":\"HB000A001\",\"productSubType\":\"5\",\"balanceIncomeNewRmb\":0,\"productName\":\"好买高瓴科技产业基金-淄博昭涵(2000万）\",\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0,\"productSaleType\":\"0\",\"currencyNetBuyAmount\":1000000.00,\"scaleType\":\"2\",\"netBuyAmount\":1000000.00,\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"162\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0,\"fundCXQXStr\":\"3+4+2年\",\"balanceVol\":1000000.00,\"balanceCost\":1000000.00,\"currencyMarketValue\":1000000.00,\"currency\":\"156\",\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"5\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":1000000.00,\"balanceIncomeNew\":0,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"productCode\":\"PE0268\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"fractionateCallFlag\":\"0\"},{\"balanceCostCurrency\":2200000.00,\"dailyAsset\":-22718.64,\"dailyAssetCurrency\":-22718.64,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"景林精选FOF子基金HM3期\",\"currentAsset\":-41867.82,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210716\",\"nav\":1.5484,\"accumIncome\":-41867.82,\"accumRealizedIncome\":0.00,\"navDt\":\"20210716\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":1393782.08,\"balanceCost\":2200000.00,\"currencyMarketValue\":2158132.17,\"currency\":\"156\",\"accumIncomeRmb\":-41867.82,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"4\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":2158132.17,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"S29494\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":-41867.82},{\"balanceCostCurrency\":1500000.00,\"currencyCashCollection\":1618389.82,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":118389.82,\"productName\":\"新方程私享精选FOF3号基金\",\"oneStepType\":\"1\",\"accumIncomeNewRmb\":118389.82,\"cashCollection\":1618389.82,\"productSaleType\":\"0\",\"currencyNetBuyAmount\":1500000.00,\"scaleType\":\"2\",\"netBuyAmount\":1500000.00,\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"2\",\"secondStepType\":\"162\",\"navDivFlag\":\"0\",\"accumIncomeNew\":118389.82,\"fundCXQXStr\":\"7+0+0年\",\"balanceVol\":1500000.00,\"balanceCost\":1500000.00,\"currencyMarketValue\":1500000.00,\"currency\":\"156\",\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"4\",\"standardFixedIncomeFlag\":\"0\",\"twoStepType\":\"16\",\"marketValue\":1500000.00,\"balanceIncomeNew\":118389.82,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"productCode\":\"P00107\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"fractionateCallFlag\":\"0\"}]";
    //private String json  = "[{\"balanceCostCurrency\":1000000.00,\"dailyAsset\":-43236.81,\"dailyAssetCurrency\":-43236.81,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"新方程星动力S6\",\"currentAsset\":-19040.06,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210820\",\"nav\":2.4730,\"accumIncome\":-19040.06,\"accumRealizedIncome\":0.00,\"navDt\":\"20210820\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":396667.99,\"balanceCost\":1000000.00,\"currencyMarketValue\":980959.93,\"currency\":\"156\",\"accumIncomeRmb\":-19040.06,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":980959.93,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"P10181\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":-19040.06},{\"balanceCostCurrency\":15000000.00,\"disCode\":\"HB000A001\",\"productSubType\":\"5\",\"balanceIncomeNewRmb\":0,\"productName\":\"源码人民币五期-淄博昭游\",\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0,\"productSaleType\":\"0\",\"currencyNetBuyAmount\":15000000.00,\"scaleType\":\"2\",\"netBuyAmount\":15000000.00,\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"162\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0,\"fundCXQXStr\":\"4+4+2年\",\"balanceVol\":15004125.00,\"balanceCost\":15000000.00,\"currencyMarketValue\":15000000.00,\"currency\":\"156\",\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":15000000.00,\"balanceIncomeNew\":0,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"productCode\":\"PE0266\",\"unconfirmedVol\":0.00,\"paidInAmt\":30000000.00,\"unconfirmedAmt\":0,\"fractionateCallFlag\":\"1\"},{\"balanceCostCurrency\":20000000.00,\"disCode\":\"HB000A001\",\"productSubType\":\"5\",\"balanceIncomeNewRmb\":0,\"productName\":\"好买高瓴科技产业基金-淄博昭涵(2000万）\",\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0,\"productSaleType\":\"0\",\"currencyNetBuyAmount\":20000000.00,\"scaleType\":\"2\",\"netBuyAmount\":20000000.00,\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"162\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0,\"fundCXQXStr\":\"5+5+2年\",\"balanceVol\":20012833.33,\"balanceCost\":20000000.00,\"currencyMarketValue\":20000000.00,\"currency\":\"156\",\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":20000000.00,\"balanceIncomeNew\":0,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"productCode\":\"PE0268\",\"unconfirmedVol\":0.00,\"paidInAmt\":50000000.00,\"unconfirmedAmt\":0,\"fractionateCallFlag\":\"1\"},{\"balanceCostCurrency\":2000000.00,\"dailyAsset\":-139431.51,\"dailyAssetCurrency\":-139431.51,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"睿璞投资-睿泰-潜心1号\",\"currentAsset\":-219984.63,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210820\",\"nav\":0.9830,\"accumIncome\":-219984.63,\"accumRealizedIncome\":0.00,\"navDt\":\"20210820\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":1810798.95,\"balanceCost\":2000000.00,\"currencyMarketValue\":1780015.36,\"currency\":\"156\",\"accumIncomeRmb\":-219984.63,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":1780015.36,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"SEV810\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":-219984.63},{\"balanceCostCurrency\":2000000.00,\"dailyAsset\":-170487.10,\"dailyAssetCurrency\":-170487.10,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"同犇消费5号\",\"currentAsset\":-558739.25,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210820\",\"nav\":1.0060,\"accumIncome\":-558739.25,\"accumRealizedIncome\":0.00,\"navDt\":\"20210820\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":1432664.76,\"balanceCost\":2000000.00,\"currencyMarketValue\":1441260.74,\"currency\":\"156\",\"accumIncomeRmb\":-558739.25,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":1441260.74,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"SLA342\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":-558739.25},{\"balanceCostCurrency\":1000000.00,\"dailyAsset\":-56201.55,\"dailyAssetCurrency\":-56201.55,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"新方程石锋厚积3号\",\"currentAsset\":-34883.71,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210820\",\"nav\":0.9960,\"accumIncome\":-34883.71,\"accumRealizedIncome\":0.00,\"navDt\":\"20210820\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":968992.25,\"balanceCost\":1000000.00,\"currencyMarketValue\":965116.28,\"currency\":\"156\",\"accumIncomeRmb\":-34883.71,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":965116.28,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"SLE846\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":-34883.71},{\"balanceCostCurrency\":3000000.00,\"dailyAsset\":-136646.48,\"dailyAssetCurrency\":-136646.48,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"丹羿锐进2号\",\"currentAsset\":173544.86,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210820\",\"nav\":1.1380,\"accumIncome\":173544.86,\"accumRealizedIncome\":0.00,\"navDt\":\"20210820\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":2788703.75,\"balanceCost\":3000000.00,\"currencyMarketValue\":3173544.86,\"currency\":\"156\",\"accumIncomeRmb\":173544.86,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":3173544.86,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"SLH333\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":173544.86},{\"balanceCostCurrency\":3000000.00,\"dailyAsset\":-77701.69,\"dailyAssetCurrency\":-77701.69,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"高毅邻山1号远望46号\",\"currentAsset\":105983.92,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210820\",\"nav\":1.0433,\"accumIncome\":105983.92,\"accumRealizedIncome\":0.00,\"navDt\":\"20210820\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":2977076.51,\"balanceCost\":3000000.00,\"currencyMarketValue\":3105983.92,\"currency\":\"156\",\"accumIncomeRmb\":105983.92,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":3105983.92,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"SLH992\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":105983.92},{\"balanceCostCurrency\":1000000.00,\"dailyAsset\":-39397.74,\"dailyAssetCurrency\":-39397.74,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"汉和资本86期\",\"currentAsset\":-198577.99,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210820\",\"nav\":0.9581,\"accumIncome\":-198577.99,\"accumRealizedIncome\":0.00,\"navDt\":\"20210820\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":836470.10,\"balanceCost\":1000000.00,\"currencyMarketValue\":801422.00,\"currency\":\"156\",\"accumIncomeRmb\":-198577.99,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":801422.00,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"SLM137\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":-198577.99},{\"balanceCostCurrency\":3000000.00,\"dailyAsset\":-41216.87,\"dailyAssetCurrency\":-41216.87,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"仁桥金选泽源5期A号\",\"currentAsset\":368007.85,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210820\",\"nav\":1.1440,\"accumIncome\":368007.85,\"accumRealizedIncome\":0.00,\"navDt\":\"20210820\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":2944062.81,\"balanceCost\":3000000.00,\"currencyMarketValue\":3368007.85,\"currency\":\"156\",\"accumIncomeRmb\":368007.85,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":3368007.85,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"SNL399\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":368007.85},{\"balanceCostCurrency\":101000000.00,\"dailyAsset\":-1616000.00,\"dailyAssetCurrency\":-1616000.00,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"新方程私享精选FOF3号基金\",\"currentAsset\":-1313000.00,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210820\",\"nav\":0.9870,\"accumIncome\":-1313000.00,\"accumRealizedIncome\":0.00,\"navDt\":\"20210820\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":101000000.00,\"balanceCost\":101000000.00,\"currencyMarketValue\":99687000.00,\"currency\":\"156\",\"accumIncomeRmb\":-1313000.00,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":99687000.00,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"P00107\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":-1313000.00},{\"balanceCostCurrency\":50000000.00,\"dailyAsset\":35000.00,\"dailyAssetCurrency\":35000.00,\"disCode\":\"HB000A001\",\"productSubType\":\"4\",\"balanceIncomeNewRmb\":0.00,\"productName\":\"景林精选FOF子基金HM3期\",\"currentAsset\":35000.00,\"oneStepType\":\"1\",\"accumIncomeNewRmb\":0.00,\"productSaleType\":\"0\",\"incomeDt\":\"20210802\",\"nav\":1.0007,\"accumIncome\":35000.00,\"accumRealizedIncome\":0.00,\"navDt\":\"20210802\",\"scaleType\":\"2\",\"stageEstablishFlag\":\"0\",\"benchmarkType\":\"0\",\"secondStepType\":\"161\",\"navDivFlag\":\"0\",\"accumIncomeNew\":0.00,\"balanceVol\":50000000.00,\"balanceCost\":50000000.00,\"currencyMarketValue\":50035000.00,\"currency\":\"156\",\"accumIncomeRmb\":35000.00,\"hkSaleFlag\":\"0\",\"hwSaleFlag\":\"1\",\"productType\":\"11\",\"standardFixedIncomeFlag\":\"1\",\"twoStepType\":\"16\",\"marketValue\":50035000.00,\"balanceIncomeNew\":0.00,\"naProductFeeType\":\"0\",\"incomeCalStat\":\"1\",\"accumRealizedIncomeRmb\":0.00,\"productCode\":\"S29494\",\"unconfirmedVol\":0.00,\"unconfirmedAmt\":0,\"currentAssetCurrency\":35000.00}]";
    //private String json1 = "[{\"proCode\":\"P00107\",\"fundHisList\":[{\"jjjz\":0.987,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":0.987,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":0.987,\"jjdm\":\"P00107\",\"fqdwjz\":0.987,\"sfkf\":\"\",\"ljjz\":0.987,\"jzrq\":\"20210820\"},{\"jjjz\":1.003,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.003,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.003,\"jjdm\":\"P00107\",\"fqdwjz\":1.003,\"sfkf\":\"\",\"ljjz\":1.003,\"jzrq\":\"20210816\"},{\"jjjz\":1.003,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.003,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.003,\"jjdm\":\"P00107\",\"fqdwjz\":1.003,\"sfkf\":\"\",\"ljjz\":1.003,\"jzrq\":\"20210813\"},{\"jjjz\":0.994,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":0.994,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":0.994,\"jjdm\":\"P00107\",\"fqdwjz\":0.994,\"sfkf\":\"\",\"ljjz\":0.994,\"jzrq\":\"20210806\"},{\"jjjz\":0.983,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":0.983,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":0.983,\"jjdm\":\"P00107\",\"fqdwjz\":0.983,\"sfkf\":\"\",\"ljjz\":0.983,\"jzrq\":\"20210730\"},{\"jjjz\":1.007,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.007,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.007,\"jjdm\":\"P00107\",\"fqdwjz\":1.007,\"sfkf\":\"\",\"ljjz\":1.007,\"jzrq\":\"20210723\"},{\"jjjz\":1.011,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.011,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.011,\"jjdm\":\"P00107\",\"fqdwjz\":1.011,\"sfkf\":\"\",\"ljjz\":1.011,\"jzrq\":\"20210716\"},{\"jjjz\":1.012,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.012,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.012,\"jjdm\":\"P00107\",\"fqdwjz\":1.012,\"sfkf\":\"\",\"ljjz\":1.012,\"jzrq\":\"20210715\"},{\"jjjz\":1.005,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.005,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.005,\"jjdm\":\"P00107\",\"fqdwjz\":1.005,\"sfkf\":\"\",\"ljjz\":1.005,\"jzrq\":\"20210709\"},{\"jjjz\":1.006,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.006,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.006,\"jjdm\":\"P00107\",\"fqdwjz\":1.006,\"sfkf\":\"\",\"ljjz\":1.006,\"jzrq\":\"20210702\"},{\"jjjz\":1.014,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.014,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.014,\"jjdm\":\"P00107\",\"fqdwjz\":1.014,\"sfkf\":\"\",\"ljjz\":1.014,\"jzrq\":\"20210630\"},{\"jjjz\":1.013,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.013,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.013,\"jjdm\":\"P00107\",\"fqdwjz\":1.013,\"sfkf\":\"\",\"ljjz\":1.013,\"jzrq\":\"20210625\"},{\"jjjz\":1,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1,\"jjdm\":\"P00107\",\"fqdwjz\":1,\"sfkf\":\"\",\"ljjz\":1,\"jzrq\":\"20210618\"},{\"jjjz\":1.005,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.005,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.005,\"jjdm\":\"P00107\",\"fqdwjz\":1.005,\"sfkf\":\"\",\"ljjz\":1.005,\"jzrq\":\"20210615\"},{\"jjjz\":1.008,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.008,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.008,\"jjdm\":\"P00107\",\"fqdwjz\":1.008,\"sfkf\":\"\",\"ljjz\":1.008,\"jzrq\":\"20210611\"},{\"jjjz\":1.013,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.013,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.013,\"jjdm\":\"P00107\",\"fqdwjz\":1.013,\"sfkf\":\"\",\"ljjz\":1.013,\"jzrq\":\"20210604\"},{\"jjjz\":1.009,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.009,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.009,\"jjdm\":\"P00107\",\"fqdwjz\":1.009,\"sfkf\":\"\",\"ljjz\":1.009,\"jzrq\":\"20210531\"},{\"jjjz\":1,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1,\"jjdm\":\"P00107\",\"fqdwjz\":1,\"sfkf\":\"\",\"ljjz\":1,\"jzrq\":\"20210521\"},{\"jjjz\":0.991,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":0.991,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":0.991,\"jjdm\":\"P00107\",\"fqdwjz\":0.991,\"sfkf\":\"\",\"ljjz\":0.991,\"jzrq\":\"20210514\"},{\"jjjz\":1,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1,\"jjdm\":\"P00107\",\"fqdwjz\":1,\"sfkf\":\"\",\"ljjz\":1,\"jzrq\":\"20210406\"}],\"proName\":\"新方程私享精选FOF3号基金\"},{\"proCode\":\"S29494\",\"fundHisList\":[{\"jjjz\":1.0007,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1.0007,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1.0007,\"jjdm\":\"S29494\",\"fqdwjz\":1.0007,\"sfkf\":\"\",\"ljjz\":1.0007,\"jzrq\":\"20210802\"},{\"jjjz\":1,\"jqpy\":\"\",\"moddt\":null,\"netJjjz\":1,\"jjjc\":\"\",\"convert\":false,\"sffhjz\":\"\",\"jzdw\":1,\"hbcl\":0,\"jjfl\":\"yg\",\"netljjz\":1,\"jjdm\":\"S29494\",\"fqdwjz\":1,\"sfkf\":\"\",\"ljjz\":1,\"jzrq\":\"20210721\"}],\"proName\":\"景林精选FOF子基金HM3期\"}]";
    /**
     * 定制账单需求  发送邮件
     * 获取配置主表CM_CHECK_MAIL_CONMAIN需要发送的人
     * queryAcctBalanceFacade接口获取资产详情
     * 根据配置子表CM_CHECK_MAIL_CONSUB过滤结果 4阳光私募   5私募股权
     * SmjzService.getPFundHisNav(java.lang.String, java.lang.String, java.lang.String)startDate，endDate都传null
     * 获取产品净值折线图数据
     * 套模板HTML生成PDF
     * 水印
     * 盖章
     */
    public Map dealSendCheckMail(String id,String productType,HttpServletRequest request) {
        //配置主表获取所有需要处理的客户
        //List<CmCheckMailConMain> list = checkMailService.getAll();
        CmCheckMailConMain main  = checkMailService.getConMainById(id);
        //获取配置子表的所有对应类型产品  用于过滤
        List<CmCheckMailConSub> allSub = checkMailService.getAllSubByMidAndProType(main.getId(),productType);
        List<QueryAcctBalanceResponse.BalanceBean> listbean = getBalanceListbean(main.getConscustno(),main.getHboneno(),request);
        //List<QueryAcctBalanceResponse.BalanceBean> listbean = new ArrayList<>();
        /**********************************************************************************/
      /*  JSONArray jsonArray= JSONArray.fromObject(json);
        for (Object o :jsonArray) {
            JSONObject jsonObject2= JSONObject.fromObject(o);
            QueryAcctBalanceResponse.BalanceBean balanceBean=(QueryAcctBalanceResponse.BalanceBean)JSONObject.toBean(jsonObject2, QueryAcctBalanceResponse.BalanceBean.class);
            listbean.add(balanceBean);
        }*/
        /**********************************************************************************/
        CrmHighBalancePageDto pageDto=constructPageDto(main.getHboneno(),main.getConscustno(),listbean,allSub);

        log.info("SendCheckMailBuss.dealSendCheckMail.pageDto:"+ JSON.toJSON(pageDto));
        HashMap<String, Object> paramMap = new HashMap<>();
        ArrayList<Object> pFundHisNavList = new ArrayList<>();
        //根据子表   4阳光私募 类型  获取折线图
        for (CmCheckMailConSub a :allSub) {
            if("4".equals(a.getProducttype())){
                //起止时间传空  获取所有
                List<SmjzDto> pFundHisNav = smjzService.getPFundHisNav(a.getProductcode(), "", "");
                FundHisDto fundHisDto = new FundHisDto();
                fundHisDto.setProCode(a.getProductcode());
                fundHisDto.setProName(a.getProductname());
                fundHisDto.setFundHisList(pFundHisNav);
                pFundHisNavList.add(fundHisDto);
                log.info("SendCheckMailBuss.dealSendCheckMail.pFundHisNav:"+ JSON.toJSON(pFundHisNav));
            }
        }

        paramMap.put("custname", main.getCustname());
        paramMap.put("nowtime", DateTimeUtil.convertDateToString("yyyy年MM月dd日", new Date()));
        paramMap.put("listYgsm", pageDto.getListYgsm());
        paramMap.put("listSmgq", pageDto.getListGq());
        paramMap.put("pFundHisNavList", JSONArray.fromObject(pFundHisNavList).toString());
        return paramMap;
    }

    public List<QueryAcctBalanceResponse.BalanceBean> getBalanceListbean(String custno,String hboneno,HttpServletRequest request) {
        QueryAcctBalanceRequest req = new QueryAcctBalanceRequest();
        req.setOutletCode("A20150120");
        req.setOperIp(getIpAddr(request));
        req.setTxChannel("1");
        req.setDataTrack("crmsale");
        if (StringUtil.isNullStr(hboneno)) {
            return new ArrayList<QueryAcctBalanceResponse.BalanceBean>();
        }
        req.setHbOneNo(hboneno);
        req.setDisCodeList(jjxxInfoService.getHbFullDisCodeList());
        log.info("SendCheckMailBuss.getBalanceListbean.queryAcctBalanceFacade.execute入参"+ JSON.toJSON(req));
        List<QueryAcctBalanceResponse.BalanceBean> listbean = queryAcctBalanceFacade.execute(req).getBalanceList();
        log.info("SendCheckMailBuss.getBalanceListbean.queryAcctBalanceFacade.execute返回"+JSON.toJSON(listbean));
        return listbean;
    }
    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    public String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    /**
     * 根据外部[中台]
     * @param hboneNo 一账通账号
     * @param custNo 客户号
     * @param listbean 明细列表
     * @param allSub 配置子表
     * @return
     */
    private CrmHighBalancePageDto constructPageDto(String hboneNo, String custNo, List<QueryAcctBalanceResponse.BalanceBean> listbean, List<CmCheckMailConSub> allSub){
        CrmHighBalancePageDto pageBean=new CrmHighBalancePageDto(hboneNo,custNo);
        if(CollectionUtils.isEmpty(listbean)){
            return pageBean;
        }
        Map<String, List<QueryAcctBalanceResponse.BalanceBean>> beanMap = listbean.stream()
                //过滤产品
                .filter(bean-> ifInSub(allSub,bean.getProductCode()))
                .collect(Collectors.groupingBy(QueryAcctBalanceResponse.BalanceBean::getProductCode));

        //构建页面展示的每一行记录
        beanMap.forEach( (productCode,beanList)->{
                    //获取产品信息
                    JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(productCode, false);
                    if(jjxxInfo==null){
                        jjxxInfo=new JjxxInfo();
                        jjxxInfo.setJjdm(productCode);
                     }
                    //使用接口中的产品名称，不使用jjxx表中简称
                    jjxxInfo.setJjjc(beanList.get(0).getProductName());//使用接口中的产品名称，不使用jjxx表中简称

                    String hmcpx = beanList.get(0).getProductSubType();
                    HmcpxEnum hmcpxEnum= getHmcpxEnum(hmcpx);

                    //分类到不同的tab下
                    switch (hmcpxEnum) {
                        case SMGQ://私募股权!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
                            pageBean.getListGq().add(buildSmgqDto(jjxxInfo,beanList));
                            return;
                        case YGSM://阳光私募!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
                            pageBean.getListYgsm().add(buildYgsmDto(jjxxInfo,beanList));
                            return;
                        default:
                    }
                }
        );
        return  pageBean;

    }

    /**
     * @description: 判断产品是否在配置子表中
     * @param productSubType 子产品类型
     * @return com.howbuy.crm.hb.enums.HmcpxEnum
     * @author: hongdong.xie
     * @date: 2024/7/2 18:55
     * @since JDK 1.8
     */
    private HmcpxEnum getHmcpxEnum(String productSubType) {
        // 20240702，王斯文要求将对账单中的产品类型调整为：固定收益合并到阳关私募
        if ("5".equals(productSubType)) {
            return HmcpxEnum.SMGQ;
        } else {
            return HmcpxEnum.YGSM;
        }
    }



    //阳关私募 --hmcpx not in ['2','5']
    //listBean大于1条时，生成合并信息
    private CrmYgsmProdDto buildYgsmDto(JjxxInfo jjxxInfo, List<QueryAcctBalanceResponse.BalanceBean> listbean){
        CrmYgsmProdDto returnDto;
        List<CrmYgsmProdDto> list= Lists.newArrayList();
        String prodCode=jjxxInfo.getJjdm();
        listbean.forEach(bean->{
            CrmYgsmProdDto detailDto=new CrmYgsmProdDto();
            fillProdInfo(jjxxInfo,detailDto);
            fillCalculateInfo(bean,detailDto);
            //明细暂定使用subProdCode
            detailDto.setProductCode(bean.getSubProductCode());

            detailDto.incrAccumIncome(bean.getAccumIncome());
            detailDto.incrAccumRealizedIncome(bean.getAccumRealizedIncome());
            detailDto.incrBalanceVol(bean.getBalanceVol());
            detailDto.incrCurrencyMarketValue(bean.getCurrencyMarketValue());
            detailDto.incrCurrencyMarketValueExFee(bean.getCurrencyMarketValueExFee());
            detailDto.incrCurrentAssetCurrency(bean.getCurrentAssetCurrency());
            detailDto.incrReceivManageFee(bean.getReceivManageFee());
            detailDto.incrReceivPreformFee(bean.getReceivPreformFee());
            //持仓收益率=[持仓收益/持仓成本]*100    当前币种
            if(bean.getBalanceCostCurrency()!=null && bean.getCurrentAssetCurrency()!=null
                    && bean.getBalanceCostCurrency().compareTo(BigDecimal.ZERO)>0){
                detailDto.setCurrentYields(bean.getCurrentAssetCurrency().divide(bean.getBalanceCostCurrency(),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
            }

            list.add(detailDto);
        });
        /* 当为分期成立产品[StageEstablishFlag=1-是] 或者 有N条持仓明细记录 . 一定合并展示 */
        boolean stageFlag=listbean.stream().filter(bean -> "1".equals(bean.getStageEstablishFlag())).count()>0; //包含[StageEstablishFlag=1]为 true
        if(stageFlag || list.size()>1){
            // 合并展示字段：  “持仓份额”、“参考市值”、“持仓收益”、“累计收益”、“已实现收益”
            returnDto=new CrmYgsmProdDto();
            returnDto.getSubList().addAll(list);
            //group取值： 产品信息
            fillProdInfo(jjxxInfo,returnDto);
            //汇总使用prodCode
            returnDto.setProductCode(prodCode);
            //group取值：get[0]
            fillCalculateInfo(listbean.get(0),returnDto);
            //汇总属性 balance
            incrYgsmBalanceValue(returnDto,list);

            //汇总是否 计算完成
            returnDto.setCalculateFinish(returnDto.getSubList().stream().filter(d-> !d.isCalculateFinish()).count()==0);

        }else{
            returnDto=list.get(0);
            returnDto.setProductCode(prodCode);
        }
        //汇总取值
        return returnDto;

    }

    //私募股权 --hmcpx=5
    private CrmSmgqProdDto buildSmgqDto(JjxxInfo jjxxInfo, List<QueryAcctBalanceResponse.BalanceBean> listbean){
        List<CrmSmgqProdDto> list= Lists.newArrayList();
        CrmSmgqProdDto returnDto;
        listbean.forEach(bean->{
            CrmSmgqProdDto detailDto=new CrmSmgqProdDto();
            fillProdInfo(jjxxInfo,detailDto);
            fillCalculateInfo(bean,detailDto);
            detailDto.setProductCode(bean.getSubProductCode());

            //持仓份额
            detailDto.incrBalanceVol(bean.getBalanceVol());
            //当前投资成本 -现有持仓份额部分对应购入本金金额-  净购买金额(投资成本)(当前币种)
            detailDto.incrCurrencyNetBuyAmount(bean.getCurrencyNetBuyAmount());
            //已发生回款  项目回款金额总和  私募股权回款(当前币种)
            detailDto.incrCurrencyCashCollection(bean.getCurrencyCashCollection());
            //认缴金额
            detailDto.incrPaidInAmt(bean.getPaidInAmt());
            //投资期限
            detailDto.setInvestmentHorizon(bean.getInvestmentHorizon());

            if(PE0053.equals(bean.getProductCode())){// 私募股权-代码等于PE0053时
                //持仓收益 -持仓接口中的持仓收益
                detailDto.incrBalanceIncomeNew(bean.getCurrentAssetCurrency());
                //累计收益 -持仓接口中的累计收益
                detailDto.incrAccumIncomeNew(bean.getAccumIncome());
            }else{ // 私募股权-代码不等于PE0053时，取接口中 累计收益（股权新算法）人民币，
                //持仓收益 -持仓接口中的持仓收益（股权新算法）原币
                detailDto.incrBalanceIncomeNew(bean.getBalanceIncomeNew());
                //累计收益 -取持仓接口中的累计收益（股权新算法）原币
                detailDto.incrAccumIncomeNew(bean.getAccumIncomeNew());
            }
            //回款进度 =已回款/投资成本
            detailDto.setBackYields(getBackYields(bean.getCurrencyCashCollection(),bean.getCurrencyNetBuyAmount()));
            list.add(detailDto);
        });

        if(list.size()==1){
            returnDto=  list.get(0);
            returnDto.setProductCode(jjxxInfo.getJjdm());
        }else{ //合计行
            returnDto=new CrmSmgqProdDto();
            returnDto.getSubList().addAll(list);
            //group取值： 产品信息
            fillProdInfo(jjxxInfo,returnDto);
            returnDto.setProductCode(jjxxInfo.getJjdm());
            //group取值：get[0]
            fillCalculateInfo(listbean.get(0),returnDto);
            returnDto.setInvestmentHorizon(listbean.get(0).getInvestmentHorizon());
//            1、合计行中的“认缴金额”，直接展示为空白，无需展示“--”；
            returnDto.setPaidInAmt(null);
//            2、合计行中的持仓收益和累计收益，直接合计明细值，不需要“计算中”的逻辑判断；
            //固定： 计算完成
            returnDto.setCalculateFinish(true);
            //汇总属性 balance
            incrSmgqBalanceValue(returnDto,returnDto.getSubList());
            //回款进度 =已回款/投资成本
            returnDto.setBackYields(getBackYields(returnDto.getCurrencyCashCollection(),returnDto.getCurrencyNetBuyAmount()));
        }
        return returnDto;
    }
    /**
     * 根据持仓数据，填充 净值、产品持仓计算信息
     * @param bean
     * @param dto
     * @return
     */
    private <T extends CrmBalanceProdDto> void fillCalculateInfo(QueryAcctBalanceResponse.BalanceBean bean, T dto){
        dto.setCurrency(bean.getCurrency());
        dto.setCurrencyStr(ConstantCache.getInstance().getConstantKeyVal("currencys").get(bean.getCurrency()));
        dto.setIncomeDt(bean.getIncomeDt());
        dto.setNav(bean.getNav());
        dto.setNavDt(bean.getNavDt());
        dto.setProductType(getProductType(bean.getProductType()));
        dto.setCalculateFinish(isCalculateFinishByStat(bean.getIncomeCalStat())); //1-计算完成 true
    }

    /**
     * 根据产品代码，填充基金相关信息
     * @param jjxxInfo
     * @param dto
     * @return
     */
    private <T extends CrmBalanceProdDto> void fillProdInfo(JjxxInfo jjxxInfo, T dto){
        if (jjxxInfo != null) {
            //转义scaleType
          /*  if (StringUtil.isNotNullStr(dto.getScaleType()) ) { //bean
                dto.setScaleType(getSaleTypeBySfmsjg(jjxxInfo.getSfmsjg()));
            }*/
            dto.setCompany(jjxxInfo.getGljc());
            dto.setHmcpx(jjxxInfo.getHmcpx());
            dto.setDuedt(jjxxInfo.getDuedt());
            dto.setProductName(jjxxInfo.getJjjc());
            //dto.setScaleType(getSaleTypeBySfmsjg(jjxxInfo.getSfmsjg()));
        }
    }
    private String getProductType(String code){
        if ("7".equals(code)) {
            return "专户";
        } else if ("11".equals(code)) {
            return "私募";
        } else {
            return "其他";
        }
    }
    /**
     *  根据计算状态 0-计算中；1-计算完成 返回 是否计算完成
     *  true标识计算完成
     * @param incomeCalStat
     * @return
     */
    private boolean isCalculateFinishByStat(String incomeCalStat){
        return  "1".equals(incomeCalStat);
    }
    /**
     * 回款进度 =已回款/投资成本
     * @param currencyCashCollection 已回款
     * @param currencyNetBuyAmount  投资成本
     * @return
     */
    private BigDecimal getBackYields(BigDecimal currencyCashCollection, BigDecimal currencyNetBuyAmount){
//回款进度 =已回款/投资成本
        if(currencyCashCollection!=null && currencyNetBuyAmount!=null
                && currencyNetBuyAmount.compareTo(BigDecimal.ZERO)>0){
            return currencyCashCollection
                    .divide(currencyNetBuyAmount,4,BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }
        return null;
    }
    /**
     * 汇总方法。
     * @param resultDto
     * @param calcuDtoList
     */
    private void incrSmgqBalanceValue(CrmSmgqProdDto resultDto,List<CrmSmgqProdDto> calcuDtoList){
        calcuDtoList.forEach( cal ->{
            //包含明细  只统计明细列表。不包含明细，统计自身
            final List<CrmSmgqProdDto> usedList=CollectionUtils.isEmpty(cal.getSubList())?Lists.newArrayList(cal):cal.getSubList();
            usedList.forEach(sub->{
                resultDto.incrBalanceVol(cal.getBalanceVol());
                resultDto.incrAccumIncomeNew(cal.getAccumIncomeNew());
                resultDto.incrBalanceIncomeNew(cal.getBalanceIncomeNew());
                resultDto.incrCurrencyCashCollection(cal.getCurrencyCashCollection());
                resultDto.incrCurrencyNetBuyAmount(cal.getCurrencyNetBuyAmount());
                resultDto.incrPaidInAmt(cal.getPaidInAmt());
            });
        });
    }

    /**
     * 汇总方法。
     * 包含明细  只统计明细列表。不包含明细，统计自身
     * @param resultDto
     * @param calcuDtoList
     */
    private void incrYgsmBalanceValue(CrmYgsmProdDto resultDto,List<CrmYgsmProdDto> calcuDtoList){

//       合并展示字段：  “持仓份额”、“参考市值”、“持仓收益”、“累计收益”、“已实现收益”
        calcuDtoList.forEach( cal ->{
            //包含明细  只统计明细列表。不包含明细，统计自身
            final List<CrmYgsmProdDto> usedList=CollectionUtils.isEmpty(cal.getSubList())?Lists.newArrayList(cal):cal.getSubList();
            usedList.forEach(sub->{
                resultDto.incrBalanceVol(sub.getBalanceVol());//持仓份额
                resultDto.incrCurrencyMarketValue(sub.getCurrencyMarketValue());//参考市值
                resultDto.incrCurrentAssetCurrency(sub.getCurrentAssetCurrency());//持仓收益
                resultDto.incrAccumIncome(sub.getAccumIncome());//累计收益
                resultDto.incrAccumRealizedIncome(sub.getAccumRealizedIncome());//累计已实现收益
            });
        });
    }

    /**
     * 过滤产品是否在配置子表中
     * @param allSub
     * @param productCode
     * @return
     */
    private boolean ifInSub(List<CmCheckMailConSub> allSub, String productCode) {
        if(CollectionUtils.isNotEmpty(allSub)){
            ArrayList<String> strings = new ArrayList<>();
            for (CmCheckMailConSub a :allSub) {
                strings.add(a.getProductcode());
            }
            return strings.contains(productCode);
        }
        return  false;
    }
}
