package com.howbuy.crm.hb.web.controller.callout.system;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.report.SyncBpFundMan;
import com.howbuy.crm.hb.service.callout.VconscustService;
import com.howbuy.crm.page.framework.domain.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/sys")
public class AutoCompleteFundController {

    @Autowired
    private VconscustService vconscustService;

    /**
     * 自动补全来源备注信息方法
     */
    @ResponseBody
    @RequestMapping("/autoMgMSremarkCombobox")
    public Map<String, List<Conscust>> autoSremarkCombobox(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        String searchParam = request.getParameter("term");
        String showdeal = request.getParameter("showdeal");
        String gdcj = request.getParameter("gdcj");
        // String conscustno = request.getParameter("conscustno");
        searchParam = searchParam.trim();
        param.put("searchParam", searchParam);
        if(StringUtils.isNotBlank(searchParam)){
            param.put("searchParam2", DigestUtil.digest(searchParam));
        }

        /*User userlogin = (User)request.getSession().getAttribute("loginUser");
        param.put("conscode", userlogin.getUserId());*/
        //3.5.1只显示有过高净值交易的数据，兼容原先查询所有
        if(StringUtils.isNotBlank(showdeal)){
            param.put("showdeal", showdeal);
        }
        if(StringUtils.isNotBlank(gdcj)){
            param.put("gdcj", gdcj);
        }
        //3.5.4 新增需求
        /*if(StringUtils.isNotBlank(conscustno)){
            param.put("conscustno", conscustno);
            String conscodenew = vconscustService.getConscodeFromConscustno(conscustno);
            param.put("vconscode",conscodenew);
        }*/
        List<Conscust> list = vconscustService.listConsCustByConscode(param);

        Map<String,List<Conscust>> result = new HashMap<String,List<Conscust>>();
        result.put("result", list);
        return result;
    }
    /**
     * 自动补全BP基金公司方法
     */
    @ResponseBody
    @RequestMapping("/bpfundCompanyLoader.do")
    public Map<String, List<SyncBpFundMan>> bpfundCompanyLoader(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        String searchParam = request.getParameter("term");
        param.put("searchParam", searchParam.toLowerCase());
        List<SyncBpFundMan> listSyncBpFundMan = vconscustService.listSyncBpFundMan(param);
        Map<String,List<SyncBpFundMan>> result = new HashMap<String,List<SyncBpFundMan>>();
        result.put("result", listSyncBpFundMan);
        return result;
    }
}
