package com.howbuy.crm.hb.web.controller.custinfo;

import com.google.common.collect.Lists;
import com.howbuy.crm.account.client.enums.YesOrNoEnum;
import com.howbuy.crm.account.client.response.custinfo.AbnormalCustInfoVO;
import com.howbuy.crm.account.client.response.custinfo.AbnormalRelatedCustVO;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.custinfo.AbnormalRelatedCustDto;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse;
import crm.howbuy.base.enums.DisCodeEnum;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description:(一账通/香港异常客户 abstract Controller)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2024/1/18 21:03
 * @since JDK 1.8
 */
@Slf4j
public class AbstractAbnormalCustController extends BaseController {



	@Autowired
	private PrebookproductinfoService prebookproductinfoService;

	@Autowired
	private QueryDealOrderListFacade queryDealOrderListFacade;



	/**
	 * 明细 列表的转译。  追加  投顾展示信息、预约信息、交易信息
	 * @param custInfo
	 * @return
	 */
	public List<AbnormalRelatedCustDto> convertRelatedCustList(AbnormalCustInfoVO custInfo){
		List<AbnormalRelatedCustDto> returnList=Lists.newArrayList();
		List<AbnormalRelatedCustVO>  relatedList=custInfo.getRelatedList();
		if(CollectionUtils.isNotEmpty(relatedList)){
			//准备 cache  翻译信息map
			ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
			Map<String, String> allUserMap=consOrgCache.getAllUserMap();
			Map<String, String> allOrgMap=consOrgCache.getAllOrgMap();
			Map<String, String> cons2OutletMap=consOrgCache.getCons2OutletMap();

			relatedList.forEach(detail->{
				String consCode=detail.getConsCode();
				//转译  明细列表
				AbnormalRelatedCustDto  relatedCustDto=new AbnormalRelatedCustDto();
				BeanUtils.copyProperties(detail,relatedCustDto);

				//是否有预约
				boolean hasPreBook=hasPreBook(detail.getCustNo());
				relatedCustDto.setHasPreBook(hasPreBook);

				//是否有交易
				boolean hasTrade=hasTrade(detail.getHboneNo());
				relatedCustDto.setHasTrade(hasTrade);

				relatedCustDto.setHasTradeOrPre( (hasPreBook || hasTrade) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());

				//所属部门
				relatedCustDto.setOrgName(allOrgMap.get(cons2OutletMap.get(consCode)));
				//所属投顾
				relatedCustDto.setConsName(allUserMap.get(consCode));
				//明细列表 追加上 主表的处理状态。
				relatedCustDto.setDealStatus(custInfo.getDealStatus());
				// 主表 香港客户号
				relatedCustDto.setMainTableHkCustNo(custInfo.getHkTxAcctNo());
				// 主表 香港客户名称
				relatedCustDto.setMainTableHkCustName(custInfo.getCustName());

				returnList.add(relatedCustDto);
			} );
		}
		return returnList;
	}


	/**
	 * @description:(是否存在预约)
	 * @param custNo
	 * @return boolean
	 * @author: haoran.zhang
	 * @date: 2024/1/18 13:17
	 * @since JDK 1.8
	 */
	public boolean hasPreBook(String custNo){
		if(StringUtil.isNullStr(custNo)){
			return false;
		}
		int count= prebookproductinfoService.selectCountByCustNo(custNo);
		return count>0;
	}


	/**
	 * @description:(是否存在交易)
	 * @param hboneNo
	 * @return boolean
	 * @author: haoran.zhang
	 * @date: 2024/1/18 13:18
	 * @since JDK 1.8
	 */
	public boolean hasTrade(String hboneNo){
		if (StringUtil.isNullStr(hboneNo)) {
			return false;
		}
		//取高端交易中台的交易记录，若【订单状态=申请成功/部分确认/确认成功】的订单数量＞0，则为：有交易
		QueryDealOrderListRequest req = newTmsBaseRequest(QueryDealOrderListRequest.class);
		req.setPageSize(500);
		req.setHbOneNo(hboneNo);
		List<String> fullDisCodeList= Arrays.stream(DisCodeEnum.values()).map(DisCodeEnum::getCode).collect(Collectors.toList());
		req.setDisCodeList(fullDisCodeList);
		List<QueryDealOrderListResponse.DealOrderBean> dealOrderlist = queryDealOrderListFacade.execute(req).getDealOrderList();
		return CollectionUtils.isNotEmpty(dealOrderlist);
	}




}
