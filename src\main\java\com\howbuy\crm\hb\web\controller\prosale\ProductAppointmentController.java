/**
 * Project Name:3.8.8
 * File Name:ProductAppointmentController.java
 * Package Name:com.hb.crm.web.controller.system
 * Date:2018/1/16 13:26
 * Copyright (c) 2018, yi.zhang All Rights Reserved.
 */
package com.howbuy.crm.hb.web.controller.prosale;

import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.service.prosale.ProductAppointmentService;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.prosale.dto.BankInfo;
import com.howbuy.crm.prosale.dto.CmPreUsedCalendar;
import com.howbuy.crm.prosale.request.GetInfoByParamRequest;
import com.howbuy.crm.prosale.response.GetCustBankInfoResponse;
import com.howbuy.crm.prosale.service.PreCalendarService;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;

/**
 * Function: TODO ADD FUNCTION. <br/>
 * Reason:	 TODO ADD REASON. <br/>
 *
 * <AUTHOR>
 * @since JDK 1.7
 */

@Slf4j
@Controller
@RequestMapping("/productappointment")
public class ProductAppointmentController {
    @Autowired
    private QueryPreBookService queryPreBookService;
    @Autowired
    private PreCalendarService preCalendarService;

    @Autowired
    private ProductAppointmentService productAppointmentService;

    @Autowired
    private JjxxInfoService jjxxInfoService;



    @RequestMapping("/checkdirectSale")
    @ResponseBody
    @Deprecated
    public String checkdirectSale(HttpServletRequest request, HttpServletResponse response) {
        String flag = StaticVar.SFMSJG_ZX;
        //基金代码
        String productId = request.getParameter("productId");
        //客户号 zj、sh是使用
        String custno = request.getParameter("custno");

        boolean checkflag = false;
        checkflag = queryPreBookService.isDxflag(custno,productId);

        if(checkflag) {
            flag = StaticVar.SFMSJG_DX;
        }
        return flag;
    }
    
    @RequestMapping("/gethbzl")
    @ResponseBody
    @Deprecated
    public String gethbzl(HttpServletRequest request, HttpServletResponse response) {
        String flag = "";
        //基金代码
        String jjdm = request.getParameter("jjdm");
        JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(jjdm, false);
        if(jjxx1 != null){
        	String hbzl = jjxx1.getHbzl();
        	if(StringUtil.isNotNullStr(ConstantCache.getInstance().getConstantKeyVal("currencys").get(hbzl))){
        		flag=hbzl;
        	}
        }
        return flag;
    }

    @RequestMapping("/getBankInfoList")
    @ResponseBody
    public void getBankInfoList(HttpServletRequest request, HttpServletResponse response) {

        String custno = request.getParameter("custno");
        GetInfoByParamRequest bankreq = new GetInfoByParamRequest();
        bankreq.setConscustno(custno);
        //CROCODILE'S TODO :  海外产品卡信息取值逻辑不同
        GetCustBankInfoResponse bankresponse = queryPreBookService.getCustBankInfo(bankreq);
        String sb = "";

        if(BaseConstantEnum.SUCCESS.getCode().equals(bankresponse.getReturnCode())){
            List<BankInfo> banklist= bankresponse.getBankInfoList();
            if (banklist != null && banklist.size() > 0) {
                sb += "[{\"id\": \"\", \"text\": \"请选择\"},";
                for (BankInfo bankinfo : banklist) {
                    String text = bankinfo.getBankacct() + ' ' + bankinfo.getBankname();
                    sb += "{ \"id\": \"" + bankinfo.getBankacct() + "\", \"text\": \"" + text + "\", \"bankprov\": \"" + bankinfo.getBankprov() + "\", \"bankcity\": \"" + bankinfo.getBankcity() + "\", \"bankaddr\": \"" + bankinfo.getBankaddr() +"\", \"bankcode\": \"" + bankinfo.getBankcode() +"\", \"bankname\": \"" + bankinfo.getBankname() +"\" },";
                }
                sb = sb.substring(0, sb.lastIndexOf(","));
                sb += "]";
            }
        }
        response.setContentType("text/html; charset=utf-8");
        PrintWriter pw = null;
        try {
            pw = response.getWriter();
            pw.print(sb.toString());
            pw.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (pw != null){
                pw.close();
            }
        }

    }

    @RequestMapping("/getManyCallComboboxValue")
    @ResponseBody
    @Deprecated
    public List<Map<String, String>> getManyCallComboboxValue(HttpServletRequest request, HttpServletResponse response) {
        //基金代码
        return getZxList();
    }

    
    private List<Map<String, String>> getZxList(){
    	List<Map<String, String>> list = new ArrayList<Map<String, String>>();
    	Map<String, String> map = new HashMap<String, String>(2);
        map.put("value", StaticVar.PAPER_ORDER);
        map.put("text", "纸质成单");
        list.add(map);
        return list;
    }


    /**
     * 是否为代销产品   true-标识代销
     * @param prodCode
     * @return
     */
    private boolean isDxFlag(String prodCode){
        JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(prodCode, false);

        //判断是否代销或者直转代产品
        return  (jjxxInfo != null && (StaticVar.SFMSJG_DX.equals(jjxxInfo.getSfmsjg()) || StaticVar.SFMSJG_ZZD.equals(jjxxInfo.getSfmsjg())));
    }
	
	@ResponseBody
	@RequestMapping("/getPretrddt.do")
	public Map<String, Object> getPretrddt(HttpServletRequest request) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>(2);
		/*String parambuy = "buy";
        String paramappend = "append";*/
        String paramsell = "sell";
		String pcode = request.getParameter("pcode");
		String busiType = request.getParameter("busiType");
        JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(pcode, false);
        //判断是否代销或者直转代产品
        boolean dxOrZzd = (jjxxInfo != null && (StaticVar.SFMSJG_DX.equals(jjxxInfo.getSfmsjg()) || StaticVar.SFMSJG_ZZD.equals(jjxxInfo.getSfmsjg())));
        // 预约业务类型是否为“赎回”
        boolean isRedeem = paramsell.equals(busiType);
        // “预计交易日期”下拉框数据
        List<Map<String,String>> pretrddtList = productAppointmentService.getPretrddtList(pcode, isRedeem, dxOrZzd);

		map.put("pretrddtList", pretrddtList);
		map.put("isExistPretrddtList", CollectionUtils.isNotEmpty(pretrddtList));
        map.put("dxFlag", dxOrZzd);
        return map;
	}

    @ResponseBody
    @RequestMapping("/getFractionPretrddt.do")
    public Map<String, Object> getFractionPretrddt(HttpServletRequest request)  {
        Map<String,Object> map = new HashMap<String,Object>(2);

        String pcode = request.getParameter("pcode");
        JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(pcode, false);
        //判断是否代销或者直转代产品
        boolean dxOrZzd = (jjxxInfo != null && (StaticVar.SFMSJG_DX.equals(jjxxInfo.getSfmsjg()) || StaticVar.SFMSJG_ZZD.equals(jjxxInfo.getSfmsjg())));
        // “预计交易日期”下拉框数据
        List<Map<String,String>> pretrddtList = productAppointmentService.getPretrddtList(pcode, false, dxOrZzd);

        map.put("pretrddtList", pretrddtList);
        map.put("isExistPretrddtList", CollectionUtils.isNotEmpty(pretrddtList));
        map.put("dxFlag", dxOrZzd);
        return map;
    }


    @ResponseBody
    @RequestMapping("/validateLatestCalendar.do")
    public ReturnMessageDto<CmPreUsedCalendar> validateLatestCalendar(String prodCode,String  expectTradeDt)  {
        Assert.notNull(prodCode);
        Assert.notNull(expectTradeDt);

        if(isDxFlag(prodCode)){ //代销产品无此校验。默认返回ok
            return  ReturnMessageDto.ok();
        }

        //按预约起始日期升序排序;
        CmPreUsedCalendar calendar
                = preCalendarService.getAvailCalendarListForSaleControl(prodCode)
                .stream().min(Comparator.comparing(CmPreUsedCalendar::getExpectTradeStartStimestamp)).orElse(null);
        if(calendar==null){
            return  ReturnMessageDto.ok();
        }
        String minDt=DateUtil.getDateFormat(calendar.getExpectTradeStartStimestamp(),"yyyyMMdd");
        String maxDt=DateUtil.getDateFormat(calendar.getExpectTradeEndStimestamp(),"yyyyMMdd");
        if(expectTradeDt.compareTo(minDt)<0 || expectTradeDt.compareTo(maxDt)>0  ){
              return  ReturnMessageDto.fail(String.format("该产品最近一次可预约时间为%s - %s ，所选择预计交易日期不在该时间范围，请确认是否提交？",
                      DateUtil.getDateFormat(calendar.getExpectTradeStartStimestamp(),"yyyy-MM-dd HH:mm:ss"),
                      DateUtil.getDateFormat(calendar.getExpectTradeEndStimestamp(),"yyyy-MM-dd HH:mm:ss")
                      ));
        }
        return  ReturnMessageDto.ok();
    }


}
