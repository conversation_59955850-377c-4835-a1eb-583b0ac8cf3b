package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.base.PreOccupyTypeEnum;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.base.YesNoEnum;
import com.howbuy.crm.hb.domain.prosale.CmPreForMatchInfo;
import com.howbuy.crm.hb.domain.prosale.CmPreForMatchVo;
import com.howbuy.crm.hb.domain.prosale.CmPreMatchRelationInfo;
import com.howbuy.crm.hb.service.prosale.PreControlHbService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.prosale.CmCustTransferDetailExtend;
import com.howbuy.crm.hb.web.dto.prosale.CmPreMatchRelationPageDto;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.prosale.dto.CmCustTransferDetail;
import com.howbuy.crm.prosale.dto.CmPreSaleControl;
import com.howbuy.crm.prosale.dto.CmPreUsedCalendar;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import com.howbuy.crm.prosale.service.PreDepositMatchService;
import com.howbuy.crm.prosale.service.PreSaleControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 销控相关-Controller
 * <AUTHOR>
 * @version 1.0
 * @created 2021-11-1 15:15:16
 */
@Slf4j
@Controller
@RequestMapping(value = "/precontrol")
public class PreDepositMatchController extends BaseController {

    @Autowired
	private PreControlHbService preControlHbService;

    @Autowired
	public PreDepositMatchService preDepositMatchService;

	@Autowired
    private PreSaleControlService preSaleControlService;

	@Autowired
	private PrebookproductinfoService prebookproductinfoService;

	@Autowired
	private JjxxInfoService jjxxInfoService;

	/**
	 * op手动操作的占位方式
	 */
	private static final List<PreOccupyTypeEnum>  mannualOccupyList=Arrays.asList(PreOccupyTypeEnum.MANNUAL_RESERVE,PreOccupyTypeEnum.MARK_DEPOSIT,PreOccupyTypeEnum.SEND_QUOTA);


	/**
	 * 自划款与预约关联 页面入口
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listpredepositmatch")
	public ModelAndView listPreDepositMatch(HttpServletRequest request) {
		Map<String,Object> modelMap=new HashMap<>();
		modelMap.put("preId",request.getParameter("preId"));
		return new ModelAndView("/prosale/listpredepositmatch",modelMap);
	}


	/**
	 * 弹窗--自划款与预约关联 页面入口
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/poppredepositmatch")
	public ModelAndView popPreDepositMatch(HttpServletRequest request) {
		Map<String,Object> modelMap=new HashMap<>();
		modelMap.put("preId",request.getParameter("preId"));
		return new ModelAndView("/prosale/poppredepositmatch",modelMap);
	}

	/**
	 * 加载页面数据方法
	 * --自划款与预约关联
	 * @param matchVo
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listpredepositmatch_json.do")
	public Map<String, Object> listPreDepositMatchJson(CmPreForMatchVo matchVo){

		Map<String, Object> returnMap= Maps.newHashMap();

//		待关联预约单默认查询范围：预约已确认交易未确认且产品=代销且资金未匹配（预约对应的中台订单付款状态不等于已付款）；
		//TODO:移入到 td-server
		List<CmPreForMatchInfo>  preList=preControlHbService.selectPreListForMatch(matchVo);
		returnMap.put("preList",preList);

		List<CmCustTransferDetail> depositList= Lists.newArrayList();
		preList.forEach(preInfo-> {
			//基金类型
			JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(preInfo.getProdCode(), false);
			preInfo.setFundTypeStr(getFundTypeStr(jjxx1));
			depositList.addAll(preDepositMatchService.selectAvailDepositListForMatch(preInfo.getHboneNo(),preInfo.getCustName(),preInfo.getProdCode()));
		} );

		//去重
		Map<String,CmCustTransferDetail>  duplicateMap=Maps.newHashMap();
		depositList.stream().forEach(deposit->{
            if(!duplicateMap.containsKey(deposit.getDepositSid())){
				duplicateMap.put(deposit.getDepositSid(),deposit);
			}
		});
		//网银存入的：是否已关联
		List<String> depositIdList=Lists.newArrayList(duplicateMap.keySet());
		Map<String,Integer>  relationMap= preControlHbService.selectMatchRelationCount(depositIdList);

		List<CmCustTransferDetailExtend> finalDisplayList=Lists.newArrayList();
		duplicateMap.forEach((k,v)->{
			CmCustTransferDetailExtend extend=new CmCustTransferDetailExtend();
			BeanUtils.copyProperties(v,extend);
			if(!relationMap.containsKey(k) || relationMap.get(k)==null || relationMap.get(k)==0){
				extend.setMatched(false);
			}else{
				extend.setMatched(true);
			}
			finalDisplayList.add(extend);
		});

		returnMap.put("depositList",finalDisplayList);
		return returnMap;
	}

	/**
	 * 产品类型 页面翻译
	 * @param jjxx1
	 * @return
	 */
	private String getFundTypeStr(JjxxInfo jjxx1){
       if(jjxx1!=null){
              if("0".equals(jjxx1.getSfhwjj())){
              	return "海外";
			  }else if("1".equals(jjxx1.getSfhwjj())){
				return  ConstantCache.getInstance().getConstantKeyVal("jjxxhmcpx").get(jjxx1.getHmcpx());
			  }
	   }
		return null;
	}


	/**
	 * 手动关联- 预约单vs 网银自划款
	 * --自划款与预约关联
	 * @param preIds
	 * @param depositIds
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/manualMatch.do")
	public ReturnMessageDto<Integer> manualMatch(String preIds, String depositIds, HttpServletRequest request){
		List<String> depositIdList = Arrays.asList(depositIds.split(SEPARATOR_COMMA));

		List<BigDecimal> preIdList=Lists.newArrayList();
		Arrays.asList(preIds.split(SEPARATOR_COMMA)).forEach(str->preIdList.add(new BigDecimal(str)));

		//解除之前的匹配
		preIdList.forEach(preId->{
			CmPreSaleControl existControl=preSaleControlService.selectByPreId(preId);
			if(existControl!=null &&
					existControl.getOccupyType()!=null &&
					!existControl.getOccupyType().equals(PreOccupyTypeEnum.DEPOSIT.getCode())){
				preSaleControlService.removeOccupyType(preId,PreOccupyTypeEnum.getEnum(existControl.getOccupyType()),getLoginUserId(request));
			}
		});

		//自划款的匹配
        ReturnMessageDto<Integer> returnMessageDto=preDepositMatchService.executeMannualMatch(preIdList,depositIdList,getLoginUserId(request));
		return returnMessageDto;
	}


	/**
	 * 自划款与预约关联 关联关系列表
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listpredepositrelation")
	public ModelAndView listPreDepositRelation(HttpServletRequest request) {
		Map<String,Object> modelMap=new HashMap<>();
		modelMap.put("preId",request.getParameter("preId"));
		return new ModelAndView("/prosale/listpredepositrelation",modelMap);
	}

	/**
	 * 弹窗--自划款与预约关联 关联关系列表
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/poppredepositrelation")
	public ModelAndView popPreDepositRelation(HttpServletRequest request) {
		Map<String,Object> modelMap=new HashMap<>();
		modelMap.put("preId",request.getParameter("preId"));
		return new ModelAndView("/prosale/poppredepositrelation",modelMap);
	}


	/**
	 * 自划款与预约关联 关联关系列表
	 * @param vo
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listmatchrelationjson.do")
	public List<CmPreMatchRelationPageDto> listMatchRelationJson(CmPreForMatchVo vo){
		//页面展示对象
		List<CmPreMatchRelationInfo>  relationList=preControlHbService.selectMatchRelationList(vo);

		Map<BigDecimal,CmPreMatchRelationPageDto> pageMap=Maps.newHashMap();//key:预约Id .value :页面展示对象

		relationList.forEach(relation->{
			 BigDecimal preId=relation.getPreId();
			if(!pageMap.containsKey(preId)){
				CmPreMatchRelationPageDto pageDto=new CmPreMatchRelationPageDto();
				//预约信息
				BeanUtils.copyProperties(relation,pageDto);

				pageMap.put(relation.getPreId(),pageDto);

				pageDto.setThisBankAcct(relation.getThisBankAcct());//监管行卡信息
			}
			//存入信息
			CmPreMatchRelationPageDto.DepositSideInfo depositSideInfo=pageMap.get(preId).new DepositSideInfo();
			BeanUtils.copyProperties(relation,depositSideInfo);
			pageMap.get(preId).getDepositList().add(depositSideInfo);
		});

         return Lists.newArrayList(pageMap.values());
	}

	/**
	 * 弹窗-自划款与预约关联 关联关系列表
	 * 根据预约id查询 该预约关联的 网银存入列表
	 * @param preId
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/listmatchrelabypreidjson.do")
	public List<CmPreMatchRelationInfo> listMatchRelaByPreIdJson(String preId){
		if(StringUtil.isEmpty(preId)){
			return Lists.newArrayList();
		}
		CmPreForMatchVo vo=new CmPreForMatchVo();
		vo.setPreId(new BigDecimal(preId));
		return preControlHbService.selectMatchRelationList(vo);
	}


	/**
	 * 是否已资金匹配
	 * @param preId
	 * @return
	 */
	private boolean isFinMatched(String preId){
		CmPrebookproductinfo preInfo=prebookproductinfoService.selectPrebookproductinfoById(preId);
		return  (preInfo!=null &&  YesNoEnum.Y.getCode().equals(preInfo.getFinMatched()));
	}


	/**
	 * 根据preId 手动解除 该预约单的关联关系
	 * @param preId
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/removematchbypreid.do")
	public ReturnMessageDto<Integer> removeMatchByPreId(String preId,HttpServletRequest request){
		Assert.notNull(preId);

		if(isFinMatched(preId)){
			return ReturnMessageDto.fail("该预约已资金匹配，不可取消关联！");
		}

		try {
			int removeCount=preDepositMatchService.removeMatchByPreId(new BigDecimal(preId),true,getLoginUserId(request));
			return ReturnMessageDto.ok("",removeCount);
		}catch (Exception e){
			log.error("根据preId:{} 解除匹配异常：",preId,e);
			return ReturnMessageDto.fail("系统异常！");
		}
	}

	/**
	 * 解除单个预约Id的多条匹配关系-根据preId 和 depositIdList
	 * @param preId
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/removematchlistbypreid.do")
	public ReturnMessageDto<Integer> removeMatchListByPreId(String preId,String depositIds, HttpServletRequest request){
		Assert.notNull(preId);
		Assert.notNull(depositIds);
		List<String> depositIdList = Arrays.asList(depositIds.split(SEPARATOR_COMMA));

		if(isFinMatched(preId)){
			return ReturnMessageDto.fail("该预约已资金匹配，不可取消关联！");
		}
		try {
			int removeCount=preDepositMatchService.removeMatchListByPreId(new BigDecimal(preId),depositIdList,true,getLoginUserId(request));
			return ReturnMessageDto.ok("",removeCount);
		}catch (Exception e){
			log.error("根据preId:{} ,自划款存入Id:{} 解除匹配异常：",preId,depositIds,e);
			return ReturnMessageDto.fail("系统异常！");
		}
	}



	/**
	 * 根据preId 和 depositId手动解除 单条关联关系
	 * @param preId
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/removesinglematch.do")
	public ReturnMessageDto<Integer> removeSingleMatch(String preId,String depositId,HttpServletRequest request){
		Assert.notNull(preId);
		Assert.notNull(depositId);

		if(isFinMatched(preId)){
			return ReturnMessageDto.fail("该预约已资金匹配，不可取消关联！");
		}

		try {
			int removeCount=preDepositMatchService.
					removeSingleMatch(new BigDecimal(preId),depositId,true,getLoginUserId(request));
			return ReturnMessageDto.ok("",removeCount);
		}catch (Exception e){
			log.error("根据preId:{} 解除匹配异常：",preId,e);
			return ReturnMessageDto.fail("系统异常！");
		}
	}


	/**
	 * @param preId
	 * @return ModelAndView
	 */
	@RequestMapping("/getsalecontrol")
	@ResponseBody
	public CmPreSaleControl  getSaleControl(String preId) {
		Assert.notNull(preId);
		return preSaleControlService.selectByPreId(new BigDecimal(preId));
	}


	/**
	 * @param preIds
	 * @return ModelAndView
	 */
	@RequestMapping("/getsalecontrollist")
	@ResponseBody
	public List<CmPreSaleControl>  getSaleControlList(String preIds) {
		Assert.notNull(preIds);
		List<CmPreSaleControl> returnList=Lists.newArrayList();

		Arrays.asList(preIds.split(SEPARATOR_COMMA)).stream().forEach(preId->{
			CmPreSaleControl control=preSaleControlService.selectByPreId(new BigDecimal(preId));
			if(control!=null){
				returnList.add(control);
			}
		});
        return  returnList;
	}


    /**
     * 1-手工标记到账 2-人工预留 的 op操作入口
     * @param dynamicPreId
	 * @param dynamicOccupyType
	 * @param request
     * @return Map<String, Object>
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/markoccupytype.do")
    public ReturnMessageDto<Integer> markOccupyType(String dynamicPreId,String dynamicOccupyType,HttpServletRequest request){
        Assert.notNull(dynamicPreId);
		PreOccupyTypeEnum typeEnum=PreOccupyTypeEnum.getEnum(dynamicOccupyType);

		BigDecimal preId=new BigDecimal(dynamicPreId);
		CmPreSaleControl control=preSaleControlService.selectByPreId(preId);
		if(control==null){
			return ReturnMessageDto.fail(String.format("预约单Id:%s  未进入销控，不允许标记占位，！",preId));
		}
		PreOccupyTypeEnum hisEnum=PreOccupyTypeEnum.getEnum(control.getOccupyType());// 历史占位类型
		try {
			//case1 :之前未占位  hisEnum为空
			if(hisEnum==null){
				if(typeEnum==null){ //再次请求：修改为未占位
					return ReturnMessageDto.fail(String.format("预约单Id:%s  未标记占位！",preId));
				}else{
					return preSaleControlService.updateToOccupyType(new BigDecimal(dynamicPreId),typeEnum,getLoginUserId(request));
				}
			}

			//case1 :之前已占位   hisEnum不为空
			if(typeEnum!=null && typeEnum.equals(hisEnum)){ //与请求占位的类型一样
				return ReturnMessageDto.fail(String.format("预约单Id:%s  标记占位，当前占位类型已经为:%s ！",preId,typeEnum.getDescription()));
			}

			// 取消所有占位 --hisEnum是op可以解除的
			if(mannualOccupyList.contains(hisEnum)){
				preSaleControlService.removeOccupyType(new BigDecimal(dynamicPreId),hisEnum,getLoginUserId(request));
			}
			//TODO: hisEnum 是网银的

			if(typeEnum!=null){
				return preSaleControlService.updateToOccupyType(new BigDecimal(dynamicPreId),typeEnum,getLoginUserId(request));
			}
			return  ReturnMessageDto.ok();

		}catch (Exception e){
			log.error("根据preId:{} 额度占用标记异常：",dynamicPreId,e);
			return ReturnMessageDto.fail("系统异常！");
		}

    }

	/**
	 * 3-销控名额 打标 op操作入口
	 * @param preIds
	 * @param request
	 * @return ReturnMessageDto<Integer>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/marksendquotatype.do")
	public ReturnMessageDto<Integer> markSendQuotaType(String preIds,HttpServletRequest request){
		Assert.notNull(preIds);
		ReturnMessageDto<Integer> returnMessageDto=ReturnMessageDto.ok("",0);
		List<String> dList =  Arrays.asList(preIds.split(SEPARATOR_COMMA));
		List<String> errorList=Lists.newArrayList();
        int sucCount=0;
		for(String preId :dList){
			BigDecimal dynamicPreId=new BigDecimal(preId);
			try {
				CmPreSaleControl control=preSaleControlService.selectByPreId(dynamicPreId);
				if(control==null || PreOccupyTypeEnum.SEND_QUOTA.getCode().equals(control.getOccupyType())){
					//未进入销控 或者 已经是-发放额度
					continue;
				}
				// 取消之前所有占位
				if(mannualOccupyList.contains(PreOccupyTypeEnum.getEnum(control.getOccupyType()))){
					preSaleControlService.removeOccupyType(dynamicPreId,PreOccupyTypeEnum.getEnum(control.getOccupyType()),getLoginUserId(request));
				}

				ReturnMessageDto<Integer> resultDto=preSaleControlService.updateToOccupyType(dynamicPreId,PreOccupyTypeEnum.SEND_QUOTA,getLoginUserId(request));
				if(resultDto.isSuccess()){
					sucCount=sucCount+1;//累加成功条数
				}else{
					log.error("预约Id:{} 标记为：{}，错误信息：{}",preId,PreOccupyTypeEnum.SEND_QUOTA.getDescription(),resultDto.getReturnMsg());
					errorList.add(preId);
				}
			}catch (Exception e){
				errorList.add(preId);
				log.error("根据preId:{} 发送销控名额异常：",dynamicPreId,e);
			}
		}
		StringBuilder msg=new StringBuilder();
		msg.append("发送销控名额条数：").append(sucCount);
		if(!CollectionUtils.isEmpty(errorList)){
			msg.append("。其中异常发放名额：").append(JSONObject.toJSONString(errorList));
		}
		returnMessageDto.setReturnMsg(msg.toString());
		returnMessageDto.setReturnObject(sucCount);
        return returnMessageDto;
	}

	/**
	 * 3-销控名额 取消 op操作入口
	 * @param dynamicPreId
	 * @param request
	 * @return ReturnMessageDto<Integer>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/cancelsendquotatype.do")
	public ReturnMessageDto<Integer> cancelSendQuotatype(String dynamicPreId,HttpServletRequest request){
		Assert.notNull(dynamicPreId);
		try {
			ReturnMessageDto<Integer> returnMessageDto= preSaleControlService.removeOccupyType(new BigDecimal(dynamicPreId),PreOccupyTypeEnum.SEND_QUOTA,getLoginUserId(request));
			//尝试 做自动匹配
			preDepositMatchService.executeAutoMatchByPreId(new BigDecimal(dynamicPreId));
			return returnMessageDto;
		}catch (Exception e){
			log.error("根据preId:{} 取消销控名额异常：",dynamicPreId,e);
			return ReturnMessageDto.fail("系统异常！");
		}
	}


	/**
	 * 发放额度校验
	 * returnCode=ReturnMessageDto.SUCCESS_CODE 表明验证通过. msg为 前端提示待确认信息
	 * returnCode=ReturnMessageDto.FAIL_CODE 表明验证失败. msg为 失败信息
	 * @param preIds
	 * @return
	 */
    @RequestMapping("/validateSendQuota")
    @ResponseBody
    public ReturnMessageDto<JSONObject>  validateSendQuota(String preIds,String outletCodes) {
        Assert.notNull(preIds);
		List<String> preIdList = Arrays.asList(preIds.split(SEPARATOR_COMMA));
		List<String> outletCodeList = Arrays.asList(outletCodes.split(SEPARATOR_COMMA));
        return preControlHbService.validateSendQuota(preIdList, outletCodeList);
    }



	/**
	 * 发放额度-页面
	 * @param request
	 * @return
	 */
	@RequestMapping("/sendQuotaPushMsg")
	public ModelAndView sendQuotaPushMsg(String preIds,HttpServletRequest request){
		Map<String,Object> modelMap=new HashMap<>();
		modelMap.put("preIds",preIds);
		return  new ModelAndView("prosale/sendQuotaPushMsg",modelMap);
	}


    /**
     * 发放额度-发送消息 并且标记 额度
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/markQuotaAndSendMessage")
    public ReturnMessageDto<Integer>  markQuotaAndSendMessage(HttpServletRequest request){
        String preIds=request.getParameter("sendQuotaPreIds");
        //msgRoles
		String msgRoles=request.getParameter("msgRoles");
		//msgTitle
		String msgTitle=request.getParameter("msgTitle");
		//msgContent
		String msgContent=request.getParameter("msgContent");
		//msgIstimer
		String msgIstimer=request.getParameter("msgIstimer");
		//msgTimer
		String msgTimer=request.getParameter("msgTimer");

		if(StringUtil.isBlank(preIds)){
			return ReturnMessageDto.fail("预约列表不能为空！");
		}
		if(StringUtil.isBlank(msgRoles)){
			return ReturnMessageDto.fail("发送对象不能为空！");
		}
		return  preControlHbService.markQuotaAndSendMessage(Arrays.asList(preIds.split(SEPARATOR_COMMA)),
				Arrays.asList(msgRoles.split(SEPARATOR_COMMA)),
				msgTitle,msgContent,msgIstimer,msgTimer,getLoginUserId(request));
    }

}