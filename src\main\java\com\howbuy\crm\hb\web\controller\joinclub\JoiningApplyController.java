package com.howbuy.crm.hb.web.controller.joinclub;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoFacade;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoRequest;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.QueryAllCustInfoAndDisCustInfoResponse;
import com.howbuy.acccenter.facade.query.queryallcustinfoanddiscustinfo.bean.CustInfoBean;
import com.howbuy.acccenter.facade.query.querykycexamdtl.QueryKycExamDtlFacade;
import com.howbuy.acccenter.facade.query.querykycexamdtl.QueryKycExamDtlRequest;
import com.howbuy.acccenter.facade.query.querykycexamdtl.QueryKycExamDtlResponse;
import com.howbuy.acccenter.facade.query.querykycexamdtl.bean.ExamInfoBean;
import com.howbuy.acccenter.facade.trade.commitkycanswer.CommitKycAnswerRequest;
import com.howbuy.acccenter.facade.trade.kycinfo.InvestorConfirmRequest;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.cc.center.feature.answer.domain.CommitAnswerModeEnum;
import com.howbuy.cc.center.feature.kycinfo.domain.UserTypeEnum;
import com.howbuy.cc.center.feature.question.domain.ExamInfoDomain;
import com.howbuy.cc.center.feature.question.domain.ExamType;
import com.howbuy.cc.center.feature.question.domain.OptionInfoDomain;
import com.howbuy.cc.center.feature.question.request.QueryKycExamDetailRequest;
import com.howbuy.cc.center.feature.question.response.QueryKycExamDetailResponse;
import com.howbuy.cc.center.feature.question.service.QueryKycExamDetailService;
import com.howbuy.crm.base.BaseConstantEnum;
import com.howbuy.crm.hb.constants.CrmTradeServerPathConstant;
import com.howbuy.crm.hb.constants.DfileConstants;
import com.howbuy.crm.hb.domain.conscust.Conscustrpubcust;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.joinclub.CmConscustsurveyanswer;
import com.howbuy.crm.hb.domain.joinclub.CmConscustsurveyrec;
import com.howbuy.crm.hb.domain.joinclub.CmConscustsurveyrecFile;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.conscust.ConscustrpubcustService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.joinclub.CmConscustsurveyanswerService;
import com.howbuy.crm.hb.service.joinclub.CmConscustsurveyrecFileService;
import com.howbuy.crm.hb.service.joinclub.CmConscustsurveyrecService;
import com.howbuy.crm.hb.web.controller.counter.BaseCounterController;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.nt.basedetail.dto.CustBaseDetailDomain;
import com.howbuy.crm.nt.basedetail.dto.CustKycInfo;
import com.howbuy.crm.nt.basedetail.request.QueryCustBaseDetailRequest;
import com.howbuy.crm.nt.basedetail.response.QueryCustBaseDetailResponse;
import com.howbuy.crm.nt.basedetail.service.QueryCustBaseDetailService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.trade.common.enums.counter.CounterBusiEnum;
import com.howbuy.crm.trade.common.enums.counter.CounterCheckLevelEnum;
import com.howbuy.crm.trade.common.enums.counter.CounterStateEnum;
import com.howbuy.crm.trade.common.response.BaseResponse;
import com.howbuy.crm.trade.model.counter.dto.CmCounterOrderDto;
import com.howbuy.crm.trade.model.counter.dto.CounterCheckFileParamVo;
import com.howbuy.crm.trade.model.counter.request.EditCmCounterOrderRequest;
import com.howbuy.crm.trade.model.counter.request.SaveCmCounterOrderRequest;
import com.howbuy.crm.trade.model.counter.vo.CmCounterOrderSearchVo;
import com.howbuy.dfile.HFileService;
import crm.howbuy.base.constants.CRM3ErrorCode;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.enums.DisCodeEnum;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Slf4j
@Controller
@RequestMapping(value = "/joinclub")
public class JoiningApplyController  extends BaseCounterController {

    @Autowired
    private ConscustService conscustService;

    @Autowired
    private CmConscustsurveyrecService cmConscustsurveyrecService;

    @Autowired
    private CmConscustsurveyanswerService cmConscustsurveyanswerService;

    @Autowired
    private QueryKycExamDetailService queryKycExamDetailService;
    @Autowired
    private QueryKycExamDtlFacade queryKycExamDtlFacade;

    @Autowired
    private CmConscustsurveyrecFileService cmConscustsurveyrecFileService;
    
    @Autowired
    private CommonService commonService;

    @Autowired
    private DecryptSingleFacade decryptSingleFacade;
    
    @Autowired
	private ConscustrpubcustService conscustrpubcustService;
    
    @Autowired
    private QueryAllCustInfoAndDisCustInfoFacade queryAllCustInfoAndDisCustInfoFacade;
    @Autowired
    private QueryCustBaseDetailService queryCustBaseDetailService;


//     * 私募客户调查问卷流水文件
//     * 历史功能，私募客户调查问卷流水文件 日期停留在2022-10-14 16:22:01
//     * 数据表：CM_CONSCUSTSURVEYREC_FILE 。
//     * 数据库存储 ： /data/files/conscustsurveyrec/doc/20200716  。



    @RequestMapping("/listCheckJoinClub")
    public String listCheckJoinClub(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute("loginUser");
        request.setAttribute("userid", user.getUserId());
        return "joinclub/listCheckJoinClub";
    }

    /**
     * 好买入会
     * @param request
     * @return
     */
    @RequestMapping("/addjoiningapply")
    public String addjoiningapply(HttpServletRequest request) {
        request.setAttribute("conscustno", request.getParameter("conscustno"));
        Conscust consCust = conscustService.getConscust(request.getParameter("conscustno"));
        QueryKycExamDtlRequest req = new QueryKycExamDtlRequest();
        req.setOutletCode("CRM");
        //分销传好臻
        QueryKycExamDtlResponse res = new QueryKycExamDtlResponse();
        if (consCust != null) {
            if(StringUtil.isNotNullStr(consCust.getIdnoCipher())){
                consCust.setIdno(decryptSingleFacade.decrypt(consCust.getIdnoCipher()).getCodecText());
            }
            if(StringUtil.isNotNullStr(consCust.getAddrCipher())){
                consCust.setAddr(decryptSingleFacade.decrypt(consCust.getAddrCipher()).getCodecText());
            }
            if(StringUtil.isNotNullStr(consCust.getEmailCipher())){
                consCust.setEmail(decryptSingleFacade.decrypt(consCust.getEmailCipher()).getCodecText());
            }
            request.setAttribute("cust", consCust);
            //资料业务类型   好买入会  21
            request.setAttribute("selectedBusiType", CounterBusiEnum.RISK_ANALYSE_HOWBUY.getKey());
            // 机构客户和基金客户展示同一份问卷
            if (StaticVar.INVST_TYPE_ORG.equals(consCust.getInvsttype()) || StaticVar.INVST_TYPE_FUND.equals(consCust.getInvsttype()) || StaticVar.INVST_TYPE_PRODUCT.equals(consCust.getInvsttype())) {
                // 获取机构和基金客户问卷
                req.setDisCode(DisCodeEnum.FOF.getCode());
                req.setExamType(ExamType.INSTITUTION.getValue());
                req.setInvstType(UserTypeEnum.INSTITUTION.getValue());
                log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(req));
                res = queryKycExamDtlFacade.execute(req);
                log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(res));
                ExamInfoBean domain = res.getExamInfoBean();
                request.setAttribute("domain", domain);

                // 根据投资者类型不同，展示不同的证件类型
                if (StaticVar.INVST_TYPE_ORG.equals(consCust.getInvsttype())) {
                    request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfInst", consCust.getIdtype()));
                } else if (StaticVar.INVST_TYPE_PRODUCT.equals(consCust.getInvsttype())) {
                    request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfProduct", consCust.getIdtype()));
                } else if (StaticVar.INVST_TYPE_FUND.equals(consCust.getInvsttype())) {
                    request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfFund", consCust.getIdtype()));
                }
                request.setAttribute("qcount", domain.getQuestions().size());
                
                //法定代表人/负责人
                Map<String,Object> paramMap = new HashMap<String,Object>();
    			paramMap.put("conscustno", request.getParameter("conscustno"));
    			Conscustrpubcust conscustrpubcust = conscustrpubcustService.getPubCustByConscustNo(paramMap);
    			if(conscustrpubcust != null && StringUtils.isNotBlank(conscustrpubcust.getPubcustno())){
    				QueryAllCustInfoAndDisCustInfoRequest dubboREQ = new QueryAllCustInfoAndDisCustInfoRequest();
                    dubboREQ.setTxAcctNo(conscustrpubcust.getPubcustno());
                    log.info(dubboREQ.getClass().getSimpleName() + "|" + JSON.toJSON(dubboREQ));
    				QueryAllCustInfoAndDisCustInfoResponse dubboRSP = queryAllCustInfoAndDisCustInfoFacade.execute(dubboREQ);
    				log.info(dubboRSP.getClass().getSimpleName() + "|" + JSON.toJSON(dubboRSP));
    				if (dubboRSP != null && "0000000".equals(dubboRSP.getReturnCode()) && dubboRSP.getCustInfo() != null) {
    					CustInfoBean custInfoBean = dubboRSP.getCustInfo();
    					request.setAttribute("corporation", custInfoBean.getCorporation());
    				}
    			}

                return "joinclub/addjoiningjgapply";
            } else if (StaticVar.INVST_TYPE_PERSONAL.equals(consCust.getInvsttype())) {
                req.setDisCode(DisChannelCodeEnum.HOWBUY.getCode());
                req.setExamType(ExamType.HIGH_END.getValue());
                req.setInvstType(UserTypeEnum.PERSONAL.getValue());
                log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(req));
                res = queryKycExamDtlFacade.execute(req);
                log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(res));
                ExamInfoBean domain = res.getExamInfoBean();
                request.setAttribute("domain", domain);
                request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("idtype", consCust.getIdtype()));
                request.setAttribute("qcount", domain.getQuestions().size());
                return "joinclub/addjoiningapply";
            }
        }
        return "";
    }

    /**
     * 好臻入会
     * @param request
     * @return
     */
    @RequestMapping("/addjoininghzapply")
    public String addjoininghzapply(HttpServletRequest request) {
        request.setAttribute("conscustno", request.getParameter("conscustno"));
        Conscust consCust = conscustService.getConscust(request.getParameter("conscustno"));
        QueryKycExamDtlRequest req = new QueryKycExamDtlRequest();
        req.setOutletCode("CRM");
        //分销传好臻
        req.setDisCode(DisChannelCodeEnum.HZ.getCode());
        QueryKycExamDtlResponse res = new QueryKycExamDtlResponse();
        if (consCust != null) {
            if(StringUtil.isNotNullStr(consCust.getIdnoCipher())){
                consCust.setIdno(decryptSingleFacade.decrypt(consCust.getIdnoCipher()).getCodecText());
            }
            if(StringUtil.isNotNullStr(consCust.getAddrCipher())){
                consCust.setAddr(decryptSingleFacade.decrypt(consCust.getAddrCipher()).getCodecText());
            }
            if(StringUtil.isNotNullStr(consCust.getEmailCipher())){
                consCust.setEmail(decryptSingleFacade.decrypt(consCust.getEmailCipher()).getCodecText());
            }
            request.setAttribute("cust", consCust);
            //资料业务类型    好臻入会  44
            request.setAttribute("selectedBusiType", CounterBusiEnum.RISK_ANALYSE_HZ.getKey());
            // 机构客户和基金客户展示同一份问卷
            if (StaticVar.INVST_TYPE_ORG.equals(consCust.getInvsttype())
                    || StaticVar.INVST_TYPE_FUND.equals(consCust.getInvsttype())
                    || StaticVar.INVST_TYPE_PRODUCT.equals(consCust.getInvsttype())) {
                // 获取机构和基金客户问卷
                req.setExamType(ExamType.INSTITUTION.getValue());
                req.setInvstType(UserTypeEnum.INSTITUTION.getValue());
                log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(req));
                res = queryKycExamDtlFacade.execute(req);
                log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(res));
                ExamInfoBean domain = res.getExamInfoBean();
                request.setAttribute("domain", domain);

                // 根据投资者类型不同，展示不同的证件类型
                if (StaticVar.INVST_TYPE_ORG.equals(consCust.getInvsttype())) {
                    request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfInst", consCust.getIdtype()));
                } else if (StaticVar.INVST_TYPE_PRODUCT.equals(consCust.getInvsttype())) {
                    request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfProduct", consCust.getIdtype()));
                } else if (StaticVar.INVST_TYPE_FUND.equals(consCust.getInvsttype())) {
                    request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfFund", consCust.getIdtype()));
                }
                request.setAttribute("qcount", domain.getQuestions().size());
                
                //法定代表人/负责人
                Map<String,Object> paramMap = new HashMap<String,Object>();
    			paramMap.put("conscustno", request.getParameter("conscustno"));
    			Conscustrpubcust conscustrpubcust = conscustrpubcustService.getPubCustByConscustNo(paramMap);
    			if(conscustrpubcust != null && StringUtils.isNotBlank(conscustrpubcust.getPubcustno())){
    				QueryAllCustInfoAndDisCustInfoRequest dubboREQ = new QueryAllCustInfoAndDisCustInfoRequest();
                    dubboREQ.setTxAcctNo(conscustrpubcust.getPubcustno());
                    log.info(dubboREQ.getClass().getSimpleName() + "|" + JSON.toJSON(dubboREQ));
    				QueryAllCustInfoAndDisCustInfoResponse dubboRSP = queryAllCustInfoAndDisCustInfoFacade.execute(dubboREQ);
    				log.info(dubboRSP.getClass().getSimpleName() + "|" + JSON.toJSON(dubboRSP));
    				if (dubboRSP != null && "0000000".equals(dubboRSP.getReturnCode()) && dubboRSP.getCustInfo() != null) {
    					CustInfoBean custInfoBean = dubboRSP.getCustInfo();
    					request.setAttribute("corporation", custInfoBean.getCorporation());
    				}
    			}
    			
                return "joinclub/addjoininghzjgapply";
            } else if (StaticVar.INVST_TYPE_PERSONAL.equals(consCust.getInvsttype())) {
                req.setExamType(ExamType.HIGH_END.getValue());
                req.setInvstType(UserTypeEnum.PERSONAL.getValue());
                log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(req));
                res = queryKycExamDtlFacade.execute(req);
                log.info("调账户中心查询问卷试题queryKycExamDtlFacade.execute："+JSON.toJSON(res));
                ExamInfoBean domain = res.getExamInfoBean();
                request.setAttribute("domain", domain);
                request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("idtype", consCust.getIdtype()));
                request.setAttribute("qcount", domain.getQuestions().size());
                return "joinclub/addjoininghzapply";
            }
        }
        return "";
    }
    //新增入会申请处理
    @ResponseBody
    @RequestMapping("/addJoiningApplyDeal")
    public BaseResponse<String> addJoiningApplyDeal(HttpServletRequest request) throws Exception{
        User user = (User)request.getSession().getAttribute("loginUser");
        String result = "";
        String str = request.getParameter("str");
        String examId = request.getParameter("examId");
        str = str.substring(1);
        String [] ques = str.split("\\|");
        String qualifiedinvestor = request.getParameter("qualifiedinvestor");
        String consCustNo = request.getParameter("consCustNo");
        String singdate = request.getParameter("singdate");
        Conscust consCust = conscustService.getConscust(consCustNo);
        CmConscustsurveyrec surveyRec = new CmConscustsurveyrec();
        String surveyRecid = String.valueOf(commonService.getSeqValue("SEQ_PCUSTREC"));
        surveyRec.setAppserialno(surveyRecid);
        surveyRec.setTradedt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
        surveyRec.setAppcode("40");
        surveyRec.setTxcode("423087");
        surveyRec.setSingdate(singdate);
        surveyRec.setTxappflag(StaticVar.CODE_SUCCESS);
        surveyRec.setAppdt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
        surveyRec.setApptm(DateTimeUtil.PatternDate(new Date(), "HHmmss"));
        surveyRec.setTxchkflag(StaticVar.JOIN_CLUB_FLAG_NOT);
        surveyRec.setSurveyid(StaticVar.CUST_DOC_SATISF_5);
        surveyRec.setSetid(examId);
        surveyRec.setCreator(user.getUserId());
        surveyRec.setConscustno(consCustNo);
        surveyRec.setCustname(consCust.getCustname());
        surveyRec.setIdnoMask(consCust.getIdnoMask());
        surveyRec.setIdnoCipher(consCust.getIdnoCipher());
        surveyRec.setIdnoDigest(consCust.getIdnoDigest());
        surveyRec.setMobileMask(consCust.getMobileMask());
        surveyRec.setMobileCipher(consCust.getMobileCipher());
        surveyRec.setMobileDigest(consCust.getMobileDigest());

        List<CmConscustsurveyanswer> listanswers = new ArrayList<CmConscustsurveyanswer>();
        String inverstamt = ""; //可投资金额
        String inverstterm = ""; //投资期限
        String inverstaim ="";//投资目的
        for(String que : ques){
            CmConscustsurveyanswer answer = new CmConscustsurveyanswer();
            answer.setAnswerid(String.valueOf(commonService.getSeqValue("SEQ_PCUSTREC")));
            answer.setTradedt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
            answer.setConscustno(consCustNo);
            answer.setSurveyid(StaticVar.CUST_DOC_SATISF_5);
            answer.setSurveyserialno(surveyRecid);
            answer.setErrorFlag(StaticVar.JOIN_CLUB_ERROR_FLAG_OK);
            answer.setStimestamp(new Date());
            answer.setQuestionid(que.substring(0,que.indexOf(":")));
            answer.setAcode(que);
            listanswers.add(answer);
        }
        //合格投资者 题目
        CmConscustsurveyanswer answer = new CmConscustsurveyanswer();
        answer.setAnswerid(String.valueOf(commonService.getSeqValue("SEQ_PCUSTREC")));
        answer.setTradedt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
        answer.setConscustno(consCustNo);
        answer.setSurveyid(StaticVar.CUST_DOC_SATISF_5);
        answer.setSurveyserialno(surveyRecid);
        answer.setErrorFlag(StaticVar.JOIN_CLUB_ERROR_FLAG_OK);
        answer.setStimestamp(new Date());
        answer.setQuestionid("0");
        answer.setAcode(qualifiedinvestor);
        listanswers.add(answer);
        surveyRec.setGpsinvestlevel("");
        surveyRec.setGpsrisklevel("");
        surveyRec.setInverstamt(inverstamt);
        surveyRec.setInverstterm(inverstterm);
        surveyRec.setInverstaim(inverstaim);
        cmConscustsurveyrecService.insertCmConscustsurveyrec(surveyRec, listanswers);

        //新增资料的逻辑
        //先判断是否有在途入会流程   有则先作废旧的
        CmCounterOrderSearchVo searchVo = new CmCounterOrderSearchVo();
        ArrayList<String> list = Lists.newArrayList();
        list.add(CounterBusiEnum.RISK_ANALYSE_HOWBUY.getKey());
        searchVo.setConscustno(consCustNo);
        searchVo.setBusiIdList(list);
        log.info("查询订单数据接口调用参数:{}" , JSON.toJSONString(searchVo));
        BaseResponse<List<CmCounterOrderDto>> httpRsp =
                getPostEntityByJsonObject(CrmTradeServerPathConstant.QUERY_COUNTER_ORDR_DTO_LIST,
                        searchVo,new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderDto>>>(){});
        if(!httpRsp.isSuccess()){
            BaseResponse<String> stringBaseResponse = new BaseResponse<>();
            stringBaseResponse.setReturnMsg("校验查询订单信息异常!");
            stringBaseResponse.setReturnCode(BaseConstantEnum.SYS_ERROR.getCode());
            return  stringBaseResponse;
        }
        List<CmCounterOrderDto> data = httpRsp.getData();
        if(!CollectionUtils.isEmpty(data) ){
            //查询到单子
            for(CmCounterOrderDto dto: data){
                if(!CounterStateEnum.ORDER_PASS.getKey().equals(dto.getCurStat())
                        && !CounterStateEnum.CANCEL.getKey().equals(dto.getCurStat())) {
                    //作废
                    Map<String, String> postParam = new HashMap<String, String>();
                    postParam.put("id", dto.getId());
                    postParam.put("operatorNo", user.getUserId());
                    BaseResponse<String> invalidRsp = getPostEntityByMap(CrmTradeServerPathConstant.INVALID_COUNTER_ORDER, postParam, new ParameterizedTypeReference<BaseResponse<String>>() {
                    });
                }
            }
        }

        SaveCmCounterOrderRequest orderRequest=new SaveCmCounterOrderRequest();
        orderRequest.setBdId(request.getParameter("bdid"));
        orderRequest.setConscustNo(consCustNo);
        orderRequest.setForId(surveyRecid);
        orderRequest.setOperatorNo(user.getUserId());

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> files = multipartRequest.getFiles("filelist[]");
        //文件处理
        Map<String, List<String>>  fileMap=processCounterFile (files);
        orderRequest.setAddFileMap(fileMap);
        BaseResponse<String> counterResp= getPostEntityByJsonObject(CrmTradeServerPathConstant.INSERT_COUNTER_ORDER,
                orderRequest,
                new ParameterizedTypeReference<BaseResponse<String>>(){});

        if(counterResp.isSuccess()){
            counterResp.setReturnMsg("提交成功！请等待OP审核（可至资料管理页查看审核进度）");
        }
        return counterResp;
    }

    //好臻入会申请处理
    @ResponseBody
    @RequestMapping("/addJoiningHzApplyDeal")
    public BaseResponse<String> addJoiningHzApplyDeal(HttpServletRequest request) throws Exception{
        User user = (User)request.getSession().getAttribute("loginUser");
        String result = "";
        String str = request.getParameter("str");
        String examId = request.getParameter("examId");
        str = str.substring(1);
        String [] ques = str.split("\\|");
        String consCustNo = request.getParameter("consCustNo");
        String singdate = request.getParameter("singdate");
        Conscust consCust = conscustService.getConscust(consCustNo);
        //新增问卷的表
        CmConscustsurveyrec surveyRec = new CmConscustsurveyrec();
        String surveyRecid = String.valueOf(commonService.getSeqValue("SEQ_PCUSTREC"));
        surveyRec.setAppserialno(surveyRecid);
        surveyRec.setTradedt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
        surveyRec.setAppcode("40");
        surveyRec.setTxcode("423087");
        surveyRec.setSingdate(singdate);
        surveyRec.setTxappflag(StaticVar.CODE_SUCCESS);
        surveyRec.setAppdt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
        surveyRec.setApptm(DateTimeUtil.PatternDate(new Date(), "HHmmss"));
        surveyRec.setTxchkflag(StaticVar.JOIN_CLUB_FLAG_NOT);
        //好臻 013   好买 012
        surveyRec.setSurveyid("013");
        surveyRec.setSetid(examId);
        surveyRec.setCreator(user.getUserId());
        surveyRec.setConscustno(consCustNo);
        surveyRec.setCustname(consCust.getCustname());
        surveyRec.setIdnoMask(consCust.getIdnoMask());
        surveyRec.setIdnoCipher(consCust.getIdnoCipher());
        surveyRec.setIdnoDigest(consCust.getIdnoDigest());
        surveyRec.setMobileMask(consCust.getMobileMask());
        surveyRec.setMobileCipher(consCust.getMobileCipher());
        surveyRec.setMobileDigest(consCust.getMobileDigest());

        List<CmConscustsurveyanswer> listanswers = new ArrayList<CmConscustsurveyanswer>();
        String inverstamt = ""; //可投资金额
        String inverstterm = ""; //投资期限
        String inverstaim ="";//投资目的
        for(String que : ques){
            CmConscustsurveyanswer answer = new CmConscustsurveyanswer();
            answer.setAnswerid(String.valueOf(commonService.getSeqValue("SEQ_PCUSTREC")));
            answer.setTradedt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
            answer.setConscustno(consCustNo);
            answer.setSurveyid("013");
            answer.setSurveyserialno(surveyRecid);
            answer.setErrorFlag(StaticVar.JOIN_CLUB_ERROR_FLAG_OK);
            answer.setStimestamp(new Date());
            answer.setQuestionid(que.substring(0,que.indexOf(":")));
            answer.setAcode(que);
            listanswers.add(answer);
        }
        surveyRec.setGpsinvestlevel("");
        surveyRec.setGpsrisklevel("");
        surveyRec.setInverstamt(inverstamt);
        surveyRec.setInverstterm(inverstterm);
        surveyRec.setInverstaim(inverstaim);
        cmConscustsurveyrecService.insertCmConscustsurveyrec(surveyRec, listanswers);

        //新增资料的逻辑
        //先判断是否有在途入会流程   有则先作废旧的
        CmCounterOrderSearchVo searchVo = new CmCounterOrderSearchVo();
        ArrayList<String> list = Lists.newArrayList();
        list.add(CounterBusiEnum.RISK_ANALYSE_HZ.getKey());
        searchVo.setConscustno(consCustNo);
        searchVo.setBusiIdList(list);
        log.info("查询订单数据接口调用参数:{}" , JSON.toJSONString(searchVo));
        BaseResponse<List<CmCounterOrderDto>> httpRsp =
                getPostEntityByJsonObject(CrmTradeServerPathConstant.QUERY_COUNTER_ORDR_DTO_LIST,
                        searchVo,new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderDto>>>(){});
        if(!httpRsp.isSuccess()){
            BaseResponse<String> stringBaseResponse = new BaseResponse<>();
            stringBaseResponse.setReturnMsg("校验查询订单信息异常!");
            stringBaseResponse.setReturnCode(BaseConstantEnum.SYS_ERROR.getCode());
            return  stringBaseResponse;
        }
        //没查询到单子
        List<CmCounterOrderDto> data = httpRsp.getData();
        if(!CollectionUtils.isEmpty(data) ){
            //查询到单子
            for(CmCounterOrderDto dto: data){
                if(!CounterStateEnum.ORDER_PASS.getKey().equals(dto.getCurStat())
                        && !CounterStateEnum.CANCEL.getKey().equals(dto.getCurStat())) {
                    //作废
                    Map<String, String> postParam = new HashMap<String, String>();
                    postParam.put("id", dto.getId());
                    postParam.put("operatorNo", user.getUserId());
                    BaseResponse<String> invalidRsp = getPostEntityByMap(CrmTradeServerPathConstant.INVALID_COUNTER_ORDER, postParam, new ParameterizedTypeReference<BaseResponse<String>>() {
                    });
                }
            }
        }
        String operatorNo=getLoginUserId(request);

        SaveCmCounterOrderRequest orderRequest=new SaveCmCounterOrderRequest();
        orderRequest.setBdId(request.getParameter("bdid"));
        orderRequest.setConscustNo(consCustNo);
        orderRequest.setForId(surveyRecid);
        orderRequest.setOperatorNo(operatorNo);

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> files = multipartRequest.getFiles("filelist[]");

        //文件处理
        Map<String, List<String>>  fileMap=processCounterFile (files);
        orderRequest.setAddFileMap(fileMap);

        BaseResponse<String> counterResp= getPostEntityByJsonObject(CrmTradeServerPathConstant.INSERT_COUNTER_ORDER,
                orderRequest,
                new ParameterizedTypeReference<BaseResponse<String>>(){});

        if(counterResp.isSuccess()){
            counterResp.setReturnMsg("提交成功！请等待OP审核（可至资料管理页查看审核进度）");
        }

        return counterResp;
    }
    /**
     * 加载页面数据方法
     *
     * @param request
     * @param response
     * @return Map<String, Object>
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listCheckJoinClubJson")
    public Map<String, Object> listCheckJoinClubJson(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();
        String custname = request.getParameter("custname");
        String mobile = request.getParameter("mobile");
        String idno = request.getParameter("idno");
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        String beginDt = request.getParameter("beginDt");
        String endDt = request.getParameter("endDt");

        if (StringUtil.isNotNullStr(custname)) {
            param.put("custname", custname);
        } else {
            param.put("custname", null);
        }

        if (StringUtil.isNotNullStr(idno)) {
            param.put("idno", DigestUtil.digest(idno.trim()));
        } else {
            param.put("idno", null);
        }

        if (StringUtil.isNotNullStr(beginDt)) {
            param.put("beginDt", beginDt);
        } else {
            param.put("beginDt", null);
        }
        if (StringUtil.isNotNullStr(endDt)) {
            param.put("endDt", endDt);
        } else {
            param.put("endDt", null);
        }
        if (StringUtil.isNotNullStr(consCode)) {
            param.put("creator", consCode);
        } else {
            param.put("creators", Util.getSubQueryByOrgCode(orgCode));
        }
        //param.put("surveyid", StaticVar.CUST_DOC_SATISF_5);
        Map<String, Object> resultMap = new HashMap<String, Object>();

        PageData<CmConscustsurveyrec> pageData = cmConscustsurveyrecService.listCmConscustsurveyrecByPage(param);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<CmConscustsurveyrec> listdata = pageData.getListData();
        List<CmConscustsurveyrec> list = new ArrayList<CmConscustsurveyrec>();
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        ConstantCache constantCache = ConstantCache.getInstance();
        for (CmConscustsurveyrec info : listdata) {
            if (consOrgCache.getAllUserMap().containsKey(info.getChecker())) {
                info.setChecker(consOrgCache.getAllUserMap().get(info.getChecker()));
            }

            String uporgcode = consOrgCache.getUpOrgMapCache().get(info.getOrgcode());
            if("0".equals(uporgcode)){
                info.setUporgname(info.getOrgname());
            }else{
                info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
            }

            // 处理时间显示格式
            if (StringUtils.isNotBlank(info.getApptm())) {
                String h = info.getApptm().substring(0, 2);
                String m = info.getApptm().substring(2, 4);
                String s = info.getApptm().substring(4, 6);
                StringBuffer sb = new StringBuffer();
                sb.append(h).append(":").append(m).append(":").append(s);
                info.setApptm(sb.toString());
            }
            info.setTxchkflagval(constantCache.getVal("joinclubflag", info.getTxchkflag()));
            list.add(info);
        }
        resultMap.put("rows", list);
        return resultMap;
    }

    /**
     * 入会管理数据导出方法
     *
     * @param request
     * @param response
     * @return Map<String, Object>
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/joinClubExport")
    public Map<String, Object> joinClubExport(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        param = new ParamUtil(request).getParamMap();
        String custname = request.getParameter("custname");
        String mobile = request.getParameter("mobile");
        String idno = request.getParameter("idno");
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        String beginDt = request.getParameter("beginDt");
        String endDt = request.getParameter("endDt");

        if (StringUtil.isNotNullStr(custname)) {
            param.put("custname", custname);
        } else {
            param.put("custname", null);
        }

        if (StringUtil.isNotNullStr(idno)) {
            param.put("idno", DigestUtil.digest(idno.trim()));
        } else {
            param.put("idno", null);
        }

        if (StringUtil.isNotNullStr(beginDt)) {
            param.put("beginDt", beginDt);
        } else {
            param.put("beginDt", null);
        }
        if (StringUtil.isNotNullStr(endDt)) {
            param.put("endDt", endDt);
        } else {
            param.put("endDt", null);
        }
        if (StringUtil.isNotNullStr(consCode)) {
            param.put("creator", consCode);
        } else {
            param.put("creators", Util.getSubQueryByOrgCode(orgCode));
        }
        //param.put("surveyid", StaticVar.CUST_DOC_SATISF_5);
        Map<String, Object> resultMap = new HashMap<String, Object>();

        List<CmConscustsurveyrec> listExportData = cmConscustsurveyrecService.listCmConscustsurveyrec(param);
        List<CmConscustsurveyrec> list = new ArrayList<CmConscustsurveyrec>();
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        ConstantCache constantCache = ConstantCache.getInstance();
        for (CmConscustsurveyrec info : listExportData) {
            if (consOrgCache.getAllUserMap().containsKey(info.getChecker())) {
                info.setChecker(consOrgCache.getAllUserMap().get(info.getChecker()));
            }
            info.setTxchkflagval(constantCache.getVal("joinclubflag", info.getTxchkflag()));

            String uporgcode = consOrgCache.getUpOrgMapCache().get(info.getOrgcode());
            if("0".equals(uporgcode)){
                info.setUporgname(info.getOrgname());
            }else{
                info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
            }

            // 处理时间显示格式
            if (StringUtils.isNotBlank(info.getApptm())) {
                String h = info.getApptm().substring(0, 2);
                String m = info.getApptm().substring(2, 4);
                String s = info.getApptm().substring(4, 6);
                StringBuffer sb = new StringBuffer();
                sb.append(h).append(":").append(m).append(":").append(s);
                info.setApptm(sb.toString());
            }
            info.setAppdt(info.getAppdt() + " " + info.getApptm());
            if(StaticVar.CUST_DOC_SATISF_5.equals(info.getSurveyid())){
                info.setSurveyid("好买入会");
            }else if("013".equals(info.getSurveyid())){
                info.setSurveyid("好臻入会");
            }
            list.add(info);
        }

        try {
            if (list != null && list.size() > 0) {
                // 清空输出流
                response.reset();

                // 设置文件格式和名字
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition", "attachment;fileName=" + new String("入会管理.xls".getBytes("gb2312"), "ISO8859-1"));
                ServletOutputStream os = response.getOutputStream();

                String[] columnName = {"姓名", "提交日期", "审核标志", "审核人", "审核日期", "最近审核通过日期", "所属投顾", "所属区域", "所属部门","入会类型"};
                String[] beanProperty = {"custname", "appdt", "txchkflagval", "checker", "checkdt", "maxcheckdt", "consname",  "uporgname", "orgname","surveyid"};
                ExcelWriter.writeExcel(os, "入会管理", 0, list, columnName, beanProperty);
                os.close(); // 关闭流
                resultMap.put("msg", "success");
            } else {
                resultMap.put("msg", "noData");
            }
        } catch (Exception e) {
            resultMap.put("msg", "error");
            log.error("文件导出异常", e);
        }
        return resultMap;
    }

    /**
     * 加载好买入会审核页面
     *
     * @param request
     * @return String
     */
    @RequestMapping("/checkJoinClubApply")
    public String checkJoinClubApply(HttpServletRequest request) {
        String appserialno = request.getParameter("appserialno");
        String result = "";
        request.setAttribute("appserialno", appserialno);
        Map<String, String> param = new HashMap<String, String>();
        param.put("appserialno", appserialno);
        CmConscustsurveyrec rec = cmConscustsurveyrecService.getCmConscustsurveyrec(param);
        String conscustno = "";
        String investtype = "";
        if (rec != null) {
            conscustno = rec.getConscustno();
            request.setAttribute("checkadvice", rec.getCheckadvice());
            request.setAttribute("singdate", rec.getSingdate());

            param.clear();
            param.put("appserialno", rec.getAppserialno());
            param.put("isdel", "0");
            //NOTICE : 2025年3月27日 WEBDAV迁移标记： 私募客户调查问卷流水文件 日期停留在2022-10-14 16:22:01
            List<CmConscustsurveyrecFile> listCmConscustsurveyrecFile = cmConscustsurveyrecFileService.listCmConscustsurveyrecFile(param);
            request.setAttribute("listCmConscustsurveyrecFile", listCmConscustsurveyrecFile);
        }
        
        if (StringUtil.isNotNullStr(conscustno)) {
//            param.clear();
//            param.put("conscustno", conscustno);
            Conscust cust = conscustService.getConscust(conscustno);

            if (cust != null) {
                investtype = cust.getInvsttype();

                if(StringUtil.isNotNullStr(cust.getIdno())){
                    cust.setIdno(decryptSingleFacade.decrypt(cust.getIdnoCipher()).getCodecText());
                }
                if(StringUtil.isNotNullStr(cust.getAddrCipher())){
                    cust.setAddr(decryptSingleFacade.decrypt(cust.getAddrCipher()).getCodecText());
                }
                if(StringUtil.isNotNullStr(cust.getEmailCipher())){
                    cust.setEmail(decryptSingleFacade.decrypt(cust.getEmailCipher()).getCodecText());
                }

                request.setAttribute("cust", cust);
                param.clear();
                param.put("surveyserialno", appserialno);
                List<CmConscustsurveyanswer> list = cmConscustsurveyanswerService.listCmConscustsurveyanswer(param);
                Map<String, String> ansmap = new HashMap<String, String>();
                Map<String, String> erroransmap = new HashMap<String, String>();
                for (CmConscustsurveyanswer obj : list) {
                    ansmap.put(obj.getQuestionid(), obj.getAcode());
                    if ("0".equals(obj.getErrorFlag())) {
                        erroransmap.put(obj.getQuestionid(), obj.getErrorFlag());
                    }
                }
                request.setAttribute("ansmap", ansmap);
                request.setAttribute("erroransmap", erroransmap);
                QueryKycExamDetailRequest req = new QueryKycExamDetailRequest();
                req.setOutletCode("CRM");
                QueryKycExamDetailResponse res = new QueryKycExamDetailResponse();
                if (StaticVar.INVST_TYPE_PERSONAL.equals(investtype)) {

                    req.setExamType(ExamType.HIGH_END.getValue());
                    req.setUserType(UserTypeEnum.PERSONAL.getValue());
                    res = queryKycExamDetailService.queryKycExamDetail(req);
                    ExamInfoDomain domain = res.getExamInfoDomain();
                    if (domain != null) {
                        request.setAttribute("domain", domain);
                        request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("idtype", cust.getIdtype()));
                        request.setAttribute("qcount", domain.getQuestions().size());
                        result = "joinclub/checkJoinClub";
                    } else {
                        log.info("queryKycExamDetailService.queryKycExamDetail(req)接口未返回任何个人问卷信息！");
                    }
                } else {
                    //获取机构问卷
                    req.setExamType(ExamType.INSTITUTION.getValue());
                    req.setUserType(UserTypeEnum.INSTITUTION.getValue());
                    res = queryKycExamDetailService.queryKycExamDetail(req);
                    ExamInfoDomain domain = res.getExamInfoDomain();
                    if (domain != null) {
                        request.setAttribute("domain", domain);

                        // 根据投资者类型不同，展示不同的证件类型
                        if (StaticVar.INVST_TYPE_ORG.equals(cust.getInvsttype())) {
                            request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfInstNew", cust.getIdtype()));
                        } else if (StaticVar.INVST_TYPE_FUND.equals(cust.getInvsttype())) {
                            request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfFund", cust.getIdtype()));
                        }else if (StaticVar.INVST_TYPE_PRODUCT.equals(investtype)) {
                            request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfProduct", cust.getIdtype()));
                        }
                        // 设置题目总数
                        request.setAttribute("qcount", domain.getQuestions().size());
                        result = "joinclub/checkJoinOrgClub";
                    } else {
                        log.info("queryKycExamDetailService.queryKycExamDetail(req)接口未返回任何机构问卷信息！");
                    }
                }
            }
        }
        return result;
    }



    @ResponseBody
    @RequestMapping("/getSurveyFileStream.do")
    public void getSurveyFileStream(HttpServletRequest request, HttpServletResponse response) {
        String id = request.getParameter("id");
        Map<String, String> param = new HashMap<String, String>();
        param.put("id", id);
        CmConscustsurveyrecFile file = cmConscustsurveyrecFileService.getCmConscustsurveyrecFile(param);

// 数据库数据
//        ID	APPSERIALNO	FILENAME	FILESIZE	FILESUFFIX	FILEPATH	ISDEL	CREATEDT	CREATERID
//        120	13917278	入会问卷	403249	PDF	/data/files/conscustsurveyrec/doc/20200724	0	2020-07-24 10:54:00	daquan.song

//实际文件存储
///data/files/conscustsurveyrec/doc/20200724/120/入会问卷.PDF

        // /data/files/conscustsurveyrec/doc/20200724
         String  dbPath = file.getFilepath();
         // 历史功能，存储全路径。 截取 [/data/files/conscustsurveyrec]  -->   /doc/20200724
         String relativePah = dbPath.replace(DfileConstants.CUST_SURVEYREC_PREFIX_PATH, "");
        //读取 存储路径 ： /doc/20200724/数据库id
        String readRelativePath  = relativePah + File.separator + id ;


        String fileName =file.getFilename() +"." + file.getFilesuffix();

        //读取
        HFileService instance = HFileService.getInstance();
        byte[]  fileBytes= new byte[0];
        try {
            log.info("文件file：{} 读取开始，relativePath:{},fileName:{}", JSON.toJSONString(file),relativePah,fileName);
            fileBytes = instance.read2Bytes(DfileConstants.CUST_SURVEYREC_STORE_CONFIG,
                    readRelativePath,
                    fileName);
        } catch (Exception e) {
            log.error("文件file：{} ，读取异常！",JSON.toJSONString(file));
            log.error("读取文件异常！",e);
        }

        ServletOutputStream outputStream = null;
        try {
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;fileName=" + new String(fileName.getBytes("gb2312"), StandardCharsets.ISO_8859_1));
            //输出文件流
            outputStream = response.getOutputStream();
            outputStream.write(fileBytes);
        }catch (Exception e){
            log.error("预览失败，fileName：{}, useId：{}", fileName, e);
        }finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭文件流异常", e);
                }
            }
        }

    }

    
    /**
     * 入会申请审核方法
     *
     * @param request
     * @return String
     * @throws Exception
     */
    @ResponseBody
    @Transactional
    @RequestMapping("/checkJoinClubDeal")
    public String checkJoinClubDeal(HttpServletRequest request) throws Exception {
        String result = "error";
        User user = (User) request.getSession().getAttribute("loginUser");
        //填写问卷流水号
        String appserialno = request.getParameter("appserialno");
        //1审核通过   2审核退回
        String type = request.getParameter("type");
        //审核建议
        String checkadvice = request.getParameter("checkadvice");
        //审核等级   OP初审
        String checkLevel = request.getParameter("checkLevel");
        //柜台订单ID
        String orderId = request.getParameter("id");
        //44好臻    21好买
        String joinType = request.getParameter("joinType");
        //文件审核拼接串
        String ids = request.getParameter("ids");
        log.info("checkJoinClubDeal参数：userId->{},appserialno->{},type->{},checkadvice->{}," +
                        "checkLevel->{},orderId->{},joinType->{}",
                user.getUserId(),appserialno,type,checkadvice,checkLevel,orderId,joinType);
        Map<String, String> param = new HashMap<String, String>();
        param.put("appserialno", appserialno);
        CmConscustsurveyrec rec = cmConscustsurveyrecService.getCmConscustsurveyrec(param);
        rec.setTxchkflag(type);
        rec.setChecker(user.getUserId());
        rec.setCheckdt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
        rec.setChecktm(DateTimeUtil.PatternDate(new Date(), "HHmmss"));
        rec.setCheckadvice(checkadvice);
        rec.setUpcheckadvice("1");//更新审核意见

        // 审核通过，
        // 更新入会申请表，去除错误标记，修改投顾客户表信息，新增投顾客户历史表
        if (StaticVar.JOIN_CLUB_FLAG_OK.equals(type)) {
            Map<String, String> updateMap = new HashMap<String, String>();
            updateMap.put("surveyserialno", appserialno);
            List<CmConscustsurveyanswer> listanswer = cmConscustsurveyanswerService.listCmConscustsurveyanswer(updateMap);
            updateMap.put("errorFlag", StaticVar.JOIN_CLUB_ERROR_FLAG_OK);
            updateMap.put("risklevelval", ConstantCache.getInstance().getVal("gpsRiskLevel", rec.getGpsrisklevel()));
            param.clear();
            param.put("conscustno", rec.getConscustno());
            Conscust consCust = conscustService.getConscust(rec.getConscustno());
            if (consCust != null) {
                String hboneno = consCust.getHboneno();
                CommitKycAnswerRequest req = new CommitKycAnswerRequest();
                req.setOutletCode("CRM");
                req.setRiskToleranceDate(rec.getSingdate());
                req.setCommitAnswerMode(CommitAnswerModeEnum.ASSIST.getValue());
                InvestorConfirmRequest invreq = new InvestorConfirmRequest();
                invreq.setOutletCode("CRM");
                invreq.setTradeChannel("9");
                invreq.setOperatorNo(user.getUserId());
                invreq.setHboneNo(hboneno);
                invreq.setAppDt(DateTimeUtil.getCurDate());
                invreq.setAppTm(DateTimeUtil.getCurTime());
                String inverstamt = "";  // 可投资金额
                String inverstterm = ""; // 投资期限
                String inverstaim = "";  // 投资目的
                Map<String, CustKycInfo> kycMap = Maps.newHashMap();
                if (StringUtils.isNotBlank(hboneno)) {
                    try {
                        QueryCustBaseDetailRequest custBaseDetailRequest = new QueryCustBaseDetailRequest();
                        custBaseDetailRequest.setHboneno(hboneno);
                        QueryCustBaseDetailResponse custBaseDetailResponse = queryCustBaseDetailService.queryConscustInfo(custBaseDetailRequest);
                        log.info("查询到客户信息:{}",JSON.toJSONString(custBaseDetailResponse));
                        if (CRM3ErrorCode.success.getValue().equals(custBaseDetailResponse.getReturnCode())) {
                            CustBaseDetailDomain custbasedetail = custBaseDetailResponse.getCustbasedetaildomain();
                            if (custbasedetail != null) {
                                kycMap = custbasedetail.getKycInfoMap();
                            }
                        }
                    } catch (Exception e) {
                        e.getMessage();
                        log.error("查询客户信息异常！", e);
                    }
                }
                //取好买分销下的是否签署
                CustKycInfo custKycInfo = kycMap.get(DisChannelCodeEnum.HOWBUY.getCode());
                Boolean investorsurvey  = null==custKycInfo?false:"1".equals(custKycInfo.getInvestorsurvey());
                log.info("查询到客户信息中的好买分销的signflag:{}",JSON.toJSONString(investorsurvey));
                if (StaticVar.INVST_TYPE_PERSONAL.equals(consCust.getInvsttype())) {
                    req.setHboneNo(hboneno);
                    req.setExamId(rec.getSetid());
                    req.setDisCode(CounterBusiEnum.RISK_ANALYSE_HZ.getKey().equals(joinType)? DisChannelCodeEnum.HZ.getCode():DisChannelCodeEnum.HOWBUY.getCode());
                    req.setOperatorNo(user.getUserId());
                    req.setUserType(UserTypeEnum.PERSONAL.getValue());
                    Map<String, OptionInfoDomain> mapans = new HashMap<String, OptionInfoDomain>();

                    for (CmConscustsurveyanswer answerInfo : listanswer) {
                        String ans = answerInfo.getAcode();
                        if ("0".equals(answerInfo.getQuestionid())) {
                            // 处理“合格投资者认证”的选项（合格投资者认证为非必填）
                            //私募合格
                            //a. 勾选个人金融资产不低于300万元 -----传 financialAssetConfirmation 金融资产确认标识=1；
                            if(StringUtils.contains(ans, "11")){
                                invreq.setFinancialAssetConfirmation(true);
                            }else{
                                invreq.setFinancialAssetConfirmation(false);
                            }
                            //b. 勾选近三年个人年均收入不低于50万元人民币 -----传 annualIncomeConfirmation 年收入确认标识=1；
                            if(StringUtils.contains(ans, "12")){
                                invreq.setAnnualIncomeConfirmation(true);
                            }else{
                                invreq.setAnnualIncomeConfirmation(false);
                            }
                            //c. 若未勾选a、b，则通过合格投资者查询接口 com.howbuy.acccenter.facade.trade.kycinfo.KycInfoFacade 查询私募合格投资者承诺书签署状态 signFlag 是否签署，
                            //>> 1-已签署 -----传 financialAssetConfirmation 金融资产确认标识=1、annualIncomeConfirmation 年收入确认标识=1；
                            //>> 0-未签署 -----传 financialAssetConfirmation 金融资产确认标识=0、annualIncomeConfirmation 年收入确认标识=0；
                            if(!StringUtils.contains(ans, "11") && !StringUtils.contains(ans, "12")){
                                invreq.setFinancialAssetConfirmation(investorsurvey);
                                invreq.setAnnualIncomeConfirmation(investorsurvey);
                            }

                            //资管合格
                            //a. 勾选家庭金融资产不低于500万元 -----传 qualifyFlag 资管合格投资者满足条件=2；
                            if (StringUtils.contains(ans, "13")) {
                                invreq.setQualifyFlag("2");
                            } else if(StringUtils.contains(ans, "14")){
                                //b. 勾选家庭金融净资产不低于300万元 -----传 qualifyFlag 资管合格投资者满足条件=1；
                                invreq.setQualifyFlag("1");
                            }else if(StringUtils.contains(ans, "15")){
                                //c. 勾选近三年本人年均收入不低于40万 -----传 qualifyFlag 资管合格投资者满足条件=3；
                                invreq.setQualifyFlag("3");
                            }
                            //根据勾选的私募/资管题进行对应传参
                            //a. 若选择了私募合格题目，则将问卷里选择的“客户签署日期”传给 confirmedDate合格投资者确认日期；
                            if(StringUtils.contains(ans, "11") || StringUtils.contains(ans, "12")){
                                invreq.setConfirmedDate(DateUtil.getString2Date(rec.getSingdate(),"yyyyMMdd"));
                            }
                            //b. 若选择了资管合格题目，则将问卷里选择的“客户签署日期”传给 fundConfirmedDate资管合格投资者确认日期、fundFlag是否签署资管合格投资者承诺书=1-已签署；
                            if(StringUtils.contains(ans, "13")
                                    || StringUtils.contains(ans, "14")
                                    || StringUtils.contains(ans, "15")){
                                invreq.setFundConfirmedDate(DateUtil.getString2Date(rec.getSingdate(),"yyyyMMdd"));
                                invreq.setFundFlag("1");
                            }
                        } else {
                            OptionInfoDomain dom = new OptionInfoDomain();
                            dom.setOptionId(ans.split(":")[2]);
                            dom.setOptionChar(ans.split(":")[3]);
                            mapans.put(ans.split(":")[1], dom);
                            // 可投资金额
                            if ("7".equals(ans.split(":")[0])) {
                                inverstamt = getPerAmtStr(ans.split(":")[3]);
                                rec.setGpsinvestlevel(ans.split(":")[3]);
                            }

                            // 一般投资期限
                            if ("12".equals(ans.split(":")[0])) {
                                inverstterm = getPerTermStr(ans.split(":")[3]);
                            }

                            // 投资目的
                            if ("14".equals(ans.split(":")[0])) {
                                inverstaim = getPerAimStr(ans.split(":")[3]);
                            }
                        }
                    }
                    req.setAnswer(mapans);
                } else {
                    //机构  产品   基金产品
                    req.setHboneNo(hboneno);
                    req.setExamId(rec.getSetid());
                    req.setOperatorNo(user.getUserId());
                    req.setDisCode(CounterBusiEnum.RISK_ANALYSE_HZ.getKey().equals(joinType)? DisChannelCodeEnum.HZ.getCode():"FOF201710");
                    req.setUserType(UserTypeEnum.INSTITUTION.getValue());
                    Map<String, OptionInfoDomain> mapans = new HashMap<String, OptionInfoDomain>();
                    for (CmConscustsurveyanswer answerInfo : listanswer) {
                        String ans = answerInfo.getAcode();
                        if ("0".equals(answerInfo.getQuestionid())) {
                            // 处理“合格投资者认证”的选项（合格投资者认证为非必填）
                            //机构
                            invreq.setDisCode("FOF201710");
                            if(StaticVar.INVST_TYPE_ORG.equals(consCust.getInvsttype())){
                                //私募合格
                                //机构和产品客户的FinancialAssetConfirmation一直是0
                                invreq.setFinancialAssetConfirmation(false);
                                //a. 勾选净资产不低于1000万元的单位 -----传 annualIncomeConfirmation 年收入确认标识=1；
                                if(StringUtils.contains(ans, "03")){
                                    invreq.setAnnualIncomeConfirmation(true);
                                }else{
                                //b. 若未勾选，则通过合格投资者查询接口 com.howbuy.acccenter.facade.trade.kycinfo.KycInfoFacade 查询私募合格投资者承诺书签署状态 signFlag 是否签署，
                                //>> 1-已签署 -----传 annualIncomeConfirmation 年收入确认标识=1；
                                //>> 0-未签署 -----传 annualIncomeConfirmation 年收入确认标识=0；
                                    invreq.setAnnualIncomeConfirmation(investorsurvey);
                                }

                                //资管合格
                                //a. 勾选最近1年末净资产不低于1000万元的法人单位 -----传 qualifyFlag 资管合格投资者满足条件=4；
                                if (StringUtils.contains(ans, "04")) {
                                    invreq.setQualifyFlag("4");
                                }
                                //根据勾选的私募/资管题进行对应传参
                                //a. 若选择了私募合格题目，则将问卷里选择的“客户签署日期”传给 confirmedDate合格投资者确认日期；
                                if(StringUtils.contains(ans, "03")){
                                    invreq.setConfirmedDate(DateUtil.getString2Date(rec.getSingdate(),"yyyyMMdd"));
                                }
                                //b. 若选择了资管合格题目，则将问卷里选择的“客户签署日期”传给 fundConfirmedDate资管合格投资者确认日期、fundFlag是否签署资管合格投资者承诺书=1-已签署；
                                if (StringUtils.contains(ans, "04")) {
                                    invreq.setFundConfirmedDate(DateUtil.getString2Date(rec.getSingdate(),"yyyyMMdd"));
                                    invreq.setFundFlag("1");
                                }
                            }else{
                                //产品   基金产品
                                //机构和产品客户的FinancialAssetConfirmation一直是0
                                invreq.setFinancialAssetConfirmation(false);
                                //私募合格题目任一选项被勾选后，传 annualIncomeConfirmation 年收入确认标识=1；
                                if((StringUtil.null2String(ans).indexOf("21") != -1)||(StringUtil.null2String(ans).indexOf("22") != -1)){
                                    invreq.setAnnualIncomeConfirmation(true);
                                    //取做问卷选择的“客户签署日期”进行接口传参
                                    //a. 若选择了私募合格题目，则传给 confirmedDate合格投资者确认日期；
                                    invreq.setConfirmedDate(DateUtil.getString2Date(rec.getSingdate(),"yyyyMMdd"));
                                }else{
                                    //b. 若未勾选任何选项，则通过合格投资者查询接口 com.howbuy.acccenter.facade.trade.kycinfo.KycInfoFacade 查询私募合格投资者承诺书签署状态 signFlag 是否签署，
                                    //>> 1-已签署 -----传 annualIncomeConfirmation 年收入确认标识=1；
                                    //>> 0-未签署 -----传 annualIncomeConfirmation 年收入确认标识=0；
                                    invreq.setAnnualIncomeConfirmation(investorsurvey);
                                }
                            }
                        } else {
                            OptionInfoDomain dom = new OptionInfoDomain();
                            dom.setOptionId(ans.split(":")[2]);
                            dom.setOptionChar(ans.split(":")[3]);
                            mapans.put(ans.split(":")[1], dom);
                            // 可投资金额
                            if ("2".equals(ans.split(":")[0])) {
                                inverstamt = getOrgAmtStr(ans.split(":")[3]);
                                rec.setGpsinvestlevel(getOrgGpsInvest(ans.split(":")[3]));
                            }

                            // 一般投资期限
                            if ("9".equals(ans.split(":")[0])) {
                                inverstterm = getOrgTermStr(ans.split(":")[3]);
                            }

                            // 投资目的
                            if ("6".equals(ans.split(":")[0])) {
                                inverstaim = getOrgAimStr(ans.split(":")[3]);
                            }
                        }
                    }
                    req.setAnswer(mapans);
                }
                rec.setInverstamt(inverstamt);
                rec.setInverstterm(inverstterm);
                rec.setInverstaim(inverstaim);
                consCust.setUddt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));

                //业务表修改   问卷结果同步到账户中心
                result = cmConscustsurveyrecService.updateCheckJoinClubYesNew(ConstantCache.getInstance().getRiskLeve(),
                        rec, updateMap, consCust, req,invreq, joinType);
                log.info("入会审核接口返回状态为：" + result);
                if (!"success".equals(result)) {
                    try {
                        log.info("入会审核处理失败，调用柜台线上化接口进行回滚数据");
                        log.info("回滚参数orderId：" + orderId);
                        //cmCounterOrderService.rollbackCounterOrderMsg(orderId); TODO: 待删除逻辑
                        return result;
                    } catch (Exception e) {
                        result = "orderBackError";
                        return result;
                    }
                }
            }
        } else {// 审核不通过
            String arraycheck = request.getParameter("arraycheck");
            List<Map<String, String>> list = new ArrayList<Map<String, String>>();
            if (StringUtil.isNotNullStr(arraycheck)) {
                String[] arr = arraycheck.split(",");
                for (String qid : arr) {
                    Map<String, String> upmap = new HashMap<String, String>();
                    upmap.put("surveyserialno", appserialno);
                    upmap.put("questionid", qid);
                    upmap.put("errorFlag", StaticVar.JOIN_CLUB_ERROR_FLAG_ERROR);
                    list.add(upmap);
                }
                cmConscustsurveyrecService.updateCheckJoinClubNo(rec, list);
                result = "success";
            } else {
            	if(StringUtils.isBlank(rec.getCheckadvice())){
            		result = "paramError";
            	}else{
            		cmConscustsurveyrecService.updateCheckJoinClubNo(rec, list);
            		result = "success";
            	}
            }
        }

        //推流程
        CounterCheckFileParamVo checkVo=new CounterCheckFileParamVo();

        checkVo.setOperatorNo(getLoginUserId(request));
        checkVo.setCheckAdvice(checkadvice);
        checkVo.setCheckLevelEnum(CounterCheckLevelEnum.getEnum(checkLevel));
        checkVo.setCheckPass(StaticVar.JOIN_CLUB_FLAG_OK.equals(type));
        checkVo.setOrderId(orderId);
        //文件列表 ：
        if(StringUtil.isEmpty(ids)){
            return "emptyFileChaeck";
        }
        List<CounterCheckFileParamVo.FileCheckResultVo> fileResultList =Lists.newArrayList();

        ids = ids.replaceFirst("\\|", "");
        String[] resultArray = ids.split("\\|");
        for (String resultSingle : resultArray) {
            String[]  fileCheckArray=resultSingle.split("`");
            CounterCheckFileParamVo.FileCheckResultVo fileCheckVo=new CounterCheckFileParamVo.FileCheckResultVo();
            fileCheckVo.setFileId(fileCheckArray[0]);
            //1-通过  0-退回
            fileCheckVo.setQualify("1".equals(fileCheckArray[1]));
            if(fileCheckArray.length>2){
                fileCheckVo.setDesc(fileCheckArray[2]);
            }
            fileResultList.add(fileCheckVo);
        }
        checkVo.setFileResultList(fileResultList);
        BaseResponse<String> postEntityByJsonObject = getPostEntityByJsonObject(CrmTradeServerPathConstant.COUNTER_CHECK,
                checkVo,
                new ParameterizedTypeReference<BaseResponse<String>>() {
                });
        if(postEntityByJsonObject.isSuccess()){
            result = "success";
        }

        return result;
    }

    /**
     * 加载入会编辑页面
     *
     * @param request
     * @return String
     */
    @RequestMapping("/editJoinClubApply")
    public String editJoinClubApply(HttpServletRequest request) {
        String appserialno = request.getParameter("appserialno");
        String result = "";
        request.setAttribute("appserialno", appserialno);
        Map<String, String> param = new HashMap<String, String>();
        param.put("appserialno", appserialno);
        CmConscustsurveyrec rec = cmConscustsurveyrecService.getCmConscustsurveyrec(param);
        String conscustno = "";
        String investtype = "";
        if (rec != null) {
            conscustno = rec.getConscustno();
            request.setAttribute("singdate", rec.getSingdate());
            param.clear();
            param.put("appserialno", rec.getAppserialno());
            param.put("isdel", "0");
            List<CmConscustsurveyrecFile> listCmConscustsurveyrecFile = cmConscustsurveyrecFileService.listCmConscustsurveyrecFile(param);
            request.setAttribute("listCmConscustsurveyrecFile", listCmConscustsurveyrecFile);
        }
        if (StringUtil.isNotNullStr(conscustno)) {
//            param.clear();
//            param.put("conscustno", conscustno);
            Conscust cust = conscustService.getConscust(conscustno);
            if (cust != null) {
                investtype = cust.getInvsttype();

                if(StringUtil.isNotNullStr(cust.getIdnoCipher())){
                    cust.setIdno(decryptSingleFacade.decrypt(cust.getIdnoCipher()).getCodecText());
                }
                if(StringUtil.isNotNullStr(cust.getAddrCipher())){
                    cust.setAddr(decryptSingleFacade.decrypt(cust.getAddrCipher()).getCodecText());
                }
                if(StringUtil.isNotNullStr(cust.getEmailCipher())){
                    cust.setEmail(decryptSingleFacade.decrypt(cust.getEmailCipher()).getCodecText());
                }
            }
            request.setAttribute("cust", cust);

            param.clear();
            param.put("surveyserialno", appserialno);
            List<CmConscustsurveyanswer> list = cmConscustsurveyanswerService.listCmConscustsurveyanswer(param);
            Map<String, String> errmap = new HashMap<String, String>();
            Map<String, String> ansmap = new HashMap<String, String>();
            for (CmConscustsurveyanswer obj : list) {
                ansmap.put(obj.getQuestionid(), obj.getAcode());
                if (StaticVar.JOIN_CLUB_ERROR_FLAG_ERROR.equals(obj.getErrorFlag())) {
                    errmap.put(obj.getQuestionid(), StaticVar.JOIN_CLUB_ERROR_FLAG_ERROR);
                }
            }
            request.setAttribute("ansmap", ansmap);
            request.setAttribute("errmap", errmap);
            QueryKycExamDetailRequest req = new QueryKycExamDetailRequest();
            req.setOutletCode("CRM");
            QueryKycExamDetailResponse res = new QueryKycExamDetailResponse();
            if (StaticVar.INVST_TYPE_PERSONAL.equals(investtype)) {
                req.setExamType(ExamType.HIGH_END.getValue());
                req.setUserType(UserTypeEnum.PERSONAL.getValue());
                res = queryKycExamDetailService.queryKycExamDetail(req);
                ExamInfoDomain domain = res.getExamInfoDomain();
                request.setAttribute("domain", domain);
                request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("idtype", cust.getIdtype()));
                request.setAttribute("qcount", domain.getQuestions().size());
                result = "joinclub/editJoinClub";
            } else {
                // 获取机构问卷
                req.setExamType(ExamType.INSTITUTION.getValue());
                req.setUserType(UserTypeEnum.INSTITUTION.getValue());
                res = queryKycExamDetailService.queryKycExamDetail(req);
                ExamInfoDomain domain = res.getExamInfoDomain();
                request.setAttribute("domain", domain);

                // 根据投资者类型不同，展示不同的证件类型
                if (StaticVar.INVST_TYPE_ORG.equals(investtype)) {
                    request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfInstNew", cust.getIdtype()));
                } else if (StaticVar.INVST_TYPE_FUND.equals(investtype)) {
                    request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfFund", cust.getIdtype()));
                }else if (StaticVar.INVST_TYPE_PRODUCT.equals(investtype)) {
                    request.setAttribute("idtypeval", ConstantCache.getInstance().getVal("IDTypesOfProduct", cust.getIdtype()));
                }

                // 设置题目总数
                request.setAttribute("qcount", domain.getQuestions().size());
                result = "joinclub/editJoinOrgClub";
            }
        }
        return result;
    }

    /**
     * 入会作废方法
     *
     * @param request
     * @return String
     */
    @ResponseBody
    @RequestMapping("/invalidjoinclub")
    public String invalidjoinclub(HttpServletRequest request) {
        String result = "";
        String appserialno = request.getParameter("appserialno");
        String txchkflag = request.getParameter("txchkflag");
        if (StringUtil.isNotNullStr(appserialno) && StringUtil.isNotNullStr(txchkflag)) {
            CmConscustsurveyrec cmConscustsurveyrec = new CmConscustsurveyrec();
            cmConscustsurveyrec.setAppserialno(appserialno);
            cmConscustsurveyrec.setTxchkflag(txchkflag);
            User user = (User) request.getSession().getAttribute("loginUser");
            cmConscustsurveyrec.setChecker(user.getUserId());
            String checkdt = DateTimeUtil.getDateFormat(new Date(), "yyyyMMdd");
            String checktm = DateTimeUtil.getDateFormat(new Date(), "HHmmss");
            cmConscustsurveyrec.setCheckdt(checkdt);
            cmConscustsurveyrec.setChecktm(checktm);
            cmConscustsurveyrecService.updateCmConscustsurveyrec(cmConscustsurveyrec);
            result = "success";
        } else {
            result = "paramError";
        }
        return result;
    }

    /**
     * 修改入会申请处理
     *
     * @param request
     * @return String
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/editJoiningApplyDeal")
    public String editJoiningApplyDeal(HttpServletRequest request) throws Exception {
        User user = (User) request.getSession().getAttribute("loginUser");
        String result = "";
        String str = request.getParameter("str");
        str = str.substring(1);
        String[] ques = str.split("\\|");
        String qualifiedinvestor = request.getParameter("qualifiedinvestor");
        String appserialno = request.getParameter("appserialno");
        String delfileids = request.getParameter("delfileids");
        String singdate = request.getParameter("singdate");
        String orderId = request.getParameter("orderId");

        if (StringUtil.isNotNullStr(appserialno)) {
        	Map<String, String> param = new HashMap<String, String>();
            //待删除文件列表
            List<String> deleteFileIdList = Lists.newArrayList();
            if(jodd.util.StringUtil.isNotBlank(delfileids)){
                delfileids = delfileids.replaceFirst("#", "");
                String [] delids = delfileids.split("#");
                for(String delid : delids){
                    deleteFileIdList.add(delid.split("_")[2]);
                }
            }

            EditCmCounterOrderRequest editFileVo=new EditCmCounterOrderRequest();
            editFileVo.setOrderId(orderId);
            editFileVo.setOperatorNo(getLoginUserId(request));
            editFileVo.setDeleteFileIdList(deleteFileIdList);

            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            List<MultipartFile> files = multipartRequest.getFiles("filelist[]");
            //文件处理
            Map<String, List<String>>  fileMap=processCounterFile (files);
            editFileVo.setAddFileMap(fileMap);

            //资料管理系统调用处理
            BaseResponse<String> postEntityForMultiFile = getPostEntityByJsonObject(CrmTradeServerPathConstant.EXECUTE_EDIT,
                    editFileVo,
                    new ParameterizedTypeReference<BaseResponse<String>>(){});

            if(postEntityForMultiFile.isSuccess()){
                param.clear();
                param.put("appserialno", appserialno);
                CmConscustsurveyrec surveyRec = cmConscustsurveyrecService.getCmConscustsurveyrec(param);
                surveyRec.setTxchkflag(StaticVar.JOIN_CLUB_FLAG_NOT);
                surveyRec.setSingdate(singdate);
                surveyRec.setCreator(user.getUserId());
                surveyRec.setAppdt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
                surveyRec.setApptm(DateTimeUtil.PatternDate(new Date(), "HHmmss"));
                List<CmConscustsurveyanswer> listanswers = new ArrayList<CmConscustsurveyanswer>();
                param.clear();
                param.put("surveyserialno", appserialno);
                listanswers = cmConscustsurveyanswerService.listCmConscustsurveyanswer(param);
                String inverstamt = ""; //可投资金额
                String inverstterm = ""; //投资期限
                String inverstaim = "";//投资目的
                for (CmConscustsurveyanswer answer : listanswers) {
                    answer.setErrorFlag(StaticVar.JOIN_CLUB_ERROR_FLAG_OK);
                    if ("0".equals(answer.getQuestionid())) {
                        answer.setAcode(qualifiedinvestor);
                    } else {
                        for (String que : ques) {
                            if (que.subSequence(0, que.indexOf(":")).equals(answer.getQuestionid())) {
                                answer.setAcode(que);
                                break;
                            }
                        }
                    }
                }
                surveyRec.setInverstamt(inverstamt);
                surveyRec.setInverstterm(inverstterm);
                surveyRec.setInverstaim(inverstaim);
                cmConscustsurveyrecService.updateCmConscustsurveyrec(surveyRec, listanswers);

                result = "success";
            }else{
                result = postEntityForMultiFile.getReturnMsg();
            }
        } else {
            result = "paramError";
        }
        return result;
    }


	
    public String getPerAmtStr(String ans) {
        String result = "";
        if ("A".equals(ans)) {
            result = "300-500万元";
        }
        if ("B".equals(ans)) {
            result = "500-1000万元";
        }
        if ("C".equals(ans)) {
            result = "1000万元以上";
        }
        return result;
    }

    public String getPerAimStr(String ans) {
        String result = "";
        if ("A".equals(ans)) {
            result = "资产保值";
        }
        if ("B".equals(ans)) {
            result = "资产稳健增长";
        }
        if ("C".equals(ans)) {
            result = "资产迅速增长";
        }
        return result;
    }

    public String getPerTermStr(String ans) {
        String result = "";
        if ("A".equals(ans)) {
            result = "1年以下";
        }
        if ("B".equals(ans)) {
            result = "1-3年";
        }
        if ("C".equals(ans)) {
            result = "3-5年左右";
        }
        if ("D".equals(ans)) {
            result = "5年以上";
        }
        return result;
    }

    public String getOrgAmtStr(String ans) {
        String result = "";
        if ("A".equals(ans)) {
            result = "1000万元以下";
        }
        if ("B".equals(ans)) {
            result = "1000-2000万元";
        }
        if ("C".equals(ans)) {
            result = "2000-3000万元";
        }
        if ("D".equals(ans)) {
            result = "3000-5000万元";
        }
        if ("E".equals(ans)) {
            result = "5000万元以上";
        }
        return result;
    }

    public String getOrgAimStr(String ans) {
        String result = "";
        if ("A".equals(ans) || "B".equals(ans)) {
            result = "资产保值";
        }
        if ("C".equals(ans) || "D".equals(ans)) {
            result = "资产稳健增长";
        }
        if ("E".equals(ans)) {
            result = "资产迅速增长";
        }
        return result;
    }

    public String getOrgTermStr(String ans) {
        String result = "";
        if ("A".equals(ans)) {
            result = "1年以内";
        }
        if ("B".equals(ans)) {
            result = "1-3年";
        }
        if ("C".equals(ans)) {
            result = "3-5年";
        }
        if ("D".equals(ans)) {
            result = "5-10年";
        }
        if ("E".equals(ans)) {
            result = "10年及以上";
        }
        return result;
    }

    public String getOrgGpsInvest(String ans) {
        String result = "";
        if ("A".equals(ans)) {
            result = "W";
        }
        if ("B".equals(ans)) {
            result = "X";
        }
        if ("C".equals(ans)) {
            result = "Y";
        }
        if ("D".equals(ans)) {
            result = "Y";
        }
        if ("E".equals(ans)) {
            result = "Z";
        }
        return result;
    }

}