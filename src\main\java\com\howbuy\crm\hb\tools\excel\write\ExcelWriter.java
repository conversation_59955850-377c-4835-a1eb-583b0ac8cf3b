/**   
 * @Title: ExcelWriter.java 
 * @Package com.hb.crm.web.util.excel.write 
 * @Description: 公共方法处理excel文件生成
 * <AUTHOR>
 * @date 2016年4月29日 下午2:13:23 
 * @version V1.0   
 */
package com.howbuy.crm.hb.tools.excel.write;

import com.howbuy.crm.hb.tools.excel.util.FundsArgumentUtils;
import jxl.Workbook;
import jxl.write.Label;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import org.apache.commons.beanutils.PropertyUtils;

import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ExcelWriter
 * @Description: 将导入结果写入excel文件
 * <AUTHOR>
 * @date 2016年4月29日 下午2:13:23
 * 
 */

public class ExcelWriter {

	/**
	 * @Title: writeExcel
	 * @Description: 文件写入
	 * @param os
	 *            文件输入流
	 * @param SheetName
	 *            Sheet文件的名称
	 * @param list
	 *            数据List
	 * @param columnName
	 *            列的数组
	 * @param beanProperty
	 *            对象的属性
	 * @throws Exception
	 */

	public static void writeExcel(OutputStream os, String SheetName, int sheetIndex, List<?> list, String[] columnName,
			String[] beanProperty) throws Exception {
		// 建立excel文件
		WritableWorkbook book = Workbook.createWorkbook(os);
		// 创建excelSheet页
		if(book != null){
			WritableSheet sheet = book.createSheet(SheetName, sheetIndex);
			Label label = null;
			// 增加excel头
			for (int i = 0; i < columnName.length; i++) {
				label = new Label(i, 0, columnName[i]);
				sheet.addCell(label);
				sheet.setColumnView(i, 9);
			}
			// 生成excel数据集合
			if (list != null) {
				int x = 1;
				for (int j = 0; j < list.size(); j++) {
					Object info = list.get(j);
					addRow(x, info, beanProperty, sheet);
					x++;
				}
			}
			// 将主体内容写入文件
			book.write();
			if (book != null) {
				book.close();
			}
		}
	}

	/**
	 * 解决当list数据超出65535时excel会导出失败
	 * 
	 * @Title: writeExcel
	 * @Description: 文件写入
	 * @param os
	 *            文件输入流
	 * @param SheetName
	 *            Sheet文件的名称
	 * @param list
	 *            数据List
	 * @param columnName
	 *            列的数组
	 * @param beanProperty
	 *            对象的属性
	 * @throws Exception
	 */

	public static void writeExcelList(OutputStream os, String SheetName, int sheetIndex, List<?> list,
			String[] columnName, String[] beanProperty) throws Exception {

		// 建立excel文件
		WritableWorkbook book = Workbook.createWorkbook(os);
		if(book != null){
			if (list != null && list.size() > 65535) {
				int count = 0;// 0-65535
	
				sheetIndex = 0;
				// 创建excelSheet页
				WritableSheet sheet = book.createSheet(SheetName, sheetIndex);
	
				Label label = null;
				// 增加excel头
				for (int i = 0; i < columnName.length; i++) {
					label = new Label(i, 0, columnName[i]);
					sheet.addCell(label);
					sheet.setColumnView(i, 9);
				}
				// 生成excel数据集合
				if (list != null) {
					int x = 1;
					for (int j = 0; j < list.size(); j++) {
						count++;
						if (count > 65535) {
							// 创建excelSheet页
							sheetIndex++;
							sheet = book.createSheet(SheetName + sheetIndex, sheetIndex);
							count = 1;
							x = 1;
						}
						Object info = list.get(j);
						addRow(x, info, beanProperty, sheet);
						x++;
					}
				}
	
			} else {
				// 创建excelSheet页
				WritableSheet sheet = book.createSheet(SheetName, sheetIndex);
	
				Label label = null;
				// 增加excel头
				for (int i = 0; i < columnName.length; i++) {
					label = new Label(i, 0, columnName[i]);
					sheet.addCell(label);
					sheet.setColumnView(i, 9);
				}
				// 生成excel数据集合
				if (list != null) {
					int x = 1;
					for (int j = 0; j < list.size(); j++) {
						Object info = list.get(j);
						addRow(x, info, beanProperty, sheet);
						x++;
					}
				}
			}
			// 将主体内容写入文件
			book.write();
			if (book != null) {
				book.close();
			}
		}

	}

	public static void writeExcel(WritableWorkbook book, String SheetName, int sheetIndex, List<?> list,
			String[] columnName, String[] beanProperty) throws Exception {
		// 创建excelSheet页
		WritableSheet sheet = book.createSheet(SheetName, sheetIndex);

		Label label = null;
		// 增加excel头
		for (int i = 0; i < columnName.length; i++) {
			label = new Label(i, 0, columnName[i]);
			sheet.addCell(label);
			sheet.setColumnView(i, 9);
		}
		// 生成excel数据集合
		if (list != null) {
			int x = 1;
			for (int j = 0; j < list.size(); j++) {
				Object info = list.get(j);
				addRow(x, info, beanProperty, sheet);
				x++;
			}
		}

	}

	/**
	 * @Title: addRow
	 * @Description: 向excel行中写入文件
	 * @param x
	 *            行数
	 * @param info
	 * @param beanProperty
	 * @param sheet
	 * @throws Exception
	 */

	private static void addRow(int x, Object info, String[] beanProperty, WritableSheet sheet) throws Exception {
		Label label = null;
		if (info instanceof Map) {
			for (int j = 0; j < beanProperty.length; j++) {
				// 保险类别（）
				if ("cplb".equals(beanProperty[j])) {
					label = new Label(j, x, FundsArgumentUtils.chartToChinese(FundsArgumentUtils.INSURANCE_TYPE_STATUS,
							String.valueOf(PropertyUtils.getProperty(info, beanProperty[j]))));
					// s收入日期判断
				} else if ("srrqpd".equals(beanProperty[j])) {
					label = new Label(j, x, FundsArgumentUtils.chartToChinese(FundsArgumentUtils.INCOME_DATE_STATUS,
							String.valueOf(PropertyUtils.getProperty(info, beanProperty[j]))));
					// 基金类型
				} else if ("jjlx".equals(beanProperty[j])) {
					label = new Label(j, x, FundsArgumentUtils.chartToChinese(FundsArgumentUtils.FUNDS_TYPE_STATUS,
							String.valueOf(PropertyUtils.getProperty(info, beanProperty[j]))));
					// 基金类型
				} else if ("jjfl".equals(beanProperty[j])) {
					label = new Label(j, x, FundsArgumentUtils.chartToChinese(FundsArgumentUtils.FUNDS_TYPE_STATUS,
							String.valueOf(PropertyUtils.getProperty(info, beanProperty[j]))));
					// 是否独立核算
				} else if ("dlhs".equals(beanProperty[j])) {
					label = new Label(j, x, FundsArgumentUtils.chartToChinese(FundsArgumentUtils.IA_STATUS,
							String.valueOf(PropertyUtils.getProperty(info, beanProperty[j]))));
				} else {
					label = new Label(j, x, String.valueOf(PropertyUtils.getProperty(info, beanProperty[j])==null?"": PropertyUtils.getProperty(info, beanProperty[j])));
				}
				sheet.addCell(label);
			}
		} else {
			for (int j = 0; j < beanProperty.length; j++) {
				// 保险类别（）
				if ("cplb".equals(beanProperty[j])) {
					label = new Label(j, x, FundsArgumentUtils.chartToChinese(FundsArgumentUtils.INSURANCE_TYPE_STATUS,
							String.valueOf(PropertyUtils.getProperty(info, beanProperty[j]))));
					// s收入日期判断
				} else if ("srrqpd".equals(beanProperty[j])) {
					label = new Label(j, x, FundsArgumentUtils.chartToChinese(FundsArgumentUtils.INCOME_DATE_STATUS,
							String.valueOf(PropertyUtils.getProperty(info, beanProperty[j]))));
					// 基金类型
				} else if ("jjlx".equals(beanProperty[j])) {
					label = new Label(j, x, FundsArgumentUtils.chartToChinese(FundsArgumentUtils.FUNDS_TYPE_STATUS,
							String.valueOf(PropertyUtils.getProperty(info, beanProperty[j]))));
					// 基金类型
				} else if ("jjfl".equals(beanProperty[j])) {
					label = new Label(j, x, FundsArgumentUtils.chartToChinese(FundsArgumentUtils.FUNDS_TYPE_STATUS,
							String.valueOf(PropertyUtils.getProperty(info, beanProperty[j]))));
					// 是否独立核算
				} else if ("dlhs".equals(beanProperty[j])) {
					label = new Label(j, x, FundsArgumentUtils.chartToChinese(FundsArgumentUtils.IA_STATUS,
							String.valueOf(PropertyUtils.getProperty(info, beanProperty[j]))));
				} else {
					label = new Label(j, x, String.valueOf((PropertyUtils.getProperty(info, beanProperty[j]) == null
							? "" : PropertyUtils.getProperty(info, beanProperty[j]))));
				}
				sheet.addCell(label);
			}
		}
	}

}
