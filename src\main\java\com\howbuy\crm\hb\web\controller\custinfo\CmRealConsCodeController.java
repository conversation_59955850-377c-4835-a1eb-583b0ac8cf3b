/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.web.controller.custinfo;

import com.howbuy.crm.base.response.BaseResponse;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.domain.custinfo.CmRealConsultant;
import com.howbuy.crm.hb.domain.custinfo.CmRealConsultantVo;
import com.howbuy.crm.hb.service.custinfo.CmRealConsultantService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.domain.User;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.db.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;


/**
 * <AUTHOR>
 * @description: (实际投顾客户管理 Controller)
 * @date 2023/4/4 15:46
 * @since JDK 1.8
 */
@Slf4j
@Controller
@RequestMapping(value = "/conscust")
public class CmRealConsCodeController {

    @Autowired
    private CmRealConsultantService cmRealConsultantService;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    /**
     * 跳转到实际投顾客户管理页面
     *
     * @return
     */
    @GetMapping("/listcmrealconsultant.do")
    public String listcmrealconscode() {
        return "custinfo/realConsultant";
    }

    /**
     * @param vo
     * @return crm.howbuy.base.db.PageResult<com.howbuy.crm.hb.domain.custinfo.CmRealConsultantVo>
     * @description:(分页查询数据)
     * @author: xfc
     * @date: 2023/4/10 10:25
     * @since JDK 1.8
     */
    @PostMapping("/querycmrealconultant")
    @ResponseBody
    public PageResult<CmRealConsultantVo> querycmrealconultant(CmRealConsultantVo vo) throws Exception {
        // 返回查询结果
        PageResult<CmRealConsultantVo> pageData = new PageResult<>();
        List<CmRealConsultantVo> cmRealConsultantVoList = new ArrayList<>();
        PageResult<CmRealConsultant> cmRealConsultantPageResult = new PageResult<>();
        try {
            cmRealConsultantPageResult = cmRealConsultantService.listCmRealConsultantByPage(vo);
            ConsOrgCache orgcache = ConsOrgCache.getInstance();

            for (CmRealConsultant cmRealConsultant : cmRealConsultantPageResult.getRows()) {
                // 获取 名义/实际 维护投顾的相关信息
                CmRealConsultantVo cmRealConsultantVo = new CmRealConsultantVo();
                getConsultantInfo(orgcache, cmRealConsultantVo, cmRealConsultant);
                cmRealConsultantVoList.add(cmRealConsultantVo);
            }
        } catch (Exception e) {
            log.error("查询出错", e);
        }
        pageData.setRows(cmRealConsultantVoList);
        pageData.setTotal(cmRealConsultantPageResult.getTotal());
        return pageData;
    }

    /**
     * @param vo
     * @return com.howbuy.crm.base.response.BaseResponse
     * @description:(保存实际修改投顾数据)
     * @author: xfc
     * @date: 2023/4/10 10:26
     * @since JDK 1.8
     */
    @PostMapping("/saverealconsultant")
    @ResponseBody
    public BaseResponse saverealconsultant(@RequestBody CmRealConsultantVo vo, HttpServletRequest request) {
        BaseResponse baseResponse = new BaseResponse();
        User user = (User) request.getSession().getAttribute("loginUser");
        try {
            // 判断该客户是否已经配置过投顾数据
            CmRealConsultantVo queryVo = new CmRealConsultantVo();
            queryVo.setConscustno(vo.getConscustno());
            PageResult<CmRealConsultant> cmRealConsultantPageResult = cmRealConsultantService.listCmRealConsultantByPage(queryVo);
            if (!cmRealConsultantPageResult.getRows().isEmpty()) {
                baseResponse.paramError("该客户已存在实际维护投顾，请在原数据上修改！");
                return baseResponse;
            }
            if (vo.getRealConscode().equals(vo.getConscode())) {
                baseResponse.paramError("名义维护投顾、实际维护投顾不可为同一人！");
                return baseResponse;
            }

            CmRealConsultant cmRealConsultant = new CmRealConsultant();
            BeanUtils.copyProperties(vo, cmRealConsultant);
            // 根据客户号查询客户信息 获取客户姓名和客户的一账通账号
            QueryConscustInfoRequest queryConscustInfoRequest = new QueryConscustInfoRequest();
            queryConscustInfoRequest.setConscustno(vo.getConscustno());
            QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);

            cmRealConsultant.setCustname(queryConscustInfoResponse.getConscustinfo().getCustname());
            cmRealConsultant.setHboneNo(queryConscustInfoResponse.getConscustinfo().getHboneno());
            cmRealConsultant.setModiferTime(new Date());
            cmRealConsultant.setModiferuser(user.getUserId());
            cmRealConsultantService.insert(cmRealConsultant);
        } catch (Exception e) {
            log.error("error in saverealconsultant,{}", e.getMessage());
            baseResponse.paramError("新增操作失败");
            return baseResponse;
        }
        baseResponse.success();
        return baseResponse;
    }

    /**
     * @param request
     * @return java.lang.String
     * @description:(跳转更新页面)
     * @author: xfc
     * @date: 2023/4/10 10:26
     * @since JDK 1.8
     */
    @GetMapping(value = "/updaterealconsultantview")
    public String updateRealConsultantView(HttpServletRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("conscustno", request.getParameter("conscustno"));

        CmRealConsultant cmRealConsultant = cmRealConsultantService.getCmRealConsultant(params);
        CmRealConsultantVo vo = new CmRealConsultantVo();
        BeanUtils.copyProperties(cmRealConsultant, vo);
        ConsOrgCache orgcache = ConsOrgCache.getInstance();

        // 获取 名义/实际 维护投顾的相关信息
        CmRealConsultantVo cmRealConsultantVo = new CmRealConsultantVo();
        getConsultantInfo(orgcache, cmRealConsultantVo, cmRealConsultant);

        request.setAttribute("cmRealConsultant", cmRealConsultantVo);
        return "/custinfo/updaterealConsultant";
    }

    @GetMapping(value = "/addrealconsultantview")
    public String addRealConsultantView() {
        return "/custinfo/addCmRealConsCode";
    }


    /**
     * @param vo
     * @return com.howbuy.crm.base.response.BaseResponse
     * @description:(更新实际维护投顾数据)
     * @author: xfc
     * @date: 2023/4/10 10:27
     * @since JDK 1.8
     */
    @PostMapping("/updaterealconsultant")
    @ResponseBody
    public BaseResponse updateRealConsultant(@RequestBody CmRealConsultantVo vo) {
        BaseResponse baseResponse = new BaseResponse();
        vo.setModiferTime(new Date());
        try {
            cmRealConsultantService.updateCmRealConsultant(vo);
        } catch (Exception e) {
            log.error("error in updateRealConsultant,{}", e.getMessage());
            baseResponse.paramError("保存失败");
            return baseResponse;
        }
        baseResponse.success();
        return baseResponse;
    }

    /**
     * @param request
     * @return com.howbuy.crm.base.response.BaseResponse
     * @description:(删除实际维护投顾)
     * @author: xfc
     * @date: 2023/4/10 10:27
     * @since JDK 1.8
     */
    @PostMapping("/deleterealconsultant")
    @ResponseBody
    public String updateRealConsultant(HttpServletRequest request) {
        String result = "success";
        String conscustno = request.getParameter("conscustno");
        try {
            cmRealConsultantService.deleteCmRealConsultant(conscustno);
        } catch (Exception e) {
            log.error("error in updateRealConsultant,{}", e.getMessage());
            result = "error";
            return result;
        }
        return result;
    }


    /**
     * @description:(获取投顾的信息)
     * @author: xfc
     * @date: 2023/4/7 10:49
     * @since JDK 1.8
     */
    private void getConsultantInfo(ConsOrgCache orgcache, CmRealConsultantVo cmRealConsultantVo, CmRealConsultant cmRealConsultant) {
        // 名义投顾部门
        String orgCode = orgcache.getCons2OutletMap().get(cmRealConsultant.getConscode());
        String oroCodeName = orgcache.getAllOrgMap().get(orgCode);
        cmRealConsultantVo.setOrgcode(oroCodeName);
        cmRealConsultantVo.setOrgcodevalue(orgCode);

        cmRealConsultantVo.setConscodevalue(cmRealConsultant.getConscode());
        cmRealConsultantVo.setRealConscodevalue(cmRealConsultant.getRealConscode());

        // 名义投顾中心
        String upCenterCode = orgcache.getAllOrgMap().get(orgcache.getUpCenterMapCache().get(orgCode));
        cmRealConsultantVo.setOrgcenter(upCenterCode);
        // 名义投顾区域
        String uporgcode = orgcache.getUpOrgMapCache().get(orgCode);
        cmRealConsultantVo.setOrgname(orgcache.getAllOrgMap().get(uporgcode));
        // 名义所属投顾
        cmRealConsultantVo.setConscode(orgcache.getAllUserMap().get(cmRealConsultant.getConscode()));

        // 实际投顾部门
        String realOrgCode = orgcache.getCons2OutletMap().get(cmRealConsultant.getRealConscode());
        String realOrgCodeName = orgcache.getAllOrgMap().get(realOrgCode);
        cmRealConsultantVo.setRealorgcode(realOrgCodeName);
        cmRealConsultantVo.setRealorgcodevalue(realOrgCode);
        // 实际投顾中心
        String realupCenterCode = orgcache.getAllOrgMap().get(orgcache.getUpCenterMapCache().get(realOrgCode));
        cmRealConsultantVo.setRealorgcenter(realupCenterCode);
        // 实际投顾区域
        String realuporgcode = orgcache.getUpOrgMapCache().get(realOrgCode);
        cmRealConsultantVo.setRealorgname(orgcache.getAllOrgMap().get(realuporgcode));
        // 实际所属投顾
        cmRealConsultantVo.setRealConscode(orgcache.getAllUserMap().get(cmRealConsultant.getRealConscode()));

        if (null != cmRealConsultant.getHboneNo()) {
            cmRealConsultantVo.setHboneNo(cmRealConsultant.getHboneNo());
        }
        if (null != cmRealConsultant.getCustname()) {
            cmRealConsultantVo.setCustname(cmRealConsultant.getCustname());
        }
        if (null != cmRealConsultant.getConscustno()) {
            cmRealConsultantVo.setConscustno(cmRealConsultant.getConscustno());
        }
        cmRealConsultantVo.setRemarks(cmRealConsultant.getRemarks());

    }


}