package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.base.*;
import com.howbuy.crm.base.hwdealorder.HwDealOrderStatusEnum;
import com.howbuy.crm.base.hwdealorder.HwSubmitStatusEnum;
import com.howbuy.crm.base.model.CenterOrgEnum;
import com.howbuy.crm.consultant.dto.ConsultantSimpleInfoDto;
import com.howbuy.crm.consultant.service.ConsultantInfoService;
import com.howbuy.crm.hb.domain.custinfo.CmCustconstant;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.hkconscust.HkConscustVO;
import com.howbuy.crm.hb.domain.prosale.PreBookWithSaleControlVo;
import com.howbuy.crm.hb.domain.prosale.PrebookWithSaleControlInfo;
import com.howbuy.crm.hb.domain.prosale.Prebookproductinfo;
import com.howbuy.crm.hb.service.custinfo.CmCustconstantService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.outerservice.CrmAccountOuterService;
import com.howbuy.crm.hb.service.prosale.PreControlHbService;
import com.howbuy.crm.hb.service.prosale.PrebookByOtherService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.web.controller.counter.BaseCounterController;
import com.howbuy.crm.hb.web.dto.ComboboxItem;
import com.howbuy.crm.hb.web.service.prebook.PrebookCommonService;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prebook.dto.CmHwOrderInfo;
import com.howbuy.crm.prebook.dto.CmPreBookProductInfo;
import com.howbuy.crm.prebook.request.PrebookTradeDealRequest;
import com.howbuy.crm.prebook.service.*;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import com.howbuy.crm.prosale.dto.CustHoldFund;
import com.howbuy.crm.prosale.request.GetCustHoldFundRequest;
import com.howbuy.crm.prosale.request.GetInfoByParamRequest;
import com.howbuy.crm.prosale.request.UpdatePrebookInfoRequest;
import com.howbuy.crm.prosale.response.GetCustHoldFundResponse;
import com.howbuy.crm.prosale.response.GetPreBalanceVolResponse;
import com.howbuy.crm.prosale.service.PayOutService;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import com.howbuy.crm.syncfund.dto.FdbXtsylMid;
import com.howbuy.crm.syncfund.service.SyncFundAttrService;
import com.howbuy.crm.trade.model.counter.po.CmCounterOrder;
import com.howbuy.interlayer.product.model.HighProductLockWhiteListCrmModel;
import com.howbuy.interlayer.product.service.HighProductForCrmService;
import com.howbuy.simu.dto.base.product.SubJjxxDto;
import com.howbuy.simu.dto.business.product.RmbhlzjjDto;
import com.howbuy.simu.service.base.product.SmxxService;
import com.howbuy.simu.service.business.product.ComprehensiveService;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancedtl.QueryAcctBalanceDtlResponse;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: Controller
 * <AUTHOR> @version 1.0
 * @created 
 */
@Slf4j
@Controller
@RequestMapping(value = "/prosale")
public class PrebookByOtherController extends BaseCounterController {

	@Autowired
	private PrebookproductinfoService prebookproductinfoService;

	@Autowired
	private ConscustService conscustService;

	@Autowired
    private QueryPreBookService queryPreBookService;

    @Autowired
    private PreBookService preBookService;

	@Autowired
	private ComprehensiveService comprehensiveService;


	@Autowired
	private SyncFundAttrService syncFundAttrService;

	@Autowired
    private HighProductForCrmService highProductForCrmService;

	@Autowired
	private ConsultantInfoService consultantInfoService;
	
	@Autowired
    private PageVisitLogService pageVisitLogService;
	
	@Autowired
	private DecryptSingleFacade decryptSingleFacade;

    @Autowired
    private QueryAcctBalanceDtlFacade queryAcctBalanceDtlFacade;


	@Autowired
	private CmCustconstantService cmCustconstantService;

    @Autowired
    private PreControlHbService preControlHbService;

    @Autowired
	private PayOutService payOutService;

	@Autowired
	private SmxxService smxxService;


	@Autowired
	private JjxxInfoService jjxxInfoService;
	@Autowired
	private PrebookCommonService prebookCommonService;

	@Autowired
	private PrebookTradeDealService prebookTradeDealService;

	@Autowired
	private PrebookBasicInfoService prebookBasicInfoService;


	@Autowired
	private CrmAccountOuterService crmAccountOuterService;

	@Autowired
	private HkOrderQueryService hkOrderQueryService;

	@Autowired
	private PrebookConfigService prebookConfigService;

	@Autowired
	private PrebookBusinessService prebookBusinessService;

	@Autowired
	private PrebookByOtherService prebookByOtherService;


	/**
	 * 预约管理菜单的menuCode
	 */
	private static final String PREBOOK_MANAGE_MENU_CODE = "B030101";


	/**
	 * 跳转到产品预约管理页面方法
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listPreByOther")
	public ModelAndView listPreByOther(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();

		ConsultantSimpleInfoDto simpleInfoDto = consultantInfoService.getInfoByconsCode(getLoginUserId(request));
		String centerOrgCode = simpleInfoDto.getCenterOrgCode();
		if (CenterOrgEnum.IC.getCode().contains(centerOrgCode)) {
			modelAndView.addObject("isIc", true);
		}
		//常规产品广度
		HttpSession session=request.getSession();
		String topcpdata = (String) session.getAttribute("topcpdata");
		modelAndView.addObject("topcpdata", topcpdata);
		// 是否是[香港运营]角色
		modelAndView.addObject("isXgOperator", prebookCommonService.isXgOperator(request, PREBOOK_MANAGE_MENU_CODE));

		modelAndView.setViewName("/prosale/listPreByOther");
		return modelAndView;
	}

	/**
	 * 加载页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/listPreByOtherByPage_json.do")
	public Map<String, Object> listPreByOtherByPage(HttpServletRequest request) throws Exception {
		long startTimestamp = System.currentTimeMillis();
		log.info("listPreByOtherByPage_json.do|预约管理查询开始");

		PreBookWithSaleControlVo vo = buildParam(request);
		long timestamp1 = System.currentTimeMillis();
		log.info("listPreByOtherByPage_json.do|构建查询条件耗时：{}", timestamp1 - startTimestamp);

		Map<String,String> allAmtVol = prebookproductinfoService.getAllAmtVolByVo(vo);
		long timestamp2 = System.currentTimeMillis();
		log.info("listPreByOtherByPage_json.do|查询总金额总份额耗时：{}", timestamp2 - timestamp1);

		PageData<PrebookWithSaleControlInfo> pageData = prebookproductinfoService.listPrebookInfoByPage(vo);
		long timestamp3 = System.currentTimeMillis();
		log.info("listPreByOtherByPage_json.do|预约管理分页查询耗时：{}", timestamp3 - timestamp2);

		Map<String, Object> resultMap = new HashMap<>(3);
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<PrebookWithSaleControlInfo> listdata = pageData.getListData();
		BigDecimal currAmt = BigDecimal.ZERO;
		BigDecimal currRealAmt = BigDecimal.ZERO;
		BigDecimal currVol = BigDecimal.ZERO;
		String allAmtStr = "0.00";
		String allRealAmtStr = "0.00";
		String allVolStr = "0.000000";
		if(allAmtVol != null){
			allAmtStr = StringUtil.isNotNullStr(allAmtVol.get("ALLAMT")) ? allAmtVol.get("ALLAMT") : "0.00";
			allRealAmtStr  = StringUtil.isNotNullStr(allAmtVol.get("ALLREALAMT")) ? allAmtVol.get("ALLREALAMT") : "0.00";
			allVolStr = StringUtil.isNotNullStr(allAmtVol.get("ALLVOL")) ? allAmtVol.get("ALLVOL") : "0.000000";
		}
		Set<String> rateSet = new HashSet<>();
		for (PrebookWithSaleControlInfo info : listdata) {
			//累加购买金额
			if(StaticVar.PREBOOK_TRADE_TYPE_BUY.equals(info.getTradeType()) || StaticVar.PREBOOK_TRADE_TYPE_APPEND.equals(info.getTradeType())){
				if(info.getBuyamtrmb() != null){
					currAmt = currAmt.add(info.getBuyamtrmb());
				}else{
					currAmt = currAmt.add(info.getBuyamt());
				}
				if(info.getRealpayamtrmb() != null){
					currRealAmt = currRealAmt.add(info.getRealpayamtrmb());
				}else{
					if(info.getRealpayamt() != null){
						currRealAmt = currRealAmt.add(info.getRealpayamt());
					}
				}
			}else{
				if(info.getSellvol() != null){
					currVol = currVol.add(info.getSellvol());
				}
			}
			//设置参数
			log.info("预约管理设置参数开始");
			setConstant(info);
			log.info("预约管理设置参数结束");
			//获取非人民币对应的汇率
			if(!StringUtils.equals(PreBookTradeTypeEnum.SALE.getCode(), info.getTradeType()) && StringUtils.isNotBlank(info.getCurrency()) && !StaticVar.CURRENCY_RMB.equals(info.getCurrency())){
				String currencyName = ConstantCache.getInstance().getConstantKeyVal("currencys").get(info.getCurrency());
				RmbhlzjjDto rmbhlzjj = comprehensiveService.getRmbhlzjj(null, info.getCurrency());
				String currencyDesc = String.format("1%s=%s人民币", currencyName, rmbhlzjj.getZjj());
				rateSet.add(currencyDesc);
			}
		}
        // [是否可线上下单]
		setOnlineOrder(listdata);


		long timestamp4 = System.currentTimeMillis();
		log.info("listPreByOtherByPage_json.do|预约管理设置参数耗时：{}", timestamp4 - timestamp3);

		//对购买和追加的数据查询是否在白名单中
		setLockWhiteList(listdata);
		long timestamp5 = System.currentTimeMillis();
		log.info("listPreByOtherByPage_json.do|预约管理白名单查询耗时：{}", timestamp5 - timestamp4);

		String  resultStr = "按当前页：预约总金额："+bigdecimalToString(currAmt,"0,000.00")+"元，实际打款金额："+bigdecimalToString(currRealAmt,"0,000.00")+"元， 赎回总份额："+bigdecimalToString(currVol,"0,000.000000")+"份；      按查询条件：预约总金额："+allAmtStr+"元，实际打款金额："+allRealAmtStr+"元，总赎回份额："+allVolStr+"份";
		// 汇率说明
		String result = convertSetToString(rateSet);
		resultMap.put("rateDesc", result);
		resultMap.put("resultStr", resultStr);
		resultMap.put("rows", listdata);
		log.info("预约管理查询总耗时：{}", System.currentTimeMillis() - startTimestamp);
		return resultMap;
	}


	/**
	 * 设置是否可线上下单
	 * @param listdata
	 */
	private void setOnlineOrder(List<? extends Prebookproductinfo> listdata){
		//预约Id列表
		List<BigDecimal> preIdList = listdata.stream().map(Prebookproductinfo::getId).collect(Collectors.toList());
		Map<String,String> onlineInfoMap = prebookBusinessService.getOnLineOrderInfoMap(preIdList);
		for(Prebookproductinfo info : listdata){
			// [是否可线上下单]
			String inlineOrder = onlineInfoMap.get(info.getId().toString());
			info.setIslineorder(YesOrNoEnum.YES.getCode().equals(inlineOrder)?YesOrNoEnum.YES.getDescription():YesOrNoEnum.NO.getDescription());
		}
	}

	public static String convertSetToString(Set<String> rateSet) {
		StringBuilder sb = new StringBuilder();
		for (String rate : rateSet) {
			if (sb.length() > 0) {
				sb.append("; ");
			}
			sb.append(rate);
		}
		return sb.toString();
	}

	/**
	 * 设置是否白名单
	 * @param listdata
	 */
	private void setLockWhiteList(List<PrebookWithSaleControlInfo> listdata){
		List<HighProductLockWhiteListCrmModel> listProductWhiteQueryModel = new ArrayList<HighProductLockWhiteListCrmModel>();
		for (PrebookWithSaleControlInfo info : listdata) {
			// 该笔预约的产品为香港产品,是否在白名单：默认为空；
			if (StaticVar.YES.equals(info.getSfxg())) {
				info.setWhiteFlag("--");
				continue;
			}
			if(!StaticVar.PREBOOK_TRADE_TYPE_SALE.equals(info.getTradeType())){
				HighProductLockWhiteListCrmModel productWhiteQueryModel = new HighProductLockWhiteListCrmModel ();
				productWhiteQueryModel.setFundCode(info.getFundcode());
				productWhiteQueryModel.setHboneNo(info.getHboneno());
				listProductWhiteQueryModel.add(productWhiteQueryModel);
				
			}else{
				info.setWhiteFlag("--");
			}
		}
		log.info("highProductForCrmService.queryLockWhiteList(listProductWhiteQueryModel)" + JSON.toJSON(listProductWhiteQueryModel));
		List<HighProductLockWhiteListCrmModel> listresturn = highProductForCrmService.queryLockWhiteList(listProductWhiteQueryModel);
		log.info("highProductForCrmService.queryLockWhiteList(listProductWhiteQueryModel)" + JSON.toJSON(listresturn));
		if(listresturn != null && listresturn.size() > 0){
			for (PrebookWithSaleControlInfo info : listdata) {
				if(!StaticVar.PREBOOK_TRADE_TYPE_SALE.equals(info.getTradeType())){
					//在一账通号为空的情况，直接跳过
					if(StringUtil.isNotNullStr(info.getHboneno())){
						for(HighProductLockWhiteListCrmModel model : listresturn){
							if(info.getFundcode().equals(model.getFundCode()) && info.getHboneno().equals(model.getHboneNo()) && "1".equals(model.getProcStat())){
								info.setWhiteFlag("是");
							}
						}
					}
					if(StringUtil.isNullStr(info.getWhiteFlag())){
						info.setWhiteFlag("否");
					}
				}
			}
		}
		
	}
	
	/**
	 * 设置是否白名单
	 * @param listdata
	 */
	private void setLockWhiteListPre(List<Prebookproductinfo> listdata){
		List<HighProductLockWhiteListCrmModel> listProductWhiteQueryModel = new ArrayList<HighProductLockWhiteListCrmModel>();
		for (Prebookproductinfo info : listdata) {
			if(!StaticVar.PREBOOK_TRADE_TYPE_SALE.equals(info.getTradeType())){
				HighProductLockWhiteListCrmModel productWhiteQueryModel = new HighProductLockWhiteListCrmModel ();
				productWhiteQueryModel.setFundCode(info.getFundcode());
				productWhiteQueryModel.setHboneNo(info.getHboneno());
				listProductWhiteQueryModel.add(productWhiteQueryModel);
				
			}else{
				info.setWhiteFlag("--");
			}
		}
		log.info("highProductForCrmService.queryLockWhiteList(listProductWhiteQueryModel)" + JSON.toJSON(listProductWhiteQueryModel));
		List<HighProductLockWhiteListCrmModel> listresturn = highProductForCrmService.queryLockWhiteList(listProductWhiteQueryModel);
		log.info("highProductForCrmService.queryLockWhiteList(listProductWhiteQueryModel)" + JSON.toJSON(listresturn));
		if(listresturn != null && listresturn.size() > 0){
			for (Prebookproductinfo info : listdata) {
				if(!StaticVar.PREBOOK_TRADE_TYPE_SALE.equals(info.getTradeType())){
					//在一账通号为空的情况，直接跳过
					if(StringUtil.isNotNullStr(info.getHboneno())){
						for(HighProductLockWhiteListCrmModel model : listresturn){
							if(info.getFundcode().equals(model.getFundCode()) && info.getHboneno().equals(model.getHboneNo()) && "1".equals(model.getProcStat())){
								info.setWhiteFlag("是");
							}
						}
					}
					if(StringUtil.isNullStr(info.getWhiteFlag())){
						info.setWhiteFlag("否");
					}
				}
			}
		}
		
	}
	
	/**
	 * 格式化数据
	 * @param data
	 * @param style
	 * @return
	 */
	private String bigdecimalToString(BigDecimal data,String style){
    	if(data != null){
    		if(data.compareTo(new BigDecimal("1000")) >= 0){
    			DecimalFormat df = new DecimalFormat();
    			df.applyPattern(style);
    			return df.format(data);
    		}else{
    			DecimalFormat df = new DecimalFormat("#0.00");
    			return df.format(data);
    		}
    	}else{
    		return "";
    	}
    }


	/**
	 * @description:(根据产品代码判断是否分次call。 1-是 0-否)
	 * @param prodCode
	 * @return java.lang.String
	 * @author: haoran.zhang
	 * @date: 2025/5/23 15:44
	 * @since JDK 1.8
	 */
	private String getFcclFlagByProdCode(String prodCode){
		// 是否分次call产品：1-是 0-否
		String isfccall = YesOrNoEnum.NO.getCode();
		ReturnMessageDto<String>  fcclFlagResp=prebookConfigService.getFcclFlag(prodCode);
		if(fcclFlagResp.isSuccess() &&  YesOrNoEnum.YES.getCode().equals(fcclFlagResp.getReturnObject())){
			isfccall = YesOrNoEnum.YES.getCode();
		}
		return isfccall;
	}

	/**
	 * 给对象设置参数
	 * @param info
	 */
	private void setConstant(Prebookproductinfo info){
		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		ConstantCache constantCache = ConstantCache.getInstance();
		info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));
		// 产品类型：1为代销，2为直销，3为直转代
		String sfmsjg = ""; 

		// 设置页面基金编码和基金名称
		if (StringUtils.isNotBlank(info.getFundcode())) {
			JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
			if (jjxx1 != null) {
				info.setFundname(jjxx1.getJjjc());
				sfmsjg = jjxx1.getSfmsjg();
				info.setSfmsjg(sfmsjg);
				info.setSfdzcd(jjxx1.getSfdzcd());
			}
		}
		// 分次call产品取认缴预约的认缴金额，非分次call产品显示“--”
		// 是否分次call产品：1-是 0-否
		String fccl = getFcclFlagByProdCode(info.getFundcode());
		info.setFccl(fccl);
		if(StaticVar.REPEATBUY_YES.equals(info.getSpectradetype())){
			info.setPrebookcheckman("sys");
		}else if(StringUtil.isNotNullStr(consOrgCache.getAllUserMap().get(info.getPrebookcheckman()))){
			info.setPrebookcheckman(consOrgCache.getAllUserMap().get(info.getPrebookcheckman()));
		}
		info.setExpectpayamtdt(info.getExpectpayamtdt());
		info.setRealpayamt(info.getRealpayamt());
		info.setSellvol(info.getSellvol());
		info.setPrebookstateval(constantCache.getConstantKeyVal("prebookStates").get(info.getPrebookstate()));
		info.setPaystateval(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));

		if (StaticVar.YES.equals(info.getSfxg())) {
			ReturnMessageDto<CmHwOrderInfo>  hwOrderResp= hkOrderQueryService.getHwDealOrderByPreId(info.getId());
			CmHwOrderInfo cmHwDealOrder =  hwOrderResp.getReturnObject();
			if (Objects.nonNull(cmHwDealOrder)) {
				// 香港产品[下单渠道]：取预约关联的海外中台订单上的【交易渠道】
				info.setTxchannelval(TradeChannelEnum.getName(cmHwDealOrder.getTradeChannel()));
				// 香港产品[下单状态]：取预约关联的海外中台订单上的【订单状态】，枚举对齐代销
				info.setOrderstateval(HwDealOrderStatusEnum.getDescription(cmHwDealOrder.getOrderStatus()));
				// 香港产品[下单时间]：取预约关联的海外中台订单上的【创建时间】
				info.setOrdertime(DateUtil.date2String(cmHwDealOrder.getCreateTimestamp(), DateUtil.DEFAULT_DATESFM));
			}
			// 香港产品[资产证明状态]：取预约关联的客户的【资产证明有效期】与当前时间进行比较  若【资产证明有效期】≥【当前服务器时间】，则展示“有效”
			HkConscustVO hkConscustVO = crmAccountOuterService.queryHkCustInfoByCustNo(info.getConscustno());
			if (Objects.nonNull(hkConscustVO) && StringUtils.isNotBlank(hkConscustVO.getAssetCertExpiredDate())) {
				String nowDate = DateUtil.date2String(new Date(), DateUtil.SHORT_DATEPATTERN);
				if (nowDate.compareTo(hkConscustVO.getAssetCertExpiredDate()) <= 0) {
					info.setZczmstate("有效");
				} else {
					info.setZczmstate("无效");
				}
			}
		} else {
			// [下单渠道]
			info.setTxchannelval(constantCache.getConstantKeyVal("tradeChans").get(info.getTxchannel()));
			// [下单状态]
			info.setOrderstateval(constantCache.getConstantKeyVal("dealorderStatus").get(info.getOrderstate()));
			// [资产证明状态]
			info.setZczmstate(prebookproductinfoService.getZczmstate(info.getHboneno(), info.getFundcode(), info.getConscustno()));
		}

		if(info.getDiscountstate() == null){
			info.setDiscountstate("1");
		}
		info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
		info.setHmcpx(constantCache.getConstantKeyVal("jjxxhmcpx").get(info.getHmcpx()));
		info.setPretypeval(constantCache.getConstantKeyVal("pretype").get(info.getPretype()));
		info.setTradestateval(constantCache.getConstantKeyVal("tradestate").get(info.getTradestate()));
		info.setRemarks(Util.ObjectToString(info.getRemarks()));
		info.setConsname(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
		info.setOutletName(consOrgCache.getOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(info.getCreator())));
		ConsultantSimpleInfoDto consultantSimpleInfoDto = consultantInfoService.getInfoByconsCode(info.getCreator());
		String centerOrgCode = null != consultantSimpleInfoDto ? consultantSimpleInfoDto.getCenterOrgCode() : null;
		info.setCenterOrgName(CenterOrgEnum.getDescription(centerOrgCode));
		info.setSourcetypeVal(constantCache.getConstantKeyVal("custrestype").get(info.getSourcetype()));
		info.setFirstsourceVal(constantCache.getConstantKeyVal("firstrestype").get(info.getFirstsource()));
		info.setLegaldocStatVal(constantCache.getConstantKeyVal("legaldocstat").get(info.getLegaldocStat()));
		String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(info.getCreator()));
		if("0".equals(uporgcode)){
			info.setUporgname(info.getOutletName());
		}else{
			info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
		}

		//翻译 上报状态
		info.setSubmitStatus(HwSubmitStatusEnum.getDescription(info.getSubmitStatus()));
	}

	/**
	 * 构建 查询vo
	 * @param request
	 * @return
	 * @throws Exception
	 */
	private PreBookWithSaleControlVo buildParam(HttpServletRequest request) throws Exception{
		PreBookWithSaleControlVo returnVo=new PreBookWithSaleControlVo();
		returnVo.setCustname(getSqlParam(request.getParameter("custname")));
		returnVo.setPreid(getSqlParam(request.getParameter("preid")));
		returnVo.setHboneno(getSqlParam(request.getParameter("hboneno")));
		returnVo.setConscustno(getSqlParam(request.getParameter("conscustno")));
		returnVo.setCredt(getSqlParam(request.getParameter("credt")));
		returnVo.setPcode(getSqlParam(request.getParameter("fundcode")));
		returnVo.setTradeTypes(getSqlParam(request.getParameter("tradeTypes")));
		returnVo.setPrebookStates(getSqlParam(request.getParameter("prebookStates")));
		returnVo.setPayStates(getSqlParam(request.getParameter("payStates")));
		returnVo.setTradeStates(getSqlParam(request.getParameter("tradeStates")));
		returnVo.setPretype(getSqlParam(request.getParameter("pretype")));
		returnVo.setIsrepeatbuy(getSqlParam(request.getParameter("isrepeatbuy")));
		returnVo.setIsstat(getSqlParam(request.getParameter("isstat")));
		returnVo.setExpecttradebegdt(getSqlParam(request.getParameter("expecttradebegdt")));
		returnVo.setExpecttradeenddt(getSqlParam(request.getParameter("expecttradeenddt")));
		returnVo.setIsOrdered(getSqlParam(request.getParameter("isOrdered")));
		returnVo.setFinMatched(getSqlParam(request.getParameter("finMatched")));


		//销控查询条件：
		returnVo.setIntoControl(getSqlParam(request.getParameter("intoControl")));
		//页面-->业务处理
		String occupyString=getSqlParam(request.getParameter("occupyType"));
		if("none".equals(occupyString)){
            //标识：未占位
			returnVo.setOccupyStatus(PreOccupyStatusEnum.NOT_OCCUPY.getCode());
		}else{
			returnVo.setOccupyType(occupyString);
		}


        returnVo.setSendQuota(getSqlParam(request.getParameter("sendQuota")));

		// 设置查询条件（投顾编码）
		String conscode=request.getParameter("conscode");
		String orgcode=request.getParameter("orgcode");
		if (StringUtils.isNotBlank(conscode)) {
			returnVo.setConscode(conscode);
		} else {
			returnVo.setOrgcode(orgcode);
		}
		// 判断常量表中合规标识：true启用，false停用
 		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
 		boolean roleCpFlag = false;
 		if (cacheMap != null && !cacheMap.isEmpty()) {
 			roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
 		}
 		// 判断登录人员的角色中是否包括“合规人员”角色
 		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
 		if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
			returnVo.setHascp("true");
		}
		// 通过Session获取产品广度信息
		String topcpdata = (String) request.getSession().getAttribute("topcpdata");
		returnVo.setTopCpData(topcpdata);
		List<String> gdfxcplist = (List<String>) request.getSession().getAttribute("gdfxcplist");
		if (CollectionUtils.isNotEmpty(gdfxcplist) && gdfxcplist.size() == 1 && gdfxcplist.contains("4")) {
			returnVo.setSfxg("1");
		}

 		
 		//是否定投
 		returnVo.setFixedflag(getSqlParam(request.getParameter("fixedflag")));

        returnVo.setCpfx(getSqlParam(request.getParameter("cpfx")));
        returnVo.setLegaldocStat(getSqlParam(request.getParameter("legaldocStat")));
        returnVo.setLegalDocUploadMethod(getSqlParam(request.getParameter("legalDocUploadMethod")));

        // 查询母子基金
		String isQueryFeederFund = request.getParameter("isQueryFeederFund");
		if (StaticVar.YES.equals(isQueryFeederFund) && StringUtils.isNotBlank(request.getParameter("fundcode"))) {
			String fundCode = request.getParameter("fundcode");
			Set<String> jjdmSet = new HashSet<>();

			List<SubJjxxDto> mjjxxLit = smxxService.getJjxxByZjjdm(Lists.newArrayList(fundCode));
			if (CollectionUtils.isNotEmpty(mjjxxLit) && StringUtils.isNotBlank(mjjxxLit.get(0).getMjjdm())) {
				String mjjdm = mjjxxLit.get(0).getMjjdm();
				List<SubJjxxDto> subJjxxList = smxxService.getSubJjdmsByMjjdm(Lists.newArrayList(mjjdm));
				if (CollectionUtils.isNotEmpty(subJjxxList)) {
					jjdmSet = subJjxxList.stream().map(SubJjxxDto::getJjdm).collect(Collectors.toSet());
				}
			} else {
				List<SubJjxxDto> subJjdmsByMjjdm = smxxService.getSubJjdmsByMjjdm(Lists.newArrayList(fundCode));
				if (CollectionUtils.isNotEmpty(subJjdmsByMjjdm)) {
					jjdmSet = subJjdmsByMjjdm.stream().map(SubJjxxDto::getJjdm).collect(Collectors.toSet());
				}
			}

			jjdmSet.add(fundCode);
			returnVo.setFeederFundCodeList(new ArrayList<>(jjdmSet));
			// !!!注意，这里查收母子基金的条件，则不能再使用pcode的查询条件。
			// 因为 在sql中，查询母子基金 和 查询pcode 是互斥的，一个是pcode = ?，一个是jjdm in (?)
			returnVo.setPcode(null);
		}
		//新增查询条件：上报状态
		returnVo.setSubmitStatus(request.getParameter("submitStatus"));
		//新增查询条件：香港客户号
		returnVo.setHkTxAcctNo(request.getParameter("hkTxAcctNo"));

//      排序和页数
        String page=getSqlParam(request.getParameter("page"));
        String rows=getSqlParam(request.getParameter("rows"));
        String sort=getSqlParam(request.getParameter("sort"));
        String order=getSqlParam(request.getParameter("order"));
        if(page!=null){
            returnVo.setPage(Integer.valueOf(page));
        }
        if(rows!=null){
            returnVo.setRows(Integer.valueOf(rows));
        }
        if(sort!=null){
            returnVo.setSort(sort);
        }
        if(order!=null){
            returnVo.setOrder(order);
        }
		return returnVo;
	}
	
	private String getSqlParam(String str){
		if (StringUtils.isNotBlank(str)) {
			return str;
		} else {
			return null;
		}
	}


	@ResponseBody
	@RequestMapping("/getLinkManByCustNo")
	public void getLinkManByCustNo(HttpServletRequest request,
								   HttpServletResponse response) throws Exception {
		String custNo = request.getParameter("custNo");
		Conscust conscust = conscustService.getConscust(custNo);
		String linkman = "";
		if (conscust != null) {
			if (StringUtil.isNotNullStr(conscust.getLinkman())) {
				linkman = conscust.getLinkman();
			} else {
				linkman = conscust.getCustname();
			}
		}

		response.setContentType("text/html;charset=UTF-8");
		PrintWriter pw = null;
		try {
			pw = response.getWriter();
			pw.print(linkman);
			pw.flush();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (pw != null) {
				pw.close();
			}
		}
	}

	/**
	 * 同步产品成立日
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/syncPrebookFundDate.do")
	public String syncPrebookFundDate(HttpServletRequest request) {
		String result = null;
		String preFundCodes = request.getParameter("preFundCodes");
		if (StringUtils.isNotBlank(preFundCodes)) {
			String[] preFundArray = preFundCodes.split(",");
			Map<String, String> param = new HashMap<String, String>(1);
			for (int i = 0; i < preFundArray.length; i++) {
				param.clear();
				// 需处理的产品
				param.put("fundCode", preFundArray[i]);
//				prebookproductinfoService.signDealZtTradeDt(param);
				// 存储过程改造 PRO_SIGN_DEAL_ZTTRADEDT
				if (!StringUtils.isEmpty(preFundArray[i])) {
					preBookService.proSignDealZtTradeDt(preFundArray[i]);
				}
				if ("0000".equals(param.get("PO_RETCODE"))) {
					log.info(preFundArray[i] + "：产品成立日同步成功！");
				} else {
					log.error(preFundArray[i] + "：产品成立日同步失败，返回信息PO_RETMSG：" + param.get("PO_RETMSG"));
				}
			}
			result = "success";
		} else {
			result = "paramError";
		}
		return result;
	}
	
	/**
	 * 批量修改预约类型
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/updatePrebookPreType.do")
	public String updatePrebookPreType(HttpServletRequest request) {
		String result = null;
		String preIds = request.getParameter("preIds");
		// 预约类型（1：纸质成单；2：电子成单；3：无纸化交易；4：异常流程）
		String pretype = request.getParameter("pretype");
		if (StringUtils.isNotBlank(preIds)) {
			String[] preIdsArray = preIds.split(",");
			List<CmPrebookproductinfo> list = new ArrayList<CmPrebookproductinfo>();
			for (int i = 0; i < preIdsArray.length; i++) {
				Map<String, String> param = new HashMap<String, String>(1);
				param.put("preId", preIdsArray[i]);
				CmCounterOrder order =getValidOrderByForId(preIdsArray[i]);
				if(order!=null){
					return "hasOrder";
				}
//
//				int invalidSize = cmCounterOrderService.listInvalidOrder(param);// 是作废的状态
//				int uploadFileSize = cmCounterOrderService.listUploadFileOrder(param);
//				boolean validFlag = false;
//				if (invalidSize == 1) {
//					validFlag = true;
//				}
//
//				if (uploadFileSize > 0 && !validFlag) {
//					result = "hasOrder";
//					return result;
//				}
				CmPrebookproductinfo info = new CmPrebookproductinfo();
				info.setId(new BigDecimal(preIdsArray[i]));
				// 预约类型
				info.setPretype(pretype);
				list.add(info);
			}
			prebookproductinfoService.updatePrebookListAndDoubleTrade(list);
			result = "success";
		} else {
			result = "paramError";
		}
		return result;
	}

	/**
	 * 修改客户资源类型
	 * @param request
	 * @return String
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/updatePrebookRestype.do")
	public String updatePrebookRestype(HttpServletRequest request) throws Exception {
		String result = "";
		String firstsource = request.getParameter("firstsource");
		String sourcetype = request.getParameter("sourcetype");
		String preid = request.getParameter("preid");
		if (StringUtils.isNotBlank(firstsource) && StringUtils.isNotBlank(sourcetype) && StringUtils.isNotBlank(preid)) {
			CmPrebookproductinfo info = new CmPrebookproductinfo();
			info.setId(new BigDecimal(preid));
			info.setFirstsource(firstsource);
			info.setSourcetype(sourcetype);
			//调接口更新预约信息
	        UpdatePrebookInfoRequest req = new UpdatePrebookInfoRequest();
	        req.setCmPrebookproductinfo(info);
	        BaseResponse res = preBookService.updatePrebookSourceType(req);
	        if(res != null && res.isSuccessful()){
	        	result = "success";
	        }else if(res != null){
	        	result = res.getDescription();
	        }
		} else {
			result = "paramError";
		}
		return result;
	}
	
	/**
	 * 修改客户投顾
	 * @param request
	 * @return String
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/updatePrebookCreator.do")
	public String updatePrebookCreator(HttpServletRequest request) throws Exception{
		String result = "";
		String conscode = request.getParameter("conscode");
		String preid = request.getParameter("preid");
		if (StringUtils.isNotBlank(conscode) && StringUtils.isNotBlank(preid)) {
			User user = getLoginUser();
			CmPrebookproductinfo info = new CmPrebookproductinfo();
			info.setId(new BigDecimal(preid));
			info.setCreator(conscode);
			info.setModifier(user.getUserId());
			info.setModdt(DateTimeUtil.getCurDate());
			//调接口更新预约信息
	        UpdatePrebookInfoRequest req = new UpdatePrebookInfoRequest();
	        req.setCmPrebookproductinfo(info);
	        BaseResponse res = preBookService.updatePrebook(req);
	        if(res != null && res.isSuccessful()){
	        	result = "success";
	        }else if(res != null){
	        	result = res.getDescription();
	        }
		} else {
			result = "paramError";
		}
		return result;
	}
	
	/**
	 * 修改预约关联批次
	 * @param request
	 * @return String
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/updatePrebookSno.do")
	public String updatePrebookSno(HttpServletRequest request) throws Exception{
		String result = "";
		String sno = request.getParameter("sno");
		String preid = request.getParameter("preid");
		if (StringUtils.isNotBlank(sno) && StringUtils.isNotBlank(preid)) {
			User user = getLoginUser();
			String userid = user.getUserId();
			CmPrebookproductinfo info = new CmPrebookproductinfo();
			info.setId(new BigDecimal(preid));
			info.setSno(new BigDecimal(sno));
			info.setModifier(userid);
			info.setModdt(DateTimeUtil.getCurDate());
			//调接口更新预约信息
	        UpdatePrebookInfoRequest req = new UpdatePrebookInfoRequest();
	        req.setCmPrebookproductinfo(info);
	        BaseResponse res = preBookService.updatePrebook(req);
	        if(res != null && res.isSuccessful()){
	        	result = "success";
	        	Map<String,Object> paramopt = new HashMap<String,Object>(4);
        		paramopt.put("preid", preid);
        		paramopt.put("sno", sno);
        		paramopt.put("des", "预约关联修改");
        		paramopt.put("creator", userid);
        		prebookproductinfoService.insertOptPreSno(paramopt);
	        }else if(res != null){
	        	result = res.getDescription();
	        }
		} else {
			result = "paramError";
		}
		return result;
	}
	
	/**
	 * 根据id查找产品预约信息
	 * 
	 * author: wu.long
	 * date: 2019年7月24日 下午3:11:29 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/selectPrebookproductinfoById")
	public CmPrebookproductinfo selectPrebookproductinfoById(HttpServletRequest request){
		CmPrebookproductinfo pInfo = new CmPrebookproductinfo();
		try {
			pInfo = prebookproductinfoService.selectPrebookproductinfoById(request.getParameter("id"));
		} catch (Exception e) {
			log.error("根据id查找产品预约信息异常：", e);
		}
		return pInfo;
	}
	
	/**
	 * 修改备忘信息
	 * 
	 * author: wu.long
	 * date: 2019年7月24日 下午6:27:38 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/updatePreProductNotes")
	public String updatePreProductNotes(HttpServletRequest request){
		String result = "";
		try {
			CmPrebookproductinfo info = new CmPrebookproductinfo();
			info.setId(new BigDecimal(request.getParameter("id")));
			info.setNotes(request.getParameter("notes"));
			//调接口更新预约信息
	        UpdatePrebookInfoRequest req = new UpdatePrebookInfoRequest();
	        req.setCmPrebookproductinfo(info);
	        BaseResponse res = preBookService.updatePrebook(req);
	        if(res != null && res.isSuccessful()){
	        	result = "success";
	        }else if(res != null){
	        	result = res.getDescription();
	        }
		} catch (Exception e) {
			log.error("修改备忘信息异常：", e);
			result = "fail";
		}
		return result;
	}

	/**
	 * 获取 是否进入op
	 * 
	 * author: fangyuan.wu
	 * date: 2020年12月29日 下午6:27:38 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/getProducttype.do")
	public String getProducttype(HttpServletRequest request){
		String pcode = request.getParameter("pcode");
		String conscustno = request.getParameter("custno");
		
		//查询产品是直销还是代销，代销的话就是进入OP，直销和直转代黑名单就是不进入op
		//默认不进入OP
        String producttype = StaticVar.COUNTERISOP_NO;
        if (StringUtils.isNotBlank(pcode)) {
			JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(pcode, false);
            if (StaticVar.SFMSJG_DX.equals(jjxx1.getSfmsjg()) || StaticVar.SFMSJG_ZZD.equals(jjxx1.getSfmsjg())) {
                producttype = StaticVar.COUNTERISOP_YES;
            } /*else if (StaticVar.SFMSJG_ZZD.equals(jjxx1.getSfmsjg())) {
                Map<String, String> paramzzd = new HashMap<String, String>(2);
                paramzzd.put("custno", conscustno);
                paramzzd.put("fundcode", pcode);
                List<Map<String, Object>> listzzd = custprivatefundService.listgetZtInfoFundcode(paramzzd);
                if (listzzd == null || (listzzd != null && listzzd.size() == 0)) {
                    producttype = StaticVar.COUNTERISOP_YES;
                }
            }*/
        }
        
        return producttype;
	}

	/**
	 * 加载备忘操作页面
	 * 
	 * author: wu.long
	 * date: 2019年7月29日 下午1:17:04 
	 * @param request
	 * @return
	 */
	@RequestMapping("/showNotesPage")
	public ModelAndView showNotesPage(HttpServletRequest request) {
		CmPrebookproductinfo pInfo = new CmPrebookproductinfo();
		Map<String, Object> map = new HashMap<String, Object>(2);
		try {
			pInfo = prebookproductinfoService.selectPrebookproductinfoById(request.getParameter("id"));
			map.put("id", request.getParameter("id"));
			map.put("pInfo", pInfo);
		} catch (Exception e) {
			log.error("根据id查找产品预约信息异常：", e);
		}
		return new ModelAndView("prosale/notesPage", "map", map);
	}


	/**
	 * 获取持有份额:
	 * @param request
	* <AUTHOR>
	* @date 2019/8/1
	*/
	@RequestMapping("getCustBalanceByPcode.do")
	@ResponseBody
	public Object getCustBalanceByPcode(HttpServletRequest request){
		String custNo = request.getParameter("custNo");
		String pcode = request.getParameter("pcode");
		//持有份额和市值
		BigDecimal balanceVol = BigDecimal.ZERO;
		GetInfoByParamRequest getInfoByParamRequest = new GetInfoByParamRequest();
		getInfoByParamRequest.setConscustno(custNo);
		getInfoByParamRequest.setFundcode(pcode);
		GetPreBalanceVolResponse balanceVolResponse = queryPreBookService.getPreBalanceVol(getInfoByParamRequest);
		if(balanceVolResponse.isSuccessful()){
			balanceVol = balanceVolResponse.getVol();
		}
		return balanceVol;
	}

	@RequestMapping("/listPayOutRate")
	@ResponseBody
	public Object listPayOutRate(String pCode){
		return payOutService.listPayOutRate(pCode);
	}


	/**
	 * 获取一个客户的持仓产品列表（下拉框数据）:
	 * @param request
	* <AUTHOR>
	* @date 2019/8/1
	*/
	@RequestMapping("/getProductBalanceByCustno.do")
	@ResponseBody
	public Map<String, Object> getProductBalanceByCustno(HttpServletRequest request) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(1);

		String custNo=request.getParameter("custno");
		//不再需要
//		String isfccl=request.getParameter("isfccl");
		String channelType=request.getParameter("channelType");

		GetCustHoldFundRequest req= new GetCustHoldFundRequest();
		req.setCustNo(custNo);
		//PreBookChannelEnum
		req.setPreBookChannelCode(channelType);
		GetCustHoldFundResponse holdFundResponse = queryPreBookService.getCustHoldFundForSell( req);
		List<CustHoldFund> holdFundList = holdFundResponse.getHoldFundList();

		if(CollectionUtils.isNotEmpty(holdFundList)){
			resultMap.put("returnFlag", "success");
			// 通过Session获取产品广度信息
			String topcpdata = (String) request.getSession().getAttribute("topcpdata");
			Map<String,Object> filterMap = filterJgProduct(holdFundList, topcpdata);

			List<Map> productData = new ArrayList<>(holdFundList.size());
			for(CustHoldFund custHoldFund : holdFundList){
				if(filterMap != null && filterMap.containsKey(custHoldFund.getFundcode())){
					continue;
				}
				Map map = new HashMap(2);
				map.put("id",custHoldFund.getFundcode());
				map.put("name",custHoldFund.getFundcode() + " " + custHoldFund.getFundname() + " " + custHoldFund.getPyjc());
				productData.add(map);
			}
			resultMap.put("productData", productData);
		}

		return resultMap;
	}

	/**
	 * 过滤监管的产品
	 */
	private Map<String,Object> filterJgProduct(List<CustHoldFund> list, String topcpdata){
		if("32".equals(topcpdata)) {
			List<String> fundCodes = new ArrayList<>(list.size());
			list.forEach(custHoldFund -> fundCodes.add(custHoldFund.getFundcode()));
			List<String> productFilterJgList = prebookproductinfoService.selectProductFilterJg(fundCodes);
			Map<String, Object> filterMap = CollectionUtils.isEmpty(productFilterJgList) ? null : productFilterJgList.stream().collect(Collectors.toMap(fundCode -> fundCode, fundCode -> 1));
			return filterMap;
		}
		return null;
	}


	/**
	 * 获取一个客户的高端持仓明细列表:
	 * @param custNo NOT NULL
     * @param prodCode NOT NULL
	 * <AUTHOR>
	 * @date 2021-7-5 14:46:43
	 */
	@RequestMapping("/getRedeemBalanceListByCustno.do")
	@ResponseBody
	public List<QueryAcctBalanceDtlResponse.BalanceDtlBean> getRedeemBalanceListByCustno(String custNo, String prodCode) {
		Assert.notNull(custNo);
		Assert.notNull(prodCode);
        QueryAcctBalanceDtlRequest request = newTmsBaseRequest(QueryAcctBalanceDtlRequest.class);
//        request.setTxAcctNo(custNo);
		String hboneNo=getHboneNoByCustNo(custNo);
		request.setHbOneNo(hboneNo);
        request.setProductCode(prodCode);
        QueryAcctBalanceDtlResponse response = queryAcctBalanceDtlFacade.execute(request);
        log.info("QueryAcctBalanceDtlFacade,查询高端持仓明细列表，请求参数：{}，response:{}",
                JSONObject.toJSONString(request),JSONObject.toJSONString(response));
        if(response!=null && CollectionUtils.isNotEmpty(response.getBalanceDtlList())){
            return response.getBalanceDtlList();
        }
        return Lists.newArrayList();
	}
	
	/**
	 * 获取一个客户的持仓产品列表（下拉框数据）:
	 * @param request
	* <AUTHOR>
	* @date 2019/8/1
	*/
	@RequestMapping("/getFdbXtsylSno.do")
	@ResponseBody
	public List<ComboboxItem> getFdbXtsylSno(HttpServletRequest request){
		List<ComboboxItem> xtsylList = Lists.newArrayList();
		List<FdbXtsylMid>  xtsylMidList=syncFundAttrService.selectXtsylList(request.getParameter("fundcode"));
		for(FdbXtsylMid xtsylMid : xtsylMidList){
			xtsylList.add(new ComboboxItem(xtsylMid.getSno().toPlainString(),
							String.join("--",xtsylMid.getTzqsr(),
									StringUtil.replaceNullStr(xtsylMid.getYjdqr()))
					)
			);
		}
		return xtsylList;
	}
	
	/**
	 * 更新中台订单信息
	 * @param request
	 * @param response
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/updateZtOrderInfoByDealno.do")
	public ReturnMessageDto<String>  updateZtOrderInfoByDealno(HttpServletRequest request,
			HttpServletResponse response) {
		String dealno = request.getParameter("dealno");
        return prebookTradeDealService.executeSyncZtOrder(dealno,getLoginUserId());
	}


	/**
	 * @api {POST} /prosale/updatehworderinfobypreid updateHwOrderInfoByPreId()
	 * @apiVersion 1.0.0
	 * @apiGroup PrebookByOtherController
	 * @apiName updateHwOrderInfoByPreId()
	 * @apiDescription 同步海外中台订单信息 到CRM预约单上
	 * @apiParam (请求参数) {String} preId
	 * @apiParamExample 请求参数示例
	 * preId=Wm
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"dWvPCp","returnMsg":"EeSZg8","returnObject":"H6X","returnList":["PFDdhl0Lk9"]}
	 */
	@ResponseBody
	@PostMapping("/updatehworderinfobypreid")
	public ReturnMessageDto<String>  updateHwOrderInfoByPreId(HttpServletRequest request) {
		String preId = request.getParameter("preId");
		return prebookTradeDealService.executeSyncHwOrder(preId,getLoginUserId());
	}


	/**
	 * 导出操作
	 * @param request
	 * @param response
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
    @RequestMapping("/exportPreInfo.do")
    public Map<String, Object> exportPreInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String ids = request.getParameter("preIds");
		String[] pres = ids.split(",");
		//传过来的预约号
        List<String> list = new ArrayList<String>();
        //将数组转list
        CollectionUtils.addAll(list, pres);
        String sqlins = Util.getOracleSQLIn(list,999,"pp.id");
        param.put("ids", sqlins);
 		List<Prebookproductinfo> listdata = prebookproductinfoService.listexportPreInfo(param);
 		
		ConstantCache constantCache = ConstantCache.getInstance();
		List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
		User user = getLoginUser();
		String ip = getIpAddr(request);
		//对购买和追加的数据查询是否在白名单中
		setLockWhiteListPre(listdata);
		HashMap<String,String> sfmsjgmap = constantCache.getConstantKeyVal("sfmsjg");
		for (Prebookproductinfo info : listdata) {
			
			//获取非人民币汇率
			BigDecimal exchangerate = getExchangerate(info);
            //税后折扣金额(人民币)
			if(info.getAfterTaxAmt() != null){
				info.setAfterTaxAmtRmb(info.getAfterTaxAmt().multiply(exchangerate).setScale(2,BigDecimal.ROUND_HALF_UP));
				info.setAfterTaxAmt(info.getAfterTaxAmt().setScale(2,BigDecimal.ROUND_HALF_UP));
			}
			// 当数据预约赎回== 2 时，不显示金额
			if (RedeemModeEnum.AMT.getCode().equals(info.getRedeemMode())) {
				info.setSellamt(null);
			}

			//设置常量参数
			setConstant(info);
			//设置金额和份额
			setAmtVol(info);
			info.setSfmsjg("代销");
			if(StringUtil.isNotNullStr(info.getSfmsjg()) ){
				info.setSfmsjg(sfmsjgmap.get(info.getSfmsjg()));
			}
			
			if(StringUtil.isNotNullStr(info.getSfhwjj()) && "0".equals(info.getSfhwjj())){
				info.setFundtype("海外");
			}
			if(StringUtil.isNotNullStr(info.getSfhwjj()) && "1".equals(info.getSfhwjj())){
				info.setFundtype(info.getHmcpx());
			}
			
			
			if(StringUtil.isNotNullStr(info.getIdnoCipher())){
				CodecSingleResponse  res = decryptSingleFacade.decrypt(info.getIdnoCipher());
				String idno = res.getCodecText();
				info.setIdno(idno);
			}
			info.setFinMatched(YesOrNoEnum.getDescription(info.getFinMatched()));
			//记录访问日志
            PageVisitLog pageVisitLog = new PageVisitLog();
            pageVisitLog.setConscustno(info.getConscustno());
            pageVisitLog.setUserId(user.getUserId());
            pageVisitLog.setVisitUrl(request.getRequestURI());
            pageVisitLog.setOperation("预约管理信息导出");
            pageVisitLog.setVisitTime(new Date());
            pageVisitLog.setPreid(info.getId());
            pageVisitLog.setIp(ip);
            listlog.add(pageVisitLog);
		}

		// [是否可线上下单]
		setOnlineOrder(listdata);


        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("预约信息.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

			String [] columnName = new String[]
					{"预计交易日期","录入时间","客户姓名","投顾客户号",
					"证件号","一账通号","预约时所属投顾","所属部门",
					"所属区域","交易类型","产品名称","产品代码",
					"产品类型","销售类型","是否计入统计","预约类型",
					"预约确认人","预约状态","是否可线上下单","下单渠道",
					"下单状态","下单时间","认缴金额(万)","预约购买金额(万)",
					"打款状态","实际打款日期","实际打款金额(万)","实际打款手续费",
					"支付方式","是否资金匹配","预约赎回金额","赎回份额",
					"赎回至","冷静期截止时间","资产证明状态","折扣状态",
					"上报状态","交易确认状态","是否在白名单","预约ID",
					"第一来源","类型来源","预约创建人","备忘",
					"折扣率","实际打款手续费","实际打款手续费（RMB）","税后折扣金额",
					"税后折扣金额（RMB）","售前留痕材料状态","售前留痕材料上传方式"};
			String [] beanProperty = new String[] {
					"expecttradedt","credt","conscustname","conscustno",
					"idno","hboneno","consname","outletName",
					"uporgname","tradeTypeVal","fundname","fundcode",
					"fundtype","sfmsjg","isstat","pretypeval",
					"prebookcheckman","prebookstateval","islineorder","txchannelval",
					"orderstateval","ordertime","totalamt","buyamt",
					"paystateval","realpayamtdt","realpayamt","realfee",
					"paymenttype","finMatched","sellamt","sellvol",
					"redeemdirection","calmdatetime","zczmstate","discountstateval",
					"submitStatus","tradestateval","whiteFlag","id",
					"firstsourceVal","sourcetypeVal","realcreator","notes",
					"discountRate","realfee","realfeeRmb","afterTaxAmt",
					"afterTaxAmtRmb","legaldocStatVal","legalDocUploadMethodVal"
			      };

            
            ExcelWriter.writeExcel(os, "预约信息", 0, listdata, columnName, beanProperty);
            os.close(); // 关闭流
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }
        for(PageVisitLog log : listlog){
        	pageVisitLogService.recordLog(log);
        }
        resultMap.put("msg", "success");
		return resultMap;
    }


	/**
	 * @api {GET} /prosale/exportAllPreInfo.do exportAllPreInfo()
	 * @apiVersion 1.0.0
	 * @apiGroup PrebookByOtherController
	 * @apiName exportAllPreInfo()
	 * @apiDescription 根据查询条件，导出全部数据（不分页）
	 * @apiSuccess (响应结果) {Object} response
	 * @apiSuccessExample 响应结果示例
	 * {}
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/exportAllPreInfo.do")
	public Map<String, Object> exportAllPreInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
		PreBookWithSaleControlVo vo = buildParam(request);
		return prebookByOtherService.exportAllPreInfo(vo, request, response);
	}


	/**
	 * 设置金额和份额
	 * @param info
	 */
	private void  setAmtVol(Prebookproductinfo info){
		if(info.getRealfee() != null){
			info.setRealfee(info.getRealfee().setScale(2,BigDecimal.ROUND_HALF_UP));
		}
		if(info.getRealfee() != null && info.getBuyamt() != null && info.getBuyamtrmb() != null && info.getBuyamt().compareTo(BigDecimal.ZERO) != 0){
			info.setRealfeeRmb( info.getRealfee().multiply( info.getBuyamtrmb().divide(info.getBuyamt(),4,BigDecimal.ROUND_HALF_UP).setScale(4,BigDecimal.ROUND_HALF_UP) ).setScale(2,BigDecimal.ROUND_HALF_UP) );
		}
		if("1".equals(info.getFccl()) && info.getTotalamt() !=null){
			info.setTotalamt(info.getTotalamt().divide(new BigDecimal(10000)).setScale(6, BigDecimal.ROUND_HALF_UP));
		}
		if(info.getZtbuyamt() != null){
			info.setBuyamt(info.getZtbuyamt().divide(new BigDecimal(10000)).setScale(6, BigDecimal.ROUND_HALF_UP));
		}else if(info.getBuyamt() != null){
			info.setBuyamt(info.getBuyamt().divide(new BigDecimal(10000)).setScale(6, BigDecimal.ROUND_HALF_UP));
		}
		if(info.getRealpayamt() != null){
			info.setRealpayamt(info.getRealpayamt().divide(new BigDecimal(10000)).setScale(6, BigDecimal.ROUND_HALF_UP));
		}

		if(info.getDiscountRate() != null){
			info.setDiscountRate(info.getDiscountRate().setScale(2,BigDecimal.ROUND_HALF_UP));
		}

		if(info.getSellvol() != null){
			info.setSellvol(info.getSellvol().setScale(6, BigDecimal.ROUND_HALF_UP));
		}
	}
	/**
	 * 获取汇率
	 * @param info
	 * @return
	 */
	private BigDecimal getExchangerate(Prebookproductinfo info){
		BigDecimal exchangerate = new BigDecimal("1.0");
        if(info.getBuyamtrmb() != null && !"156".equals(info.getCurrency())){
            exchangerate = info.getBuyamtrmb().divide(info.getBuyamt(),4,BigDecimal.ROUND_UP);
        }else{
            RmbhlzjjDto rmbhlzjjDto = comprehensiveService.getRmbhlzjj(info.getRatedt() == null ? null : DateTimeUtil.strToDate(info.getRatedt()),info.getCurrency());
			if (rmbhlzjjDto != null) {
				exchangerate = new BigDecimal(rmbhlzjjDto.getZjj());
			}
        }
        return exchangerate;
	}
	
	/**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        String unknown = "unknown";
        if (ip == null || ip.length() == 0 || unknown.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

	/**
	 * @description: 获取预约id列表
	 * @param preIdListStr	预约id列表string
	 * @return java.util.List<java.math.BigDecimal> 预约id列表
	 * @author: jin.wang03
	 * @date: 2024/2/23 18:00
	 * @since JDK 1.8
	 */
	private static List<BigDecimal> getPreIdListByStr(String preIdListStr) {
		// 预约id列表
		List<String> list = Arrays.asList(preIdListStr.split(","));
		// 将预约id列表转换为BigDecimal类型
		return list.stream().map(BigDecimal::new).collect(Collectors.toList());
	}


	/**
	 * @api {POST} /prosale/validatebeforelockwhitelist validateBeforeLockWhiteList()
	 * @apiVersion 1.0.0
	 * @apiGroup PrebookByOtherController
	 * @apiName validateBeforeLockWhiteList()
	 * @apiDescription 好买代销白名单的前置校验
	 * @apiParam (请求参数) {String} ids
	 * @apiParamExample 请求参数示例
	 * ids=O
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"A2","returnMsg":"e63TcdUXsv","returnObject":"G5","returnList":["Ysj"]}
	 */
	@ResponseBody
	@PostMapping("/validatebeforelockwhitelist")
	public ReturnMessageDto<String> validateBeforeLockWhiteList(String ids) {
		if (ids == null) {
			return ReturnMessageDto.fail("请选择数据！");
		}
		List<BigDecimal> preIdList = getPreIdListByStr(ids);
		if (isExistHzProduct(preIdList)) {
			return ReturnMessageDto.fail("勾选的预约中有好臻产品，请使用“好臻金额锁定及修改”功能！");
		}
		return ReturnMessageDto.ok();
	}

	/**
	 * 新增白名单
	 * @param request
	 * @param ids 预约Id
	 * @param validateType  白名单校验方式：0-新增全部白名单  1-新增配额白名单 校验
	 * @return
	 */
	@RequestMapping("/addLockWhiteList")
    @ResponseBody
    public String addLockWhiteList(HttpServletRequest request,String ids,String validateType){
        if(ids == null){
            return "paramError";
        }
		List<BigDecimal> preIdList = getPreIdListByStr(ids);
		if (isExistHzProduct(preIdList)) {
			return "勾选的预约中有好臻产品，请使用“好臻金额锁定及修改”功能！";
		}

		Map<String, String> param = new HashMap<>(1);
        param.put("ids", ids);
        List<Map<String,String>> list = prebookproductinfoService.listFundCodeAndHbonenoByPreid(param);
		if (CollectionUtils.isNotEmpty(list)) {
        	List<HighProductLockWhiteListCrmModel> listadd = new ArrayList<>(8);
        	for(Map<String,String> map : list){
        		HighProductLockWhiteListCrmModel model = new HighProductLockWhiteListCrmModel();
        		model.setFundCode(StringUtil.replaceNull(map.get("FUNDCODE")));
        		model.setHboneNo(StringUtil.replaceNull(map.get("HBONENO")));
        		listadd.add(model);
        	}
        	//校验：
			ReturnMessageDto<String> validateDto = preControlHbService.validateWhiteList(preIdList, validateType);
			if(!validateDto.isSuccess()){
				log.error("销控白名单新增：{} 校验临时关闭！，校验结果：{}",JSONObject.toJSONString(preIdList), JSONObject.toJSONString(validateDto));
                 return  validateDto.getReturnMsg();
			}

        	log.info("新增白名单传入参数: " + JSON.toJSONString(listadd));
        	List<HighProductLockWhiteListCrmModel> listreturn = highProductForCrmService.addLockWhiteList(listadd);
        	log.info("新增白名单返回参数: " + JSON.toJSONString(listreturn));
        	if(listreturn != null && listreturn.size() > 0){
        		String startstr = "部分成功，其中";
        		StringBuilder sb = new StringBuilder();
        		sb.append(startstr);
        		boolean allfail = true;
        		for(HighProductLockWhiteListCrmModel returnmodel : listreturn){
        			if("0".equals(returnmodel.getProcStat())){
        				sb.append(returnmodel.getHboneNo()+returnmodel.getFundCode()+",");
        			}else{
        				allfail = false;
        			}
        		}
        		if(allfail){
        			return "添加失败!";
        		}
        		if(!startstr.equals(sb.toString())){
        			return sb.toString().substring(0,sb.toString().length()-1)+"添加失败！";
        		}
        	}else{
        		return "接口返回错误！";
        	}
        }else{
        	return "添加失败，选择的客户没有对应的一账通号！";
        }
        
        return "success";
    }

	/**
	 * @description: 校验勾选的预约单中，是否存在好臻产品
	 * @param preIdList 预约id列表
	 * @return boolean 是否存在好臻产品
	 * @author: jin.wang03
	 * @date: 2024/2/29 18:40
	 * @since JDK 1.8
	 */
	private boolean isExistHzProduct(List<BigDecimal> preIdList) {
		List<CmPreBookProductInfo> cmPreBookProductInfos = prebookBasicInfoService.selectListByPreIdList(preIdList);
		if (CollectionUtils.isNotEmpty(cmPreBookProductInfos)) {
			for (CmPreBookProductInfo preBookInfo : cmPreBookProductInfos) {
				if (DisChannelCodeEnum.HZ.getCode().equals(preBookInfo.getPreDisCode())) {
					return true;
				}
			}
		}
		return false;
	}

	@RequestMapping("/deleteLockWhiteList")
    @ResponseBody
    public String deleteLockWhiteList(HttpServletRequest request,String ids){
        if(ids == null){
            return "paramError";
        }

		List<BigDecimal> preIdList = getPreIdListByStr(ids);
		if (isExistHzProduct(preIdList)) {
			return "勾选的预约中有好臻产品，请使用“好臻金额锁定及修改”功能！";
		}

        Map<String, String> param = new HashMap<>(1);
        param.put("ids", ids);
        List<Map<String,String>> list = prebookproductinfoService.listFundCodeAndHbonenoByPreid(param);
        if(CollectionUtils.isNotEmpty(list)){
        	List<HighProductLockWhiteListCrmModel> listadd = new ArrayList<>(8);
        	for(Map<String,String> map : list){
        		HighProductLockWhiteListCrmModel model = new HighProductLockWhiteListCrmModel();
        		model.setFundCode(StringUtil.replaceNull(map.get("FUNDCODE")));
        		model.setHboneNo(StringUtil.replaceNull(map.get("HBONENO")));
        		listadd.add(model);
        	}
        	log.info("删除白名单传入参数: " + JSON.toJSONString(listadd));
        	List<HighProductLockWhiteListCrmModel> listreturn = highProductForCrmService.deleteLockWhiteList(listadd);
        	log.info("删除白名单返回参数: " + JSON.toJSONString(listreturn));
        	if(listreturn != null && listreturn.size() > 0){
        		String startstr = "部分成功，其中";
        		StringBuilder sb = new StringBuilder();
        		sb.append(startstr);
        		boolean allfail = true;
        		for(HighProductLockWhiteListCrmModel returnmodel : listreturn){
        			if("0".equals(returnmodel.getProcStat())){
        				sb.append(returnmodel.getHboneNo()+returnmodel.getFundCode()+",");
        			}else{
        				allfail = false;
        			}
        		}
        		if(allfail){
        			return "删除失败!";
        		}
        		if(!startstr.equals(sb.toString())){
        			return sb.toString().substring(0,sb.toString().length()-1)+"删除失败！";
        		}
        	}else{
        		return "接口返回错误！";
        	}
        }else{
        	return "删除失败，选择的客户没有对应的一账通号！";
        }
        
        return "success";
    }


	/**
	 * @api {POST} /prosale/opRefund opRefund()
	 * @apiVersion 1.0.0
	 * @apiGroup PrebookByOtherController
	 * @apiName opRefund()
	 * @apiDescription 根据预约id进行[退款]操作
	 * @apiParam (请求参数) {Number} preId
	 * @apiParam (请求参数) {String} operator
	 * @apiParam (请求参数) {String} remark
	 * @apiParam (请求参数) {Object} preBookPayVo
	 * @apiParam (请求参数) {Number} preBookPayVo.realPayAmt
	 * @apiParam (请求参数) {String} preBookPayVo.realPayAmtDt
	 * @apiParam (请求参数) {String} preBookPayVo.expectTradeDt
	 * @apiParam (请求参数) {Number} preBookPayVo.fee
	 * @apiParam (请求参数) {Object} preBookUpdateVo
	 * @apiParam (请求参数) {Number} preBookUpdateVo.id
	 * @apiParam (请求参数) {String} preBookUpdateVo.preType
	 * @apiParam (请求参数) {String} preBookUpdateVo.remarks
	 * @apiParam (请求参数) {String} preBookUpdateVo.expectTradeDt
	 * @apiParam (请求参数) {String} preBookUpdateVo.expectPayAmtDt
	 * @apiParam (请求参数) {String} preBookUpdateVo.currency
	 * @apiParam (请求参数) {Number} preBookUpdateVo.buyAmt
	 * @apiParam (请求参数) {Number} preBookUpdateVo.sellVol
	 * @apiParam (请求参数) {String} preBookUpdateVo.realBuyMan
	 * @apiParam (请求参数) {String} preBookUpdateVo.operator
	 * @apiParam (请求参数) {Object} bankVo
	 * @apiParam (请求参数) {String} bankVo.bankAcct
	 * @apiParam (请求参数) {String} bankVo.bankName
	 * @apiParam (请求参数) {String} bankVo.bankCode
	 * @apiParam (请求参数) {String} bankVo.bankAddr
	 * @apiParam (请求参数) {String} bankVo.bankProv
	 * @apiParam (请求参数) {String} bankVo.bankCity
	 * @apiParam (请求参数) {Array} hkCpAcctNoList
	 * @apiParam (请求参数) {Object} confirmTradeVo
	 * @apiParam (请求参数) {Number} confirmTradeVo.manageFee
	 * @apiParam (请求参数) {Number} confirmTradeVo.preformFee
	 * @apiParamExample 请求参数示例
	 * bankVo={"bankCode":"8glWRggUO","bankAcct":"DTwcCr","bankName":"n","bankAddr":"z4VpHXt7z","bankProv":"WjszGOMOpJ","bankCity":"4rXiiTg"}&preId=6171.************&preBookUpdateVo={"buyAmt":1994.*************,"preType":"NFAqYs","expectPayAmtDt":"U7W","currency":"1bk","id":3710.************,"sellVol":5471.************,"expectTradeDt":"v049TE","realBuyMan":"C2LSR","remarks":"nb0DTmPmO","operator":"d"}&preBookPayVo={"realPayAmt":4509.************,"fee":4271.************,"realPayAmtDt":"5","expectTradeDt":"Wy72juy"}&confirmTradeVo={"manageFee":4988.************,"preformFee":4314.************}&remark=WmCPW3Ys&hkCpAcctNoList=HzI4w&operator=v
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"TXRG","returnMsg":"rOG5w","returnObject":"vak","returnList":["L0m9LbPU"]}
	 */
	@ResponseBody
	@PostMapping("/opRefund")
	public ReturnMessageDto<String> opRefund(PrebookTradeDealRequest prebookTradeRequest, HttpServletRequest request) {
		User user = getLoginUser();
		prebookTradeRequest.setOperator(user.getUserId());

		return prebookTradeDealService.executeRefund(prebookTradeRequest);
	}
	
	/**
     * 同步客户来源:
     * @param request
     * <AUTHOR>
     * @date 20210901
     */
    @ResponseBody
    @RequestMapping("/syncCustSourceType.do")
    public ModelAndView syncCustSourceType(HttpServletRequest request){
        Map<String,Object> map = new HashMap<>(2);
        String id = request.getParameter("id");
        CmPrebookproductinfo info = prebookproductinfoService.selectPrebookproductinfoById(id);
        map.put("id", id);
        String conscustno = info.getConscustno();
        String conscode = info.getCreator();
        Map<String,String> param = new HashMap<>(2);
        param.put("custno", conscustno);
        param.put("conscode", conscode);
        List<CmCustconstant> list = cmCustconstantService.listCustConstantByCustAndConscode(param);
        List<Map<String,String>> resultlist = new ArrayList<>();
        if(list != null && list.size() > 0){
        	for(CmCustconstant obj : list){
        		String hisid = obj.getBeforehisid();
        		String objsourcetype = preBookService.getCustSourceTypeByHisid(conscustno, hisid);
        		Map<String,String> objmap = new HashMap<>(2);
                objmap.put("id", objsourcetype);
                objmap.put("val", ConstantCache.getInstance().getVal("custrestype", objsourcetype)+",分配时间为"+obj.getStartdt());
        		resultlist.add(objmap);
        	}
        	map.put("resultcount", list.size());
        }else{
        	map.put("resultcount", 0);
        }
        map.put("result", resultlist);
        return new ModelAndView("prosale/syncCustSourceType","info",map);
    }
    
    @ResponseBody
    @RequestMapping("/saveSyncSourceType.do")
    public String saveSyncSourceType(HttpServletRequest request) {
    	String result="";
        User userlogin = (User)request.getSession().getAttribute("loginUser");
        String id = request.getParameter("id");
        String restype = request.getParameter("restype");
        CmPrebookproductinfo info = prebookproductinfoService.selectPrebookproductinfoById(id);
        info.setSourcetype(restype);
        info.setModifier(userlogin.getUserId());
        UpdatePrebookInfoRequest req = new UpdatePrebookInfoRequest();
        req.setCmPrebookproductinfo(info);
        BaseResponse res = preBookService.updatePrebook(req);
        if(res.isSuccessful()){
        	result="success";
        }else{
        	result = res.getDescription();
        }
        return result;
    }
}