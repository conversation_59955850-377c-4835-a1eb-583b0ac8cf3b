package com.howbuy.crm.hb.web.controller.custinfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.howbuy.crm.hb.domain.custinfo.CmCustfamilySub;
import com.howbuy.crm.hb.domain.custinfo.WaitTransferCust;
import com.howbuy.crm.hb.service.custinfo.CmCustfamilySubService;
import com.howbuy.crm.hb.service.custinfo.WaitTransferCustService;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.util.CustTransferUtil;

import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;

/**
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping(value = "/waittransfercust")
public class WaitTransferCustController {

    private static Logger LOG = LoggerFactory.getLogger(WaitTransferCustController.class);
    
    @Autowired
    private WaitTransferCustService waitTransferCustService;
    
    @Autowired
    private CmCustfamilySubService cmCustfamilySubService;
    
    @SuppressWarnings("unchecked")
	@RequestMapping("/listWaitTransferCust_json.do")
    public String listWaitTransferCustJson(HttpServletRequest request) {
    	List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        //是否有分配权限
        boolean canassign = false;
        for (String role : roles) {
            List<String> temp = AuthCache.getInstance().getOperListStr(role, "020132");
            if (temp != null && temp.contains("3")) {
                canassign = true;
                break;
            }
        }
        HttpSession session=request.getSession();
        String topcpdata = (String) session.getAttribute("topcpdata");
        request.setAttribute("topcpdata", topcpdata);
        request.setAttribute("canassign", canassign);
        request.setAttribute("userId", userlogin.getUserId());
        return "/custinfo/listWaitTransferCust";
    }

    /**
     * 申请划转的客户查询
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listWaitTransferCust.do")
    public Map<String, Object> listWaitTransferCust(HttpServletRequest request) throws Exception {
        // 设置查询参数
        Map<String, String> param = buildallsearchMap(request);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        PageData<WaitTransferCust> pd = null;
        pd = waitTransferCustService.listWaitTransferCustByPage(param);
        resultMap.put("total", pd.getPageBean().getTotalNum());
        List<WaitTransferCust> listtemp = pd.getListData();
        //查询二分体部门
        List<String> eftxlist = new ArrayList<String>();
        eftxlist.addAll(ConsOrgCache.getInstance().getAllOrgSubsMap().get(StaticVar.CH_ORGCODE));
        //查询IC部门
        List<String> iclist = new ArrayList<String>();
        iclist.addAll(ConsOrgCache.getInstance().getAllOrgSubsMap().get(StaticVar.IC_ORGCODE));
        ConstantCache constantCache = ConstantCache.getInstance();
//        Map<String, String> allConsMap = ConsOrgCache.getInstance().getAllConsMap();
        Map<String, String> allUserMap = ConsOrgCache.getInstance().getAllUserMap();
        for (WaitTransferCust v : listtemp) {
        	StringBuilder sb = new StringBuilder();
        	if("1".equals(v.getGdqzlabel())){
        		sb.append("潜在客户");
        	}
        	if("1".equals(v.getGdcjlabel())){
        		if(StringUtils.isNotBlank(sb.toString())){
        			sb.append("/");
        		}
        		sb.append("成交客户");
        	}
        	v.setGdlabelval(sb.toString());
        	v.setConscode(allUserMap.get(v.getConscode()));
        	v.setOrgname(ConsOrgCache.getInstance().getAllOrgMap().get(v.getOutletcode()));
        	v.setAuditMan(allUserMap.get(v.getAuditMan()));
        	StringBuilder sb1 = new StringBuilder();
        	//资金量标签
        	if(StringUtils.isNotBlank(v.getZjllabel())){
        		sb1.append(ConstantCache.getInstance().getVal("zjllabel", v.getZjllabel()));
        	}
        	//客户意向标签
        	if(StringUtils.isNotBlank(v.getKhyxlabel())){
        		if(StringUtils.isNotBlank(sb1.toString())){
        			sb1.append("/");
        		}
        		sb1.append(ConstantCache.getInstance().getVal("khyxlabel", v.getKhyxlabel()));
        	}
        	v.setZjyxlabelval(sb1.toString());
        	if(StringUtils.isNotBlank(v.getVisittime())){
        		v.setVisittime(v.getVisittime().substring(0, 8));
        	}
        	//如果有指定承接人，直接用承接人
        	if(StringUtil.isNotNullStr(v.getNextcons())){
        		v.setDirectcons(ConsOrgCache.getInstance().getAllConsMap().get(v.getNextcons()));
        	}else{
        		//根据各种条件找到待分配的库
	        	String newowner = CustTransferUtil.getTransferOwner(v, eftxlist, iclist);
	        	if(StringUtils.isNotBlank(newowner) && !"noowner".equals(newowner) && !"nonstandard".equals(newowner)){
	        		v.setDirectcons(ConsOrgCache.getInstance().getAllConsMap().get(newowner));
	        	}
        	}
        	v.setStateVal(constantCache.getVal("transfstate", v.getState()));
        }
        
        resultMap.put("rows", listtemp);
        return resultMap;
    }

    public Map<String, String> buildallsearchMap(HttpServletRequest request) throws Exception {
    	// 获取参数
        Map<String, String> param = new ParamUtil(request).getParamMap();
        String custsource = request.getParameter("custSource");
        String isnotleaf = request.getParameter("isnotleaf");
        param.put("isnotleaf", isnotleaf);
        if (StringUtils.isNotBlank(custsource) && StaticVar.STR_FALSE.equals(isnotleaf)) {
        	param.put("newsourceno", custsource);
        } else if (StringUtils.isNotBlank(custsource) && StaticVar.STR_TRUE.equals(isnotleaf)) {
        	param.put("newsourceno", ObjectUtils.getCurrentSource(custsource));
        }
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        if (StringUtils.isNotBlank(consCode)) {
            param.put("conscode", consCode);
        } else {
        	String other = "other";
        	String orgHowbuy = "0";
            //选择了未分配组
            if (orgCode.startsWith(other)) {
                param.put("othertearm", orgCode.replaceFirst("other", ""));
            } else {
                String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
                //选择了团队
                if (StaticVar.ORGTYPE_TEAM.equals(orgType)) {
                    param.put("teamcode", orgCode);
                } else {
                    if (!orgHowbuy.equals(orgCode)) {
                        List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                        param.put("outletcodes", Util.getSqlInStr(suborgs));
                    }
                }
            }
        }
       
        return param;
    }
    
    /**
     * 处理申请划转
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/dealApplyTransfer.do")
    public String dealApplyTransfer(HttpServletRequest request) throws Exception {
        User user = (User) request.getSession().getAttribute("loginUser");
        String result = "";
        String ids = request.getParameter("ids");
        String transfremarks = request.getParameter("transfremarks");
        String transconscode = request.getParameter("transconscode");
        //判断指定承接人是否虚拟投顾
        if(StringUtil.isNotNullStr(transconscode)){
        	Map<String,String> paramcons = new HashMap<String,String>(1);
        	paramcons.put("conscode", transconscode);
        	int conscount = waitTransferCustService.getNormalConsCount(paramcons);
        	if(conscount == 0){
        		result = "consError";
        		return result;
        	}
        }
        if (StringUtils.isNotBlank(ids)) {
        	String[] custs = ids.split(",");
        	//判断是否存在家庭账户
        	result = dealApplyFamilyCust(custs);
        	if(!"".equals(result)){
    			return result;
    		}
        	List<String> list = new ArrayList<String>();
            //将数组转list
            CollectionUtils.addAll(list, custs);
            String sqlins = Util.getOracleSQLIn(list,999,"t.conscustno");
            Map<String,String> map = new HashMap<String,String>(1);
            map.put("sqlins", sqlins);
            //判断打满3个标签后才可申请划转客户
            int notalllabelscount = waitTransferCustService.getNoAllLabelsCountByCusts(map);
            if(notalllabelscount > 0){
            	result = "notalllabels";
            }else{
            	Map<String,String> maptrans = new HashMap<>(5);
            	maptrans.put("ids", ids);
            	maptrans.put("transfremarks", transfremarks);
            	maptrans.put("transconscode", transconscode);
            	maptrans.put("userid", user.getUserId());
            	//处理划转
            	waitTransferCustService.insertWaitTransferCust(maptrans);
            	result = "success";
            }
        } else {
            result = "paramError";
        }
        return result;
    }
    
    /**
     * 处理申请划转
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/dealCheckTransfer.do")
    public String dealCheckTransfer(HttpServletRequest request) throws Exception {
        User user = (User) request.getSession().getAttribute("loginUser");
        //取消划转
        String cancelhuazhuan = "1";
        //划转
        String huazhuan = "2";
        String result = "";
        String ids = request.getParameter("ids");
        String type = request.getParameter("type");
        String checkPassType = "2";
        if (StringUtils.isNotBlank(ids)) {
        	String[] custs = ids.split(",");
        	//判断是否有关联账户
        	if(checkPassType.equals(type)){
        		result = dealFamilyCust(custs);
        		if(!"".equals(result)){
        			return result;
        		}
        	}
            //传过来的客户号
            List<String> list = new ArrayList<String>();
            //将数组转list
            CollectionUtils.addAll(list, custs);
            String sqlins = Util.getOracleSQLIn(list,999,"t.conscustno");
            Map<String,String> param = new HashMap<String,String>(1);
    		param.put("sqlins", sqlins);
    		List<WaitTransferCust> listownner = waitTransferCustService.listWaitTransferCustByCustnos(param);
    		//传参
    		Map<String,String> map = new HashMap<String,String>(3);
            map.put("sqlins", sqlins);
            map.put("creator", user.getUserId());
            map.put("type", type);
    		//有指定承接人的
            List<WaitTransferCust> hasnextcons = new ArrayList<>();
            //没有指定承接人的高端成交客户
            List<WaitTransferCust> hasgdcj = new ArrayList<>();
    		//没有指定承接人且非高端成交客户
            List<WaitTransferCust> nothasnextcons = new ArrayList<>();
    		//如果是撤销划转
            if (cancelhuazhuan.equals(type)) {
                result = waitTransferCustService.cancelTransferCust(listownner, map);
            } else {
                for (WaitTransferCust cust : listownner) {
                    //有指定承接人
                    if (StringUtil.isNotNullStr(cust.getNextcons())) {
                        hasnextcons.add(cust);
                        //高端成交客户
                    } else if ("1".equals(cust.getGdcjlabel())) {
                        hasgdcj.add(cust);
                    } else {
                        nothasnextcons.add(cust);
                    }
                }
                //查询二分体部门
                List<String> eftxlist = new ArrayList<String>();
                eftxlist.addAll(ConsOrgCache.getInstance().getAllOrgSubsMap().get(StaticVar.CH_ORGCODE));
                LOG.info(eftxlist.toString());
                //查询IC部门
                List<String> iclist = new ArrayList<String>();
                iclist.addAll(ConsOrgCache.getInstance().getAllOrgSubsMap().get(StaticVar.IC_ORGCODE));
                result = waitTransferCustService.dealTransferCust(hasgdcj, hasnextcons, nothasnextcons, map, eftxlist, iclist);
            }
    		if(huazhuan.equals(type) && (hasgdcj.size() >0 || hasnextcons.size() > 0)){
    			result = "partsuccess";
    		}
    	} else {
            result = "paramError";
        }
        return result;
    }
    
    public String dealFamilyCust(String[] custs){
		String result = "";
		//传过来的客户号
        List<String> list = new ArrayList<String>();
        //将数组转list
        CollectionUtils.addAll(list, custs);
        Map<String, String> paramfamily = new HashMap<String, String>(1);
		paramfamily.put("sqlins", Util.getOracleSQLIn(list,999,"conscustno"));
		// 判断是否存在家庭账户的客户没转全部成员的
        List<CmCustfamilySub> listfamily = cmCustfamilySubService.listCmCustfamilySubByAssignCust(paramfamily);
        
        Map<String, List<CmCustfamilySub>> mapfamily = new HashMap<String, List<CmCustfamilySub>>(8);
        for (CmCustfamilySub sub : listfamily) {
            if (mapfamily.containsKey(sub.getFamilycode())) {
                mapfamily.get(sub.getFamilycode()).add(sub);
            } else {
                List<CmCustfamilySub> listsub = new ArrayList<CmCustfamilySub>();
                listsub.add(sub);
                mapfamily.put(sub.getFamilycode(), listsub);
            }
        }
        //遍历查找转分配中遗漏了的家庭成员
        StringBuffer sb = new StringBuffer();
        sb.append("选中的客户中存在");
        for(Map.Entry<String, List<CmCustfamilySub>> entry : mapfamily.entrySet()){
            List<String> familycusts = new ArrayList<String>();
            familycusts.add(entry.getKey());
            for (CmCustfamilySub sub : entry.getValue()) {
                familycusts.add(sub.getConscustno());
            }
            if (list.containsAll(familycusts)) {
                continue;
            } else {
                sb.append("关联账户" + entry.getValue().get(0).getFamilyname() + ",它还有其他家庭（主）辅账户");
                for (String subobj : familycusts) {
                    if (!list.contains(subobj)) {
                        if (entry.getKey().equals(subobj)) {
                            sb.append(entry.getValue().get(0).getFamilyname() + ",");
                        } else {
                            for (CmCustfamilySub obj : entry.getValue()) {
                                if (subobj.equals(obj.getConscustno())) {
                                    sb.append(obj.getCustname() + ",");
                                }
                            }
                        }
                    }
                }
                sb.append("未统一转分配;");
            }
        }
        String selectFlagStr = "选中的客户中存在";
        String familystr = sb.toString();
        if (!selectFlagStr.equals(familystr)) {
            familystr += "划转批复失败。";
            result = familystr;
        }
        return result;
	}
    
    public String dealApplyFamilyCust(String[] custs){
		String result = "";
		//传过来的客户号
        List<String> list = new ArrayList<String>();
        //将数组转list
        CollectionUtils.addAll(list, custs);
        Map<String, String> paramfamily = new HashMap<String, String>(1);
		paramfamily.put("sqlins", Util.getOracleSQLIn(list,999,"conscustno"));
		// 判断是否存在家庭账户的客户没转全部成员的
        List<CmCustfamilySub> listfamily = cmCustfamilySubService.listCmCustfamilySubByAssignCust(paramfamily);
        if(listfamily != null && listfamily.size() > 0){
        	//遍历查找转分配中遗漏了的家庭成员
            StringBuffer sb = new StringBuffer();
            sb.append("选中的客户中存在家庭账户:");
            for(int i = 0; i <listfamily.size();i++ ){
            	CmCustfamilySub obj = listfamily.get(i);
            	if(i == 0){
            		if(list.contains(obj.getFamilycode())){
            			sb.append(obj.getFamilyname()+";");
            		}
            	}
            	if(list.contains(obj.getConscustno())){
        			sb.append(obj.getCustname()+";");
        		}
            }
            sb.append("只能人工划转，不能申请自动划转，划转失败！");
            result = sb.toString();
        }
        
        return result;
	}
    
    /**
     * 判断是否有高端成交客户
     * @param request
     * @param params
     * @return
     * @throws Exception
     */
	@ResponseBody
	@RequestMapping("/checkhascllabel.do")
	public Map<String, Object> checkhascllabel(HttpServletRequest request, @RequestParam Map<String, String> params)
			throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(1);
		String ids = params.get("ids");
		if (StringUtils.isNotBlank(ids)) {
        	String[] custs = ids.split(",");
        	List<String> list = new ArrayList<String>();
            //将数组转list
            CollectionUtils.addAll(list, custs);
            String sqlins = Util.getOracleSQLIn(list,999,"t.conscustno");
            Map<String,String> map = new HashMap<String,String>(1);
            map.put("sqlins", sqlins);
            int count = waitTransferCustService.getCjkhCountByCusts(map);
            if(count > 0){
            	resultMap.put("resultcode", 1);
            }else{
            	resultMap.put("resultcode", 0);
            }
        } else {
        	resultMap.put("resultcode", 0);
        }
		return resultMap;
	}

    @RequestMapping("/applytrans.do")
    public String applyTrans(HttpServletRequest request){

        String ids = request.getParameter("ids");
        request.setAttribute("ids",ids);
        return "/custinfo/applytrans";
    }

}
