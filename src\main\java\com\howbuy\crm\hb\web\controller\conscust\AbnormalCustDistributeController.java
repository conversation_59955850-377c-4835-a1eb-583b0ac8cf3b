package com.howbuy.crm.hb.web.controller.conscust;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.domain.callout.HbOneCustAbnormal;
import com.howbuy.crm.hb.domain.conscust.ConscustVO;
import com.howbuy.crm.hb.domain.conscust.Conscustrpubcust;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.service.conscust.ConscustrpubcustService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.util.ParamUtil;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "/conscust")
public class AbnormalCustDistributeController {
	@Autowired
	private ConscustService conscustService;
	
	@Autowired
	private ConscustrpubcustService conscustrpubcustService;
	
	@Autowired
    private QueryConscustInfoService queryConscustInfoService;
	
	@Autowired
	private DecryptSingleFacade decryptSingleFacade;

	/**
	 * 
	 * <p>功能描述：异常客户分配</p>
	 * <p>创建日期：2014年11月28日</p>
	 * @return String
	 * <AUTHOR>
	 * @update [更改日期 yyyy-MM-dd] [更改人姓名]
	 */
	@RequestMapping(value="/abnormalCustDistributeIndex.do")
	public String abnormalCustDistributeIndex(){
		return "conscust/abnormalCustDistribute"; 
	}
	
	/**
	 * <p>功能描述：一帐通异常客户处理页面</p>
	 */
	@RequestMapping(value="/abnormalHboneCust.do")
	public String abnormalHboneCustIndex(Map map){
		// “处理状态”默认选中“未处理”
		map.put("defaultDealStatus", StaticVar.HBONE_ABNORMAL_NOT_HANDLE);
		return "conscust/abnormalHboneCust"; 
	}
	
    /**
     * 
     * <p>功能描述：获取分配给异常投顾的客户信息</p>
     * <p>创建日期：2014年11月28日</p>
     * @return Map<String,Object>
     * <AUTHOR>
     * @update [更改日期 yyyy-MM-dd] [更改人姓名]
     */
	@RequestMapping("/listAbnormalCustInfo.do")
	public @ResponseBody Map<String, Object> listAbnormalCustInfo(HttpServletRequest request,HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		// 获取分页参数
		param = new ParamUtil(request).getParamMap();
		if(StringUtils.isNotBlank(param.get("telNo"))){
			param.put("telNo", DigestUtil.digest(param.get("telNo").trim()));
		}
		if(StringUtils.isNotBlank(param.get("email"))){
			param.put("email", DigestUtil.digest(param.get("email").trim().toLowerCase()));
		}
		
		PageData<Conscust> csPotentialUploadLogData = conscustService.listAbnormalCustByPage(param);
		
		List<Conscust> list = csPotentialUploadLogData.getListData();
		if(CollectionUtils.isNotEmpty(list)){
			for(Conscust conscust : list){
				if(StringUtils.isNotBlank(conscust.getTelnoCipher())){
					CodecSingleResponse  res = decryptSingleFacade.decrypt(conscust.getTelnoCipher());
					conscust.setTelno(res.getCodecText());
				}
				if(StringUtils.isNotBlank(conscust.getMobileCipher())){
					CodecSingleResponse  res = decryptSingleFacade.decrypt(conscust.getMobileCipher());
					conscust.setMobile(res.getCodecText());
				}
				if(StringUtils.isNotBlank(conscust.getMobile2Cipher())){
					CodecSingleResponse  res = decryptSingleFacade.decrypt(conscust.getMobile2Cipher());
					conscust.setMobile2(res.getCodecText());
				}
				if(StringUtils.isNotBlank(conscust.getEmailCipher())){
					CodecSingleResponse  res = decryptSingleFacade.decrypt(conscust.getEmailCipher());
					conscust.setEmail(res.getCodecText());
				}
				if(StringUtils.isNotBlank(conscust.getEmail2Cipher())){
					CodecSingleResponse  res = decryptSingleFacade.decrypt(conscust.getEmail2Cipher());
					conscust.setEmail2(res.getCodecText());
				}
				if(StringUtils.isNotBlank(conscust.getIdnoCipher())){
					CodecSingleResponse  res = decryptSingleFacade.decrypt(conscust.getIdnoCipher());
					conscust.setIdno(res.getCodecText());
				}
			}
		}
		
		//3.5.1 敏感数据
		/*for(Conscust conscust:csPotentialUploadLogData.getListData()){
			conscust.setEmail(Util.getEncryptValue(request, StaticVar.ENCRYPT_EMAIL, conscust.getEmail(), null, null));
			conscust.setMobile(Util.getEncryptValue(request, StaticVar.ENCRYPT_MOBILE, conscust.getMobile(), null, null));
			conscust.setIdno(Util.getEncryptValue(request, StaticVar.ENCRYPT_BANKNO	, conscust.getIdno(), null, null));
		}*/
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", csPotentialUploadLogData.getPageBean().getTotalNum());
		resultMap.put("rows", list);
		
		return resultMap;
	}
	/**
	 * 
	 * <p>功能描述：根据异常的手机号或者邮箱获取正确的有投顾的客户数据</p>
	 * <p>创建日期：2014年12月1日</p>
	 * @return Map<String,Object>
	 * <AUTHOR>
	 * @update [更改日期 yyyy-MM-dd] [更改人姓名]
	 */
	@RequestMapping(value="/getAbnormalTelNoEmailCustInfo.do")
	public @ResponseBody Map<String,Object> getAbnormalTelNoEmailCustInfo(HttpServletRequest request,HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();

		Map<String, String> param = new HashMap<String, String>();
		param = new ParamUtil(request).getParamMap();
		
		String telnoStr = param.get("telno");
		String emailStr = param.get("email");
		String idNO = param.get("idNO");
		
		String telno = null;
		String email = null;
		String mobile = null;
		String mobile2 = null;
		String email2 = null;

		if(telnoStr != null && telnoStr.contains(",")){
			String[] telnos = telnoStr.split(",");
			
			telno = telnos[0];
			mobile = telnos[1];
			
			if(telnos.length >=3 ){
				mobile2 = telnos[2];
			}
			
		}else{
			telno = telnoStr;
		}
		
		if(emailStr != null && emailStr.contains(",")){
			String[] emails = emailStr.split(",");
			
			email = emails[0];
			email2 = emails[1];
		}else{
			email = emailStr;
		}
		
		Map<String,Object> paramMap = new HashMap<String,Object>();
		paramMap.put("telno", StringUtils.isBlank(telno) ? null : DigestUtil.digest(telno.trim()));
		paramMap.put("email", StringUtils.isBlank(email) ? null : DigestUtil.digest(email.trim().toLowerCase()));
		paramMap.put("mobile", StringUtils.isBlank(mobile) ? null : DigestUtil.digest(mobile.trim()));
		paramMap.put("mobile2", StringUtils.isBlank(mobile2) ? null : DigestUtil.digest(mobile2.trim()));
		paramMap.put("email2", StringUtils.isBlank(email2) ? null : DigestUtil.digest(email2.trim().toLowerCase()));
		paramMap.put("idNO", StringUtils.isBlank(idNO) ? null : DigestUtil.digest(idNO.trim()));
		
		List<ConscustVO> sameTelNoEmailCusts = conscustService.getAbnormalTelNoEmailCustInfo(paramMap);
		if(CollectionUtils.isNotEmpty(sameTelNoEmailCusts)){
			for(ConscustVO conscust : sameTelNoEmailCusts){
				if(StringUtils.isNotBlank(conscust.getTelnoCipher())){
					CodecSingleResponse  res = decryptSingleFacade.decrypt(conscust.getTelnoCipher());
					conscust.setTelno(res.getCodecText());
				}
				if(StringUtils.isNotBlank(conscust.getMobileCipher())){
					CodecSingleResponse  res = decryptSingleFacade.decrypt(conscust.getMobileCipher());
					conscust.setMobile(res.getCodecText());
				}
				if(StringUtils.isNotBlank(conscust.getMobile2Cipher())){
					CodecSingleResponse  res = decryptSingleFacade.decrypt(conscust.getMobile2Cipher());
					conscust.setMobile2(res.getCodecText());
				}
				if(StringUtils.isNotBlank(conscust.getEmailCipher())){
					CodecSingleResponse  res = decryptSingleFacade.decrypt(conscust.getEmailCipher());
					conscust.setEmail(res.getCodecText());
				}
				if(StringUtils.isNotBlank(conscust.getEmail2Cipher())){
					CodecSingleResponse  res = decryptSingleFacade.decrypt(conscust.getEmail2Cipher());
					conscust.setEmail2(res.getCodecText());
				}
			}
		}
		
		resultMap.put("total", sameTelNoEmailCusts.size());
		resultMap.put("rows", sameTelNoEmailCusts);
		
		return resultMap;
	}
	/**
	 * 
	 * <p>功能描述：异常用户关联</p>
	 * <p>创建日期：2014年12月1日</p>
	 * @return Map<String,Object>
	 * <AUTHOR>
	 * @update [更改日期 yyyy-MM-dd] [更改人姓名]
	 */
	@RequestMapping(value="/relationCust.do")
	public @ResponseBody Map<String,Object> relationCust(HttpServletRequest request,HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try{
			Map<String, String> param = new HashMap<String, String>();
			param = new ParamUtil(request).getParamMap();
			
			String normalConscustNo = param.get("normalConscustNo");
			String abNormalConscustNo = param.get("abNormalConscustNo");
			
			Map<String,Object> paramMap = new HashMap<String,Object>();
			paramMap.put("normalConscustNo", normalConscustNo);
			paramMap.put("abNormalConscustNo", abNormalConscustNo);		
			
			if(!conscustrpubcustService.existPubCust(paramMap)){
				
				conscustService.relationCust(paramMap);
				resultMap.put("returnFlag", "success");
			}else{
				resultMap.put("returnFlag", "error");
				resultMap.put("errorMsg", "已经存在关联的公募客户号！");
			}

			
			
		}catch(Exception ex){
			ex.printStackTrace();
			resultMap.put("returnFlag", "error");
			resultMap.put("errorMsg", ex.getMessage());
		}
		
		return resultMap;
	}
	/**
	 * 
	 * <p>功能描述：根据投顾客户号获取公募客户号</p>
	 * <p>创建日期：2014年12月2日</p>
	 * @return Map<String,Object>
	 * <AUTHOR>
	 * @update [更改日期 yyyy-MM-dd] [更改人姓名]
	 */
	@RequestMapping(value="/getPubCustNoByConscustNo.do")
	public @ResponseBody Map<String,Object> getPubCustNoByConscustNo(HttpServletRequest request,HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		
		try{
			Map<String, String> param = new HashMap<String, String>();
			param = new ParamUtil(request).getParamMap();
			
			String normalConscustNo = param.get("normalConscustNo");
			
			Map<String,Object> paramMap = new HashMap<String,Object>();
			paramMap.put("conscustno", normalConscustNo);

			Conscustrpubcust conscustrpubcust = conscustrpubcustService.getPubCustByConscustNo(paramMap);
			
			if(conscustrpubcust != null){
				String pubCustNo = conscustrpubcust.getPubcustno();  //公募客户号
				resultMap.put("returnFlag", "success");
				resultMap.put("pubCustNo", pubCustNo);
			}else{
				resultMap.put("returnFlag", "error");
				resultMap.put("errorMsg", "没有对应的公募客户号！");
			}
			
		}catch(Exception ex){
			ex.printStackTrace();
			resultMap.put("returnFlag", "error");
			resultMap.put("errorMsg", ex.getMessage());
		}
		
		return resultMap;
	}
	
	
	/**
     * <p>功能描述：获取分配异常一账通客户信息</p>
     */
	@RequestMapping("/listAbnormalHboneCust.do")
	public @ResponseBody Map<String, Object> listAbnormalHboneCust(HttpServletRequest request,HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		// 获取分页参数
		param = new ParamUtil(request).getParamMap();
		/*String dealStatus = param.get("dealStatus");
		
		if(StringUtil.isNullStr(dealStatus)){
			param.put("dealStatus", "0");
		}*/
		
		String idNo = param.get("idNo");
		if(StringUtils.isNotBlank(idNo)){
			param.put("idNo", DigestUtil.digest(idNo.trim()));
		}
		
		String mobile = param.get("mobile");
		if(StringUtils.isNotBlank(mobile)){
			param.put("mobile", DigestUtil.digest(mobile.trim()));
		}
		
		PageData<HbOneCustAbnormal> csPotentialUploadLogData = conscustService.listAbnormalHboneCust(param);
		
		for(HbOneCustAbnormal hbOneCustAbnormal:csPotentialUploadLogData.getListData()){
			hbOneCustAbnormal.setMobile2(hbOneCustAbnormal.getMobileMask());
			hbOneCustAbnormal.setIdNo2(hbOneCustAbnormal.getIdNoMask());
			if(StringUtils.isNotBlank(hbOneCustAbnormal.getMobileCipher())){
				CodecSingleResponse  res = decryptSingleFacade.decrypt(hbOneCustAbnormal.getMobileCipher());
				mobile = res.getCodecText();
				hbOneCustAbnormal.setMobile(mobile);
			}
            if(StringUtils.isNotBlank(hbOneCustAbnormal.getIdNoCipher())){
            	CodecSingleResponse  res = decryptSingleFacade.decrypt(hbOneCustAbnormal.getIdNoCipher());
            	idNo = res.getCodecText();
				hbOneCustAbnormal.setIdNo(idNo);
			}
            // 设置“处理状态”
			String dealStatusName = ConstantCache.getInstance().getVal("dealStatus", hbOneCustAbnormal.getDealStatus());
			hbOneCustAbnormal.setDealStatusName(dealStatusName);
		}
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", csPotentialUploadLogData.getPageBean().getTotalNum());
		resultMap.put("rows", csPotentialUploadLogData.getListData());
		
		return resultMap;
	}
	
	/**
     * <p>功能描述：获取一账通对应的客户信息</p>
     */
	@RequestMapping("/getAbnormalConsCustInfo.do")
	public @ResponseBody Map<String, Object> getAbnormalConsCustInfo(HttpServletRequest request,HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		// 获取分页参数
		param = new ParamUtil(request).getParamMap();		
		String hboneNo = param.get("hboneNo");		
		Map<String,String> paramMap = new HashMap<String,String>();
		paramMap.put("hboneNo", hboneNo);		
		List<Conscust> listCust = conscustService.listAbnormalConsCust(paramMap);
		if(listCust != null && listCust.size()>0){
			/*for(Conscust conscust:listCust){
				conscust.setMobile(Util.getEncryptValue(request, StaticVar.ENCRYPT_MOBILE, conscust.getMobile(), null, null));
				conscust.setIdno(Util.getEncryptValue(request, StaticVar.ENCRYPT_BANKNO	, conscust.getIdno(), null, null));
			}*/
			resultMap.put("rows", listCust);
		}else{
			resultMap.put("rows", 0);
		}
		
		return resultMap;
	}
	
	/**
	 * <p>功能描述：同步一账通客户信息</p>
	 */
	@RequestMapping(value="/doSyncCust.do")
	public @ResponseBody Map<String,Object> doSyncCust(HttpServletRequest request,HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try{
			Map<String, String> param = new HashMap<String, String>();
			param = new ParamUtil(request).getParamMap();
			
			String conscustNo = param.get("conscustNo");
			String hboneNo = param.get("hboneNo");
			String custName = param.get("custName");
			String mobile = param.get("mobile");
			String userType = param.get("userType");
			String idType = param.get("idType");
			String idNo = param.get("idNo");
			
			if(StringUtils.isNotBlank(idNo)){
				param.put("idno", DigestUtil.digest(idNo.trim()));
			}
			if(StringUtils.isNotBlank(mobile)){
				param.put("mobile", DigestUtil.digest(mobile.trim()));
			}
			// 判断一账通账号有没有被其他客户占用，有占用则不允许关联和合并信息
			if(StringUtil.isNotNullStr(hboneNo) && StringUtil.isNotNullStr(conscustNo)){
				param.put("hboneno", hboneNo);
				param.put("conscuststatus", "0");
				List<Conscust> listConsCust = conscustService.listConscust2(param);
				boolean hadExsit = false;
				for(Conscust queryConscust : listConsCust){
					if(queryConscust!=null && queryConscust.getConscustno()!=null && !conscustNo.equals(queryConscust.getConscustno())){
						hadExsit = true;
					}
				}
				if(hadExsit){
					resultMap.put("returnFlag", "error");
					resultMap.put("errorMsg", "一账通已被其他客户占用，不能关联和更新此客户信息！");
					return resultMap;
				}
			}else{
				resultMap.put("returnFlag", "error");
				resultMap.put("errorMsg", "参数错误！");
				return resultMap;
			}
			
			HttpSession loginSession=request.getSession();
			User loginUser=(User)loginSession.getAttribute("loginUser");
			String userId =  loginUser.getUserId();
			ConscustInfoDomain conscust = null;
			if(StringUtil.isNotNullStr(conscustNo)){
				QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
		        queryRequest.setConscustno(conscustNo);
		        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
		        conscust = queryResponse.getConscustinfo();
			}
			
			if(StringUtil.isNullStr(conscustNo) || conscust == null){
				resultMap.put("returnFlag", "error");
				resultMap.put("errorMsg", "投顾客户号异常");
				return resultMap;
			}
			if(StringUtil.isNotNullStr(hboneNo)){
				conscust.setHboneno(hboneNo);
			}
			if(StringUtil.isNotNullStr(custName) && !"null".equals(custName) && StringUtil.isNotNullStr(idNo) && !"null".equals(idNo)){
				conscust.setCustname(custName);
			}
			if(StringUtil.isNotNullStr(mobile) && !"null".equals(mobile)){
				conscust.setMobile(mobile);
				conscust.setMobileCipher(null);
				conscust.setMobileDigest(null);
				conscust.setMobileMask(null);
			}
			if(StringUtil.isNotNullStr(userType) && !"null".equals(userType)){
				conscust.setInvsttype(userType);
			}
			if(StringUtil.isNotNullStr(idType) && !"null".equals(idType)){
				conscust.setIdtype(idType);
			}
			if(StringUtil.isNotNullStr(idNo) && !"null".equals(idNo)){
				conscust.setIdno(idNo);
				conscust.setIdnoCipher(null);
				conscust.setIdnoDigest(null);
				conscust.setIdnoMask(null);
			}
			// 同步一账通数据
			conscustService.syncHboneCust(conscust, userId, hboneNo);
			resultMap.put("returnFlag", "success");
		}catch(Exception ex){
			ex.printStackTrace();
			resultMap.put("returnFlag", "error");
			resultMap.put("errorMsg", ex.getMessage());
		}
		return resultMap;
	}

	@RequestMapping(value="/updateHboneAbnormalNoneedHandle.do")
	@ResponseBody
	public Map<String,Object> updateHboneAbnormalNoneedHandle(String hboneNo) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			// 修改为无需处理
			conscustService.updateHboneAbnormalNoneedHandle(hboneNo);
			resultMap.put("returnFlag", "success");
		}catch(Exception ex){
			ex.printStackTrace();
			resultMap.put("returnFlag", "error");
			resultMap.put("errorMsg", ex.getMessage());
		}
		return resultMap;
	}

}
