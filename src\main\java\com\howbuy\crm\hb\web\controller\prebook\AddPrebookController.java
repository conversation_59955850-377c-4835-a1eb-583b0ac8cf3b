package com.howbuy.crm.hb.web.controller.prebook;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.base.*;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.domain.prosale.CmCustTransfervol;
import com.howbuy.crm.hb.service.prosale.CmCustTransfervolService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.prebook.PreBookLegaldocUploadMethodDTO;
import com.howbuy.crm.hb.web.request.prebook.HbPreBookLegalDocUploadMethodRequest;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.core.webapp.util.SessionUserManager;
import com.howbuy.crm.prebook.dto.CmPreBookProductInfo;
import com.howbuy.crm.prebook.request.PreBookLegalDocUploadMethodRequest;
import com.howbuy.crm.prebook.response.PreBookLegalDocUploadMethodResponse;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.prebook.service.PrebookBusinessService;
import com.howbuy.crm.prebook.service.PrebookValidateService;
import com.howbuy.crm.prebook.vo.CmPreBookInsertVo;
import com.howbuy.crm.prosale.dto.PreBookValidateResultDto;
import com.howbuy.crm.prosale.request.GetInfoByParamRequest;
import com.howbuy.crm.prosale.response.GetPreTigFundCustResponse;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.utils.ParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @description:(新增预约 的 controller)
 * @return
 * @author: haoran.zhang
 * @date: 2023/5/19 14:39
 * @since JDK 1.8
 */
@Slf4j
@Controller
@RequestMapping(value = "/addprebook")
public class AddPrebookController extends BaseController {

    @Autowired
    private QueryPreBookService queryPreBookService;

    @Autowired
    private PrebookBusinessService prebookBusinessService;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    @Autowired
    private PrebookValidateService prebookValidateService;

    @Autowired
    private JjxxInfoService jjxxInfoService;

    @Autowired
    private CmCustTransfervolService cmCustTransfervolService;

    /**
     * 认申购对应的  预约列表。
     */
    private static final List<PreBookTradeTypeEnum> BUY_TRADE_LIST=Lists.newArrayList(PreBookTradeTypeEnum.APPEND,PreBookTradeTypeEnum.BUY);

    @Autowired
    private PrebookBasicInfoService prebookBasicInfoService;


    /**
     * @description:(查询客户是否满足购买预约的条件:)
     * @param custNo 客户号
     * @param channelType 产品类型  {@link com.howbuy.crm.base.PreBookChannelEnum}
     * @return java.lang.Object
     * @author: haoran.zhang
     * @date: 2023/5/24 10:57
     * @since JDK 1.8
     */
    @RequestMapping("/custcertifybychannel.do")
    @ResponseBody
    public ReturnMessageDto<PreBookValidateResultDto> custCertifyByChannel(
                                       @RequestParam(value = "custNo") String custNo,
                                       @RequestParam(value = "channelType") String channelType){
//        仅判断 [实名  入会  掌机登录  字段完整]
        List<PreBookValidateTypeEnum> defineValidateTypeList= Lists.newArrayList(
                PreBookValidateTypeEnum.REAL_NAME,
                PreBookValidateTypeEnum.INVEST_CERTIFIED,PreBookValidateTypeEnum.CUST_ATTRIBUTE
        );
        ReturnMessageDto<PreBookValidateResultDto> resultDto=
                queryPreBookService.getValidateResultByChannel(custNo, PreBookChannelEnum.getEnum(channelType),defineValidateTypeList);
        return resultDto;
    }


    /**
     * @description:(保存预约之前的 confirm信息 汇总在这里: 注意：不是业务校验，只是confirm提示信息)
     * @param preInfo  提交的预约信息
     * @return ReturnMessageDto<String>  success表示 不需要前段confirm . fail 表示 前端需要confim提示！
     * @author: haoran.zhang
     * @date: 2023/5/24 10:57
     * @since JDK 1.8
     */
    @RequestMapping("/getconfirmbeforesave.do")
    @ResponseBody
    public ReturnMessageDto<String> getConfirmBeforeSave(CmPreBookInsertVo preInfo) {
        dealAttributeForInsertVo(preInfo);
        return prebookValidateService.getConfirmBeforeSave(preInfo);
    }



    /**
     * @description:(页面端 属性处理 )
     * @param insertVo
     * @return void
     * @author: haoran.zhang
     * @date: 2024/2/27 11:26
     * @since JDK 1.8
     */
    private void dealAttributeForInsertVo(CmPreBookInsertVo insertVo){
        //日期 格式化处理
        insertVo.setExpectpayamtdt(ParamUtil.trimDate(insertVo.getExpectpayamtdt()));
        insertVo.setExpecttradedt(ParamUtil.trimDate(insertVo.getExpecttradedt()));
        //金额单位处理
        if(insertVo.getBuyamt()!=null){
            insertVo.setBuyamt(insertVo.getBuyamt().multiply(BIG_DECIMAL_1W));
        }
        if(insertVo.getSubscribeAmt()!=null){
            insertVo.setSubscribeAmt(insertVo.getSubscribeAmt().multiply(BIG_DECIMAL_1W));
        }
        insertVo.setOperator(getLoginUserId());
    }


    @ResponseBody
    @RequestMapping("/saveprebook")
    public ReturnMessageDto<String> savePrebook(CmPreBookInsertVo insertVo){
        //页面端 属性 处理
        dealAttributeForInsertVo(insertVo);
        ReturnMessageDto<String>  insertResult=prebookBusinessService.insertPreBook(insertVo);
        if(!insertResult.isSuccess()){
            return  insertResult;
        }
        //插入预约成功后， 主动获取提示语  confirm
        String confirmMsg = getMsgAferSave(insertVo, false);
        //获取售前材料提醒文案
        String preIdStr = insertResult.getReturnObject();
        String msg = getLegalMsg(preIdStr);
        return ReturnMessageDto.ok(confirmMsg,msg);
    }

    /**
     * @description: 获取售前材料提醒文案
     * @param preIdStr
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/11/5 16:21
     * @since JDK 1.8
     */
    private String getLegalMsg(String preIdStr) {
        CmPreBookProductInfo bookProductInfo = prebookBasicInfoService.getPreBookById(new BigDecimal(preIdStr));
        if (PreBookLegalDocStatEnum.NOT_UPLOADED.getCode().equals(bookProductInfo.getLegaldocStat())) {
            // 根据 分销渠道的不同 设置提示语
            if (DisChannelCodeEnum.HZ.getCode().equals(bookProductInfo.getPreDisCode())) {
                // 好臻
                return "创建成功，请尽快上传股权项目经理给客户发送售前留痕材料的截图！";
            } else {
                // 默认为好买
                return "创建成功，请尽快上传售前留痕材料！";
            }
        }

        return null;
    }


    /**
     * @description:(分次call 认缴预约、追加 预约 保存)
     * @param insertVo
     * @return com.howbuy.crm.base.ReturnMessageDto<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/2/28 9:31
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/savesubscribe")
    public ReturnMessageDto<String> saveSubscribe(CmPreBookInsertVo insertVo){

        //页面端 属性 处理
        dealAttributeForInsertVo(insertVo);
        //标记为： 分次call
        insertVo.setFcclFlag(YesOrNoEnum.YES.getCode());

        //maincallId为空， 或者  交易类型=购买    表明：  首次 分次call 预约
        if(insertVo.getMainCallId()==null ||
                PreBookTradeTypeEnum.BUY.getCode().equals(insertVo.getTradeType()) ){
            // 判断同客户同产品是否存在非撤销的份额转让单，如存在，则提示：“该产品存在份额转让，请到份额转让模块操作首次实缴”
            Map<String,Object> param1 = Maps.newHashMap();
            param1.put("custno", insertVo.getConscustno());
            param1.put("fundcode", insertVo.getPcode());
            param1.put("normalstat", "1");
            CmCustTransfervol obj = cmCustTransfervolService.getCmCustTransfervol(param1);
            if(obj != null){
                return ReturnMessageDto.fail("该产品存在份额转让，请到份额转让模块操作首次实缴.");
            }
        }


        ReturnMessageDto<String>  insertResult=prebookBusinessService.insertPreBook(insertVo);
        if(!insertResult.isSuccess()){
            return  insertResult;
        }
        //插入预约成功后， 主动获取提示语  confirm
        String confirmMsg = getMsgAferSave(insertVo, true);
        // 获取售前材料提醒文案
        String preIdStr = insertResult.getReturnObject();
        String msg = getLegalMsg(preIdStr);
        return ReturnMessageDto.ok(confirmMsg, msg);
    }

    /**
     * @param insertVo
     * @param fcclFlag
     * @return java.lang.String
     * @description:(获取保存预约之后的提示信息)
     */
    private String getMsgAferSave(CmPreBookInsertVo insertVo, boolean fcclFlag) {
        String  confirmMsg=null;
        PreBookTradeTypeEnum tradeTypeEnum=PreBookTradeTypeEnum.getEnum(insertVo.getTradeType());
        if(tradeTypeEnum!=PreBookTradeTypeEnum.SALE){
            //待重构 TODO :
            GetInfoByParamRequest checkreq = new GetInfoByParamRequest();
            checkreq.setConscustno(insertVo.getConscustno());
            checkreq.setFundcode(insertVo.getPcode());
            checkreq.setTradetype(tradeTypeEnum.getCode());

            checkreq.setSellVol(insertVo.getSellvol()==null?null: insertVo.getSellvol().toPlainString());
            checkreq.setSellAmt(insertVo.getSellAmt());
            checkreq.setRedeemMode(insertVo.getRedeemMode());

            checkreq.setExpectTradedt(insertVo.getExpecttradedt());
            checkreq.setAppAmt(insertVo.getBuyamt());

            if (SessionUserManager.isRoleHkCp() && !fcclFlag) {
                checkreq.setIsXgcj(YesOrNoEnum.YES.getCode());
            }
            GetPreTigFundCustResponse tigresponse = queryPreBookService.getPreTigFundCust(checkreq);
            confirmMsg = tigresponse.getMsg();
        }
        return confirmMsg;
    }


    /**
     * @api {POST} /addprebook/getlegaldocuploadmethod getLegaldocUploadMethod()
     * @apiVersion 1.0.0
     * @apiGroup AddPrebookController
     * @apiName getLegaldocUploadMethod()
     * @apiDescription 创建预约时，获取售前材料上传方式交互的接口
     * @apiParam (请求参数) {String} consCustNo 投顾客户号
     * @apiParam (请求参数) {String} prodCode 基金代码
     * @apiParam (请求参数) {String} preType 预约类型
     * @apiParam (请求参数) {String} tradeType 交易类型( 1：购买，2：追加，3：赎回)
     * @apiParamExample 请求参数示例
     * prodCode=lLeFr&preType=sU&consCustNo=iqO&tradeType=11v
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {Object} returnObject
     * @apiSuccess (响应结果) {String} returnObject.uploadLegalDocFlag 是否需要上传售前留痕材料 1-是；0-否
     * @apiSuccess (响应结果) {String} returnObject.legalDocDefaultUploadMethod 上传好臻售前留痕材料的默认方式 0-自主上传资料；1-自动发送客户邮箱
     * @apiSuccess (响应结果) {String} returnObject.defaultMethodModifyFlag 默认方式是否允许修改 1-是；0-否
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccess (响应结果) {String} returnList.uploadLegalDocFlag 是否需要上传售前留痕材料 1-是；0-否
     * @apiSuccess (响应结果) {String} returnList.legalDocDefaultUploadMethod 上传好臻售前留痕材料的默认方式 0-自主上传资料；1-自动发送客户邮箱
     * @apiSuccess (响应结果) {String} returnList.defaultMethodModifyFlag 默认方式是否允许修改 1-是；0-否
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"bozZyQ2C9x","returnMsg":"lC","returnObject":{"uploadLegalDocFlag":"Lj3yo8H","defaultMethodModifyFlag":"zO3x8jV4MW","legalDocDefaultUploadMethod":"ofeFnO"},"returnList":[{"uploadLegalDocFlag":"4g","defaultMethodModifyFlag":"bvtvjl","legalDocDefaultUploadMethod":"HKO6uh"}]}
     */
    @ResponseBody
    @PostMapping("/getlegaldocuploadmethod")
    public ReturnMessageDto<PreBookLegaldocUploadMethodDTO> getLegaldocUploadMethod(HbPreBookLegalDocUploadMethodRequest request) {
        PreBookLegaldocUploadMethodDTO preBookLegaldocUploadMethodDTO = new PreBookLegaldocUploadMethodDTO();
//        preBookLegaldocUploadMethodDTO.setUploadLegalDocFlag("1");
//        preBookLegaldocUploadMethodDTO.setDefaultMethodModifyFlag("1");
//        preBookLegaldocUploadMethodDTO.setLegalDocDefaultUploadMethod("0");

        PreBookLegalDocUploadMethodRequest tdRequest = new PreBookLegalDocUploadMethodRequest();
        tdRequest.setConsCustNo(request.getConsCustNo());
        tdRequest.setProdCode(request.getProdCode());
        tdRequest.setPreType(request.getPreType());
        tdRequest.setTradeType(request.getTradeType());

        ReturnMessageDto<PreBookLegalDocUploadMethodResponse> legaldocUploadMethod = prebookBusinessService.getLegaldocUploadMethod(tdRequest);
        BeanUtils.copyProperties(legaldocUploadMethod.getReturnObject(), preBookLegaldocUploadMethodDTO);

        return ReturnMessageDto.ok("", preBookLegaldocUploadMethodDTO);
    }

}