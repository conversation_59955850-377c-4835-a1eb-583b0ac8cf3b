package com.howbuy.crm.hb.web.controller.prebook;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.Maps;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.crm.base.*;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.doubletrade.dto.NeedDoubleResult;
import com.howbuy.crm.doubletrade.dto.ValidateDoubleTradeDTO;
import com.howbuy.crm.doubletrade.request.ValidateDoubleTradeVo;
import com.howbuy.crm.doubletrade.service.DoubleTradeRelatedService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.ComboboxItem;
import com.howbuy.crm.hb.web.dto.prebook.*;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.hkcust.dto.HkCustBankInfoDto;
import com.howbuy.crm.hkcust.service.HkAcctInfoService;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.prebook.dto.CmPreBookProductInfo;
import com.howbuy.crm.prebook.dto.FcclAppendPreBookInfoDto;
import com.howbuy.crm.prebook.dto.FcclPreBookInfoDTO;
import com.howbuy.crm.prebook.dto.PreBookProdRelatedDto;
import com.howbuy.crm.prebook.request.HzAmtLockRequest;
import com.howbuy.crm.prebook.request.ModifyBuyAmtRequest;
import com.howbuy.crm.prebook.request.PreBookBatchUpdateTradeDateRequest;
import com.howbuy.crm.prebook.request.PrebookTradeDealRequest;
import com.howbuy.crm.prebook.service.*;
import com.howbuy.crm.prebook.vo.CmPreBookInsertVo;
import com.howbuy.crm.prosale.dto.BankInfo;
import com.howbuy.crm.prosale.dto.SearchProdResultDto;
import com.howbuy.crm.prosale.dto.SearchProdVo;
import com.howbuy.crm.prosale.request.GetInfoByParamRequest;
import com.howbuy.crm.prosale.response.GetCustBankInfoResponse;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import com.howbuy.crm.util.exception.BusinessException;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:(预约 的 controller)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/5/19 14:39
 * @since JDK 1.8
 */
@Slf4j
@Controller
@RequestMapping(value = "/prebook")
public class PrebookController extends BaseController {


    @Autowired
    private PreBookService preBookService;

    @Autowired
    private DoubleTradeRelatedService doubleTradeRelatedService;

    @Autowired
    private JjxxInfoService jjxxInfoService;
    @Autowired
    private PrebookBusinessService prebookBusinessService;
    @Autowired
    private PrebookBasicInfoService prebookBasicInfoService;

    @Autowired
    private QueryPreBookService queryPreBookService;

    @Autowired
    private HkAcctInfoService hkAcctInfoService;


    @Autowired
    private FcclPrebookService fcclPrebookService;

    @Autowired
    private PrebookValidateService prebookValidateService;

    @Autowired
    private HzPreBookService hzPreBookService;

    @Autowired
    private PrebookConfigService prebookConfigService;



    /**
     * @description:(跳转到 新增预约 页面)
     * @param channelType 预约产品类型 {@link PreBookChannelEnum}
     * @return org.springframework.web.servlet.ModelAndView
     * @author: haoran.zhang
     * @date: 2023/5/19 14:50
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/initadd")
    public ModelAndView initAdd(@RequestParam(value = "channelType") String channelType,
                                @RequestParam(value = "tradeType",defaultValue = "1") String tradeType,
                                @RequestParam(value = "defineCustNo",required=false) String defineCustNo ,
                                @RequestParam(value = "isCurConsCode" ,defaultValue="0") String isCurConsCode){
        Map<String,Object> returnMap= Maps.newHashMap();

        //必输项
        PreBookChannelEnum preBookChannelEnum=PreBookChannelEnum.getEnum(channelType);
        returnMap.put("channelType", preBookChannelEnum.getCode());
        returnMap.put("channelTypeDesc", preBookChannelEnum.getDescription());
        returnMap.put("disChannelCode", preBookChannelEnum.getDisChannelCode());


        //如果为指定 客户号
        if(StringUtil.isNotNullStr(defineCustNo)){
            returnMap.put("defineCustNo", defineCustNo);
        }
        if(YesNoEnum.Y.getCode().equals(isCurConsCode)){
            returnMap.put("defineConsCode", getLoginUserId());
        }

        //默认选中  购买|追加|赎回 tab
        returnMap.put("defineTradeType", tradeType);


        return new ModelAndView("prebook/initAdd",returnMap);
    }

    

    /**
     * @description:(新增页面  购买|追加)
     * @param channelType	
     * @param tradeType
     * @param defineConsCode 指定 该投顾的客户的预约
     * @param defineCustNo 指定 该客户的预约
     * @return org.springframework.web.servlet.ModelAndView
     * @author: haoran.zhang
     * @date: 2023/5/24 14:44
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/initaddforbuy")
    public ModelAndView initAddForBuy(@RequestParam(value = "channelType") String channelType,
                                      @RequestParam(value = "tradeType") String tradeType,
                                      @RequestParam(value = "defineCustNo",required=false) String defineCustNo ,
                                      @RequestParam(value = "defineConsCode",required=false) String defineConsCode){
        Map<String,Object> returnMap= Maps.newHashMap();

        PreBookChannelEnum preBookChannelEnum=PreBookChannelEnum.getEnum(channelType);
        returnMap.put("channelType", preBookChannelEnum.getCode());
        returnMap.put("channelTypeDesc", preBookChannelEnum.getDescription());
        returnMap.put("disChannelCode", preBookChannelEnum.getDisChannelCode());
        //购买 追加
        returnMap.put("tradeType", tradeType);

        //如果为指定 客户号
        if(StringUtil.isNotNullStr(defineCustNo)){
            returnMap.put("defineCustNo", defineCustNo);
            //defineCustName
            ConscustInfoDomain custInfo= getCustInfoByCustNo(defineCustNo);
            returnMap.put("defineCustName", custInfo.getCustname());
            returnMap.put("linkMan", custInfo.getLinkman());
        }
        if(StringUtil.isNotNullStr(defineConsCode)){
            returnMap.put("defineConsCode", defineConsCode);
        }

        //海外产品 预约， 页面单独处理
        if(PreBookChannelEnum.HW==preBookChannelEnum){
            return new ModelAndView("prebook/initAddForHk",returnMap);
        }else{
            //高端代销产品、直销产品 预约 ，页面单独处理
            return new ModelAndView("prebook/initAddForBuy",returnMap);
        }
    }


    /**
     * @description:(新增页面   分次call 认缴预约 。首次购买 )
     * @param defineConsCode 指定 该投顾的客户的预约
     * @param defineCustNo 指定 该客户的预约
     * @return org.springframework.web.servlet.ModelAndView
     * @author: haoran.zhang
     * @date: 2024年2月26日
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/initaddforsubscribe")
    public ModelAndView initAddForSubscribe(@RequestParam(value = "defineCustNo",required=false) String defineCustNo ,
                                            @RequestParam(value = "defineConsCode",required=false) String defineConsCode){
        Map<String,Object> returnMap= Maps.newHashMap();

        //分次call 首次，固定为：HZ
        PreBookChannelEnum preBookChannelEnum=PreBookChannelEnum.HZ ;
        returnMap.put("channelType", preBookChannelEnum.getCode());
        returnMap.put("channelTypeDesc", preBookChannelEnum.getDescription());
        returnMap.put("disChannelCode", preBookChannelEnum.getDisChannelCode());

        //分次call 首次，固定为：购买
        returnMap.put("tradeType", PreBookTradeTypeEnum.BUY.getCode());

        //如果为指定 客户号
        if(StringUtil.isNotNullStr(defineCustNo)){
            returnMap.put("defineCustNo", defineCustNo);
            //defineCustName
            ConscustInfoDomain custInfo= getCustInfoByCustNo(defineCustNo);
            returnMap.put("defineCustName", custInfo.getCustname());
            returnMap.put("linkMan", custInfo.getLinkman());
        }
        if(StringUtil.isNotNullStr(defineConsCode)){
            returnMap.put("defineConsCode", defineConsCode);
        }

        return new ModelAndView("prebook/initAddForSubscribe",returnMap);
    }


    /**
     * @description:(新增页面   分次call 实缴预约 。非首次购买 )
     * @param mainCallId 指定 分次call 的 认缴ID
     * @return org.springframework.web.servlet.ModelAndView
     * @author: haoran.zhang
     * @date: 2024年2月26日
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/initaddforfcclpaid")
        public ModelAndView initAddForFcclPaid(@RequestParam(value = "mainCallId",required=true) String mainCallId){


        ReturnMessageDto<FcclAppendPreBookInfoDto>  fcclResp=fcclPrebookService.getInsertVoByMainCallId(new BigDecimal(mainCallId));
        if(!fcclResp.isSuccess()){
            throw new BusinessException(fcclResp.getReturnMsg());
        }

        FcclAppendPreBookInfoDto model=fcclResp.getReturnObject();
        //实缴  预约的 新增属性
        CmPreBookInsertVo insertVo=model.getInsertVo();

        Map<String,Object> map = new HashMap<String,Object>();
        map.put("mainCallId", mainCallId);
        //认缴金额
        map.put("subscribeAmt", model.getSubscribeAmt() == null ? 0 : model.getSubscribeAmt().divide(new BigDecimal(10000)));
        //预约交易类型
        map.put("tradeType", insertVo.getTradeType());

        //累计实缴金额:计算策略 ：预约 状态=已确认 且 打款状态=已到账确认  的 REALPAYAMT 汇总
        BigDecimal hasBuyAmt=model.getHasBuyAmt() == null ? BigDecimal.ZERO : model.getHasBuyAmt();
        map.put("hasBuyAmt", hasBuyAmt.divide(new BigDecimal(10000)));
        //首次预约 creator
        String firstPreCreator = model.getFirstPreCreator();
        ConsOrgCache consOrgCache=ConsOrgCache.getInstance();
        String orgCode = consOrgCache.getCons2OutletMap().get(firstPreCreator);
        //所属部门
        map.put("orgName", consOrgCache.getAllOrgMap().get(orgCode));
        //所属投顾
        map.put("consName", consOrgCache.getAllUserMap().get((Util.ObjectToString(firstPreCreator))));
        //客户名称
        map.put("custName", model.getCustName());
        map.put("custNo", insertVo.getConscustno());

        //产品名称
        map.put("fundName", model.getFundName());
        map.put("fundCode", insertVo.getPcode());
        //预约类型
        String preType=insertVo.getPretype();
        map.put("preTypeName", ConstantCache.getInstance().getConstantKeyVal("pretype").get(preType));
        map.put("preType", preType);
        //货币
        map.put("currency", insertVo.getCurrency());

        //联系人
        map.put("realBuyMan", insertVo.getRealbuyman());


        String bankName=StringUtil.isEmpty(insertVo.getBankName())? "":insertVo.getBankName();
        //开户银行
        map.put("bankName", bankName);

        String bankProv=StringUtil.isEmpty(insertVo.getBankProv())? "":insertVo.getBankProv();
        map.put("bankProv", bankProv);
        map.put("bankProvName", ConstantCache.getInstance().getProvCityMap().get(bankProv));

        String bankCity=StringUtil.isEmpty(insertVo.getBankCity())? "":insertVo.getBankCity();
        map.put("bankCity", bankCity);
        map.put("bankCityName", ConstantCache.getInstance().getProvCityMap().get(bankCity));
        map.put("bankAddr", StringUtil.isEmpty(insertVo.getBankAddr())? "":insertVo.getBankAddr());
        map.put("bankAcct", StringUtil.isEmpty(insertVo.getBankAcct())? "":insertVo.getBankAcct());

        return new ModelAndView("prebook/initAddForFcclPaid",map);
    }




    /**
     * @description:(新增页面  赎回)
     * @param channelType
     * @param defineConsCode 指定 该投顾的客户的预约
     * @param defineCustNo 指定 该客户的预约
     * @return org.springframework.web.servlet.ModelAndView
     * @author: haoran.zhang
     * @date: 2023/5/24 14:44
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/initaddforsell")
    public ModelAndView initAddForSell(@RequestParam(value = "channelType") String channelType,
                                       @RequestParam(value = "defineCustNo",required=false) String defineCustNo ,
                                       @RequestParam(value = "defineConsCode" ,required=false) String defineConsCode){
        Map<String,Object> returnMap= Maps.newHashMap();

        PreBookChannelEnum preBookChannelEnum=PreBookChannelEnum.getEnum(channelType);
        returnMap.put("channelType", preBookChannelEnum.getCode());
        returnMap.put("channelTypeDesc", preBookChannelEnum.getDescription());
        returnMap.put("disChannelCode", preBookChannelEnum.getDisChannelCode());

        //如果为指定 客户号
        if(StringUtil.isNotNullStr(defineCustNo)){
            returnMap.put("defineCustNo", defineCustNo);
            //defineCustName
            ConscustInfoDomain custInfo= getCustInfoByCustNo(defineCustNo);
            returnMap.put("defineCustName", custInfo.getCustname());
        }
        if(StringUtil.isNotNullStr(defineConsCode)){
            returnMap.put("defineConsCode", defineConsCode);
        }
        return new ModelAndView("prebook/initAddForSell",returnMap);
    }


    /**
     * @description:(按照预约类型 查找 产品列表)
     * @param term 搜索内容
     * @param channelType  产品类型  {@link com.howbuy.crm.base.PreBookChannelEnum}
     * @param isFccl 是否分次call  1-分次call  0-否  空标识无约束
     * @return java.util.List<com.howbuy.crm.prosale.dto.SearchProdResultDto>
     * @author: haoran.zhang
     * @date: 2023/5/22 10:10
     * @since JDK 1.8
     */
    @RequestMapping("/searchprodlistforbuy.do")
    @ResponseBody
    public List<SearchProdResultDto> searchProdListForBuy(@RequestParam(value = "term") String term,
                                                          @RequestParam(value = "isFccl") String isFccl,
                                                          @RequestParam(value = "channelType") String channelType){
        //非 分次call
        PreBookChannelEnum prechannelEnum=PreBookChannelEnum.getEnum(channelType);
        SearchProdVo searchProdVo=new SearchProdVo();
        searchProdVo.setFundCodeOrName(term);
        if(!StringUtils.isEmpty(isFccl)){
            searchProdVo.setIsfccl(YesOrNoEnum.YES.getCode().equals(isFccl));
        }else{
            searchProdVo.setIsfccl(null);
        }

        List<SearchProdResultDto> prodList=preBookService.getSearchedProdListForBuy(prechannelEnum,searchProdVo);
        return prodList;
    }


    /**
     * @description:(校验双录相关属性)
     * @param validateVo
     * @return com.howbuy.crm.base.ReturnMessageDto<com.howbuy.crm.hb.web.dto.prebook.ValidateDoubleTradePageDto>
     * @author: haoran.zhang
     * @date: 2024/6/19 18:06
     * @since JDK 1.8
     */
    @RequestMapping("/validatedoubletrade.do")
    @ResponseBody
    public ReturnMessageDto<ValidateDoubleTradePageDto>   validateDoubleTradeByVo(ValidateDoubleTradeVo validateVo){

        ReturnMessageDto<ValidateDoubleTradeDTO> resp=doubleTradeRelatedService.validateDoubleTradeByVo(validateVo);
        if(!resp.isSuccess()){
            return  ReturnMessageDto.fail(resp.getReturnMsg());
        }

        //转义对象关系
        ValidateDoubleTradeDTO validateDto=resp.getReturnObject();
        ValidateDoubleTradePageDto returnDto=new ValidateDoubleTradePageDto();

        returnDto.setDoubleTypeItemList(Lists.newArrayList());
        validateDto.getDoubleTypeEnumList().stream().forEach(doubleTypeEnum ->
                returnDto.getDoubleTypeItemList().add(new ComboboxItem(doubleTypeEnum.getCode(),doubleTypeEnum.getDescription()))
        );
        NeedDoubleResult needDoubleResult=validateDto.getNeedDoubleResult();
        if(needDoubleResult!=null){
            returnDto.setNeedDouble(needDoubleResult.isNeedDouble());
            returnDto.setSituationEnum(needDoubleResult.getSituationEnum());
            returnDto.setAgelimitflag(needDoubleResult.getAgelimitflag());
        }else{
            //默认值：需要双录   作为拦截使用
            returnDto.setNeedDouble(true);
        }

        returnDto.setDoubleTempId(validateDto.getDoubleTempId());
        return ReturnMessageDto.ok("",returnDto);
    }



    @RequestMapping("/getcustrelatedinfo.do")
    @ResponseBody
    public ReturnMessageDto<PreBookCustRelatedPageDto> getCustRelatedInfo(String custNo,
                                                                          String tradeType){


        Assert.notNull(custNo);
        PreBookTradeTypeEnum tradeTypeEnum=PreBookTradeTypeEnum.getEnum(tradeType);
        Assert.notNull(tradeTypeEnum);

        ConscustInfoDomain custInfo=getCustInfoByCustNo(custNo);
        if(custInfo==null){
            return  ReturnMessageDto.fail("客户不存在！");
        }

        ReturnMessageDto<PreBookCustRelatedPageDto> pageResult=ReturnMessageDto.ok();
        PreBookCustRelatedPageDto relatedDto = new PreBookCustRelatedPageDto();
        relatedDto.setCustNo(custInfo.getConscustno());
        relatedDto.setHboneNo(custInfo.getHboneno());
        relatedDto.setInvestType(custInfo.getInvsttype());
        relatedDto.setCustName(custInfo.getCustname());
        relatedDto.setConsCode(custInfo.getConscode());
        relatedDto.setLinkMan(custInfo.getLinkman());
        pageResult.setReturnObject(relatedDto);
        return pageResult;
    }





    /**
     * @description:(新增预约 选中 产品和客户信息后  需要的属性)
     * @param custNo 客户号
     * @param prodCode	产品代码
     * @param tradeType 预约交易类型
     * @return com.howbuy.crm.hb.web.dto.prebook.PreBookProdRelatedPageDto
     * @author: haoran.zhang
     * @date: 2023/5/22 15:07
     * @since JDK 1.8
     */
    @RequestMapping("/getprodrelatedinfo.do")
    @ResponseBody
    public ReturnMessageDto<PreBookProdRelatedPageDto> getProdRelatedInfo(String custNo,
                                                                          String prodCode,
                                                                          String tradeType){
        Assert.notNull(custNo);
        Assert.notNull(prodCode);
        PreBookTradeTypeEnum tradeTypeEnum=PreBookTradeTypeEnum.getEnum(tradeType);
        Assert.notNull(tradeTypeEnum);

        ReturnMessageDto<PreBookProdRelatedPageDto> returnDto=ReturnMessageDto.ok();


        ReturnMessageDto<PreBookProdRelatedDto>  result=prebookBusinessService.getProdRelatedInfo(custNo, prodCode, tradeType,getLoginUserId());

        returnDto.setReturnCode(result.getReturnCode());
        returnDto.setReturnMsg(result.getReturnMsg());

        PreBookProdRelatedDto relatedDto=result.getReturnObject();
        if(relatedDto!=null){
            PreBookProdRelatedPageDto pageDto=new PreBookProdRelatedPageDto();
            BeanUtils.copyProperties(relatedDto,pageDto);
            List<PreTypeEnum> preTypeEnumList=relatedDto.getPreTypeEnumList();
            //电子成单  纸质成单 下拉框
            preTypeEnumList.stream()
                    .forEach(preTypeEnum -> pageDto.getPreTypeItemList().add(
                            new ComboboxItem(preTypeEnum.getCode(),preTypeEnum.getDescription()))
                            );
            //默认选中的 预约类型
            //只有一个选项，选中
            if(preTypeEnumList.size()==1){
                pageDto.setDefaultPreType(preTypeEnumList.get(0).getCode());
            }
            //多个选项，选中 :电子成单
            if(preTypeEnumList.size()>1 && preTypeEnumList.contains(PreTypeEnum.ELECTIC)){
                pageDto.setDefaultPreType(PreTypeEnum.ELECTIC.getCode());
            }

            //赎回类别
            List<RedeemModeEnum> redeemModeEnumList=relatedDto.getRedeemModeEnumList();
            redeemModeEnumList.stream().forEach(redeemModeEnum -> pageDto.getRedeemItemList().add(new ComboboxItem(redeemModeEnum.getCode(),redeemModeEnum.getDescription())));

            //默认赋值  赎回类别
//            。若仅有1个选项，默认选择，否则默认选中“按份额赎回”
            if(redeemModeEnumList.size()==1){
                pageDto.setDefaultRedeemMode(redeemModeEnumList.get(0).getCode());
            }

            List<String> availList = relatedDto.getCalendarList();
            if(CollectionUtils.isNotEmpty(availList)) {
                pageDto.getPretrddtList().add(buildSelectMap("","请选择"));
                availList.stream().forEach(tradeDt -> {
                    pageDto.getPretrddtList().add(buildSelectMap(tradeDt, tradeDt));
                });
            }
            returnDto.setReturnObject(pageDto);
        }

        return returnDto;
    }




    @RequestMapping("/getcurrency")
    @ResponseBody
    public String getCurrency(String  prodCode) {
        JjxxInfo jjxxInfo=jjxxInfoService.getJjxxByJjdm(prodCode);
        //之前有逻辑： hb_constant中如果没有配置 currency 。 不展示。  不再使用
        return StringUtil.replaceNull(jjxxInfo.getHbzl());
    }



    /**
     * @description:(进入[打款]页面 前置校验)
     * @param preId
     * @return org.springframework.web.servlet.ModelAndView
     * @author: haoran.zhang
     * @date: 2023/6/14 15:03
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/validateBeforePay")
    public ReturnMessageDto<String> validateBeforePay(@RequestParam(value = "preId") String preId){
        Assert.notNull(preId);
//        CmPreBookProductInfo preBookInfo=prebookBasicInfoService.getPreBookById(new BigDecimal(preId));
        //打款入口已拦截海外产品， 此处校验删除。校验入口 仍然保留
        return ReturnMessageDto.ok();
    }


    /**
     * @description:(进入[申请折扣]页面 前置校验)
     * @param preId
     * @return org.springframework.web.servlet.ModelAndView
     * @author: haoran.zhang
     * @date: 2023/6/14 15:03
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/validateBeforeApplyDisCount")
    public ReturnMessageDto<String> validateBeforeApplyDisCount(@RequestParam(value = "preId") String preId){
        Assert.notNull(preId);
        return prebookValidateService.validateBeforeApplyDisCount(new BigDecimal(preId));
    }

    /**
     * @description:(进入[交易确认]页面 前置校验)
     * @param preId
     * @return org.springframework.web.servlet.ModelAndView
     * @author: haoran.zhang
     * @date: 2023/6/14 15:03
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/validateBeforeConfirmTrade")
    public ReturnMessageDto<String> validateBeforeConfirmTrade(@RequestParam(value = "preId") String preId){
        Assert.notNull(preId);
        CmPreBookProductInfo preBookInfo=prebookBasicInfoService.getPreBookById(new BigDecimal(preId));

        //拦截香港产品
        if(PreBookArchTypeEnum.HW.getCode().equals(preBookInfo.getArchType())){
            return ReturnMessageDto.fail("不支持海外产品操作！") ;
        }
        return ReturnMessageDto.ok();
    }


    /**
     * @description:(新增预约 选中 产品和客户信息后  需要的属性)
     * @param custNo 客户号
     * @param channelType  产品类型  {@link com.howbuy.crm.base.PreBookChannelEnum}
     * @return com.howbuy.crm.hb.web.dto.prebook.PreBookProdRelatedPageDto
     * @author: haoran.zhang
     * @date: 2023/5/22 15:07
     * @since JDK 1.8
     */
    @RequestMapping("/getbankacctlist.do")
    @ResponseBody
    public List<CustBankInfoDto> getBankAcctList(@RequestParam(value = "custNo") String custNo,
                                                 @RequestParam(value = "channelType") String channelType){
        Assert.notNull(custNo);
        PreBookChannelEnum channelEnum=PreBookChannelEnum.getEnum(channelType);
        Assert.notNull(channelEnum);


        List<CustBankInfoDto> bankList= Lists.newArrayList();

        if(channelEnum==PreBookChannelEnum.HW){
            List<HkCustBankInfoDto> bankInfoDtoList= hkAcctInfoService.getHkBankAcctList(custNo);
            if(CollectionUtils.isNotEmpty(bankInfoDtoList)){
                bankInfoDtoList.stream().forEach(hkDto->{
                    CustBankInfoDto custBankInfoDto = new CustBankInfoDto();
                    custBankInfoDto.setBankCode(hkDto.getBankCode());
                    custBankInfoDto.setBankName(hkDto.getBankName());
                    custBankInfoDto.setBankChineseName(hkDto.getBankChineseName());
                    custBankInfoDto.setBankRegionCode(hkDto.getBankRegionCode());
                    custBankInfoDto.setBankAcctDigest(hkDto.getBankAcctDigest());
                    custBankInfoDto.setBankAcctMask(hkDto.getBankAcctMask());
                    custBankInfoDto.setBankAcctName(hkDto.getBankAcctName());
                    custBankInfoDto.setBankprov(null);
                    custBankInfoDto.setBankcity(null);
                    custBankInfoDto.setBankaddr(null);
                    custBankInfoDto.setBankAcct(null);
                    custBankInfoDto.setCpAcctNo(hkDto.getHkCpAcctNo());
                    bankList.add(custBankInfoDto);
                });
            }
        }else{
            GetInfoByParamRequest bankreq = new GetInfoByParamRequest();
            bankreq.setConscustno(custNo);
            GetCustBankInfoResponse bankresponse = queryPreBookService.getCustBankInfo(bankreq);
            if(BaseConstantEnum.SUCCESS.getCode().equals(bankresponse.getReturnCode())){
                List<BankInfo> banklist= bankresponse.getBankInfoList();
                banklist.stream().forEach(bankResp->{
                    CustBankInfoDto custBankInfoDto = new CustBankInfoDto();
                    custBankInfoDto.setBankCode(bankResp.getBankcode());
                    custBankInfoDto.setBankName(bankResp.getBankname());
                    custBankInfoDto.setBankChineseName(null);
                    custBankInfoDto.setBankRegionCode(null);
                    custBankInfoDto.setBankAcctDigest(null);
                    custBankInfoDto.setBankAcctMask(MaskUtil.maskBankAcct(bankResp.getBankacct()));
                    custBankInfoDto.setBankAcctName(null);
                    custBankInfoDto.setBankprov(bankResp.getBankprov());
                    custBankInfoDto.setBankcity(bankResp.getBankcity());
                    custBankInfoDto.setBankaddr(bankResp.getBankaddr());
                    custBankInfoDto.setBankAcct(bankResp.getBankacct());
                    custBankInfoDto.setCpAcctNo(bankResp.getCpAcctNo());
                    bankList.add(custBankInfoDto);
                });
            }
        }
        return bankList;
    }


    /**
     * @api {POST} /prebook/validateBeforeHZAmtLock validateBeforeHZAmtLock()
     * @apiVersion 1.0.0
     * @apiGroup PrebookController
     * @apiName validateBeforeHZAmtLock()
     * @apiDescription 好臻金额锁定及修改【前置校验】
     * @apiParam (请求参数) {String} preIdListStr
     * @apiParamExample 请求参数示例
     * preIdList=eGjoUVRba
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"gqTcSIT","returnMsg":"jCFmQ9Br","returnObject":"N5","returnList":["XH72hXAAoh"]}
     */
    @ResponseBody
    @PostMapping("/validateBeforeHZAmtLock")
    public ReturnMessageDto<String> validateBeforeHZAmtLock(String preIdListStr) {
        // 预约id列表
        List<BigDecimal> preIdList = getPreIdListByStr(preIdListStr);
        return prebookValidateService.validateBeforeHZAmtLock(preIdList);
    }


    /**
     * @api {POST} /prebook/validatebeforeupdateprebooktradedate validateBeforeUpdatePrebookTradeDate()
     * @apiVersion 1.0.0
     * @apiGroup PrebookController
     * @apiName validateBeforeUpdatePrebookTradeDate()
     * @apiDescription 批量修改预计交易日期 前置校验
     * @apiParam (请求参数) {String} preIds
     * @apiParam (请求参数) {String} tradeDate
     * @apiParamExample 请求参数示例
     * preIds=7hmk&tradeDate=9RWhh
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"CRSlVI9t","returnMsg":"ZrVqj","returnObject":"By3yhhf9","returnList":["7hQwY"]}
     */
    @ResponseBody
    @PostMapping("/validatebeforeupdateprebooktradedate")
    public ReturnMessageDto<String> validateBeforeUpdatePrebookTradeDate(String preIds, String tradeDate) {
        // 预约id列表
        List<BigDecimal> preIdList = getPreIdListByStr(preIds);
        return prebookValidateService.validateBeforeUpdatePrebookTradeDate(preIdList, tradeDate);
    }


    /**
     * @api {POST} /prebook/updatePrebookTradeDate.do updatePrebookTradeDate()
     * @apiVersion 1.0.0
     * @apiGroup PrebookController
     * @apiName updatePrebookTradeDate()
     * @apiDescription 批量修改预计交易日期
     * @apiParam (请求参数) {String} preIds
     * @apiParam (请求参数) {String} tradeDate
     * @apiParamExample 请求参数示例
     * preIds=WmrgogM&tradeDate=GVYy2vus
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"tdlO6wn1P","returnMsg":"MV","returnObject":"S3M0y","returnList":["IxK"]}
     */
    @ResponseBody
    @PostMapping("/updatePrebookTradeDate.do")
    public ReturnMessageDto<String> updatePrebookTradeDate(String preIds, String tradeDate, HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute("loginUser");
        // 预约id列表
        List<BigDecimal> preIdList = getPreIdListByStr(preIds);

        PreBookBatchUpdateTradeDateRequest updateTradeDateRequest = new PreBookBatchUpdateTradeDateRequest();
        updateTradeDateRequest.setPreIdList(preIdList);
        updateTradeDateRequest.setUpdateTradeDate(tradeDate);
        updateTradeDateRequest.setOperator(user.getUserId());

        return prebookBusinessService.batchUpdateTradeDate(updateTradeDateRequest);
    }

    /**
     * @api {POST} /prebook/hzAmtLock hzAmtLock()
     * @apiVersion 1.0.0
     * @apiGroup PrebookController
     * @apiName hzAmtLock()
     * @apiDescription 好臻金额锁定及修改
     * @apiParam (请求参数) {String} preIdListStr
     * @apiParam (请求参数) {String} isHzAmtLock
     * @apiParamExample 请求参数示例
     * isHzAmtLock=uJyoW2eNV&preIdList=14W3jzHGL5
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"3DOyl61","returnMsg":"KGAvnXWa","returnObject":"5U","returnList":["m"]}
     */
    @ResponseBody
    @PostMapping("/hzAmtLock")
    public ReturnMessageDto<String> hzAmtLock(HttpServletRequest request, String preIdListStr, String isHzAmtLock) {
        // 预约id列表
        List<BigDecimal> preIdList = getPreIdListByStr(preIdListStr);

        HzAmtLockRequest hzAmtLockRequest = new HzAmtLockRequest();
        hzAmtLockRequest.setPreIdList(preIdList);
        // 认缴金额是否锁定
        hzAmtLockRequest.setSubscribeAmtLock(isHzAmtLock);
        // 操作人
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        hzAmtLockRequest.setOperator(userlogin.getUserId());

        return hzPreBookService.executeAddHzAmtLock(hzAmtLockRequest);
    }


    /**
     * @api {POST} /prebook/validateBeforeHZAmtUnlock validateBeforeHZAmtUnlock()
     * @apiVersion 1.0.0
     * @apiGroup PrebookController
     * @apiName validateBeforeHZAmtUnlock()
     * @apiDescription 删除好臻金额锁定及修改【前置校验】
     * @apiParam (请求参数) {String} preIdListStr
     * @apiParamExample 请求参数示例
     * preIdList=bbym
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"Mzx","returnMsg":"Tk9R","returnObject":"q1OXr","returnList":["rrCQ"]}
     */
    @ResponseBody
    @PostMapping("/validateBeforeHZAmtUnlock")
    public ReturnMessageDto<String> validateBeforeHZAmtUnlock(String preIdListStr) {
        // 预约id列表
        List<BigDecimal> preIdList = getPreIdListByStr(preIdListStr);
        return prebookValidateService.validateBeforeHZAmtUnlock(preIdList);
    }


    /**
     * @api {POST} /prebook/hzAmtUnlock hzAmtUnlock()
     * @apiVersion 1.0.0
     * @apiGroup PrebookController
     * @apiName hzAmtUnlock()
     * @apiDescription 删除好臻金额锁定及修改
     * @apiParam (请求参数) {String} preIdListStr
     * @apiParamExample 请求参数示例
     * preIdList=6O4SkhS
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"6HqntuXyV","returnMsg":"o7pk","returnObject":"iX","returnList":["8QwJN1O"]}
     */
    @ResponseBody
    @PostMapping("/hzAmtUnlock")
    public ReturnMessageDto<String> hzAmtUnlock(HttpServletRequest request, String preIdListStr) {
        List<BigDecimal> preIdList = getPreIdListByStr(preIdListStr);

        HzAmtLockRequest hzAmtLockRequest = new HzAmtLockRequest();
        hzAmtLockRequest.setPreIdList(preIdList);

        User userlogin = (User) request.getSession().getAttribute("loginUser");
        hzAmtLockRequest.setOperator(userlogin.getUserId());

         return hzPreBookService.executeRemoveHzAmtLock(hzAmtLockRequest);
    }

    /**
     * @description: 获取预约id列表
     * @param preIdListStr	预约id列表string
     * @return java.util.List<java.math.BigDecimal> 预约id列表
     * @author: jin.wang03
     * @date: 2024/2/23 18:00
     * @since JDK 1.8
     */
    private static List<BigDecimal> getPreIdListByStr(String preIdListStr) {
        // 预约id列表
        List<String> list = Arrays.asList(preIdListStr.split(","));
        // 将预约id列表转换为BigDecimal类型
        return list.stream().map(BigDecimal::new).collect(Collectors.toList());
    }


    /**
     * @description:(根据产品代码判断是否分次call。 1-是 0-否)
     * @param prodCode
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2025/5/23 15:44
     * @since JDK 1.8
     */
    private String getFcclFlagByProdCode(String prodCode){
        // 是否分次call产品：1-是 0-否
        String isfccall = YesOrNoEnum.NO.getCode();
        ReturnMessageDto<String>  fcclFlagResp=prebookConfigService.getFcclFlag(prodCode);
        if(fcclFlagResp.isSuccess() &&  YesOrNoEnum.YES.getCode().equals(fcclFlagResp.getReturnObject())){
            isfccall = YesOrNoEnum.YES.getCode();
        }
        return isfccall;
    }

    /**
     * @api {GET} /prebook/modifyBuyAmt modifyBuyAmt()
     * @apiVersion 1.0.0
     * @apiGroup PrebookController
     * @apiName modifyBuyAmt()
     * @apiDescription 修改预约金额页面
     * @apiParam (请求参数) {String} id
     * @apiParamExample 请求参数示例
     * id=sbwL3Kou
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":true,"status":"INSUFFICIENT_SPACE_ON_RESOURCE"}
     */
    @ResponseBody
    @GetMapping("/modifyBuyAmt")
    public ModelAndView modifyBuyAmt(String id) {
        Map<String, Object> returnMap = Maps.newHashMap();

        BigDecimal preId = new BigDecimal(id);
        CmPreBookProductInfo preBookInfo = prebookBasicInfoService.getPreBookById(preId);
        returnMap.put("record",preBookInfo);

        String prodCode = preBookInfo.getPcode();
        JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(prodCode);
        // 是否分次call (0-否 1-是)
        String fccl = getFcclFlagByProdCode(prodCode);
        returnMap.put("fccl", fccl);
        returnMap.put("prodName", jjxxInfo.getJjjc());
        // 客户基本信息
        ConscustInfoDomain custInfo = getCustInfoByCustNo(preBookInfo.getConscustno());
        returnMap.put("custName", custInfo.getCustname());
        // 货币
        returnMap.put("currencyDesc",CurrencyEnum.getDescription(preBookInfo.getCurrency()));

        // 页面展示的金额单位是万元，需要将金额转换为万元
        if (StaticVar.NO.equals(fccl)) {
            // 非分次call
            returnMap.put("buyAmt", preBookInfo.getBuyamt().divide(new BigDecimal(10000), 6, RoundingMode.HALF_UP).toString());
        } else {
            // 分次call
            FcclPreBookInfoDTO fcclPreBookInfo = fcclPrebookService.getFcclPreBookInfoByPreId(preId);
            returnMap.put("totalAmt", fcclPreBookInfo.getManyCallInfo().getTotalamt().divide(new BigDecimal(10000), 6, RoundingMode.HALF_UP).toString());
            returnMap.put("buyAmt", preBookInfo.getBuyamt().divide(new BigDecimal(10000), 6, RoundingMode.HALF_UP).toString());
            // 是否是首次实缴
            returnMap.put("isFirstRealPay", preId.equals(fcclPreBookInfo.getManyCallInfo().getFirstpreid()));
        }

        return new ModelAndView("prebook/modifyBuyAmt", returnMap);
    }


    /**
     * @api {POST} /prebook/validateDateBeforeModifyBuyAmt validateDateBeforeModifyBuyAmt()
     * @apiVersion 1.0.0
     * @apiGroup PrebookController
     * @apiName validateDateBeforeModifyBuyAmt()
     * @apiDescription 新增validateDateBeforeModifyBuyAmt方法
     * @apiParam (请求参数) {Number} id 预约id
     * @apiParam (请求参数) {String} buyAmt 购买金额 (非分次call)
     * @apiParam (请求参数) {String} totalAmt 认缴金额 (分次call)
     * @apiParam (请求参数) {String} remark 备注
     * @apiParamExample 请求参数示例
     * buyAmt=zberM9nyC&totalAmt=F7xGa99o&remark=euVB0xh&id=9137.063869725196
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"wg5l1wv","returnMsg":"Be33aEb","returnObject":"g","returnList":["x3"]}
     */
    @ResponseBody
    @PostMapping("/validateDateBeforeModifyBuyAmt")
    public ReturnMessageDto<String> validateDateBeforeModifyBuyAmt(ModifyBuyAmtDTO modifyBuyAmtDTO) {
        ModifyBuyAmtRequest modifyBuyAmtRequest = new ModifyBuyAmtRequest();
        modifyBuyAmtRequest.setId(modifyBuyAmtDTO.getId());

        return  prebookValidateService.validateDateBeforeModifyBuyAmt(modifyBuyAmtRequest);
    }


    /**
     * @api {POST} /prebook/validatebeforerefund validateBeforeRefund()
     * @apiVersion 1.0.0
     * @apiGroup PrebookController
     * @apiName validateBeforeRefund()
     * @apiDescription 预约管理，退款的前置校验
     * @apiParam (请求参数) {Number} preId
     * @apiParam (请求参数) {String} operator
     * @apiParam (请求参数) {String} remark
     * @apiParam (请求参数) {Object} preBookPayVo
     * @apiParam (请求参数) {Number} preBookPayVo.realPayAmt
     * @apiParam (请求参数) {String} preBookPayVo.realPayAmtDt
     * @apiParam (请求参数) {String} preBookPayVo.expectTradeDt
     * @apiParam (请求参数) {Number} preBookPayVo.fee
     * @apiParam (请求参数) {Object} preBookUpdateVo
     * @apiParam (请求参数) {Number} preBookUpdateVo.id
     * @apiParam (请求参数) {String} preBookUpdateVo.preType
     * @apiParam (请求参数) {String} preBookUpdateVo.remarks
     * @apiParam (请求参数) {String} preBookUpdateVo.expectTradeDt
     * @apiParam (请求参数) {String} preBookUpdateVo.expectPayAmtDt
     * @apiParam (请求参数) {String} preBookUpdateVo.currency
     * @apiParam (请求参数) {Number} preBookUpdateVo.buyAmt
     * @apiParam (请求参数) {Number} preBookUpdateVo.sellVol
     * @apiParam (请求参数) {String} preBookUpdateVo.realBuyMan
     * @apiParam (请求参数) {String} preBookUpdateVo.operator
     * @apiParam (请求参数) {Object} bankVo
     * @apiParam (请求参数) {String} bankVo.bankAcct
     * @apiParam (请求参数) {String} bankVo.bankName
     * @apiParam (请求参数) {String} bankVo.bankCode
     * @apiParam (请求参数) {String} bankVo.bankAddr
     * @apiParam (请求参数) {String} bankVo.bankProv
     * @apiParam (请求参数) {String} bankVo.bankCity
     * @apiParam (请求参数) {Array} hkCpAcctNoList
     * @apiParam (请求参数) {Object} confirmTradeVo
     * @apiParam (请求参数) {Number} confirmTradeVo.manageFee
     * @apiParam (请求参数) {Number} confirmTradeVo.preformFee
     * @apiParamExample 请求参数示例
     * bankVo={"bankCode":"OFN","bankAcct":"eP0jAYsN96","bankName":"3pJGf0kuMI","bankAddr":"I5d5qG3F8p","bankProv":"7sEjujBzr","bankCity":"mAZztpHx"}&preId=2046.************&preBookUpdateVo={"buyAmt":7372.************,"preType":"dywN761vM","expectPayAmtDt":"WI","currency":"wPHOBfwL7","id":2622.************,"sellVol":7273.************,"expectTradeDt":"fgTxsdlM","realBuyMan":"Ixur7WfJ","remarks":"mS","operator":"m7A"}&preBookPayVo={"realPayAmt":2085.*************,"fee":8878.************,"realPayAmtDt":"0UHH5cfv","expectTradeDt":"qFqR5"}&confirmTradeVo={"manageFee":1896.*************,"preformFee":4027.************}&remark=Mg4bZ&hkCpAcctNoList=kmCpjKCg&operator=4f72uuuqlv
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"hi9g","returnMsg":"TD5UAf","returnObject":"yW8z","returnList":["AkPmtvlF"]}
     */
    @ResponseBody
    @PostMapping("/validatebeforerefund")
    public ReturnMessageDto<String> validateBeforeRefund(PrebookTradeDealRequest prebookRequest) {
        if (Objects.isNull(prebookRequest.getPreId())) {
            return ReturnMessageDto.fail("退款的预约id不能为空！");
        }
        return prebookValidateService.validateBeforeRefund(prebookRequest);
    }

    /**
     * @api {POST} /prebook/validateBeforeModifyBuyAmt validateBeforeModifyBuyAmt()
     * @apiVersion 1.0.0
     * @apiGroup PrebookController
     * @apiName validateBeforeModifyBuyAmt()
     * @apiDescription 修改预约金额的前置校验
     * @apiParam (请求参数) {Number} id 预约id
     * @apiParam (请求参数) {String} buyAmt 购买金额 (非分次call)
     * @apiParam (请求参数) {String} totalAmt 认缴金额 (分次call)
     * @apiParam (请求参数) {String} realPayAmt 实缴金额 (分次call)
     * @apiParam (请求参数) {String} remark 备注
     * @apiParamExample 请求参数示例
     * buyAmt=ShzTHSy&totalAmt=4ruCx2&realPayAmt=f2yD5HP&remark=UVc341Z0&id=8969.346665425564
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"Q6","returnMsg":"fO2E","returnObject":"cTrap","returnList":["fH2"]}
     */
    @ResponseBody
    @PostMapping("/validateBeforeModifyBuyAmt")
    public ReturnMessageDto<String> validateBeforeModifyBuyAmt(ModifyBuyAmtDTO modifyBuyAmtDTO) {
        ModifyBuyAmtRequest modifyBuyAmtRequest = new ModifyBuyAmtRequest();
        modifyBuyAmtRequest.setId(modifyBuyAmtDTO.getId());

        modifyBuyAmtRequest.setBuyAmt(transferAmt(modifyBuyAmtDTO.getBuyAmt()));
        modifyBuyAmtRequest.setTotalAmt(transferAmt(modifyBuyAmtDTO.getTotalAmt()));

        return  prebookValidateService.validateBeforeModifyBuyAmt(modifyBuyAmtRequest);
    }


    /**
     * @api {POST} /prebook/toModifyBuyAmt toModifyBuyAmt()
     * @apiVersion 1.0.0
     * @apiGroup PrebookController
     * @apiName toModifyBuyAmt()
     * @apiDescription 修改预约金额
     * @apiParam (请求参数) {Number} id 预约id
     * @apiParam (请求参数) {String} buyAmt 购买金额 (非分次call)
     * @apiParam (请求参数) {String} totalAmt 认缴金额 (分次call)
     * @apiParam (请求参数) {String} realPayAmt 实缴金额 (分次call)
     * @apiParam (请求参数) {String} remark 备注
     * @apiParamExample 请求参数示例
     * buyAmt=A&totalAmt=vKlh&realPayAmt=lHOCI&remark=w42XIDL&id=6942.487682703519
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"l","returnMsg":"VPTC78cQ","returnObject":"e","returnList":["J"]}
     */
    @ResponseBody
    @PostMapping("/toModifyBuyAmt")
    public ReturnMessageDto<String> toModifyBuyAmt(HttpServletRequest request, ModifyBuyAmtDTO modifyBuyAmtDTO) {
        ModifyBuyAmtRequest modifyBuyAmtRequest = new ModifyBuyAmtRequest();
        modifyBuyAmtRequest.setId(modifyBuyAmtDTO.getId());

        modifyBuyAmtRequest.setBuyAmt(transferAmt(modifyBuyAmtDTO.getBuyAmt()));
        modifyBuyAmtRequest.setTotalAmt(transferAmt(modifyBuyAmtDTO.getTotalAmt()));

        User userlogin = (User) request.getSession().getAttribute("loginUser");
        modifyBuyAmtRequest.setOperator(userlogin.getUserId());

        return prebookBusinessService.toModifyBuyAmt(modifyBuyAmtRequest);
    }

    /**
     * @description: 金额转换（将页面传过来的字符串，转为bigDecimal，并且单元从 万 换算成 元）
     * @param amt 金额（万）
     * @return java.math.BigDecimal 金额（元）
     * @author: jin.wang03
     * @date: 2024/2/27 13:33
     * @since JDK 1.8
     */
    private BigDecimal transferAmt(String amt) {
        if (StringUtils.isEmpty(amt)) {
            return null;
        }
        return new BigDecimal(amt).multiply(new BigDecimal(10000));
    }

}