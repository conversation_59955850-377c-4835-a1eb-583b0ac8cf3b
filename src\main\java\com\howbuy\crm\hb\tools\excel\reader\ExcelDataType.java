/**   
 * @Title: ExcelDataType.java 
 * @Package com.hb.crm.web.util.excel.reader 
 * @Description: TODO(用一句话描述该文件做什么) 
 * <AUTHOR>
 * @date 2016年4月29日 下午1:28:48 
 * @version V1.0   
 */

package com.howbuy.crm.hb.tools.excel.reader;

import com.howbuy.crm.hb.tools.excel.bean.BatchImpFile;
import com.howbuy.crm.hb.tools.excel.util.FundsArgumentUtils;

/**
 * @ClassName: ExcelDatType
 * @Description: 数据进行格式化
 * <AUTHOR>
 * @date 2016年4月29日 下午1:28:48
 * 
 */

public class ExcelDataType {

	/**
	 * @Fields DOUBLE_TYPE : double类型
	 */
	public final static String DOUBLE_TYPE = "double";
	
	/** 
	* @Fields PERCENTAGE_DOUBLE : 带百分号的数据转化为double类型
	*/ 
	public final static String PERCENTAGE_DOUBLE ="percentage_double";
	/**   
	 * @Title: dataFormatType   
	 * @Description:数据根据格式进行转化   
	 * @param value
	 * @param type
	 * @return        
	 */
	public static Object dataFormatType(String value, BatchImpFile batchImpFile) {
		String type =batchImpFile.getDataFormatType();
		value = value.replaceAll(" ", "");
		if (ExcelDataType.DOUBLE_TYPE.equals(type)) {
			return Double.valueOf(value);
		} else if (ExcelDataType.PERCENTAGE_DOUBLE.equals(type)) {
			return  Double.valueOf(value);
		} else if(batchImpFile.getArgument()!=null){
			return FundsArgumentUtils.chineseToChart(batchImpFile.getArgument(), value);
		}else{
			return value;
		}
	}
}
