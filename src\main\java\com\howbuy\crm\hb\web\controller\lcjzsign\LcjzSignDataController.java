/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.web.controller.lcjzsign;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.date.DateUtil;
import com.howbuy.crm.base.YesNoEnum;
import com.howbuy.crm.hb.domain.lcjzsigndata.CmLcjzSignQueryVo;
import com.howbuy.crm.hb.domain.lcjzsigndata.CmLcjzSignVo;
import com.howbuy.crm.hb.domain.lcjzsigndata.CmLcjzsigndata;
import com.howbuy.crm.hb.service.lcjzsigndata.CmLcjzSignDataService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.CommPageBean;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.db.PageResult;
import crm.howbuy.base.utils.StringUtil;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/7/26 15:40
 * @since JDK 1.8
 */
@Slf4j
@Controller
@RequestMapping(value = "/lcjzsign")
public class LcjzSignDataController {

    @Autowired
    private CmLcjzSignDataService cmLcjzSignDataService;
    /**
     * @description:(理财九章报表数据页面)
     * @param request
     * @return java.lang.String
     * @author: xufanchao
     * @date: 2023/8/1 08:54
     * @since JDK 1.8
     */
    @RequestMapping("/queryLcjzSignData")
    public String listCheckJoinClub(HttpServletRequest request) {
        if (null == cmLcjzSignDataService.getUpdateTime()) {
            request.setAttribute("updateTime", "暂无数据更新");
        } else {
            request.setAttribute("updateTime", DateUtil.formateDate(cmLcjzSignDataService.getUpdateTime(), DateUtil.YYYYMMDD));
        }
        return "lcjzsigndata/lcjzsigndata";
    }



    /**
     * @description:(查询页面报表数据)
     * @param vo
     * @return crm.howbuy.base.db.PageResult<com.howbuy.crm.hb.domain.lcjzsigndata.CmLcjzSignVo>
     * @author: xufanchao
     * @date: 2023/8/1 08:55
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/listlcjzdata.do")
    public PageResult<CmLcjzSignVo> listSyncYxsLive(CmLcjzSignQueryVo vo) {
        // 设置查询分页参数
        fillAuthVo(vo);


        String orgCode = vo.getOrgCode();
        //选择了未分配组
        if (orgCode.startsWith(StaticVar.STR_OTHER)) {
            vo.setOthertearm(orgCode.replaceFirst("other", ""));
        } else {
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if (StaticVar.ORGTYPE_TEAM.equals(orgType)) {
                vo.setTeamcode(orgCode);
            } else {
                if (!"0".equals(orgCode)) {
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    vo.setOutletcodes(suborgs);
                }
            }
        }


        String latitude = vo.getLatitude();
        PageData<CmLcjzsigndata> cmLcjzsigndataList;
        if (latitude.equals(YesNoEnum.Y.getCode())) {
            cmLcjzsigndataList = cmLcjzSignDataService.listCmLcjzsigndatalatitudeByPage(vo);
        } else {
            cmLcjzsigndataList = cmLcjzSignDataService.listCmLcjzsigndataByPage(vo);
        }

        List<CmLcjzsigndata> listData = cmLcjzsigndataList.getListData();
        List<String> conferenceTypesOrder = Arrays.asList(
                "理财九章-大类-FOF",
                "理财九章-大类-保险",
                "理财九章-大类-固收",
                "理财九章-大类-家办",
                "理财九章-大类-海外",
                "理财九章-大类-股权",
                "理财九章-大类-股票",
                "理财九章-财富健康度",
                "理财九章-长宽高"
        );
        listData.sort(Comparator
                .comparing(CmLcjzsigndata::getAppointtime).reversed()
                .thenComparing(CmLcjzsigndata::getConferenceid, Comparator.reverseOrder())
                .thenComparing((data1, data2) -> {
                    int conferenceTypeIndex1 = conferenceTypesOrder.indexOf(data1.getConferencetype());
                    int conferenceTypeIndex2 = conferenceTypesOrder.indexOf(data2.getConferencetype());
                    return Integer.compare(conferenceTypeIndex1, conferenceTypeIndex2);
                })
        );
        PageResult<CmLcjzSignVo> pageResult = new PageResult<>();
        pageResult.setTotal(cmLcjzsigndataList.getPageBean().getTotalNum());
        pageResult.setRows(transtoCmLcjzSignVo(listData, latitude));
        return pageResult;
    }

    /**
     * @description:(入会管理数据导出方法)
     * @param request
     * @param response
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @author: xufanchao
     * @date: 2023/8/1 08:55
     * @since JDK 1.8
     */
    @ResponseBody
    @PostMapping("/lcjzdataexport")
    public Map<String, Object> joinClubExport(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询分页参数
        CmLcjzSignQueryVo vo = new CmLcjzSignQueryVo();
        String latitude = request.getParameter("latitude");
        vo.setConferenceName(request.getParameter("conferenceName"));
        vo.setBeginDt(request.getParameter("beginDt"));
        vo.setEndDt(request.getParameter("endDt"));
        vo.setConsCode(request.getParameter("consCode"));
        vo.setIsCheckIn(request.getParameter("isCheckIn"));
        vo.setIsCons(request.getParameter("isCons"));
        vo.setOrgCode(request.getParameter("orgCode"));
        if (StringUtils.isNotBlank(request.getParameter("conferenceType"))) {
            String conferenceType = JSON.toJSONString(request.getParameter("conferenceType").split(","));
            vo.setConferenceType(conferenceType);
        }
        String orgCode = vo.getOrgCode();
        //选择了未分配组
        if (orgCode.startsWith(StaticVar.STR_OTHER)) {
            vo.setOthertearm(orgCode.replaceFirst("other", ""));
        } else {
            String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
            //选择了团队
            if (StaticVar.ORGTYPE_TEAM.equals(orgType)) {
                vo.setTeamcode(orgCode);
            } else {
                if (!"0".equals(orgCode)) {
                    List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                    vo.setOutletcodes(suborgs);
                }
            }
        }
        fillAuthVo(vo);
        List<CmLcjzsigndata> cmLcjzsigndataList;
        if (latitude.equals(YesNoEnum.Y.getCode())) {
            cmLcjzsigndataList = cmLcjzSignDataService.queryCmLcjzsigndatalatitudeByPage(vo);
        } else {
            cmLcjzsigndataList = cmLcjzSignDataService.queryCmLcjzsigndataList(vo);
        }
        List<CmLcjzSignVo> cmLcjzSignVos = transtoCmLcjzSignVo(cmLcjzsigndataList, latitude);
        Map<String, Object> resultMap = new HashMap<>();
        try {
            if (cmLcjzSignVos != null && !cmLcjzSignVos.isEmpty()) {
                // 清空输出流
                response.reset();

                // 设置文件格式和名字
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition", "attachment;fileName=" + new String(("理财九章报名签到数据-" + DateUtil.formateDate(new Date(), DateUtil.YYYYMMDD) + ".xls").getBytes("gb2312"), StandardCharsets.ISO_8859_1));
                ServletOutputStream os = response.getOutputStream();

                String[] columnName = {
                        "投顾客户号", "报名人姓名", "报名人是否投顾", "报名时投顾", "所属中心", "所属区域", "所属分公司",
                        "课程ID", "课程名称", "主办方", "城市", "课程日期", "讲师姓名", "课程地点", "课程时间",
                        "课程状态", "会议ID", "会议名称", "举办部门", "会议类型", "报名数据来源",
                        "报名时间", "是否线下签到", "签到时间", "高端产品首次成交时间"
                };

                String[] beanProperty = {
                        "conscustno", "custname", "istg", "consname", "orgcenter", "orgcode",
                        "orgname", "courseid", "coursename", "sponsor", "citycode", "coursedt",
                        "teacherName", "coursearea", "coursetime", "coursestatus", "conferenceid",
                        "conferencename", "sponsororgname", "conferencetype", "applysource", "applytime", "issign", "signdt", "dealdt"
                };
                ExcelWriter.writeExcel(os, "理财九章签到数据", 0, cmLcjzSignVos, columnName, beanProperty);
                os.close(); // 关闭流
                resultMap.put("msg", "success");
            } else {
                resultMap.put("msg", "noData");
            }
        } catch (Exception e) {
            resultMap.put("msg", "error");
            log.error("文件导出异常", e);
        }
        return resultMap;
    }

    /**
     * @param vo
     * @return void
     * @description:(拼接查询参数)
     * @author: xufanchao
     * @date: 2023/7/28 22:35
     * @since JDK 1.8
     */
    private void fillAuthVo(CmLcjzSignQueryVo vo) {
        Map<String, String> constantMap = new HashMap<>();
        constantMap.put("9a", "理财九章-长宽高");
        constantMap.put("0", null);
        constantMap.put("9b", "理财九章-财富健康度");
        constantMap.put("9d", "理财九章-大类-股票");
        constantMap.put("9e", "理财九章-大类-固收");
        constantMap.put("9f", "理财九章-大类-股权");
        constantMap.put("9c", "理财九章-大类-FOF");
        constantMap.put("9g", "理财九章-大类-海外");
        constantMap.put("9h", "理财九章-大类-保险");
        constantMap.put("9i", "理财九章-大类-家办");
        // 处理会议类型字段
        if (StringUtil.isNotNullStr(vo.getConferenceType())) {
            String[] array = JSON.parseObject(vo.getConferenceType(), String[].class);
            List<String> list = Arrays.asList(array);
            if (!list.isEmpty()) {
                List<String> conferenceTypeList = new ArrayList<>();
                for (String s : list) {
                    conferenceTypeList.add(constantMap.get(s));
                }
                vo.setConferenceTypeName(conferenceTypeList);
            }
        }
    }

    /**
     * @description:(数据转换)
     * @param cmLcjzsigndataList
     * @param latitude
     * @return java.util.List<com.howbuy.crm.hb.domain.lcjzsigndata.CmLcjzSignVo>
     * @author: xufanchao
     * @date: 2023/7/28 22:35
     * @since JDK 1.8
     */
    private List<CmLcjzSignVo> transtoCmLcjzSignVo(List<CmLcjzsigndata> cmLcjzsigndataList, String latitude) {
        List<CmLcjzSignVo> cmLcjzSignVos = new ArrayList<>();
        cmLcjzsigndataList.stream().forEach(it -> {
            CmLcjzSignVo cmLcjzSignVo = new CmLcjzSignVo();
            cmLcjzSignVo.setConscustno(it.getConscustno());
            cmLcjzSignVo.setCustname(it.getRealname());
            cmLcjzSignVo.setIstg(it.getIfcons());
            cmLcjzSignVo.setConsname(it.getConsname());
            cmLcjzSignVo.setOrgcenter(it.getU1name());
            cmLcjzSignVo.setOrgcode(it.getU2name());
            cmLcjzSignVo.setOrgname(it.getU3name());
            cmLcjzSignVo.setCourseid(it.getCourseid());
            cmLcjzSignVo.setCoursename(it.getCoursename());
            cmLcjzSignVo.setSponsor(it.getSponsor());
            cmLcjzSignVo.setCitycode(it.getCityname());
            cmLcjzSignVo.setTeacherName(it.getLecturer());
            cmLcjzSignVo.setCoursearea(it.getActivityarea());
            cmLcjzSignVo.setCoursetime(it.getActivitytime());
            cmLcjzSignVo.setCoursestatus(it.getOnlineflag());
            cmLcjzSignVo.setConferenceid(it.getConferenceid());
            cmLcjzSignVo.setConferencename(it.getConferencename());
            cmLcjzSignVo.setSponsororgname(it.getHoldorg());
            cmLcjzSignVo.setConferencetype(it.getConferencetype());
            cmLcjzSignVo.setApplysource(it.getAssignsource());
            cmLcjzSignVo.setApplystatus(it.getAppointstatus());
            cmLcjzSignVo.setIssign(it.getIfcheckin());
            if (it.getAppointtime().equals(new Timestamp(0))) {
                cmLcjzSignVo.setApplytime("");
            } else {
                cmLcjzSignVo.setApplytime(DateUtil.formateDate(it.getAppointtime(), DateUtil.STANDARDDATE));
            }
            if (it.getCheckintime().equals(new Timestamp(0))) {
                cmLcjzSignVo.setSigndt("");
            } else {
                cmLcjzSignVo.setSigndt(DateUtil.formateDate(it.getCheckintime(), DateUtil.STANDARDDATE));
            }
            if (it.getStartdate().equals(new Timestamp(0))) {
                cmLcjzSignVo.setCoursedt("");
            } else {
                cmLcjzSignVo.setCoursedt(DateUtil.formateDate(it.getStartdate(), DateUtil.STANDARDDATE));
            }
            if (it.getGdfstackdt().equals(new Timestamp(0))) {
                cmLcjzSignVo.setDealdt("");
            } else {
                cmLcjzSignVo.setDealdt(DateUtil.formateDate(it.getGdfstackdt(), DateUtil.STANDARDDATE));
            }
            cmLcjzSignVos.add(cmLcjzSignVo);
        });
        return cmLcjzSignVos;
    }


    /**
     * @description:(查询会议信息)
     * @param
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @author: xufanchao
     * @date: 2023/7/27 19:38
     * @since JDK 1.8
     */
    @ResponseBody
    @RequestMapping("/comboxconferOrg.do")
    public Map<String, Object> queryConferenceOrg() throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        CmLcjzSignQueryVo cmLcjzSignQueryVo = new CmLcjzSignQueryVo();
        CommPageBean commPageBean = new CommPageBean();
        List<CmLcjzsigndata> cmLcjzsigndataList = cmLcjzSignDataService.queryCmLcjzsigndataList(cmLcjzSignQueryVo, commPageBean)
                .stream().collect(Collectors.groupingBy(CmLcjzsigndata::getConferencename)).values().stream().map(lcjzsigndata -> lcjzsigndata.get(0)).collect(Collectors.toList());

        try {
            resultMap.put("rowsData", cmLcjzsigndataList);
            resultMap.put("resultcode", 1);
        } catch (Exception e) {
            log.error("查询会议信息出错", e);
            resultMap.put("resultcode", 0);
        }
        return resultMap;
    }


}