package com.howbuy.crm.hb.web.controller.conference;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import com.howbuy.crm.hb.domain.conference.*;
import com.howbuy.crm.hb.service.conference.CmConferenceCustInfomationService;
import com.howbuy.crm.hb.service.conference.CmConferenceDepartmentService;
import com.howbuy.crm.hb.service.conference.CmConferenceInfomationService;
import com.howbuy.crm.hb.service.conference.CmConferenceService;
import com.howbuy.crm.page.framework.domain.User;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

@Controller
@RequestMapping(value = "/conferencedept")
public class CmConferenceDepartmentController {

    private static Logger LOG = LoggerFactory.getLogger(CmConferenceDepartmentController.class);

    @Autowired
    private CmConferenceDepartmentService cmConferenceDepartmentService;

    @Autowired
    private CmConferenceInfomationService cmConferenceInfomationService;

    @Autowired
    private CmConferenceCustInfomationService cmConferenceCustInfomationService;

    @Autowired
    private CmConferenceService cmConferenceService;

    @RequestMapping(value = "/addConferenceDepartmentView.do")
    public String addConferenceView(HttpServletRequest request) {
        String conferenceid = request.getParameter("conferenceid");
        request.setAttribute("conferenceid", conferenceid);
        return "/conference/addConferenceDepartmentView";
    }

  //  @ResponseBody
  //  @RequestMapping("/saveConferenceDepartment.do")
    public String saveConferenceDepartment(HttpServletRequest request,
                                           HttpServletResponse response) throws Exception {
        String result = "";

        User user = (User) request.getSession().getAttribute("loginUser");

        List<CmConferenceDepartment> addDepartmentList = new ArrayList<CmConferenceDepartment>();
        List<CmConferenceDepartment> updateDepartmentList = new ArrayList<CmConferenceDepartment>();
        List<String> checklist = new ArrayList<String>();
        String conferenceid = request.getParameter("conferenceid");

        CmConference cmConference = cmConferenceService.queryCmConferenceInfo(conferenceid);

        if (cmConference == null) {
            result = "noconf";
        } else {
            int allOrgnub = cmConferenceDepartmentService.getDepartmentAllOrgnub(conferenceid);
            int maxnumb = cmConference.getMaxnumber();
            int count = 0;

            Enumeration enu = request.getParameterNames();
            while (enu.hasMoreElements()) {
                String paraName = (String) enu.nextElement();
                if (paraName.indexOf("update") != -1) {

                    String uporgCode = paraName.replace("update", "");

                    int upmax = 0;

                    if (StringUtils.isNotEmpty(request.getParameter(paraName))) {
                        upmax = Integer.parseInt(request.getParameter(paraName));
                    }

                    Map<String, String> paramMap = new HashMap<String, String>();
                    paramMap.put("conferenceid", conferenceid);
                    paramMap.put("orgcode", uporgCode);

                    CmConferenceDepartment cmDepartment = cmConferenceDepartmentService.getDepartmentinfo(paramMap);

                    int oldorgnub = cmDepartment.getOrgcodenub();
                    count = count + upmax - oldorgnub;
                    CmConferenceDepartment updateDepartment = new CmConferenceDepartment();
                    updateDepartment.setConferenceid(conferenceid);
                    updateDepartment.setOrgcode(uporgCode);
                    updateDepartment.setModifier(user.getUserId());
                    updateDepartment.setOrgcodenub(upmax);
                    updateDepartmentList.add(updateDepartment);

                    checklist.add(uporgCode);
                }
            }

            for (int i = 0; i < 5; i++) {
                String maxnumber = request.getParameter("maxnumber" + i);
                if (StringUtil.isNotNullStr(maxnumber)) {
                    int max = Integer.parseInt(maxnumber);

                    String orgCode = request.getParameter("orgCode" + i);

                    Map<String, String> paramMap = new HashMap<String, String>();
                    paramMap.put("conferenceid", conferenceid);
                    paramMap.put("orgcode", orgCode);

                    CmConferenceDepartment cmDepartment = cmConferenceDepartmentService.getDepartmentinfo(paramMap);
                    if (cmDepartment == null) {
                        count = count + max;
                        CmConferenceDepartment addDepartment = new CmConferenceDepartment();
                        addDepartment.setConferenceid(conferenceid);
                        addDepartment.setOrgcode(orgCode);
                        addDepartment.setCreater(user.getUserId());
                        addDepartment.setOrgcodenub(max);
                        addDepartmentList.add(addDepartment);
                    }

                    checklist.add(orgCode);
                }
            }
            boolean isRepeat = checklist.size() != new HashSet<String>(checklist).size();
            if (isRepeat) {
                result = "repeat";
            } else if (count + allOrgnub > maxnumb) {
                result = "more";
            } else {
                cmConferenceDepartmentService.insertCmConferenceDepartmentList(addDepartmentList);
                cmConferenceDepartmentService.updateCmConferenceDepartmentList(updateDepartmentList);
                result = "success";
            }

        }

        return result;
    }

    @ResponseBody
    @RequestMapping("/saveConferenceDepartment.do")
    public String saveDepartment(HttpServletRequest request){
        String result = null;
        User user = (User) request.getSession().getAttribute("loginUser");
        List<CmConferenceDepartmentWithOrg> addDepartmentList = new ArrayList();
        List<CmConferenceDepartmentWithOrg> updateDepartmentList = new ArrayList();
        Map<String,Integer> updateMap = new HashMap<>();
        String conferenceid = request.getParameter("conferenceid");
        CmConference cmConference = cmConferenceService.queryCmConferenceInfo(conferenceid);

        if (cmConference == null) {
            result = "noconf";
        } else {
            Enumeration enu = request.getParameterNames();
            Map<String,String> params = new HashMap();
            while (enu.hasMoreElements()) {
                String paramName = (String) enu.nextElement();
                params.put(paramName, request.getParameter(paramName));
            }
            for(Map.Entry<String, String> entry : params.entrySet()){
            	if(entry.getKey().contains("add")){
                    String orgCode = entry.getValue();
                    if(orgCode == null || "请选择".equals(orgCode)) {
                        return "请选择部门";
                    }
                    CmConferenceDepartmentWithOrg departmentWithOrg = new CmConferenceDepartmentWithOrg();
                    departmentWithOrg.setOrgcode(orgCode);
                    String row = entry.getKey().replace("add","");
                    String num = params.get("maxnum"+row);
                    if(num == null || "".equals(num)) {
                        return "请设置人数";
                    }
                    if(!num.matches("-?[0-9]+\\.?[0-9]*")) {
                        return "只能输入数字";
                    }
                    departmentWithOrg.setOrgcodenub(Integer.parseInt(num));
                    departmentWithOrg.setParentCode(params.get("parentCode"+row));
                    addDepartmentList.add(departmentWithOrg);
                }else if(entry.getKey().contains("update")){
                    String num = entry.getValue();
                    if(num == null || "".equals(num)) {
                        return "请设置人数";
                    }
                    if(!num.matches("-?[0-9]+\\.?[0-9]*")) {
                        return "只能输入数字";
                    }
                    updateMap.put(entry.getKey().replace("update",""),Integer.valueOf(num));
                }
            }
            result = cmConferenceDepartmentService.saveConferenceDepartment(conferenceid,addDepartmentList,updateMap,user.getUserId());
            if("0".equals(result)){
                result = "success";
            }
        }
        return result;
    }

    @ResponseBody
    @RequestMapping("/saveConferenceInfomation.do")
    public String saveConferenceInfomation(HttpServletRequest request,
                                           HttpServletResponse response) throws Exception {
        String result = "";

        User user = (User) request.getSession().getAttribute("loginUser");

        List<CmConferenceInfomation> addInfomationList = new ArrayList<CmConferenceInfomation>();
        List<String> checklist = new ArrayList<String>();
        String conferenceid = request.getParameter("conferenceid");

        CmConference cmConference = cmConferenceService.queryCmConferenceInfo(conferenceid);

        if (cmConference == null) {
            result = "noconf";
        } else {

            Enumeration enu = request.getParameterNames();
            while (enu.hasMoreElements()) {
                String paraName = (String) enu.nextElement();
                if (paraName.indexOf("update") != -1) {

                    String upfield = paraName.replace("update", "");
                    upfield = URLDecoder.decode(upfield, "UTF-8");

                    checklist.add(upfield);
                }
            }

            int num = checklist.size();
            for (int i = num; i < 17; i++) {

                String field = request.getParameter("field" + i);

                if (StringUtil.isNotNullStr(field)) {
                    field = URLDecoder.decode(field, "UTF-8");
                    String fieldvalue = request.getParameter("fieldvalue" + i);

                    Map<String, String> paramMap = new HashMap<String, String>();
                    paramMap.put("conferenceid", conferenceid);
                    paramMap.put("field", field);

                    CmConferenceInfomation cmInfomation = cmConferenceInfomationService.getInfomationinfo(paramMap);
                    if (cmInfomation == null) {

                        CmConferenceInfomation addInfomation = new CmConferenceInfomation();
                        addInfomation.setConferenceid(conferenceid);
                        addInfomation.setField(field);
                        addInfomation.setCreater(user.getUserId());
                        if (StringUtils.isNotEmpty(fieldvalue)) {
                            fieldvalue = URLDecoder.decode(fieldvalue, "UTF-8");
                            addInfomation.setFieldvalue(fieldvalue);
                        } else {
                            addInfomation.setFieldvalue("");
                        }
                        addInfomationList.add(addInfomation);
                    }

                    checklist.add(field);
                }
            }
            boolean isRepeat = checklist.size() != new HashSet<String>(checklist).size();
            if (isRepeat) {
                result = "repeat";
            } else {
                cmConferenceInfomationService.insertCmConferenceInfomationList(addInfomationList);
                result = "success";
            }

        }

        return result;
    }

    @ResponseBody
    @RequestMapping("/queryConferenceDepartment.do")
    public ModelAndView queryConferenceDepartment(HttpServletRequest request) {
        String conferenceid = request.getParameter("conferenceid");

      //  List<CmConferenceDepartment> listtemp = cmConferenceDepartmentService.listCmConferenceDepartment(conferenceid);
       /* for (CmConferenceDepartment v : listtemp) {
            v.setOrgcodename(ConsOrgCache.getInstance().getOrgMap().get(v.getOrgcode()));
        }*/
        Map<String, Object> map = new HashMap();
      //  map.put("listtemp", listtemp);
        request.setAttribute("conferenceid", conferenceid);
        map.putAll(cmConferenceDepartmentService.getDepartmentForLimit(conferenceid));

        return new ModelAndView("/conference/departmentList", "map", map);
    }

    @ResponseBody
    @RequestMapping("/queryConferenceInfomation.do")
    public ModelAndView queryConferenceInfomation(HttpServletRequest request) {
        String conferenceid = request.getParameter("conferenceid");
        List<CmConferenceInfomation> listtemp = cmConferenceInfomationService.listCmConferenceInfomation(conferenceid);

        Map<String, List<CmConferenceInfomation>> map = new HashMap<String, List<CmConferenceInfomation>>();
        map.put("listtemp", listtemp);
        request.setAttribute("listsize", listtemp.size());
        request.setAttribute("conferenceid", conferenceid);

        return new ModelAndView("/conference/infomationList", "map", map);
    }

    @ResponseBody
    @RequestMapping("/deleteDepartmentInfo.do")
    public String deleteDepartmentInfo(HttpServletRequest request) throws Exception {
        String result;
        String conferenceId = request.getParameter("conferenceid");
        String orgCode = request.getParameter("orgcode");

        Map<String, String> paramMap = new HashMap();
        paramMap.put("conferenceId", conferenceId);
        paramMap.put("orgCode", orgCode);

        result = cmConferenceDepartmentService.deleteDepartmentGroup(paramMap);

        return result;
    }

    @ResponseBody
    @RequestMapping("/deleteInfomationInfo.do")
    public String deleteInfomationInfo(HttpServletRequest request) throws Exception {
        String result = "success";
        String conferenceid = request.getParameter("conferenceid");
        String field = request.getParameter("field");
        field = URLDecoder.decode(field, "UTF-8");
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("conferenceid", conferenceid);
        paramMap.put("field", field);
        cmConferenceInfomationService.deleteInfomation(paramMap);
        return result;
    }

    @ResponseBody
    @RequestMapping("/selectJoinNum.do")
    public String selectJoinNum(HttpServletRequest request) throws Exception {
        String conferenceId = request.getParameter("conferenceId");
        String orgCode = request.getParameter("orgCode");
        Integer num = cmConferenceDepartmentService.getJoinNum(conferenceId,orgCode);
        return num != null ? num.toString() : "0";
    }

}
