package com.howbuy.crm.hb.web.controller.associationmail;

import com.howbuy.crm.hb.domain.associationmail.AssociationOrgname;
import com.howbuy.crm.hb.service.associationmail.AssociationOrgnameService;
import com.howbuy.crm.hb.web.util.ResultCode;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.db.PageData;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by shucheng on 2021/5/21 17:18
 */
@Slf4j
@Controller
@RequestMapping("/associationOrgname")
public class AssociationOrgnameController {

    @Autowired
    private AssociationOrgnameService associationOrgnameService;

    @RequestMapping(value="/listAssociationOrgname.do",method=RequestMethod.GET)
    public ModelAndView listAssociationOrgname() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/associationmail/listAssociationOrgname");
        return modelAndView;
    }

    @RequestMapping("/listAssociationOrgnameByPage.do")
    @ResponseBody
    public Map<String, Object> listAssociationOrgnameByPage(HttpServletRequest request) throws Exception {
        Map<String, String> param = new ParamUtil(request).getParamMap();
        PageData<AssociationOrgname> pageData = associationOrgnameService.listAssociationOrgnameByPage(param);
        List<AssociationOrgname> list = pageData.getListData();
        // 返回查询结果
        for (AssociationOrgname v : list) {
            v.setCreator(ConsOrgCache.getInstance().getAllUserMap().get(v.getCreator()));
            v.setModifier(ConsOrgCache.getInstance().getAllUserMap().get(v.getModifier()));
        }
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        resultMap.put("rows", list);
        return resultMap;
    }

    /**
     * 初始化机构别名修改页
     * @param request
     * @return
     */
    @RequestMapping("/initEdit.do")
    @ResponseBody
    public Map<String, Object> initEdit(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String id = request.getParameter("id");
        AssociationOrgname associationOrgname = associationOrgnameService.findAssociationOrgnameById(id);
        resultMap.put("domain", associationOrgname);
        return resultMap;
    }

    /**
     * 保存数据前进行参数验证
     * @param request
     * @return
     */
    @RequestMapping("/checkParam.do")
    @ResponseBody
    public Map<String, Object> checkParam(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            String id = request.getParameter("id");
            String orgName = request.getParameter("orgName");
            String orgAliasName = request.getParameter("orgAliasName");

            // 判断是否存在机构名、机构别名完全相同的记录
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("id", id);
            paramMap.put("orgName", orgName);
            paramMap.put("orgAliasName", orgAliasName);
            boolean exist = associationOrgnameService.checkRecordExist(paramMap);
            resultMap.put("isValid", !exist);
            resultMap.put("message", "该条记录已存在");
        } catch (Exception e) {
            resultMap.put("isValid", false);
            resultMap.put("message", "查询失败");
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }

    /**
     * 新增或修改机构别名
     * @param request
     * @return
     */
    @RequestMapping("/save.do")
    @ResponseBody
    public Map<String, Object> save(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            String id = request.getParameter("id");
            String orgName = request.getParameter("orgName");
            String orgAliasName = request.getParameter("orgAliasName");
            AssociationOrgname associationOrgname = new AssociationOrgname();
            associationOrgname.setId(id);
            associationOrgname.setOrgName(orgName);
            associationOrgname.setOrgAliasName(orgAliasName);

            if (StringUtils.isBlank(id)) {
                associationOrgnameService.insertAssociationOrgname(associationOrgname);
            } else {
                associationOrgnameService.updateAssociationOrgname(associationOrgname);
            }
            resultMap.put("errorCode", ResultCode.RESULT_CODE_SUCCESS);
            resultMap.put("errorMsg", "操作成功");
        } catch (Exception e) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "操作失败");
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }

    /**
     * 删除机构别名
     * @param request
     * @return
     */
    @RequestMapping("/delete.do")
    @ResponseBody
    public Map<String, Object> delete(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            String id = request.getParameter("id");
            associationOrgnameService.deleteAssociationOrgname(id);
            resultMap.put("errorCode", ResultCode.RESULT_CODE_SUCCESS);
            resultMap.put("errorMsg", "删除成功");
        } catch (Exception e) {
            resultMap.put("errorCode", ResultCode.RESULT_CODE_FAIL);
            resultMap.put("errorMsg", "删除失败");
            log.error(e.getMessage(), e);
        }
        return resultMap;
    }
}
