/**   
 * @Title: BatchImpBean.java 
 * @Package com.hb.crm.web.util.excel.bean 
 * @Description: excel文件导入的配置bean类
 * <AUTHOR>
 * @date 2016年4月28日 下午1:08:37 
 * @version V1.0   
 */

package com.howbuy.crm.hb.tools.excel.bean;

/**
 * @ClassName: BatchImpBean
 * @Description: 进行文件导入公用类
 * <AUTHOR>
 * @date 2016年4月28日 下午1:08:37
 * 
 */

public abstract class BatchImpBean {
	/** 
	* @Fields columnIndex :列索引
	*/ 
	private int[] columnIndex;

	/**
	 * @Fields list :导入的文件的列的定义
	 */
	private BatchImpFile[] arrays;

	/**
	 * @Fields errorExcuteClass :错误信息处理类
	 */
	private String errorExcuteClass;

	/**
	 * @Fields beanClassName : 属性对应的dean
	 */
	private String beanClassName;
	
	public abstract BatchImpBean inits();

	/**  
	 * @Title:  getColumnIndex <BR>  
	 * @Description: please write your description <BR>  
	 * @return: int[] <BR>  
	 */
	
	public int[] getColumnIndex() {
		return columnIndex;
	}

	/**  
	 * @Title:  setColumnIndex <BR>  
	 * @Description: please write your description <BR>  
	 * @return: int[] <BR>  
	 */
	
	public void setColumnIndex(int[] columnIndex) {
		this.columnIndex = columnIndex;
	}

	/**
	 * @Title: getBeanClassName <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public String getBeanClassName() {
		return beanClassName;
	}

	/**
	 * @Title: setBeanClassName <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public void setBeanClassName(String beanClassName) {
		this.beanClassName = beanClassName;
	}

	/**
	 * @Title: getErrorExcuteClass <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public String getErrorExcuteClass() {
		return errorExcuteClass;
	}

	/**
	 * @Title: setErrorExcuteClass <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public void setErrorExcuteClass(String errorExcuteClass) {
		this.errorExcuteClass = errorExcuteClass;
	}

	/**
	 * @Title: getArrays <BR>
	 * @Description: please write your description <BR>
	 * @return: BatchImpFile[] <BR>
	 */

	public BatchImpFile[] getArrays() {
		return arrays;
	}

	/**
	 * @Title: setArrays <BR>
	 * @Description: please write your description <BR>
	 * @return: BatchImpFile[] <BR>
	 */

	public void setArrays(BatchImpFile[] arrays) {
		this.arrays = arrays;
	}

}
