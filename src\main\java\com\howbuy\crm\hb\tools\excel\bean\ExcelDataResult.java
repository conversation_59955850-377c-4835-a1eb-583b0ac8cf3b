/**   
 * @Title: ExcelDataResult.java 
 * @Package com.hb.crm.web.util.excel.bean 
 * @Description: 数据转化工具类
 * <AUTHOR>
 * @date 2016年4月29日 下午2:02:24 
 * @version V1.0   
 */
package com.howbuy.crm.hb.tools.excel.bean;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: ExcelDataResult
 * @Description: 用来存储excel导入的返回结果
 * <AUTHOR>
 * @date 2016年4月29日 下午2:02:24
 * 
 */

public class ExcelDataResult {

	/**
	 * @Fields cheakExcelFlag :校验结果
	 */
	private boolean cheakExcelFlag = true;

	/**
	 * @Fields lists : 存储数据
	 */
	private List<?> lists;
	
	
	/** 
	* @Fields errorMapLists : 错误数据
	*/ 
	private List<Map<String,String>> errorMapLists;

	/**  
	 * @Title:  getErrorMapLists <BR>  
	 * @Description: please write your description <BR>  
	 * @return: List<Map<String,String>> <BR>  
	 */
	
	public List<Map<String, String>> getErrorMapLists() {
		return errorMapLists;
	}

	/**  
	 * @Title:  setErrorMapLists <BR>  
	 * @Description: please write your description <BR>  
	 * @return: List<Map<String,String>> <BR>  
	 */
	
	public void setErrorMapLists(List<Map<String, String>> errorMapLists) {
		this.errorMapLists = errorMapLists;
	}

	/**
	 * @Title: isCheakExcelFlag <BR>
	 * @Description: please write your description <BR>
	 * @return: boolean <BR>
	 */

	public boolean isCheakExcelFlag() {
		return cheakExcelFlag;
	}

	/**
	 * @Title: setCheakExcelFlag <BR>
	 * @Description: please write your description <BR>
	 * @return: boolean <BR>
	 */

	public void setCheakExcelFlag(boolean cheakExcelFlag) {
		this.cheakExcelFlag = cheakExcelFlag;
	}

	/**
	 * @Title: getLists <BR>
	 * @Description: please write your description <BR>
	 * @return: List<?> <BR>
	 */

	public List<?> getLists() {
		return lists;
	}

	/**
	 * @Title: setLists <BR>
	 * @Description: please write your description <BR>
	 * @return: List<?> <BR>
	 */

	public void setLists(List<?> lists) {
		this.lists = lists;
	}

}
