package com.howbuy.crm.hb.web.aspect;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Priority;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 数据角色范围切面
 * @Date 2024/4/22 11:05
 */
@Slf4j
@Aspect
@Component
@Priority(2)
public class DataRoleRangeAspect {

    private static final String ROLE_HK_CP = "ROLE_HK_CP";
    @Pointcut("execution(* com.howbuy.crm.hb.web.controller..*.*(..))")
    public void entryPoint() {
    }

    /**
     * 控制器后置操作
     *
     * @param result 返回结果
     */
    @AfterReturning(pointcut = "entryPoint()", returning = "result")
    public void afterReturn(Object result) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(requestAttributes)) {
            return;
        }

        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        if (Objects.isNull(request)) {
            return;
        }

        HttpSession session = request.getSession();
        if (Objects.isNull(session)) {
            return;
        }

        List<String> loginRoles = (List<String>) session.getAttribute("loginRoles");
        boolean isXgcj = false;
        if (CollectionUtils.isNotEmpty(loginRoles)) {
            // 判断角色是否包含香港场检
            isXgcj = loginRoles.contains(ROLE_HK_CP);
        }

        if (result instanceof ModelAndView) {
            ModelAndView modelAndView = (ModelAndView) result;
            modelAndView.addObject("isXgcj", isXgcj);
        }
    }

}
