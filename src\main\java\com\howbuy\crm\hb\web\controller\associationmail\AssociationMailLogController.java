package com.howbuy.crm.hb.web.controller.associationmail;

import com.howbuy.crm.hb.domain.associationmail.AssociationMailPageConstant;
import com.howbuy.crm.hb.domain.associationmail.BdpMatchLog;
import com.howbuy.crm.hb.domain.associationmail.ResolveMailLog;
import com.howbuy.crm.hb.domain.associationmail.SendMessageLog;
import com.howbuy.crm.hb.service.associationmail.AssociationMailLogService;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by shucheng on 2021/6/15 13:51
 */
@Slf4j
@Controller
@RequestMapping("/associationMailLog")
public class AssociationMailLogController {

    @Autowired
    private AssociationMailLogService associationMailLogService;

    @RequestMapping(value="/viewAssociationMailLog.do",method=RequestMethod.GET)
    public ModelAndView listAssociationExpMail() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/associationmail/viewAssociationMailLog");
        return modelAndView;
    }

    @RequestMapping("/findAssociationMailLog.do")
    public ModelAndView findAssociationMailLog(HttpServletRequest request, String credtStr) throws Exception {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/associationmail/viewAssociationMailLogDetail");

        Date credt = DateUtils.parseDate(credtStr, "yyyyMMdd");
        modelAndView.addObject("showDateStr", DateFormatUtils.format(credt, "yyyy年MM月dd日"));

        Map<String, String> param = new HashMap<>();
        param.put("credtStr", credtStr);
        ResolveMailLog resolveMailLog = associationMailLogService.findResolveMailLog(param);
        modelAndView.addObject("resolveMailLog", resolveMailLog);
        modelAndView.addObject("resolveMailLogFlag", resolveShowLogFlag(credtStr, resolveMailLog));

        BdpMatchLog bdpMatchLog = associationMailLogService.findBdpMatchLog(param);
        modelAndView.addObject("bdpMatchLog", bdpMatchLog);
        modelAndView.addObject("bdpMatchLogFlag", resolveShowLogFlag(credtStr, bdpMatchLog));

        SendMessageLog sendMessageLog = associationMailLogService.findSendMessageLog(param);
        modelAndView.addObject("sendMessageLog", sendMessageLog);
        modelAndView.addObject("sendMessageLogFlag", resolveShowLogFlag(credtStr, sendMessageLog));
        return modelAndView;
    }

    /**
     * 确定页面需要展示什么样的日志详情
     * 处理逻辑：
     * 有数据，显示日志
     * 旧的日期，没数据，就是没有日志
     * 新的日期，没数据，显示任务待开始
     * @param credtStr 查询的日志日期 yyyyMMdd
     * @param data 日志数据
     * @return
     * @throws Exception
     */
    private String resolveShowLogFlag(String credtStr, Object data) throws Exception {

        if (data != null) {
            return AssociationMailPageConstant.LOG_HAS_DATA;
        } else {
            Date credt = DateUtils.parseDate(credtStr, "yyyyMMdd");
            Date today = DateUtils.truncate(new Date(), Calendar.DATE);
            // 如果传入的时间是在今天前
            if (credt.compareTo(today) == -1) {
                return AssociationMailPageConstant.LOG_NOT_HAS_DATA;
            } else {
                return AssociationMailPageConstant.LOG_NOT_NEED_START;
            }
        }
    }
}
