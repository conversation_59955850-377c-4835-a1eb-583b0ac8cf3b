package com.howbuy.crm.hb.web.dto.counter;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.base.YesOrNoEnum;
import com.howbuy.crm.trade.common.util.CounterUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description: 实体类CmCounterOrder.java
 */
@Data
public class CmCounterOrderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private String id;

    /**
     * 业务细分id:关联CM_COUNTER_BUSINESS的id
     */
    private String bdid;

    /**
     * 投顾客户号
     */
    private String conscustno;

    /**
     * 产品代码
     */
    private String fundCode;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date credate;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date moddate;

    /**
     * 最新审核人
     */
    private String curChecker;

    /**
     * 最新审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date curCheckdate;

    /**
     * 最新当前状态(1:待分部审核、2:分部退回、3:分部已审核总部待审核、4:总部审核退回、5:分部已审核、6:OP初审退回、7:OP初审通过、8:OP复审退回、9:审核通过、10:作废、11:总部已复核(直销)、12:总部复核退回(直销))、13:失效
     */
    private String curStat;

    /**
     * 订单最终状态1：在途；2：通过；3：作废
     */
    private String lastStat;

    /**
     * 最新审核意见
     */
    private String curCheckdes;

    /**
     * 邮寄状态（管理人）(1未邮寄、2部分邮寄、3已邮寄)
     */
    private String manageMailStat;

    /**
     * 邮寄日期（管理人）
     */
    private String manageMailDate;

    /**
     * 邮寄单号（管理人）
     */
    private String manageMailNo;

    /**
     * 邮寄状态（分部）(1未邮寄、2部分邮寄、3已邮寄)
     */
    private String deptMailStat;

    /**
     * 邮寄日期（分部）
     */
    private String deptMailDate;

    /**
     * 邮寄单号（分部）
     */
    private String deptMailNo;

    /**
     * 归档状态（smop）(1未归档、2部分归档、3已归档)
     */
    private String archSmopStat;

    /**
     * 归档日期（smop）
     */
    private String archSmopDate;

    /**
     * 归档编号（smop）
     */
    private String docSmopNo;

    /**
     * 签收状态（smop）(1未签收、2部分签收、3已签收)
     */
    private String signSmopStat;

    /**
     * 签收日期（smop）
     */
    private String signSmopDate;

    /**
     * 上传人
     */
    private String uploador;

    /**
     * 外部关联订单id
     */
    private String forId;

    /**
     * 变更前银行卡
     */
    private String inBankAcct;

    /**
     * 变更后银行卡
     */
    private String outBankAcct;

    /**
     * 变更后银行卡类型：1：新卡；2：当前客户已绑定的银行卡
     */
    private String afterCardType;

    /**
     * 一账通号
     */
    private String hboneNo;

    /**
     * 外部交互id
     */
    private String extraForId;




    /**
     * 业务类型
     */
    private String busiId;

    /**
     * 业务属性
     */
    private String busiProId;

    /**
     * 客户类型
     */
    private String custType;

    /**
     * 预约类型（1：纸质成单；2：电子成单）
     */
    private String preType;

    /**
     * 是否与产品相关1：相关；0：不相关
     */
    private String productType;

    /**
     * 签字文件个数
     */
    private int hasSignfile;

    /**
     * 撤回:1表示可以撤回
     */
    private int withDraw;

    /**
     * 最新审核状态
     */
    private String curStatName;

    /**
     * 业务类型名称
     */
    private String busiIdName;

    /**
     * 业务属性名
     */
    private String busiProIdName;

    /**
     * 客户类型名称
     */
    private String custTypeName;

    /**
     * 预约类型名
     */
    private String preTypeName;

    /**
     * 是否与产品相关 名称
     */
    private String productTypeName;

    /**
     * 签收状态（smop）名称
     */
    private String signSmopStatName;

    /**
     * 归档状态（smop）名称
     */
    private String archSmopStatName;

    /**
     * 邮寄状态（管理人）名称
     */
    private String manageMailStatName;

    /**
     * 邮寄状态（分部）名称
     */
    private String deptMailStatName;



    /**
     * 业务地区  add by haoran.zhang 2022年10月20日
     */
    private String busiArea;




    /**
     * 所属区域
     */
    private String regionName;

    /**
     * 所属部门
     */
    private String orgName;

    /**
     * 所属投顾
     */
    private String consName;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 证件号码掩码
     */
    private String idnoMask;

    /**
     * 证件号码密文
     */
    private String idnoCipher;

    /**
     * 预计交易日期
     */
    private String expectTradeDt;

    /**
     * 是否候补
     */
    private String isLater;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 上传人名称
     */
    private String uploadorName;

    /**
     * 审核人名称
     */
    private String curCheckerName;

    /**
     * 预约id
     */
    private String preId;

    /**
     * 创建时间字符串
     */
    private String credateStr;

    /**
     * 最新审核时间字符串
     */
    private String curCheckdateStr;

    /**
     * 最新修改日期
     */
    private String modDateStr;


    /**
     * 文件类型名称
     */
    private String fileTypeName;

    /**
     * 退回原因
     */
    private String fileFlowReturnDes;

    /**
     * 流水审核人
     */
    private String orderFlowCurChecker;

    /**
     * 流水审核人名称
     */
    private String orderFlowCurCheckerName;

    /**
     * 流水审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderFlowCurCheckdate;

    /**
     * 流水审核时间字符串
     */
    private String orderFlowCurCheckdateStr;

    /**
     * 流水审核状态
     */
    private String orderFlowCurStat;

    /**
     * 流水审核状态名称
     */
    private String orderFlowCurStatName;

    /**
     * 流水审核意见
     */
    private String orderFlowCurCheckdes;

    /**
     * 香港客户号  只在 busiID=22-香港开户 赋值
     */
    private String hkTxAcctNo;

    /**
     * 香港客户号是否开户  只在 busiID=22-香港开户 赋值
     */
    private boolean hkTxAcctNoIsOpen;

    /**
     * 新增的特殊业务字段
     */
    private String speicalBusiParam;

    /**
     * 合规上传人
     */
    private String hgUploader;

    /**
     * 合规审核人
     */
    private String hgAuditor;

    /**
     * 退回原因汇总
     */
    private String fileFlowReturnDesSummary;

    /**
     * 发送客户时间（导出使用，这个字段只在下面两个业务中才赋值：好臻-售前留痕材料、好买-售前留痕材料）
     */
    private String legalDocSendCustomerDt;



    /**
     * 特殊业务类型，该业务类型禁止crm内部审核，只允许外部接口请求审核     *
     * @return 1-禁止crm内部审核 0-允许crm内部审核
     */
    public String getForbiddenCrmCheck(){
        if(StringUtil.isNotBlank(this.busiId)
                && CounterUtil.forbiddenCrmCheck().contains(this.busiId)){
            return YesOrNoEnum.YES.getCode();
        }
        return YesOrNoEnum.NO.getCode();
    }

}
