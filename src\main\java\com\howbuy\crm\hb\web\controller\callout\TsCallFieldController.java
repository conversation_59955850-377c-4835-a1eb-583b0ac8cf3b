package com.howbuy.crm.hb.web.controller.callout;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.common.collect.Maps;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import com.howbuy.crm.hb.domain.conscust.Custconstant;
import com.howbuy.crm.hb.service.custinfo.CustconstantService;
import com.howbuy.crm.hb.web.util.Util;
import crm.howbuy.base.constants.StaticVar;
import lombok.extern.slf4j.Slf4j;

import com.alibaba.fastjson.JSON;import com.howbuy.crm.hb.domain.webservice.PhoneCallRecord;
import com.howbuy.crm.hb.persistence.callout.TeleAuthorityMapper;
import com.howbuy.crm.hb.service.callout.CsCalloutRecService;
import com.howbuy.crm.hb.service.webservice.TeleSaleCountStatisticsServiceImpl;

import crm.howbuy.base.db.PageData;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import crm.howbuy.base.utils.DateTimeUtil;


/**
 * 电话录音调取
 */
@Slf4j
@Controller
@RequestMapping(value = "/teleSale")
public class TsCallFieldController {
	@Autowired
	private TeleSaleCountStatisticsServiceImpl service;

	@Autowired
	private TeleAuthorityMapper teleAuthorityMapper;

	@Autowired
	private CsCalloutRecService csCalloutRecService;

	@Autowired
	private DecryptSingleFacade decryptSingleFacade;

	@Autowired
	private CustconstantService custconstantService;

	/**
	 * 跳转到电话录音调取页面
	 *
	 * @return String
	 */
	@RequestMapping("/listStaffCallField.do")
	public String listStaffCall(HttpServletRequest request, HttpServletResponse response, ModelMap model)
			throws UnsupportedEncodingException {
		// 设置查询时间：默认为系统昨天和系统今天时间
		Map<String, String> startAndEndDayMap = DateTimeUtil.getDayStartAndEnd();
		request.setAttribute("beginDate", startAndEndDayMap.get("beginDate"));
		request.setAttribute("endDate", startAndEndDayMap.get("endDate"));
		return "report/teleSale/listStaffCallField";
	}

	private Boolean paramValidate(String orgCode, String telType, String consCode, String startDt, String endDt,
								  String teleNume) {
		if (StringUtils.isBlank(orgCode) || StringUtils.isBlank(telType)) {
			return false;
		}

		if (StringUtils.isBlank(consCode) && StringUtils.isBlank(teleNume)) {
			return false;
		}

		return true;
	}

	/**
	 * 电话录音调取 查询 页面
	 *
	 * @param request
	 * @param response
	 * @param model
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/queryListStaffCallField.do")
	public Map<String, Object> listVCmConsultTellField(HttpServletRequest request, HttpServletResponse response, ModelMap model) {
		List<PhoneCallRecord> resultList = processListStaffCall(request);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if (resultList.size() == 0) {
			resultMap.put("total", 0);
			resultMap.put("rows", 0);
			return resultMap;
		}

		PageData<PhoneCallRecord> pageData = new PageData<PhoneCallRecord>();
		pageData.setListData(resultList);

		resultMap.put("total", resultList.size());
		resultMap.put("rows", pageData.getListData());
		return resultMap;
	}


	// 获取机构/组的分机号
	private HashSet<String> getAuthTelno(String orgCode) {
		HashSet<String> telNoSet = new HashSet<String>();
		if (orgCode.contains("other")) {
			orgCode = orgCode.replace("other", "");
			List<Map<String, String>> telNoList = teleAuthorityMapper.listConsultantTeleNoByOtherTeam(orgCode);
			telNoSet = getTelNoSet(telNoList);
		} else {
			List<Map<String, String>> orgList = teleAuthorityMapper.listHbOrganizationTree(orgCode);

			if (orgList != null && orgList.size() > 0) {
				Map<String, String> entity = orgList.get(0);
				String orgType = entity.get("ORGTYPE");
				// 小组
				if ("1".equals(orgType)) {
					List<Map<String, String>> telNoList = teleAuthorityMapper.listConsultantTeleNoByTeamCode(orgCode);
					telNoSet = getTelNoSet(telNoList);
				}

				// 机构
				if ("0".equals(orgType)) {
					List<String> outletcodeList = new ArrayList<String>();
					for (Map<String, String> orgMap : orgList) {
						String orgCodeV = orgMap.get("ORGCODE");
						String orgTypeV = orgMap.get("ORGTYPE");
						if (orgCodeV != null) {
							if (orgCodeV.length() > 1 && "0".equals(orgTypeV)) {
								outletcodeList.add(orgCodeV);
							}
						}
					}
					Map<String, Object> paramMap = new HashMap<String, Object>();
					paramMap.put("outletCodes", outletcodeList);
					List<Map<String, String>> telNoList = teleAuthorityMapper.listConsultantTeleNoByOuletCode(paramMap);
					telNoSet = getTelNoSet(telNoList);
				}
			}

		}
		return telNoSet;
	}

	private HashSet<String> getTelNoSet(List<Map<String, String>> list) {
		HashSet<String> telNoSet = new HashSet<String>();
		if (list != null) {
			for (Map<String, String> map : list) {
				telNoSet.add(map.get("TELNO"));
			}
		}
		return telNoSet;
	}

	private List<PhoneCallRecord> processListStaffCall(HttpServletRequest request) {
		String orderId = "";
		String tid = request.getParameter("tid");
		String contractNo = request.getParameter("contractNo");
		if (StringUtils.isNotBlank(tid)) {
			Map<String, String> paramMap = new HashMap<String, String>();
			paramMap.put("tid", tid);
			orderId = csCalloutRecService.getCsCalloutRec(paramMap);
		} else if (StringUtils.isNotBlank(contractNo)) {
			Map<String, String> paramMap = new HashMap<String, String>();
			paramMap.put("contractNo", contractNo);
			orderId = csCalloutRecService.getCsCalloutRec(paramMap);
		}
		//System.out.println("tid:" + tid + " contractNo:" + contractNo + " orderId:" + orderId);
		log.info("回访录音文件查询:"+"tid:" + tid + " contractNo:" + contractNo + " orderId:" + orderId);

		String queryFlag = request.getParameter("queryFlag");
		String orgCode = request.getParameter("orgCode");
		String consCode = request.getParameter("consCode");
		String startDt = request.getParameter("startDt");
		String endDt = request.getParameter("endDt");
		String teleNume = request.getParameter("teleNume");
		String telType = request.getParameter("telType");
		String serverLocation = request.getParameter("serverLocation");
		Map<String, String> paramMap = new HashMap<String, String>();
		List<PhoneCallRecord> resultList = new ArrayList<PhoneCallRecord>();
		//解密出明文
		if(StringUtils.isNotBlank(teleNume)) {
			CodecSingleResponse codecSingleResponse = decryptSingleFacade.decrypt(teleNume);
			if (codecSingleResponse != null && codecSingleResponse.getCodecText() != null) {
				teleNume = codecSingleResponse.getCodecText();
			}
		}
		log.info("teleNume:"+teleNume);
		boolean paramFlag = paramValidate(orgCode, telType, consCode, startDt, endDt, teleNume);

		HashSet<String> authTelNo = new HashSet<String>();
		if (StringUtils.isBlank(queryFlag)) {
			return resultList;
		}

		Map<String, List<PhoneCallRecord>> resultMap = new HashMap<String, List<PhoneCallRecord>>();
		if (StringUtils.isNotBlank(orderId)) {
			paramMap.put("orderId", orderId);
			resultMap = service.getStaffCallCountStatisticsFields(paramMap);
		} else {
			if (!paramFlag) {
				return resultList;
			}
			if (StringUtils.isBlank(consCode)) {
				consCode = "";
				authTelNo = getAuthTelno(orgCode);
			} else {
				Map<String, String> orgMap = new HashMap<String, String>();
				orgMap.put("consCode", consCode);
				consCode = service.getCmConsultantTel(orgMap);
				authTelNo.add(consCode);
			}

			if (telType.contains("0") && telType.contains("1")) {
				paramMap.put("telType", "");// 呼入呼出都需要查询
			} else {
				paramMap.put("telType", telType);
			}
			paramMap.put("orderId", "");
			paramMap.put("ext_no", consCode);
			paramMap.put("startDt", DateTimeUtil.dateFormat(startDt));
			paramMap.put("endDt", DateTimeUtil.dateFormat(endDt));
			paramMap.put("teleNume", teleNume);
			paramMap.put("serverLocation", serverLocation);
			resultMap = service.getStaffCallCountStatisticsFields(paramMap);
		}
		//System.out.println(resultMap.toString());
		log.info("查询录音结果:"+JSON.toJSONString(resultMap));

		// 呼出
		List<PhoneCallRecord> list1 = resultMap.get("list1");
		// 呼入
		List<PhoneCallRecord> list2 = resultMap.get("list2");

		if (list1 != null) {
			for (PhoneCallRecord entity : list1) {
				String caller = entity.getCaller();
				String callee = entity.getCallee();
				Boolean flag = validatTelNo(caller, callee, authTelNo);
				if (flag || StringUtils.isNotBlank(orderId)) {
					entity = encryptPhone(request, entity);
					resultList.add(entity);
				}
			}
		}

		if (list2 != null) {
			for (PhoneCallRecord entity : list2) {
				String caller = entity.getCaller();
				String callee = entity.getCallee();
				Boolean flag = validatTelNo(caller, callee, authTelNo);
				if (flag || StringUtils.isNotBlank(orderId)) {
					entity = encryptPhone(request, entity);
					resultList.add(entity);
				}
			}
		}
		return resultList;
	}

	private boolean validatTelNo(String caller, String callee, HashSet<String> telSet) {
		if (!StringUtils.isBlank(caller)) {
			if (telSet.contains(caller)) {
				return true;
			}
		}

		if (!StringUtils.isBlank(callee)) {
			if (telSet.contains(callee)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * @description 手机号进行加密
	 * @param request
	 * @param entity
	 * @return com.howbuy.crm.hb.domain.webservice.PhoneCallRecord
	 * @author: jianjian.yang
	 * @date: 2023/10/26 10:56
	 * @since JDK 1.8
	 */
	private PhoneCallRecord encryptPhone(HttpServletRequest request, PhoneCallRecord entity) {
		String consCode = request.getParameter("consCode");
		String callee = entity.getCallee();
		String caller = entity.getCaller();

		String userId = (String) request.getSession().getAttribute("userId");
		Map<String, String> params= Maps.newHashMap();
		String conscustNo = request.getParameter("conscustNo");
		if(StringUtils.isNotBlank(conscustNo)){
			params.put("custno", conscustNo);
			Custconstant conscodeMgrcodeByCustNo = custconstantService.getConscodeMgrcodeByCustNo(params);
			String mgrCode = conscodeMgrcodeByCustNo != null ? conscodeMgrcodeByCustNo.getSeniormgrcode() : null;
			//手机号座机号判定位数，小于等于7位表示不是手机号或座机号
			int phoneNumLength = 7;
			if (StringUtils.isNotBlank(callee) && callee.length() > phoneNumLength) {
				String calleeTel = Util.getEncryptValue(request, StaticVar.ENCRYPT_MOBILE, callee, userId, mgrCode);
				entity.setCallee(calleeTel);
				return entity;
			}

			if (StringUtils.isNotBlank(caller) && caller.length() > phoneNumLength) {
				String callerTel = Util.getEncryptValue(request, StaticVar.ENCRYPT_MOBILE, caller, userId, mgrCode);
				entity.setCaller(callerTel);
				return entity;
			}
		}

		return entity;
	}

}
