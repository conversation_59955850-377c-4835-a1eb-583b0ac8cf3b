/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.web.controller.custinfo;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.hb.domain.conscust.ConscustVO;
import com.howbuy.crm.hb.domain.hkconscust.HkConscustVO;
import com.howbuy.crm.hb.service.custinfo.RelateConscustAndHkConscustService;
import com.howbuy.crm.hb.web.controller.counter.BaseCounterController;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.db.PageData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: 关联CRM的投顾客户和香港客户页面
 * <AUTHOR>
 * @date 2023/12/13 14:18
 * @since JDK 1.8
 */
@Slf4j
@Controller
@RequestMapping(value = "/relateHkConscust")
public class RelateConscustAndHkConscustController extends BaseCounterController {

	@Autowired
	private RelateConscustAndHkConscustService relateConscustAndHkConscustService;

	/**
	 * @api {GET} /relateHkConscust/listRelateHkConscust.do listMergeConscust()
	 * @apiVersion 1.0.0
	 * @apiGroup RelateConscustAndHkConscustController
	 * @apiName listMergeConscust()
	 * @apiSuccess (响应结果) {Object} view
	 * @apiSuccess (响应结果) {Object} model
	 * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
	 * @apiSuccess (响应结果) {Boolean} cleared
	 * @apiSuccessExample 响应结果示例
	 * {"view":{},"model":{},"cleared":false,"status":"UPGRADE_REQUIRED"}
	 */
	@GetMapping("/listRelateHkConscust.do")
	public ModelAndView listMergeConscust() {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/custinfo/relateConscustAndHkConscustList");
		return modelAndView;
	}

	/**
	 * @api {POST} /relateHkConscust/listRelateConscustByPage.do listRelateConscustByPage()
	 * @apiVersion 1.0.0
	 * @apiGroup RelateConscustAndHkConscustController
	 * @apiName listRelateConscustByPage()
	 * @apiSuccess (响应结果) {Object} response
	 * @apiSuccessExample 响应结果示例
	 * {}
	 */
	@ResponseBody
	@PostMapping("/listRelateConscustByPage.do")
	public Map<String, Object> listRelateConscustByPage(HttpServletRequest request) throws Exception {
		Map<String, String> param = getSearchParamMap(request);
		PageData<ConscustVO> pageData = relateConscustAndHkConscustService.listRelateConscustByPage(param);

		// 返回查询结果
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		resultMap.put("rows", pageData.getListData());

		return resultMap;
	}

	/**
	 * @api {POST} /relateHkConscust/listRelateHkConscustByPage.do listRelateHkConscustByPage()
	 * @apiVersion 1.0.0
	 * @apiGroup RelateConscustAndHkConscustController
	 * @apiName listRelateHkConscustByPage()
	 * @apiSuccess (响应结果) {Object} response
	 * @apiSuccessExample 响应结果示例
	 * {}
	 */
	@ResponseBody
	@PostMapping("/listRelateHkConscustByPage.do")
	public Map<String, Object> listRelateHkConscustByPage(HttpServletRequest request) throws Exception {
		Map<String, String> param = getHkSearchParamMap(request);
		PageData<HkConscustVO> pageData = relateConscustAndHkConscustService.listRelateHkConscustByPage(param);

		// 返回查询结果
		Map<String, Object> resultMap = new HashMap<>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		resultMap.put("rows", pageData.getListData());

		return resultMap;
	}


	/**
	 * @description: 解析投顾客户查询条件，封装到Map中
	 * @param request 投顾客户的查询条件
	 * @return java.util.Map<java.lang.String,java.lang.String> 投顾客户查询条件Map
	 * @author: jin.wang03
	 * @date: 2023/12/13 19:25
	 * @since JDK 1.8
	 */
	private static Map<String, String> getSearchParamMap(HttpServletRequest request) throws Exception {
		String custName = request.getParameter("custName");
		String hkTxAcctNo = request.getParameter("hkTxAcctNo");
		String custNo = request.getParameter("custNo");
		String mobile = request.getParameter("mobile");
		String idNo = request.getParameter("idNo");

		Map<String, String> param = new ParamUtil(request).getParamMap();
		param.put("custNo", StringUtils.isBlank(custNo) ? null : custNo);
		param.put("custName", StringUtils.isBlank(custName) ? null : custName);
		param.put("hkTxAcctNo", StringUtils.isBlank(hkTxAcctNo) ? null : hkTxAcctNo);
		param.put("mobile", StringUtils.isBlank(mobile) ? null : DigestUtil.digest(mobile.trim()));
		param.put("idNo", StringUtils.isBlank(idNo) ? null : DigestUtil.digest(idNo.trim()));
		param.put("conscustStatus", "0");
		return param;
	}

	/**
	 * @description: 解析香港客户的查询条件，封装到Map中
	 * @param request 香港客户的查询条件
	 * @return java.util.Map<java.lang.String,java.lang.String> 香港客户查询条件Map
	 * @author: jin.wang03
	 * @date: 2023/12/14 16:21
	 * @since JDK 1.8
	 */
	private static Map<String, String> getHkSearchParamMap(HttpServletRequest request) throws Exception {
		String hkCustName = request.getParameter("hkCustName");
		String hkTxAcctNo = request.getParameter("hkTxAcctNo");
		String invstType = request.getParameter("invstType");
		String mobile = request.getParameter("mobile");
		String idNo = request.getParameter("idNo");

		Map<String, String> param = new ParamUtil(request).getParamMap();
		param.put("hkCustName", StringUtils.isBlank(hkCustName) ? null : hkCustName);
		param.put("hkTxAcctNo", StringUtils.isBlank(hkTxAcctNo) ? null : hkTxAcctNo);
		param.put("mobileDigest", StringUtils.isBlank(mobile) ? null : DigestUtil.digest(mobile.trim()));
		param.put("idNoDigest", StringUtils.isBlank(idNo) ? null : DigestUtil.digest(idNo.trim()));
		param.put("invstType", StringUtils.isBlank(invstType) ? null : invstType);

		return param;
	}


	/**
	 * @api {POST} /relateHkConscust/relate.do relate()
	 * @apiVersion 1.0.0
	 * @apiGroup RelateConscustAndHkConscustController
	 * @apiName relate()
	 * @apiSuccess (响应结果) {String} response
	 * @apiSuccessExample 响应结果示例
	 * "AwB"
	 */
	@ResponseBody
	@PostMapping("/relate.do")
	public String relate(HttpServletRequest request) {
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		String userId = userlogin.getUserId();
		String custNo = request.getParameter("custNo");
		String hkTxAcctNo = request.getParameter("hkTxAcctNo");

		return relateConscustAndHkConscustService.doRelateConscustAndHkConscust(custNo, hkTxAcctNo,userId );
	}


	/**
	 * @api {POST} /relateHkConscust/disRelate.do disRelate()
	 * @apiVersion 1.0.0
	 * @apiGroup RelateConscustAndHkConscustController
	 * @apiName disRelate()
	 * @apiSuccess (响应结果) {String} response
	 * @apiSuccessExample 响应结果示例
	 * "GI9wddSw"
	 */
	@ResponseBody
	@PostMapping("/disRelate.do")
	public String disRelate(HttpServletRequest request) {
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		String userId = userlogin.getUserId();
		String custNo = request.getParameter("custNo");
		String hkTxAcctNo = request.getParameter("hkTxAcctNo");

		return relateConscustAndHkConscustService.doDisRelateConscustAndHkConscust(custNo, hkTxAcctNo, userId);
	}

}
