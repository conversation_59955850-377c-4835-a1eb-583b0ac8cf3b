package com.howbuy.crm.hb.web.controller.custinfo;

import com.google.common.collect.Lists;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.account.client.enums.DealStatusEnum;
import com.howbuy.crm.account.client.enums.custinfo.HkAbnormalSceneEnum;
import com.howbuy.crm.account.client.enums.custinfo.HkBusinessNodeEnum;
import com.howbuy.crm.account.client.request.custinfo.*;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.AbnormalCustInfoVO;
import com.howbuy.crm.account.client.response.custinfo.AbnormalRelatedDetailVO;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.service.custinfo.MergeConscustService;
import com.howbuy.crm.hb.service.outerservice.CrmHkAbnormalOuterService;
import com.howbuy.crm.hb.web.dto.custinfo.AbnormalCustInfoDto;
import com.howbuy.crm.hb.web.request.hkabnormalcust.HkAbnormalCustInfoRequest;
import com.howbuy.crm.page.cache.ConsOrgCache;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description: 香港异常客户 Controller
 */
@Slf4j
@Controller
@RequestMapping(value = "/hkabnormalcust")
public class HkAbnormalCustController extends AbstractAbnormalCustController {

	@Autowired
	private CrmHkAbnormalOuterService hkAbnormalOuterService;

	@Autowired
	private MergeConscustService mergeConscustService;


	/**
	 * @api {GET} /hkabnormalcust/queryhkabnormalpage hkAbnormal()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName hkAbnormal()
	 * @apiDescription 跳转到视频直播访问数据
	 * @apiSuccess (响应结果) {Object} view
	 * @apiSuccess (响应结果) {Object} model
	 * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
	 * @apiSuccess (响应结果) {Boolean} cleared
	 * @apiSuccessExample 响应结果示例
	 * {"view":{},"model":{},"cleared":false,"status":"INTERNAL_SERVER_ERROR"}
	 */
	@RequestMapping("/queryhkabnormalpage")
	public ModelAndView hkAbnormal(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/custinfo/hkAbnormalCust");
		return modelAndView;
	}


	/**
	 * @api {GET} /hkabnormalcust/listhkabnomalcust.do listHkAbnomalCust()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName listHkAbnomalCust()
	 * @apiParam (请求参数) {String} custName
	 * @apiParam (请求参数) {String} hkTxAcctNo
	 * @apiParam (请求参数) {String} hboneNo
	 * @apiParam (请求参数) {String} custNo
	 * @apiParam (请求参数) {String} mobileDigest
	 * @apiParam (请求参数) {String} idNoDigest
	 * @apiParam (请求参数) {String} abnormalSource
	 * @apiParam (请求参数) {String} dealStatus
	 * @apiParam (请求参数) {String} createBginDdate
	 * @apiParam (请求参数) {String} createEndDate
	 * @apiParam (请求参数) {Number} page
	 * @apiParam (请求参数) {Number} rows
	 * @apiParamExample 请求参数示例
	 * hkTxAcctNo=tgI2Xy&custNo=J5x1G7QL&abnormalSource=eTZDRfyUM&mobileDigest=hb&idNoDigest=5mgtliljHt&dealStatus=UX4UYIu&createBginDdate=lMpKQyNG&page=3585&custName=9YnsfFKNYg&rows=4112&createEndDate=xrTPBD8&hboneNo=xvM7OcvL
	 * @apiSuccess (响应结果) {Number} page
	 * @apiSuccess (响应结果) {Number} size
	 * @apiSuccess (响应结果) {Number} total
	 * @apiSuccess (响应结果) {Array} rows
	 * @apiSuccess (响应结果) {String} rows.id 异常客户数据ID
	 * @apiSuccess (响应结果) {String} rows.messageClientId 消息通知的clientId
	 * @apiSuccess (响应结果) {String} rows.hkTxAcctNo 香港客户号
	 * @apiSuccess (响应结果) {String} rows.custName 客户姓名
	 * @apiSuccess (响应结果) {String} rows.investType 投资者类型
	 * @apiSuccess (响应结果) {String} rows.mobileAreaCode 手机地区码
	 * @apiSuccess (响应结果) {String} rows.mobileDigest 手机号摘要
	 * @apiSuccess (响应结果) {String} rows.mobileMask 手机号掩码
	 * @apiSuccess (响应结果) {String} rows.mobileCipher 手机号密文
	 * @apiSuccess (响应结果) {String} rows.idSignAreaCode 证件地区码
	 * @apiSuccess (响应结果) {String} rows.idType 证件类型
	 * @apiSuccess (响应结果) {String} rows.idNoDigest 证件号码摘要
	 * @apiSuccess (响应结果) {String} rows.idNoMask 证件号码掩码
	 * @apiSuccess (响应结果) {String} rows.idNoCipher 证件号码密文
	 * @apiSuccess (响应结果) {String} rows.hboneNo 一账通号
	 * @apiSuccess (响应结果) {String} rows.abnormalSource 异常来源：1-香港注册2-香港开户7-好买开户 [暂时同于  7-分销开户]3-香港客户信息同步4-香港客户号/一账通绑定5-一账通实名
	 * @apiSuccess (响应结果) {String} rows.abnormalSceneType 异常描述，按异常类别汇总：1-匹配到多个投顾客户号【香港注册、香港开户】2-匹配到的投顾客户号已被占用 【香港注册】3-手机号相同，证件类型/证件号不匹配 【香港开户】4-证件相同，但手机号不匹配【香港开户】5-CRM重复客户预警【香港客户信息同步】6-同时绑定香港客户号时，投顾客户号/香港客户号被占用 【好买开户、一账通实名】7-香港开户证件与一账通证件不一致 【香港开户】8-两边绑定的投顾客户号不一致【香港客户号/一账通绑定】
	 * @apiSuccess (响应结果) {String} rows.operateChannel 操作通道 1-MQ  2-菜单页面
	 * @apiSuccess (响应结果) {String} rows.creator 创建人
	 * @apiSuccess (响应结果) {Number} rows.createTimestamp 创建时间
	 * @apiSuccess (响应结果) {String} rows.modifier 修改人
	 * @apiSuccess (响应结果) {Number} rows.modifyTimestamp 修改时间
	 * @apiSuccess (响应结果) {String} rows.recStat 记录有效状态（1-正常  0-删除）
	 * @apiSuccess (响应结果) {String} rows.dealStatus 处理状态：0-未处理 1-已处理 2-无需处理
	 * @apiSuccess (响应结果) {String} rows.dealOperator 处理人
	 * @apiSuccess (响应结果) {String} rows.dealRemark 处理意见
	 * @apiSuccess (响应结果) {Object} rows.hkSideInfo 香港账户侧 客户信息
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.hkTxAcctNo
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.hboneNo
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.custName
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.investType
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.custStatus
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.custStatusDesc
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.isRealName
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.openAcct
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.idNoDigest
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.idSignAreaCode
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.idType
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.idNoMask
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.idTypeDesc
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.mobileAreaCode
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.mobileDigest
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.mobileMask
	 * @apiSuccess (响应结果) {Object} rows.hboneSideInfo 一账通账户侧 客户信息
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.hkTxAcctNo
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.hboneNo
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.custName
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.investType
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.custStatus
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.custStatusDesc
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.isRealName
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.openAcct
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.idNoDigest
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.idSignAreaCode
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.idType
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.idNoMask
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.idTypeDesc
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.mobileAreaCode
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.mobileDigest
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.mobileMask
	 * @apiSuccess (响应结果) {String} rows.relatedCustNo crm已关联的 客户号
	 * @apiSuccess (响应结果) {Number} rows.dealTimestamp 处理时间
	 * @apiSuccess (响应结果) {Array} rows.relatedList 关联客户列表
	 * @apiSuccess (响应结果) {String} rows.relatedList.id 异常客户待关联id
	 * @apiSuccess (响应结果) {String} rows.relatedList.abnormalId 异常客户数据ID
	 * @apiSuccess (响应结果) {String} rows.relatedList.custNo 客户号
	 * @apiSuccess (响应结果) {String} rows.relatedList.custName 客户名称
	 * @apiSuccess (响应结果) {String} rows.relatedList.investType 投资者类型
	 * @apiSuccess (响应结果) {String} rows.relatedList.mobileAreaCode 手机地区码
	 * @apiSuccess (响应结果) {String} rows.relatedList.mobileDigest 手机号摘要
	 * @apiSuccess (响应结果) {String} rows.relatedList.mobileMask 手机号掩码
	 * @apiSuccess (响应结果) {String} rows.relatedList.mobileCipher 手机号密文
	 * @apiSuccess (响应结果) {String} rows.relatedList.idSignAreaCode 证件地区码
	 * @apiSuccess (响应结果) {String} rows.relatedList.idType 证件类型
	 * @apiSuccess (响应结果) {String} rows.relatedList.idNoDigest 证件号码摘要
	 * @apiSuccess (响应结果) {String} rows.relatedList.idNoMask 证件号码掩码
	 * @apiSuccess (响应结果) {String} rows.relatedList.idNoCipher 证件号码密文
	 * @apiSuccess (响应结果) {String} rows.relatedList.hboneNo 一账通号
	 * @apiSuccess (响应结果) {String} rows.relatedList.hkTxAcctNo 香港客户号
	 * @apiSuccess (响应结果) {String} rows.relatedList.consCode 客户所属投顾
	 * @apiSuccess (响应结果) {String} rows.relatedList.creator 创建人
	 * @apiSuccess (响应结果) {Number} rows.relatedList.createTimestamp 创建时间
	 * @apiSuccess (响应结果) {String} rows.relatedList.modifier 修改人
	 * @apiSuccess (响应结果) {Number} rows.relatedList.modifyTimestamp 修改时间
	 * @apiSuccess (响应结果) {String} rows.relatedList.idTypeDesc 证件类型描述
	 * @apiSuccess (响应结果) {String} rows.relatedList.createDt 投顾客户号】的【创建日期】      yyyy-MM-dd
	 * @apiSuccess (响应结果) {Object} rows.relatedList.hkSideInfo 香港账户侧 客户信息
	 * @apiSuccess (响应结果) {Object} rows.relatedList.hboneSideInfo 一账通账户侧 客户信息
	 * @apiSuccess (响应结果) {String} rows.relatedList.hasTradeOrPre 是否有预约或交易 1-是 0-否
	 * @apiSuccess (响应结果) {Boolean} rows.relatedList.hasPreBook 是否有预约
	 * @apiSuccess (响应结果) {Boolean} rows.relatedList.hasTrade 是否有交易
	 * @apiSuccess (响应结果) {String} rows.relatedList.orgName 所属部门
	 * @apiSuccess (响应结果) {String} rows.relatedList.consName 所属投顾
	 * @apiSuccess (响应结果) {String} rows.relatedList.dealStatus 主表      处理状态：0-未处理 1-已处理 2-无需处理
	 * @apiSuccess (响应结果) {String} rows.idTypeDesc 证件类型描述
	 * @apiSuccess (响应结果) {String} rows.abnormalSceneDesc 异常详细描述
	 * @apiSuccessExample 响应结果示例
	 * {"total":8344,"size":3812,"page":7548,"rows":[{"operateChannel":"4Hb","modifier":"R8DlkD","hboneSideInfo":{"investType":"cTs14M5FQ","idType":"nWvPMBSXkl","idNoDigest":"sLYADPNQfM","custName":"V","idNoMask":"PDsXD8q","hkTxAcctNo":"raAT","mobileAreaCode":"wAMlJzyF","idSignAreaCode":"gAP8b","mobileDigest":"mgjMZNj","idTypeDesc":"LJwJ7nw9","isRealName":"qbPA9HqChc","custStatusDesc":"6dYQV","hboneNo":"QYVkn5r8","custStatus":"T","openAcct":"zB","mobileMask":"1VdUq"},"createTimestamp":3145818836611,"modifyTimestamp":2390302218539,"mobileAreaCode":"F","abnormalSceneDesc":"vgB28al4aW","mobileDigest":"kyFL0enjo","id":"8TD","dealRemark":"F","hboneNo":"ujeA","investType":"jS","creator":"ZHC033r5m4","idType":"3vI","messageClientId":"GS8fIsBo","dealOperator":"nC19tE","dealTimestamp":3479901023159,"idNoDigest":"i38","dealStatus":"m","custName":"Ox6u","mobileCipher":"WnWg","idNoMask":"U","hkTxAcctNo":"7eMl","idSignAreaCode":"k0iDcQS","abnormalSource":"TTg","abnormalSceneType":"aED","relatedList":[{"modifier":"ZJFUSwGfZ","consName":"Crl3","createTimestamp":3460912228549,"modifyTimestamp":101922666265,"mobileAreaCode":"ZlLra8zly","abnormalId":"iRS61","mobileDigest":"no1o","id":"jYQytfi","hboneNo":"QiES4","investType":"NmIFQB","custNo":"cmN4iK8OhT","creator":"vJw","hasTrade":false,"idType":"zq","orgName":"f2MvQC","hasPreBook":true,"idNoDigest":"EyndPC","dealStatus":"z3","custName":"EgVKxV","mobileCipher":"M","idNoMask":"TdS6","createDt":"a","consCode":"LVSsgG","hkTxAcctNo":"MvA39QiPp","idSignAreaCode":"LvQQL7","hasTradeOrPre":"GwegQJb","idTypeDesc":"mDgB","idNoCipher":"b1XzJLH","mobileMask":"TFCy7MNVa"}],"idTypeDesc":"57puBJvGk","idNoCipher":"iZWXaIlSnI","relatedCustNo":"of5","recStat":"AMkbJPc","hkSideInfo":{"investType":"H360u","idType":"HA","idNoDigest":"tg","custName":"mIixGz","idNoMask":"cEk86DJ9e","hkTxAcctNo":"TvI","mobileAreaCode":"YNPzL","idSignAreaCode":"WROJEgKbW","mobileDigest":"Z","idTypeDesc":"5rGI2","isRealName":"NdIhtP8c","custStatusDesc":"a","hboneNo":"ytYAmA","custStatus":"H0JyzbV","openAcct":"Ng6FQvGkb","mobileMask":"gTERuLMs"},"mobileMask":"NWXb9"}]}
	 */
	@ResponseBody
	@RequestMapping("/listhkabnomalcust.do")
	public PageVO<AbnormalCustInfoDto> listHkAbnomalCust(HkAbnormalCustInfoRequest request) {

		// 处理异常来源、场景和业务节点的筛选条件
		String processedAbnormalSource = processAbnormalSourceFilter(request);
		// 如果处理后的异常来源为空且原始筛选条件不为空，说明没有符合条件的数据，直接返回空结果
		if (processedAbnormalSource == null &&
				(StringUtil.isNotNullStr(request.getAbnormalSource())
						|| StringUtil.isNotNullStr(request.getScene())
						|| StringUtil.isNotNullStr(request.getBusinessNode()))) {
			PageVO<AbnormalCustInfoDto> emptyPage = new PageVO<>();
			emptyPage.setTotal(0L);
			emptyPage.setSize(request.getRows());
			emptyPage.setPage(request.getPage());
			emptyPage.setRows(Lists.newArrayList());
			return emptyPage;
		}

		//digest 处理
		if (StringUtil.isNotNullStr(request.getMobileDigest())) {
			request.setMobileDigest(DigestUtil.digest(request.getMobileDigest()));
		}
		if (StringUtil.isNotNullStr(request.getIdNoDigest())) {
			request.setIdNoDigest(DigestUtil.digest(request.getIdNoDigest()));
		}

		AbnormalCustInfoRequest custInfoRequest = getAbnormalCustInfoRequest(request, processedAbnormalSource);

		PageVO<AbnormalCustInfoVO> pageResult = hkAbnormalOuterService.queryHkAbnormalPage(custInfoRequest);

		PageVO<AbnormalCustInfoDto> returnPage = new PageVO<>();
		returnPage.setTotal(pageResult.getTotal());
		returnPage.setSize(pageResult.getSize());
		returnPage.setPage(pageResult.getPage());
		List<AbnormalCustInfoDto> rowList = Lists.newArrayList();


		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		Map<String, String> allUserMap=consOrgCache.getAllUserMap();
		Map<String, String> allOrgMap=consOrgCache.getAllOrgMap();
		Map<String, String> cons2OutletMap=consOrgCache.getCons2OutletMap();

		List<AbnormalCustInfoVO> pageList = pageResult.getRows();
		for (AbnormalCustInfoVO custInfo : pageList) {

			//主表 转译
			AbnormalCustInfoDto dto = new AbnormalCustInfoDto();
			BeanUtils.copyProperties(custInfo, dto);

			String relatedConsCode = custInfo.getRelatedConsCode();
			if (StringUtil.isNotNullStr(relatedConsCode)) {
				dto.setConsName(allUserMap.get(relatedConsCode));
				dto.setOrgName(allOrgMap.get(cons2OutletMap.get(relatedConsCode)));
			}

			//明细列表
			dto.setRelatedList(convertRelatedCustList(custInfo));
			rowList.add(dto);
		}
		returnPage.setRows(rowList);
		return returnPage;
	}

	/**
	 * @description:(请在此添加描述)
	 * @param request
	 * @param processedAbnormalSource
	 * @return com.howbuy.crm.account.client.request.custinfo.AbnormalCustInfoRequest
	 * @author: jin.wang03
	 * @date: 2025/5/23 9:43
	 * @since JDK 1.8
	 */
	private static AbnormalCustInfoRequest getAbnormalCustInfoRequest(HkAbnormalCustInfoRequest request, String processedAbnormalSource) {
		AbnormalCustInfoRequest custInfoRequest = new AbnormalCustInfoRequest();
		custInfoRequest.setCustName(request.getCustName());
		custInfoRequest.setHkTxAcctNo(request.getHkTxAcctNo());
		custInfoRequest.setHboneNo(request.getHboneNo());
		custInfoRequest.setCustNo(request.getCustNo());
		custInfoRequest.setMobileDigest(request.getMobileDigest());
		custInfoRequest.setIdNoDigest(request.getIdNoDigest());
		custInfoRequest.setDealStatus(request.getDealStatus());
		custInfoRequest.setCreateBginDdate(request.getCreateBginDdate());
		custInfoRequest.setCreateEndDate(request.getCreateEndDate());
		custInfoRequest.setAbnormalId(request.getAbnormalId());
		custInfoRequest.setAbnormalLevel(request.getAbnormalLevel());

		custInfoRequest.setAbnormalSource(processedAbnormalSource);

		custInfoRequest.setPage(request.getPage());
		custInfoRequest.setRows(request.getRows());
		return custInfoRequest;
	}

	/**
	 * 处理异常来源、场景和业务节点的筛选条件
	 * 
	 * @param request 请求参数
	 * @return 处理后的异常来源字符串，如果没有符合条件的数据，返回null
	 */
	private String processAbnormalSourceFilter(HkAbnormalCustInfoRequest request) {
		// 如果异常来源相关的筛选条件都为空，直接返回原始的异常来源
		if (StringUtil.isNullStr(request.getAbnormalSource()) 
				&& StringUtil.isNullStr(request.getScene()) 
				&& StringUtil.isNullStr(request.getBusinessNode())) {
			return request.getAbnormalSource();
		}
		
		// 存储各个筛选条件对应的异常来源集合
		Set<String> abnormalSourceSet = new HashSet<>();
		Set<String> sceneSourceSet = new HashSet<>();
		Set<String> businessNodeSourceSet = new HashSet<>();
		
		// 处理异常来源筛选条件
		if (StringUtil.isNotNullStr(request.getAbnormalSource())) {
			abnormalSourceSet.addAll(Arrays.asList(request.getAbnormalSource().split(",")));
		}
		
		// 处理场景筛选条件，使用场景枚举类
		if (StringUtil.isNotNullStr(request.getScene())) {
			String[] scenes = request.getScene().split(",");
			sceneSourceSet.addAll(HkAbnormalSceneEnum.getSourceCodesBySceneCodes(scenes));
		}
		
		// 处理业务节点筛选条件，使用业务节点枚举类
		if (StringUtil.isNotNullStr(request.getBusinessNode())) {
			String[] businessNodes = request.getBusinessNode().split(",");
			businessNodeSourceSet.addAll(HkBusinessNodeEnum.getSourceCodesByNodeCodes(businessNodes));
		}
		
		// 计算异常来源、场景和业务节点的交集
		Set<String> sourceResultSet = new HashSet<>();
		
		// 如果某个筛选条件为空，则不参与交集计算
		if (!abnormalSourceSet.isEmpty() && !sceneSourceSet.isEmpty() && !businessNodeSourceSet.isEmpty()) {
			// 三个条件都不为空，取三者交集
			sourceResultSet.addAll(abnormalSourceSet);
			sourceResultSet.retainAll(sceneSourceSet);
			sourceResultSet.retainAll(businessNodeSourceSet);
		} else if (!abnormalSourceSet.isEmpty() && !sceneSourceSet.isEmpty()) {
			// 异常来源和场景不为空，取两者交集
			sourceResultSet.addAll(abnormalSourceSet);
			sourceResultSet.retainAll(sceneSourceSet);
		} else if (!abnormalSourceSet.isEmpty() && !businessNodeSourceSet.isEmpty()) {
			// 异常来源和业务节点不为空，取两者交集
			sourceResultSet.addAll(abnormalSourceSet);
			sourceResultSet.retainAll(businessNodeSourceSet);
		} else if (!sceneSourceSet.isEmpty() && !businessNodeSourceSet.isEmpty()) {
			// 场景和业务节点不为空，取两者交集
			sourceResultSet.addAll(sceneSourceSet);
			sourceResultSet.retainAll(businessNodeSourceSet);
		} else if (!abnormalSourceSet.isEmpty()) {
			// 只有异常来源不为空
			sourceResultSet.addAll(abnormalSourceSet);
		} else if (!sceneSourceSet.isEmpty()) {
			// 只有场景不为空
			sourceResultSet.addAll(sceneSourceSet);
		} else if (!businessNodeSourceSet.isEmpty()) {
			// 只有业务节点不为空
			sourceResultSet.addAll(businessNodeSourceSet);
		}
		
		// 如果结果集为空，返回null
		if (sourceResultSet.isEmpty()) {
			return null;
		}
		
		// 将结果集转换为逗号分隔的字符串
		return String.join(",", sourceResultSet);
	}



	/**
	 * @api {GET} /hkabnormalcust/validatbeforemerge.do validateBeforeMerge()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName validateBeforeMerge()
	 * @apiParam (请求参数) {String} detailIds
	 * @apiParamExample 请求参数示例
	 * detailIds=lmfPQsOypN
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {Array} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"S","data":["OMLf7hj62"],"description":"1BEx"}
	 */
	@ResponseBody
	@RequestMapping("/validatbeforemerge.do")
	public Response<List<String>> validateBeforeMerge(String  detailIds){
		List<String> detailIdList=Lists.newArrayList(detailIds.split(","));
		if(CollectionUtils.isEmpty(detailIdList) || detailIdList.size()<2){
			return Response.fail("请至少选择2条数据再合并");
		}
//		判断②：在当前主表数据下，选中的明细数据量是否≤3
//		若否，则弹窗提示：最多选择3条数据进行合并
		if(detailIdList.size()>3){
			return Response.fail("最多选择3条数据进行合并");
		}
		List<AbnormalRelatedDetailVO>  detailList=hkAbnormalOuterService.queryHkAbnormalDetailList(detailIdList);
		if(CollectionUtils.isEmpty(detailList)){
			return Response.fail("查询异常");
		}
		//判断 明细列表的  abnormalId 是否一致
		List<String> mainIdList=detailList.stream().map(AbnormalRelatedDetailVO::getAbnormalId).distinct().collect(Collectors.toList());
		if(mainIdList.size()>1){
			return Response.fail("请在同一条香港客户数据下，选择不同投顾客户再进行合并！\n" +
					"注：不可以跨明细列表合并客户。");
		}
		List<String> custNoList=detailList.stream().map(AbnormalRelatedDetailVO::getCustNo).distinct().collect(Collectors.toList());

        //合并客户相关校验  前置判断
		ReturnMessageDto<String>  mergeResp=mergeConscustService.validateCustNoList(custNoList);
		if(!mergeResp.isSuccess()){
			return Response.fail(mergeResp.getReturnMsg());
		}

		return Response.ok(custNoList);

	}


	/**
	 * @api {GET} /hkabnormalcust/noneeddealabnormal.do noNeedDealAbnormal()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName noNeedDealAbnormal()
	 * @apiDescription 标记异常：无需处理
	 * @apiParam (请求参数) {String} id 异常客户信息id
	 * @apiParamExample 请求参数示例
	 * id=qw9F
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"b3hKMs8z","data":"Wnx3","description":"p3"}
	 */
	@ResponseBody
	@RequestMapping("/noneeddealabnormal.do")
	public Response<String> noNeedDealAbnormal(String id) {
		DealAbnormalRequest dealAbnormalRequest = new DealAbnormalRequest();
		dealAbnormalRequest.setId(id);
		dealAbnormalRequest.setOperator(getLoginUserId());
		dealAbnormalRequest.setRemark(null);
		dealAbnormalRequest.setDealStatus(DealStatusEnum.NO_NEED_DEAL.getCode());
		return hkAbnormalOuterService.dealAbnormal(dealAbnormalRequest);
	}


	/**
	 * @api {GET} /hkabnormalcust/dealabnormal.do dealAbnormal()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName dealAbnormal()
	 * @apiDescription 标记异常：已处理
	 * @apiParam (请求参数) {String} id 异常客户信息id
	 * @apiParamExample 请求参数示例
	 * id=Rw17ZX
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"pkacbJ","data":"vIovY","description":"VU7rEs10QM"}
	 */
	@ResponseBody
	@RequestMapping("/dealabnormal.do")
	public Response<String> dealAbnormal(String id) {
		DealAbnormalRequest dealAbnormalRequest = new DealAbnormalRequest();
		dealAbnormalRequest.setId(id);
		dealAbnormalRequest.setOperator(getLoginUserId());
		dealAbnormalRequest.setRemark(null);
		dealAbnormalRequest.setDealStatus(DealStatusEnum.DEAL.getCode());
		return hkAbnormalOuterService.dealAbnormal(dealAbnormalRequest);
	}


	/**
	 * @api {POST} /hkabnormalcust/unbindrelatedcustno.do unbindRelatedCustNo()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName unbindRelatedCustNo()
	 * @apiDescription 异常主表-解绑投顾客户号
	 * @apiParam (请求参数) {String} id
	 * @apiParamExample 请求参数示例
	 * id=woCOPp
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"DSGp","data":"3Wlg","description":"rVXF"}
	 */
	@ResponseBody
	@PostMapping("/unbindrelatedcustno.do")
	public Response<String> unbindRelatedCustNo(String id) {
		HkUnbindRelatedCustNoRequest dealAbnormalRequest = new HkUnbindRelatedCustNoRequest();
		dealAbnormalRequest.setId(id);
		dealAbnormalRequest.setOperator(getLoginUserId());
		return hkAbnormalOuterService.unbindRelatedCustNo(dealAbnormalRequest);
	}


	/**
	 * @api {POST} /hkabnormalcust/createcustinfobyhk.do createCustInfoByHk()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName createCustInfoByHk()
	 * @apiDescription 新建投顾客户
	 * @apiParam (请求参数) {String} id
	 * @apiParamExample 请求参数示例
	 * id=wqfcMI
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"VHQMS0d","data":"4Xps0VDn","description":"x"}
	 */
	@ResponseBody
	@PostMapping("/createcustinfobyhk.do")
	public Response<String> createCustInfoByHk(String id) {
		HkAbnormalCreateConsCustRequest dealAbnormalRequest = new HkAbnormalCreateConsCustRequest();
		dealAbnormalRequest.setId(id);
		dealAbnormalRequest.setOperator(getLoginUserId());
		return hkAbnormalOuterService.createCustInfoByHk(dealAbnormalRequest);
	}


	/**
	 * @api {POST} /hkabnormalcust/subtablebbindhkcustno.do subTablebBindHkCustNo()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName subTablebBindHkCustNo()
	 * @apiDescription 香港异常客户页，子表投顾客户号 绑定香港客户号
	 * @apiParam (请求参数) {String} id
	 * @apiParamExample 请求参数示例
	 * id=ly7XfCPuq
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"GBmX","data":"mad6","description":"nZ"}
	 */
	@ResponseBody
	@PostMapping("/subtablebbindhkcustno.do")
	public Response<String> subTablebBindHkCustNo(String id) {
		HkAbnormalSubTableBindHkCustNoRequest dealAbnormalRequest = new HkAbnormalSubTableBindHkCustNoRequest();
		dealAbnormalRequest.setId(id);
		dealAbnormalRequest.setOperator(getLoginUserId());
		return hkAbnormalOuterService.subTablebBindHkCustNo(dealAbnormalRequest);
	}


	/**
	 * @api {POST} /hkabnormalcust/validatebeforeupdatecustinfobyhk.do validatebBeforeUpdateCustInfoByHk()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName validatebBeforeUpdateCustInfoByHk()
	 * @apiDescription 香港异常客户页- 子表 使用香港开户信息更新投顾客户 前置校验
	 * @apiParam (请求参数) {String} id
	 * @apiParamExample 请求参数示例
	 * id=Zx81vpYp7
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"fAb48Ts","data":"ZC","description":"g"}
	 */
	@ResponseBody
	@PostMapping("/validatebeforeupdatecustinfobyhk.do")
	public Response<String> validatebBeforeUpdateCustInfoByHk(String id) {
		HkAbnormalUpdateConsCustRequest dealAbnormalRequest = new HkAbnormalUpdateConsCustRequest();
		dealAbnormalRequest.setId(id);
		dealAbnormalRequest.setOperator(getLoginUserId());
		return hkAbnormalOuterService.validatebBeforeUpdateCustInfoByHk(dealAbnormalRequest);
	}


	/**
	 * @api {POST} /hkabnormalcust/updatecustinfobyhk.do updateCustInfoByHk()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName updateCustInfoByHk()
	 * @apiDescription 香港异常客户页- 子表 使用香港开户信息更新投顾客户
	 * @apiParam (请求参数) {String} id
	 * @apiParamExample 请求参数示例
	 * id=3JNmQW
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"qcA","data":"0","description":"HPM"}
	 */
	@ResponseBody
	@PostMapping("/updatecustinfobyhk.do")
	public Response<String> updateCustInfoByHk(String id) {
		HkAbnormalUpdateConsCustRequest dealAbnormalRequest = new HkAbnormalUpdateConsCustRequest();
		dealAbnormalRequest.setId(id);
		dealAbnormalRequest.setOperator(getLoginUserId());
		return hkAbnormalOuterService.updateCustInfoByHk(dealAbnormalRequest);
	}

	/**
	 * @api {GET} /hkabnormalcust/batchnoneeddealabnormal.do batchNoNeedDealAbnormal()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName batchNoNeedDealAbnormal()
	 * @apiDescription 批量设置异常信息为：无需处理
	 * @apiParam (请求参数) {String} ids id列表
	 * @apiParamExample 请求参数示例
	 * ids=c2
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"ZaHFPlo74z","data":"4","description":"StUHN86"}
	 */
	@ResponseBody
	@RequestMapping("/batchnoneeddealabnormal.do")
	public Response<String> batchNoNeedDealAbnormal(String  ids ) {
		List<String> idList=Lists.newArrayList(ids.split(","));
		BatchDealAbnormalRequest dealAbnormalRequest = new BatchDealAbnormalRequest();
		dealAbnormalRequest.setIdList(idList);
		dealAbnormalRequest.setOperator(getLoginUserId());
		dealAbnormalRequest.setRemark(null);
		dealAbnormalRequest.setDealStatus(DealStatusEnum.NO_NEED_DEAL.getCode());
		return hkAbnormalOuterService.batchDealAbnormal(dealAbnormalRequest);
	}




}
