package com.howbuy.crm.hb.web.controller.insur;

import com.alibaba.dubbo.common.json.JSON;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import com.howbuy.crm.hb.domain.custinfo.CmCustconstant;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.insur.*;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.CmCustconstantService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.insur.*;
import com.howbuy.crm.hb.service.reward.CmCustPrpSourceCoeffService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.nt.uploadmodule.dto.NtCmUploadModule;
import com.howbuy.crm.nt.uploadmodule.service.UploadModuleService;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prosale.service.PreBookService;
import com.howbuy.dfile.HFileService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.enums.CrmUploadModuleEnum;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @version 1.0
 * @Description: Controller
 * @created
 */
@Slf4j
@Controller
@RequestMapping(value = "/insur")
public class CmBxPrebookInfoController extends BaseController {

	@Autowired
	private CmBxPrebookBuyinfoService cmBxPrebookBuyinfoService;

	@Autowired
	private CmBxProductGroupService cmBxProductGroupService;

	@Autowired
	private CmBxCompanyService cmBxCompanyService;
	
	@Autowired
	private CmBxChannelService cmBxChannelService;
	
	@Autowired
	private CmBxProductChannelService cmBxProductChannelService;
	
	@Autowired
	private CmBxProductService cmBxProductService;
	
	@Autowired
	private CommonService commonService;
	
	@Autowired
	private CmBxPrebookinfoService cmBxPrebookinfoService;
	
	@Autowired
	private CmBxPrebookSigninfoService cmBxPrebookSigninfoService;
	
	@Autowired
	private CmBxPrebookAnnexService cmBxPrebookAnnexService;
	
	@Autowired
	private CmCustconstantService cmCustconstantService;
	
	@Autowired
	private CmBxPrebookEndpayListService cmBxPrebookEndpayListService;
	
	@Autowired
	private ConscustService conscustService;
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;
	
	@Autowired
	private DecryptSingleFacade decryptSingleFacade;

	@Autowired
	private PageVisitLogService pageVisitLogService;

	@Autowired
	private UploadModuleService ntUploadModuleService;

	@Autowired
	private CmBxEditLogService cmBxEditLogService;

	@Autowired
	protected PreBookService preBookService;
	@Autowired
	protected CmCustPrpSourceCoeffService cmCustPrpSourceCoeffService;

	//迁移defile  保险相关业务   2024年6月19日  /data/files/insur
	//1 已知： CM_BX_PREBOOK_ANNEX 表中 filepath 全部为： '/data/files/insur/'
	//2 实际业务数据存储示例：
//	PREID	FILETYPE	FILENAME	FILEPATH	SUFNAME	FILESIZE	ISDEL
// 			1840	3	宋莹女士认购天天向上 打款凭证年缴30万	/data/files/insur/	JPG	8776509	0
//			1840	3	宋莹女士认购天天向上 打款凭证年缴30万	/data/files/insur/	JPG	8776509	0
//			1840	3	宋莹女士认购天天向上 打款凭证年缴30万	/data/files/insur/	JPG	8776509	1
//			1840	3	宋莹女士认购天天向上 打款凭证年缴30万	/data/files/insur/	JPG	8776509	0
//			1840	3	宋莹女士认购天天向上 打款凭证年缴30万	/data/files/insur/	JPG	8776509	0
//			1840	3	宋莹女士认购天天向上 打款凭证年缴30万	/data/files/insur/	JPG	8776509	0

	//3 实际NFS 存储示例：
	// /data/files/insur/1840/3/xxxx.jpg
//	[log@w-crm-hb-10-12-50-46 3]$ pwd
///data/files/insur/1840/3
//			[log@w-crm-hb-10-12-50-46 3]$ ls
//1842.JPG  1843.JPG  1844.JPG  1845.JPG  1846.JPG  1953.JPG

	//历史配置路径： /data/files/insur   暂不删除
//	@Value("${UPLOAD_INSURURL}")
	private static String insurPath="/data/files/insur/";

	/**
	 * 创新产品相关 附件存储配置
	 */
	private static final String  INSUR_FILE_PATH_CONFIG="insur_file_config";


	/**
	 * 跳转到产品预约管理页面方法
	 *
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listinsurprebook.do")
	public ModelAndView listinsurprebook(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/insur/listinsurprebook");
		return modelAndView;
	}

	/**
	 * 加载页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listInsurPrebookByPage_json.do")
	public Map<String, Object> listInsurPrebookByPage(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		PageData<CmBxPrebookinfo> pageData = cmBxPrebookinfoService.listCmBxPrebookinfoByPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmBxPrebookinfo> listdata = pageData.getListData();
		for (CmBxPrebookinfo info : listdata) {
			info.setPrestateval(ConstantCache.getInstance().getVal("insurprestate", info.getPrestate()));
			info.setInsurstateval(ConstantCache.getInstance().getVal("insurstate", info.getInsurstate()));

            ConsOrgCache orgcache = ConsOrgCache.getInstance();
            String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
            if("0".equals(uporgcode)){
                info.setUporgname(orgcache.getAllOrgMap().get(info.getOrgcode()));
            }else{
                info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
            }

			info.setOrgcode(ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
			info.setConscode(ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
			info.setRelation(ConstantCache.getInstance().getVal("insurrelation", info.getRelation()));
			info.setBusitype(ConstantCache.getInstance().getVal("insurbusitype", info.getBusitype()));
			info.setProdtype(ConstantCache.getInstance().getVal("insurprodtype", info.getProdtype()));
			info.setPaystateval(ConstantCache.getInstance().getVal("insurpaystate", info.getPaystate()));
			info.setVisitstateval(ConstantCache.getInstance().getVal("insurvisitstate", info.getVisitstate()));
			info.setPayyears(ConstantCache.getInstance().getVal("insurpayyears", info.getPayyears()));
			info.setBusisourceval(ConstantCache.getInstance().getVal("insurbusisource", info.getBusisource()));
			info.setEnsureyears(ConstantCache.getInstance().getVal("insurensureyears", info.getEnsureyears()));
		}
		resultMap.put("rows", listdata);
		return resultMap;
	}
	
	@ResponseBody
    @RequestMapping("/signInsurPre.do")
    public ModelAndView signInsurPre(HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
		String id = request.getParameter("id");
        Map<String,Object> map = new HashMap<String,Object>(8);
        map.put("preid", id);
        //查询预约信息
        Map<String,Object> parapre = new HashMap<String,Object>(1);
        parapre.put("id", id);
        CmBxPrebookinfo preinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(parapre);
        map.put("signdt", preinfo.getExpectsigndt());
        String fundcode = preinfo.getFundcode();
       //查询产品信息
        Map<String,String> paramprod = new HashMap<String,String>(2);
        paramprod.put("fundcode", fundcode);
        paramprod.put("isdel", StaticVar.INSUR_ISDEL_NO);
        CmBxProduct product = cmBxProductService.getCmBxProduct(paramprod);
        if(product != null){
            map.put("busitype", product.getBusitype());
        }else{
        	map.put("busitype", "");
        }
        //查询签单信息
        Map<String,Object> paramsign = new HashMap<String,Object>(1);
        paramsign.put("preid", preinfo.getId());
        CmBxPrebookSigninfo signinfo = cmBxPrebookSigninfoService.getCmBxPrebookSigninfo(paramsign);
        if(signinfo != null){
        	map.put("insurid", signinfo.getInsurid());
        	map.put("paystate", signinfo.getPaystate());
        	map.put("paydt", signinfo.getPaydt());
        	map.put("signdt", signinfo.getSigndt());
        }else{
        	map.put("insurid", "");
        	map.put("paystate", StaticVar.INSUR_PAYSTAT_HASPAY);
        	map.put("paydt", "");
        }
        
        //查询是否有上传回执或小票附件信息
        Map<String,Object> paramannex = new HashMap<String,Object>(3);
        paramannex.put("preid", preinfo.getId());
        paramannex.put("filetype", StaticVar.INSUR_FILETYPE_HZXP);
        paramannex.put("isdel", StaticVar.INSUR_ISDEL_NO);
        List<CmBxPrebookAnnex> listannex = cmBxPrebookAnnexService.listCmBxPrebookAnnex(paramannex);
        if(listannex != null && listannex.size() > 0){
        	map.put("hasannex", "1");
        }else{
        	map.put("hasannex", "0");
        }
        return new ModelAndView("insur/signinsurpre", "map", map);
    }
	
	
	/**
	 * (跳转到新增预约投保界面)
	 */
	@ResponseBody
	@RequestMapping("/addCMBxPrebookinfo.do")
	public ModelAndView addCmBxPrebookinfo(HttpServletRequest request, HttpServletResponse response)
			throws Exception {
		Map<String,Object> map = new HashMap<String,Object>(8);
		NtCmUploadModule uploadmodule = ntUploadModuleService.getCmUploadModule(CrmUploadModuleEnum.INSUR_PREBOOKINSURE);
		StringBuilder sb = new StringBuilder();
		if(uploadmodule.getTypeSuffixsMap() != null){
			sb.append("仅支持以下类型的格式文件！</br></br><table border='1' cellpadding='0' cellspacing='0' ><tr bgcolor='#F5F5F5'><td style='width: 80px;' align='center'>类型</td><td style='width: 400px;' align='center'>支持格式</td></tr>");
			uploadmodule.getTypeSuffixsMap().forEach((key,value)->{
				sb.append("<tr><td align='center'>"+key+"</td><td>"+value+"</td></tr>");
			});
			sb.append("</table>");
		}
		uploadmodule.setTypeSuffixResult(sb.toString());
		map.put("upmod", uploadmodule);
		return new ModelAndView("insur/addinsurprebook", "map", map);
	}
	
	
	/**
	 * 加载页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/getAllMsgByBxFundcode.do")
	public Map<String, Object> getAllMsgByBxFundcode(HttpServletRequest request) throws Exception {
		List<Map<String,Object>> channList = new ArrayList<>();
		List<Map<String,Object>> busitypeList = new ArrayList<>();
		List<Map<String,Object>> compList = new ArrayList<>();
		List<Map<String,Object>> currList = new ArrayList<>();
		List<CmBxProductGroup> listCmBxProductGroup = new ArrayList<CmBxProductGroup>();
		String defaultchann = "";
		String defaultbusitype = "";
		String defaultcomp = "";
		String defaultcurr = "";
		
        Map<String,Object> map = new HashMap<String,Object>(8);
        String fundcode = request.getParameter("fundcode");
		//查询合作渠道
        Map<String,String> paramother = new HashMap<String,String>(2);
        paramother.put("isdel", StaticVar.INSUR_ISDEL_NO);
        paramother.put("fundcode", fundcode);
        
        List<CmBxProductChannel> listProChannel = cmBxProductChannelService.listCmBxProductChannel(paramother);
        if(CollectionUtils.isNotEmpty(listProChannel)){
        	if(listProChannel.size() == 1){
        		defaultchann = listProChannel.get(0).getChanncode();
        	}
        	Map<String, Object> channmap = new HashMap<>(2);
			channmap.put("id", "");
    		channmap.put("text", "请选择");
	        channList.add(channmap);
        	for(CmBxProductChannel proChannel : listProChannel){
        		paramother.put("channcode", proChannel.getChanncode());
        		CmBxChannel channel = cmBxChannelService.getCmBxChannel(paramother);
        		if(channel != null){
        			channmap = new HashMap<>(2);
        			channmap.put("id", channel.getChanncode());
            		channmap.put("text", channel.getChannname());
        	        channList.add(channmap);
        		}
        	}
        }

        CmBxProduct product = cmBxProductService.getCmBxProduct(paramother);
        if(product == null){
        	product = new CmBxProduct();
        }else{
	        ConstantCache constantCache= ConstantCache.getInstance();
	        //查询业务类型
	        product.getBusitype();
	        Map<String, Object> busitypemap = new HashMap<>(2);
	        defaultbusitype = product.getBusitype();
	        busitypemap.put("id", product.getBusitype());
	        busitypemap.put("text", StringUtils.isBlank(product.getBusitype())  ?  null : constantCache.getVal("insurbusitype", product.getBusitype()));
	        busitypeList.add(busitypemap);
	        
	        //查询保险公司
	        if(StringUtil.isNotNullStr(product.getCompcode())){
	        	Map<String, Object> compmap = new HashMap<>(2);
		        paramother.put("compcode", product.getCompcode());
		        CmBxCompany comp = cmBxCompanyService.getCmBxCompany(paramother);
		        defaultcomp = comp.getCompcode();
		        compmap.put("id", comp.getCompcode());
				compmap.put("text", comp.getCompname());
				compList.add(compmap);
	        }
	        //查询币种
	        if(StringUtil.isNotNullStr(product.getCurrency())){
	        	LinkedHashMap<String, String> mapcache = ConstantCache.getInstance().getConstantKeyVal("currencys");
		        String[] arrcur = product.getCurrency().split(",");
		        if(arrcur.length ==1){
		        	defaultcurr = arrcur[0];
		        }
		        Map<String, Object> curr = new HashMap<>(2);
		        curr.put("id", "");
		        curr.put("text", "请选择");
		        currList.add(curr);
		        for(String cur : arrcur){
		        	if(curr != null){
		        		curr = new HashMap<String,Object>(2);
		        		curr.put("id", cur);
		        		curr.put("text", mapcache.get(cur));
		        		currList.add(curr);
		        	}
		        }
	        }
			//1.产品
			if("1".equals(product.getProdproper())){
				CmBxProductGroup group = new CmBxProductGroup();
				group.setAttfundcode(product.getFundcode());
				group.setAttfundname(product.getFundname());
				group.setProdtype(ConstantCache.getInstance().getVal("insurprodtype", product.getProdtype()));
				//1主险；2附加险
				group.setProdproper(product.getProdproper());
				listCmBxProductGroup.add(group);
				
				List<CmBxProductGroup> listGroup = cmBxProductGroupService.listCmBxProductGroupMsg(paramother);
				if(CollectionUtils.isNotEmpty(listGroup)){
					for(CmBxProductGroup model : listGroup){
						model.setProdtype(ConstantCache.getInstance().getVal("insurprodtype", model.getProdtype()));
						listCmBxProductGroup.add(model);
					}
				}
			}else{
				CmBxProductGroup group = new CmBxProductGroup();
				group.setAttfundcode(fundcode);
				group.setAttfundname(product.getFundname());
				group.setProdtype(ConstantCache.getInstance().getVal("insurprodtype", product.getProdtype()));
				//1主险；2附加险
				group.setProdproper(product.getProdproper());
				listCmBxProductGroup.add(group);
			}
        }
    	map.put("channList",channList);
    	map.put("busitypeList", busitypeList);
        map.put("compList",compList);
        map.put("currList", currList);
        map.put("listCmBxProductGroup", listCmBxProductGroup);
        map.put("defaultchann",defaultchann);
    	map.put("defaultbusitype", defaultbusitype);
        map.put("defaultcomp",defaultcomp);
        map.put("defaultcurr", defaultcurr);
        map.put("istpcheck", product.getIstpcheck());
		return map;
	}
	
	/**
     * 提交上传文件
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveSignInsurPre.do", method = RequestMethod.POST)
    public Map<String, Object> saveSignInsurPre(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        User user = getLoginUser();
        String preid = request.getParameter("preid");
        String insurid = request.getParameter("insurid");
        String signdt = request.getParameter("signdt");
        String paystate = request.getParameter("paystate");
        String paydt = request.getParameter("paydt");
        
        //判断除这个预约以外的预约是否用了这个保单号？
        Map<String, Object> paramsign = new HashMap<String, Object>(3);
        paramsign.put("prestates", " ('"+StaticVar.INSUR_PRESTATE_CONFORM+"','"+StaticVar.INSUR_PRESTATE_NOTCONFORM+"') ");
        paramsign.put("insurid", insurid);
        paramsign.put("isnotpreid", preid);
        List<CmBxPrebookSigninfo> signlist = cmBxPrebookSigninfoService.listCmBxPrebookSigninfo(paramsign);
        if(signlist != null && signlist.size() > 0){
        	resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "保单号已存在，请重新输入");
            return resultMap;
        }
        paramsign.clear();
        paramsign.put("preid", preid);
        CmBxPrebookSigninfo signinfo = cmBxPrebookSigninfoService.getCmBxPrebookSigninfo(paramsign);
        CmBxPrebookSigninfo signinfoin = null;
        if(signinfo == null){
        	signinfoin = new CmBxPrebookSigninfo();
        	signinfoin.setPreid(new BigDecimal(preid));
        	signinfoin.setPaydt(paydt);
        	signinfoin.setSigndt(signdt);
        	signinfoin.setPaystate(paystate);
        	signinfoin.setInsurid(insurid);
        	signinfoin.setCreator(user.getUserId());
        	signinfoin.setCreatdt(new Date());
        }else{
        	signinfo.setInsurid(insurid);
        	signinfo.setPaydt(paydt);
        	signinfo.setSigndt(signdt);
        	signinfo.setPaystate(paystate);
        	signinfo.setModifier(user.getUserId());
        	signinfo.setModifydt(new Date());
        }
        
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> files = multipartRequest.getFiles("filelist[]");
        List<CmBxPrebookAnnex> listannex = new ArrayList<CmBxPrebookAnnex>();
        try {
        	if (files != null && files.size() > 0) {
                for (int i = 0; i < files.size(); i++) {

                    MultipartFile file = files.get(i);
                    String filename = file.getOriginalFilename();
                    String suffix = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
                    
                    CmBxPrebookAnnex annex = new CmBxPrebookAnnex();
                    String id = commonService.getSeqValue("SEQ_INSUR_ID");
                    int filesize = (int) file.getSize();
                    annex.setId(new BigDecimal(id));
                    annex.setPreid(new BigDecimal(preid));
                    annex.setFiletype(StaticVar.INSUR_FILETYPE_HZXP);
                    annex.setFilesize(new BigDecimal(filesize));
                    annex.setFilename(filename.substring(0,filename.lastIndexOf(".")));
                    annex.setFilepath(insurPath);
                    annex.setSufname(suffix);
                    annex.setIsdel(StaticVar.INSUR_ISDEL_NO);
                    annex.setCreator(user.getUserId());
                    listannex.add(annex);
                    String servicefilename = id + "." + suffix;
                    try {
//                        FileUtil.uploadFile(file.getInputStream(),
//								path+File.separator+preid+File.separator+StaticVar.INSUR_FILETYPE_HZXP+File.separator, servicefilename);
						processInsurFile(annex,file.getBytes());
                    } catch (Exception e) {
                        log.error("saveSignInsurPre:" + servicefilename + ":" + e.getMessage(), e);
                        resultMap.put("uploadFlag", "error");
                        resultMap.put("errorMsg", "签单失败，系统问题");
                        return resultMap;
                    }
                }
            }
        	//批量处理数据
        	cmBxPrebookSigninfoService.mergeCmBxPrebookSigninfo(signinfoin, signinfo, listannex);
            resultMap.put("uploadFlag", "success");
            resultMap.put("errorMsg", "签单成功");
            
           //更新新投保人和受保人年龄
           upAgeAndInsurage("2",request, new BigDecimal(preid),signdt);
        } catch (Exception ex) {
            ex.printStackTrace();
            resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "上传失败，系统问题");
        }

        return resultMap;
    }


	/**
	 * 获取 存储的 相对路径
	 * @param annex
	 * @return
	 */
	private String getRelativePath(CmBxPrebookAnnex annex){
		return String.join(File.separator,
				annex.getPreid().toString(),
				annex.getFiletype());
	}

	private String getStoreFileName(CmBxPrebookAnnex annex){
		return String.join(".",annex.getId().toString(),annex.getSufname());
	}

	/**
	 * webdav 存储
	 * @param annex
	 * @param fileBytes
	 */
	private void  processInsurFile(CmBxPrebookAnnex annex,byte[] fileBytes) throws Exception{
		//存储相对路径
		String relativePath=getRelativePath(annex);
		//存储 文件名
		String fileName=getStoreFileName(annex);
		HFileService instance = HFileService.getInstance();
		//webdav不支持参数 inputStream. 详见[WebDavHFileService]
		instance.write(INSUR_FILE_PATH_CONFIG, relativePath,fileName, fileBytes);
	}
    
    
    @RequestMapping("/changePrebook.do")
    @ResponseBody
    public String changePrebook(HttpServletRequest request){
    	String result="";
        String id = request.getParameter("id");
        User userlogin = getLoginUser();
        //查询预约
        Map<String,Object> parapre = new HashMap<String,Object>(1);
        parapre.put("id", id);
        CmBxPrebookinfo preinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(parapre);
        if(preinfo == null){
            return "预约不存在";
        }
        preinfo.setPrestate(StaticVar.INSUR_PRESTATE_CANCEL);
        preinfo.setModifier(userlogin.getUserId());
        preinfo.setModifydt(new Date());
        cmBxPrebookinfoService.updateCmBxPrebookinfo(preinfo);
		result = "success";
        return result;
    }
    
    /**
     * 新增预约
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveInsurPrebook.do", method = RequestMethod.POST)
    public Map<String, Object> saveInsurPrebook(HttpServletRequest request) throws Exception{
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        User user = getLoginUser();
        
    	String fundcode = request.getParameter("fundcode");
        String conscustno = request.getParameter("conscustno");
        String idtype = request.getParameter("idtype");
        String idno = request.getParameter("idno");
        String age = request.getParameter("age");
        String insurage = request.getParameter("insurage");
        String insurname = request.getParameter("insurname");
        String insuridtype = request.getParameter("insuridtype");
        String insuridno = request.getParameter("insuridno");
        String relation = request.getParameter("relation");
        String otherrelation = request.getParameter("otherrelation");
        String expectsigndt = request.getParameter("expectsigndt");
        String channcode = request.getParameter("channcode");
        //String busitype = request.getParameter("busitype");
        //String compcode = request.getParameter("compcode");
        String currency = request.getParameter("currency");
        String consremark = request.getParameter("consremark");
        String attrinsurs = request.getParameter("attrinsurs");
        String busisource = request.getParameter("busisource");
        log.info("attrinsurs:{}" , attrinsurs);
        
        if(StringUtils.isBlank(fundcode)){
        	 resultMap.put("uploadFlag", "error");
             resultMap.put("errorMsg", "请选项产品！");
             return resultMap;
        }
        
        Map<String, String> param = new HashMap<String, String>(2);
        param.put("isdel", "1");
        param.put("fundcode", fundcode);
        CmBxProduct pro = cmBxProductService.getCmBxProduct(param);
        if(pro == null){
       	    resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "未找到产品信息，请联系开发排查！");
            return resultMap;
       }
        
        CmBxPrebookinfo prebookinfo = new CmBxPrebookinfo();
        String preid = commonService.getSeqValue("SEQ_INSUR_ID");
        prebookinfo.setId(new BigDecimal(preid));
        prebookinfo.setFundcode(fundcode);
        prebookinfo.setBusisource(busisource);
        prebookinfo.setConscustno(conscustno);
        prebookinfo.setIdtype(idtype);
        //prebookinfo.setIdno(idno);
        if(StringUtil.isNotNullStr(idno)){
    		prebookinfo.setIdnoDigest(DigestUtil.digest(idno.trim()));
    		prebookinfo.setIdnoMask(MaskUtil.maskIdNo(idno.trim()));
    		prebookinfo.setIdnoCipher(encryptSingleFacade.encrypt(idno.trim()).getCodecText());
    	}
        prebookinfo.setAge(Integer.parseInt(age));
        prebookinfo.setInsurage(Integer.parseInt(insurage));
        //关系是本人的，受保人的姓名查询保人的姓名
        prebookinfo.setInsurname(insurname);
        setBxInfoByRelation(relation,conscustno,insuridno,prebookinfo);
        prebookinfo.setInsuridtype(insuridtype);
        prebookinfo.setRelation(relation);
        prebookinfo.setOtherrelation(otherrelation);
        prebookinfo.setExpectsigndt(expectsigndt);
        prebookinfo.setChanncode(channcode);
        prebookinfo.setCurrency(currency);
        prebookinfo.setConsremark(consremark);
        //预约状态：未确认
        prebookinfo.setPrestate(StaticVar.INSUR_PRESTATE_NOTCONFORM);
        //保单状态:
        prebookinfo.setInsurstate(StaticVar.INSUR_STATE_DQD);
        //复核状态:待复核
        prebookinfo.setCheckstate(StaticVar.INSUR_CHECHSTATE_NOTCHECK);
        //财务结算状态:待结算
        prebookinfo.setFinstate(StaticVar.INSUR_FINSTATE_DJS);
        prebookinfo.setCreatdt(new Date());
        //查询客户的当前投顾
        Map<String,String> paramcust = new HashMap<String,String>(1);
        paramcust.put("custno", conscustno);
        CmCustconstant cons = cmCustconstantService.getCmCustconstant(paramcust);
        prebookinfo.setCreator(cons.getConscode());
        prebookinfo.setRealcreator(user.getUserId());
        cmBxPrebookinfoService.insertCmBxPrebookinfo(prebookinfo);
        log.info("saveCmBxPrebookinfo:{}" , JSON.json(prebookinfo));
        
        if(StringUtils.isBlank(attrinsurs)){
        	resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "至少输入一条购买产品信息！");
            return resultMap;
        }
        
        String[]  arrCmBxPrebookBuyinfo = attrinsurs.split(":");
        String buyinfoid = null;
        for(String strCmBxPrebookBuyinfo : arrCmBxPrebookBuyinfo){
        	if(StringUtils.isNotBlank(strCmBxPrebookBuyinfo)){
        		String [] arrField = strCmBxPrebookBuyinfo.split(",");
            	CmBxPrebookBuyinfo cmBxPrebookBuyinfo = new CmBxPrebookBuyinfo();
            	buyinfoid = commonService.getSeqValue("SEQ_INSUR_ID");
            	cmBxPrebookBuyinfo.setId(new BigDecimal(buyinfoid));
            	cmBxPrebookBuyinfo.setPreid(prebookinfo.getId());
				cmBxPrebookBuyinfo.setFundcode(arrField[0]);
				cmBxPrebookBuyinfo.setPayyears(arrField[1]);
				cmBxPrebookBuyinfo.setEnsureyears(arrField[2]);
				cmBxPrebookBuyinfo.setYearamk(StringUtils.isBlank(arrField[3]) ? null : new BigDecimal(arrField[3]));
				if (arrField.length == 5) {
					cmBxPrebookBuyinfo.setInsuramk(StringUtils.isBlank(arrField[4]) ? null : new BigDecimal(arrField[4]));
				}
				cmBxPrebookBuyinfo.setCreator(user.getUserId());
				cmBxPrebookBuyinfo.setCreatdt(new Date());
				cmBxPrebookBuyinfo.setIsdel(StaticVar.INSUR_ISDEL_NO);
				log.info("添加预约保单，获取管理系数时客户号：{}", conscustno);
				cmBxPrebookBuyinfo.setManagePoint(getManagePointByCusNo(conscustno));
				log.info("添加预约保单:{}", cmBxPrebookBuyinfo);
				cmBxPrebookBuyinfoService.insertCmBxPrebookBuyinfo(cmBxPrebookBuyinfo);
			}
		}
        
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> jhsfiles = multipartRequest.getFiles("jhsfilelist[]");
        List<MultipartFile> yybfiles = multipartRequest.getFiles("yybfilelist[]");
        List<CmBxPrebookAnnex> listannex = new ArrayList<CmBxPrebookAnnex>();
        try {
        	//计划书
        	if (jhsfiles != null && jhsfiles.size() > 0) {
                for (int i = 0; i < jhsfiles.size(); i++) {
                    MultipartFile file = jhsfiles.get(i);
                    String filename = file.getOriginalFilename();
                    String suffix = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
                    
                    CmBxPrebookAnnex annex = new CmBxPrebookAnnex();
                    String id = commonService.getSeqValue("SEQ_INSUR_ID");
                    int filesize = (int) file.getSize();
                    annex.setId(new BigDecimal(id));
                    annex.setPreid(new BigDecimal(preid));
                    annex.setFiletype(StaticVar.INSUR_FILETYPE_JHS);
                    annex.setFilesize(new BigDecimal(filesize));
                    annex.setFilename(filename.substring(0,filename.lastIndexOf(".")));
                    annex.setFilepath(insurPath);
                    annex.setSufname(suffix);
                    annex.setIsdel(StaticVar.INSUR_ISDEL_NO);
                    annex.setCreator(user.getUserId());
                    listannex.add(annex);
                    String servicefilename = id + "." + suffix;
                    try {
//                        FileUtil.uploadFile(file.getInputStream(), path+File.separator+preid+File.separator+StaticVar.INSUR_FILETYPE_JHS+File.separator, servicefilename);
                        processInsurFile(annex,file.getBytes());
                    } catch (Exception e) {
                        log.error("saveSignInsurPre:" + servicefilename + ":" + e.getMessage(), e);
                        resultMap.put("uploadFlag", "error");
                        resultMap.put("errorMsg", "新增预约投保计划书失败，系统问题");
                        return resultMap;
                    }
                }
            }
        	
        	//预约表
        	if (yybfiles != null && yybfiles.size() > 0) {
                for (int i = 0; i < yybfiles.size(); i++) {
                    MultipartFile file = yybfiles.get(i);
                    String filename = file.getOriginalFilename();
                    String suffix = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
                    
                    CmBxPrebookAnnex annex = new CmBxPrebookAnnex();
                    String id = commonService.getSeqValue("SEQ_INSUR_ID");
                    int filesize = (int) file.getSize();
                    annex.setId(new BigDecimal(id));
                    annex.setPreid(new BigDecimal(preid));
                    annex.setFiletype(StaticVar.INSUR_FILETYPE_SQD);
                    annex.setFilesize(new BigDecimal(filesize));
                    annex.setFilename(filename.substring(0,filename.lastIndexOf(".")));
                    annex.setFilepath(insurPath);
                    annex.setSufname(suffix);
                    annex.setIsdel(StaticVar.INSUR_ISDEL_NO);
                    annex.setCreator(user.getUserId());
                    listannex.add(annex);
                    String servicefilename = id + "." + suffix;
                    try {
//                        FileUtil.uploadFile(file.getInputStream(), path+File.separator+preid+File.separator+StaticVar.INSUR_FILETYPE_SQD+File.separator, servicefilename);
						processInsurFile(annex,file.getBytes());
                    } catch (Exception e) {
                        log.error("saveSignInsurPre:" + servicefilename + ":" + e.getMessage(), e);
                        resultMap.put("uploadFlag", "error");
                        resultMap.put("errorMsg", "新增预约投保预约表失败，系统问题");
                        return resultMap;
                    }
                }
            }
        	//批量处理数据
        	cmBxPrebookSigninfoService.batchUploadAnnex(listannex);
            resultMap.put("uploadFlag", "success");
            resultMap.put("errorMsg", "新增预约投保成功");
            
            //更新新投保人和受保人年龄
			upAgeAndInsurage("1", request, prebookinfo.getId(), null);
		} catch (Exception ex) {
			ex.printStackTrace();
			resultMap.put("uploadFlag", "error");
			resultMap.put("errorMsg", "上传失败，系统问题");
		}

		return resultMap;
	}

	/**
	 * 关系是本人的，受保人的姓名查询保人的姓名
	 *
	 * @param relation
	 * @param conscustno
	 * @param insuridno
	 * @param prebookinfo
	 */
	private void setBxInfoByRelation(String relation, String conscustno, String insuridno, CmBxPrebookinfo prebookinfo) {
		if (StaticVar.INSUR_RELATION_ME.equals(relation)) {
			Conscust conscust = conscustService.getConscust(conscustno);
    		prebookinfo.setInsurname(conscust.getCustname());
    		prebookinfo.setInsuridnoDigest(conscust.getIdnoDigest());
    		prebookinfo.setInsuridnoMask(conscust.getIdnoMask());
    		prebookinfo.setInsuridnoCipher(conscust.getIdnoCipher());
        }else{
        	
        	if(StringUtil.isNotNullStr(insuridno)){
        		prebookinfo.setInsuridnoDigest(DigestUtil.digest(insuridno.trim()));
        		prebookinfo.setInsuridnoMask(MaskUtil.maskIdNo(insuridno.trim()));
        		prebookinfo.setInsuridnoCipher(encryptSingleFacade.encrypt(insuridno.trim()).getCodecText());
        	}
        }
    }
    
    private boolean upAgeAndInsurage(String type,HttpServletRequest request,BigDecimal preid,String signdt){
    	boolean upstatus = false;
    	
    	String idtype = null;
        String idno = "";
        Integer age = null;
        String insuridtype = null;
        String insuridno = "";
        Integer insurage = null;
        
    	if("1".equals(type)){
    		signdt = request.getParameter("expectsigndt");
        	idtype = request.getParameter("idtype");
            idno = request.getParameter("idno");
            age = Integer.parseInt(request.getParameter("age"));
            insuridtype = request.getParameter("insuridtype");
            insuridno = request.getParameter("insuridno");
            insurage = Integer.parseInt(request.getParameter("insurage"));
    	}else if("2".equals(type)){
    		Map<String,Object> param = new HashMap<String,Object> (1);
        	param.put("id", preid);
        	CmBxPrebookinfo cmBxPrebookinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(param);
    		if(StringUtil.isNotNullStr(cmBxPrebookinfo.getIdnoCipher())){
        		idno = decryptSingleFacade.decrypt(cmBxPrebookinfo.getIdnoCipher()).getCodecText();
    		}
    		idtype = cmBxPrebookinfo.getIdtype();
    		age = cmBxPrebookinfo.getAge();
    		if(StringUtil.isNotNullStr(cmBxPrebookinfo.getInsuridnoCipher())){
    			insuridno = decryptSingleFacade.decrypt(cmBxPrebookinfo.getInsuridnoCipher()).getCodecText();
    		}
    		insuridtype = cmBxPrebookinfo.getInsuridtype();
    		insurage = cmBxPrebookinfo.getInsurage();
    	}
    	
    	
         if("0".equals(idtype) || "0".equals(insuridtype)){
        	Integer tempAge = null;
         	Integer tempInsurage = null;
         	if("0".equals(idtype)){
         		//根据身份证号取年龄
         		tempAge = getAgeByIdno(idno, signdt);
         	}
         	if("0".equals(insuridtype)){
         		//根据身份证号取年龄
         		tempInsurage = getAgeByIdno(insuridno, signdt);
         	}
         	
         	if( (tempAge != null && !tempAge.equals(age)) || (tempInsurage != null && !tempInsurage.equals(insurage))){
         		CmBxPrebookinfo info = new CmBxPrebookinfo();
                info.setId(preid);
           	    info.setAge(tempAge);
           	    info.setInsurage(tempInsurage);
                cmBxPrebookinfoService.updateCmBxPrebookinfoOnly(info);
                upstatus = true;
            }
         }
         
         	
        return upstatus;
    }
    
    /**
     * 根据身份证号取年龄
     * @param idno
     * @param signdt
     * @return
     */
    private Integer getAgeByIdno(String idno,String signdt){
		String expectsignBirthday = signdt.substring(4, 8);
		Integer expectsignYear = Integer.parseInt(signdt.substring(0, 4));
    	Integer age = null;
    	if(idno.length() == 15){
    		age = expectsignYear - Integer.parseInt("19" + idno.substring(6,8));
 			if(expectsignBirthday.compareTo(idno.substring(8,12)) < 0){
 				age--;
 			}
     	}
        if(idno.length() == 18){
        	age = expectsignYear - Integer.parseInt(idno.substring(6,10));
         	if(expectsignBirthday.compareTo(idno.substring(10,14)) < 0){
         		age--;
 			}
     	}
        return age;
    }
    
    /**
     * 上传预约附件
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/uploadPrebook.do")
    public ModelAndView uploadPrebook(HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
		String id = request.getParameter("id");
        Map<String,Object> map = new HashMap<String,Object>(1);
        map.put("preid", id);
        //通过预约和登录人权限，查询是否有选择上传文件的功能
        List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
        //是否有选择上传文件权限
        String canupload = "";
        boolean canrole = false;
        for (String role : roles) {
            List<String> temp = AuthCache.getInstance().getOperListStr(role, "130201");
            if (temp != null && temp.contains("10")) {
            	canrole = true;
                break;
            }
        }
        if(canrole){
        	Map<String,Object> parapre = new HashMap<String,Object>(1);
            parapre.put("id", id);
            CmBxPrebookinfo preinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(parapre);
            if(preinfo != null && StaticVar.INSUR_STATE_DQD.equals(preinfo.getInsurstate())){
            	canupload = "true";
            }
        }
        map.put("canupload", canupload);
        return new ModelAndView("insur/uploadPrebook", "map", map);
    }
    
    /**
     * 提交上传文件
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/uploadInsurPreAnnex.do", method = RequestMethod.POST)
    public Map<String, Object> uploadInsurPreAnnex(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        User user = getLoginUser();
        String preid = request.getParameter("preid");
        
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> files_jhs = multipartRequest.getFiles("filelist_jhs[]");
        List<MultipartFile> files_sqd = multipartRequest.getFiles("filelist_sqd[]");
        List<MultipartFile> files_hzxp = multipartRequest.getFiles("filelist_hzxp[]");
        List<CmBxPrebookAnnex> listannex = new ArrayList<CmBxPrebookAnnex>();
        try {
        	if (files_jhs != null && files_jhs.size() > 0) {
        		for (int i = 0; i < files_jhs.size(); i++) {
                    MultipartFile file = files_jhs.get(i);
                    String filename = file.getOriginalFilename();
                    String suffix = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
                    CmBxPrebookAnnex annex = new CmBxPrebookAnnex();
                    String id = commonService.getSeqValue("SEQ_INSUR_ID");
                    int filesize = (int) file.getSize();
                    annex.setId(new BigDecimal(id));
                    annex.setPreid(new BigDecimal(preid));
                    annex.setFiletype(StaticVar.INSUR_FILETYPE_JHS);
                    annex.setFilesize(new BigDecimal(filesize));
                    annex.setFilename(filename.substring(0,filename.lastIndexOf(".")));
                    annex.setFilepath(insurPath);
                    annex.setSufname(suffix);
                    annex.setIsdel(StaticVar.INSUR_ISDEL_NO);
                    annex.setCreator(user.getUserId());
                    listannex.add(annex);
                    String servicefilename = id + "." + suffix;
                    try {
//                        FileUtil.uploadFile(file.getInputStream(), path+File.separator+preid+File.separator+StaticVar.INSUR_FILETYPE_JHS+File.separator, servicefilename);
						processInsurFile(annex,file.getBytes());
                    } catch (Exception e) {
                        log.error("saveSignInsurPre:" + servicefilename + ":" + e.getMessage(), e);
                        resultMap.put("uploadFlag", "error");
                        resultMap.put("errorMsg", "上传失败，系统问题");
                        return resultMap;
                    }
                }
            }
        	if (files_sqd != null && files_sqd.size() > 0) {
                for (int i = 0; i < files_sqd.size(); i++) {
                    MultipartFile file = files_sqd.get(i);
                    String filename = file.getOriginalFilename();
                    String suffix = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
                    CmBxPrebookAnnex annex = new CmBxPrebookAnnex();
                    String id = commonService.getSeqValue("SEQ_INSUR_ID");
                    int filesize = (int) file.getSize();
                    annex.setId(new BigDecimal(id));
                    annex.setPreid(new BigDecimal(preid));
                    annex.setFiletype(StaticVar.INSUR_FILETYPE_SQD);
                    annex.setFilesize(new BigDecimal(filesize));
                    annex.setFilename(filename.substring(0,filename.lastIndexOf(".")));
                    annex.setFilepath(insurPath);
                    annex.setSufname(suffix);
                    annex.setIsdel(StaticVar.INSUR_ISDEL_NO);
                    annex.setCreator(user.getUserId());
                    listannex.add(annex);
                    String servicefilename = id + "." + suffix;
                    try {
//                        FileUtil.uploadFile(file.getInputStream(), path+File.separator+preid+File.separator+StaticVar.INSUR_FILETYPE_SQD+File.separator, servicefilename);
						processInsurFile(annex,file.getBytes());
                    } catch (Exception e) {
                        log.error("saveSignInsurPre:" + servicefilename + ":" + e.getMessage(), e);
                        resultMap.put("uploadFlag", "error");
                        resultMap.put("errorMsg", "上传失败，系统问题");
                        return resultMap;
                    }
                }
            }
        	if (files_hzxp != null && files_hzxp.size() > 0) {
                for (int i = 0; i < files_hzxp.size(); i++) {
                    MultipartFile file = files_hzxp.get(i);
                    String filename = file.getOriginalFilename();
                    String suffix = filename.substring(filename.lastIndexOf(".") + 1).toUpperCase();
                    CmBxPrebookAnnex annex = new CmBxPrebookAnnex();
                    String id = commonService.getSeqValue("SEQ_INSUR_ID");
                    int filesize = (int) file.getSize();
                    annex.setId(new BigDecimal(id));
                    annex.setPreid(new BigDecimal(preid));
                    annex.setFiletype(StaticVar.INSUR_FILETYPE_HZXP);
                    annex.setFilesize(new BigDecimal(filesize));
                    annex.setFilename(filename.substring(0,filename.lastIndexOf(".")));
                    annex.setFilepath(insurPath);
                    annex.setSufname(suffix);
                    annex.setIsdel(StaticVar.INSUR_ISDEL_NO);
                    annex.setCreator(user.getUserId());
                    listannex.add(annex);
                    String servicefilename = id + "." + suffix;
                    try {
//                        FileUtil.uploadFile(file.getInputStream(), path+File.separator+preid+File.separator+StaticVar.INSUR_FILETYPE_HZXP+File.separator, servicefilename);
						processInsurFile(annex,file.getBytes());
                    } catch (Exception e) {
                        log.error("saveSignInsurPre:" + servicefilename + ":" + e.getMessage(), e);
                        resultMap.put("uploadFlag", "error");
                        resultMap.put("errorMsg", "签单失败，系统问题");
                        return resultMap;
                    }
                }
            }
        	//批量处理数据
        	cmBxPrebookSigninfoService.batchUploadAnnex(listannex);
            resultMap.put("uploadFlag", "success");
            resultMap.put("errorMsg", "签单成功");
        } catch (Exception ex) {
            ex.printStackTrace();
            resultMap.put("uploadFlag", "error");
            resultMap.put("errorMsg", "上传失败，系统问题");
        }

        return resultMap;
    }

    @RequestMapping("/insurPreDetail.do")
    public ModelAndView insurPreDetail(HttpServletRequest request){
        String id = request.getParameter("id");
        String type = request.getParameter("type");
        Map<String,Object> map = new HashMap<String,Object>(8);
        Map<String,Object> param = new HashMap<String,Object>(1);
        param.put("id", new BigDecimal(id));
        CmBxPrebookinfo preinfo = cmBxPrebookinfoService.getViewCmBxPrebookinfo(param);
        if(preinfo != null){
        	preinfo.setIdno("");
        	if(StringUtil.isNotNullStr(preinfo.getIdnoCipher())){
        		CodecSingleResponse  res = decryptSingleFacade.decrypt(preinfo.getIdnoCipher());
				String idno = res.getCodecText();
				preinfo.setIdno(idno);
        	}
        	preinfo.setInsuridno("");
        	if(StringUtil.isNotNullStr(preinfo.getInsuridnoCipher())){
        		CodecSingleResponse  res = decryptSingleFacade.decrypt(preinfo.getInsuridnoCipher());
				String idno = res.getCodecText();
				preinfo.setInsuridno(idno);
        	}
			//新增所属投顾的投顾名称，和投顾部门
			Map<String, String> cons2OutletMap=ConsOrgCache.getInstance().getCons2OutletMap();
			Map<String, String> allUserMap=ConsOrgCache.getInstance().getAllUserMap();
			preinfo.setPresentorgname(ConsOrgCache.getInstance().getAllOrgMap().get(cons2OutletMap.get(preinfo.getNowconscode())));
			preinfo.setPresentconsname(allUserMap.get(preinfo.getNowconscode()));
        	preinfo.setPrestateval(ConstantCache.getInstance().getVal("insurprestate", preinfo.getPrestate()));
        	preinfo.setInsurstateval(ConstantCache.getInstance().getVal("insurstate", preinfo.getInsurstate()));
        	preinfo.setOrgcode(ConsOrgCache.getInstance().getAllOrgMap().get(preinfo.getOrgcode()));
        	preinfo.setConscode(ConsOrgCache.getInstance().getAllUserMap().get(preinfo.getConscode()));
        	preinfo.setIdtype(ConstantCache.getInstance().getVal("idtype", preinfo.getIdtype()));
        	preinfo.setInsuridtype(ConstantCache.getInstance().getVal("idtype", preinfo.getInsuridtype()));
        	preinfo.setRelation(ConstantCache.getInstance().getVal("insurrelation", preinfo.getRelation()));
        	preinfo.setBusitype(ConstantCache.getInstance().getVal("insurbusitype", preinfo.getBusitype()));
        	preinfo.setBusisourceval(ConstantCache.getInstance().getVal("insurbusisource", preinfo.getBusisource()));
        	preinfo.setCurrency(ConstantCache.getInstance().getVal("currencys", preinfo.getCurrency()));
        }else{
        	preinfo = new CmBxPrebookinfo();
        }
        map.put("info", preinfo);
        //查询签单信息
        Map<String,Object> paramsign = new HashMap<String,Object>(2);
        paramsign.put("preid", new BigDecimal(id));
        paramsign.put("isdel", StaticVar.INSUR_ISDEL_NO);
        CmBxPrebookSigninfo sign =  cmBxPrebookSigninfoService.getCmBxPrebookSigninfo(paramsign);
        if(sign != null){
        	sign.setPaystate(ConstantCache.getInstance().getVal("insurpaystate", sign.getPaystate()));
        }else{
        	sign = new CmBxPrebookSigninfo();
        }
        map.put("sign", sign);
        //购买信息
        Map<String,Object> parambuy = new HashMap<String,Object>(2);
        parambuy.put("preid", new BigDecimal(id));
        parambuy.put("isdel", StaticVar.INSUR_ISDEL_NO);
        List<CmBxPrebookBuyinfo> listbuy = cmBxPrebookBuyinfoService.listCmBxPrebookBuyinfo(parambuy);
        for(CmBxPrebookBuyinfo buy : listbuy){
			buy.setProdtype(ConstantCache.getInstance().getVal("insurprodtype", buy.getProdtype()));
			buy.setPayyears(ConstantCache.getInstance().getVal("insurpayyears", buy.getPayyears()));
			buy.setEnsureyears(ConstantCache.getInstance().getVal("insurensureyears", buy.getEnsureyears()));
			if(StringUtil.isNotNullStr(buy.getExpsalesdt())){
				buy.setSalesdt(buy.getExpsalesdt());
			}
			if(buy.getExpprocoe() != null){
				buy.setProcoe(buy.getExpprocoe());
			}
			if(StringUtil.isNotNullStr(buy.getExpirestat())){
				buy.setExpirestatval(ConstantCache.getInstance().getVal("insurexpirestat", buy.getExpirestat()));
			}
			if("20991231".equals(buy.getExpiredate())){
				buy.setExpiredate("终身");
			}
        }
        map.put("listbuy", listbuy);
        //附件信息
        Map<String,Object> paramattr = new HashMap<String,Object>(2);
        paramattr.put("preid", new BigDecimal(id));
        paramattr.put("isdel", StaticVar.INSUR_ISDEL_NO);
        List<CmBxPrebookAnnex> listannexhas = cmBxPrebookAnnexService.listHasUpFileTypeByPreid(paramattr);
        for(CmBxPrebookAnnex annex : listannexhas){
        	annex.setFiletypeval(ConstantCache.getInstance().getVal("insurfiletype", annex.getFiletype()));
        }
        map.put("listhasannex", listannexhas);
        //查询客户是否是登录人的或者登录人有预约确认的权限的情况，返回到页面可以显示预约确认备注
        String canseeconfirmremark = "";
        Map<String,String> paramcust = new HashMap<String,String>(1);
        paramcust.put("custno", preinfo.getConscustno());
        CmCustconstant cons = cmCustconstantService.getCmCustconstant(paramcust);
        List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
        User userlogin = getLoginUser();
        //本人的客户情况
        if(cons != null && userlogin.getUserId().equals(cons.getConscode())){
        	canseeconfirmremark = "true";
        }
        //有点预约确认权限的人
        for (String role : roles) {
            List<String> temp = AuthCache.getInstance().getOperListStr(role, "130202");
            if (temp != null && temp.contains("4")) {
            	canseeconfirmremark = "true";
                break;
            }
        }
        map.put("canseeconfirmremark", canseeconfirmremark);
        //记录访问日志
        PageVisitLog pageVisitLog = new PageVisitLog();
        String userId= (String)request.getSession().getAttribute("userId");
        String ip = getIpAddr(request);
        pageVisitLog.setConscustno(preinfo.getConscustno());
        pageVisitLog.setUserId(userId);
        pageVisitLog.setVisitUrl(request.getRequestURI());
        pageVisitLog.setPreid(preinfo.getId());
        pageVisitLog.setVisitTime(new Date());
        pageVisitLog.setIp(ip);
        if("check".equals(type)){
        	pageVisitLog.setOperation("核保管理详情访问");
			// 查询修改信息
			List<CmBxEditLog> cmBxEditLogs = cmBxEditLogService.listCmBxEditLog(new BigDecimal(id));
			// 修改的信息有几种 合作渠道、 缴费年限 、 保障期限、 年缴保费、核保通过日期、 保费缴清日、冷静期截止日
			List<CmBxEditLog> cmBxEditLogList = cmBxEditLogs.stream().collect(Collectors.toMap(CmBxEditLog::getPreid, a -> a, (o1, o2) -> {
				if (o1.getCaltime() != null || o2.getCaltime() != null) {
					o1.setCaltime(new BigDecimal(1));
				}
				if (o1.getPaydt() != null || o2.getPaydt() != null) {
					o1.setPaydt(new BigDecimal(1));
				}
				if (o1.getPayamt() != null || o2.getPayamt() != null) {
					o1.setPayamt(new BigDecimal(1));
				}
				if (o1.getEnsureyears() != null || o2.getEnsureyears() != null) {
					o1.setEnsureyears(new BigDecimal(1));
				}
				if (o1.getPassdt() != null || o2.getPassdt() != null) {
					o1.setPassdt(new BigDecimal(1));
				}
				if (o1.getChannel() != null || o2.getChannel() != null) {
					o1.setChannel(new BigDecimal(1));
				}
				if (o1.getPayyears() != null || o2.getPayyears() != null) {
					o1.setPayyears(new BigDecimal(1));
				}
				return o1;
			})).values().stream().collect(Collectors.toList());
			if (cmBxEditLogList.size() != 0) {
				map.put("cmBxEditLogs", cmBxEditLogList.get(0));
			}else {
				CmBxEditLog cmBxEditLog = new CmBxEditLog();
				cmBxEditLog.setCaltime(new BigDecimal(2));
				cmBxEditLog.setPaydt(new BigDecimal(2));
				cmBxEditLog.setPassdt(new BigDecimal(2));
				cmBxEditLog.setPayamt(new BigDecimal(2));
				cmBxEditLog.setChannel(new BigDecimal(2));
				cmBxEditLog.setEnsureyears(new BigDecimal(2));
				cmBxEditLog.setPayyears(new BigDecimal(2));
				map.put("cmBxEditLogs", cmBxEditLog);
			}
			// 修改的信息有几种 合作渠道、 缴费年限 、 保障期限、 年缴保费、核保通过日期、 保费缴清日、冷静期截止日
        	pageVisitLogService.recordLog(pageVisitLog);
        	return new ModelAndView("insur/insurCheckPreDetail","map",map);
        }else if("sale".equals(type)){
        	pageVisitLog.setOperation("销售业绩详情访问");
        	pageVisitLogService.recordLog(pageVisitLog);
            return new ModelAndView("insur/salePerformanceDetail","map",map);
        }else if("commission".equals(type)){
        	pageVisitLog.setOperation("佣金结算详情访问");
        	pageVisitLogService.recordLog(pageVisitLog);
            return new ModelAndView("insur/commissionDetail","map",map);
        }else{
        	pageVisitLog.setOperation("预约投保详情访问");
        	pageVisitLogService.recordLog(pageVisitLog);
        	return new ModelAndView("insur/insurPreDetail","map",map);
        }
    }
    
    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    @RequestMapping("/showAnnexDetail.do")
    public ModelAndView showAnnexDetail(HttpServletRequest request){
        String id = request.getParameter("id");
        String type = request.getParameter("type");
        String filetype = request.getParameter("filetype");
        
        Map<String,Object> map = new HashMap<String,Object>(2);
      
        //附件信息
        Map<String,Object> paramattr = new HashMap<String,Object>(3);
        paramattr.put("preid", new BigDecimal(id));
        paramattr.put("filetype", filetype);
        paramattr.put("isdel", StaticVar.INSUR_ISDEL_NO);
        List<CmBxPrebookAnnex> listannexhas = cmBxPrebookAnnexService.listCmBxPrebookAnnex(paramattr);
        for(CmBxPrebookAnnex annex : listannexhas){
        	annex.setCreator(ConsOrgCache.getInstance().getAllUserMap().get(annex.getCreator()));
        }
        map.put("listhasannex", listannexhas);
        //通过预约和登录人权限，查询是否有选择上传文件的功能
        List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
        String candel="";
    	String candown="";
    	//公司佣金详情
    	if("commission".equals(type)){
    		candel = getStrFlag(roles,"130204","9");
    		candown = getStrFlag(roles,"130204","10");
        //销售业绩详情
        }else if("sale".equals(type)){
        	candel = getStrFlag(roles,"130203","7");
        	candown = getStrFlag(roles,"130203","8");
        }else if("check".equals(type)){
        	//预约管理的详情
        	candel = getStrFlag(roles,"130202","12");
        	candown = getStrFlag(roles,"130202","13");
        }else if("annexmanage".equals(type)){
        	//附件管理的查看
        	if("true".equals(getStrFlag(roles,"130201","11"))){
            	Map<String,Object> parapre = new HashMap<String,Object>(1);
                parapre.put("id", id);
                CmBxPrebookinfo preinfo = cmBxPrebookinfoService.getCmBxPrebookinfo(parapre);
                if(preinfo != null && StaticVar.INSUR_STATE_DQD.equals(preinfo.getInsurstate())){
                	candel = "true";
                }
            }
            candown = getStrFlag(roles,"130201","12");
        }else if("queryannex".equals(type)){
        	candown = "true";
        }else{
        	//预约投保的详情
        	candel = getStrFlag(roles,"130201","8");
        	candown = getStrFlag(roles,"130201","9");
        }
        map.put("candel", candel);
        map.put("candown", candown);
        return new ModelAndView("insur/viewannex","map",map);
    }
    
    private String getStrFlag(List<String> roles,String menucode,String optcode){
    	String flag = "";
    	for (String role : roles) {
            List<String> temp = AuthCache.getInstance().getOperListStr(role, menucode);
            if (temp != null && temp.contains(optcode)) {
            	flag = "true";
                break;
            }
        }
    	return flag;
    }
    
    @RequestMapping("/delannex.do")
    @ResponseBody
    public String delannex(HttpServletRequest request){
    	String result="";
        String id = request.getParameter("id");
        User userlogin = getLoginUser();
        //查询附件
        Map<String,Object> paraannex = new HashMap<String,Object>(1);
        paraannex.put("id", id);
        CmBxPrebookAnnex annex = cmBxPrebookAnnexService.getCmBxPrebookAnnex(paraannex);
        if(annex == null){
            return "paramError";
        }
        annex.setIsdel(StaticVar.INSUR_ISDEL_YES);
        annex.setModifier(userlogin.getUserId());
        annex.setModifydt(new Date());
        cmBxPrebookAnnexService.updateCmBxPrebookAnnex(annex);
		result = "success";
        return result;
    }
    
    @RequestMapping("/downannex.do")
	public String downannex( HttpServletRequest request,
			HttpServletResponse response) {
    	String id = request.getParameter("id");
    	 //查询附件
        Map<String,Object> paraannex = new HashMap<String,Object>(1);
        paraannex.put("id", id);
        CmBxPrebookAnnex annex = cmBxPrebookAnnexService.getCmBxPrebookAnnex(paraannex);
        if(annex != null){
			//迁移defile  保险相关业务   2024年6月19日  /data/files/insur
        	String filename = annex.getFilename()+"."+annex.getSufname();
			HFileService instance = HFileService.getInstance();
			String relativePath = getRelativePath(annex);
			String storeFileName =getStoreFileName(annex);
			try {
				byte[]  fileBytes=
						instance.read2Bytes(INSUR_FILE_PATH_CONFIG,relativePath,storeFileName);
				response.setContentType("multipart/form-data");
				response.setHeader("Content-Disposition", "attachment;fileName="+ new String( filename.getBytes("gb2312"), "ISO8859-1" ) );
				InputStream inputStream = new ByteArrayInputStream(fileBytes);;
				OutputStream os = response.getOutputStream();
				byte[] b = new byte[2048];
				int length;
				while ((length = inputStream.read(b)) > 0) {
					os.write(b, 0, length);
				}
				os.close();
				inputStream.close();
			} catch (Exception e) {
				log.error(String.format("读取文件失败,路径：%s, 文件存储名:%s,文件名：%s",
						relativePath,storeFileName,filename),e);
			}
        }
		return null;
	}
    
    /**
     * 导出操作
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportInsurPreInfo.do")
    public void exportInsurPreInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
    	Map<String, String> param = new ParamUtil(request).getParamMap();
    	List<CmBxPrebookinfo> exportList = cmBxPrebookinfoService.listCmBxPrebookinfoByExp(param);
    	List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
    	User user = getLoginUser();
    	String ip = getIpAddr(request);
    	for (CmBxPrebookinfo info : exportList) {
			info.setPrestateval(ConstantCache.getInstance().getVal("insurprestate", info.getPrestate()));
			info.setInsurstateval(ConstantCache.getInstance().getVal("insurstate", info.getInsurstate()));

            ConsOrgCache orgcache = ConsOrgCache.getInstance();
            String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
            if("0".equals(uporgcode)){
                info.setUporgname(orgcache.getAllOrgMap().get(info.getOrgcode()));
            }else{
                info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
            }

			info.setOrgcode(ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
			info.setConscode(ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
			info.setRelation(ConstantCache.getInstance().getVal("insurrelation", info.getRelation()));
			info.setBusitype(ConstantCache.getInstance().getVal("insurbusitype", info.getBusitype()));
			info.setProdtype(ConstantCache.getInstance().getVal("insurprodtype", info.getProdtype()));
			info.setPaystate(ConstantCache.getInstance().getVal("insurpaystate", info.getPaystate()));
			info.setVisitstate(ConstantCache.getInstance().getVal("insurvisitstate", info.getVisitstate()));
			info.setPayyears(ConstantCache.getInstance().getVal("insurpayyears", info.getPayyears()));
			info.setBusisourceval(ConstantCache.getInstance().getVal("insurbusisource", info.getBusisource()));
			info.setEnsureyears(ConstantCache.getInstance().getVal("insurensureyears", info.getEnsureyears()));
			info.setCurrency(ConstantCache.getInstance().getVal("currencys", info.getCurrency()));
			if(StringUtil.isNotNullStr(info.getIdnoCipher())){
				CodecSingleResponse  res = decryptSingleFacade.decrypt(info.getIdnoCipher());
				String idno = res.getCodecText();
				info.setIdno(idno);
			}else{
				info.setIdno("");
			}
			if (null != info.getRate() && null != info.getYearamk() && StringUtils.isNotEmpty(info.getPayyears())) {
				BigDecimal years = BigDecimal.ONE;
				boolean includeCn = payYearsIncludeCn(info.getPayyears());
				if(includeCn){
					years = new BigDecimal(transferPayYears(info.getPayyears())).subtract(new BigDecimal(info.getAge()));
				}else{
					years = new BigDecimal(info.getPayyears());
				}
				//总保费保持和页面展示的一样  四舍五入保留两位
				info.setTotalPremium(info.getRate().multiply(info.getYearamk())
						.multiply(years).setScale(2, RoundingMode.HALF_UP)
						.toPlainString());

				info.setNewTotalPremium(info.getYearamk()
						.multiply(years).setScale(2, RoundingMode.HALF_UP)
						.toPlainString());
			}
			//参考前端JS逻辑,这里重复给Busitype 赋值
			if(StringUtils.isNotBlank(info.getBusitype()) && !"大陆".equals(info.getBusitype().trim())){
				info.setIsInvestment("是");
			}else{
				info.setIsInvestment("否");
			}
			// 考虑为空的情况
			if (StringUtils.isBlank(info.getSfsyxbx())){
				info.setSfsyxbx(null);
			}else if ("1".equals(info.getSfsyxbx())) {
				info.setSfsyxbx("收益型保险");
			}else{
				info.setSfsyxbx("非收益型保险");
			}
			//导出日志
            PageVisitLog pageVisitLog = new PageVisitLog();
            pageVisitLog.setConscustno(info.getConscustno());
            pageVisitLog.setUserId(user.getUserId());
            pageVisitLog.setVisitUrl(request.getRequestURI());
            pageVisitLog.setOperation("预约投保导出");
            pageVisitLog.setVisitTime(new Date());
            pageVisitLog.setPreid(info.getId());
            pageVisitLog.setIp(ip);
            listlog.add(pageVisitLog);
    	}
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("预约投保信息导出.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String [] columnName = new String [] {"录入时间","签单日期","预约状态","保单状态","区域(预约时)","部门(预约时)","投顾(预约时)","投保人","证件号","受保人","投保人与受保人关系","其他关系","业务类型","保险公司","产品类型","合作渠道","产品名称","缴费年限","年缴保费","币种","保障年限","业务来源","首年缴费状态","回访状态","首年保费缴清日","保单号","核保通过日期","冷静期截止日","次年保费日","退保/拒保日期","汇率","总保费","总保费（RMB）","一级策略","是否投资海外","投顾备注","保单备注"};

            String [] beanProperty = new String [] {"credt","signdt","prestateval","insurstateval","uporgname","orgcode","conscode","custname","idnoMask","insurname","relation","otherrelation","busitype","compname","prodtype","channname","fundname","payyears","yearamk","currency","ensureyears","busisourceval","paystate","visitstate","paydt","insurid","passdt","caltime","nextyearpaydt","cancelsurdt","rate","newTotalPremium","totalPremium","sfsyxbx","isInvestment","consremark","insurremark"};


            ExcelWriter.writeExcel(os, "投保信息", 0, exportList, columnName, beanProperty);
            os.close(); // 关闭流
            for(PageVisitLog log : listlog){
            	pageVisitLogService.recordLog(log);
            }
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }
    	
    }
	/**
	 * @description 转换查询出来的payyears  表中存在中文数据 （eg. 至70岁）
	 * @param payYearsInDB
	 * @return
	 * <AUTHOR>
	 * @date 2023/9/5 下午5:20
	 * @since JDK 1.8
	 */
	private String transferPayYears(String payYearsInDB) {
		if (payYearsInDB.startsWith("至") && payYearsInDB.endsWith("岁")) {
			return payYearsInDB.substring(payYearsInDB.indexOf("至")+1, payYearsInDB.indexOf("岁"));
		}
		return payYearsInDB;
	}

	/**
	 * 是否包含特殊汉字，至和岁
	 * @param payYearsInDB
	 * @return
	 */
	private boolean payYearsIncludeCn(String payYearsInDB) {
		if(StringUtils.isBlank(payYearsInDB)){
			return false;
		}
        return payYearsInDB.startsWith("至") && payYearsInDB.endsWith("岁");
    }


	/**
	 * @description:修改 销售业绩(续佣) 中的投顾（算绩效）
	 * @param request
	 * @return java.lang.String
	 * @author: xfc
	 * @date: 2023/3/29 18:37
	 * @since JDK 1.8
	 */
	@ResponseBody
	@PostMapping("/updateprebookendpay")
	public String updateprebookendpay(HttpServletRequest request) {
		String result = "";
		User userlogin = getLoginUser();
		// 修改投顾
		String conscode = request.getParameter("conscode");
		String endPayId = request.getParameter("endPayId");
		String curCusNo = request.getParameter("curCusNo");
		if (StringUtils.isNotBlank(conscode) && StringUtils.isNotBlank(endPayId)) {
			// 获取预约单缴款计划明细表  更新投顾(算绩效)的值
			Map<String, Object> params = new HashMap<>(1);
			params.put("id", endPayId);
			CmBxPrebookEndpayList cmBxPrebookEndpayList = cmBxPrebookEndpayListService.getCmBxPrebookEndpayList(params);
			cmBxPrebookEndpayList.setCalconscode(conscode);
			cmBxPrebookEndpayList.setModifier(userlogin.getUserId());
			cmBxPrebookEndpayList.setModifydt(new Date());
			//更新管理系数
			cmBxPrebookEndpayList.setManagePoint(Objects.equals(getCurConsCode(curCusNo), conscode) ? getManagePointByCusNo(curCusNo) : "");
			cmBxPrebookEndpayListService.updateCmBxPrebookEndpayList(cmBxPrebookEndpayList);
			result = "success";
		} else {
			result = "paramError";
		}
		return result;
	}

	/**
	 * 获取当前投顾
	 *
	 * @return
	 */
	private String getCurConsCode(String custNo) {
		Map<String, String> paramMap = new HashMap<>();
		paramMap.put("custno", custNo);
		CmCustconstant cmCustconstant = cmCustconstantService.getCmCustconstant(paramMap);
		String curConsCode = "";
		if (cmCustconstant != null) {
			curConsCode = cmCustconstant.getConscode();
		}
		return curConsCode;
	}


	/**
	 * 修改客户投顾
	 *
	 * @param request
	 * @return String
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/updatePrebookCreator.do")
	public String updatePrebookCreator(HttpServletRequest request) throws Exception{
		String result = "";
		User userlogin = getLoginUser();
		String conscode = request.getParameter("conscode");
		String preid = request.getParameter("preid");
		if (StringUtils.isNotBlank(conscode) && StringUtils.isNotBlank(preid)) {
			CmBxPrebookinfo info = new CmBxPrebookinfo();
			info.setId(new BigDecimal(preid));
			info.setCreator(conscode);
			info.setModifier(userlogin.getUserId());
			info.setModifydt(new Date());
			cmBxPrebookinfoService.updateCmBxPrebookinfoOnly(info);
			result = "success";
		} else {
			result = "paramError";
		}
		return result;
	}

	/**
	 * 修改客户回访状态
	 *
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/updateVisitState.do")
	public String updateVisitState(HttpServletRequest request) {
		String result = "";
		User userlogin = getLoginUser();
		String visitstate = request.getParameter("visitstate");
		String preid = request.getParameter("preid");
		if (StringUtils.isNotBlank(visitstate) && StringUtils.isNotBlank(preid)) {
			CmBxPrebookSigninfo info = new CmBxPrebookSigninfo();
			info.setPreid(new BigDecimal(preid));
			info.setVisitstate(visitstate);
			info.setModifier(userlogin.getUserId());
			info.setModifydt(new Date());
			cmBxPrebookSigninfoService.updateCmBxPrebookSigninfo(info);
			result = "success";
		} else {
			result = "paramError";
		}
		return result;
	}
	
	@RequestMapping("/showPayApply.do")
	public ModelAndView showPayApply(HttpServletRequest request){
		String buyid = request.getParameter("buyid");
		Map<String,Object> map = new HashMap<String,Object>(1);
		//查询缴款计划
		Map<String,Object> paramattr = new HashMap<String,Object>(2);
		paramattr.put("buyid", new BigDecimal(buyid));
		paramattr.put("isdel", StaticVar.INSUR_ISDEL_NO);
		List<CmBxPrebookEndpayList> list = cmBxPrebookEndpayListService.listCmBxPrebookEndpayList(paramattr);
		for(CmBxPrebookEndpayList payapply : list) {
			if (StringUtil.isNotNullStr(payapply.getPaystatedetail()) && StaticVar.INSUR_PAYSTAT_NOTPAY.equals(payapply.getPaystate())) {
				payapply.setPaystateval(ConstantCache.getInstance().getVal("insurpaystateall", payapply.getPaystatedetail()));
			} else {
				payapply.setPaystateval(ConstantCache.getInstance().getVal("insurpaystateall", payapply.getPaystate()));
			}
		}
		map.put("listpayapply", list);

		return new ModelAndView("insur/viewpayapply", "map", map);
	}


	/**
	 * @param custNo 客户号
	 * @return
	 * @description 根据客户号获取对应管理系数
	 * @author: chao.chen01
	 * @date: 2023/9/5 下午4:38
	 * @since JDK 1.8
	 */
	public String getManagePointByCusNo(String custNo) {
		String sourceType = preBookService.getCustSourceType(custNo, DateTimeUtil.getCurDate());
		if (StringUtils.isNotEmpty(sourceType)) {
			return cmCustPrpSourceCoeffService.getManageCoeffBySourceType(sourceType);
		}
		return "";
	}

}