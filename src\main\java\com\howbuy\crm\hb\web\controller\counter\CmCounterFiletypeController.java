package com.howbuy.crm.hb.web.controller.counter;

import com.google.common.collect.Maps;
import com.howbuy.crm.hb.constants.CrmTradeServerPathConstant;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.trade.common.response.BaseResponse;
import com.howbuy.crm.trade.common.response.PageResult;
import com.howbuy.crm.trade.model.counter.dto.CmCounterFiletypeDto;
import com.howbuy.crm.trade.model.counter.dto.CounterFilemodelStream;
import com.howbuy.crm.trade.model.counter.request.SaveCmCounterFiletypeRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @Description: Controller
 * @version 1.0
 */
@Slf4j
@Controller
@RequestMapping(value = "/counter")
public class CmCounterFiletypeController  extends BaseCounterController {

	@RequestMapping("/listCounterFiletype.do")
	public ModelAndView listCmCounterOrderFile(HttpServletRequest request) {
		Map<String,String> map= Maps.newHashMap();
		map.put("userId",getLoginUserId());
        return new ModelAndView("/counter/listCounterFiletype",map);
	}
	
	
	/**
     * 分页查询文件类型列表
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listCmCounterFiletypeByPage")
    public PageResult<CmCounterFiletypeDto> listCmCounterFiletypeByPage(HttpServletRequest request) throws Exception {
    	PageResult<CmCounterFiletypeDto> pageResult = new PageResult<CmCounterFiletypeDto> ();
    	String fileTypeName = request.getParameter("fileTypeName");
    	String page = request.getParameter("page");
    	String rows = request.getParameter("rows");
    	// 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("fileTypeName", fileTypeName);
    	postParam.put("page", StringUtils.isBlank(page) ? "1" : page );
    	postParam.put("rows", StringUtils.isBlank(rows) ? "10" : rows);
		BaseResponse<PageResult<CmCounterFiletypeDto>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_CM_COUNTER_FILE_TYPE_BY_PAGE, postParam,new ParameterizedTypeReference<BaseResponse<PageResult<CmCounterFiletypeDto>>>(){});
		if (httpRsp.isSuccess()) {
			pageResult = httpRsp.getData();
		}
        return pageResult;
    }


	@RequestMapping(value = "/previewModelImg")
	public String previewCounterImg(HttpServletRequest request) {
		String docId = request.getParameter("docId");
		if (StringUtils.isNotBlank(docId)) {
			request.setAttribute("docid", docId);
			try {
				//插入日志
//                this.creatLog(user.getUserId(), docfile.getFid(), "'" + docfile.getName() + "." + docfile.getSufname() + "'", StaticVar.OPT_TYPE_FILE_VIEW);

			} catch (Exception e) {
				log.error("previewCounterImg error", e);
			}
		}

		return "/counter/previewModelImg";
	}

	@ResponseBody
	@RequestMapping("/getModelFileStream")
	public void getModelFileStream(HttpServletRequest request, HttpServletResponse response) {
		String docId = request.getParameter("docId");
		String userId=getLoginUserId();
		Map<String, String> param = new HashMap<String, String>();
		param.put("id", docId);
		CounterFilemodelStream fileStreamInfo = null;
		BaseResponse<CounterFilemodelStream> httpRsp =
				getPostEntityByMap(CrmTradeServerPathConstant.GET_CM_COUNTER_MODEL_FILE_STREAM,
						param,
						new ParameterizedTypeReference<BaseResponse<CounterFilemodelStream>>() {
						});
		if (httpRsp.isSuccess()) {
			fileStreamInfo = httpRsp.getData();
		}else{
			fileStreamInfo=new CounterFilemodelStream();
		}

		byte[] bytes= fileStreamInfo.getFilebyte();
		ServletOutputStream outputStream = null;
		try {
			response.setContentType("application/octet-stream");
			//输出文件流
			outputStream = response.getOutputStream();
			outputStream.write(bytes);
		}catch (Exception e){
			log.error("文档预览失败，fileName：{}, useId：{}", fileStreamInfo.getModelName(), userId, e);
		}finally {
			if (null != outputStream) {
				try {
					outputStream.close();
				} catch (IOException e) {
					log.error("CrmCounterFileOuterService>>>downloadPdf 关闭文件流异常", e);
				}
			}
		}

	}

    
    @ResponseBody
    @RequestMapping(value = "/uploadfileByCountFileType", method = RequestMethod.POST)
    public Map<String, Object> uploadfileByCountFileType(HttpServletRequest request) {
    	Map<String, Object> resultMap = new HashMap<String, Object>();
	
    	User user = (User) request.getSession().getAttribute("loginUser");

		SaveCmCounterFiletypeRequest orderRequest= new SaveCmCounterFiletypeRequest();

    	//删除更新过程中去除的countFileModel
    	String delfileids = request.getParameter("delfileids").replaceFirst("#", "");
		List<String> deleteList=Arrays.asList(delfileids.split("#"));

    	//上传文件
    	MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        List<MultipartFile> fileList = multipartRequest.getFiles("filelist[]");
        
        // 设置参数
		orderRequest.setFileTypeName(request.getParameter("fileTypeName"));
		orderRequest.setOperatorNo(user.getUserId());
		orderRequest.setId(request.getParameter("id"));
		orderRequest.setDelFilemodelIdList(deleteList);

		orderRequest.setFilePathList(processCounterTempFile(fileList));

        BaseResponse<String> httpRsp = getPostEntityByJsonObject(CrmTradeServerPathConstant.SANE_COUNTER_FILE_TYPE,
				orderRequest,
				new ParameterizedTypeReference<BaseResponse<String>>(){});
		resultMap.put("uploadFlag", httpRsp.isSuccess());
        resultMap.put("errorMsg", httpRsp.getReturnMsg());
    	
        return resultMap;
    }

    /**
     * 查询文件类型列表
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/queryCounterFiletypeList")
    public Map<String, Object> queryCounterFiletypeList(HttpServletRequest request) throws Exception {
    	Map<String, Object> resultMap = new HashMap<String, Object>();
    	String id = request.getParameter("id");
    	// 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("id", id);	
    	List<CmCounterFiletypeDto> list = null;
		BaseResponse<List<CmCounterFiletypeDto>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_FILE_TYPE_DTO_LIST, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterFiletypeDto>>>(){});
		if (httpRsp.isSuccess()) {
			list = httpRsp.getData();
		}
        
        resultMap.put("rows", list);
        return resultMap;
    }
    
    
    //启用或者禁用操作
    @RequestMapping(value = "/delCounterFiletype", method = RequestMethod.POST)
    public @ResponseBody
    Map<String, Object> delCounterFiletype(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String id = request.getParameter("id");
        if (StringUtils.isEmpty(id)) {
        	resultMap.put("errorMsg", "禁用或者启用失败");
        	return resultMap;
        }
        User user = (User) request.getSession().getAttribute("loginUser");
        String stat = request.getParameter("stat");
        
        // 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("id", id);
    	postParam.put("stat", stat);
    	postParam.put("operatorNo", user.getUserId());
		BaseResponse<String> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.DEL_COUNTER_FILE_TYPE, postParam,new ParameterizedTypeReference<BaseResponse<String>>(){});
		resultMap.put("returnMsg", httpRsp.getReturnMsg());

        return resultMap;
    }
	
}