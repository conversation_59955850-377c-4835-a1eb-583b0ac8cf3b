package com.howbuy.crm.hb.web.controller.hbone;

import com.alibaba.fastjson.JSON;
import com.howbuy.acc.common.enums.OuterSysTypeEnum;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoFacade;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoRequest;
import com.howbuy.acccenter.facade.query.queryhboneinfo.QueryAccHboneInfoResponse;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.request.QueryConscustListRequest;
import com.howbuy.crm.conscust.request.UpdateConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.response.QueryConscustListResponse;
import com.howbuy.crm.conscust.service.ConscustAcctInfoService;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.conscust.service.QueryConscustListService;
import com.howbuy.crm.conscust.service.UpdateConscustInfoService;
import com.howbuy.crm.hb.domain.hbone.HbOneInfo;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.util.ParamUtil;
import com.howbuy.crm.util.RMIConstant;
import crm.howbuy.base.db.PageData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Description: Controller*/


@Slf4j
@Controller
@RequestMapping(value = "/hbone")
public class CustRelationHbOneController {

	@Autowired
    private QueryAccHboneInfoFacade queryAccHboneInfoFacade;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    @Autowired
    private UpdateConscustInfoService updateConscustInfoService;

    @Autowired
    private QueryConscustListService queryConscustListService;

    @Autowired
    private ConscustAcctInfoService conscustAcctInfoService;

    @RequestMapping(value = "/custRelationHbOneIndex")
    public String custRelationIndex() {
        return "hbone/custRelationHbOne";
    }

    @ResponseBody
    @RequestMapping("/listhboneCustByPage")
    public Map<String, Object> listPubCustByPage(HttpServletRequest request) throws Exception {
        Map<String, String> param = new ParamUtil(request).getParamMap();
        PageData<HbOneInfo> HbOneInfoData = new PageData<>();
        QueryAccHboneInfoRequest queryHboneInfo = new QueryAccHboneInfoRequest();
        queryHboneInfo.setHboneNo(param.get("hboneNo"));
        queryHboneInfo.setMobileDigest(DigestUtil.digest(param.get("mobile")));
        queryHboneInfo.setIdType(param.get("idType"));
        queryHboneInfo.setIdNoDigest(DigestUtil.digest(param.get("idNo")));
        queryHboneInfo.setUserType(param.get("userType"));
        QueryAccHboneInfoResponse baseHboneInfo = queryAccHboneInfoFacade.execute(queryHboneInfo);
        List<HbOneInfo> hbOnelist = new ArrayList<>();
        if (RMIConstant.RMISuccNew.equals(baseHboneInfo.getReturnCode())) {
            HbOneInfo hbOne = new HbOneInfo();
            hbOne.setBindZcVip("否");
            String hboneNo = baseHboneInfo.getHboneNo();

            // 调用账户中心接口判断微信绑定臻财或APP
            log.info("getUnionIdByHboneNo request：{}", hboneNo);
            String unionId = conscustAcctInfoService.getUnionIdByHboneNo(hboneNo);
            log.info("getUnionIdByHboneNo response：{}", unionId);
            if (StringUtils.isNotEmpty(unionId)) {
                hbOne.setBindZcVip("是");
            }
            hbOne.setExistPassword(baseHboneInfo.isExistPassword() ? "有" : "无");
            hbOne.setHboneNo(hboneNo);
            hbOne.setCustName(baseHboneInfo.getCustName());
            hbOne.setMobileVerifyStatus("1".equals(baseHboneInfo.getMobileVerifyStatus()) ? "已验证" : "未验证");
            hbOne.setMobile(baseHboneInfo.getMobileMask());
            hbOne.setIdNo(baseHboneInfo.getIdNoMask());
            hbOnelist.add(hbOne);
        }
        HbOneInfoData.setListData(hbOnelist);
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", HbOneInfoData.getListData().size());
        resultMap.put("rows", HbOneInfoData.getListData());

        return resultMap;
    }

/**
     * 功能描述: <br>
     * <取消关联>
     * @Param: [request]
     * @Return: java.util.Map<java.lang.String,java.lang.Object>
     * @Author: pei.luo
     * @Date: 2020/8/25 9:06*/


    @ResponseBody
    @RequestMapping("/doCancelHbOneRelation")
    public Map<String, Object> doCancelHbOneRelation(HttpServletRequest request) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();

        try{
            HttpSession loginSession = request.getSession();
            User userlogin = (User) request.getSession().getAttribute("loginUser");

            String userId = userlogin.getUserId();

            Map<String, String> param = new HashMap<String, String>();
            param = new ParamUtil(request).getParamMap();

            QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
            queryRequest.setConscustno(param.get("conscustno"));
            QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
            ConscustInfoDomain conscust = queryResponse.getConscustinfo();

            UpdateConscustInfoRequest updateRequest = new UpdateConscustInfoRequest();
            conscust.setCreator(userId);
            conscust.setHboneno("");
            updateRequest.setConscustinfo(conscust);
            updateConscustInfoService.updateConsCustInfo(updateRequest);

            resultMap.put("returnFlag", "success");

        }catch(Exception ex){
            ex.printStackTrace();
            resultMap.put("returnFlag", "error");
            resultMap.put("errorMsg", ex.getMessage());
        }

        return resultMap;
    }

/**
     * 功能描述: <br>
     * <关联投顾客户一账通用户>
     * @Param: [request]
     * @Return: java.util.Map<java.lang.String,java.lang.Object>
     * @Author: pei.luo
     * @Date: 2020/8/25 9:05*/


    @ResponseBody
    @RequestMapping("/doCustRelationHbOne")
    public  Map<String, Object> doCustRelation(HttpServletRequest request) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();

        String resultMsg = "";

        try{
            HttpSession loginSession = request.getSession();
            User loginUser = (User) request.getSession().getAttribute("loginUser");
            String userId = loginUser.getUserId();

            Map<String, String> param = new HashMap<String, String>();
            param = new ParamUtil(request).getParamMap();

            String consCustNo = param.get("consCustNo") == null ? null : param.get("consCustNo").toString();
            String hboneNo = param.get("hboneNo") == null ? null : param.get("hboneNo").toString();

            if(hboneNo != null){
                QueryConscustListRequest requestParam = new QueryConscustListRequest();
                requestParam.setHboneno(hboneNo);
                QueryConscustListResponse responseData = queryConscustListService.queryConscustInfo(requestParam);
                List<ConscustInfoDomain> conscustlist = responseData.getConscustlist();

                if(conscustlist != null && conscustlist.size() > 0){
                    resultMsg += "存在对应的一账通账号关联 ";
                }else{
                    //修改一账通和投顾客户关系表
                    QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
                    queryRequest.setConscustno(consCustNo);
                    QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
                    ConscustInfoDomain conscust = queryResponse.getConscustinfo();

                    UpdateConscustInfoRequest updateRequest = new UpdateConscustInfoRequest();
                    conscust.setCreator(userId);
                    conscust.setHboneno(hboneNo);
                    updateRequest.setConscustinfo(conscust);
                    updateConscustInfoService.updateConsCustInfo(updateRequest);

                    resultMsg += "一账通客户已经关联  ";
                }
            }

            resultMap.put("returnFlag", "success");
            resultMap.put("resultMsg", resultMsg);

        }catch(Exception ex){
            ex.printStackTrace();
            resultMap.put("returnFlag", "error");
            resultMap.put("errorMsg", ex.getMessage());
        }

        return resultMap;
    }
}
