package com.howbuy.crm.hb.web.controller.conference;


import com.howbuy.crm.hb.domain.conference.CmConferenceConscust;
import com.howbuy.crm.hb.domain.conference.CmConferenceCustInfomation;
import com.howbuy.crm.hb.domain.conference.CmConferenceInfomation;
import com.howbuy.crm.hb.service.conference.CmConferenceConscustService;
import com.howbuy.crm.hb.service.conference.CmConferenceCustInfomationService;
import com.howbuy.crm.page.framework.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.*;

@Slf4j
@Controller
@RequestMapping(value = "/conferencecustinfomation")
public class CmConferenceCustInfomationController {

    @Autowired
    private CmConferenceCustInfomationService cmConferenceCustInfomationService;

    @Autowired
    private CmConferenceConscustService cmConferenceConscustService;

    @RequestMapping("/queryConferenceCustInfomation.do")
    public ModelAndView queryConferenceCustInfomation(HttpServletRequest request){
        String conferenceid = request.getParameter("conferenceid");
        String conscustno = request.getParameter("conscustno");

        List<CmConferenceCustInfomation> listcustinfo = cmConferenceCustInfomationService.listCmConferenceCustInfomation(conferenceid,conscustno);

        for(int i=0;i<listcustinfo.size();i++){
            CmConferenceCustInfomation cmConferenceCustInfomation = listcustinfo.get(i);
            String value = cmConferenceCustInfomation.getFieldvalue();
            if(StringUtils.isNotBlank(value)){
                String[] values = value.split(";");
                List<String> valueslist = new ArrayList<String>();
                for(int k=0;k<values.length;k++){
                    valueslist.add(values[k]);
                }
                cmConferenceCustInfomation.setFieldvalueList(valueslist);
            }
        }
        Map<String,List<CmConferenceCustInfomation>> map = new HashMap<String,List<CmConferenceCustInfomation>>();
        map.put("listtemp", listcustinfo);
        request.setAttribute("conferenceid", conferenceid);
        request.setAttribute("conscustno", conscustno);

        return new ModelAndView("/conference/custinfomationList", "map", map);
    }

    @ResponseBody
    @RequestMapping("/saveConferenceCustInfomation.do")
    public String saveConferenceCustInfomation(HttpServletRequest request,
                                           HttpServletResponse response) throws Exception {
        String result = "";

        User user = (User)request.getSession().getAttribute("loginUser");

        List<CmConferenceInfomation> addInfomationList = new ArrayList<CmConferenceInfomation>();
        List<CmConferenceInfomation> updateInfomationList = new ArrayList<CmConferenceInfomation>();

        String conferenceid = request.getParameter("conferenceid");
        String conscustno = request.getParameter("conscustno");

        Map<String, String> conferenceparam = new HashMap<String, String>();
        conferenceparam.put("conferenceid", conferenceid);
        conferenceparam.put("conscustno", conscustno);
        CmConferenceConscust confconscust = cmConferenceConscustService.getConferenceConscustinfo(conferenceparam);


        if(confconscust == null){
            result = "nocust";
        }else{
            result = "success";
            Enumeration enu=request.getParameterNames();
            while(enu.hasMoreElements()){
                String paraName=(String)enu.nextElement();
                if(paraName.indexOf("update") != -1) {

                    String upfield = paraName.replace("update", "");
                    upfield = URLDecoder.decode(upfield , "UTF-8");
                    String upfieldvalue = request.getParameter(paraName);

                    if(StringUtils.isNotBlank(upfieldvalue)){
                        upfieldvalue = URLDecoder.decode(upfieldvalue , "UTF-8");
                        CmConferenceCustInfomation cmConferenceCustInfomation = new CmConferenceCustInfomation();
                        cmConferenceCustInfomation.setConferenceid(conferenceid);
                        cmConferenceCustInfomation.setConscustno(conscustno);
                        cmConferenceCustInfomation.setField(upfield);
                        cmConferenceCustInfomation.setFieldvalue(upfieldvalue);
                        cmConferenceCustInfomation.setCreater(user.getUserId());
                        cmConferenceCustInfomation.setModifier(user.getUserId());
                        cmConferenceCustInfomationService.insertCmConferenceCustInfomation(cmConferenceCustInfomation);
                    }else{
                        Map<String, String> paramMap = new HashMap<String, String>();
                        paramMap.put("conferenceid", conferenceid);
                        paramMap.put("field", upfield);
                        paramMap.put("conscustno", conscustno);
                        cmConferenceCustInfomationService.deleteCustInfomation(paramMap);
                    }

                }
            }

        }

        return result;
    }
}
