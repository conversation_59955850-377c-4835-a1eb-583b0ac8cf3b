package com.howbuy.crm.hb.web.controller.cache;

import com.howbuy.crm.page.cache.JgjjCache;
import com.howbuy.crm.page.core.domain.PageCmJjxx1Jgflb;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

/**
 * 缓存数据测试页面
 * <AUTHOR>
 * @date 2022/5/7 17:25
 */
@RequestMapping("/testCache")
public class TestCacheController {

    /**
     * 获取JgjjCache中指定基金代码对应的Jgjj实体数据
     * @param jjdm
     * @return
     */
    @RequestMapping(value = "/getJgjjCacheByJjdm", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public PageCmJjxx1Jgflb jgjjCache(String jjdm) {
        Map<String, PageCmJjxx1Jgflb> jgjjBeanMap = JgjjCache.getInstance().getJgjjBeanMap();
        return jgjjBeanMap.get(jjdm);
    }
}
