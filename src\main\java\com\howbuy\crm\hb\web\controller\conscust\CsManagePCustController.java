package com.howbuy.crm.hb.web.controller.conscust;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.domain.conscust.CmPotentialCust;
import com.howbuy.crm.hb.service.conscust.PotentialCustService;
import com.howbuy.crm.hb.service.custinfo.CustconstantService;
import com.howbuy.crm.hb.tools.excel.write.ExcelWriter;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.CmCustSourceCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.CmAct;
import com.howbuy.crm.page.core.service.CmActService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.util.PCustStatus;
import crm.howbuy.base.db.PageData;
import jxl.write.WriteException;
import jxl.write.biff.RowsExceededException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping(value = "/conscust")
public class CsManagePCustController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(CsManagePCustController.class);
    @Autowired
    private PotentialCustService potentialCustService;
    @Autowired
    private CmActService cmActService;
    @Autowired
    private CustconstantService custconstantService;

    /**
     * 跳转到名单客户管理页面
     */
    @SuppressWarnings("unchecked")
    @RequestMapping("/listCmManagePCust.do")
    public String listCmManagePCust(HttpServletRequest request, HttpServletResponse response, ModelMap model) {
        List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
        response.setContentType("text/html; charset=utf-8");
        String menuCode = "120424";
        String operCode = "01";
        boolean hasAuth = false;
        for (String role : roles) {
            List<String> temp = AuthCache.getInstance().getOperListStr(role, menuCode);
            if (temp != null && temp.contains(operCode)) {
                hasAuth = true;
            }
        }
        model.put("hasAuth", hasAuth);
        return "/conscust/listCmManagePCust";
    }

    /**
     * 加载活动类型方法
     *
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/loadActType.do")
    public void loadActType(HttpServletRequest request,
                            HttpServletResponse response) throws Exception {
        Map<String, String> param = new ParamUtil(request).getParamMap();
        List<CmAct> cmActList = cmActService.listCmAct(param);
        List<Map<String, Object>> actTypeList = new LinkedList<Map<String, Object>>();
        Map<String, Object> resultMap = new LinkedHashMap<String, Object>();
        resultMap.put("id", "");
        resultMap.put("text", "全部");
        actTypeList.add(resultMap);
        for (CmAct cmAct : cmActList) {
            resultMap = new HashMap<String, Object>();
            resultMap.put("id", cmAct.getActCode());
            resultMap.put("text", cmAct.getActName());
            actTypeList.add(resultMap);
        }

        String actType = JSON.toJSONString(actTypeList);
        response.setContentType("text/html; charset=utf-8");
        PrintWriter pw = null;
        try {
            pw = response.getWriter();
            pw.print(actType);
            pw.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (pw != null) {
                pw.close();
            }
        }
    }


    /**
     * 加载名单客户管理数据方法
     *
     * @param request
     * @param response
     * @return Map<String, Object>
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listCmManagePCust_json.do")
    public Map<String, Object> listCmManagePCust_json(HttpServletRequest request,
                                                      HttpServletResponse response) throws Exception {
        // 设置查询参数
        Map<String, String> param = new ParamUtil(request).getParamMap();
        param.put("mobile", param.get("mobile") == null ? null : DigestUtil.digest(param.get("mobile").trim()));
        param.put("telno", param.get("telno") == null ? null : DigestUtil.digest(param.get("telno").trim()));
        param.put("email", param.get("email") == null ? null : DigestUtil.digest(param.get("email").trim()));

        // 根据权限模型判断列表显示内容
        param.put("conscode", Util.getSubUserQueryByUserId(request));
        PageData<CmPotentialCust> pData = null;
        if (StringUtils.isBlank(param.get("custnamequery"))
                && StringUtils.isBlank(param.get("emailquery"))
                && StringUtils.isBlank(param.get("mobilequery"))) {

            pData = potentialCustService.listPotentialCustByPage(param);
            logger.info("隐藏查询参数：{}，查询结果待处理条数：{}", JSON.toJSONString(param), pData.getListData().size());
        } else {
            pData = potentialCustService.listPotentialcfCustByPage(param);
            logger.info("显示查询参数：{}，查询结果待处理条数：{}", JSON.toJSONString(param), JSON.toJSONString(pData));
        }

        // 获取客户来源Map
        Map<String, String> sourceMap = CmCustSourceCache.getInstance().getSourceNameMap();

        // 获取投顾Map
        Map<String, String> consMap = ConsOrgCache.getInstance().getAllConsMap();

        // 获取活动Map
        Map<String, String> actMap = ConstantCache.getInstance().getActNameMap();

        // 转义列表字段
        for (CmPotentialCust cmPotentialCust : pData.getListData()) {

            // 转义客户来源字段
            if (StringUtils.isNotEmpty(cmPotentialCust.getNewsourceno())) {
                cmPotentialCust.setSource(cmPotentialCust.getNewsourcename());
            } else {
                cmPotentialCust.setSource(sourceMap.get(cmPotentialCust.getSource()));
            }
            // 根据权限模型对购买人电话进行加密
            //edit by haibo.yu 20180802 导入人可以看到明文，其他人看到加密的信息
            String userId = getLoginUserId();
            if (userId.equals(cmPotentialCust.getUploader())) {
                cmPotentialCust.setMobile2mask(cmPotentialCust.getMobilemask());
                cmPotentialCust.setTelno2mask(cmPotentialCust.getTelnomask());
                cmPotentialCust.setEmail2mask(cmPotentialCust.getEmailmask());
            } else {
                if (StringUtils.isNotEmpty(cmPotentialCust.getMobilemask())) {
                    cmPotentialCust.setMobile2mask(cmPotentialCust.getMobilemask());
                }
                if (StringUtils.isNotEmpty(cmPotentialCust.getTelnomask())) {
                    cmPotentialCust.setTelno2mask(cmPotentialCust.getTelnomask());
                }
                if (StringUtils.isNotEmpty(cmPotentialCust.getEmailmask())) {
                    cmPotentialCust.setEmail2mask(cmPotentialCust.getEmailmask());
                }
            }
            // 转义投顾字段
            if (StringUtils.isNotEmpty(cmPotentialCust.getConscode()) && consMap.containsKey(cmPotentialCust.getConscode())) {
                cmPotentialCust.setConscode(consMap.get(cmPotentialCust.getConscode()));
            }
            // 转义活动字段
            if (StringUtils.isNotEmpty(cmPotentialCust.getActcode()) && actMap.containsKey(cmPotentialCust.getActcode())) {
                cmPotentialCust.setActcode(actMap.get(cmPotentialCust.getActcode()));
            }

        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", pData.getPageBean().getTotalNum());
        resultMap.put("rows", pData.getListData());
        return resultMap;
    }



    /**
     * 删除名单客户数据方法（假删除，只修改删除标识）
     *
     * @param request
     * @param response
     * @return Map<String, Object>
     */
    @ResponseBody
    @RequestMapping("/deletePCustByIds.do")
    public Map<String, Object> deletePCustByIds(HttpServletRequest request,
                                                HttpServletResponse response) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String ids = request.getParameter("ids");
        try {
            if (StringUtils.isNotBlank(ids)) {
                String[] pcustidArray = ids.split(",");
                List<CmPotentialCust> listCmPotentialCust = new ArrayList<CmPotentialCust>();
                for (String pcustid : pcustidArray) {
                    CmPotentialCust potentialCust = new CmPotentialCust();
                    potentialCust.setPcustid(pcustid);
                    potentialCust.setPcuststatus(PCustStatus.Del.getValue());
                    listCmPotentialCust.add(potentialCust);
                }
                potentialCustService.updatePotentialCust(listCmPotentialCust);
                resultMap.put("msg", "success");
            } else {
                resultMap.put("msg", "paramError");
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("msg", "error");
        }
        return resultMap;
    }

    /**
     * 删除当前所有数据方法（假删除，只修改删除标识）
     * 2025年5月15日  删除 无调用入口。   后续可删除
     * @param request
     * @param response
     * @return Map<String, Object>
     */
    @ResponseBody
    @RequestMapping("/deleteAllPCustByParams.do")
    public Map<String, Object> deleteAllPCustByParams(HttpServletRequest request,
                                                      HttpServletResponse response) {

        Map<String, Object> resultMap = new HashMap<String, Object>();
        try {
            // 设置查询参数
            Map<String, String> param = new ParamUtil(request).getParamMap();
            param.put("mobile", param.get("mobile") == null ? null : DigestUtil.digest(param.get("mobile").trim()));
            param.put("telno", param.get("telno") == null ? null : DigestUtil.digest(param.get("telno").trim()));
            param.put("email", param.get("email") == null ? null : DigestUtil.digest(param.get("email").trim()));

            List<CmPotentialCust> listCmPotentialCust = potentialCustService.listPotentialCust(param);
            for (CmPotentialCust cmPotentialCust : listCmPotentialCust) {
                cmPotentialCust.setPcuststatus(PCustStatus.Del.getValue());
            }
            potentialCustService.updatePotentialCust(listCmPotentialCust);
            resultMap.put("msg", "success");
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("msg", "error");
        }
        return resultMap;
    }


    /**
     * 确认名单客户操作方法
     *
     * @param request
     * @param response
     * @return Map<String, Object>
     */
    @ResponseBody
    @RequestMapping("/confirmPCustByIds.do")
    public ReturnMessageDto<String> confirmPCustByIds(HttpServletRequest request,
                                                      HttpServletResponse response) {
        String ids = request.getParameter("ids");
        if (StringUtils.isBlank(ids)) {
            return ReturnMessageDto.fail("参数出现异常，操作失败！");
        }

        try {
            List<String> processIdList = Lists.newArrayList(ids.split(","));
            String userId=getLoginUserId();
            // 执行确认方法
            return potentialCustService.processConfirmPotentialCust(processIdList, userId);
        } catch (Exception e) {
            logger.error("确认名单客户操作方法异常：", e);
            return ReturnMessageDto.fail("系统异常!");
        }
    }

    /**
     * 确认全部名单客户操作方法
     * 2025年5月15日 调用无入口  可删除
     * @param request
     * @param response
     * @return Map<String, Object>
     */
    @ResponseBody
    @RequestMapping("/confirmPCustByAll.do")
    public ReturnMessageDto<String> confirmPCustByAll(HttpServletRequest request,
                                                      HttpServletResponse response) {
        Map<String, String> param;
        // 设置查询参数
        try {
            param = new ParamUtil(request).getParamMap();
        }catch (Exception e) {
            logger.error("确认名单客户操作方法异常：", e);
            return ReturnMessageDto.fail("参数出现异常，操作失败！");
        }

        param.put("mobile", param.get("mobile") == null ? null : DigestUtil.digest(param.get("mobile").trim()));
        param.put("telno", param.get("telno") == null ? null : DigestUtil.digest(param.get("telno").trim()));
        param.put("email", param.get("email") == null ? null : DigestUtil.digest(param.get("email").trim()));
        // 根据查询参数获取列表记录
        List<CmPotentialCust> listCmPotentialCust = potentialCustService.listPotentialCust(param);
        logger.info("隐藏查询参数：{}，查询结果待处理条数：{}",JSON.toJSONString(param), listCmPotentialCust.size());

        try {
            List<String> processIdList = listCmPotentialCust.stream().map(CmPotentialCust::getPcustid).collect(Collectors.toList());
            String userId=getLoginUserId();
            // 执行确认方法
            return potentialCustService.processConfirmPotentialCust(processIdList, userId);
        } catch (Exception e) {
            logger.error("确认名单客户操作方法异常：", e);
            return ReturnMessageDto.fail("系统异常!");
        }
    }

    @RequestMapping("/listCmManagePCust/toExport.do")
    public void listCmManagePCustToExport(HttpServletRequest request,
                                          HttpServletResponse response) {
        try {
            // 设置查询参数
            Map<String, String> param = new ParamUtil(request).getParamMap();
            param.put("mobile", param.get("mobile") == null ? null : DigestUtil.digest(param.get("mobile").trim()));
            param.put("telno", param.get("telno") == null ? null : DigestUtil.digest(param.get("telno").trim()));
            param.put("email", param.get("email") == null ? null : DigestUtil.digest(param.get("email").trim()));

            // 根据权限模型判断列表显示内容
            param.put("conscode", Util.getSubQueryByUserId(request));
            List<Map<String, Object>> pData = null;
            if (StringUtils.isBlank(param.get("custnamequery"))
                    && StringUtils.isBlank(param.get("emailquery"))
                    && StringUtils.isBlank(param.get("mobilequery"))) {
                pData = new ArrayList<>();
            } else {
                pData = potentialCustService.listPotentialcfCust(param);
            }
            // 获取投顾Map
            Map<String, String> consMap = ConsOrgCache.getInstance().getAllConsMap();
            // 获取投顾Map
            Map<String, String> consOutletMap = ConsOrgCache.getInstance().getCons2OutletMap();
            Map<String, String> allOrgMap = ConsOrgCache.getInstance().getAllOrgMap();
            List<Map<String, String>> listMaps = new ArrayList<>(pData.size());
            for (int i = 0; i < pData.size(); i++) {
                Map<String, Object> cmPotentialCust = pData.get(i);
                Map<String, String> map = new HashMap<>(5);
                map.put("rowid", (String) cmPotentialCust.get("PCUSTID"));
                map.put("conscustno", (String) cmPotentialCust.get("CONSCUSTNO"));
                String mobileAreaCode = (String) cmPotentialCust.get("MOBILE_AREA_CODE");
                map.put("mobileAreaCode", mobileAreaCode);
                String mobile = (String) cmPotentialCust.get("MOBILE_MASK");
                map.put("mobile", mobile);
                map.put("sourcename", (String) cmPotentialCust.get("DICT_NAME"));
                Map<String, String> paramMap = new HashMap<String, String>();
                paramMap.put("custno", (String) cmPotentialCust.get("CONSCUSTNO"));
                String conscode = custconstantService.getConscodeMgrcodeByCustNo(paramMap).getConscode();
                // 转义投顾字段
                if (StringUtils.isNotEmpty(conscode) && consMap.containsKey(conscode)) {
                    map.put("conscodename", consMap.get(conscode));
                    map.put("conscodeOutlet", allOrgMap.get(consOutletMap.get(conscode)));
                } else {
                    map.put("conscodename", "");
                    map.put("conscodeOutlet", "");
                }
                listMaps.add(map);
            }
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition", "attachment;fileName="
                    + new String("名单客户.xls".getBytes("gb2312"),
                    "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();
            String[] columnName = {"序号", "投顾客户号", "手机区号", "手机号", "客户来源", "所属投顾", "所属部门"};
            String[] beanProperty = {"rowid", "conscustno", "mobileAreaCode", "mobile", "sourcename", "conscodename", "conscodeOutlet"};
            ExcelWriter.writeExcel(os, "名单客户", 0, listMaps,
                    columnName, beanProperty);
            os.close(); // 关闭流
        } catch (Exception e) {
            logger.error("文件导出异常", e);
        }

    }

}
