package com.howbuy.crm.hb.web.controller.counter;

import com.howbuy.crm.hb.constants.CrmTradeServerPathConstant;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.trade.common.enums.counter.CounterIsQualEnum;
import com.howbuy.crm.trade.common.response.BaseResponse;
import com.howbuy.crm.trade.common.util.CounterUtil;
import com.howbuy.crm.trade.model.counter.dto.CmCounterOrderFileFlowDto;
import com.howbuy.crm.trade.model.counter.po.CmCounterOrderFlow;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: Controller
 * @version 1.0
 */
@Controller
@RequestMapping(value = "/counterorderflow")
public class CmCounterOrderFlowController  extends BaseCounterController {

	
	/**
	 * 跳转到柜台订单主列表页面
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listCounterOrderFlow")
	public ModelAndView listCounterOrderFlow(HttpServletRequest request){
		String orderid = request.getParameter("orderid");
		return new ModelAndView("/counter/listCounterOrderFlow","orderid",orderid);
	}
	
	@ResponseBody
	@RequestMapping("/queryCounterOrderFlowList")
	public Map<String, Object> queryCounterOrderFlowList(HttpServletRequest request) throws Exception {
		// 设置查询分页参数
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("orderId", request.getParameter("orderid"));
		postParam.put("busiArea", request.getParameter("isXgcj"));
		BaseResponse<List<CmCounterOrderFlow>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_ORDER_FLOW_LIST, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderFlow>>>(){});
		if (httpRsp.isSuccess()) {
			List<CmCounterOrderFlow> counterOrderFlowlist = httpRsp.getData();
			ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
			Map<String, String> allUserMap = consOrgCache.getAllUserMap();
			for(CmCounterOrderFlow counterOrderFlow:counterOrderFlowlist){
				String curCheckerName = allUserMap.get(counterOrderFlow.getCurChecker());
				counterOrderFlow.setCurChecker(StringUtils.isNotBlank(curCheckerName) ? curCheckerName : counterOrderFlow.getCurChecker());
//				counterOrderFlow.setCurStat( StringUtils.isBlank(counterOrderFlow.getCurStat()) ? null : CounterStateEnum.getDesc(counterOrderFlow.getCurStat()));
				counterOrderFlow.setCurStat(CounterUtil.getCurStatName(counterOrderFlow.getCurStat(),counterOrderFlow.getRevisitFlag()));
			}
			resultMap.put("total", counterOrderFlowlist.size());
			resultMap.put("rows", counterOrderFlowlist);
		}

		return resultMap;
	}

	/**
	 * 跳转到柜台订单主列表页面
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/viewListCounterOrderFileFlow")
	public ModelAndView viewListCounterOrderFileFlow(HttpServletRequest request) {
		ModelAndView modelview = new ModelAndView();
		String oflowId = request.getParameter("oflowId");
		CmCounterOrderFlow counterOrderFlow = null;
		Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("id", request.getParameter("oflowId"));
		BaseResponse<CmCounterOrderFlow> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.GET_COUNTER_ORDER_FLOW, postParam,new ParameterizedTypeReference<BaseResponse<CmCounterOrderFlow>>(){});
		if (httpRsp.isSuccess()) {
			counterOrderFlow = httpRsp.getData();
			if(counterOrderFlow != null){
				if(StringUtils.isNotBlank(counterOrderFlow.getCurCheckdes())){
					modelview.addObject("curCheckdes",counterOrderFlow.getCurCheckdes());
				}
			}
		}
		modelview.addObject("oflowId",oflowId);
		modelview.setViewName("/counter/listCounterOrderFileFlow");
		return modelview;
	}


	@ResponseBody
	@RequestMapping("/queryCounterOrderFileFlowList")
	public Map<String, Object> queryCounterOrderFileFlowList(HttpServletRequest request) throws Exception {
		// 设置查询分页参数
		Map<String, Object> resultMap = new HashMap<String, Object>();
		List<CmCounterOrderFileFlowDto> counterOrderFileFlowDtolist = null;
	    Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("oflowId", request.getParameter("oflowId"));
		BaseResponse<List<CmCounterOrderFileFlowDto>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_ORDER_FILE_FLOW_DTO_LIST, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterOrderFileFlowDto>>>(){});
		if (httpRsp.isSuccess()) {
			counterOrderFileFlowDtolist = httpRsp.getData();
			for(CmCounterOrderFileFlowDto cmCounterOrderFileFlowDto:counterOrderFileFlowDtolist){
				cmCounterOrderFileFlowDto.setIsQual( StringUtils.isBlank(cmCounterOrderFileFlowDto.getIsQual()) ? null : CounterIsQualEnum.getDesc(cmCounterOrderFileFlowDto.getIsQual()) );
			}
		}
		
		resultMap.put("total", CollectionUtils.isEmpty(counterOrderFileFlowDtolist) ? 0 : counterOrderFileFlowDtolist.size());
		resultMap.put("rows", counterOrderFileFlowDtolist);

		return resultMap;
	}
}
