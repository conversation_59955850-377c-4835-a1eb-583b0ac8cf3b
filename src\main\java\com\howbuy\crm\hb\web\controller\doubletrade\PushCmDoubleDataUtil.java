package com.howbuy.crm.hb.web.controller.doubletrade;

import java.util.List;
import com.howbuy.crm.doubletrade.dto.DoubleTradeInfoDomain;
import com.howbuy.crm.doubletrade.request.UpdateDoubleTradeInfoRequest;
import com.howbuy.crm.doubletrade.service.PushStatusToZtTradeInfoService;
import com.howbuy.crm.page.framework.context.SpringBeanUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 推送双录数据工具类
 */
public class PushCmDoubleDataUtil implements Runnable {
	private String systemFlag;

	private List<DoubleTradeInfoDomain> syncList;

	private Logger logger = LoggerFactory.getLogger(PushCmDoubleDataUtil.class);

	public String getSystemFlag() {
		return systemFlag;
	}

	public void setSystemFlag(String systemFlag) {
		this.systemFlag = systemFlag;
	}

	public List<DoubleTradeInfoDomain> getSyncList() {
		return syncList;
	}

	public void setSyncList(List<DoubleTradeInfoDomain> syncList) {
		this.syncList = syncList;
	}

	/**
	 * 向ZT推送数据状态方法
	 */
	public void pushStatusToZT() {
		try {
			System.out.println("同步双录状态到ZT系统Start...");
			List<DoubleTradeInfoDomain> syncList = getSyncList();
			UpdateDoubleTradeInfoRequest reqParam = new UpdateDoubleTradeInfoRequest();
			reqParam.setDoubleTradeList(syncList);
			PushStatusToZtTradeInfoService pushStatusToZtTradeInfoService = (PushStatusToZtTradeInfoService)SpringBeanUtil.getBean("PushStatusToZtTradeInfoService");
			pushStatusToZtTradeInfoService.pushStatusToZT(reqParam);
			System.out.println("同步双录状态到ZT系统End...");
		} catch (Exception e) {
			logger.error("向ZT推送数据失败，原因：" + e.getMessage());
		}
	}

	@Override
	public void run() {
		try {
			if ("ZT".equals(getSystemFlag())) {
				this.pushStatusToZT();
			}
		} catch (RuntimeException e) {
			logger.error("运行时异常：" + e.getMessage());
		}
	}

}
