package com.howbuy.crm.hb.web.controller.player;

import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 视频播放器类
 */
@Slf4j
@Controller
@RequestMapping("/videoPlayer")
public class VideoPlayer {

    /**
     * 视频播放方法
     * 可以支持MP4和FLV格式视频
     * 参数为视频的完整地址：videoURL
     */
    @RequestMapping("/toPlay.do")
    public String toPlay(HttpServletRequest request, ModelMap model) {
        String playerPage = "player/mp4Player";
        String videoURL = request.getParameter("videoURL");

        // 测试视频
//        videoURL = "http://192.168.222.161:8085/upload/202001/20200102475733.mp4";
//        videoURL = "http://192.168.222.161:8085/upload/201912/20191231710272.flv";
        if (StringUtils.isNotBlank(videoURL)) {
            model.put("videoURL", videoURL);
            String videoType = videoURL.substring(videoURL.lastIndexOf(".") + 1).toUpperCase();
            if ("MP4".equals(videoType)) {
                playerPage = "player/mp4Player";
            } else if ("FLV".equals(videoType)) {
                playerPage = "player/flvPlayer";
            } else {
                model.put("errorMsg", "暂不支持该格式视频！");
                log.info("暂不支持该格式视频！");
            }
        } else {
            model.put("errorMsg", "播放地址获取失败！");
            log.error("播放地址获取失败！");
        }
        return playerPage;
    }

}
