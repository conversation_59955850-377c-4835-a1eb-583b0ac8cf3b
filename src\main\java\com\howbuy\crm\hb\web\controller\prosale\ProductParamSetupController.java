package com.howbuy.crm.hb.web.controller.prosale;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.prosale.CmProductParamSetup;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.prosale.ProductParamSetupService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.utils.StringUtil;

/**
 * 产品参数设置controller层
 * 
 * author: wu.long
 * date: 2019年7月17日 下午4:32:48
 * version: V1.0
 * since: jdk 1.8,tomcat 8.0
 */
@Slf4j
@Controller
@RequestMapping(value = "/productParamSetup")
public class ProductParamSetupController {
	@Autowired
	private ProductParamSetupService productParamSetupService;
	
	@Autowired
	private ConscustService conscustService;
	
	/**
	 * 加载产品参数设置页面
	 * 
	 * author: wu.long
	 * date: 2019年7月18日 下午3:51:43 
	 * @param request
	 * @param response
	 * @param model
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping("/showProductSetupPage")
	public String showProductSetupPage(HttpServletRequest request,HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
		return "prosale/productParamSetup";
	}
	
	/**
	 * 新增或修改产品参数设置信息
	 * 
	 * author: wu.long
	 * date: 2019年7月19日 下午1:54:30 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/addOrUpdateProductParamSetup")
	public String addOrUpdateProductParamSetup(HttpServletRequest request){
		String result = "";
		try {
			User user = (User)request.getSession().getAttribute("loginUser");
			String id = request.getParameter("id");
			String pcode = request.getParameter("form_pcode");
			String supportRepurchase = request.getParameter("supportRepurchase");
			String internalDeduction = request.getParameter("internalDeduction");
			String personalPreStatus =request.getParameter("personalPreStatus");
			String institutionPreStatus =request.getParameter("institutionPreStatus");
			String productPreStatus =request.getParameter("productPreStatus");
			result = productParamSetupService.addOrUpdateProductParamSetup(id, pcode, supportRepurchase,personalPreStatus, institutionPreStatus, productPreStatus, internalDeduction, user.getUserId());
		} catch (Exception e) {
			log.error((StringUtils.isNotBlank(request.getParameter("id"))?"修改":"新增")+"产品参数设置信息异常：", e);
		}
		return result;
	}
	
	
	
	/**
	 * 获取产品系数配置:
	 * @param request
	* <AUTHOR>
	* @date 2020/12/25
	*/
	@RequestMapping("/getProductParamSetup.do")
	@ResponseBody
	public Map<String, Object> getProductParamSetup(HttpServletRequest request) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(1);
        String retMsg = null;
		String pcode = request.getParameter("pcode");
		String custno = request.getParameter("custno");
		StringBuilder sb = new StringBuilder();
		String notFlag = "0";
		String yesFlag = "1";
		CmProductParamSetup paramsetup = null;
		if(StringUtils.isNotBlank(pcode) && StringUtils.isNotBlank(custno)){
			paramsetup = productParamSetupService.selectParamSetupByPCode(pcode);
			if(paramsetup != null){
				Conscust conscust = conscustService.getConscust(custno);
				if(conscust != null){
					if(notFlag.equals(paramsetup.getPersonalPreStatus()) && notFlag.equals(paramsetup.getProductPreStatus()) && notFlag.equals(paramsetup.getInstitutionPreStatus())){
						sb.append("该产品已暂停预约，请预约其他feeder!</br>");
					}else{
						if(yesFlag.equals(paramsetup.getPersonalPreStatus()) && yesFlag.equals(paramsetup.getProductPreStatus()) && yesFlag.equals(paramsetup.getInstitutionPreStatus())){
						}else{
							StringBuilder notFlagStr = new StringBuilder();
							if(notFlag.equals(paramsetup.getPersonalPreStatus())){
								notFlagStr.append(",个人客户");
							}
							if(notFlag.equals(paramsetup.getProductPreStatus())){
								notFlagStr.append(",产品客户");
							}
							if(notFlag.equals(paramsetup.getInstitutionPreStatus())){
								notFlagStr.append(",机构客户,基金客户");
							}
							boolean preflag = true; 
							//个人客户不符合
							if (StaticVar.INVST_TYPE_PERSONAL.equals(conscust.getInvsttype()) && notFlag.equals(paramsetup.getPersonalPreStatus())) {
								preflag = false;
							}
							//机构客户不符合
							if (StaticVar.INVST_TYPE_ORG.equals(conscust.getInvsttype()) && notFlag.equals(paramsetup.getInstitutionPreStatus())) {
								preflag = false;
							}
							//基金客户不符合
							if (StaticVar.INVST_TYPE_FUND.equals(conscust.getInvsttype()) && notFlag.equals(paramsetup.getInstitutionPreStatus())) {
								preflag = false;
							}
							//产品客户不符合
							if (StaticVar.INVST_TYPE_PRODUCT.equals(conscust.getInvsttype()) && notFlag.equals(paramsetup.getProductPreStatus())) {
								preflag = false;
							}
							if(!preflag){
								sb.append("该产品针对"+notFlagStr.toString().replaceFirst(",", "")+"不可预约!</br>");
							}
						}
					}
				}
				if(StringUtil.isNotNullStr(sb.toString())){
					retMsg = sb.toString();
				}
			}
		}else{
			retMsg = "参数错误，请重新输入产品和客户!";
		}
		resultMap.put("retMsg", retMsg);
		return resultMap;
	}
	
	
	/**
	 * 根据条件查询产品参数设置信息
	 * 
	 * author: wu.long
	 * date: 2019年7月22日 上午10:27:01 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/selectProductParamSetupByPage")
	public Map<String, Object> selectProductParamSetupByParams(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			Map<String, String> paramMap = new HashMap<String, String>();
			//获取分页参数
			paramMap = new ParamUtil(request).getParamMap();
			paramMap.put("pcode", request.getParameter("pcode"));
			resultMap = productParamSetupService.selectProductParamSetupByPage(paramMap);
		} catch (Exception e) {
			log.error("根据条件查询产品参数设置信息异常：", e);
		}
		return resultMap;
	}
	
	/**
	 * 删除产品参数设置信息
	 * 
	 * author: wu.long
	 * date: 2019年7月22日 下午5:30:53 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/deleteProductParamSetupById")
	public String deleteProductParamSetupById(HttpServletRequest request){
		String result = "";
		try {
			User user = (User)request.getSession().getAttribute("loginUser");
			String id = request.getParameter("id");
			result = productParamSetupService.deleteProductParamSetupById(id, user.getUserId());
		} catch (Exception e) {
			log.error("删除产品参数设置信息异常：", e);
		}
		return result;
	}
	
	/**
	 * 根据id查询产品参数信息
	 * 
	 * author: wu.long
	 * date: 2019年7月30日 下午6:46:05 
	 * @param request
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/selectSfmsjgByPCode")
	public Map<String, Object> selectSfmsjgByPCode(HttpServletRequest request){
		Map<String, Object> resultMap = new HashMap<String, Object>();
		try {
			String pcode = request.getParameter("pcode");
			resultMap = productParamSetupService.selectSfmsjgInfoByPCode(pcode);
		} catch (Exception e) {
			log.error("根据id查询产品参数信息异常：", e);
		}
		return resultMap;
	}
	
	
	
	
	
	
	
	
	
}
