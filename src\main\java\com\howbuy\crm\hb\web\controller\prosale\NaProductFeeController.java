package com.howbuy.crm.hb.web.controller.prosale;

import com.howbuy.crm.base.PreBookArchTypeEnum;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.domain.hkconscust.HkConscust;
import com.howbuy.crm.hb.persistence.common.CommonMapper;
import com.howbuy.crm.hb.service.hkconscust.HkConscustService;
import com.howbuy.crm.hb.service.prosale.CustprivatefundService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.reader.ExcelReader;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.prosale.dto.NaProductFee;
import com.howbuy.crm.prosale.dto.NaProductFeeDetail;
import com.howbuy.crm.prosale.service.NaProductFeeService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
import jxl.Sheet;
import jxl.Workbook;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Description: 
 * @reason:
 * @Date: 2020/7/24 10:09
 */
@Slf4j
@Controller
@RequestMapping(value = "/naproduct")
public class NaProductFeeController  extends BaseController {

    @Autowired
    private NaProductFeeService naProductFeeService;
    
    @Autowired
    private CommonMapper commonMapper;
    
    @Autowired
    private QueryConscustInfoService queryConscustInfoService;
    
    @Autowired
    private PageVisitLogService pageVisitLogService;
    
    @Autowired
    private CustprivatefundService custprivatefundService;
    
    @Autowired
    private HkConscustService hkConscustService;
	@Autowired
	private JjxxInfoService jjxxInfoService;

	@Autowired
	private PrebookBasicInfoService prebookBasicInfoService;
    
    private final String DOWNLOAD_FILE_NAME="导入NA产品费用模板.xls";
	
	private final String MODEL_FILE_NAME="NaProductFeeModel.xls";
    

    @RequestMapping("/listNaProductFee.do")
    public ModelAndView listNaProductFee() {
		return new ModelAndView("prosale/listNaProductFee");
    }
    
    /**
	 * 加载页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listNaProductFeeByPage_json.do")
	public Map<String, Object> listNaProductFeeByPage(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String custname = request.getParameter("custname");
		String fundcode = request.getParameter("fundcode");
		String hkcustid = request.getParameter("hkcustid");
		
		// 设置查询条件（客户名）
		if (StringUtils.isNotBlank(custname)) {
			param.put("custname", custname);
		} else {
			param.put("custname", null);
		}

		// 设置查询条件（香港客户id）
		if (StringUtils.isNotBlank(hkcustid)) {
			param.put("hkcustid", hkcustid);
		} else {
			param.put("hkcustid", null);
		}

		// 设置查询条件（产品代码）
		if (StringUtils.isNotBlank(fundcode)) {
			param.put("fundcode", fundcode);
		} else {
			param.put("fundcode", null);
		}
		param.put("isdel", StaticVar.INSUR_ISDEL_NO);
		PageData<NaProductFee> pageData = naProductFeeService.listNaProductFeeByPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<NaProductFee> listdata = pageData.getListData();

		resultMap.put("rows", listdata);
		return resultMap;
	}
	
	/**
     * 新增Na费用明细:
     * @param request
    * <AUTHOR>
    * @date 2020/7/27
    */
    @RequestMapping("/saveNaProductFee.do")
    @ResponseBody
    public String saveNaProductFee(HttpServletRequest request){
    	String result="";
        String custno = request.getParameter("custno");
        String fundcode = request.getParameter("fundcode");
        String receivmanagefee = request.getParameter("receivmanagefee");
        String receivpreformfee = request.getParameter("receivpreformfee");
        String paymanagefee = request.getParameter("paymanagefee");
        String paypreformfee = request.getParameter("paypreformfee");
        String totalmanagefee = request.getParameter("totalmanagefee");
        String totalpreformfee = request.getParameter("totalpreformfee");
        String strResult = request.getParameter("strResult");
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        //判断客户产品是否已经存在
        Map<String,String> param = new HashMap<>(3);
        param.put("conscustno", custno);
        param.put("fundcode", fundcode);
        param.put("isdel", StaticVar.INSUR_ISDEL_NO);
        List<NaProductFee> listfee = naProductFeeService.listNaProductFee(param);
        if(listfee != null && listfee.size() > 0){
			return "该客户和产品已经存在！";
        }

       //拦截香港产品
		String archType =prebookBasicInfoService.getArchType(custno,fundcode);
		if(PreBookArchTypeEnum.HW.getCode().equals(archType)){
			return  "不支持海外产品操作！";
		}

        NaProductFee fee = new NaProductFee();
        BigDecimal id = new BigDecimal(commonMapper.getSeqValue("SEQ_PRODUCT_PARAM_SETUP_ID"));
        fee.setId(id);
        fee.setConscustno(custno);
        fee.setFundcode(fundcode);
        fee.setReceivmanagefee(StringUtil.isNotNullStr(receivmanagefee) ? new BigDecimal(receivmanagefee) : null);
        fee.setReceivpreformfee(StringUtil.isNotNullStr(receivpreformfee) ? new BigDecimal(receivpreformfee) : null);
        fee.setPaymanagefee(StringUtil.isNotNullStr(paymanagefee) ? new BigDecimal(paymanagefee) : null);
        fee.setPaypreformfee(StringUtil.isNotNullStr(paypreformfee) ? new BigDecimal(paypreformfee) : null);
        fee.setTotalmanagefee(StringUtil.isNotNullStr(totalmanagefee) ? new BigDecimal(totalmanagefee) : null);
        fee.setTotalpreformfee(StringUtil.isNotNullStr(totalpreformfee) ? new BigDecimal(totalpreformfee) : null);
        fee.setIsdel(StaticVar.INSUR_ISDEL_NO);
        fee.setCreator(userlogin.getUserId());
        //解析明细字符串，去掉第一个X，后面有多少条就用X分割开
        strResult = strResult.replaceFirst("X", "");
        String [] results = strResult.split("X");
        List<NaProductFeeDetail> listdetail = new ArrayList<>();
        for(int i = 0;i < results.length;i++){
        	String strdetail = results[i];
        	NaProductFeeDetail naDetail = new NaProductFeeDetail();
        	naDetail.setId(new BigDecimal(commonMapper.getSeqValue("SEQ_PRODUCT_PARAM_SETUP_ID")));
        	naDetail.setFid(id);
        	//每条记录的每个字段是用#隔开的，第一个和第二个是必填项
        	naDetail.setFeetype(strdetail.split("#")[0]);
        	naDetail.setFeedt(strdetail.split("#")[1]);
        	//两个费用为非必填项，所以根据实际的长度决定
        	if(strdetail.split("#").length > 2 && StringUtil.isNotNullStr(strdetail.split("#")[2])){
        		naDetail.setManagefee(new BigDecimal(strdetail.split("#")[2]));
        	}
        	if(strdetail.split("#").length > 3 && StringUtil.isNotNullStr(strdetail.split("#")[3])){
        		naDetail.setPreformfee(new BigDecimal(strdetail.split("#")[3]));
        	}
        	naDetail.setIsdel(StaticVar.INSUR_ISDEL_NO);
        	naDetail.setCreator(userlogin.getUserId());
        	listdetail.add(naDetail);
        }
        naProductFeeService.addNaProductFee(fee, listdetail);
        result = "success";
        return result;
    }
    
    /**
	 * 获取家庭客户信息方法
	 * @param request
	 * @return Map<String, Object>
	 */
	@ResponseBody
	@RequestMapping("/getNaProductFeeByEdit.do")
	public Map<String, Object> getNaProductFeeByEdit(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>(3);
		String id = request.getParameter("id");
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>(3);
		param.put("id", id);
		NaProductFee fee = naProductFeeService.getNaProductFee(param);
		resultMap.put("fee",fee);
		param.clear();
		param.put("fid", id);
		param.put("isdel", StaticVar.INSUR_ISDEL_NO);
		List<NaProductFeeDetail> listdetail = naProductFeeService.listNaProductFeeDetail(param);
		resultMap.put("details",listdetail);
		return resultMap;
	}
	
	/**
     * 新增Na费用明细:
     * @param request
    * <AUTHOR>
    * @date 2020/7/27
    */
    @RequestMapping("/updateNaProductFee.do")
    @ResponseBody
    public String updateNaProductFee(HttpServletRequest request){
    	String result="";
    	String id = request.getParameter("id");
        String receivmanagefee = request.getParameter("receivmanagefee");
        String receivpreformfee = request.getParameter("receivpreformfee");
        String paymanagefee = request.getParameter("paymanagefee");
        String paypreformfee = request.getParameter("paypreformfee");
        String totalmanagefee = request.getParameter("totalmanagefee");
        String totalpreformfee = request.getParameter("totalpreformfee");
        String strResult = request.getParameter("strResult");
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        //判断客户产品是否已经存在
        Map<String,String> param = new HashMap<>(1);
        param.put("id", id);
        NaProductFee fee = naProductFeeService.getNaProductFee(param);
        fee.setReceivmanagefee(StringUtil.isNotNullStr(receivmanagefee) ? new BigDecimal(receivmanagefee) : null);
        fee.setReceivpreformfee(StringUtil.isNotNullStr(receivpreformfee) ? new BigDecimal(receivpreformfee) : null);
        fee.setPaymanagefee(StringUtil.isNotNullStr(paymanagefee) ? new BigDecimal(paymanagefee) : null);
        fee.setPaypreformfee(StringUtil.isNotNullStr(paypreformfee) ? new BigDecimal(paypreformfee) : null);
        fee.setTotalmanagefee(StringUtil.isNotNullStr(totalmanagefee) ? new BigDecimal(totalmanagefee) : null);
        fee.setTotalpreformfee(StringUtil.isNotNullStr(totalpreformfee) ? new BigDecimal(totalpreformfee) : null);
        fee.setModifier(userlogin.getUserId());
        fee.setModifydt(new Date());
        //解析明细字符串，去掉第一个X，后面有多少条就用X分割开
        strResult = strResult.replaceFirst("X", "");
        String [] results = strResult.split("X");
        List<NaProductFeeDetail> listdetail = new ArrayList<>();
        for(int i = 0;i < results.length;i++){
        	String strdetail = results[i];
        	NaProductFeeDetail naDetail = new NaProductFeeDetail();
        	naDetail.setId(new BigDecimal(commonMapper.getSeqValue("SEQ_PRODUCT_PARAM_SETUP_ID")));
        	naDetail.setFid(fee.getId());
        	//每条记录的每个字段是用#隔开的，第一个和第二个是必填项
        	naDetail.setFeetype(strdetail.split("#")[0]);
        	naDetail.setFeedt(strdetail.split("#")[1]);
        	//两个费用为非必填项，所以根据实际的长度决定
        	if(strdetail.split("#").length > 2 && StringUtil.isNotNullStr(strdetail.split("#")[2])){
        		naDetail.setManagefee(new BigDecimal(strdetail.split("#")[2]));
        	}
        	if(strdetail.split("#").length > 3 && StringUtil.isNotNullStr(strdetail.split("#")[3])){
        		naDetail.setPreformfee(new BigDecimal(strdetail.split("#")[3]));
        	}
			if(strdetail.split("#").length > 4 && StringUtil.isNotNullStr(strdetail.split("#")[4])){
				naDetail.setCarryid(strdetail.split("#")[4]);
			}
        	naDetail.setIsdel(StaticVar.INSUR_ISDEL_NO);
        	naDetail.setCreator(userlogin.getUserId());
        	listdetail.add(naDetail);
        }
        naProductFeeService.updateNaProductFee(fee, listdetail);
        result = "success";
        return result;
    }
    
  
    /**
     * 导出操作
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportNaProdFee.do")
    public void exportNaProdFee(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
    	Map<String, String> param = new ParamUtil(request).getParamMap();
    	List<NaProductFee> exportList = naProductFeeService.listNaProductFeeByExp(param);
    	List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
		User user = (User) request.getSession().getAttribute("loginUser");
		String ip = getIpAddr(request);
    	for (NaProductFee info : exportList) {
			info.setFeetype(ConstantCache.getInstance().getVal("naProdFeeType", info.getFeetype()));
			//记录访问日志
            PageVisitLog pageVisitLog = new PageVisitLog();
            pageVisitLog.setConscustno(info.getConscustno());
            pageVisitLog.setUserId(user.getUserId());
            pageVisitLog.setVisitUrl(request.getRequestURI());
            pageVisitLog.setOperation("NA产品费用维护");
            pageVisitLog.setVisitTime(new Date());
            pageVisitLog.setPreid(info.getId());
            pageVisitLog.setIp(ip);
            listlog.add(pageVisitLog);
    	}
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("NA产品费用导出.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String [] columnName = new String[]{"客户香港ID","投顾客户号","客户姓名","产品代码","产品名称","累计应收管理费","累计应收业绩报酬","累计实收管理费","累计实收业绩报酬","累计管理费","累计业绩报酬","费用类型","费用发生日期","应收管理费","应收业绩报酬"};

            String [] beanProperty = new String[] {"hkcustid","conscustno","custname","fundcode","fundname","receivmanagefee","receivpreformfee","paymanagefee","paypreformfee","totalmanagefee","totalpreformfee","feetype","feedt","managefee","preformfee"};
            ExcelWriter.writeExcel(os, "NA产品费用", 0, exportList, columnName, beanProperty);
            os.close(); // 关闭流
            for(PageVisitLog log : listlog){
            	pageVisitLogService.recordLog(log);
            }
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }
    	
    }
    
    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    @RequestMapping("/downloadImportMode.do")
   	public String downloadImportMode( HttpServletRequest request,
   			HttpServletResponse response) {
		return dowmloadTemplate(MODEL_FILE_NAME,DOWNLOAD_FILE_NAME,request,response);
   	}
    
    @RequestMapping(value="/importNaProductFee.do",method=RequestMethod.POST)
	public  @ResponseBody Map<String, Object> importNaProductFee(HttpServletRequest request){
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		
		User user = (User)request.getSession().getAttribute("loginUser");
		
		InputStream input = null;
		Workbook workBook = null;
		
		String errorMsg = "";
		String uploadFlag = "success";
		
		try {  
			// 转型为MultipartHttpRequest：  
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;  
			// 获得文件：  
			MultipartFile file = multipartRequest.getFile("file");   
			// 获得输入流：  
			input = file.getInputStream();  
			workBook = Workbook.getWorkbook(input);
			List<NaProductFee> list = new ArrayList<NaProductFee>();
			for (Sheet sheet : workBook.getSheets()) {
				//在第二行开始遍历
				for(int i = 1;i < sheet.getRows();i++){
					NaProductFee vo = new NaProductFee();
					vo.setHkcustid(ExcelReader.getValue(sheet.getCell(0, i)).trim());
					vo.setFundcode(ExcelReader.getValue(sheet.getCell(1, i)).trim());
					vo.setFeetype(ExcelReader.getValue(sheet.getCell(2, i)).trim());
					vo.setFeedt(ExcelReader.getValue(sheet.getCell(3, i)).trim());
					if(StringUtil.isNotNullStr(ExcelReader.getValue(sheet.getCell(4, i)).trim())){
						vo.setManagefee(new BigDecimal(ExcelReader.getValue(sheet.getCell(4, i)).trim()));
					}
					if(StringUtil.isNotNullStr(ExcelReader.getValue(sheet.getCell(5, i)).trim())){
						vo.setPreformfee(new BigDecimal(ExcelReader.getValue(sheet.getCell(5, i)).trim()));
					}
					list.add(vo);
				}
			}
			if (CollectionUtils.isEmpty(list)) {
				resultMap.put("uploadFlag", "error");
				resultMap.put("errorMsg", "请输入导入的内容！");
				return resultMap;
			}

			StringBuilder  sb = new StringBuilder();
			//判断回访类型、客户号、操作人必填项
			for (NaProductFee vo : list) {
				if (StringUtil.isNullStr(vo.getHkcustid()) || StringUtil.isNullStr(vo.getFundcode()) || StringUtil.isNullStr(vo.getFeetype()) || StringUtil.isNullStr(vo.getFeedt())) {
					sb.append("客户香港ID、产品代码、费用类型、费用发生日期为必填项！");
					break;
				}

			}
			if (StringUtil.isNotNullStr(sb.toString())) {
				resultMap.put("uploadFlag", "error");
				resultMap.put("errorMsg", sb.toString());
				return resultMap;
			}

			//判断客户号、产品代码是否存在，费用类型枚举值对不对，费用发生日期格式问题
			for (NaProductFee vo : list) {
				StringBuilder sb1 = new StringBuilder();
				String hkcustid = vo.getHkcustid();
				//根据香港客户id查询出关联的投顾客户号
				HkConscust hkcust = hkConscustService.findHkConscustByHkcustid(hkcustid);
				if (hkcust != null) {
					vo.setConscustno(hkcust.getConscustno());
				} else {
					addmesg(hkcustid, sb1, "未匹配上投顾客户号，请至“香港客户维护表”中查询详情");
				}
				String conscustno = vo.getConscustno();
				QueryConscustInfoRequest req = new QueryConscustInfoRequest();
				req.setConscustno(conscustno);
				QueryConscustInfoResponse res = queryConscustInfoService.queryConscustInfo(req);
				if (res == null || res.getConscustinfo() == null) {
					addmesg(hkcustid, sb1, "客户号不存在");
				}
				JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(vo.getFundcode(), false);
				if (jjxx1 == null) {
					addmesg(hkcustid, sb1, vo.getFundcode() + "代码不存在");
				}
				if (StringUtil.isNotNullStr(vo.getFeetype()) && !(StaticVar.NA_FEETYPE_RECEIVEFEE.equals(vo.getFeetype()) || StaticVar.NA_FEETYPE_PAYFEE.equals(vo.getFeetype()))) {
					addmesg(hkcustid, sb1, "费用类型枚举不对，必须使用'1'和'2'");
				}
				if (StringUtil.isNotNullStr(vo.getFeedt()) && vo.getFeedt().length() != 8) {
					addmesg(hkcustid, sb1, "费用发生日期的日期格式不对");
				}
				if (StringUtil.isNotNullStr(sb1.toString())) {
					sb.append(sb1).append(".</br>");
				}
			}
			if (StringUtil.isNotNullStr(sb.toString())) {
				resultMap.put("uploadFlag", "error");
				resultMap.put("errorMsg", sb.toString());
				return resultMap;
			} else {
				String result = naProductFeeService.importNaProductFee(list, user.getUserId());
				String strSuccess = "success";
				if (!strSuccess.equals(result)) {
					resultMap.put("uploadFlag", "error");
					resultMap.put("errorMsg", "导入失败，请重新导入！");
					return resultMap;
				}
			}

			resultMap.put("uploadFlag", uploadFlag);  
			resultMap.put("errorMsg", errorMsg);

		} catch (Exception e) {
			log.error("importNaProductFee导入失败" + e.getMessage(), e);
			resultMap.put("uploadFlag", "error");
			resultMap.put("errorMsg", "请检查模板是否正确");
		} finally {
			try {
				if (input != null) {
					input.close();
				}
			} catch (IOException e) {
				log.error(e.getMessage(), e);
			}
		}
		 
		return resultMap;
	}

	private void addmesg(String hkcustid, StringBuilder sb1, String msg) {
		if (StringUtil.isNotNullStr(sb1.toString())) {
			sb1.append("、").append(msg);
		} else {
			sb1.append("ebrokerID：").append(hkcustid).append(" ").append(msg);
		}
	}
    
    /**
	 * 根据产品代码获取持仓人员
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/getCustByFundcode.do")
	public Map<String, Object> getCustByFundcode(HttpServletRequest request) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>(2);
		String fundcode = request.getParameter("fundcode");
		Map<String,Object> params = new HashMap<>(1);
		params.put("fundcode", fundcode);
		List<Map<String,Object>> listcust = custprivatefundService.listCustByHoldFund(params);
		List<Map<String,String>> custList = new ArrayList<>();
		Map<String, String> custMap = new HashMap<>(2);
		custMap.put("id", "");
		custMap.put("text", "请选择");
		custList.add(custMap);
		if(listcust != null && listcust.size() > 0){
			for(Map<String,Object> cust : listcust){
				String idno = StringUtil.replaceNullStr(cust.get("IDNO_MASK"));
				Map<String, String> newcustMap = new HashMap<>(2);
				newcustMap.put("id", StringUtil.replaceNullStr(cust.get("CONSCUSTNO")));
				newcustMap.put("text", StringUtil.replaceNullStr(cust.get("CUSTNAME"))+" "+StringUtil.replaceNullStr(cust.get("CONSCUSTNO"))+" "+idno);
				custList.add(newcustMap);
			}
		}
		map.put("custList", custList);
        return map;
	}
}