package com.howbuy.crm.hb.web.controller.callout;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acc.common.utils.MaskUtil;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.encrypt.EncryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;

import com.howbuy.crm.hb.outersevice.CallStatOuterService;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.howbuy.crm.hb.domain.callout.CsCalloutRec;
import com.howbuy.crm.hb.service.callout.CsCalloutRecService;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.CmPhoneAreaService;
import com.howbuy.crm.hb.service.custinfo.PhoneAreaService;
import com.howbuy.crm.hb.web.controller.doubletrade.SendCallClient;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.core.domain.PageCmConsultant;
import com.howbuy.crm.util.CCDESUtil;
import com.howbuy.crm.util.PhoneUtil;

import crm.howbuy.base.constants.Constants;

/**
 * 拨号请求处理类
 */
@Slf4j
@Controller
@RequestMapping("/callout")
public class CallPhoneController  {
	
	@Value("${SOFTPHONE_ADDRESS_SLT}")
	private String softphone_address_slt;
	
	@Autowired
	private CmPhoneAreaService phoneAreaService;
	
	@Autowired
	private CommonService commonService;
	
	@Autowired
	private SendCallClient sendCallClient;
	
	@Autowired
	private CsCalloutRecService csCalloutRecService;

	@Autowired
	private DecryptSingleFacade decryptSingleFacade;
	
	@Autowired
	private EncryptSingleFacade encryptSingleFacade;

	@Autowired
	private CallStatOuterService callStatOuterService;

	// 拨号通道（RHPT：睿狐普通，SLT：商路通）
	private final static String DIAL_CHANNEL_RHPT = "RHPT";
	private final static String DIAL_CHANNEL_SLT = "SLT";
	
	/**
	 * 呼叫路由控制
	 * @param request
	 * @param response
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/callPhone.do")
	public String callPhone(HttpServletRequest request, HttpServletResponse response) throws Exception {
		// 登陆用户检测
		String consCode = (String) request.getSession().getAttribute("userId");
		if (StringUtils.isBlank(consCode)) {
			return "无法获取登录用户信息，请点退出重新登录！";
		}

		// 从缓存中获取投顾信息
		Map<String, PageCmConsultant> consDetailMap = ConsOrgCache.getInstance().getConsDetailMap();
		if (consDetailMap != null && consDetailMap.containsKey(consCode)) {
			PageCmConsultant cmConsultant = consDetailMap.get(consCode);
			String callIp = cmConsultant.getIp();
			String workplace = cmConsultant.getWorkplace();
			String extNo = cmConsultant.getTelno();

			// 根据投顾信息选择拨号通道
			if (CallPhoneController.DIAL_CHANNEL_SLT.equals(cmConsultant.getDialchannel())) {// 如果是商路通，则走商路通通道拨号
				return callPhoneBySynroute(request, consCode, callIp, workplace);
			} else if (CallPhoneController.DIAL_CHANNEL_RHPT.equals(cmConsultant.getDialchannel())) {// 如果是睿狐普通，则走睿狐普通通道拨号
				return callPhoneByFox(request, consCode, workplace, extNo);
			} else {// 默认走睿狐普通通道拨号
				return callPhoneByFox(request, consCode, workplace, extNo);
			}
		} else {
			return "缓存中未找到该投顾信息！";
		}
		
    }
	
	/**
	 * 使用商路通通道进行拨号
	 * @param request
	 * @return String
	 */
	public String callPhoneBySynroute(HttpServletRequest request, String consCode, String callIp, String workplace) throws Exception{
		// 校验拨号信息
		Map<String, String> retMap = validateInfo(request, consCode);
		if (retMap != null && retMap.containsKey("errorMsg")) {
			return retMap.get("errorMsg");
		}

		// 判断IP是否存在
		if (StringUtils.isBlank(callIp)) {
			return "ip地址为空或配置有误！";
		}
		
		// 获取客户手机号
		String custPhoneNumber = null;
		if (retMap != null && retMap.containsKey("custPhoneNumber")) {
			custPhoneNumber = retMap.get("custPhoneNumber");
		}

		// 获取客户手机号码归属地
		String phoneArea = null;
		if (retMap != null && retMap.containsKey("phoneArea")) {
			phoneArea = retMap.get("phoneArea");
		}

		CloseableHttpClient httpclient = null;
		CloseableHttpResponse httpResponse = null;
		try {
			// 向电话交换机发送呼叫请求
			httpclient = HttpClients.createDefault();
			String url = softphone_address_slt + "?strCalled=" + PhoneAreaService.formatPhoneNumber(custPhoneNumber, phoneArea, workplace) + "&strClientIP=" + callIp;
			log.info("========"+url);
			HttpGet httpGet = new HttpGet(url);
			httpResponse = httpclient.execute(httpGet);
			if (httpResponse != null && httpResponse.getStatusLine() != null) {
				if (httpResponse.getStatusLine().getStatusCode() == 200) {
					log.info("========request url is ok!");
					return "拨号连接成功，请接听电话！";
				} else {
					log.info("========response status error：" + httpResponse.getStatusLine().getStatusCode());
					return "软拨号服务器响应异常：错误码：" + httpResponse.getStatusLine().getStatusCode();
				}
			} else {
				log.info("========response value is null");
				return "软拨号服务器无响应，返回值为空";
			}
		} catch (Exception e) {
			log.error(e.getMessage());
			return "电话交换机通讯异常，请稍后再试！";
		} finally {
			try {
				if (httpResponse != null) {
					httpResponse.close();
				}
				if (httpclient != null) {
					httpclient.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		
	}

	/**
	 * 使用睿狐通道进行拨号
	 * @param request
	 * @return String
	 */
	public String callPhoneByFox(HttpServletRequest request, String consCode, String workplace, String extNo) throws Exception {
		// 校验拨号信息
		Map<String, String> retMap = validateInfo(request, consCode);
		if (retMap != null && retMap.containsKey("errorMsg")) {
			return retMap.get("errorMsg");
		}

		// 获取客户手机号码
		String custPhoneNumber = null;
		if (retMap != null && retMap.containsKey("custPhoneNumber")) {
			custPhoneNumber = retMap.get("custPhoneNumber");
		}
		
		// 获取客户手机号码归属地
		String phoneArea = null;
		if (retMap != null && retMap.containsKey("phoneArea")) {
			phoneArea = retMap.get("phoneArea");
		}
				
		// 分机号判断
		if (StringUtils.isNotBlank(extNo) && extNo.indexOf("-") > 0) {
			extNo = extNo.substring(extNo.lastIndexOf("-") + 1).trim();
		}else{
			return "请正确配置您的座机号码，格式为\"区号-电话号码-分机号\"！";
		}

		// 工作区域判断
		if (StringUtils.isBlank(workplace)) {
			return "您当前设置的所属工作区域不支持软拨号，请修改您的所属工作区域(浦东、虹口或者北京)！";
		}
		
		// 唯一key(分机号+当前时间戳)
		String key = extNo + System.currentTimeMillis();
		
		// 放入队列缓存
		callStatOuterService.addCall(key);
		
		// 拨号参数设置
		String callParam = "";
		String recId = commonService.getSeqValue("SEQ_CS_CALLOUT_REC_ID");
		callParam = extNo + ";" + PhoneAreaService.formatPhoneNumber(custPhoneNumber, phoneArea, workplace) + ";" + key + ";" + recId;
		
		// 记录呼叫流水
		String tid = request.getParameter("tid");
		String waitId = request.getParameter("waitId");
		CsCalloutRec csCalloutRec = new CsCalloutRec();
		csCalloutRec.setId(recId);
		csCalloutRec.setConscode(consCode);
		csCalloutRec.setExtNo(extNo);
		csCalloutRec.setMobile(custPhoneNumber);
		if(StringUtils.isNotBlank(custPhoneNumber)){
			csCalloutRec.setMobileDigest(DigestUtil.digest(custPhoneNumber.trim()));
			csCalloutRec.setMobileMask(MaskUtil.maskMobile(custPhoneNumber.trim()));
			csCalloutRec.setMobileCipher(encryptSingleFacade.encrypt(custPhoneNumber.trim()).getCodecText());
		}
		if (StringUtils.isNotBlank(tid) && tid.length() > 0) {
			csCalloutRec.setTid(tid);
		} else if(StringUtils.isNotBlank(waitId) && waitId.length() > 0) {
			csCalloutRec.setWaitId(waitId);
		}
		
		try {
			csCalloutRecService.insertCsCalloutRec(csCalloutRec);
			sendCallClient.send(workplace, callParam);
		} catch (Exception e) {
			log.error(e.getMessage());
			return "电话交换机通讯异常，请稍后再试！";
		}

		// 在缓存队列中检测拨号状态，5秒内返回并删除队列
		long begin = System.currentTimeMillis();
		long end = begin;
		String callStat = Constants.CALL_STAT_READY;
		do {
			Thread.sleep(200);
			callStat = callStatOuterService.getState(key);
			end = System.currentTimeMillis();
		} while (end - begin < 5000	&& Constants.CALL_STAT_READY.equals(callStat));

		// 队列中删除
		callStatOuterService.removeCall(key);

		if (Constants.CALL_STAT_READY.equals(callStat)) {
			return "拨号成功，请接听电话！";
		} else {
			return "拨号失败，请稍后再试！";
		}
	}
	
	/**
	 *  拨号参数校验
	 */
	public Map<String, String> validateInfo(HttpServletRequest request, String consCode) {
		Map<String, String> retMap = new HashMap<String, String>();
		String custPhoneNumber = null;
		String mobile = request.getParameter("mobile");
		if (StringUtils.isBlank(mobile)) {
			retMap.put("errorMsg", "客户电话号码为空！");
			return retMap;
		}

		//解密出明文
		CodecSingleResponse codecSingleResponse = decryptSingleFacade.decrypt(mobile);
		if(codecSingleResponse != null && codecSingleResponse.getCodecText() != null) {
			mobile = codecSingleResponse.getCodecText();
		}
		// 超过十五位，界面肯定 通过加密才传过来
		if (mobile.length() <= 15) {
			custPhoneNumber = mobile;
		} else {
			try {
				custPhoneNumber = CCDESUtil.decrypt(mobile, "mymobile");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		// 特殊符号过滤
		if (StringUtils.isNotBlank(custPhoneNumber)) {
			custPhoneNumber = custPhoneNumber.replaceAll("-", "").replaceAll(" ", "");
		}

		// 客户电话号码格式判断
		if (!PhoneUtil.isNumber(custPhoneNumber) || custPhoneNumber.length() < 8) {
			retMap.put("errorMsg", "电话号码长度或格式有误！");
			return retMap;
		}
		
		// 手机还是固话判断
		if (PhoneUtil.validateMobile(custPhoneNumber)) {
			Map<String, String> resultMap = phoneAreaService.getProvCityByMobile(custPhoneNumber, consCode);
			String phoneArea = resultMap.get("provinceName");
			retMap.put("phoneArea", phoneArea);
		} else {
			// 固话号码超过八位，验证区号+号码 格式是否正确,如果等于八位，默认为未加区号的上海号码
			if (custPhoneNumber.length() > 8) {// 验证区号+固话格式是否正确
				if (!PhoneUtil.validateFixedPhone(custPhoneNumber)) {
					retMap.put("errorMsg", "电话号码格式有误");
					return retMap;
				}
			}
		}

		retMap.put("custPhoneNumber", custPhoneNumber);
		return retMap;

	}

}
