/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.web.controller.custinfo;

import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.account.client.utils.IdTypeUtil;
import com.howbuy.crm.hb.domain.conscust.ConscustVO;
import com.howbuy.crm.hb.domain.hkconscust.HkConscustVO;
import com.howbuy.crm.hb.service.hkconscust.HkConscustService;
import com.howbuy.crm.hb.service.outerservice.CrmAccountOuterService;
import com.howbuy.crm.hb.web.dto.hkconscust.QueryHkCustInfoVO;
import com.howbuy.crm.page.cache.ConsOrgCache;
import crm.howbuy.base.db.CommPageBean;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.ListUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/1/12 16:05
 * @since JDK 1.8
 */
@Slf4j
@Controller
@RequestMapping(value = "/hkCust")
public class CmHkCustController {

    @Autowired
    private CrmAccountOuterService crmAccountOuterService;

    @Autowired
    private HkConscustService hkConscustService;

    private final String IS_SAME = "1";
    private final String NOT_SAME = "0";

    /**
     * @api {GET} /hkCust/list.do list()
     * @apiVersion 1.0.0
     * @apiGroup CmHkCustController
     * @apiName list()
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":false,"status":"NOT_MODIFIED"}
     */
    @GetMapping("/list.do")
    public ModelAndView list(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();

        modelAndView.setViewName("/custinfo/hkCustList");
        return modelAndView;
    }


    /**
     * @api {POST} /hkCust/custList custList()
     * @apiVersion 1.0.0
     * @apiGroup CmHkCustController
     * @apiName custList()
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * {}
     */
    @ResponseBody
    @PostMapping("/custList")
    public Map<String, Object> custList(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("total", 0);
        resultMap.put("rows", new ArrayList<>());

        String consCustNo = request.getParameter("consCustNo");
        String hboneIsSameFlag = request.getParameter("hboneIsSameFlag");

        String hkCustName = request.getParameter("hkCustName");
        String hkCustNO = request.getParameter("hkCustNO");
        String eBrokerId = request.getParameter("eBrokerId");

        String hkInvstType = request.getParameter("hkInvstType");
        String hkCustState = request.getParameter("hkCustState");
        String hkOpenType = request.getParameter("hkOpenType");
        String openDateStart = request.getParameter("openDateStart");
        String openDateEnd = request.getParameter("openDateEnd");
        String hkMobile = request.getParameter("hkMobile");
        String hkIdNo = request.getParameter("hkIdNo");

        if (StringUtils.isNotBlank(consCustNo)) {
            HkConscustVO hkConscustVO = crmAccountOuterService.queryHkCustInfoByCustNo(consCustNo);
            if (hkConscustVO == null || StringUtils.isBlank(hkConscustVO.getHkTxAcctNo())) {
                return resultMap;
            } else if (StringUtils.isNotBlank(hkCustNO) && !hkCustNO.equals(hkConscustVO.getHkTxAcctNo())) {
                return resultMap;
            } else {
                hkCustNO = hkConscustVO.getHkTxAcctNo();
            }
        }

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("hkCustName", StringUtils.isNotBlank(hkCustName) ? hkCustName : null);
        paramMap.put("hkTxAcctNo", StringUtils.isNotBlank(hkCustNO) ? hkCustNO : null);
        paramMap.put("eBrokerId", StringUtils.isNotBlank(eBrokerId) ? eBrokerId : null);
        paramMap.put("invstType", StringUtils.isNotBlank(hkInvstType) ? hkInvstType : null);
        paramMap.put("hkCustStatus", StringUtils.isNotBlank(hkCustState) ? hkCustState : null);
        paramMap.put("hkOpenType", StringUtils.isNotBlank(hkOpenType) ? hkOpenType : null);
        paramMap.put("openDateStart", StringUtils.isNotBlank(openDateStart) ? openDateStart : null);
        paramMap.put("openDateEnd", StringUtils.isNotBlank(openDateEnd) ? openDateEnd : null);
        paramMap.put("mobileDigest", StringUtils.isNotBlank(hkMobile) ? DigestUtil.digest(hkMobile) : null);
        paramMap.put("idNoDigest", StringUtils.isNotBlank(hkIdNo) ? DigestUtil.digest(hkIdNo) : null);

        Integer page = Integer.valueOf(request.getParameter("page"));
        Integer rows = Integer.valueOf(request.getParameter("rows"));

        PageData<HkConscustVO> hkConscustVOPageData = getHkConscustVOPageData(paramMap, hboneIsSameFlag, page, rows);

        if (CollectionUtils.isEmpty(hkConscustVOPageData.getListData())) {
            return resultMap;
        }
        List<HkConscustVO> listData = hkConscustVOPageData.getListData();

        // listData中过滤到hkTxAcctNo为空的数据，并map成hkTxAcctNo的list
        List<String> hkTxAcctNoList = listData.stream().filter(hkConscustVO -> StringUtils.isNotBlank(hkConscustVO.getHkTxAcctNo()))
                .map(HkConscustVO::getHkTxAcctNo).collect(Collectors.toList());

        Map<String, ConscustVO> conscustVOAndHkTxAcctNoMap = new HashMap<>();
        List<ConscustVO> conscustVOS = hkConscustService.listHkConscustByHkTxAcctNoList(hkTxAcctNoList);
        if (CollectionUtils.isNotEmpty(conscustVOS)) {
            conscustVOS.forEach(conscustVO -> conscustVOAndHkTxAcctNoMap.put(conscustVO.getHkTxAcctNo(), conscustVO));

        }
        List<QueryHkCustInfoVO> queryHkCustInfoVOS = new ArrayList<>(listData.size());
        for (HkConscustVO hkConscustVO : listData) {
            QueryHkCustInfoVO queryHkCustInfoVO = new QueryHkCustInfoVO();
            queryHkCustInfoVO.setHkCustName(StringUtils.isNotBlank(hkConscustVO.getUsedName())
                    ? hkConscustVO.getUsedName() : "--");
            queryHkCustInfoVO.setHkCustNo(hkConscustVO.getHkTxAcctNo());
            queryHkCustInfoVO.setEbrokerID(hkConscustVO.getEbrokerId());
            queryHkCustInfoVO.setHkCustState(hkConscustVO.getHkCustStatus());
            queryHkCustInfoVO.setHkOpenType(hkConscustVO.getOpenType());
            queryHkCustInfoVO.setHkOpenDate(hkConscustVO.getOpenDate());
            queryHkCustInfoVO.setHkCustRelateHbone(StringUtils.isNotBlank(hkConscustVO.getHboneNo()) ? hkConscustVO.getHboneNo() : "");
            // 香港手机号(格式 = 手机区号 + 手机掩码)
            queryHkCustInfoVO.setMobileMask(hkConscustVO.getMobileAreaCode() + " " + hkConscustVO.getMobileMask());
            // 证件类型 翻译
            queryHkCustInfoVO.setIdTypeDesc(IdTypeUtil.getIdTypeDesc(hkConscustVO.getInvstType(), hkConscustVO.getIdType()));
            queryHkCustInfoVO.setIdNoMask(hkConscustVO.getIdNoMask());

            if (conscustVOAndHkTxAcctNoMap.containsKey(hkConscustVO.getHkTxAcctNo())) {
                ConscustVO conscustVO = conscustVOAndHkTxAcctNoMap.get(hkConscustVO.getHkTxAcctNo());
                queryHkCustInfoVO.setConsCustNo(conscustVO.getConscustno());
                queryHkCustInfoVO.setConsCustRelateHbone(StringUtils.isNotBlank(conscustVO.getHboneno()) ? conscustVO.getHboneno() : "");

                if (StringUtils.isNotBlank(conscustVO.getConscode())) {
                    String consCode = conscustVO.getConscode();
                    ConsOrgCache orgcache = ConsOrgCache.getInstance();
                    queryHkCustInfoVO.setOrgName(orgcache.getAllOrgMap().get(orgcache.getCons2OutletMap().get(consCode)));
                    String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(consCode));
                    if ("0".equals(uporgcode)) {
                        queryHkCustInfoVO.setUpOrgName(conscustVO.getOrgname());
                    } else {
                        queryHkCustInfoVO.setUpOrgName(orgcache.getAllOrgMap().get(uporgcode));
                    }
                }
            }
            queryHkCustInfoVOS.add(queryHkCustInfoVO);
        }

        resultMap.put("total", hkConscustVOPageData.getPageBean().getTotalNum());
        resultMap.put("rows", queryHkCustInfoVOS);
        return resultMap;
    }

    /**
     * @description:(请在此添加描述)
     * @param paramMap
     * @param hboneIsSameFlag
     * @param page
     * @param rows
     * @return crm.howbuy.base.db.PageData<com.howbuy.crm.hb.domain.hkconscust.HkConscustVO>
     * @author: jin.wang03
     * @date: 2024/1/15 17:34
     * @since JDK 1.8
     */
    private PageData<HkConscustVO> getHkConscustVOPageData(Map<String, String> paramMap, String hboneIsSameFlag,
                                                           Integer page, Integer rows) {
        // 查询出符合条件的所有结果
        CommPageBean commPageBean = new CommPageBean();
        commPageBean.setCurPage(1);
        int pageSize = 200;
        commPageBean.setPageSize(pageSize);

        PageData<HkConscustVO> hkConscustVOPageData = crmAccountOuterService.queryHkCustInfoPage(paramMap, commPageBean);
        if (CollectionUtils.isEmpty(hkConscustVOPageData.getListData())) {
            return new PageData<>();
        }

        long totalNum = hkConscustVOPageData.getPageBean().getTotalNum();
        // 根据 totalNum 和 rows 计算出总页数
        int totalPage = (int) (totalNum % pageSize == 0 ? totalNum / pageSize : totalNum / pageSize + 1);
        // 查询出符合条件的所有结果
        for (int i = 2; i <= totalPage; i++) {
            commPageBean.setCurPage(i);
            hkConscustVOPageData.getListData().addAll(crmAccountOuterService.queryHkCustInfoPage(paramMap, commPageBean).getListData());
        }

        List<HkConscustVO> hkConscustVOList = hkConscustVOPageData.getListData();

        // 根据listData中的香港客户号，查询出对应的投顾客户号
        List<String> hkTxAcctNoList = hkConscustVOList.stream().map(HkConscustVO::getHkTxAcctNo)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<List<String>> hkTxAcctSplitList = ListUtil.splitList(hkTxAcctNoList);
        List<ConscustVO> conscustVOS = new ArrayList<>();
        for (List<String> list : hkTxAcctSplitList) {
            conscustVOS.addAll(hkConscustService.listConsCustNoAndHboneNoByHkTxAcctNoList(list));
        }

        // 得出listData中 香港客户号 和 投顾客户号 的对应关系
        Map<String, String> hkTxAcctNoAndConsCustNoMap = new HashMap<>();
        // 得出listData中 投顾客户号 和 一账通号 的对应关系
        Map<String, String> consCustNoAndHboneNoMap = new HashMap<>();
        for (ConscustVO conscustVO : conscustVOS) {
            hkTxAcctNoAndConsCustNoMap.put(conscustVO.getHkTxAcctNo(), conscustVO.getConscustno());
            consCustNoAndHboneNoMap.put(conscustVO.getConscustno(), conscustVO.getHboneno());
        }

        if (NOT_SAME.equals(hboneIsSameFlag)) {
            hkConscustVOList = hkConscustVOList.stream()
                    .filter(hkConscustVO -> hkTxAcctNoAndConsCustNoMap.containsKey(hkConscustVO.getHkTxAcctNo())
                            && !StringUtils.equals(hkConscustVO.getHboneNo(), consCustNoAndHboneNoMap.get(hkTxAcctNoAndConsCustNoMap.get(hkConscustVO.getHkTxAcctNo()))
                    )).collect(Collectors.toList());
        } else if (IS_SAME.equals(hboneIsSameFlag)) {
            hkConscustVOList = hkConscustVOList.stream()
                    .filter(hkConscustVO -> hkTxAcctNoAndConsCustNoMap.containsKey(hkConscustVO.getHkTxAcctNo())
                            && StringUtils.equals(hkConscustVO.getHboneNo(), consCustNoAndHboneNoMap.get(hkTxAcctNoAndConsCustNoMap.get(hkConscustVO.getHkTxAcctNo()))
                    )).collect(Collectors.toList());
        }

        // 排序：①有投顾客户号的放前面；②按香港客户号倒序，大号放前面
        List<HkConscustVO> hkConscustWithConsCustNoList = new ArrayList<>();
        List<HkConscustVO> hkConscustWithoutConsCustNoVOList = new ArrayList<>();
        for (HkConscustVO listDatum : hkConscustVOList) {
            if (hkTxAcctNoAndConsCustNoMap.containsKey(listDatum.getHkTxAcctNo())) {
                hkConscustWithConsCustNoList.add(listDatum);
            } else {
                hkConscustWithoutConsCustNoVOList.add(listDatum);
            }
        }
        hkConscustVOList.clear();
        // hkConscustWithConsCustNoList根据香港客户号倒序，大号放前面
        hkConscustWithConsCustNoList.sort((o1, o2) -> {
            if (Long.parseLong(o1.getHkTxAcctNo()) > Long.parseLong(o2.getHkTxAcctNo())) {
                return -1;
            } else if (Long.parseLong(o1.getHkTxAcctNo()) < Long.parseLong(o2.getHkTxAcctNo())) {
                return 1;
            } else {
                return 0;
            }
        });
        // hkConscustWithoutConsCustNoVOList根据香港客户号倒序，大号放前面
        hkConscustWithoutConsCustNoVOList.sort((o1, o2) -> {
            if (Long.parseLong(o1.getHkTxAcctNo()) > Long.parseLong(o2.getHkTxAcctNo())) {
                return -1;
            } else if (Long.parseLong(o1.getHkTxAcctNo()) < Long.parseLong(o2.getHkTxAcctNo())) {
                return 1;
            } else {
                return 0;
            }
        });
        hkConscustWithConsCustNoList.addAll(hkConscustWithoutConsCustNoVOList);

        hkConscustVOPageData.getPageBean().setTotalNum(hkConscustWithConsCustNoList.size());
        List<HkConscustVO> collect = hkConscustWithConsCustNoList.stream().skip((long) (page - 1) * rows).limit(rows).collect(Collectors.toList());

        hkConscustVOPageData.setListData(collect);
        return hkConscustVOPageData;
    }


}