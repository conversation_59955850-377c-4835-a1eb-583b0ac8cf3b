package com.howbuy.crm.hb.web.controller.insur;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.howbuy.crm.hb.domain.insur.CmBxChannel;
import com.howbuy.crm.hb.domain.insur.CmBxProduct;
import com.howbuy.crm.hb.domain.insur.CmBxProductCoeff;
import com.howbuy.crm.hb.domain.insur.CmBxProductCoeffRatio;
import com.howbuy.crm.hb.domain.insur.CmBxProductGroup;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.insur.CmBxChannelService;
import com.howbuy.crm.hb.service.insur.CmBxProductCoeffRatioService;
import com.howbuy.crm.hb.service.insur.CmBxProductCoeffService;
import com.howbuy.crm.hb.service.insur.CmBxProductGroupService;
import com.howbuy.crm.hb.service.insur.CmBxProductService;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;

/**
 * <AUTHOR>
 * @Description: Controller
 * @version 1.0
 */
@Controller
@RequestMapping(value = "/cmBxProductCoeff")
@Slf4j
public class CmBxProductCoeffController {
	@Autowired
	private CmBxProductCoeffService cmBxProductCoeffService;
	
	@Autowired
	private CmBxProductCoeffRatioService cmBxProductCoeffRatioService;
	
	@Autowired
	private CmBxProductService cmBxProductService;
	
	@Autowired
	private CmBxProductGroupService cmBxProductGroupService;
	
	@Autowired
    private CommonService commonService;
	
	@Autowired
	private CmBxChannelService cmBxChannelService;
	
	@RequestMapping("/listCmBxProductCoeff.do")
	public ModelAndView listCmBxProductCoeff(HttpServletRequest request)	throws Exception {
		String fundcode = request.getParameter("fundcode");	
		Map<String, String> param = new ParamUtil(request).getParamMap();
		param.put("isdel", "1");
		param.put("fundcode", fundcode);
		List<CmBxProductCoeff> list = cmBxProductCoeffService.listCmBxProductCoeff(param);
		if(CollectionUtils.isNotEmpty(list)){
			// 对枚举字段进行转义
			ConstantCache constantCache= ConstantCache.getInstance();
	    	for(CmBxProductCoeff model : list){
	    		model.setPayyearsname(StringUtils.isBlank(model.getPayyears())  ?  null : constantCache.getVal("insurpayyears", model.getPayyears()));
	    		model.setEnsureyearsname(StringUtils.isBlank(model.getEnsureyears())  ?  null : constantCache.getVal("insurensureyears", model.getEnsureyears()));
	    		if(StringUtils.isNotBlank(model.getChanncode())){
	    			Map<String,String> paramchannel = new HashMap<String,String>(2);
		    		paramchannel.put("isdel", "1");
		    		paramchannel.put("channcode", model.getChanncode());
		            CmBxChannel channel = cmBxChannelService.getCmBxChannel(paramchannel);
		            model.setChannname(channel != null ? channel.getChannname() : null);
	    		}
	    	}
		}
		// 返回查询结果
		ModelAndView modelAndView = new ModelAndView();
		//modelAndView.addObject(resultMap);
		modelAndView.addObject("list", list);
		modelAndView.addObject("fundcode", fundcode);
		modelAndView.setViewName("/insur/listCmBxProductCoeff");
		return modelAndView;
	}
	
	@RequestMapping("/editCmBxProductCoeffView.do")
	public ModelAndView editCmBxProductCoeffView(HttpServletRequest request)	throws Exception {
		Map<String,String> fundMap = null;
		Map<String,String> channelMap = null;
		Map<String,String> payyearsMap = null;
		Map<String,String> ensureyearsMap = null;
		List<Map<String,String>> fundMapList = new ArrayList<Map<String,String>> ();
		List<Map<String,String>> channelMapList = new ArrayList<Map<String,String>> ();
		List<Map<String,String>> payyearsMapList = new ArrayList<Map<String,String>> ();
		List<Map<String,String>> ensureyearsMapList = new ArrayList<Map<String,String>> ();
		
		String fundcode = request.getParameter("fundcode");
		Map<String, String> param  = new HashMap<String,String> (2);
		//1.产品
		param.put("isdel", "1");
		param.put("fundcode", fundcode);
		CmBxProduct product = cmBxProductService.getCmBxProduct(param);
		if(product != null){
			//主险
			if("1".equals(product.getProdproper())){
				fundMap = new HashMap<String,String> (2);
				fundMap.put("detailfundname", product.getFundname());
				fundMap.put("detailfundcode", product.getFundcode());
				fundMapList.add(fundMap);
				
				List<CmBxProductGroup> listCmBxProductGroup = cmBxProductGroupService.listCmBxProductGroupMsg(param);
				if(CollectionUtils.isNotEmpty(listCmBxProductGroup)){
					for( CmBxProductGroup group : listCmBxProductGroup){
						fundMap = new HashMap<String,String> (2);
						fundMap.put("detailfundname", group.getAttfundname());
						fundMap.put("detailfundcode", group.getAttfundcode());
						fundMapList.add(fundMap);
					}
				}
			}else{
				fundMap = new HashMap<String,String> (2);
				fundMap.put("detailfundname", product.getFundname());
				fundMap.put("detailfundcode", fundcode);
				fundMapList.add(fundMap);
			}
		} 
		
		//2.合作渠道
		List<CmBxChannel> listCmBxChannel = cmBxChannelService.listCmBxChannelByFundcode(param);
		if(CollectionUtils.isNotEmpty(listCmBxChannel)){
			for( CmBxChannel channel : listCmBxChannel){
				channelMap = new HashMap<String,String> (2);
				channelMap.put("channname", channel.getChannname());
				channelMap.put("channcode", channel.getChanncode());
				channelMapList.add(channelMap);
			}
		}
		
		
		//3.缴费年限
        LinkedHashMap<String, String> insurpayyears = ConstantCache.getInstance().getConstantKeyVal("insurpayyears");
        for(Map.Entry<String, String> entry : insurpayyears.entrySet()){
        	payyearsMap = new HashMap<String,String> (2);
        	payyearsMap.put("payyearsname", entry.getValue());
        	payyearsMap.put("payyears", entry.getKey());
        	payyearsMapList.add(payyearsMap);
        }
		
		//4.保障期限
		LinkedHashMap<String, String> insurensureyears = ConstantCache.getInstance().getConstantKeyVal("insurensureyears");
        for(String code : insurensureyears.keySet()){
        	ensureyearsMap = new HashMap<String,String> (2);
        	ensureyearsMap.put("ensureyearsname", insurensureyears.get(code));
        	ensureyearsMap.put("ensureyears", code);
        	ensureyearsMapList.add(ensureyearsMap);
        }
		
		// 返回查询结果
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.addObject("fundcode", request.getParameter("fundcode"));
		modelAndView.addObject("fundMapList", fundMapList);
		modelAndView.addObject("channelMapList", channelMapList);
		modelAndView.addObject("payyearsMapList", payyearsMapList);
		modelAndView.addObject("ensureyearsMapList", ensureyearsMapList);
		modelAndView.setViewName("/insur/editCmBxProductCoeff");
		return modelAndView;
	}
	
	/**
	 * 加载列表页面数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listCmBxProductCoeffViewData")
	public Map<String, Object> listCmBxProductCoeffViewData(HttpServletRequest request)	throws Exception {
		String fundcode = request.getParameter("fundcode");
		String detailfundcode = request.getParameter("detailfundcode");	
		Map<String, String> param = new ParamUtil(request).getParamMap();
		param.put("isdel", "1");
		param.put("fundcode", fundcode);
		param.put("detailfundcode", detailfundcode);
		List<CmBxProductCoeff> list = cmBxProductCoeffService.listCmBxProductCoeff(param);
	    if(CollectionUtils.isNotEmpty(list)){
	    	// 对枚举字段进行转义
	    	ConstantCache constantCache= ConstantCache.getInstance();	    	
	    	for(CmBxProductCoeff model : list){
	    		String signbegdt = model.getSignbegdt();
	    		String signenddt = model.getSignenddt();
	    		String  passbegdt =  model.getPassbegdt();
	    		String passenddt = model.getPassenddt();
	    		if(StringUtils.isNotBlank(signbegdt)){
	    			model.setSignbegdt(signbegdt.substring(0, 4) + "-" + signbegdt.substring(4, 6) + "-" + signbegdt.substring(6, 8) );
	    		}
	    		if(StringUtils.isNotBlank(signenddt)){
	    			model.setSignenddt(signenddt.substring(0, 4) + "-" + signenddt.substring(4, 6) + "-" + signenddt.substring(6, 8) );
	    		}
	    		if(StringUtils.isNotBlank(passbegdt)){
	    			model.setPassbegdt(passbegdt.substring(0, 4) + "-" + passbegdt.substring(4, 6) + "-" + passbegdt.substring(6, 8) );
	    		}
                if(StringUtils.isNotBlank(passenddt)){
                	model.setPassenddt(passenddt.substring(0, 4) + "-" + passenddt.substring(4, 6) + "-" + passenddt.substring(6, 8) );
	    		}
                
	    		model.setPayyearsname(StringUtils.isBlank(model.getPayyears())  ?  null : constantCache.getVal("insurpayyears", model.getPayyears()));
	    		model.setEnsureyearsname(StringUtils.isBlank(model.getEnsureyears())  ?  null : constantCache.getVal("insurensureyears", model.getEnsureyears()));
	    		if(StringUtils.isNotBlank(model.getChanncode())){
	    			Map<String,String> paramchannel = new HashMap<String,String>(2);
		    		paramchannel.put("isdel", "1");
		    		paramchannel.put("channcode", model.getChanncode());
		            CmBxChannel channel = cmBxChannelService.getCmBxChannel(paramchannel);
		            model.setChannname(channel != null ? channel.getChannname() : null);
	    		}
	    	}
	    }

		// 返回查询结果
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		resultMap.put("total", list.size());
		resultMap.put("rows", list);
		
		return resultMap;
	}
	
	@RequestMapping(value = "/saveCmBxProductCoeff", method = RequestMethod.POST)
	@ResponseBody
	public Map<String, Object> saveCmBxProductCoeff(HttpServletRequest request) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		resultMap.put("errorMsg", "操作成功");
	    resultMap.put("errorCode", "0000");
		Map<String, String> param = new ParamUtil(request).getParamMap();
		param.put("isdel", "1");
		String signbegdt = StringUtils.isNotBlank(request.getParameter("signbegdt")) ? request.getParameter("signbegdt").replace("-", "") : null;
	    String signenddt = StringUtils.isNotBlank(request.getParameter("signenddt")) ? request.getParameter("signenddt").replace("-", "") : null;  
	    String passbegdt = StringUtils.isNotBlank(request.getParameter("passbegdt")) ? request.getParameter("passbegdt").replace("-", "") : null;
	    String passenddt = StringUtils.isNotBlank(request.getParameter("passenddt")) ? request.getParameter("passenddt").replace("-", "") : null;
	    param.put("signbegdt", signbegdt);
	    param.put("signenddt", signenddt);
	    param.put("passbegdt", passbegdt);
	    param.put("passenddt", passenddt);
	    String id = request.getParameter("id");
	    if("undefined".equals(id) || "null".equals(id)){
	    	id = null;
	    }
	    param.put("id", null);
		CmBxProductCoeff coeff = cmBxProductCoeffService.getCmBxProductCoeffSupNull(param);
		if(coeff != null ){
			if(StringUtils.isBlank(id) || (StringUtils.isNotBlank(id) && !coeff.getId().toString().equals(id))){
				resultMap.put("errorMsg", "当前系数中有重复数据，请检查!");
			    resultMap.put("errorCode", "9999");
			    return resultMap;
			}else{
				return resultMap;
			}
		}
		
		User user = (User)request.getSession().getAttribute("loginUser");
		
	    String fundcode = request.getParameter("fundcode");	
	    String detailfundcode = request.getParameter("detailfundcode");		
	    String channcode = request.getParameter("channcode");	
	    String payyears = request.getParameter("payyears");	
	    String ensureyears = request.getParameter("ensureyears");	
	    String yearamk = request.getParameter("yearamk");
	    String yearamkend = request.getParameter("yearamkend");
	    String procoe = request.getParameter("procoe");	
		
		CmBxProductCoeff bean = new CmBxProductCoeff();
		bean.setFundcode(fundcode);
		bean.setDetailfundcode(detailfundcode);
		bean.setSignbegdt(StringUtils.isNotBlank(signbegdt) ? signbegdt.replace("-", "") : null);
		bean.setSignenddt(StringUtils.isNotBlank(signenddt) ? signenddt.replace("-", "") : null  );
		bean.setPassbegdt(StringUtils.isNotBlank(passbegdt) ? passbegdt.replace("-", "") : null );
		bean.setPassenddt(StringUtils.isNotBlank(passenddt) ? passenddt.replace("-", "") : null );
		bean.setChanncode(channcode);
		bean.setPayyears(payyears);
		bean.setEnsureyears(ensureyears);
		bean.setModifier(user.getUserId());
		bean.setYearamk(StringUtils.isBlank(yearamk) ? null :  new BigDecimal(yearamk));
		bean.setYearamkend(StringUtils.isBlank(yearamkend) ? null :  new BigDecimal(yearamkend));
		bean.setProcoe(StringUtils.isBlank(procoe) ? null :  new BigDecimal(procoe));
		bean.setIsdel("1");
		
		//修改
		if(StringUtils.isNotBlank(id)){
			bean.setId(new BigDecimal(id));
			bean.setModifydt(new Date());
			bean.setModifier(user.getUserId());
			cmBxProductCoeffService.updateCmBxProductCoeffNull(bean);
		//新增
		}else{
			param.clear();
			//1.产品
			param.put("isdel", "1");
			param.put("fundcode", fundcode);
			CmBxProduct product = cmBxProductService.getCmBxProduct(param);
			
			bean.setId(new BigDecimal(commonService.getSeqValue("SEQ_INSUR_ID")));
			bean.setCreator(user.getUserId());
			bean.setCreatdt(new Date());
			bean.setIsspecialhandle( product == null ? null : product.getIsspecialhandle() );
			cmBxProductCoeffService.insertCmBxProductCoeff(bean);
		}

		return resultMap;
	}
	
	/**
	 * 删除
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/delCmBxProductCoeff", method = RequestMethod.POST)
	@ResponseBody
    public Map<String, Object> delCmBxProductCoeff(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        User user = (User) request.getSession().getAttribute("loginUser");
        String id = request.getParameter("id");

        if (StringUtils.isNotEmpty(id)) {
        	CmBxProductCoeff bean = new CmBxProductCoeff();
        	bean.setId(new BigDecimal(id));
        	bean.setIsdel("0");
        	bean.setModifier(user.getUserId());
        	bean.setModifydt(new Date());
        	cmBxProductCoeffService.updateCmBxProductCoeff(bean);
        }
        return resultMap;
    }
	
	@ResponseBody
    @RequestMapping("/setCoeffRatio.do")
    public ModelAndView setCoeffRatio(HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
		String id = request.getParameter("id");
		String ratiotype = request.getParameter("ratiotype");
        Map<String,Object> map = new HashMap<String,Object>(2);
        map.put("id", id);
        map.put("ratiotype", ratiotype);
        return new ModelAndView("insur/setProductCoeffRatio", "map", map);
    }
	
	@ResponseBody
	@RequestMapping("/listProductCoeffRatio")
	public Map<String, Object> listProductCoeffRatio(HttpServletRequest request)	throws Exception {
		String coeffid = request.getParameter("coeffid");	
		String ratiotype = request.getParameter("ratiotype");
		Map<String, Object> param = new HashMap<>(3);
		param.put("isdel", StaticVar.INSUR_ISDEL_NO);
		param.put("coeffid", coeffid);
		param.put("ratiotype", ratiotype);
		List<CmBxProductCoeffRatio> list = cmBxProductCoeffRatioService.listCmBxProductCoeffRatio(param);
	    // 返回查询结果
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		resultMap.put("total", list.size());
		resultMap.put("rows", list);
		return resultMap;
	}
	
	@RequestMapping(value = "/saveCmBxProductCoeffRatio", method = RequestMethod.POST)
	@ResponseBody
	public Map<String, Object> saveCmBxProductCoeffRatio(HttpServletRequest request) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		
		String id = request.getParameter("id");
		String year = request.getParameter("year");
		String ratiotype = request.getParameter("ratiotype");
	    if("undefined".equals(id) || "null".equals(id)){
	    	id = null;
	    }
	    String coeffratio = request.getParameter("coeffratio");
	    String coeffratioone = request.getParameter("coeffratioone");
	    String coeffratiotwo = request.getParameter("coeffratiotwo");
	    String coeffid = request.getParameter("coeffid");
	    User user = (User) request.getSession().getAttribute("loginUser");
		CmBxProductCoeffRatio bean = new CmBxProductCoeffRatio();
		//根据coeffid、ratiotype、year查询是否存在
		Map<String, Object> queryp = new HashMap<>();
		queryp.put("coeffid", coeffid);
		queryp.put("ratiotype", ratiotype);
		queryp.put("year", new BigDecimal(year));
		queryp.put("isdel", StaticVar.ISDEL_YES);
		List<CmBxProductCoeffRatio> listq = cmBxProductCoeffRatioService.listCmBxProductCoeffRatio(queryp);
		if(listq != null && listq.size() > 0){
			if(StringUtils.isNotBlank(id)){
				CmBxProductCoeffRatio cratio = listq.get(0);
				if(cratio.getId().compareTo(new BigDecimal(id)) != 0){
					resultMap.put("errorMsg", "已存在该年份数据!");
				    resultMap.put("errorCode", "9999");
					return resultMap;
				}
			}else{
				resultMap.put("errorMsg", "已存在该年份数据!");
			    resultMap.put("errorCode", "9999");
				return resultMap;
			}
		}
		
		//修改
		if(StringUtils.isNotBlank(id)){
			bean.setId(new BigDecimal(id));
			bean.setCoeffid(new BigDecimal(coeffid));
			bean.setYear(new BigDecimal(year));
			bean.setRatio(new BigDecimal(coeffratio));
			if(StringUtil.isNotNullStr(coeffratioone)){
				bean.setRatioone(new BigDecimal(coeffratioone));
			}else{
				bean.setRatioone(null);
			}
			if(StringUtil.isNotNullStr(coeffratiotwo)){
				bean.setRatiotwo(new BigDecimal(coeffratiotwo));
			}else{
				bean.setRatiotwo(null);
			}
			bean.setModifydt(new Date());
			bean.setModifier(user.getUserId());
			cmBxProductCoeffRatioService.updateCmBxProductCoeffRatio(bean);
		//新增
		}else{
			bean.setId(new BigDecimal(commonService.getSeqValue("SEQ_INSUR_ID")));
			bean.setCoeffid(new BigDecimal(coeffid));
			bean.setYear(new BigDecimal(year));
			bean.setRatio(new BigDecimal(coeffratio));
			if(StringUtil.isNotNullStr(coeffratioone)){
				bean.setRatioone(new BigDecimal(coeffratioone));
			}
			if(StringUtil.isNotNullStr(coeffratiotwo)){
				bean.setRatiotwo(new BigDecimal(coeffratiotwo));
			}
			bean.setRatiotype(ratiotype);
			bean.setIsdel(StaticVar.INSUR_ISDEL_NO);
			bean.setCreator(user.getUserId());
			bean.setCreatdt(new Date());
			cmBxProductCoeffRatioService.insertCmBxProductCoeffRatio(bean);
		}
		resultMap.put("errorMsg", "操作成功");
	    resultMap.put("errorCode", "0000");
		return resultMap;
	}
	
	/**
	 * 删除
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/delCmBxProductCoeffRatio", method = RequestMethod.POST)
	@ResponseBody
    public Map<String, Object> delCmBxProductCoeffRatio(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        User user = (User) request.getSession().getAttribute("loginUser");
        String id = request.getParameter("id");

        if (StringUtils.isNotEmpty(id)) {
        	CmBxProductCoeffRatio bean = new CmBxProductCoeffRatio();
        	bean.setId(new BigDecimal(id));
        	bean.setIsdel(StaticVar.INSUR_ISDEL_YES);
        	bean.setModifier(user.getUserId());
        	bean.setModifydt(new Date());
        	cmBxProductCoeffRatioService.updateCmBxProductCoeffRatio(bean);
        }
        return resultMap;
    }
	
	@RequestMapping("/listCmBxProductCoeffRatio.do")
	public ModelAndView listCmBxProductCoeffRatio(HttpServletRequest request)	throws Exception {
		String coeffid = request.getParameter("id");	
		String ratiotype = request.getParameter("ratiotype");
		Map<String, Object> param = new HashMap<String,Object>(3);
		param.put("isdel", StaticVar.INSUR_ISDEL_NO);
		param.put("coeffid", coeffid);
		param.put("ratiotype", ratiotype);
		List<CmBxProductCoeffRatio> list = cmBxProductCoeffRatioService.listCmBxProductCoeffRatio(param);
		// 返回查询结果
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.addObject("list", list);
		if("1".equals(ratiotype)){
			modelAndView.setViewName("/insur/listCmBxProductGsCoeffRatio");
		}else{
			modelAndView.setViewName("/insur/listCmBxProductCoeffRatio");
		}
		return modelAndView;
	}
	
	@RequestMapping("/addCopyProCoeffRatio.do")
    @ResponseBody
    public String addCopyProCoeffRatio(HttpServletRequest request){
    	String result="";
        String id = request.getParameter("id");
        String startyears = request.getParameter("startyears");
        String endyears = request.getParameter("endyears");
        User userlogin = (User) request.getSession().getAttribute("loginUser");
      //根据产品代码查询有效的产品
        Map<String,String> paramquery = new HashMap<String,String>(1);
        paramquery.put("id", id);
        CmBxProductCoeff coeff = cmBxProductCoeffService.getCmBxProductCoeff(paramquery);
        //根据查询到的记录找出符合条件的所有缴费年限不为空的系数设置
        if(coeff != null) {
        	Map<String,String> paramcoeff = new HashMap<String,String>(4);
        	paramcoeff.put("fundcode", coeff.getFundcode());
        	paramcoeff.put("detailfundcode", coeff.getDetailfundcode());
        	paramcoeff.put("startyears", startyears);
        	if(StringUtil.isNotNullStr(endyears)) {
        		paramcoeff.put("endyears", endyears);
        	}
        	List<String> listpayyears = cmBxProductCoeffService.listNotPayyearsProductCoeff(paramcoeff);
    		List<CmBxProductCoeff> insertlist = new ArrayList<CmBxProductCoeff>();
    		if(listpayyears != null && listpayyears.size() > 0){
    			Map<String, String> param = new HashMap<String, String>(8);
    			//1.产品
    			param.put("isdel", "1");
    			param.put("fundcode", coeff.getFundcode());
    			CmBxProduct product = cmBxProductService.getCmBxProduct(param);
    			for(String payyear : listpayyears){
    				String inid = commonService.getSeqValue("SEQ_INSUR_ID");
    				CmBxProductCoeff incoeff = new CmBxProductCoeff();
    				BeanUtils.copyProperties(coeff,incoeff);
    				incoeff.setId(new BigDecimal(inid));
    				incoeff.setPayyears(payyear);
    				incoeff.setCreator(userlogin.getUserId());
    				incoeff.setCreatdt(new Date());
    				incoeff.setModifier(userlogin.getUserId());
    				incoeff.setModifydt(new Date());
    				incoeff.setIsspecialhandle(product == null ? null : product.getIsspecialhandle());
    				insertlist.add(incoeff);
    			}
    		}
    		cmBxProductCoeffService.copyCmBxProductCoeff(insertlist);
        }else{
        	result = "参数错误";
        }
		result = "success";
        return result;
    }
	
	
	
	@RequestMapping("/cmBxProductCoeffList.do")
	public ModelAndView qrymBxProductCoeff(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();        
        modelAndView.setViewName("/insur/cmBxProductCoeffList");
        return modelAndView;
	}
	
	/**
	 * 加载列表页面数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/cmBxProductCoeffListByPage.do")
	public Map<String, Object> cmBxProductCoeffListByPage(HttpServletRequest request)	throws Exception {
		Map<String, String> param = new ParamUtil(request).getParamMap();
		param.put("isdel", "1");
		PageData<CmBxProductCoeff> pageData = cmBxProductCoeffService.listCmBxProductCoeffByPage(param);
		List<CmBxProductCoeff> list = pageData.getListData();
		if(CollectionUtils.isNotEmpty(list)){
			// 对枚举字段进行转义
			ConstantCache constantCache= ConstantCache.getInstance();
	    	for(CmBxProductCoeff model : list){
	    		model.setPayyearsname(StringUtils.isBlank(model.getPayyears())  ?  null : constantCache.getVal("insurpayyears", model.getPayyears()));
	    		model.setEnsureyearsname(StringUtils.isBlank(model.getEnsureyears())  ?  null : constantCache.getVal("insurensureyears", model.getEnsureyears()));
	    		if(StringUtils.isNotBlank(model.getChanncode())){
	    			Map<String,String> paramchannel = new HashMap<String,String>(2);
		    		paramchannel.put("isdel", "1");
		    		paramchannel.put("channcode", model.getChanncode());
		            CmBxChannel channel = cmBxChannelService.getCmBxChannel(paramchannel);
		            model.setChannname(channel != null ? channel.getChannname() : null);
	    		}
	    	}
		}

		// 返回查询结果
		Map<String, Object> resultMap = new HashMap<String, Object>(1);
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmBxProductCoeff> listdata = pageData.getListData();
		resultMap.put("rows", listdata);
		
		return resultMap;
	}
	
	
	/**
	 * 导出操作
	 * @param request
	 * @param response
	 * @throws Exception
	 */
    @RequestMapping("/exportCmBxProductCoeff.do")
    public void exportCmBxProductCoeff(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
    	Map<String, String> param = new ParamUtil(request).getParamMap();
    	param.put("isdel", StaticVar.INSUR_ISDEL_NO);
    	List<CmBxProductCoeff> listist = cmBxProductCoeffService.exportCmBxProductCoeff(param);
    	List<CmBxProductCoeff> exportList = new ArrayList<>();
    	int count = 0;
    	if(CollectionUtils.isNotEmpty(listist)){
    		ConstantCache constantCache= ConstantCache.getInstance();
			LinkedHashMap<String, String> insurpayyearsMap = constantCache.getConstantKeyVal("insurpayyears");
			LinkedHashMap<String, String> insurensureyearsMap = constantCache.getConstantKeyVal("insurensureyears");
			LinkedHashMap<String, String> isSpecialHandleMap = constantCache.getConstantKeyVal("isSpecialHandle");
			for (CmBxProductCoeff info : listist) {
    			Map<String,Object> paramratio = new HashMap<String,Object>(3);
    			paramratio.put("isdel", StaticVar.INSUR_ISDEL_NO);
    			paramratio.put("coeffid", info.getId());
    			paramratio.put("ratiotype", "2");
    			List<CmBxProductCoeffRatio> listTgRatio = cmBxProductCoeffRatioService.listCmBxProductCoeffRatio(paramratio);
    			paramratio.put("ratiotype", "1");
    			List<CmBxProductCoeffRatio> listGsRatio = cmBxProductCoeffRatioService.listCmBxProductCoeffRatio(paramratio);
    			info.setPayyearsname(StringUtils.isBlank(info.getPayyears())  ?  null : insurpayyearsMap.get(info.getPayyears()));
        		info.setEnsureyearsname(StringUtils.isBlank(info.getEnsureyears())  ?  null : insurensureyearsMap.get(info.getEnsureyears()));
        		info.setIsspecialhandle(StringUtils.isBlank(info.getIsspecialhandle())  ?  null : isSpecialHandleMap.get(info.getIsspecialhandle()));
        		if(StringUtils.isNotBlank(info.getChanncode())){
        			Map<String,String> paramchannel = new HashMap<String,String>(2);
    	    		paramchannel.put("isdel", StaticVar.INSUR_ISDEL_NO);
    	    		paramchannel.put("channcode", info.getChanncode());
    	            CmBxChannel channel = cmBxChannelService.getCmBxChannel(paramchannel);
    	            info.setChannname(channel != null ? channel.getChannname() : null);
        		}
        		List<CmBxProductCoeffRatio> listRatio = getallRatio(listTgRatio,listGsRatio);
        		
        		if(listRatio != null && listRatio.size() > 0){
        			for(CmBxProductCoeffRatio ratio : listRatio){
        				exportList.add(changeExportObj(info,String.valueOf(ratio.getYear()),ratio.getGsratio(),ratio.getTgratio(),new BigDecimal(++count)));
        			}
        		}else{
        			exportList.add(changeExportObj(info,"",null,null,new BigDecimal(++count)));
        		}
        	}
    	}
    	
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("产品系数导出.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String [] columnName = new String []{"序号","产品代码","产品名称","签单日期起始","签单日期结束","核保通过起始日","核保通过结束日","合作渠道",
					"缴费年限(年)","保障年限(年)","年缴金额(起始)","年缴金额(结束)","折标系数","年份","公司佣金率","投顾佣金率","是否特殊处理"};

            String [] beanProperty = new String []{"id","detailfundcode","detailfundname","signbegdt","signenddt","passbegdt",
					"passenddt","channname","payyears","ensureyears","yearamk","yearamkend","procoe","yearnum","gsproratio","proratio","isspecialhandle"};
            ExcelWriter.writeExcel(os, "产品系数", 0, exportList, columnName, beanProperty);
            os.close(); // 关闭流
        } catch (Exception e) {
            log.error("产品系数导出异常", e);
        }
    	
    }
    
    private CmBxProductCoeff changeExportObj(CmBxProductCoeff obj,String yearnum,BigDecimal gsproratio,BigDecimal proratio,BigDecimal count){
    	CmBxProductCoeff info = new CmBxProductCoeff();
    	BeanUtils.copyProperties(obj,info);
    	info.setId(count);
    	info.setYearnum(yearnum);
    	info.setGsproratio(gsproratio);
    	info.setProratio(proratio);
    	return info;
    }
    
    /**
     * 根据投顾佣金率和公司佣金率拼出完整的年份对应的投顾和公司佣金率
     * @param listTgRatio
     * @param listGsRatio
     * @return
     */
    private List<CmBxProductCoeffRatio> getallRatio(List<CmBxProductCoeffRatio> listTgRatio,List<CmBxProductCoeffRatio> listGsRatio){
    	List<CmBxProductCoeffRatio> listRatio = new ArrayList<>();
    	List<BigDecimal> years = new ArrayList<>();
    	for(CmBxProductCoeffRatio ratio : listTgRatio){
    		years.add(ratio.getYear());
    		ratio.setTgratio(ratio.getRatio());
    		listRatio.add(ratio);
    	}
    	for(CmBxProductCoeffRatio ratio : listGsRatio){
    		//公司佣金率的年份不存在在投顾佣金率中
    		if(!years.contains(ratio.getYear())){
    			years.add(ratio.getYear());
    			ratio.setGsratio(ratio.getRatio());
        		listRatio.add(ratio);
    		}else{
    			//找出该年份的记录，放入公司佣金率
    			for(CmBxProductCoeffRatio allratio : listRatio){
    				if(allratio.getYear().compareTo(ratio.getYear()) == 0){
    					allratio.setGsratio(ratio.getRatio());
    				}
    			}
    		}
    	}
    	//按年份升序排
    	listRatio.sort((o1,o2) -> o1.getYear().compareTo(o2.getYear()));
    	return listRatio;
    }
    
    
    
    /**
	 * 修改是否特殊处理
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/saveIsspecialhandle.do", method = RequestMethod.POST)
	@ResponseBody
    public String saveIsspecialhandle(HttpServletRequest request) {
		String rsgString = "success";

        User user = (User) request.getSession().getAttribute("loginUser");
        String id = request.getParameter("id");
        String isspecialhandle = request.getParameter("isspecialhandle");

        if (StringUtils.isNotEmpty(id)) {
        	CmBxProductCoeff bean = new CmBxProductCoeff();
        	bean.setId(new BigDecimal(id));
        	bean.setIsspecialhandle(isspecialhandle);
        	bean.setModifier(user.getUserId());
        	bean.setModifydt(new Date());
        	cmBxProductCoeffService.updateCmBxProductCoeff(bean);
        }
        return rsgString;
    }
}
