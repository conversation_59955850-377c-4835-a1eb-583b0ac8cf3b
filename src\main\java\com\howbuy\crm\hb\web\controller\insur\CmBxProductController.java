package com.howbuy.crm.hb.web.controller.insur;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.howbuy.crm.hb.domain.insur.CmBxChannel;
import com.howbuy.crm.hb.domain.insur.CmBxCompany;
import com.howbuy.crm.hb.domain.insur.CmBxPrebookinfo;
import com.howbuy.crm.hb.domain.insur.CmBxProduct;
import com.howbuy.crm.hb.domain.insur.CmBxProductChannel;
import com.howbuy.crm.hb.domain.insur.CmBxProductGroup;
import com.howbuy.crm.hb.domain.insur.InsurFundCode;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.insur.CmBxChannelService;
import com.howbuy.crm.hb.service.insur.CmBxCompanyService;
import com.howbuy.crm.hb.service.insur.CmBxPrebookinfoService;
import com.howbuy.crm.hb.service.insur.CmBxProductChannelService;
import com.howbuy.crm.hb.service.insur.CmBxProductGroupService;
import com.howbuy.crm.hb.service.insur.CmBxProductService;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
/**
 * @Description: Controller
 * <AUTHOR> @version 1.0
 * @created 
 */
@Slf4j
@Controller
@RequestMapping(value = "/insur")
public class CmBxProductController {

	@Autowired
	private CmBxProductService cmBxProductService;
	
	@Autowired
	private CmBxChannelService cmBxChannelService;

	@Autowired
	private CmBxCompanyService cmBxCompanyService;
	
	@Autowired
	private CommonService commonService;
	
	@Autowired
	private CmBxProductChannelService cmBxProductChannelService;
	
	@Autowired
	private CmBxProductGroupService cmBxProductGroupService;
	
	@Autowired
	private CmBxPrebookinfoService cmBxPrebookinfoService;
	

	/**
	 * 跳转到产品预约管理页面方法
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listinsurproduct.do")
	public ModelAndView listinsurproduct(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/insur/listinsurproduct");
		return modelAndView;
	}

	/**
	 * 加载页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listInsurProductByPage_json.do")
	public Map<String, Object> listInsurProductByPageJson(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String, String> param = new ParamUtil(request).getParamMap();
		PageData<CmBxProduct> pageData = cmBxProductService.listCmBxProductByPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmBxProduct> listdata = pageData.getListData();
		for (CmBxProduct info : listdata) {
			info.setBusitype(ConstantCache.getInstance().getVal("insurbusitype", info.getBusitype()));
			info.setProdtype(ConstantCache.getInstance().getVal("insurprodtype", info.getProdtype()));
			info.setProdproper(ConstantCache.getInstance().getVal("insurprodproper", info.getProdproper()));
			info.setIstpcheck(ConstantCache.getInstance().getVal("insuristpcheck", info.getIstpcheck()));
			info.setIsspecialhandle(ConstantCache.getInstance().getVal("isSpecialHandle", info.getIsspecialhandle()));
			if(StringUtil.isNotNullStr(info.getCurrency())){
				String [] listcurs = info.getCurrency().split(",");
				StringBuilder sb = new StringBuilder();
				for(String curr : listcurs){
					sb.append(","+ConstantCache.getInstance().getVal("currencys", curr));
				}
				info.setCurrency(sb.toString().replaceFirst(",", ""));
			}
		}
		resultMap.put("rows", listdata);
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping("/autoCompleteInsurFund.do")
	public Map<String, List<InsurFundCode>> autoCompleteInsurFund(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>(2);
		String searchParam = request.getParameter("term");
		String prodproper = request.getParameter("prodproper");
		param.put("prodproper", prodproper);
		param.put("searchParam", searchParam.toUpperCase());
		List<CmBxProduct> listbx = cmBxProductService.listCmBxProduct(param);
		List<InsurFundCode> list = new ArrayList<InsurFundCode>();
		for(CmBxProduct info : listbx){
			InsurFundCode fc = new InsurFundCode();
			fc.setCode(Util.ObjectToString(info.getFundcode()));
			fc.setName(Util.ObjectToString(info.getFundname()));
			list.add(fc);
		}
		Map<String,List<InsurFundCode>> result = new HashMap<String,List<InsurFundCode>>(1);
		result.put("result", list);
		return result;
	}
	
	@ResponseBody
    @RequestMapping("/addInsurProduct.do")
    public ModelAndView addInsurProduct(HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
        
        Map<String,Object> map = new HashMap<String,Object>(4);
        //查询合作渠道
        Map<String,String> paramother = new HashMap<String,String>(1);
        paramother.put("isdel", StaticVar.INSUR_ISDEL_NO);
        List<CmBxChannel> listchann = cmBxChannelService.listCmBxChannel(paramother);
        List<Map<String,Object>> channList = new ArrayList<>();
        if(listchann != null && listchann.size() > 0){
        	for(CmBxChannel chann : listchann ){
        		Map<String, Object> channmap = new HashMap<>(2);
        		channmap.put("id", chann.getChanncode());
        		channmap.put("text", chann.getChannname());
    	        channList.add(channmap);
        	}
        }
        map.put("channList",channList);
        //查询保险公司
        List<CmBxCompany> listcomp = cmBxCompanyService.listCmBxCompany(paramother);
        List<Map<String,Object>> compList = new ArrayList<>();
        if(listcomp != null && listcomp.size() > 0){
        	for(CmBxCompany comp : listcomp){
        		Map<String, Object> compmap = new HashMap<>(2);
        		compmap.put("id", comp.getCompcode());
        		compmap.put("text", comp.getCompname());
                compList.add(compmap);
        	}
        }
        map.put("compList",compList);
        //查询币种
        LinkedHashMap<String, String> mapcache = ConstantCache.getInstance().getConstantKeyVal("currencys");
        List<Map<String,Object>> currList = new ArrayList<>();
        for(Map.Entry<String, String> entry : mapcache.entrySet()){
        	Map<String, Object> curr = new HashMap<>(2);
        	curr.put("id", entry.getKey());
        	curr.put("text", entry.getValue());
        	currList.add(curr);
        }
        map.put("currList", currList);
        //查询附加保险
        Map<String,String> parambx = new HashMap<String,String>(2);
        parambx.put("isdel", StaticVar.INSUR_ISDEL_NO);
        parambx.put("prodproper", StaticVar.INSUR_PROPER_ATTR);
        List<CmBxProduct> listproduct = cmBxProductService.listCmBxProduct(parambx);
        List<Map<String,Object>> attrList = new ArrayList<>();
        if(listproduct != null && listproduct.size() > 0){
        	for(CmBxProduct prod :listproduct){
        		Map<String, Object> prodmap = new HashMap<>(2);
        		prodmap.put("id", prod.getFundcode());
        		prodmap.put("text", prod.getFundname());
        		attrList.add(prodmap);
        	}
        }
        map.put("attrList", attrList);
        return new ModelAndView("insur/addinsurproduct", "map", map);
    }
	
	@ResponseBody
    @RequestMapping("/editInsurProduct.do")
    public ModelAndView editInsurProduct(HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
		String id = request.getParameter("id");
        Map<String,Object> map = new HashMap<String,Object>(4);
        //根据产品代码查询有效的产品
        Map<String,String> paramquery = new HashMap<String,String>(1);
        paramquery.put("id", id);
        CmBxProduct product = cmBxProductService.getCmBxProduct(paramquery);
        map.put("product", product);
        if(product != null){
        	paramquery.clear();
        	paramquery.put("fundcode", product.getFundcode());
        }
        //根据产品查询产品和合作渠道关系
        List<CmBxProductChannel> listchanns = cmBxProductChannelService.listCmBxProductChannel(paramquery);
        StringBuilder sbchanns = new StringBuilder();
        for(CmBxProductChannel chann : listchanns){
        	sbchanns.append(","+chann.getChanncode());
        }
        if(StringUtil.isNotNullStr(sbchanns.toString())){
        	map.put("haschanns", sbchanns.substring(1).toString());
        }else{
        	map.put("haschanns", "");
        }
        //根据产品查询产品和附加险关系
        List<CmBxProductGroup> listattrs = cmBxProductGroupService.listCmBxProductGroup(paramquery);
        map.put("listattrs", listattrs);
        if(listattrs != null && listattrs.size() > 0){
        	map.put("attrflag", true);
        }else{
        	map.put("attrflag", false);
        }
        //查询合作渠道
        Map<String,String> paramother = new HashMap<String,String>(1);
        paramother.put("isdel", StaticVar.INSUR_ISDEL_NO);
        List<CmBxChannel> listchann = cmBxChannelService.listCmBxChannel(paramother);
        List<Map<String,Object>> channList = new ArrayList<>();
        if(listchann != null && listchann.size() > 0){
        	for(CmBxChannel chann : listchann ){
        		Map<String, Object> channmap = new HashMap<>(2);
        		channmap.put("id", chann.getChanncode());
        		channmap.put("text", chann.getChannname());
    	        channList.add(channmap);
        	}
        }
        map.put("channList",channList);
        //查询保险公司
        List<CmBxCompany> listcomp = cmBxCompanyService.listCmBxCompany(paramother);
        List<Map<String,Object>> compList = new ArrayList<>();
        if(listcomp != null && listcomp.size() > 0){
        	for(CmBxCompany comp : listcomp){
        		Map<String, Object> compmap = new HashMap<>(2);
        		compmap.put("id", comp.getCompcode());
        		compmap.put("text", comp.getCompname());
                compList.add(compmap);
        	}
        }
        map.put("compList",compList);
        //查询币种
        LinkedHashMap<String, String> mapcache = ConstantCache.getInstance().getConstantKeyVal("currencys");
        List<Map<String,Object>> currList = new ArrayList<>();
        for(Map.Entry<String, String> entry : mapcache.entrySet()){
        	Map<String, Object> curr = new HashMap<>(2);
        	curr.put("id", entry.getKey());
        	curr.put("text", entry.getValue());
        	currList.add(curr);
        }
        map.put("currList", currList);
        //查询附加保险
        Map<String,String> parambx = new HashMap<String,String>(2);
        parambx.put("isdel", StaticVar.INSUR_ISDEL_NO);
        parambx.put("prodproper", StaticVar.INSUR_PROPER_ATTR);
        List<CmBxProduct> listproduct = cmBxProductService.listCmBxProduct(parambx);
        List<Map<String,Object>> attrList = new ArrayList<>();
        if(listproduct != null && listproduct.size() > 0){
        	for(CmBxProduct prod :listproduct){
        		Map<String, Object> prodmap = new HashMap<>(2);
        		prodmap.put("id", prod.getFundcode());
        		prodmap.put("text", prod.getFundname());
        		attrList.add(prodmap);
        	}
        }
        map.put("attrList", attrList);
        return new ModelAndView("insur/editinsurproduct", "map", map);
    }
	
	@ResponseBody
	@RequestMapping("/saveInsurProduct.do")
	public String saveInsurProduct(HttpServletRequest request) throws Exception {

		String result="";
		User user = (User) request.getSession().getAttribute("loginUser");
		String fundcode = request.getParameter("fundcode");
		String[] channs = request.getParameterValues("channs[]");
		String istpcheck = request.getParameter("istpcheck");
		String isspecialhandle = request.getParameter("isspecialhandle");
		CmBxProduct prod = new CmBxProduct();
		//查询产品代码是产品名称是否存在
		Map<String,String> paramquery = new HashMap<String,String>(2);
		paramquery.put("fundcode", fundcode);
		paramquery.put("isdel", StaticVar.INSUR_ISDEL_NO);
		List<CmBxProduct> listqcode = cmBxProductService.listCmBxProduct(paramquery);
		//产品存在，并且是编辑处理，就取第一条
		prod = listqcode.get(0);
		prod.setModifier(user.getUserId());
		prod.setModifydt(new Date());
		prod.setIstpcheck(istpcheck);
		prod.setIsspecialhandle(isspecialhandle);
		List<CmBxProductChannel> listpc = new ArrayList<CmBxProductChannel>();
		//处理合作渠道
		for(String chann : channs){
			if(StringUtil.isNotNullStr(chann)){
				CmBxProductChannel pc = new CmBxProductChannel();
				String pcid = commonService.getSeqValue("SEQ_INSUR_ID");
				pc.setId(new BigDecimal(pcid));
				pc.setFundcode(fundcode);
				pc.setChanncode(chann);
				pc.setCreator(user.getUserId());
				listpc.add(pc);
			}
		}
		cmBxProductService.updateCmBxProduct(prod, listpc);
		result="success";
		return result;
	}
	
	@RequestMapping("/delProduct.do")
    @ResponseBody
    public String delProduct(HttpServletRequest request){
    	String result="";
        String id = request.getParameter("id");
        User userlogin = (User) request.getSession().getAttribute("loginUser");
      //根据产品代码查询有效的产品
        Map<String,String> paramquery = new HashMap<String,String>(1);
        paramquery.put("id", id);
        CmBxProduct product = cmBxProductService.getCmBxProduct(paramquery);
        if(product == null){
            return "产品不存在";
        }
        Map<String,String> paramq = new HashMap<String,String>(1);
        if(!StaticVar.INSUR_PROPER_MAIN.equals(product.getProdproper())){
        	//非主险，检查是否有关联的正常的主险，如果有就不能删除
        	paramq.put("attfundcode", product.getFundcode());
        	List<CmBxProductGroup> listgroup = cmBxProductGroupService.listCmBxProductGroup(paramq);
        	if(listgroup != null && listgroup.size() > 0){
        		return "当前附加险已关联到主险，请先取消关联再删除!";
        	}
        }else{
        	//主险的情况检查是否有已经使用的预约，如果有就不能删除
        	paramq.put("fundcode", product.getFundcode());
        	paramq.put("prestates", " ('"+StaticVar.INSUR_PRESTATE_NOTCONFORM+"','"+StaticVar.INSUR_PRESTATE_CONFORM+"') ");
        	List<CmBxPrebookinfo> listpre = cmBxPrebookinfoService.listCmBxPrebookinfo(paramq);
        	if(listpre != null && listpre.size() > 0){
        		return "当前产品已关联到预约，不能删除!";
        	}
        }
        product.setIsdel(StaticVar.INSUR_ISDEL_YES);
		product.setModifier(userlogin.getUserId());
		product.setModifydt(new Date());
		cmBxProductService.updateCmBxProduct(product);
		result = "success";
        return result;
    }

}