package com.howbuy.crm.hb.web.controller.doubletrade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Joiner;
import com.howbuy.crm.hb.domain.doubletrade.CmVisitRecord;
import com.howbuy.crm.hb.service.doubletrade.CmVisitRecordService;
import com.howbuy.crm.hb.service.doubletrade.DoubleTradeExportService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2020/5/11 15:37
 */
@Slf4j
@Controller
@RequestMapping("/doubletrade")
public class CmVisitRecordController {

    @Autowired
    private CmVisitRecordService cmVisitRecordService;

    @RequestMapping("/visitRecordList.html")
    public String visitRecordList(Map map){
        List<Map> list = new ArrayList<>();
        Map<String,String> param = new HashMap<String,String>();
        param.put("menuCode", "050304");//新规回访记录
        param.put("operaCode", "04");//新规任务
        List<Map<String,String>> visitorList = cmVisitRecordService.listUserByAuth(param);
        for (Map<String,String> visitor : visitorList) {
            Map visitMap = new HashMap();
            visitMap.put("id", visitor.get("CONSCODE"));
            visitMap.put("text", visitor.get("CONSNAME"));
            list.add(visitMap);
        }
        map.put("visitorList", list);
        return "/doubletrade/visitRecordList";
    }

    @RequestMapping("/visitRecordList_json.do")
    @ResponseBody
    public Object visitRecordList_json(HttpServletRequest request) throws Exception {
        Map<String,String> param = new ParamUtil(request).getParamMap();
        PageData<CmVisitRecord> pageData = cmVisitRecordService.listVisitRecordByPage(param);
        if(pageData != null){
            ConstantCache constantCache = ConstantCache.getInstance();
            for(CmVisitRecord cmVisitRecord : pageData.getListData()){
                if(cmVisitRecord.getVisitResult() != null){
                    cmVisitRecord.setVisitResult(constantCache.getVal("visitResult", cmVisitRecord.getVisitResult()));
                }
                if(cmVisitRecord.getVisitProblem() != null){
                    cmVisitRecord.setVisitProblem(constantCache.getVal("visitProblem", cmVisitRecord.getVisitProblem()));
                }
                if(cmVisitRecord.getVisitMan() != null){
                    cmVisitRecord.setVisitMan(ConsOrgCache.getInstance().getAllUserMap().get(cmVisitRecord.getVisitMan()));
                }
                if(StringUtil.isNotNullStr(cmVisitRecord.getRecstate())){
                	cmVisitRecord.setRecstate(constantCache.getVal("recstate", cmVisitRecord.getRecstate()));
                }

                ConsOrgCache orgcache = ConsOrgCache.getInstance();
                String uporgcode = orgcache.getUpOrgMapCache().get(cmVisitRecord.getOrgCode());
                if("0".equals(uporgcode)){
                    cmVisitRecord.setUpOrgName(cmVisitRecord.getOrgName());
                }else{
                    cmVisitRecord.setUpOrgName(orgcache.getAllOrgMap().get(uporgcode));
                }
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        resultMap.put("rows", pageData.getListData());
        return resultMap;
    }

    @RequestMapping("/getHisComm")
    @ResponseBody
    public Object getHisComm(String commRecordIds){
        return cmVisitRecordService.getHisCommRecord(commRecordIds);
    }


    /**
     * 新规回访记录导出:
    * <AUTHOR>
    * @date 2020/5/13
    */
    @ResponseBody
    @RequestMapping("/exportVisitRecordExcel.do")
    public Map<String, Object> exportVisitRecordExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        DoubleTradeExportService exportService = new DoubleTradeExportService();
        try {
            String visitRecordListStr = request.getParameter("visitRecordList");
            JSONArray jsonArray = JSON.parseArray(visitRecordListStr);
            List<CmVisitRecord> visitRecordList = jsonArray.toJavaList(CmVisitRecord.class);
            if(visitRecordList != null){
                for (CmVisitRecord cmVisitRecord : visitRecordList) {
                    if(StringUtil.isNotNullStr(cmVisitRecord.getCommRecordIds())){
                        List<String> commRecordList = cmVisitRecordService.getHisCommRecord(cmVisitRecord.getCommRecordIds());
                        cmVisitRecord.setCommRecord(Joiner.on(";").join(commRecordList));
                    }
                }
                exportService.exportVisitRecordByList(response, visitRecordList);
            } else {
                resultMap.put("msg", "dataError");
                return resultMap;
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
		resultMap.put("msg", "success");
        return resultMap;
    }
    
    //检查全量导出的数据符不符合要求
  	@ResponseBody
  	@RequestMapping("/checkexportAllVisitRecordExcel.do")
  	public String checkexportAllVisitRecordExcel(HttpServletRequest request) throws Exception {
  		String result = "success";
  		// 获取选中的编号
  		Map<String,String> param = new ParamUtil(request).getParamMap();
  		List<CmVisitRecord> visitRecordList = cmVisitRecordService.listVisitRecord(param);
  		if(visitRecordList == null ){
  			result = "请先查询出需导出的数据";
  		}else if(visitRecordList != null && visitRecordList.size() == 0){
  			result = "请先查询出需导出的数据";
  		}else if(visitRecordList != null && visitRecordList.size() > 10000){
  			result = "数据量过大，请重新查询数据导出";
  		}
  	    return result;
  	}
    
    /**
     * 新规回访记录全量导出:
    * <AUTHOR>
    * @date 2020/6/17
    */
    @ResponseBody
    @RequestMapping("/exportAllVisitRecordExcel.do")
    public Map<String, Object> exportAllVisitRecordExcel(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String,String> param = new ParamUtil(request).getParamMap();
        DoubleTradeExportService exportService = new DoubleTradeExportService();
        try {
            List<CmVisitRecord> visitRecordList = cmVisitRecordService.listVisitRecord(param);
            if(visitRecordList != null){
            	ConstantCache constantCache = ConstantCache.getInstance();
                for (CmVisitRecord cmVisitRecord : visitRecordList) {
                    if(cmVisitRecord.getVisitResult() != null){
                        cmVisitRecord.setVisitResult(constantCache.getVal("visitResult", cmVisitRecord.getVisitResult()));
                    }
                    if(cmVisitRecord.getVisitProblem() != null){
                        cmVisitRecord.setVisitProblem(constantCache.getVal("visitProblem", cmVisitRecord.getVisitProblem()));
                    }
                    if(cmVisitRecord.getVisitMan() != null){
                        cmVisitRecord.setVisitMan(ConsOrgCache.getInstance().getAllUserMap().get(cmVisitRecord.getVisitMan()));
                    }

                    ConsOrgCache orgcache = ConsOrgCache.getInstance();
                    String uporgcode = orgcache.getUpOrgMapCache().get(cmVisitRecord.getOrgCode());
                    if("0".equals(uporgcode)){
                        cmVisitRecord.setUpOrgName(cmVisitRecord.getOrgName());
                    }else{
                        cmVisitRecord.setUpOrgName(orgcache.getAllOrgMap().get(uporgcode));
                    }
                }
                exportService.exportVisitRecordByList(response, visitRecordList);
            } else {
                resultMap.put("msg", "dataError");
                return resultMap;
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
		resultMap.put("msg", "success");
        return resultMap;
    }
    
    @RequestMapping("/editVisitRecord.do")
    @ResponseBody
    public Object editVisitRecord(HttpServletRequest request){
        Map<String,Object> map = new HashMap<String,Object>();
        String tradeid = request.getParameter("tradeid");
        
        Map<String,String> param = new HashMap<String,String>();
        param.put("tradeId",tradeid);
        List<CmVisitRecord> visitRecordList = cmVisitRecordService.listVisitRecord(param);
        if(visitRecordList != null && visitRecordList.size() >0){
        	CmVisitRecord record = visitRecordList.get(0);
        	map.put("record", record);
        }
        return new ModelAndView("doubletrade/editVisitRecord","map",map);
    }
    
    @ResponseBody
    @RequestMapping("/dealEditRecord.do")
    public String dealEditRecord(HttpServletRequest request) {
        String result = null;
        String id = request.getParameter("id");
        String visitResultDt = request.getParameter("visitResultDt");
        String visitResult = request.getParameter("visitResult");
        String feedBackType = request.getParameter("feedBackType");
        
        if (StringUtils.isBlank(id)) {
            return "paramError";
        }
        CmVisitRecord record = new CmVisitRecord();
        record.setId(Long.valueOf(id));
        record.setVisitResult(visitResult);
        record.setFeedBackType(feedBackType);
        if(StaticVar.VISIT_RESULT_COMPLET.equals(visitResult)){
        	record.setVisitResultDt(visitResultDt);
        }else{
        	record.setVisitResultDt("");
        }
        cmVisitRecordService.update(record);
        result = "success";
        return result;
    }
}
