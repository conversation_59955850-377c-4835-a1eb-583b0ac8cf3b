package com.howbuy.crm.hb.web.controller.custinfo;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.acc.common.ListUtil;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.acccenter.facade.query.idcert.QueryIdCardInfoFacade;
import com.howbuy.acccenter.facade.query.idcert.QueryIdCardInfoRequest;
import com.howbuy.acccenter.facade.query.idcert.QueryIdCardInfoResponse;
import com.howbuy.acccenter.facade.query.idcert.bean.OfflineImageFileBean;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import com.howbuy.crm.account.client.request.custinfo.HboneAcctRelationOptRequest;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.consultant.ConsultantSimpleResponse;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.dto.CustconstantInfoDomain;
import com.howbuy.crm.conscust.request.*;
import com.howbuy.crm.conscust.response.BatchAssignCustResponse;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.response.QueryCustconstantInfoResponse;
import com.howbuy.crm.conscust.response.UpdateCustconstantInfoResponse;
import com.howbuy.crm.conscust.service.BatchAssignCustService;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.conscust.service.QueryCustconstantInfoService;
import com.howbuy.crm.conscust.service.UpdateCustconstantInfoService;
import com.howbuy.crm.consultant.dto.ConsultantSimpleInfoDto;
import com.howbuy.crm.consultant.service.ConsultantInfoService;
import com.howbuy.crm.custlabel.dto.CmCustLabelDomain;
import com.howbuy.crm.custlabel.request.DealCmCustLabelRequest;
import com.howbuy.crm.custlabel.response.DealCmCustLabelResponse;
import com.howbuy.crm.custlabel.service.DealCmCustLabelService;
import com.howbuy.crm.hb.constants.CommonConstant;
import com.howbuy.crm.hb.domain.conscust.ExportCustcontantHisEntity;
import com.howbuy.crm.hb.domain.conscust.ExportCustcontantHisVo;
import com.howbuy.crm.hb.domain.custinfo.*;
import com.howbuy.crm.hb.domain.hbone.HboneAcctCustDetailInfoDTO;
import com.howbuy.crm.hb.domain.hkconscust.HkConscustVO;
import com.howbuy.crm.hb.domain.prosale.CmCustTransferDetail;
import com.howbuy.crm.hb.enums.TransReasonEumn;
import com.howbuy.crm.hb.outersevice.ConsultantOuterService;
import com.howbuy.crm.hb.persistence.custinfo.WaitTransferCustMapper;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.*;
import com.howbuy.crm.hb.service.hkconscust.HkConscustService;
import com.howbuy.crm.hb.service.outerservice.CrmAccountOuterService;
import com.howbuy.crm.hb.service.prosale.CmCustTransferDetailService;
import com.howbuy.crm.hb.service.prosale.PreControlHbService;
import com.howbuy.crm.hb.service.qywechat.CmConscustQyWechatService;
import com.howbuy.crm.hb.service.usergroup.CmCustomizegroupUserService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.controller.cache.CacheCode;
import com.howbuy.crm.hb.web.controller.cache.CacheUtil;
import com.howbuy.crm.hb.web.dto.custinfo.IdCardDisplayInfoDto;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.nt.yxslive.po.CustYxsLivePo;
import com.howbuy.crm.nt.yxslive.service.CustYxsLiveStaticService;
import com.howbuy.crm.nt.yxslive.vo.CustYxsLiveVo;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.trade.common.enums.YesNoEnum;
import com.howbuy.crm.util.MainLogUtils;
import com.howbuy.crm.util.excel.TransferCustInfoListener;
import com.howbuy.dfile.HFileService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.db.PageResult;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.HttpUtils;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户管理 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@RequestMapping(value = "/conscust")
public class CmConscustController extends BaseController {

    public static final String WAIT_TRANSFER_TYPE = "waittransfer";
    @Autowired
    private ConscustService custService;

    @Autowired
    private ConscustService conscustService;

    @Autowired
    private CrmAccountOuterService crmAccountOuterService;

    @Autowired
    private CustYxsLiveStaticService custYxsLiveStaticService;

    @Autowired
    private CustconstantService custconstantService;

    @Autowired
    private CmCustfamilySubService cmCustfamilySubService;

    @Autowired
    private UpdateCustconstantInfoService updateCustconstantInfoService;

    @Autowired
    private QueryCustconstantInfoService queryCustconstantInfoService;

    @Autowired
    private CmCustLabelService cmCustLabelservice;

    @Autowired
    private WaitTransferCustService waitTransferCustService;

    @Autowired
    private DealCmCustLabelService dealCmCustLabelService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private CmRealConsultantService cmRealConsultantService;

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;

    @Autowired
    private CmCustomizegroupUserService cmCustomizegroupUserService;

    @Autowired
    private WaitTransferCustMapper waitTransferCustMapper;

    @Autowired
    private DecryptSingleFacade decryptSingleFacade;

    @Autowired
    private PageVisitLogService pageVisitLogService;

    @Autowired
    private CmCustTransferDetailService cmCustTransferDetailService;

    @Autowired
    private CmCustconstanthisService cmCustconstanthisService;

    @Autowired
    private PreControlHbService preControlHbService;

    @Autowired
    private CmConscustQyWechatService cmConscustQyWechatService;

    @Autowired
    private ConsultantInfoService consultantInfoService;

    @Autowired
    private CmTransferCustService cmTransferCustService;

    @Autowired
    private HkConscustService hkConscustService;


    @Autowired
    private QueryIdCardInfoFacade queryIdCardInfoFacade;

    @Autowired
    private BatchAssignCustService batchAssignCustService;

    //NOTICE : outerService 不应该此处注入，但 历史结构未完全重构。
    @Autowired
    private ConsultantOuterService consultantOuterService;

    @Value("${GLOBAL_HB_WEBCHAT}")
    private String wewbchatUrl;
    @Value("${WECHAT_WEB_URL}")
    private String wechatWebUrl;
    @Value("${CRM_CGI_URL}")
    private String crmCgiUrl;

    /**
     * 读取 cim_web_server 文件
     */
    private String cimWebServerStoreConfig="cim_web_file_config";

    /**
     * cim_web_server 切换webdav  .
     * 账户中心系统  数据库存储 路径为  /data/app/cim_web_server/center_feature/20240117/
     * 其中 [/data/app/cim_web_server/] 会被迁移为 webdav
     * 例如：/data/app/cim_web_server/center_feature/20240117/xxx.jpg
     * 实际 请求参数 ：  config , center_feature/20240117  ,  xxx.jpg
     */
//    整理：
//            1 挂载服务器 路径：
//            10.12.110.55:/data/files/cim_web_server
//2 基于NFS 建的  webdav :  http://webdav-nfs01.inner.ehowbuy.com/cim_web_server/
//            3 账户中心本地服务器路径：
//            /data/app/cim_web_server/center_feature
//4 账户中心 数据库存的路径 ：
//            /data/app/cim_web_server/center_feature
//5 导致使用时路径
//    应该是截取到： /data/app/cim_web_server/center_feature
    private static final String  relativePath_prefix = "/data/app/cim_web_server/center_feature/";

    private final String DOWNLOAD_FILE_NAME = "批量划转客户导入模版.xls";

    private final String MODEL_FILE_NAME = "crmbatchtranscustmode.xls";

    /**
     * 默认 手机地区码 -86
     */
    public static final String DEFAULT_MOBILE_AREA_CODE = "+86";



    /**
     * 跳转到投顾客户列表
     *
     * @param request
     * @return
     */
    @RequestMapping("/list.do")
    public ModelAndView list(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        List<String> roles = (List<String>) request.getSession().getAttribute("loginRoles");
        User userlogin = (User) request.getSession().getAttribute("loginUser");

        // 获取登陆用户信息
        HttpSession session=request.getSession();
        String topcpdata = (String) session.getAttribute("topcpdata");

        //是否有分配权限
        boolean canassign = false;
        for (String role : roles) {
            List<String> temp = AuthCache.getInstance().getOperListStr(role, "020107");
            if (temp != null && (temp.contains("7") || temp.contains("8"))) {
                canassign = true;
                break;
            }
        }
        //是否有添加组权限
        boolean canaddgroup = false;
        for (String role : roles) {
            List<String> temp = AuthCache.getInstance().getOperListStr(role, "020107");
            if (temp != null && (temp.contains("18") || temp.contains("19"))) {
                canaddgroup = true;
                break;
            }
        }
        Map<String, String> param = new HashMap<String, String>(3);
        param.put("menu_code", "020107");
        param.put("auth_para", "34");
        param.put("usercode", userlogin.getUserId());
        modelAndView.addObject("topcpdata", topcpdata);
        modelAndView.addObject("cansearchsametrade", conscustService.getHasOpenRange(param));
        modelAndView.addObject("canassign", canassign);
        modelAndView.addObject("canaddgroup", canaddgroup);
        modelAndView.addObject("keyuser", Util.getkeyUser(userlogin.getUserId()));
        modelAndView.addObject("userId", userlogin.getUserId());
        modelAndView.addObject("wechatWebUrl", wechatWebUrl);
        modelAndView.addObject("crmCgiUrl", crmCgiUrl);
        modelAndView.setViewName("/custinfo/custList");
        return modelAndView;
    }


    /**
     * 投顾客户查询
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/custList")
    public Map<String, Object> listConsCustList(HttpServletRequest request) throws Exception {
        long startTimestamp = System.currentTimeMillis();
        // 设置查询分页参数
        Map<String, String> params = buildParam(request);
        // 统计构建查询条件的耗时
        long timestamp1 = System.currentTimeMillis();
        log.info("conscust|custList 构建查询条件耗时：{}", timestamp1 - startTimestamp);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        PageData<Conscust> pd = conscustService.selectConscustByPage(params);
        // 统计查询耗时
        long timestamp2 = System.currentTimeMillis();
        log.info("conscust|custList 查询耗时：{}", timestamp2 - timestamp1);
        List<Conscust> conscustList = pd.getListData();

        if (CollectionUtils.isNotEmpty(conscustList)) {
            transferCustInfo(conscustList, params);
        }

        // 部分字段转义耗时
        long timestamp3 = System.currentTimeMillis();
        log.info("conscust|custList 部分字段转义耗时：{}", timestamp3 - timestamp2);
        resultMap.put("total", pd.getPageBean().getTotalNum());
        resultMap.put("rows", pd.getListData());
        // 总耗时
        long timestamp4 = System.currentTimeMillis();
        log.info("conscust|custList 总耗时：{}", timestamp4 - startTimestamp);
        return resultMap;
    }

    /**
     * @description: 查询投顾客户列表 部分展示字段转义
     * @param conscustList 投顾客户列表
     * @param params 查询参数
     * @author: jin.wang03
     * @date: 2024/2/2 10:37
     * @since JDK 1.8
     */
    private void transferCustInfo(List<Conscust> conscustList, Map<String, String> params) {
        ConstantCache constantCache = ConstantCache.getInstance();
        Map<String, String> provCityMap = constantCache.getProvCityMap();
        Map<String, String> custLevelMap = constantCache.getConstantKeyVal("custlevel") != null
                ? constantCache.getConstantKeyVal("custlevel") : new HashMap<>();

        ConsOrgCache orgcache = ConsOrgCache.getInstance();
        Map<String, String> allConsMap = orgcache.getAllConsMap();
        Map<String, String> allOrgMap = orgcache.getAllOrgMap();
        Map<String, String> cons2OutletMap = orgcache.getCons2OutletMap();
        Map<String, String> cons2TeamMap = orgcache.getCons2TeamMap();
        Map<String, String> upOrgMapCache = orgcache.getUpOrgMapCache();

        List<String> custNoList = conscustList.stream().map(Conscust::getConscustno).collect(Collectors.toList());
        List<CmRealConsultant> cmRealConsultants = cmRealConsultantService.listCmRealConsultantByCustNoList(custNoList);
        Map<String, String> custNoAndRealConscodeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(cmRealConsultants)) {
            cmRealConsultants.forEach(cmRealConsultant -> custNoAndRealConscodeMap.put(cmRealConsultant.getConscustno(), cmRealConsultant.getRealConscode()));
        }

        for (Conscust v : conscustList) {
            if (StringUtils.isNotBlank(v.getLinkman()) && StringUtils.isBlank(params.get("isSameTrade"))) {
                v.setCustname(v.getCustname() + "<BR>&nbsp;&nbsp;&nbsp;&nbsp;联系人：" + v.getLinkman());
                v.setMobileMask(StringUtil.replaceNull(v.getLinkmobileMask()) + "<br>" + v.getMobileMask());
            } else if (StringUtils.isNotBlank(v.getLinkman()) && StringUtils.isNotBlank(params.get("isSameTrade"))) {
                v.setCustname(v.getCustname() + "<BR>&nbsp;&nbsp;&nbsp;&nbsp;联系人：" + v.getLinkman());
            }
            v.setCityname(provCityMap.get(v.getCitycode()));
            if (StringUtils.isNotBlank(v.getVisittime())) {
                v.setVisittime(v.getVisittime().substring(0, 10).replaceAll("-", ""));
            }
            if (StringUtils.isNotBlank(v.getConscode())) {
                v.setConsname(allConsMap.get(v.getConscode()));
                v.setOrgname(allOrgMap.get(cons2OutletMap.get(v.getConscode())));
                String stname = allOrgMap.get(cons2TeamMap.get(v.getConscode()));
                v.setTeamname(stname == null ? "--" : stname);

                String uporgcode = upOrgMapCache.get(cons2OutletMap.get(v.getConscode()));
                v.setUporgname("0".equals(uporgcode) ? v.getOrgname() : allOrgMap.get(uporgcode));

                // 判断当前投顾和配置了实际维护投顾是否相同
                if (custNoAndRealConscodeMap.containsKey(v.getConscustno()) &&
                        !StringUtils.equals(v.getConscode(), custNoAndRealConscodeMap.get(v.getConscustno()))) {
                    v.setIsrealcustcode("1");
                }
            }
            v.setMobileMask(transferShowMobileMask(v.getMobileMask(), v.getMobileAreaCode()));
            v.setConscustlvl(custLevelMap.get(v.getConscustlvl()));
            v.setTotallabel(StringUtils.isBlank(v.getTotallabel()) ? "-" : v.getTotallabel());
        }
    }

    @ResponseBody
    @RequestMapping("/ajaxGetCustCons.do")
    public void ajaxGetCustCons(HttpServletRequest request, HttpServletResponse response) {
        String conscustno = request.getParameter("conscustno");
        String org = request.getParameter("org");
        StringBuffer result = new StringBuffer();
        Map<String, String> param = new HashMap<>(1);
        param.put("custno", conscustno);
        List<Map<String, String>> list = custconstantService.ajaxGetCustConsByConscust(param);
        if (list != null && list.size() > 0) {
            result.append("<table style='height:55px;width:450px;color:black'>");
            result.append("<tr><td style='width:125px'>部门</td><td style='width:60px'>投顾</td>");
            if (StringUtils.isNotBlank(org)) {
                result.append("<td style='width:60px'>划转人</td>");
            }
            result.append("<td style='width:60px'>起始日期</td><td style='width:60px'>截止日期</td></tr>");
            for (Map<String, String> map : list) {
                result.append("<tr>");
                result.append("<td>" + ObjectUtils.ObjectToString(ConsOrgCache.getInstance().getOrgMap().get(ConsOrgCache.getInstance().getUser2OutletMap().get(map.get("CONSCODE")))) + "</td>");
                result.append("<td>" + ObjectUtils.ObjectToString(map.get("CONSNAME")) + "</td>");
                if (StringUtils.isNotBlank(org)) {
                    result.append("<td>" + ObjectUtils.ObjectToString(ConsOrgCache.getInstance().getAllConsMap().get(map.get("CREATOR"))) + "</td>");
                }
                result.append("<td>" + ObjectUtils.ObjectToString(map.get("STARTDT")) + "</td>");
                result.append("<td>" + ObjectUtils.ObjectToString(map.get("ENDDT")) + "</td>");
                result.append("</tr>");
            }
            result.append("</table>");
        }
        response.setContentType("text/html; charset=utf-8");
        PrintWriter pw = null;
        try {
            pw = response.getWriter();
            pw.print(result.toString());
            pw.flush();
        } catch (IOException e) {
            log.error("数据写入异常", e);
        } finally {
            if (pw != null) {
                pw.close();
            }
        }
    }

    /**
     * @api {GET} /conscust/ajaxGetCustMobile.do ajaxGetCustMobile()
     * @apiVersion 1.0.0
     * @apiGroup CmConscustController
     * @apiName ajaxGetCustMobile()
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "Xp"
     */
    @ResponseBody
    @RequestMapping("/ajaxGetCustMobile.do")
    public String ajaxGetCustMobile(HttpServletRequest request) {
        String conscustno = request.getParameter("conscustno");

        String hboneShowMobileMask = "";
        HboneAcctCustDetailInfoDTO hboneDetailInfoDTO = conscustService.getHboneDetailInfoByCustNo(conscustno);
        if (hboneDetailInfoDTO != null && StringUtils.isNotEmpty(hboneDetailInfoDTO.getMobileMask())) {
            String hboneMobileMask = hboneDetailInfoDTO.getMobileMask();
            String hboneMobileAreaCode = hboneDetailInfoDTO.getMobileAreaCode();
            hboneShowMobileMask = transferShowMobileMask(hboneMobileMask, hboneMobileAreaCode);
        }
        String hkShowMobileMask = "";
        HkConscustVO hkConscustVO = hkConscustService.queryHkCustInfoByCustNo(conscustno);
        if (hkConscustVO != null && StringUtils.isNotEmpty(hkConscustVO.getMobileMask())) {
            String hkMobileMask = hkConscustVO.getMobileMask();
            String hkMobileAreaCode = hkConscustVO.getMobileAreaCode();
            hkShowMobileMask = transferShowMobileMask(hkMobileMask, hkMobileAreaCode);
        }


        if (StringUtils.isNotEmpty(hboneShowMobileMask) || StringUtils.isNotEmpty(hkShowMobileMask)) {
            StringBuilder result = new StringBuilder();
            result.append("<table style='height:55px;width:230px;color:black'>");
            result.append("<tr><td style='width:80px'>分类</td><td style='width:120px'>手机号</td></tr>");

            result.append("<tr>");
            result.append("<td>一账通</td>");
            result.append(String.format("<td>%s</td>", hboneShowMobileMask));
            result.append("</tr>");

             //合规控制
            if(isNotCgRole()){
                result.append("<tr>");
                result.append("<td>香港账号</td>");
                result.append(String.format("<td>%s</td>", hkShowMobileMask));
                result.append("</tr>");
            }
            result.append("</table>");
            return result.toString();
        }
        return "false";

    }

    /**
     * @description: 【区号】为+86或为空时，默认只展示【手机号】,【区号】不为空且≠+86时，拼接区号进行展示，规则：{【区号】} + 空格 + {【手机号】}，示例：+881 7752****
     * @param mobileMask
     * @param mobileAreaCode
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2023/12/22 13:52
     * @since JDK 1.8
     */
    private String transferShowMobileMask(String mobileMask, String mobileAreaCode) {
        if (StringUtils.isNotBlank(mobileMask)) {
            return (StringUtils.isNotBlank(mobileAreaCode) && !DEFAULT_MOBILE_AREA_CODE.equals(mobileAreaCode)) ?
                    mobileAreaCode + " " + mobileMask : mobileMask;
        }
        return "";
    }


    public Map<String, String> buildParam(HttpServletRequest request) throws Exception {
        Map<String, String> pmap = new ParamUtil(request).getParamMap();
        String email = request.getParameter("email");
        if (StringUtils.isNotBlank(email)) {
            pmap.put("email", DigestUtil.digest(email.trim().toLowerCase()));
        }
        String mobile = request.getParameter("mobile");
        if (StringUtils.isNotBlank(mobile)) {
            pmap.put("mobile", DigestUtil.digest(mobile.trim()));
        }
        String idno = request.getParameter("idno");
        if (StringUtils.isNotBlank(idno)) {
            pmap.put("idno", DigestUtil.digest(idno.trim()));
        }
        String telNo = request.getParameter("telNo");
        if (StringUtils.isNotBlank(telNo)) {
            pmap.put("telNo", DigestUtil.digest(telNo.trim()));
        }
        String custsource = request.getParameter("source");
        String isnotleaf = request.getParameter("isnotleaf");
        pmap.put("isnotleaf", isnotleaf);
        if (StringUtils.isNotBlank(custsource) && StaticVar.STR_FALSE.equals(isnotleaf)) {
            pmap.put("newsourceno", custsource);
        } else if (StringUtils.isNotBlank(custsource) && StaticVar.STR_TRUE.equals(isnotleaf)) {
            pmap.put("newsourceno", ObjectUtils.getCurrentSource(custsource));
        }
        String birthmonth = request.getParameter("birthmonth");
        if (StringUtils.isNotBlank(birthmonth)) {
            pmap.put("birthmonth", birthmonth.substring(4));
        }
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        if (StringUtils.isNotBlank(consCode)) {
            pmap.put("conscode", consCode);
        } else {
            //选择了未分配组
            if (orgCode.startsWith(StaticVar.STR_OTHER)) {
                pmap.put("othertearm", orgCode.replaceFirst("other", ""));
            } else {
                String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
                //选择了团队
                if (StaticVar.ORGTYPE_TEAM.equals(orgType)) {
                    pmap.put("teamcode", orgCode);
                } else {
                    if (!"0".equals(orgCode)) {
                        List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                        pmap.put("outletcodes", ObjectUtils.getSqlInStr(suborgs));
                    } else {
                        //判断是否有权限看到全部客户（包括未分配客户）
                        String topgd = (String) request.getSession().getAttribute("topgddata");
                        if (!StaticVar.DATARANGE_GD_ALL.equals(topgd)) {
                            pmap.put("notCanSeeConsNullFlag", "true");
                        }
                    }
                }
            }
        }
        String saleDirections = request.getParameter("saleDirections");
        if (StringUtils.isNotBlank(saleDirections)) {
            String[] arrsale = saleDirections.split(",");
            pmap.put("saleDirections", ObjectUtils.getSqllikeProd("T1.saleDirection", arrsale));
        }
        String hasprodtype = request.getParameter("hasprodtype");
        String ishw = request.getParameter("ishw");
        if (StringUtils.isNotBlank(hasprodtype)) {
            String[] arrtype = hasprodtype.split(",");
            pmap.put("hasprodtype", ObjectUtils.getSqlHasProdType(ishw, arrtype));
        }
        //是否跨年度查询 0-非跨年度；1-跨年度
        String kndStatus = "0";
        String begBirthmonthDt = request.getParameter("begBirthmonthDt");
        String endBirthmonthDt = request.getParameter("endBirthmonthDt");
        String begBirthmonth = StringUtils.isNotBlank(begBirthmonthDt) ? begBirthmonthDt.substring(4) : null;
        String endBirthmonth = StringUtils.isNotBlank(endBirthmonthDt) ? endBirthmonthDt.substring(4) : null;
        if (StringUtils.isNotBlank(begBirthmonthDt) && StringUtils.isNotBlank(endBirthmonthDt)) {
            int years = Integer.parseInt(endBirthmonthDt.substring(0, 4)) - Integer.parseInt(begBirthmonthDt.substring(0, 4));
            if (years == 1) {
                kndStatus = "1";
            } else if (years > 1) {
                begBirthmonth = null;
                endBirthmonth = null;
            }
        }
        pmap.put("kndStatus", kndStatus);
        pmap.put("begBirthmonth", begBirthmonth);
        pmap.put("endBirthmonth", endBirthmonth);
        // 来源类型
        String sourceType = request.getParameter("sourceType");
        if (StringUtils.isNotBlank(sourceType)) {
            String sqlInStr = ObjectUtils.getSqlInStr(Arrays.asList(sourceType.split(",")));
            pmap.put("sourceType", sqlInStr);
        }
        return pmap;
    }


    /**
     * 分配客户
     * 历史逻辑： 面向字符串编程。 服务端返回： xxxooo提示信息。js用  indexOf('xxx')，提示信息输出。
     * 2025年7月9日 fix 批量分配客户接口返回错误信息
     * @param request
     * @return ReturnMessageDto<String>
     */
    @Deprecated
    @ResponseBody
    @RequestMapping("/batchAssignCust.do")
    public ReturnMessageDto<String> batchAssignCust(HttpServletRequest request){
        User user = (User) request.getSession().getAttribute("loginUser");
        String result = "";
        String ids = request.getParameter("ids");
        String consCode = request.getParameter("consCode");
        String type = request.getParameter("type");
        String assignReason = request.getParameter("assignReason");
        if (StringUtils.isBlank(ids) || StringUtils.isBlank(consCode)) {
            return ReturnMessageDto.fail("参数出现异常，操作失败！");
        }

        String[] custs = ids.split(",");
        //传过来的客户号
        List<String> list = new ArrayList<>();
        //将数组转list
        CollectionUtils.addAll(list, custs);
        String sqlin = ObjectUtils.getSqlInStr(list);

        // 如果是真实投顾，则判断他客户总数是否大于指定数（默认3000），大于的话，给出提示
        result = overMaxCustLimit(consCode, custs.length);
        if (StringUtils.isNotBlank(result)) {
            return ReturnMessageDto.fail(result);
        }

        // 划转类操作（非[待划转转分配]) 判断选中的列表中是否有待划转客户
        if (!WAIT_TRANSFER_TYPE.equals(type)) {
            result = hasWaitTransfer(list);
            if (StringUtils.isNotBlank(result)) {
                return ReturnMessageDto.fail("有客户在待划转列表，不允许划转投顾！");
            }
        }

        // 判断是否存在家庭账户的客户没转全部成员的
        result = judgeFamilyTransferAllMembers(list);
        if (StringUtils.isNotBlank(result)) {
            return ReturnMessageDto.fail(result);
        }

        // 获取本次划转客户DTO的list
        List<CustconstantInfoDomain> updatelist = getAssignCustList(sqlin, request, user);
        // 调用接口新增投顾、修改投顾和插入历史记录
        if (CollectionUtils.isEmpty(updatelist)) {
//            return "repeatCode"; 历史代码有问题，前端未  做 repeatCode判断
            return ReturnMessageDto.fail("本次划转客户数据列表为空！");

        }
        // 修改之前,根据客户号获取所有分配成功的客户的一账通  企业微信ID   旧投顾的企业微信账号    新投顾的企业微信账号 CmWechatTranscustInfo
        ArrayList<CmWechatTransCustInfo> cmWechatTranscustInfos = new ArrayList<>();
        if (StaticVar.TRANSF_REASON_LZHZ.equals(assignReason)) {
            cmWechatTranscustInfos = cmConscustQyWechatService.getWechatTransCustInfoByCustNo(sqlin, consCode);
        }
        UpdateCustconstantInfoRequest reqParam = new UpdateCustconstantInfoRequest();
        reqParam.setListCustconstantInfoDomain(updatelist);
        UpdateCustconstantInfoResponse updateCustconstantInfoResponse = updateCustconstantInfoService.batchUpdateCustConstant(reqParam);
        if ("0000".equals(updateCustconstantInfoResponse.getReturnCode())) {
            if (!StaticVar.TRANSF_REASON_LZHZ.equals(assignReason)) {
                // 非[离职划转]的情况，直接返回成功
                return ReturnMessageDto.ok();
            }
            result = dealAfterAssign(list, assignReason, cmWechatTranscustInfos, user);
            //业务操作成功，但是有提示语
            return ReturnMessageDto.ok(result);
        } else {
            return ReturnMessageDto.fail("划转失败！");
        }

    }


    /**
     * @api {GET} /conscust/batchTransferConsCust.do batchTransferConsCust()
     * @apiVersion 1.0.0
     * @apiGroup CmConscustController
     * @apiName batchTransferConsCust()
     * @apiDescription 批量划转客户
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"te2MknN","returnMsg":"vswEje","returnObject":"RmL","returnList":["6Ikg81XCJ"]}
     */
    @ResponseBody
    @RequestMapping("/batchTransferConsCust.do")
    public ReturnMessageDto<String> batchTransferConsCust(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute("loginUser");
        String result = "";
        String optype = request.getParameter("optype");
        String id = request.getParameter("id");
        String ids = request.getParameter("ids");
        String consCode = request.getParameter("consCode");
        String type = request.getParameter("type");
        String assignReason = request.getParameter("assignReason");
        String assignremarks = request.getParameter("assignremarks");
        String doContinueAssign = request.getParameter("doContinueAssign");
        if (StringUtils.isBlank(ids) || StringUtils.isBlank(consCode)) {
            return ReturnMessageDto.fail("参数出现异常，操作失败！");
        }

        String[] custs = ids.split(",");
        //传过来的客户号
        List<String> list = new ArrayList<>();
        //将数组转list
        CollectionUtils.addAll(list, custs);
        String sqlin = ObjectUtils.getSqlInStr(list);

        // 如果是真实投顾，则判断他客户总数是否大于指定数（默认3000），大于的话，给出提示
        result = overMaxCustLimit(consCode, custs.length);
        if (StringUtils.isNotBlank(result)) {
            return ReturnMessageDto.fail(result);
        }

        // 划转类操作（非[待划转转分配]) 判断选中的列表中是否有待划转客户
        if (!"waittransfer".equals(type)) {
            result = hasWaitTransfer(list);
            if (StringUtils.isNotBlank(result)) {
                return ReturnMessageDto.fail("有客户在待划转列表，不允许划转投顾！");
            }
        }

        // 判断是否存在家庭账户的客户没转全部成员的
        result = judgeFamilyTransferAllMembers(list);
        if (StringUtils.isNotBlank(result)) {
            return ReturnMessageDto.fail(result);
        }

        // 获取本次划转客户DTO的list
        List<AssignCustInfo> updatelist = getAssignCustInfoList(sqlin, request, user);
        // 调用接口新增投顾、修改投顾和插入历史记录
        if (CollectionUtils.isEmpty(updatelist)) {
            return ReturnMessageDto.fail("客户的所属投顾与分配投顾一致，请重新分配！");
        }

        BatchAssignCustRequest reqParam = new BatchAssignCustRequest();
        reqParam.setAssignCustInfoList(updatelist);
        if (StaticVar.YES.equals(doContinueAssign)) {
            reqParam.setDoContinueAssign(true);
        }
        BatchAssignCustResponse execute = batchAssignCustService.execute(reqParam);

        if (Objects.isNull(execute)) {
            return ReturnMessageDto.fail("系统异常！");
        }

        return new ReturnMessageDto<>(execute.getReturnCode(), execute.getDescription());
    }

    /**
     * @description: 处理分配客户成功后的逻辑
     * @param list
     * @param assignReason
     * @param cmWechatTranscustInfos
     * @param user
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/3/3 14:45
     * @since JDK 1.8
     */
    private String dealAfterAssign(List<String> list, String assignReason, ArrayList<CmWechatTransCustInfo> cmWechatTranscustInfos, User user) {
        String result = null;
        // 处理在待划转列表中的客户
        Map<String, String> map = new HashMap<>(1);
        String sqlins = Util.getOracleSQLIn(list, 999, "t.conscustno");
        map.put("sqlins", sqlins);
        waitTransferCustMapper.delWaitTransferCusts(map);

        // 处理企业微信离职划转逻辑 划转原因是离职划转
        // 请求划转  并且划转失败的客户号, 提醒用
        ArrayList<String> failCustNos = new ArrayList<>();
        ArrayList<String> externaluserIdList = new ArrayList<>();
        // 挨个调佣企业微信划转并记录返回code   组装报错custNoList
        if (CollectionUtils.isNotEmpty(cmWechatTranscustInfos)) {
            for (CmWechatTransCustInfo dto : cmWechatTranscustInfos) {
                dto.setCreator(user.getUserId());
                dto.setAssignReason(assignReason);
                if (StringUtils.isNotEmpty(dto.getExternaluserId())) {
                    externaluserIdList.add(dto.getExternaluserId());
                    Map<String, String> wechatMap = Maps.newHashMap();
                    wechatMap.put("externalUserId", dto.getExternaluserId());
                    wechatMap.put("oldConsCode", dto.getOldConsCode());
                    wechatMap.put("consCode", dto.getConsCode());
                    try {
                        String wechatResult = HttpUtils.post2(wewbchatUrl + "/wechatuser/transferCustomer", wechatMap);
                        log.info("离职划转：{} http请求wechat离职划转:{}", JSON.toJSONString(wechatMap), wechatResult);
                        if (StringUtil.isNotNullStr(wechatResult)) {
                            dto.setReturnCode(wechatResult);
                        }
                        //返回为空  或者返回的不是成功
                        if (!StaticVar.WECHAT_RESPONSE_SUCCESS.equals(wechatResult)) {
                            failCustNos.add(dto.getConscustNo());
                        }
                    } catch (Exception e) {
                        log.error("离职划转：{} http请求wechat离职划转,异常信息：{}", JSON.toJSONString(wechatMap), e);
                        failCustNos.add(dto.getConscustNo());
                    }
                }
            }
        }
        //落下记录表
        cmConscustQyWechatService.batchInsertWechatTransCustInfo(cmWechatTranscustInfos);
        //返回前端
        //全未添加
        if (externaluserIdList.isEmpty()) {
            result = "CRM客户划转成功，企业微信无需进行划转";
        } else if (externaluserIdList.size() != cmWechatTranscustInfos.size()) {
            //部分添加  部分未添加  需要划转的全部成功
            if (failCustNos.isEmpty()) {
                result = "CRM客户划转成功，企业微信针对添加客户已做相应划转";
            } else {
                result = "CRM客户划转成功，其中"
                        + String.join(",", failCustNos)
                        + "企业微信中投顾状态不符合离职继承条件，请人工处理";
            }
        } else if (externaluserIdList.size() == cmWechatTranscustInfos.size()) {
            //都添加 需要划转的全部成功
            if (failCustNos.isEmpty()) {
                result = "CRM客户与企业微信客户均划转成功";
            } else {
                result = "CRM客户划转成功，其中"
                        + String.join(",", failCustNos)
                        + "企业微信中投顾状态不符合离职继承条件，请人工处理";
            }
        }

        return result;
    }

    /**
     * @description: 获取本次划转客户DTO的list
     * @param sqlin
     * @param request
     * @param user
     * @return java.util.List<com.howbuy.crm.conscust.dto.CustconstantInfoDomain>
     * @author: jin.wang03
     * @date: 2025/3/3 14:46
     * @since JDK 1.8
     */
    private List<CustconstantInfoDomain> getAssignCustList(String sqlin, HttpServletRequest request, User user) {
        String optype = request.getParameter("optype");
        String id = request.getParameter("id");
        String consCode = request.getParameter("consCode");
        String assignReason = request.getParameter("assignReason");
        String assignremarks = request.getParameter("assignremarks");

        List<CustconstantInfoDomain> updatelist = new ArrayList<>();
        //调用接口查询客户
        QueryCustconstantInfoRequest rsqParam = new QueryCustconstantInfoRequest();
        Map<String, Object> newParam = new HashMap<>(1);
        newParam.put("custnos", sqlin);
        rsqParam.setMapParams(newParam);
        QueryCustconstantInfoResponse listcc = queryCustconstantInfoService.queryListCustconstantInfo(rsqParam);
        if (listcc != null && CollectionUtils.isNotEmpty(listcc.getListCustconstantInfoDomain())) {
            for (CustconstantInfoDomain cc : listcc.getListCustconstantInfoDomain()) {
                if (!consCode.equals(cc.getConscode())) {
                    cc.setStartdt(DateTime.now().toString("yyyyMMdd"));
                    cc.setConscode(consCode);
                    cc.setNextcons(consCode);
                    cc.setReason(assignReason);
                    cc.setRemark(assignremarks);
                    cc.setModifier(user.getUserId());
                    cc.setModdt(DateTime.now().toString("yyyyMMdd"));
                    cc.setCreator(user.getUserId());
                    if (StringUtil.isNotNullStr(id) && "1".equals(optype)) {
                        cc.setRepeatcustlogid(new BigDecimal(id));
                    }
                    //关联关系 重复客户划转id --> 历史分配
                    if (StringUtil.isNotNullStr(id)) {
                        cc.setTransfLogId(new BigDecimal(id));
                    }
                    updatelist.add(cc);
                }
            }
        }
        return updatelist;
    }


    /**
     * @description: 获取本次划转客户DTO的list
     * @param sqlin
     * @param request
     * @param user
     * @return java.util.List<com.howbuy.crm.conscust.dto.AssignCustInfo>
     * @author: jin.wang03
     * @date 2025/3/25 14:46
     */
    private List<AssignCustInfo> getAssignCustInfoList(String sqlin, HttpServletRequest request, User user) {
        String optype = request.getParameter("optype");
        String id = request.getParameter("id");
        String nextcons = request.getParameter("consCode");
        String assignReason = request.getParameter("assignReason");
        String assignremarks = request.getParameter("assignremarks");

        List<AssignCustInfo> updatelist = new ArrayList<>();
        //调用接口查询客户
        QueryCustconstantInfoRequest rsqParam = new QueryCustconstantInfoRequest();
        Map<String, Object> newParam = new HashMap<>(1);
        newParam.put("custnos", sqlin);
        rsqParam.setMapParams(newParam);
        QueryCustconstantInfoResponse rep = queryCustconstantInfoService.queryListCustconstantInfo(rsqParam);
        if (rep == null || CollectionUtils.isEmpty(rep.getListCustconstantInfoDomain())) {
            return updatelist;
        }

        for (CustconstantInfoDomain cc : rep.getListCustconstantInfoDomain()) {
            if (nextcons.equals(cc.getConscode())) {
                continue;
            }
            AssignCustInfo assignCustInfo = new AssignCustInfo();

            assignCustInfo.setCustno(cc.getCustno());
            assignCustInfo.setNextcons(nextcons);
            assignCustInfo.setReason(assignReason);
            assignCustInfo.setRemark(assignremarks);
            assignCustInfo.setModifier(user.getUserId());
            assignCustInfo.setModdt(DateTime.now().toString("yyyyMMdd"));

            assignCustInfo.setArea(cc.getArea());
            assignCustInfo.setWifichannel(cc.getWifichannel());
            assignCustInfo.setCustsource(cc.getCustsource());
            assignCustInfo.setDealtype(cc.getDealtype());
            assignCustInfo.setCusttype(cc.getCusttype());

            //关联关系 重复客户划转id --> 历史分配
            if (StringUtil.isNotNullStr(id)) {
                assignCustInfo.setTransfLogId(new BigDecimal(id));
                if ("1".equals(optype)) {
                    assignCustInfo.setRepeatcustlogid(new BigDecimal(id));
                }
            }
            updatelist.add(assignCustInfo);

        }

        return updatelist;
    }

    /**
     * @description: 批量划转客户 校验家庭账号的人员是否全部被选中
     * @param list
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/3/3 14:21
     * @since JDK 1.8
     */
    private String judgeFamilyTransferAllMembers(List<String> list) {
        Map<String, String> paramfamily = new HashMap<>(1);
        paramfamily.put("sqlins", Util.getOracleSQLIn(list, 999, "conscustno"));
        List<CmCustfamilySub> listfamily = cmCustfamilySubService.listCmCustfamilySubByAssignCust(paramfamily);
        Map<String, List<CmCustfamilySub>> familyMap = new HashMap<>(8);
        for (CmCustfamilySub sub : listfamily) {
            if (familyMap.containsKey(sub.getFamilycode())) {
                familyMap.get(sub.getFamilycode()).add(sub);
            } else {
                List<CmCustfamilySub> listsub = new ArrayList<>();
                listsub.add(sub);
                familyMap.put(sub.getFamilycode(), listsub);
            }
        }
        //遍历查找转分配中遗漏了的家庭成员
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, List<CmCustfamilySub>> entry : familyMap.entrySet()) {
            List<String> familyCusts = new ArrayList<>();
            familyCusts.add(entry.getKey());
            for (CmCustfamilySub sub : entry.getValue()) {
                familyCusts.add(sub.getConscustno());
            }
            if (!list.containsAll(familyCusts)) {
                sb.append("家庭账户")
                        .append(entry.getValue().get(0).getFamilyname())
                        .append(",它还有其他家庭（主）辅账户");

                for (String subobj : familyCusts) {
                    if (!list.contains(subobj)) {
                        if (entry.getKey().equals(subobj)) {
                            sb.append(entry.getValue().get(0).getFamilyname()).append(",");
                        } else {
                            for (CmCustfamilySub obj : entry.getValue()) {
                                if (subobj.equals(obj.getConscustno())) {
                                    sb.append(obj.getCustname()).append(",");
                                }
                            }
                        }
                    }
                }
                sb.append("未统一转分配;");
            }
        }
        if (sb.length() > 0) {
            sb.insert(0, "选中的客户中存在");
            sb.append("转分配失败。");
        }
        return sb.toString();
    }

    /**
     * @description: 校验客户是否在待划转列表中
     * @param list
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/3/3 14:21
     * @since JDK 1.8
     */
    private String hasWaitTransfer(List<String> list) {
        Map<String, String> paramwait = new HashMap<String, String>(1);
        String sqlins = Util.getOracleSQLIn(list, 999, "t.conscustno");
        paramwait.put("sqlins", sqlins);
        List<WaitTransferCust> listowner = waitTransferCustMapper.listWaitTransferCustByCustnos(paramwait);
        if (CollectionUtils.isNotEmpty(listowner)) {
            // 有客户在待划转列表，不允许划转投顾
            return "haswaittransfer";
        }
        return null;
    }

    /**
     * @description: 校验投顾名下的客户数是否超过最大客户数
     * @param consCode
     * @param assignNum
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/3/3 14:20
     * @since JDK 1.8
     */
    private String overMaxCustLimit(String consCode, int assignNum) {
        ConsultantSimpleInfoDto simpleInfoDto = consultantInfoService.getInfoByconsCode(consCode);
        // 是否虚拟投顾（1 是/0 否） */
        boolean isVirtual = "1".equals(simpleInfoDto.getIsVirtual());
        if (!isVirtual) {
            Map<String, String> consParam = new HashMap<>(1);
            consParam.put("conscode", consCode);
            int curCustNum = custconstantService.hadCustNum(consParam);
            // 获取常量表key-value数据
            LinkedHashMap<String, String> maxCustLimitMap = ConstantCache.getInstance().getConstantKeyVal("maxCustLimit");
            int maxLimit = 3000;
            for (String key : maxCustLimitMap.keySet()) {
                maxLimit = Integer.parseInt(key);
            }
            if ((curCustNum + assignNum) > maxLimit) {
                return "当前分配投顾的客户数已达上限" + maxLimit + "，不允许分配（当前客户数：" + curCustNum + "，要分配客户数：" + assignNum + "）";

            }
        }
        return "";
    }

    @ResponseBody
    @RequestMapping("/batchTransferCust.do")
    public Map<String, Object> batchTransferCust(HttpServletRequest request, @RequestBody List<Map<String, String>> batchParams) {
        Map<String, Object> resultMap = conscustService.batchTransferCust(batchParams);
        return resultMap;
    }

    @ResponseBody
    @RequestMapping("/batchUpdateCmTransfLog.do")
    public Map<String, Object> batchUpdateCmTransfLog(HttpServletRequest request, @RequestBody List<Map<String, String>> batchParams) {
        Map<String, Object> resultMap = conscustService.batchUpdateCmTransfLog(batchParams);
        return resultMap;
    }

    /**
     * @description:(取消导入接口)
     * @param request
     * @return java.lang.String
     * @author: xfc
     * @date: 2023/4/11 18:13
     * @since JDK 1.8
     */
    @PostMapping("/deleteimport")
    @ResponseBody
    public String deleteImport(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute("loginUser");
        cmTransferCustService.deleteByModiferUser(user.getUserId());
        return "success";
    }

    /**
     * @description:(确认导入)
     * @param request
     * @return java.lang.String
     * @author: xfc
     * @date: 2023/4/11 18:13
     * @since JDK 1.8
     */
    @PostMapping(value = "/confirmimport")
    @ResponseBody
    public ReturnMessageDto<String> confirmImport(HttpServletRequest request) {
        ReturnMessageDto<String> result = null;

        // 根据当前操作用户来处理导入数据
        User user = (User) request.getSession().getAttribute("loginUser");
        List<CmTransferCust> cmTransferCustList = cmTransferCustService.selectByModiferUser(user.getUserId());
        try {
            result = cmTransferCustService.batchInsertTransferCust(cmTransferCustList, user.getUserId(), "");
        } catch (Exception e) {
            log.info("确认划转数据失败");
            result = ReturnMessageDto.fail("确认划转数据失败");
        } finally {
            // 页面交互 存在二次交互的场景。
            // 非二次交互的情况，删除导入的数据
            // 二次交互的情况，将再continueConfirmImport删除导入数据
            if (Objects.isNull(result) || !"0001".equals(result.getReturnCode())) {
                cmTransferCustService.deleteByModiferUser(user.getUserId());
            }
        }
        return result;
    }


    /**
     * @api {POST} /conscust/continueconfirmimport continueConfirmImport()
     * @apiVersion 1.0.0
     * @apiGroup CmConscustController
     * @apiName continueConfirmImport()
     * @apiDescription 导入批量划转客户 二次确认
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"xGYOXMASkF","returnMsg":"a0kG","returnObject":"U4","returnList":["Vnb4TSL"]}
     */
    @PostMapping(value = "/continueconfirmimport")
    @ResponseBody
    public ReturnMessageDto<String> continueConfirmImport(HttpServletRequest request) {
        ReturnMessageDto<String> result = null;

        // 根据当前操作用户来处理导入数据
        User user = (User) request.getSession().getAttribute("loginUser");
        List<CmTransferCust> cmTransferCustList = cmTransferCustService.selectByModiferUser(user.getUserId());
        try {
            result = cmTransferCustService.batchInsertTransferCust(cmTransferCustList,
                    user.getUserId(), StaticVar.YES);
        } catch (Exception e) {
            log.info("确认划转数据失败");
            result = ReturnMessageDto.fail("确认划转数据失败");
        } finally {
            cmTransferCustService.deleteByModiferUser(user.getUserId());
        }
        return result;
    }

    /**
     * @return java.lang.String
     * @description:(下载模板文件)
     * @author: xfc
     * @date: 2023/3/9 11:18
     * @since JDK 1.8
     */
    @RequestMapping(value = "/downloadmode.do", method = RequestMethod.GET)
    public void downloadModel(HttpServletRequest request,HttpServletResponse response) {
        dowmloadTemplate(MODEL_FILE_NAME,DOWNLOAD_FILE_NAME,request,response);
    }

    /**
     * @param request request请求
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @description:(上传处理分配投顾数据接口)
     * @author: xfc
     * @date: 2023/3/9 15:03
     * @since JDK 1.8
     */
    @RequestMapping(value = "/uploadtransfercustinfo", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> uploadTransferCustInfo(HttpServletRequest request) throws IOException {
        User user = (User) request.getSession().getAttribute("loginUser");

        Map<String, Object> resultMap = new HashMap<String, Object>();
        InputStream input = null;
        StringBuilder errorMsg = new StringBuilder();
        String uploadFlag = "success";
        try {
            // 转型为MultipartHttpRequest：
            MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
            // 获得文件：
            MultipartFile file = multipartRequest.getFile("file");
            if (StringUtil.isNull(file)) {
                return validReurn(resultMap, "error", errorMsg.append("请上传文件"));
            }
            // 获得输入流：
            input = file.getInputStream();

            int rowCount = WorkbookFactory.create(input).getSheetAt(0).getLastRowNum() + 1;

            if (rowCount > 3000) {
                return validReurn(resultMap, "error", errorMsg.append("一次最多可导入3千条划转数据，超过请分次导入"));
            }
            // 获取上传数据 初步校验格式
            TransferCustInfoListener transferCustInfoListener = new TransferCustInfoListener();
            log.info("开始进行初始化的格式校验");
            List<TransferCustInfo> transferCustInfoList = EasyExcel.read(file.getInputStream(), TransferCustInfo.class, transferCustInfoListener).doReadAllSync();

            // 获取excel 表头检验结果
            errorMsg.append(transferCustInfoListener.getErrHeadMsg());
            if (errorMsg.length() != 0) {
                return validReurn(resultMap, "error", errorMsg);
            }

            errorMsg.append(transferCustInfoListener.getErrMsg());
            if (errorMsg.length() != 0) {
                return validReurn(resultMap, "error", errorMsg);
            }
            // 判断是否有重复投顾客户号
            Map<String, List<TransferCustInfo>> groupCustMap = transferCustInfoList.stream().collect(Collectors.groupingBy(TransferCustInfo::getConcustno));
            Set<String> sameConcustno = groupCustMap.entrySet().stream().filter(it -> it.getValue().size() > 1).map(Map.Entry::getKey).collect(Collectors.toSet());
            if (!sameConcustno.isEmpty()) {
                sameConcustno.forEach(it -> errorMsg.append("客户号:").append(it).append("有重复，请修改后重新上传").append("\n"));
                return validReurn(resultMap, "error", errorMsg);
            }

            // 导入数据正确性校验
            for (TransferCustInfo transferCustInfo : transferCustInfoList) {
                // 投顾客户号
                String concustno = transferCustInfo.getConcustno();
                // 当前投顾编码
                String custconscode = transferCustInfo.getCustconscode();
                String custconsName = transferCustInfo.getCustconsName();
                // 分配投顾编码
                String disCustconsCode = transferCustInfo.getDisCustconsCode();
                String disCustconsName = transferCustInfo.getDisCustconsName();
                // (1) 检验投顾客户号是否存在，是否属于当前投顾  (2) 判断分配投顾是否存在，且状态是否正常
                checkConParams(concustno, custconscode, disCustconsCode, disCustconsName, errorMsg);
            }
            if (errorMsg.length() != 0) {
                return validReurn(resultMap, "error", errorMsg);
            }

            // 判断 分配投顾还可再分配人数
            Map<String, List<TransferCustInfo>> groupByDiscustconscodeMap = transferCustInfoList.stream()
                    .collect(Collectors.groupingBy(TransferCustInfo::getDisCustconsCode));
            groupByDiscustconscodeMap.forEach((key1, value) -> {
                // 如果是真实投顾，则判断他客户总数是否大于指定数（默认3000），大于的话，给出提示
                ConsultantSimpleInfoDto simpleInfoDto = consultantInfoService.getInfoByconsCode(key1);
                if (null == simpleInfoDto) {
                    // 投顾信息不存在
                    errorMsg.append("分配投顾编号：").append(key1).append("不存在对应投顾信息，请修改后重新上传<br>");
                    return;
                }
                // 是否虚拟投顾（1 是/0 否） */
                boolean isVirtual = "1".equals(simpleInfoDto.getIsVirtual());
                if (!isVirtual) {
                    Map<String, String> consParam = new HashMap<String, String>(1);
                    consParam.put("conscode", key1);
                    int curCustNum = custconstantService.hadCustNum(consParam);
                    int assignNum = value.size();
                    // 获取常量表key-value数据
                    LinkedHashMap<String, String> maxCustLimitMap = ConstantCache.getInstance().getConstantKeyVal("maxCustLimit");
                    int maxLimit = 3000;
                    for (String key : maxCustLimitMap.keySet()) {
                        maxLimit = Integer.parseInt(key);
                    }
                    if ((curCustNum + assignNum) > maxLimit) {
                        errorMsg.append("当前分配投顾的客户数已达上限").append(maxLimit).append("，不允许分配（当前客户数：")
                                .append(curCustNum).append("，要分配客户数：").append(value.size()).append("）").append("<br>");
                    }
                }
            });
            if (errorMsg.length() != 0) {
                return validReurn(resultMap, "error", errorMsg);
            }


            // 判断是否存在家庭账户的客户没转全部成员的
            String familyTransferResult = judgeFamilyTransferAllMembers(new ArrayList<>(groupCustMap.keySet()));
            if (!familyTransferResult.isEmpty()) {
                return validReurn(resultMap, "error", new StringBuilder(familyTransferResult));
            }


            // 把需要导入的数据存到记录表中
            List<CmTransferCust> cmTransferCustList = transTransDataToLog(transferCustInfoList, user);
            cmTransferCustService.batchInsertCmTransferCust(cmTransferCustList);
            return validReurn(resultMap, uploadFlag, errorMsg);
        } catch (Exception e) {
            log.error("error in uploadtransfercustinfo,{}", e.getMessage());
        } finally {
            if (null != input) {
                input.close();
            }

        }
        return resultMap;
    }


    /**
     * @param resultMap
     * @param uploadFlag
     * @param errorMsg
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @description:(返回数据封装)
     * @author: xfc
     * @date: 2023/4/11 08:59
     * @since JDK 1.8
     */
    private Map<String, Object> validReurn(Map<String, Object> resultMap, String uploadFlag, StringBuilder errorMsg) {
        resultMap.put("uploadFlag", uploadFlag);
        resultMap.put("errorMsg", errorMsg.toString());
        return resultMap;
    }

    /**
     * @description:(转换到导入数据日志实体类)
     * @param transferCustInfoList
     * @param user
     * @return java.util.List<com.howbuy.crm.hb.domain.custinfo.CmTransferCust>
     * @author: xfc
     * @date: 2023/4/11 18:14
     * @since JDK 1.8
     */
    private List<CmTransferCust> transTransDataToLog(List<TransferCustInfo> transferCustInfoList, User user) {
        List<CmTransferCust> cmTransferCustList = new ArrayList<>();
        String userId = user.getUserId();
        transferCustInfoList.forEach(it -> {
            CmTransferCust cmTransferCust = new CmTransferCust();
            cmTransferCust.setCustconscode(it.getDisCustconsCode());
            cmTransferCust.setConcustno(it.getConcustno());
            if (null != it.getReason()) {
                cmTransferCust.setReason(new BigDecimal(TransReasonEumn.getEnum(it.getReason())));
            }
            if (null != it.getRemarks()) {
                cmTransferCust.setRemarks(it.getRemarks());
            }
            cmTransferCust.setModiferuser(userId);
            cmTransferCustList.add(cmTransferCust);
        });
        return cmTransferCustList;
    }

    /**
     * @description:(校验导入数据的正确性)
     * @author: xfc
     * @date: 2023/4/10 18:10
     * @since JDK 1.8
     */
    private void checkConParams(String concustno, String custconscode, String disCustconsCode,
                                String disCustconsName, StringBuilder errorMsg) {
        // 根据客户号查询客户信息 获取客户姓名和客户的一账通账号
        QueryConscustInfoRequest queryConscustInfoRequest = new QueryConscustInfoRequest();
        queryConscustInfoRequest.setConscustno(concustno);
        QueryConscustInfoResponse queryConscustInfoResponse = queryConscustInfoService.queryConscustInfo(queryConscustInfoRequest);
        // 查询 客户号是否存在
        if (null == queryConscustInfoResponse || Objects.isNull(queryConscustInfoResponse.getConscustinfo())
                || StringUtils.isEmpty(queryConscustInfoResponse.getConscustinfo().getConscustno())) {
            errorMsg.append("客户号：").append(concustno).append("不存在");
            return;
        }
        ConscustInfoDomain conscustDto = queryConscustInfoResponse.getConscustinfo();
        // 查询原投顾号是否存在
        ConsultantSimpleInfoDto consultcodedto = consultantInfoService.getConsultantByCode(custconscode);
        if (null == consultcodedto || consultcodedto.getConsStatus().equals(StaticVar.CONSSTATUS_DEL)) {
            errorMsg.append("原投顾编号:").append(custconscode).append("不存在或状态异常，请修改后重新上传").append("<br>");
        }

        // 判断当前客户是否属于当前投顾
        if (!custconscode.equals(conscustDto.getConscode())) {
            errorMsg.append("客户号:").append(concustno).append("当前所属投顾不是").append(custconscode).append("请确认修改后重新上传" + "<br>");
        }

        // 查询分配投顾号是否存在
        ConsultantSimpleInfoDto disconsultantInfoDto = consultantInfoService.getConsultantByCode(disCustconsCode);
        if (null == disconsultantInfoDto || disconsultantInfoDto.getConsStatus().equals(StaticVar.CONSSTATUS_DEL)) {
            errorMsg.append("分配投顾编号:").append(disCustconsCode).append("不存在或状态异常，请修改后重新上传").append("<br>");
        } else if (!StringUtils.equals(disconsultantInfoDto.getConsName(), disCustconsName)) {
            // 导入失败！投顾客户号{1111}，其分配投顾和分配投顾编号不一致
            errorMsg.append("投顾客户号").append(concustno).append("，其分配投顾和分配投顾编号不一致").append("<br>");
        }

    }

    /**
     * 分配客户
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/batchAssignCustAll.do")
    public String batchAssignCustAll(HttpServletRequest request) throws Exception {
        User user = (User) request.getSession().getAttribute("loginUser");
        String result = "";
        String toConsCode = request.getParameter("toConsCode");
        String formConsCode = request.getParameter("formConsCode");
        String assignReason = request.getParameter("assignReason");
        String assignremarks = request.getParameter("assignremarks");
        if (StringUtils.isNotBlank(toConsCode) && StringUtils.isNotBlank(formConsCode)) {
            CustTranfInfo trnsinfo = (CustTranfInfo) CacheUtil.getObject(CacheCode.CONSTRANF, user.getUserId());
            // 没有当前投顾正在处理的转分配任务
            if (trnsinfo == null) {
                List<CustconstantInfoDomain> updatelist = new ArrayList<CustconstantInfoDomain>();
                QueryCustconstantInfoRequest rsqParam = new QueryCustconstantInfoRequest();
                Map<String, Object> newParam = new HashMap<String, Object>(1);
                newParam.put("conscode", formConsCode);
                rsqParam.setMapParams(newParam);
                QueryCustconstantInfoResponse listcc = queryCustconstantInfoService.queryListCustconstantInfo(rsqParam);
                if (listcc != null && listcc.getListCustconstantInfoDomain() != null && listcc.getListCustconstantInfoDomain().size() > 0) {
                    for (CustconstantInfoDomain cc : listcc.getListCustconstantInfoDomain()) {
                        if (!toConsCode.equals(cc.getConscode())) {
                            cc.setStartdt(DateTime.now().toString("yyyyMMdd"));
                            cc.setConscode(toConsCode);
                            cc.setNextcons(toConsCode);
                            cc.setReason(assignReason);
                            cc.setRemark(assignremarks);
                            cc.setModifier(user.getUserId());
                            cc.setModdt(DateTime.now().toString("yyyyMMdd"));
                            cc.setCreator(user.getUserId());
                            updatelist.add(cc);
                        }
                    }
                }
                // 调用接口新增投顾、修改投顾和插入历史记录
                if (updatelist != null && updatelist.size() > 0) {
                    //设置缓存，
                    CustTranfInfo info = new CustTranfInfo();
                    info.setFromconscode(formConsCode);
                    info.setToconscode(toConsCode);
                    info.setCurrent(0);
                    info.setTotal(updatelist.size());
                    CacheUtil.setObject(CacheCode.CONSTRANF, user.getUserId(), info);
                    //将需要处理的拆成100个一份的list集合
                    List<List<CustconstantInfoDomain>> listsubs = Lists.partition(updatelist, 100);
                    for (int i = 0; i < listsubs.size(); i++) {
                        CustTranfInfo in = (CustTranfInfo) CacheUtil.getObject(CacheCode.CONSTRANF, user.getUserId());
                        //缓存中存在当前投顾有未完成的任务时继续处理，如果不存在说明已经中断处理了
                        if (in != null) {
                            List<CustconstantInfoDomain> subupdatelist = listsubs.get(i);
                            UpdateCustconstantInfoRequest reqParam = new UpdateCustconstantInfoRequest();
                            reqParam.setListCustconstantInfoDomain(subupdatelist);
                            UpdateCustconstantInfoResponse updateCustconstantInfoResponse = updateCustconstantInfoService.batchUpdateCustConstant(reqParam);
                            if ("0000".equals(updateCustconstantInfoResponse.getReturnCode())) {
                                //更新当前缓存中的当前处理数据
                                in.setCurrent(in.getCurrent() + subupdatelist.size());
                                if (CacheUtil.getObject(CacheCode.CONSTRANF, user.getUserId()) != null) {
                                    CacheUtil.setObject(CacheCode.CONSTRANF, user.getUserId(), in);
                                    log.info("已处理条数：" + ((CustTranfInfo) CacheUtil.getObject(CacheCode.CONSTRANF, user.getUserId())).getCurrent());
                                }
                            }
                        } else {
                            break;
                        }
                    }
                    //删除当前投顾的处理数据
                    CacheUtil.deleteOne(CacheCode.CONSTRANF, user.getUserId());
                    result = "success";
                }

            } else {
                for (int i = 0; i < 1000; i++) {
                    Thread.sleep(2000L);
                    log.info("等待再次执行全库转分配" + i);
                    if ((CustTranfInfo) CacheUtil.getObject(CacheCode.CONSTRANF, user.getUserId()) == null) {
                        result = "success";
                        break;
                    }
                }
            }
        }
        return result;
    }

    @ResponseBody
    @RequestMapping("/ajaxGetCustTransfInfo.do")
    public void ajaxGetCustTransfInfo(HttpServletRequest request, HttpServletResponse response) {
        User user = (User) request.getSession().getAttribute("loginUser");
        CustTranfInfo info = (CustTranfInfo) CacheUtil.getObject(CacheCode.CONSTRANF, user.getUserId());
        String result = "success";
        if (info != null) {
            result = "正在处理从\"" + ConsOrgCache.getInstance().getAllConsMap().get(info.getFromconscode()) + "\"到\"" + ConsOrgCache.getInstance().getAllConsMap().get(info.getToconscode()) + "\"的转分配;已处理数:" + info.getCurrent() + "/" + info.getTotal();
            log.info(user.getUserId() + result);
        }
        response.setContentType("text/html; charset=utf-8");
        PrintWriter pw = null;
        try {
            pw = response.getWriter();
            pw.print(result);
            pw.flush();
        } catch (IOException e) {
            log.error("数据写入异常", e);
        } finally {
            if (pw != null) {
                pw.close();
            }
        }
    }

    @ResponseBody
    @RequestMapping("/BrokerCustTransfInfo.do")
    public void brokerCustTransfInfo(HttpServletRequest request, HttpServletResponse response) {
        User user = (User) request.getSession().getAttribute("loginUser");
        CustTranfInfo info = (CustTranfInfo) CacheUtil.getObject(CacheCode.CONSTRANF, user.getUserId());
        String result = "notask";
        if (info != null) {
            //删除当前投顾的处理数据
            CacheUtil.deleteOne(CacheCode.CONSTRANF, user.getUserId());
            result = "success";
        }
        response.setContentType("text/html; charset=utf-8");
        PrintWriter pw = null;
        try {
            pw = response.getWriter();
            pw.print(result);
            pw.flush();
        } catch (IOException e) {
            log.error("数据写入异常", e);
        } finally {
            if (pw != null) {
                pw.close();
            }
        }
    }

    @ResponseBody
    @RequestMapping("/ajaxGetCustVisitInfo.do")
    public void ajaxGetCustVisitInfo(HttpServletRequest request, HttpServletResponse response) {
        String conscustno = request.getParameter("conscustno");
        StringBuffer result = new StringBuffer();
        Map<String, String> param = new HashMap<String, String>(1);
        param.put("custno", conscustno);
        List<Map<String, String>> list = custconstantService.ajaxGetCustVisitInfo(param);
        if (list != null && list.size() > 0) {
            result.append("<table style='height:55px;width:400px;color:black'>");
            result.append("<tr><td width='25px'>序号</td><td width='390px'>拜访摘要</td></tr>");
            int i = 1;
            for (Map<String, String> map : list) {
                result.append("<tr>");
                result.append("<td>" + i + "</td>");
                result.append("<td>" + Util.ObjectToString(map.get("VISITSUMMARY")) + "</td>");
                result.append("</tr>");
                i++;
            }
            result.append("</table>");
        }
        response.setContentType("text/html; charset=utf-8");
        PrintWriter pw = null;
        try {
            pw = response.getWriter();
            pw.print(result.toString());
            pw.flush();
        } catch (IOException e) {
            log.error("数据写入异常", e);
        } finally {
            if (pw != null) {
                pw.close();
            }
        }

    }

    @ResponseBody
    @RequestMapping("/ajaxGetCustLabelInfo.do")
    public void ajaxGetCustLabelInfo(HttpServletRequest request, HttpServletResponse response) {
        String conscustno = request.getParameter("conscustno");
        StringBuffer result = new StringBuffer();
        Map<String, String> param = new HashMap<String, String>(1);
        param.put("conscustno", conscustno);
        List<CmCustLabel> list = cmCustLabelservice.listCmCustLabelLog(param);
        if (list != null && list.size() > 0) {
            result.append("<table style='height:55px;width:400px;color:black'>");
            result.append("<tr><td width='80px'>打标日期</td><td width='100px'>打标人</td><td width='80px'>资金量</td><td width='70px'>客户意向</td><td width='70px'>联系情况</td></tr>");
            for (CmCustLabel label : list) {
                result.append("<tr>");
                result.append("<td>" + label.getLabeldt() + "</td>");
                result.append("<td>" + ConsOrgCache.getInstance().getAllUserMap().get(label.getConscode()) + "</td>");
                result.append("<td>" + ConstantCache.getInstance().getVal("zjllabel", label.getZjllabel()) + "</td>");
                result.append("<td>" + ConstantCache.getInstance().getVal("khyxlabel", label.getKhyxlabel()) + "</td>");
                result.append("<td>" + ConstantCache.getInstance().getVal("lxqklabel", label.getLxqklabel()) + "</td>");
                result.append("</tr>");
            }
            result.append("</table>");
        }
        response.setContentType("text/html; charset=utf-8");
        PrintWriter pw = null;
        try {
            pw = response.getWriter();
            pw.print(result.toString());
            pw.flush();
        } catch (IOException e) {
            log.error("数据写入异常", e);
        } finally {
            if (pw != null) {
                pw.close();
            }
        }
    }

    @ResponseBody
    @RequestMapping("/getConsCodeComboxValue.do")
    public String getConsCodeComboxValue(HttpServletRequest request) {
        String userid = request.getParameter("userId");
        String gds = (String) request.getSession().getAttribute("topgddata");
        if (userid != null && "selina.huang".equals(userid)) {
            return userid;
        } else if (gds != null && (gds.equals(StaticVar.DATARANGE_GD_ALL) || gds.equals(StaticVar.DATARANGE_GD_ALL_NOWFP) || gds.equals(StaticVar.DATARANGE_GD_OUTLET))) {
            return "";
        } else if (gds != null && (gds.equals(StaticVar.DATARANGE_GD_TEARM) || gds.equals(StaticVar.DATARANGE_GD_SELF))) {
            return userid;
        } else {
            return userid;
        }
    }

    @ResponseBody
    @RequestMapping("/getCustLabelResult.do")
    public void getCustLabelResult(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String custno = request.getParameter("custno");
        String zjl = "";
        String khyx = "";
        String lxqk = "";
        if (StringUtils.isNotBlank(custno)) {
            Map<String, String> param = new HashMap<String, String>(1);
            param.put("conscustno", custno);
            CmCustLabel label = cmCustLabelservice.getCmCustLabel(param);
            if (label != null) {
                zjl = ObjectUtils.replaceNull(label.getZjllabel());
                khyx = ObjectUtils.replaceNull(label.getKhyxlabel());
                lxqk = ObjectUtils.replaceNull(label.getLxqklabel());
            }
        }
        //response.setCharacterEncoding("utf-8");
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter pw = null;
        try {
            pw = response.getWriter();
            pw.print(zjl + "#" + khyx + "#" + lxqk);
            pw.flush();
        } catch (IOException e) {
            log.error("数据返回异常", e);
        } finally {
            if (pw != null) {
                pw.close();
            }
        }
    }

    /**
     * 批量客户打标
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/batchCustLabel.do")
    public String batchCustLabel(HttpServletRequest request) throws Exception {
        User user = (User) request.getSession().getAttribute("loginUser");
        String result = "";
        String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
        String labelzjl = ObjectUtils.replaceNull(request.getParameter("labelzjl"));
        String labelkhyx = ObjectUtils.replaceNull(request.getParameter("labelkhyx"));
        String labellxqk = ObjectUtils.replaceNull(request.getParameter("labellxqk"));
        if (StringUtils.isNotBlank(ids)) {
            String[] custs = ids.split(",");
            //传过来的客户号
            List<String> list = new ArrayList<String>();
            //将数组转list
            CollectionUtils.addAll(list, custs);
            // 检查打标的客户是否有在申请划转的客户中
            Map<String, String> paramsql = new HashMap<String, String>(1);
            String sqlins = Util.getOracleSQLIn(list, 999, "conscustno");
            paramsql.put("sqlins", sqlins);
            int count = waitTransferCustService.getWaitTransCountByCustnos(paramsql);
            if (count > 0) {
                result = "hastransfer";
                return result;
            }
            List<CmCustLabelDomain> listlabels = new ArrayList<CmCustLabelDomain>();
            for (String custno : list) {
                CmCustLabelDomain obj = new CmCustLabelDomain();
                String id = commonService.getSeqValue("SEQ_CUSTREC");
                obj.setId(new BigDecimal(id));
                obj.setConscustno(custno);
                obj.setConscode(user.getUserId());
                QueryConscustInfoRequest req = new QueryConscustInfoRequest();
                req.setConscustno(custno);
                QueryConscustInfoResponse res = queryConscustInfoService.queryConscustInfo(req);
                if (res.getConscustinfo() != null) {
                    obj.setHboneno(res.getConscustinfo().getHboneno());
                } else {
                    return "paramError";
                }
                obj.setZjllabel(labelzjl);
                obj.setKhyxlabel(labelkhyx);
                obj.setLxqklabel(labellxqk);

                /**
                 * added by wu.long at 20190911
                 */
                StringBuilder sb = new StringBuilder();
                if (StringUtils.isNotBlank(labelzjl)) {
                    sb.append("/" + ConstantCache.getInstance().getVal("zjllabel", labelzjl));
                }
                if (StringUtils.isNotBlank(labelkhyx)) {
                    sb.append("/" + ConstantCache.getInstance().getVal("khyxlabel", labelkhyx));
                }
                if (StringUtils.isNotBlank(labellxqk)) {
                    sb.append("/" + ConstantCache.getInstance().getVal("lxqklabel", labellxqk));
                }
                String custlabelval = sb.toString();
                if (StringUtils.isNotBlank(custlabelval)) {
                    obj.setTotallabel(custlabelval.replaceFirst("/", ""));
                }

                obj.setLabeldt(DateTimeUtil.getDateFormat(new Date(), "yyyyMMdd"));
                listlabels.add(obj);
            }
            DealCmCustLabelRequest req = new DealCmCustLabelRequest();
            req.setListCmCustLabelDomain(listlabels);
            DealCmCustLabelResponse res = dealCmCustLabelService.dealCmCustLabel(req);
            if (res.isSuccessful()) {
                result = "success";
            } else {
                result = "inserterror";
            }
        } else {
            result = "paramError";
        }
        return result;
    }

    /**
     * 添加客户进组
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/batchAddGroup.do")
    public String batchAddGroup(HttpServletRequest request) throws Exception {
        User user = (User) request.getSession().getAttribute("loginUser");
        String result = "";
        String ids = request.getParameter("ids");
        String groupid = request.getParameter("groupid");
        if (StringUtils.isNotBlank(ids) && StringUtils.isNotBlank(groupid)) {
            cmCustomizegroupUserService.insertCustgroupUserBatch(ids, groupid, user.getUserId());
            result = "success";
        } else {
            result = "paramError";
        }
        return result;
    }

    /**
     * 导出操作
     *
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportCust.do")
    public void exportCust(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
        String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
        if (StringUtils.isNotBlank(ids)) {
            String[] custs = ids.split(",");
            //传过来的客户号
            List<String> list = new ArrayList<String>();
            //将数组转list
            CollectionUtils.addAll(list, custs);
            // 检查打标的客户是否有在申请划转的客户中
            Map<String, String> paramsql = new HashMap<String, String>(1);
            String sqlins = Util.getOracleSQLIn(list, 999, "t.conscustno");
            paramsql.put("sqlins", sqlins);
            List<Conscust> exportList = conscustService.selectExportCust(paramsql);
            List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
            if (CollectionUtils.isNotEmpty(exportList)) {
                User user = (User) request.getSession().getAttribute("loginUser");
                String ip = getIpAddr(request);
                for (Conscust cust : exportList) {
                    ConsOrgCache orgcache = ConsOrgCache.getInstance();
                    cust.setOrgname(orgcache.getAllOrgMap().get(orgcache.getCons2OutletMap().get(cust.getConscode())));
                    String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(cust.getConscode()));
                    if ("0".equals(uporgcode)) {
                        cust.setUporgname(cust.getOrgname());
                    } else {
                        cust.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
                    }
                    //导出日志
                    PageVisitLog pageVisitLog = new PageVisitLog();
                    pageVisitLog.setConscustno(cust.getConscustno());
                    pageVisitLog.setUserId(user.getUserId());
                    pageVisitLog.setVisitUrl(request.getRequestURI());
                    pageVisitLog.setOperation("投顾客户导出");
                    pageVisitLog.setVisitTime(new Date());
                    pageVisitLog.setPreid(null);
                    pageVisitLog.setIp(ip);
                    listlog.add(pageVisitLog);
                }
                for (PageVisitLog pageVisitLog : listlog) {
                    pageVisitLogService.recordLog(pageVisitLog);
                }
            }
            try {
                // 清空输出流
                response.reset();
                // 设置文件格式和名字
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition",
                        "attachment;fileName=" + new String("投顾客户.xls".getBytes("gb2312"), "ISO8859-1"));
                ServletOutputStream os = response.getOutputStream();

                String[] columnName = new String[]{"投顾客户号", "客户名称", "所在地区", "所属投顾","投顾编号", "所属区域", "所属部门"};

                String[] beanProperty = new String[]{"conscustno", "custname", "cityname", "consname", "conscode", "uporgname", "orgname"};
                ExcelWriter.writeExcel(os, "投顾客户", 0, exportList, columnName, beanProperty);
                os.close(); // 关闭流

            } catch (Exception e) {
                log.error("文件导出异常", e);
            }
        }
    }

    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || StaticVar.STR_UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    @ResponseBody
    @RequestMapping("/getCustMsg.do")
    public Map<String, Object> getCustMsg(HttpServletRequest request, HttpServletResponse response) {
        String conscustno = request.getParameter("conscustno");
        Conscust cust =null;
        if(StringUtil.isNotNullStr(conscustno)){
            cust = custService.getConscust(conscustno);
        }

        if (cust != null && StringUtil.isNotNullStr(cust.getIdnoCipher())) {
            CodecSingleResponse res = decryptSingleFacade.decrypt(cust.getIdnoCipher());
            String idno = res.getCodecText();
            cust.setIdno(idno);
        }

        Map<String, Object> map = new HashMap<String, Object>(1);
        map.put("cust", cust);
        return map;

    }

    /**
     * 跳转到高端客户历史分配查询页面
     *
     * @param request
     * @return
     */
    @RequestMapping("/queryCustConstantHis.do")
    public ModelAndView queryCustConstantHis(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        request.setAttribute("beginDate", DateTimeUtil.fmtDate(DateTimeUtil.addDaysFromNow(-30), "yyyyMMdd"));
        modelAndView.setViewName("/custinfo/custConstantHisList");
        return modelAndView;
    }

    /**
     * 高端客户历史分配查询
     *
     * @param request
     * @param params
     * @return
     * @throws Exception
     */
    @RequestMapping("/queryCustconstanthisList")
    @ResponseBody
    public Map<String, Object> queryCustconstanthisList(HttpServletRequest request, @RequestParam Map<String, String> params) throws Exception {
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>(16);
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();
        String conscustno = request.getParameter("conscustno");
        String custName = request.getParameter("custName");
        String optName = request.getParameter("optName");
        String begOptDt = request.getParameter("begOptDt");
        String endOptDt = request.getParameter("endOptDt");
        String fromConsCode = request.getParameter("fromConsCode");
        String toConsCode = request.getParameter("toConsCode");
        String fromOrgCode = request.getParameter("fromOrgCode");
        String toOrgCode = request.getParameter("toOrgCode");
        String reason = request.getParameter("reason");
        param.put("conscustno", conscustno);
        param.put("custName", custName);
        param.put("optName", optName);
        param.put("begOptDt", begOptDt);
        param.put("endOptDt", endOptDt);

        // 设置查询条件（投顾编码）
        if (StringUtils.isNotBlank(fromConsCode)) {
            param.put("fromConsCode", fromConsCode);
        } else {
            param.put("fromOrgCode", fromOrgCode);
        }

        // 设置查询条件（投顾编码）
        if (StringUtils.isNotBlank(toConsCode)) {
            param.put("toConsCode", toConsCode);
        } else {
            param.put("toOrgCode", toOrgCode);
        }

        param.put("reason", reason);
        Map<String, Object> returnMap = new HashMap<>(3);
        PageData<Map<String, Object>> pd = custService.listCustConstanthisByPage(param);
        List<Map<String, Object>> listMap = pd.getListData();
        if (CollectionUtils.isNotEmpty(listMap)) {
            for (Map<String, Object> map : listMap) {
                ConsOrgCache orgcache = ConsOrgCache.getInstance();
                String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(map.get("FROMCONS")));
                if ("0".equals(uporgcode)) {
                    map.put("FROMUPORGNAME", map.get("FROMORGNAME"));
                } else {
                    map.put("FROMUPORGNAME", orgcache.getAllOrgMap().get(uporgcode));
                }

                String toorgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(map.get("TOCONS")));
                if ("0".equals(toorgcode)) {
                    map.put("TOUPORGNAME", map.get("TOORGNAME"));
                } else {
                    map.put("TOUPORGNAME", orgcache.getAllOrgMap().get(toorgcode));
                }
            }
        }

        returnMap.put("total", pd.getPageBean().getTotalNum());
        returnMap.put("page", pd.getPageBean().getCurPage());
        returnMap.put("rows", pd.getListData());
        return returnMap;
    }

    /**
     * 导出操作
     *
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportCustconstanthisList.do")
    public void exportCustconstanthisList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
        String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
        if (StringUtils.isNotBlank(ids)) {
            String[] custs = ids.split(",");
            //传过来的客户号
            List<String> list = new ArrayList<String>();
            //将数组转list
            CollectionUtils.addAll(list, custs);
            // 检查打标的客户是否有在申请划转的客户中
            Map<String, String> paramsql = new HashMap<String, String>(1);
            String sqlins = Util.getOracleSQLIn(list, 999, "T.CUSTCONSHISID");
            paramsql.put("sqlins", sqlins);
            List<Map<String, Object>> exportList = conscustService.selectExportCustConstanthis(paramsql);

            if (CollectionUtils.isNotEmpty(exportList)) {
                for (Map<String, Object> map : exportList) {
                    ConsOrgCache orgcache = ConsOrgCache.getInstance();
                    String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(map.get("FROMCONS")));
                    if ("0".equals(uporgcode)) {
                        map.put("FROMUPORGNAME", map.get("FROMORGNAME"));
                    } else {
                        map.put("FROMUPORGNAME", orgcache.getAllOrgMap().get(uporgcode));
                    }

                    String toorgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(map.get("TOCONS")));
                    if ("0".equals(toorgcode)) {
                        map.put("TOUPORGNAME", map.get("TOORGNAME"));
                    } else {
                        map.put("TOUPORGNAME", orgcache.getAllOrgMap().get(toorgcode));
                    }
                }
            }

            try {
                // 清空输出流
                response.reset();
                // 设置文件格式和名字
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition",
                        "attachment;fileName=" + new String("分配历史记录导出.xls".getBytes("gb2312"), "ISO8859-1"));
                ServletOutputStream os = response.getOutputStream();

                String[] columnName = new String[]{"投顾客户号", "姓名", "原投顾", "原区域", "原部门", "分配投顾", "分配区域", "分配部门", "操作人", "操作日期", "分配原因", "客户状态", "备注"};

                String[] beanProperty = new String[]{"CUSTNO", "CUSTNAME", "FROMCONSNAME", "FROMUPORGNAME", "FROMORGNAME", "TOCONSNAME", "TOUPORGNAME", "TOORGNAME", "CREATOR", "CREDT", "REASON", "CUSTTRADETYPE", "REMARK"};
                ExcelWriter.writeExcel(os, "分配记录", 0, exportList, columnName, beanProperty);
                os.close(); // 关闭流
            } catch (Exception e) {
                log.error("文件导出异常", e);
            }
        }
    }

    /**
     * 导出操作
     *
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportCustconstanthisAll.do")
    public void exportCustconstanthisAll(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>(16);
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();
        String conscustno = request.getParameter("conscustno");
        String custName = request.getParameter("custName");
        //String optName = request.getParameter("optName");
        String begOptDt = request.getParameter("begOptDt");
        String endOptDt = request.getParameter("endOptDt");
        String fromConsCode = request.getParameter("fromConsCode");
        String toConsCode = request.getParameter("toConsCode");
        String fromOrgCode = request.getParameter("fromOrgCode");
        String toOrgCode = request.getParameter("toOrgCode");
        String reason = request.getParameter("reason");
        param.put("conscustno", conscustno);
        param.put("custName", custName);
        //param.put("optName", optName);
        param.put("begOptDt", begOptDt);
        param.put("endOptDt", endOptDt);

        // 设置查询条件（投顾编码）
        if (StringUtils.isNotBlank(fromConsCode)) {
            param.put("fromConsCode", fromConsCode);
        } else {
            param.put("fromOrgCode", fromOrgCode);
        }

        // 设置查询条件（投顾编码）
        if (StringUtils.isNotBlank(toConsCode)) {
            param.put("toConsCode", toConsCode);
        } else {
            param.put("toOrgCode", toOrgCode);
        }

        param.put("reason", reason);
        //到处查询，只有此界面的三个导出使用，功能相同，所以数据写在SQL中，方便大量数据
        List<Map<String, Object>> exportList = conscustService.selectExportCustConstanthis(param);
        if (CollectionUtils.isNotEmpty(exportList) && exportList.size() > 60000) {
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write("导出数据量超过6万，请重新选择!");
        } else {
            List<ExportCustcontantHisVo> listexport = new ArrayList<>();

            if (CollectionUtils.isNotEmpty(exportList)) {

                ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
                Map<String, String> upOrgMap = consOrgCache.getUpOrgMapCache();
                Map<String, String> cons2OutletMap = consOrgCache.getCons2OutletMap();
                Map<String, String> allOrgMap = consOrgCache.getAllOrgMap();
                Map<String, String> provCityMap = ConstantCache.getInstance().getProvCityMap();

                for (Map<String, Object> map : exportList) {
                    listexport.add(createExportVo(map, upOrgMap, cons2OutletMap, allOrgMap, provCityMap));
                }

                com.alibaba.excel.ExcelWriter excelWriter = null;
                try {
                    // 设置导出内容
                    response.setContentType("application/vnd.ms-excel");
                    response.setCharacterEncoding("utf-8");

                    // 设置文件名称
                    String fileName = URLEncoder.encode("高端客户历史分配记录导出", "UTF-8");
                    response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
                    excelWriter = EasyExcel.write(response.getOutputStream(), ExportCustcontantHisEntity.class).build();
                    WriteSheet writeSheet = EasyExcel.writerSheet("分配记录").build();
                    excelWriter.write(listexport, writeSheet);
                } catch (Exception e) {
                    log.error("文件导出异常" + e.getMessage());
                } finally {
                    // 关闭流操作
                    if (excelWriter != null) {
                        excelWriter.finish();
                    }
                }
            }
        }

    }

    /**
     * @return com.howbuy.crm.hb.domain.conscust.ExportCustcontantHisVo
     * @throws
     * @Title: createExportVo
     * @Author: 转换数据，仅用于此界面导出方法，客服导出和全量导出，删除多余的3个查询SQL，整合到列表中
     * @DateTime: 2023/2/21 9:24
     * @param: [map]
     */
    private ExportCustcontantHisVo createExportVo(Map<String, Object> map, Map<String, String> upOrgMap, Map<String, String> cons2OutletMap, Map<String, String> allOrgMap, Map<String, String> provCityMap) {
        ExportCustcontantHisVo vo = new ExportCustcontantHisVo();
        vo.setCustNo(StringUtil.replaceNullStr(map.get("CUSTNO")));
        vo.setCustName(StringUtil.replaceNullStr(map.get("CUSTNAME")));
        vo.setFromConsName(StringUtil.replaceNullStr(map.get("FROMCONSNAME")));
        vo.setFromOrgName(StringUtil.replaceNullStr(map.get("FROMORGNAME")));
        vo.setToConsName(StringUtil.replaceNullStr(map.get("TOCONSNAME")));
        vo.setToOrgName(StringUtil.replaceNullStr(map.get("TOORGNAME")));
        vo.setCreator(StringUtil.replaceNullStr(map.get("CREATOR")));
        vo.setCredt(StringUtil.replaceNullStr(map.get("CREDT")));
        vo.setReason(StringUtil.replaceNullStr(map.get("REASON")));
        vo.setCustTradeType(StringUtil.replaceNullStr(map.get("CUSTTRADETYPE")));
        vo.setRemark(StringUtil.replaceNullStr(map.get("REMARK")));
        vo.setArea(StringUtil.replaceNullStr(map.get("AREA")));
        vo.setWifiChannel(StringUtil.replaceNullStr(map.get("WIFICHANNEL")));
        vo.setCustSource(StringUtil.replaceNullStr(map.get("CUSTSOURCE")));
        vo.setDealType(StringUtil.replaceNullStr(map.get("DEALTYPE")));
        vo.setCustType(StringUtil.replaceNullStr(map.get("CUSTTYPE")));
        vo.setProvCode(StringUtil.replaceNullStr(map.get("PROVCODE")));
        vo.setCityCode(StringUtil.replaceNullStr(map.get("CITYCODE")));
        vo.setWifiChannel(StringUtil.replaceNullStr(map.get("WIFICHANNEL")));
        vo.setDealType(StringUtil.replaceNullStr(map.get("DEALTYPE")));
        vo.setCustSource(StringUtil.replaceNullStr(map.get("CUSTSOURCE")));
        vo.setArea(StringUtil.replaceNullStr(map.get("AREA")));
        //此部分逻辑唯一但是写了2次，因此移入列表SQL，删除3个查询SQL
        String uporgcode = upOrgMap.get(cons2OutletMap.get(map.get("FROMCONS")));
        if ("0".equals(uporgcode)) {
            vo.setFromUporgName(vo.getFromOrgName());
        } else {
            vo.setFromUporgName(allOrgMap.get(uporgcode));
        }

        String toorgcode = upOrgMap.get(cons2OutletMap.get(map.get("TOCONS")));
        if ("0".equals(toorgcode)) {
            vo.setToUporgName(vo.getToOrgName());
        } else {
            vo.setToUporgName(allOrgMap.get(toorgcode));
        }

        if (StringUtils.isNotBlank(vo.getProvCode())) {
            vo.setProvCode(provCityMap.get(vo.getProvCode()));
        }

        if (StringUtils.isNotBlank(vo.getCityCode())) {
            vo.setCityCode(provCityMap.get(vo.getCityCode()));
        }
        return vo;
    }

    /**
     * 导出操作
     *
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportCustconstanthisCustList.do")
    public void exportCustconstanthisCustList(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
        String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
        if (StringUtils.isNotBlank(ids)) {
            String[] custs = ids.split(",");
            //传过来的客户号
            List<String> list = new ArrayList<String>();
            //将数组转list
            CollectionUtils.addAll(list, custs);
            // 检查打标的客户是否有在申请划转的客户中
            Map<String, String> paramsql = new HashMap<String, String>(1);
            String sqlins = Util.getOracleSQLIn(list, 999, "T.CUSTCONSHISID");
            paramsql.put("sqlins", sqlins);
            List<Map<String, Object>> exportList = conscustService.selectExportCustConstanthis(paramsql);

            if (CollectionUtils.isNotEmpty(exportList)) {

                List<ExportCustcontantHisVo> listexport = Lists.newArrayList();

                ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
                Map<String, String> upOrgMap = consOrgCache.getUpOrgMapCache();
                Map<String, String> cons2OutletMap = consOrgCache.getCons2OutletMap();
                Map<String, String> allOrgMap = consOrgCache.getAllOrgMap();
                Map<String, String> provCityMap = ConstantCache.getInstance().getProvCityMap();

                for (Map<String, Object> map : exportList) {
                    listexport.add(createExportVo(map, upOrgMap, cons2OutletMap, allOrgMap, provCityMap));
                }

                com.alibaba.excel.ExcelWriter excelWriter = null;
                try {
                    // 设置导出内容
                    // 清空输出流
                    response.reset();
                    response.setContentType("application/vnd.ms-excel");
                    response.setCharacterEncoding("utf-8");

                    // 设置文件名称
                    String fileName = URLEncoder.encode("分配历史记录(客服)导出", "UTF-8");
                    response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xls");
                    excelWriter = EasyExcel.write(response.getOutputStream(), ExportCustcontantHisEntity.class).build();
                    WriteSheet writeSheet = EasyExcel.writerSheet("分配记录").build();
                    excelWriter.write(listexport, writeSheet);
                } catch (Exception e) {
                    log.error("文件导出异常:{}", e.getMessage());
                } finally {
                    // 关闭流操作
                    if (excelWriter != null) {
                        excelWriter.finish();
                    }
                }
            }
        }
    }

    /**
     * 展示修改页面
     *
     * @param request
     * @return Map<String, Object>
     * @throws Exception
     */
    @RequestMapping(value = "/viewCustconstanthis", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> viewCustconstanthis(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>(3);
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        String id = request.getParameter("id");
        if (StringUtils.isNotBlank(id)) {
            Map<String, String> param = new HashMap<String, String>(1);
            param.put("id", id);
            Map<String, Object> map = conscustService.getCustConstanthis(param);
            resultMap.put("domain", map);
        } else {
            resultMap.put("errorMsg", "操作失败：id不能为空");
            resultMap.put("errorCode", "9999");
        }

        return resultMap;
    }

    /**
     * 更新
     *
     * @param request
     * @return Map<String, Object>
     * @throws Exception
     */
    @RequestMapping(value = "/updateCustconstanthis", method = RequestMethod.POST)
    @ResponseBody
    public String updateCustconstanthis(HttpServletRequest request) {
        String result = "success";
        String id = request.getParameter("id");
        String nextcons = request.getParameter("toconscode");
        String reason = request.getParameter("reason");
        String custtradetype = request.getParameter("custtradetype");
        String remark = request.getParameter("remark");
        User user = (User) request.getSession().getAttribute("loginUser");
        if (!StringUtils.isEmpty(id)) {
            Map<String, String> param = new HashMap<>(7);
            param.put("id", id);
            param.put("reason", reason);
            param.put("custtradetype", custtradetype);
            param.put("remark", StringUtil.replaceNull(remark));
            param.put("modifier", user.getUserId());
            param.put("moddt", DateTimeUtil.getCurDate());
            if (StringUtil.isNotNullStr(nextcons)) {
                param.put("nextcons", nextcons);
            }
            conscustService.updateCustConstanthis(param);
            log.info(user.getUserId() + "更新了一条id是" + id + "的客户分配历史信息！");
        } else {
            log.info("更新客户历史记录id为null");
        }
        return result;
    }

    /**
     * 跳转到投顾客户列表
     *
     * @param request
     * @return
     */
    @RequestMapping("/custDepositFlow.do")
    public ModelAndView custDepositFlow(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/custinfo/custDepositFlow");
        modelAndView.addObject("arrivalDtEnd", DateTime.now().toString("yyyyMMdd"));
        Date date = DateTimeUtil.addDaysFromNow(-6);
        modelAndView.addObject("arrivalDtStart", DateTimeUtil.convertDateToString("yyyyMMdd", date));
        return modelAndView;
    }


    @RequestMapping(value = "/validateCustDepositFlowParam.do", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, String> validateCustDepositFlowParam(HttpServletRequest request) {
        Map<String, String> resultMap = new HashMap<String, String>(2);
        String custName = request.getParameter("custName");
        String hboneNo = request.getParameter("hboneNo");
        String arrivalDtStart = request.getParameter("arrivalDtStart");
        String arrivalDtEnd = request.getParameter("arrivalDtEnd");
        String consCode = request.getParameter("consCode");
        String orgCode = request.getParameter("orgCode");
        //1:查询；2：全量查询
        String type = request.getParameter("type");

        // 如果是部分查询，则要输入客户姓名
        if (StringUtils.isBlank(custName) && "1".equals(type)) {
            resultMap.put("errorCode", "9999");
            resultMap.put("errorMsg", "请先输入需要查询的客户姓名!");
            return resultMap;
        }

        if (StringUtils.isBlank(arrivalDtStart) || StringUtils.isBlank(arrivalDtEnd)) {
            resultMap.put("errorCode", "9999");
            resultMap.put("errorMsg", "请先输入需要查询的到账日期!");
            return resultMap;
        } else {
            // 获取时间间隔
            int days = DateTimeUtil.getBetweenDays(arrivalDtStart, arrivalDtEnd);
            if (Integer.parseInt(arrivalDtEnd) < Integer.parseInt(arrivalDtStart)) {
                resultMap.put("errorCode", "9999");
                resultMap.put("errorMsg", "查询到账日期开始日期不能大于结束日期!");
                return resultMap;
            }
            // 校验时间区间范围为最近7天的数据
            if (days > 7) {
                resultMap.put("errorCode", "9999");
                resultMap.put("errorMsg", "查询到账日期区间不得超过7天!");
                return resultMap;
            }
        }

        resultMap = validateCust(custName, hboneNo, consCode, orgCode, request);
        return resultMap;
    }


    private Map<String, String> validateCust(String custName, String hboneNo, String consCode, String orgCode, HttpServletRequest request) {
        Map<String, String> resultMap = new HashMap<String, String>(2);
        resultMap.put("errorCode", "0000");
        resultMap.put("errorMsg", "成功!");
        if (StringUtils.isBlank(custName) && StringUtils.isBlank(hboneNo)) {
            return resultMap;
        }

        List<Conscust> list = listQualifiedConscust(custName, hboneNo, consCode, orgCode, request);
        log.info("validateCust parameters . custName:{}, hboneNo:{}, consCode:{}, orgCode:{}, resultList.size:{}",
                custName, hboneNo, consCode, orgCode, list == null ? 0 : list.size());

        if (CollectionUtils.isEmpty(list)) {
            resultMap.put("errorCode", "9999");
            resultMap.put("errorMsg", "该客户不存在!");
        }

        return resultMap;
    }

    /**
     * 获取所属投顾控件选择的部门或投顾下符合条件的CRM客户
     *
     * @param custName 客户姓名
     * @param hboneNo  客户一账通号
     * @param consCode 投顾号
     * @param orgCode  投顾部门code
     * @param request
     * @return
     */
    private List<Conscust> listQualifiedConscust(String custName, String hboneNo, String consCode, String orgCode, HttpServletRequest request) {
        Map<String, String> pmap = new HashMap<String, String>(5);

        pmap.put("hboneNo", hboneNo);
        pmap.put("custName", custName);
        if (StringUtils.isNotBlank(consCode)) {
            pmap.put("conscode", consCode);
        } else {
            //选择了未分配组
            if (orgCode.startsWith(StaticVar.STR_OTHER)) {
                pmap.put("othertearm", orgCode.replaceFirst("other", ""));
            } else {
                String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
                //选择了团队
                if (StaticVar.ORGTYPE_TEAM.equals(orgType)) {
                    pmap.put("teamcode", orgCode);
                } else {
                    if (!StaticVar.HOWBUY_ORGCODE.equals(orgCode)) {
                        List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                        pmap.put("outletcodes", ObjectUtils.getSqlInStr(suborgs));
                    } else {
                        //判断是否有权限看到全部客户（包括未分配客户）
                        String topgd = (String) request.getSession().getAttribute("topgddata");
                        if (!StaticVar.DATARANGE_GD_ALL.equals(topgd)) {
                            pmap.put("notCanSeeConsNullFlag", "true");
                        }
                    }
                }
            }
        }

        List<Conscust> list = conscustService.listConscustnoByCondition(pmap);
        return list;
    }

    /**
     * 投顾客户查询
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/custDepositFlowList.do")
    public Map<String, Object> custDepositFlowList(HttpServletRequest request) throws Exception {
        String custName = request.getParameter("custName");
        String arrivalDtStart = request.getParameter("arrivalDtStart");
        String arrivalDtEnd = request.getParameter("arrivalDtEnd");
        String hboneNo = request.getParameter("hboneNo");
        String consCode = request.getParameter("consCode");
        String orgCode = request.getParameter("orgCode");
        //账户类型:1-公募；2-私募
        String acctType = request.getParameter("acctType");
        //是否关联预约:1-是；2-否
        String isRelatedAcct = request.getParameter("isRelatedAcct");

        Map<String, Object> resultMap = new HashMap<String, Object>(2);

        Map<String, Object> param = new HashMap<String, Object>(4);
        param.put("custname", StringUtils.isBlank(custName) ? null : custName);
        param.put("vouchDtStart", StringUtils.isBlank(arrivalDtStart) ? null : arrivalDtStart);
        param.put("vouchDtEnd", StringUtils.isBlank(arrivalDtEnd) ? null : arrivalDtEnd);
        param.put("hboneNo", StringUtils.isBlank(hboneNo) ? null : hboneNo);
        param.put("acctType", StringUtils.isBlank(acctType) ? null : acctType);
        param.put("publicBankAcct", StaticVar.PUBLIC_BANK_ACCT);
        param.put("isRelatedAcct", StringUtils.isBlank(isRelatedAcct) ? null : isRelatedAcct);

        String resultStr = "";
        int noHboneNo = cmCustTransferDetailService.getTransferDetailNullHboneCount(param);
        if (noHboneNo != 0) {
            resultStr = "查到" + noHboneNo + "笔姓名为" + custName + "的到账，具体是否为该客户到账需线下与后台同事确认!";
        }

        List<CmCustTransferDetail> list = new ArrayList<>();
        if (StringUtils.isNotBlank(custName) || StringUtils.isNotBlank(hboneNo)) {
            // 姓名或一账通号至少输了一个的情况下，先过滤出符合条件的CRM客户
            List<Conscust> conscustList = listQualifiedConscust(custName, hboneNo, consCode, orgCode, request);
            // 获取CRM客户的一账通号（忽略一账通号为空的）
            List<String> hboneNoList = conscustList.stream().map(Conscust::getHboneno)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hboneNoList)) {
                // 有一账通号则到网银流水中按一账通号及其他条件（到账时间、账户类型、是否关联预约）进行过滤
                param.put("hboneNos", ListUtil.splitList(hboneNoList));
                list = cmCustTransferDetailService.listCmCustTransferDetail(param);
            }
        } else {
            // 姓名或一账通号都没有输入的情况
            List<CmCustTransferDetail> listCmCustTransferDetail = cmCustTransferDetailService.listCmCustTransferDetail(param);
            if (CollectionUtils.isNotEmpty(listCmCustTransferDetail)) {
                // 全量查询中custName和hboneNo都为空的情况下，直接查询资金流水，然后过滤出当前用户有查看权限的客户数据
                list = listCmCustTransferDetail.stream().filter(cmCustTransferDetail -> {
                    String transferDetailHboneNo = cmCustTransferDetail.getHboneNo();
                    if (StringUtils.isNotBlank(transferDetailHboneNo)) {
                        Map<String, String> validateMap = validateCust(null, transferDetailHboneNo, consCode, orgCode, request);
                        return "0000".equals(validateMap.get("errorCode"));
                    }
                    return false;
                }).collect(Collectors.toList());
            }
        }

        List<String> bmIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            //处理 是否关联账户
            for (CmCustTransferDetail cmCustTransferDetail : list) {
                bmIdList.add(cmCustTransferDetail.getDepositSid());
            }
            Map<String, Integer> relationCountMap = preControlHbService.selectMatchRelationCount(bmIdList);
            if (MapUtils.isNotEmpty(relationCountMap)) {
                for (CmCustTransferDetail cmCustTransferDetail : list) {
                    if (relationCountMap.get(cmCustTransferDetail.getDepositSid()) != null) {
                        cmCustTransferDetail.setIsRelatedAcct("是");
                    } else {
                        cmCustTransferDetail.setIsRelatedAcct("否");
                    }
                }
            }
        }

        resultMap.put("total", list.size());
        resultMap.put("rows", list);
        resultMap.put("resultStr", resultStr);

        return resultMap;
    }

    public Map<String, String> buildParamMap(HttpServletRequest request) throws Exception {
        Map<String, String> pmap = new ParamUtil(request).getParamMap();
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        if (StringUtils.isNotBlank(consCode)) {
            pmap.put("conscode", consCode);
        } else {
            //选择了未分配组
            if (orgCode.startsWith(StaticVar.STR_OTHER)) {
                pmap.put("othertearm", orgCode.replaceFirst("other", ""));
            } else {
                String orgType = ConsOrgCache.getInstance().getAllOrgTypeMap().get(orgCode);
                //选择了团队
                if (StaticVar.ORGTYPE_TEAM.equals(orgType)) {
                    pmap.put("teamcode", orgCode);
                } else {
                    if (!StaticVar.HOWBUY_ORGCODE.equals(orgCode)) {
                        List<String> suborgs = ConsOrgCache.getInstance().getAllOrgSubsMap().get(orgCode);
                        pmap.put("outletcodes", ObjectUtils.getSqlInStr(suborgs));
                    } else {
                        //判断是否有权限看到全部客户（包括未分配客户）
                        String topgd = (String) request.getSession().getAttribute("topgddata");
                        if (!StaticVar.DATARANGE_GD_ALL.equals(topgd)) {
                            pmap.put("notCanSeeConsNullFlag", "true");
                        }
                    }
                }
            }
        }
        return pmap;
    }


    /**
     * @description:(页面展示 处理列表翻译)
     * @param sourceList
     * @return void
     * @author: haoran.zhang
     * @date: 2025/5/22 10:09
     * @since JDK 1.8
     */
    private void translateSourceDataList(List<SyncCmsCustSource>  sourceList){
        if(CollectionUtils.isEmpty(sourceList)){
            return;
        }
        //分享人投顾列表
        List<String> shareConsCodeList=
                sourceList.stream().filter(syncCmsCustSource -> StringUtils.isNotBlank(syncCmsCustSource.getShareRelateConscode()))
                .map(SyncCmsCustSource::getShareRelateConscode).collect(Collectors.toList());
        //分享人 投顾  所属中心
        //批量获取投顾信息
        Map<String, ConsultantSimpleResponse>  consultantSimpleResponseMap = consultantOuterService.batchQueryConsultantSimple(shareConsCodeList);

        ConsOrgCache orgcache = ConsOrgCache.getInstance();
        Map<String, String> allConsMap = orgcache.getAllConsMap();
        Map<String, String> cons2OutletMap = orgcache.getCons2OutletMap();
        Map<String, String> allOrgMap= orgcache.getAllOrgMap();
        for (SyncCmsCustSource v : sourceList) {
            // 分享人相关投顾
            String shareRelateConscode = v.getShareRelateConscode();
            if (StringUtils.isNotBlank(shareRelateConscode)) {

                v.setShareRelateConsname(allConsMap.get(shareRelateConscode));
                //分享人相关投顾所属部门
                v.setShareOrgname(allOrgMap.get(cons2OutletMap.get(shareRelateConscode)));
                //分享人相关投顾所属区域
                String uporgcode = orgcache.getUpOrgMapCache().get(cons2OutletMap.get(shareRelateConscode));
                if (StaticVar.HOWBUY_ORGCODE.equals(uporgcode)) {
                    v.setShareUporgname(v.getShareOrgname());
                } else {
                    v.setShareUporgname(allOrgMap.get(uporgcode));
                }
                //分享人投顾 所属中心
                if(consultantSimpleResponseMap.containsKey(shareRelateConscode)){
                    ConsultantSimpleResponse simpleConsult = consultantSimpleResponseMap.get(shareRelateConscode);
                    String centerOrgCode = simpleConsult.getCenterOrgCode();
                    v.setShareCenterOrgCode(centerOrgCode);
                    v.setShareCenterOrgName(allOrgMap.get(centerOrgCode));
                }
            }
            if (YesNoEnum.N.getCode().equals(v.getRegOrLogin())) {
                v.setRegOrLoginName("登陆");
            } else if (YesNoEnum.Y.getCode().equals(v.getRegOrLogin())) {
                v.setRegOrLoginName("注册");
            }
        }
    }

    /**
     * 跳转到投顾分享列表
     *
     * @param request
     * @return
     */
    @RequestMapping("/syncCmsCustSourceList.do")
    public ModelAndView syncCmsCustSourceList(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/custinfo/listSyncCmsCustSource");
        return modelAndView;
    }


    /**
     * 投顾分享查询
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listSyncCmsCustSource.do")
    public Map<String, Object> listSyncCmsCustSource(HttpServletRequest request) throws Exception {
        // 设置查询分页参数
        Map<String, String> params = buildParamMap(request);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        log.info("/listSyncCmsCustSource.do:" + JSON.toJSON(params));
        PageData<SyncCmsCustSource> pd = conscustService.listSyncCmsCustSourceByPage(params);
        List<SyncCmsCustSource> list = pd.getListData();
        translateSourceDataList(list);

        resultMap.put("total", pd.getPageBean().getTotalNum());
        resultMap.put("rows", pd.getListData());
        return resultMap;
    }


    /**
     * 跳转到视频直播访问数据
     *
     * @param request
     * @return
     */
    @RequestMapping("/syncYxsLive.do")
    public ModelAndView syncYxsLive(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/custinfo/syncYxsLive");
        return modelAndView;
    }

    /**
     * 视频直播访问数据 ajax 数据
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listSyncYxsLive.do")
    public PageResult<CustYxsLivePo> listSyncYxsLive(CustYxsLiveVo vo, HttpServletRequest request) {
        // 设置查询分页参数

        fillAuthVo(request, vo);
        PageResult<CustYxsLivePo> pageResult = custYxsLiveStaticService.selectPageByVo(vo);
        return pageResult;
    }


    /**
     * 客户来源渠道来源 1-APP 2-WAP 3-其他
     *
     * @param source
     * @return
     */
    private String transferSource(String source) {
        //客户来源渠道来源 1-APP 2-WAP 3-其他
        if ("1".equals(source)) {
            return "APP";
        } else if ("2".equals(source)) {
            return "WAP";
        } else if ("3".equals(source)) {
            return "其他";
        }
        return "";
    }

    /**
     * 导出【视频访问数据（直播&回放）】
     *
     * @param request
     * @param response
     * @return Map<String, Object>
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/exportSyncYxsLive.do")
    public Map<String, Object> exportSyncYxsLive(CustYxsLiveVo vo,
                                                 HttpServletRequest request,
                                                 HttpServletResponse response) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>(2);

        fillAuthVo(request, vo);
        List<CustYxsLivePo> exportList = custYxsLiveStaticService.selectListByVo(vo);

        // 判断导出List是否为空
        if (CollectionUtils.isNotEmpty(exportList)) {
            for (CustYxsLivePo v : exportList) {
                v.setTradeFlag(YesNoEnum.Y.getCode().equals(v.getTradeFlag()) ? "是" : "否");
                v.setShareFlag(YesNoEnum.Y.getCode().equals(v.getShareFlag()) ? "是" : "否");
                v.setCustFlag(YesNoEnum.Y.getCode().equals(v.getCustFlag()) ? "是" : "否");
                v.setSource(transferSource(v.getSource()));
            }
            ServletOutputStream os = null;
            try {
                // 清空输出流
                response.reset();
                // 设置文件格式和名字
                response.setContentType("multipart/form-data");
                String titleName = "视频访问数据-" + DateUtil.getDateYYYYMMDD() + ".xls";
                response.setHeader("Content-Disposition", "attachment;fileName=" + new String(titleName.getBytes("gb2312"), "ISO8859-1"));
                os = response.getOutputStream();
                ExcelWriter.writeExcel(os, "视频访问数据（直播&回放）", 0, exportList, new String[][]{
                        {"访问日期", "countDate"},
                        {"视频ID", "id"},
                        {"视频标题", "title"},
                        {"一账通号", "hboneNo"},
                        {"投顾客户号", "conscustNo"},
                        {"客户姓名", "custName"},
                        {"所属投顾", "consName"},
                        /*{"客户一账通", "hboneno"},*/
                        {"中心（所属投顾）", "u1Name"},
                        {"区域（所属投顾）", "u2Name"},
                        {"分公司（所属投顾）", "u3Name"},
                        /*{"分享人一账通", "shareHboneno"},*/
                        {"是否分享", "shareFlag"},
                        {"是否新客", "custFlag"},
                        {"是否成交", "tradeFlag"},
                        {"渠道来源", "source"}
                });
            } catch (Exception e) {
                log.error("文件导出异常", e);
            } finally {
                // 关闭流
                if (os != null) {
                    try {
                        os.close();
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }

            resultMap.put("msg", "success");
        } else {
            resultMap.put("msg", "noData");
        }
        return resultMap;
    }


    /**
     * 导出投顾分享
     *
     * @param request
     * @param response
     * @return Map<String, Object>
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/exportSyncCmsCustSource.do")
    public Map<String, Object> exportSyncCmsCustSource(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        // 获取选中的订单编号
        Map<String, String> params = buildParamMap(request);
        List<SyncCmsCustSource> exportList = conscustService.listSyncCmsCustSource(params);

        // 判断导出List是否为空
        if (exportList != null) {
            //翻译 处理  页面展示
            translateSourceDataList(exportList);

            ServletOutputStream os = null;
            try {
                // 清空输出流
                response.reset();
                // 设置文件格式和名字
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition", "attachment;fileName=" + new String("投顾分享.xls".getBytes("gb2312"), "ISO8859-1"));
                os = response.getOutputStream();
                ExcelWriter.writeExcel(os, "投顾分享明细", 0, exportList, new String[][]{
                        {"登陆/注册", "regOrLoginName"},
                        {"登陆/注册时间", "regTime"},
                        {"客户姓名", "custname"},
                        {"投顾客户号", "conscustno"},
                        {"来源分类", "sourceType"},
                        {"来源页面标题", "sourceTitle"},
                        {"来源页面", "sourceUrl"},
                        /*{"客户一账通", "hboneno"},*/
                        {"分享人是否投顾", "shareTgName"},
                        {"分享人姓名", "shareCustname"},
                        {"分享人投顾客户号", "shareConscustno"},
                        /*{"分享人一账通", "shareHboneno"},*/
                        {"分享人相关投顾", "shareRelateConsname"},
                        {"分享人所属区域", "shareUporgname"},
                        {"分享人所属部门", "shareOrgname"},
                        {"分享人所属中心", "shareCenterOrgName"}
                });
            } catch (Exception e) {
                log.error("文件导出异常", e);
            } finally {
                // 关闭流
                if (os != null) {
                    try {
                        os.close();
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }

            resultMap.put("msg", "success");
        } else {
            resultMap.put("msg", "noData");
        }
        return resultMap;
    }

    /**
     * 批量修改分配原因
     *
     * @param request
     * @return String
     */
    @ResponseBody
    @RequestMapping("/batchUpdateAssignReason.do")
    public String batchUpdateAssignReason(HttpServletRequest request) {
        String result = null;
        String ids = request.getParameter("ids");
        //分配原因
        String reason = request.getParameter("reason");
        if (StringUtils.isNotBlank(ids) && StringUtil.isNotNullStr(reason)) {
            String[] idsArray = ids.split(",");
            List<Map<String, String>> list = new ArrayList<Map<String, String>>();
            for (int i = 0; i < idsArray.length; i++) {

                Map<String, String> param = new HashMap<>(2);
                param.put("id", idsArray[i]);
                param.put("reason", reason);
                list.add(param);
            }
            conscustService.batchUpdateCustConstanthis(list);
            result = "success";
        } else {
            result = "paramError";
        }
        return result;
    }

    /**
     * 批量撤销分配
     *
     * @param request
     * @return String
     */
    @ResponseBody
    @RequestMapping("/batchCancelAssign.do")
    public String batchCancelAssign(HttpServletRequest request) {
        String result = null;
        String ids = request.getParameter("ids");
        if (StringUtils.isNotBlank(ids)) {
            String[] idsArray = ids.split(",");
            List<String> list = new ArrayList<String>();
            //存在多次分配
            List<String> hasassign = new ArrayList<String>();
            //分配投顾已经离职
            List<String> consend = new ArrayList<String>();
            for (int i = 0; i < idsArray.length; i++) {
                String id = idsArray[i];
                Map<String, String> param = new HashMap<String, String>(1);
                param.put("id", id);
                Map<String, Object> obj = cmCustconstanthisService.getCancelCheckInfo(param);
                if (obj == null) {
                    result = "paramError";
                    return result;
                } else {
                    if (StringUtil.isNull(obj.get("CONSCODE"))) {
                        hasassign.add(StringUtil.replaceNullStr(obj.get("CUSTNO")));
                    } else {
                        if (!"1".equals(StringUtil.replaceNullStr(obj.get("CONSSTATUS")))) {
                            consend.add(StringUtil.replaceNullStr(obj.get("CUSTNO")));
                        }
                    }
                }
                list.add(id);
            }
            StringBuilder sb = new StringBuilder();
            if (hasassign.size() > 0) {
                sb.append("客户号：");
                sb.append(String.join(",", removeDouList(hasassign)));
                sb.append("已进行后续分配，不能撤销!");
                result = sb.toString();
                return result;
            }
            if (StringUtil.isNull(sb.toString()) && consend.size() > 0) {
                sb.append("客户号：");
                sb.append(String.join(",", removeDouList(consend)));
                sb.append("上一个投顾已离职，不能撤销!");
                result = sb.toString();
                return result;
            }
            User user = (User) request.getSession().getAttribute("loginUser");
            cmCustconstanthisService.batchCancelCustConstanthis(list, user.getUserId());
            result = "success";
        } else {
            result = "paramError";
        }
        return result;
    }


    /**
     * 查看手机明文信息
     *
     * @param request
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/showMobileByCipher")
    public String showMobileByCipher(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute("loginUser");
        String mobileCipher = request.getParameter("mobileCipher");
        String conscustno = request.getParameter("conscustno");

        //记录访问日志
        PageVisitLog pageVisitLog = new PageVisitLog();
        pageVisitLog.setConscustno(conscustno);
        pageVisitLog.setUserId(user.getUserId());
        pageVisitLog.setVisitUrl(request.getRequestURI());
        pageVisitLog.setOperation("查看手机明文信息");
        pageVisitLog.setVisitTime(new Date());
        pageVisitLog.setIp(getIpAddr(request));
        pageVisitLogService.recordLog(pageVisitLog);

        return decryptSingleFacade.decrypt(mobileCipher).getCodecText();

    }

    private List<String> removeDouList(List<String> list) {
        HashSet<String> h = new HashSet(list);
        list.clear();
        list.addAll(h);
        return list;
    }

    /**
     * 清除标签 仅清除登录人打过的所有标签
     *
     * @param request
     * @return String
     */
    @ResponseBody
    @RequestMapping("/clearLabelCust.do")
    public String clearLabelCust(HttpServletRequest request) {
        String result = null;
        User user = (User) request.getSession().getAttribute("loginUser");
        String conscustnos = request.getParameter("conscustnos");
        if (StringUtils.isNotBlank(conscustnos) && StringUtil.isNotNullStr(user.getUserId())) {
            String[] conscustNoArr = conscustnos.split(",");
            List<String> conscustNoList = Arrays.asList(conscustNoArr);
            Map<String, Object> param = new HashMap<>(2);
            //登录投顾号
            param.put("consCode", user.getUserId());
            //选中的客户号
            param.put("conscustNoList", conscustNoList);
            conscustService.clearLabelCust(param);
            result = "success";
        } else {
            result = "paramError";
        }
        return result;
    }


    /**
     * @api {GET} /conscust/unbindhbone.do unBindhHbone()
     * @apiVersion 1.0.0
     * @apiGroup CmConscustController
     * @apiName unBindhHbone()
     * @apiParam (请求参数) {String} custNo
     * @apiParam (请求参数) {String} hboneNo
     * @apiParam (请求参数) {String} operator
     * @apiParam (请求参数) {String} operateSource
     * @apiParam (请求参数) {String} operateChannel
     * @apiParam (请求参数) {String} remark
     * @apiParamExample 请求参数示例
     * custNo=XbliwQh&operateChannel=xSCAwOBgjR&operateSource=KQvY&remark=m8y&operator=6F2X4N9L&hboneNo=pTkJUhy
     * @apiSuccess (响应结果) {String} code
     * @apiSuccess (响应结果) {String} description
     * @apiSuccess (响应结果) {String} data
     * @apiSuccessExample 响应结果示例
     * {"code":"GtB6kK","data":"h","description":"gtUJ"}
     */
    @RequestMapping("/unbindhbone.do")
    @ResponseBody
    public Response<String> unBindhHbone(HboneAcctRelationOptRequest bindOptRequest) {
        bindOptRequest.setOperator(getLoginUserId());
        return crmAccountOuterService.disRelateConsCustNoAndHbone(bindOptRequest);
    }

    /**
     * 证件号上传状态
     * @param hboneNo
     * @return
     */
    @RequestMapping("/getIdCardInfo.do")
    @ResponseBody
    public  ReturnMessageDto<IdCardDisplayInfoDto> getIdCardInfo(String hboneNo){

        if(StringUtil.isEmpty(hboneNo)){
            return ReturnMessageDto.fail("一账通号不能为空");
        }

        QueryIdCardInfoRequest req = new QueryIdCardInfoRequest();
        req.setHboneNo(hboneNo);
        QueryIdCardInfoResponse rsp = queryIdCardInfoFacade.execute(req);
        if(!ACCT_CENTER_SUCCESS.equals(rsp.getReturnCode())){
            return ReturnMessageDto.fail(rsp.getDescription());
        }

        //证件图片验证状态 1-已验证；0-未验证
//        boolean isPicVeryStat= "1".equals(rsp.getIdCardPictureVrfyStat());
        // idImageUploadStatus 证件上传状态：0-未上传1-柜台上传；2-crm上传
//        boolean offlineStat= "1".equals(rsp.getIdImageUploadStatus()) || "2".equals(rsp.getIdImageUploadStatus()) ; // 线下已上传 逻辑： 1-柜台上传；2-crm上传

        //图片验证状态 0-未验证  且  证件上传状态：0-未上传
//        if(  (!isPicVeryStat)  && (!offlineStat) ){
//            return "未上传";
//        }

        List<IdCardDisplayInfoDto> displayList=Lists.newArrayList();
        // 调用账户中心，优先取线上OCR客户影像，若线上无值，则取线下上传的pdf等格式证件影像
        //线上
//        picturePath  Eg: /data/app/cim_web_server/center_feature/20240117/
//        * 正面
//         Eg: 9200814809240117130226146I1b.jpg
        if(StringUtil.isNotNullStr(rsp.getFrontPictureName())){
            displayList.add(new IdCardDisplayInfoDto(rsp.getFrontPictureName(),"正面图片名称",rsp.getPicturePath()));
        }
//        反面
//        *Eg:  9200814809240117130226146I1f.jpg
        if(StringUtil.isNotNullStr(rsp.getBackPictureName())){
            displayList.add(new IdCardDisplayInfoDto(rsp.getBackPictureName(),"反面图片名称",rsp.getPicturePath()));
        }

        //线下
//      "offlineImageFileList": [
//          {
//              "fileType": "1",
//              "fileName": "1177427133-刘维平-172002-有限合伙类产品认申购-投资人身份证明复印件-353830.PDF",
//              "fileFormat": "PDF",
//              "filePath": "/data/app/cim_web_server/center_feature/20201125/"
//          }
//      ]
        //以上为空。尝试取线下
        if(CollectionUtils.isEmpty(displayList)){
            List<OfflineImageFileBean> offlineList=rsp.getOfflineImageFileList();
            if(CollectionUtils.isNotEmpty(offlineList)){
                offlineList.stream().forEach(offlineImage->{
                    displayList.add(new IdCardDisplayInfoDto(offlineImage.getFileName(),"投资人身份证明复印件",offlineImage.getFilePath()));
                });
            }
        }

        ReturnMessageDto<IdCardDisplayInfoDto> returnDto=ReturnMessageDto.ok();
        returnDto.setReturnList(displayList);
        return returnDto;
    }

    @ResponseBody
    @RequestMapping("/getIdCardFileStream.do")
    public void getIdCardFileStream(HttpServletRequest request, HttpServletResponse response) {

        HFileService instance = HFileService.getInstance();
        //Eg: /data/app/cim_web_server/center_feature/20240117/
        String filePath = request.getParameter("filePath");
        //Eg :9200814809240117130226146I1f.jpg
        String fileName = request.getParameter("fileName");


        String paramRelativaPath=filePath.replace(relativePath_prefix,"");
        //如果 paramRelativaPath  最后一位是 /  ，替换为 空字符串
        paramRelativaPath= Arrays.stream(paramRelativaPath.split(SEPARATOR_FILE))
                .filter(StringUtil::isNotNullStr)
                .collect(Collectors.joining("/"));

        byte[]  fileBytes= new byte[0];
        try {
            log.info("文件filePath：{}，参数path:{} ,文件名称：{}，读取开始！",filePath,paramRelativaPath,fileName);
            fileBytes = instance.read2Bytes(cimWebServerStoreConfig,
                    paramRelativaPath,
                    fileName);
        } catch (Exception e) {
            log.error("文件filePath：{}，参数path:{} ,文件名称：{}，读取异常！",filePath,paramRelativaPath,fileName);
            log.error("读取文件异常！",e);
        }
        ServletOutputStream outputStream = null;
        try {
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;fileName=" + new String(fileName.getBytes("gb2312"), StandardCharsets.ISO_8859_1));
            //输出文件流
            outputStream = response.getOutputStream();
            outputStream.write(fileBytes);
        }catch (Exception e){
            log.error("身份证件预览失败，fileName：{}, useId：{}", fileName, getLoginUserId(), e);
        }finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关闭文件流异常", e);
                }
            }
        }

    }


}
