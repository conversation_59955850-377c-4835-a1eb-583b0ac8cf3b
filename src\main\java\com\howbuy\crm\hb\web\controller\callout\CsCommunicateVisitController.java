package com.howbuy.crm.hb.web.controller.callout;

import com.howbuy.crm.hb.service.callout.CsCalloutMyTaskService;
import com.howbuy.crm.util.callout.ValidatePhoneUtil;
import crm.howbuy.base.utils.MapRemoveNullUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 沟通拜访记录操作类
 */
@Slf4j
@Controller
@RequestMapping(value = "/callout/communicate")
public class CsCommunicateVisitController {

    @Autowired
    private CsCalloutMyTaskService csCalloutMyTaskService;

    /**
     * 验证客户手机号操作(客户列表-添加客户)
     */
    @ResponseBody
    @RequestMapping("/validatePhoneUtil")
    public String validatePhoneUtil(HttpServletRequest request, @RequestParam Map<String, String> pageParam) throws Exception {
        String result = "";
        try{
            // 移除map中值为空的元素
            MapRemoveNullUtil.removeNullValue(pageParam);
            String mobile = pageParam.get("mobile");
            Map<String,Object> smap = ValidatePhoneUtil.isDefraudPhone(mobile);
            result = smap.get("fraudInfo").toString();
        }catch(Exception e){
            result = "failure";
            e.printStackTrace();
        }
        return result;
    }
}
