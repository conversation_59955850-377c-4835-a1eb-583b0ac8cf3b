/**   
 * @Title: FundsArgumentUtils.java 
 * @Package com.hb.crm.web.util.excel.util 
 * @Description: 公募基金费率维护参数转化类 ，将中文转化为代码和代码转化中文 
 * <AUTHOR>
 * @date 2016年5月23日 上午9:25:14 
 * @version V1.0   
 */
package com.howbuy.crm.hb.tools.excel.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName: FundsArgumentUtils
 * @Description: 公募基金费率维护参数转化类
 * <AUTHOR>
 * @date 2016年5月23日 上午9:25:14
 * 
 */

public class FundsArgumentUtils {

	private static Logger logger = LoggerFactory.getLogger(FundsArgumentUtils.class);

	/**
	 * @Fields IA_STATUS : 是否判断
	 */
	public final static String IA_STATUS = "0";
	/**
	 * @Fields FUNDS_TYPE_STATUS : 基金类别
	 */
	public final static String FUNDS_TYPE_STATUS = "1";
	/**
	 * @Fields INSURANCE_TYPE_STATUS :保险类别
	 */
	public final static String INSURANCE_TYPE_STATUS = "2";
	/**
	 * @Fields INCOME_DATE_STATUS : 收入日期判断
	 */
	public final static String INCOME_DATE_STATUS = "3";

	/**
	 * @Title: chartToChinese
	 * @Description:将字符转化成中文
	 * @param type
	 * @param value
	 * @return
	 * @throws Exception
	 */

	public static String chartToChinese(String type, String value){

		Map<String, String> map = new HashMap<String, String>();

		if (FundsArgumentUtils.IA_STATUS.equals(type)) {
			map = PublicFundsArgument.IA_STATUS;
		} else if (FundsArgumentUtils.INCOME_DATE_STATUS.equals(type)) {
			map = PublicFundsArgument.INCOME_DATE_STATUS;
		} else if (FundsArgumentUtils.INSURANCE_TYPE_STATUS.equals(type)) {
			map = PublicFundsArgument.INSURANCE_TYPE_STATUS;
		} else if (FundsArgumentUtils.FUNDS_TYPE_STATUS.equals(type)) {
			map = PublicFundsArgument.FUNDS_TYPE_STATUS;
		} else {
			return value;
		}
		for(Map.Entry<String, String> entry : map.entrySet()){
			if(entry.getKey().equals(value)){
				return entry.getValue();
			}
		}
		return value;
	}

	/**
	 * @Title: chineseToChart
	 * @Description: 将中文转化成字符
	 * @param type
	 * @param value
	 * @return
	 */

	public static String chineseToChart(String type, String value) {
		Map<String, String> map = new HashMap<String, String>();

		if (FundsArgumentUtils.IA_STATUS.equals(type)) {
			map = PublicFundsArgument.IA_STATUS;
		} else if (FundsArgumentUtils.INCOME_DATE_STATUS.equals(type)) {
			map = PublicFundsArgument.INCOME_DATE_STATUS;
		} else if (FundsArgumentUtils.INSURANCE_TYPE_STATUS.equals(type)) {
			map = PublicFundsArgument.INSURANCE_TYPE_STATUS;
		} else if (FundsArgumentUtils.FUNDS_TYPE_STATUS.equals(type)) {
			map = PublicFundsArgument.FUNDS_TYPE_STATUS;
		} else {
			return value;
		}
		for (Map.Entry<String, String> entry : map.entrySet()) {
			String key = entry.getKey();
			String mapValue = entry.getValue();
			if (mapValue.equals(value)) {
				return key;
			}
		}
		return value;
	}

}
