package com.howbuy.crm.hb.web.controller.cache;


/**
 * @description: 缓存枚举类，用于定义缓存的key前缀和超时时间
 * @author: hongdong.xie
 * @date: 2023/12/20 17:56
 * @since JDK 1.8
 */
public enum CacheCode {

    /**
     * 会议代码缓存前缀
     */
    CMCONFERENCE(10, "CONFERENCE_INDEX_NO|"),
    /**
     * 投顾编号
     */
    CONSTRANF(3602,"CONSCODE|");

    /**
     * 超时时间，秒
     */
    public Integer timeOut;
    /**
     * 缓存key前缀
     */
    public String keyPrefix;

    CacheCode(Integer timeOut,String keyPrefix){
        this.timeOut = timeOut;
        this.keyPrefix = keyPrefix;
    }
}
