package com.howbuy.crm.hb.web.controller.insur;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import com.howbuy.crm.hb.domain.insur.CmBxCurrencyRmbRate;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.insur.CmBxCurrencyRmbRateService;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;

/**
 * @Description: Controller
 * @version 1.0
 */
@Slf4j
@Controller
@RequestMapping(value = "/cmBxCurrencyRmbRate")
public class CmBxCurrencyRmbRateController {
	@Autowired
	private CmBxCurrencyRmbRateService cmBxCurrencyRmbRateService;
	
	@Autowired
    private CommonService commonService;
	
	@RequestMapping("/listCmBxCurrencyRmbRate.do")
	public ModelAndView listCmBxCurrencyRmbRate(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();        
        modelAndView.setViewName("/insur/listCmBxCurrencyRmbRate");
        return modelAndView;
	}
	
	/**
	 * 加载列表页面数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listCmBxCurrencyRmbRateByPage.do")
	public Map<String, Object> listCmBxCurrencyRmbRateByPage(HttpServletRequest request)	throws Exception {
		Map<String, String> param = new ParamUtil(request).getParamMap();
		param.put("isdel", StaticVar.INSUR_ISDEL_NO);
		PageData<CmBxCurrencyRmbRate> pageData = cmBxCurrencyRmbRateService.listCmBxCurrencyRmbRateByPage(param);
		// 返回查询结果
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmBxCurrencyRmbRate> listdata = pageData.getListData();
		for(CmBxCurrencyRmbRate rate : listdata){
			rate.setCurrencyval(ConstantCache.getInstance().getVal("currencys", rate.getCurrency()));
		}
		resultMap.put("rows", listdata);
		return resultMap;
	}
	
	
	
	/**
	 * 更新
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/updateCmBxCurrencyRmbRate", method = RequestMethod.POST)
	@ResponseBody
    public String updateCmBxCurrencyRmbRate(HttpServletRequest request) {
        String result = "success";
        String id = request.getParameter("id");
		String rate = request.getParameter("rate");
        User user = (User) request.getSession().getAttribute("loginUser");
        if (!StringUtils.isEmpty(id)) {
        	CmBxCurrencyRmbRate cmBxCurrencyRmbRate = new CmBxCurrencyRmbRate();
        	cmBxCurrencyRmbRate.setId(new BigDecimal(id));
        	cmBxCurrencyRmbRate.setRate(new BigDecimal(rate));
        	cmBxCurrencyRmbRate.setModifier(user.getUserId());
        	cmBxCurrencyRmbRate.setModifydt(new Date());
        	cmBxCurrencyRmbRateService.updateCmBxCurrencyRmbRate(cmBxCurrencyRmbRate);
        	log.info(user.getUserId()+"更新了一条id是"+cmBxCurrencyRmbRate.getId()+"的维护信息！");
        }else{
        	log.info("更新创新产品汇率维护信息的id为null");
        }
        return result;
    }
	
	
	/**
	 * 删除
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/delCmBxCurrencyRmbRate", method = RequestMethod.POST)
	@ResponseBody
    public String delCmBxCurrencyRmbRate(HttpServletRequest request) {
		String result = "success";
        User user = (User) request.getSession().getAttribute("loginUser");
        String id = request.getParameter("id");
        if (StringUtils.isNotBlank(id)) {
        	CmBxCurrencyRmbRate cmBxCurrencyRmbRate = new CmBxCurrencyRmbRate();
        	cmBxCurrencyRmbRate.setId(new BigDecimal(id));
        	cmBxCurrencyRmbRate.setIsdel(StaticVar.INSUR_ISDEL_YES);//删除
        	cmBxCurrencyRmbRate.setModifier(user.getUserId());
        	cmBxCurrencyRmbRate.setModifydt(new Date());
        	cmBxCurrencyRmbRateService.updateCmBxCurrencyRmbRate(cmBxCurrencyRmbRate);
        	log.info(user.getUserId()+"删除了一条id是"+cmBxCurrencyRmbRate.getId()+"的维护信息！");
        }else{
        	log.info("删除维护信息的id为null");
        }
        return result;
    }
	
	
	/**
	 * 新增
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/insertCmBxCurrencyRmbRate", method = RequestMethod.POST)
	@ResponseBody
    public  Map<String, Object> insertCmBxCurrencyRmbRate(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String currency = request.getParameter("currency");
		String ratedt = request.getParameter("ratedt");
		String rate = request.getParameter("rate");
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        
        if(ratedt != null && (!ratedt.matches("^[0-9]{8}$"))){
        	resultMap.put("errorMsg", "日期格式不对！");
            resultMap.put("errorCode", "9999");
            return resultMap;
		}
        User user = (User) request.getSession().getAttribute("loginUser");
        Map<String,String> param = new HashMap<String,String> ();
        param.put("currency", currency);
        param.put("ratedt", ratedt);
        param.put("isdel", StaticVar.INSUR_ISDEL_NO);
        CmBxCurrencyRmbRate CurrencyRmbRate = cmBxCurrencyRmbRateService.getCmBxCurrencyRmbRate(param);
        if(CurrencyRmbRate != null){
        	resultMap.put("errorMsg", "当前币种和日期的汇率已存在，请重新输入");
        	resultMap.put("errorCode", "9999");
        	return resultMap;
        }
        CmBxCurrencyRmbRate cmBxCurrencyRmbRate = new CmBxCurrencyRmbRate();
        cmBxCurrencyRmbRate.setId(new BigDecimal(commonService.getSeqValue("SEQ_INSUR_ID")));
        cmBxCurrencyRmbRate.setCurrency(currency);
        cmBxCurrencyRmbRate.setRatedt(ratedt);
        cmBxCurrencyRmbRate.setRate(new BigDecimal(rate));
        cmBxCurrencyRmbRate.setCreator(user.getUserId());
        cmBxCurrencyRmbRate.setIsdel(StaticVar.INSUR_ISDEL_NO);
    	cmBxCurrencyRmbRateService.insertCmBxCurrencyRmbRate(cmBxCurrencyRmbRate);
    	log.info(user.getUserId()+"插入了一条id是"+cmBxCurrencyRmbRate.getId()+"的维护信息！");
        return resultMap;
    }
	
	
	/**
	 * 展示修改页面
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/viewCmBxCurrencyRmbRate", method = RequestMethod.POST)
	@ResponseBody
    public  Map<String, Object> viewCmBxCurrencyRmbRate(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("errorMsg", "操作成功");
		resultMap.put("errorCode", "0000");
		String id = request.getParameter("id");
		if(StringUtils.isNotBlank(id)){
			Map<String,String> param = new HashMap<String,String> ();
			param.put("id", id);
			param.put("isdel", StaticVar.INSUR_ISDEL_NO);
			CmBxCurrencyRmbRate cmBxCurrencyRmbRate = cmBxCurrencyRmbRateService.getCmBxCurrencyRmbRate(param);
			resultMap.put("domain", cmBxCurrencyRmbRate);
		}else{
			resultMap.put("errorMsg", "操作失败：id不能为空");
			resultMap.put("errorCode", "9999");
		}
      
        return resultMap;
    }
	
}
