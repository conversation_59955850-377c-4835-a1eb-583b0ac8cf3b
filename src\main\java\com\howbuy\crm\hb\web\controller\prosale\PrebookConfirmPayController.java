package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.base.hwdealorder.HwSubmitStatusEnum;
import com.howbuy.crm.base.trade.FeeRateMethodEnum;
import com.howbuy.crm.hb.domain.prosale.Prebookproductinfo;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.hb.web.vo.PreBookDealPayViewVo;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prebook.dto.CmPrebookExtra;
import com.howbuy.crm.prebook.request.PrebookTradeDealRequest;
import com.howbuy.crm.prebook.service.CmPrebookExtraService;
import com.howbuy.crm.prebook.service.PrebookPayService;
import com.howbuy.crm.prebook.service.PrebookTradeDealService;
import com.howbuy.crm.prebook.vo.CmPreBookBankVo;
import com.howbuy.crm.prebook.vo.PreBookDealPayVo;
import com.howbuy.crm.prebook.vo.PreBookPayVo;
import com.howbuy.crm.util.exception.BusinessException;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2019/8/9 13:51
 */
@Controller
@RequestMapping("/prosale")
public class PrebookConfirmPayController  extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(PrebookConfirmPayController.class);

    @Autowired
    private PrebookproductinfoService prebookproductinfoService;

    @Autowired
    private JjxxInfoService jjxxInfoService;
    @Autowired
    private PrebookTradeDealService prebookTradeDealService;

    @Autowired
    private CmPrebookExtraService cmPrebookExtraService;

    @Autowired
    private PrebookPayService prebookPayService;

    @RequestMapping("/listConfirmPay.do")
    public ModelAndView listConfirmPay(){
        return new ModelAndView("prosale/listConfirmPay");
    }

    @SuppressWarnings("unchecked")
	@RequestMapping("/listConfirmPayJson.do")
    @ResponseBody
    public Object listConfirmPayJson(HttpServletRequest request) throws Exception{
        Map<String, Object> resultMap = new HashMap<String, Object>();
        // 设置查询参数
        Map<String, String> param;
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();
        String custname = request.getParameter("custname");
        String credt = request.getParameter("credt");
        String pcode = request.getParameter("pcode");
        String tradeTypes = request.getParameter("tradeTypes");
        String prebookStates = request.getParameter("prebookStates");
        String payStates = request.getParameter("payStates");
        String tradestate = request.getParameter("tradestate");
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        String isstat = request.getParameter("isstat");
        String tradeStartDate = request.getParameter("tradeStartDate");
        String tradeEndDate = request.getParameter("tradeEndDate");
        String conscustno = request.getParameter("conscustno");
        String hboneno = request.getParameter("hboneno");
        //是否香港产品 1-是 0-否
        param.put("sfxg", request.getParameter("sfxg"));

        //查询条件（客户名）不为空，增增加客户名参数
        if(StringUtil.isNotNullStr(custname)){
            param.put("custname", custname);
        }

        //如果查询条件（录入日期）不为空，则增加录入日期查询参数
        if(StringUtil.isNotNullStr(credt)){
            param.put("credt", credt);
        }

        //如果查询条件（预计交易日期开始）不为空，则增加预计交易日期开始查询参数
        if(StringUtil.isNotNullStr(tradeStartDate)){
            param.put("expecttradebegdt", tradeStartDate);
        }
        //如果查询条件（预计交易日期截止）不为空，则增加预计交易日期截止查询参数
        if(StringUtil.isNotNullStr(tradeEndDate)){
            param.put("expecttradeenddt", tradeEndDate);
        }

        //如果查询条件（产品代码）不为空，则增加产品代码查询参数
        if(StringUtil.isNotNullStr(pcode)){
            param.put("pcode", pcode);
        }
        //如果查询条件（交易类型）不为空，则增加交易类型查询参数
        if(StringUtil.isNotNullStr(tradeTypes)){
            param.put("tradeTypes", tradeTypes);
        }

        //如果查询条件（预约状态）不为空，则增加预约状态查询参数
        if(StringUtil.isNotNullStr(prebookStates)){
            param.put("prebookStates", prebookStates);
        }
        //如果查询条件（交易确认状态）不为空，则增加交易确认状态查询参数
        if(StringUtil.isNotNullStr(tradestate)){
            param.put("tradeStates", tradestate);
        }
        //如果查询条件（打款状态）不为空，则增加打款状态查询参数
        if(StringUtil.isNotNullStr(payStates)){
            param.put("payStates", payStates);
        }

        //如果查询条件（是否计入统计）不为空，则增加录入日期查询参数
        if(StringUtil.isNotNullStr(isstat)){
            param.put("isstat", isstat);
        }
        if (StringUtil.isNotNullStr(consCode)) {
            param.put("conscode", consCode);
        } else {
            param.put("orgcode", orgCode);
        }

        //如果查询条件（投顾客户号）不为空，则增加投顾客户号查询参数
        if (StringUtil.isNotNullStr(conscustno)) {
            param.put("conscustno", conscustno);
        }
        //如果查询条件（一账通号）不为空，则增加一账通号查询参数
        if (StringUtil.isNotNullStr(hboneno)) {
            param.put("hboneno", hboneno);
        }

        // 判断常量表中合规标识：true启用，false停用
  		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
  		boolean roleCPFlag = false;
  		if (cacheMap != null && !cacheMap.isEmpty()) {
  			roleCPFlag = JSON.toJSONString(cacheMap).contains("true");
  		}

        List<String> gdfxcplist = (List<String>) request.getSession().getAttribute("gdfxcplist");
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(gdfxcplist) && gdfxcplist.size() == 1 && gdfxcplist.contains("4")) {
            param.put("sfxg", "1");
        }

        param.put("submitStatus",request.getParameter("submitStatus"));
        param.put("hkTxAcctNo",request.getParameter("hkTxAcctNo"));

  		// 判断登录人员的角色中是否包括"合规人员"角色
  		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
      		if (roleCPFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
            param.put("hascp", "true");
        }
        // 通过Session获取产品广度信息
        String topcpdata = (String) request.getSession().getAttribute("topcpdata");
      	param.put("topCpData", topcpdata);
        PageData<Prebookproductinfo> pageData = prebookproductinfoService.listPrebookproductinfoByPage(param);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<Prebookproductinfo> listdata= pageData.getListData();
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        ConstantCache constantCache = ConstantCache.getInstance();
        for(Prebookproductinfo info : listdata){
            JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
            info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));
            if(jjxx != null){
                // 分次CALL产品查询认缴金额
                if(StaticVar.FCCL_YES.equals(jjxx.getFccl())){
                    info.setFccl(StaticVar.FCCL_YES);
                }
            }
            info.setBuyamt(info.getDealno() == null ? info.getBuyamt() : info.getZtbuyamt());
            info.setPrebookstateval(constantCache.getConstantKeyVal("prebookStates").get(info.getPrebookstate()));
            info.setPaystateval(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));
            info.setOrderstateval(constantCache.getConstantKeyVal("dealorderStatus").get(info.getOrderstate()));
            info.setPretypeval(constantCache.getConstantKeyVal("pretype").get(info.getPretype()));
            if (info.getDiscountstate() != null) {
                info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
            } else {
                info.setDiscountstate("1");
                info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
            }
            if(StringUtil.isNotNullStr(consOrgCache.getAllUserMap().get(info.getPrebookcheckman()))){
            	info.setPrebookcheckman(consOrgCache.getAllUserMap().get(info.getPrebookcheckman()));
            }
            info.setTradestateval(constantCache.getConstantKeyVal("tradestate").get(info.getTradestate()));
            info.setRemarks(Util.ObjectToString(info.getRemarks()));
            info.setNotes(Util.ObjectToString(info.getNotes()));
            info.setConsname(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
            info.setOutletName(consOrgCache.getOrgMap().get(consOrgCache.getCons2OutletMap().get(info.getCreator())));

            String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(info.getCreator()));
            if("0".equals(uporgcode)){
                info.setUporgname(info.getOutletName());
            }else{
                info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
            }
            //上报状态翻译
            info.setSubmitStatus(HwSubmitStatusEnum.getDescription(info.getSubmitStatus()));
        }
        resultMap.put("rows", listdata);
        return resultMap;
    }


    /**
     * @description:(页面属性处理)
     * @param payVo
     * @return com.howbuy.crm.hb.web.vo.PreBookDealPayViewVo
     * @author: haoran.zhang
     * @date: 2025/4/17 17:42
     * @since JDK 1.8
     */
    private PreBookDealPayViewVo  transferToView(PreBookDealPayVo payVo){
        PreBookDealPayViewVo vo = new PreBookDealPayViewVo();
        vo.setId(payVo.getPreId().toPlainString());
        vo.setPname(payVo.getProdName());
        vo.setConscustname(payVo.getConsCustName());
        vo.setCurrency(ConstantCache.getInstance().getVal("currencys", payVo.getCurrency()));
        vo.setRealpayamt(payVo.getRealPayAmt() == null ? null : payVo.getRealPayAmt().divide(BIG_DECIMAL_1W).toPlainString());
        vo.setBuyamt(payVo.getBuyAmt() == null ? null : payVo.getBuyAmt().divide(BIG_DECIMAL_1W).toPlainString());
        vo.setExpectpayamtdt(payVo.getExpectPayAmtDt());
        vo.setExpecttradedt(payVo.getExpectTradeDt());
        vo.setRealpayamtdt(payVo.getRealPayAmtDt());
        vo.setRealbuyman(payVo.getRealBuyMan());
        vo.setCustno(payVo.getConsCustNo());
        vo.setFee(payVo.getFee() == null ? null : payVo.getFee().toPlainString());

        vo.setDxflag(payVo.isDxFlag());

        //是否 分次call
        vo.setIsfccl(payVo.getIsFccl());

        DecimalFormat df = new DecimalFormat("#0.00");

        //计算  应收打款金额
        vo.setShouldfee(payVo.getShouldFee() == null ? "" : df.format(payVo.getShouldFee().doubleValue()));

        //认缴金额
        BigDecimal subscribeAmt = payVo.getSubscribeAmt();
        vo.setFeeRateMethod(payVo.getFeeRateMethod() == null ? null : FeeRateMethodEnum.getDescription(payVo.getFeeRateMethod()));
        vo.setTotalamt(subscribeAmt == null ? null : subscribeAmt.divide(BIG_DECIMAL_1W).toPlainString());

        //是否香港产品
        vo.setSfxg(payVo.isSfxg());
        //预约银行卡信息
        PreBookDealPayVo.PayBankInnerVo  bankInnerVo= payVo.getBankInfo();
        if(bankInnerVo != null){
            vo.setBankacct(bankInnerVo.getBankAcct());
            vo.setBankcode(bankInnerVo.getBankCode());
            vo.setBankprov(bankInnerVo.getBankProv());
            vo.setBankcity(bankInnerVo.getBankCity());
            vo.setBankaddr(bankInnerVo.getBankAddr());
        }else{
            vo.setBankacct("");
            vo.setBankcode("");
            vo.setBankprov("");
            vo.setBankcity("");
            vo.setBankaddr("");
        }
        //用户持有银行卡
        List<Map<String,Object>> bankViewList =transferBankListView(payVo.getBankList());
        vo.setBankList(bankViewList);
        vo.setBankSize(bankViewList.size());

        vo.setDiscountway(payVo.getDiscountWay()==null?"":
                ConstantCache.getInstance().getConstantKeyVal("newdiscountWays").get(payVo.getDiscountWay()));
        vo.setDiscountrate(payVo.getDiscountRate()==null?"":
                df.format(payVo.getDiscountRate().doubleValue()));
        vo.setBeforetaxamt(payVo.getBeforeTaxAmt()==null?"":
                df.format(payVo.getBeforeTaxAmt().doubleValue()));
        vo.setDiscountStateDesc(payVo.getDiscountStateDesc());
        return  vo;
    }



    @ResponseBody
    @RequestMapping("/confirmPay.do")
    public ModelAndView confirmPay(HttpServletRequest request,
                                   HttpServletResponse response){
        String id = request.getParameter("id");
        BigDecimal preId = new BigDecimal(id);

        ReturnMessageDto<PreBookDealPayVo>  payResp = prebookPayService.getPayInfoVo(preId);
        if(!payResp.isSuccess()){
            throw  new BusinessException(payResp.getReturnMsg());
        }
        PreBookDealPayVo payVo = payResp.getReturnObject();
        //转义为 页面对象
        PreBookDealPayViewVo viewVo = transferToView(payVo);
        //转为map
        Map<String, Object> map = transformToMap(viewVo);
        
        return new ModelAndView("prosale/confirmPay", "map", map);
    }


    /**
     * @description:(页面对象转换)
     * @param viewVo
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @author: haoran.zhang
     * @date: 2025/6/17 19:10
     * @since JDK 1.8
     */
   private Map<String, Object> transformToMap(PreBookDealPayViewVo viewVo) {
    Map<String, Object> map = new HashMap<>();

    map.put("id", viewVo.getId());
    map.put("pname", viewVo.getPname());
    map.put("conscustname", viewVo.getConscustname());
    map.put("currency", viewVo.getCurrency());
    map.put("realpayamt", viewVo.getRealpayamt());
    map.put("buyamt", viewVo.getBuyamt());
    map.put("expectpayamtdt", viewVo.getExpectpayamtdt());
    map.put("expecttradedt", viewVo.getExpecttradedt());
    map.put("realpayamtdt", viewVo.getRealpayamtdt());
    map.put("realbuyman", viewVo.getRealbuyman());
    map.put("custno", viewVo.getCustno());
    map.put("fee", viewVo.getFee());
    map.put("dxflag", viewVo.isDxflag());
    map.put("isfccl", viewVo.getIsfccl());
    map.put("shouldfee", viewVo.getShouldfee());
    map.put("feeRateMethod", viewVo.getFeeRateMethod());
    map.put("totalamt", viewVo.getTotalamt());
    map.put("sfxg", viewVo.isSfxg());
    map.put("bankacct", viewVo.getBankacct());
    map.put("bankcode", viewVo.getBankcode());
    map.put("bankprov", viewVo.getBankprov());
    map.put("bankcity", viewVo.getBankcity());
    map.put("bankaddr", viewVo.getBankaddr());
    map.put("bankList", viewVo.getBankList());
    map.put("bankSize", viewVo.getBankSize());
    map.put("discountway", viewVo.getDiscountway());
    map.put("discountrate", viewVo.getDiscountrate());
    map.put("beforetaxamt", viewVo.getBeforetaxamt());
    map.put("discountStateDesc", viewVo.getDiscountStateDesc());

    return map;
}



    /**
     * 获取页面下拉框 银行卡 信息列表
     * @param innerBankList
     * @return
     */
    private List<Map<String,Object>> transferBankListView(List<PreBookDealPayVo.PayBankInnerVo>  innerBankList){
        List<Map<String,Object>> bankList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(innerBankList)){
          innerBankList.forEach(innerBank->{
              Map<String, Object> bank = new HashMap<>();
              bank.put("id", innerBank.getBankAcct());
              bank.put("text", innerBank.getBankAcct() + ' ' + innerBank.getBankName());
              bank.put("bankprov", innerBank.getBankProv());
              bank.put("bankcity", innerBank.getBankCity());
              bank.put("bankaddr", innerBank.getBankAddr());
              bank.put("bankcode", innerBank.getBankCode());
              bank.put("bankname", innerBank.getBankName());
              bankList.add(bank);
          });
        }
        return bankList;
    }

    @ResponseBody
    @RequestMapping("/saveConfirmPay.do")
    public ReturnMessageDto<String> saveConfirmPay(HttpServletRequest request) {
        String id = request.getParameter("id");
        String realpayamt = request.getParameter("realpayamt");
        String fee = request.getParameter("fee");
        String realpayamtdt = request.getParameter("realpayamtdt");
        String expecttradedt = request.getParameter("expecttradedt");
        String bankno = request.getParameter("bankno");
        String branchaccount = request.getParameter("branchaccount");
        String depositbank = request.getParameter("depositbank");
        String provCode = request.getParameter("provCode");
        String cityCode = request.getParameter("cityCode");
        String hkCpAcctNoList = request.getParameter("hkCpAcctNoList");
        //支付方式备注
        String confirmPayRemark =request.getParameter("confirmPayRemark");

        String operator=getLoginUserId();

        //构建 请求参数
        PrebookTradeDealRequest payRequest = new PrebookTradeDealRequest();
        payRequest.setPreId(new BigDecimal(id));
        payRequest.setOperator(operator);

        // 当为香港产品的并且选择了对应的银行卡
        if (StringUtil.isNotNullStr(hkCpAcctNoList)) {
            payRequest.setHkCpAcctNoList(Arrays.asList(hkCpAcctNoList.split(",")));
        }
        //打款金额相关
        PreBookPayVo payVo = new PreBookPayVo();
        payVo.setFee(new BigDecimal(fee));
        payVo.setRealPayAmt(new BigDecimal(realpayamt).multiply(new BigDecimal(10000)));
        payVo.setRealPayAmtDt(realpayamtdt);
        payVo.setExpectTradeDt(expecttradedt);

        //银行卡信息
        CmPreBookBankVo bankVo = new CmPreBookBankVo();
        if(StringUtil.isNotNullStr(bankno)) {
            bankVo.setBankAcct(bankno);
        }
        if(StringUtil.isNotNullStr(depositbank)) {
            bankVo.setBankCode(depositbank);
        }
        if(StringUtil.isNotNullStr(provCode)) {
            bankVo.setBankProv(provCode);
        }
        if(StringUtil.isNotNullStr(cityCode)) {
            bankVo.setBankCity(cityCode);
        }
        if(StringUtil.isNotNullStr(branchaccount)) {
            bankVo.setBankAddr(branchaccount);
        }
        payRequest.setPreBookPayVo(payVo);
        payRequest.setBankVo(bankVo);
        ReturnMessageDto<String>  dealMessagetDto= prebookTradeDealService.executeConfirmPay(payRequest);

        //更新支付方式备注：
        if (dealMessagetDto.isSuccess() && StringUtil.isNotNullStr(confirmPayRemark)) {
            //更新支付方式备注
            CmPrebookExtra extra = new CmPrebookExtra();
            extra.setPreId(new BigDecimal(id));
            extra.setConfirmPayRemark(confirmPayRemark);
            cmPrebookExtraService.update(extra, operator);
        }

        return dealMessagetDto;
    }

}
