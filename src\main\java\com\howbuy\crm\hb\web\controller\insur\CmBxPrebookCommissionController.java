package com.howbuy.crm.hb.web.controller.insur;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.base.CurrencyEnum;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.base.YesOrNoEnum;
import com.howbuy.crm.hb.domain.insur.*;
import com.howbuy.crm.hb.enums.CommissionOptDataEnum;
import com.howbuy.crm.hb.enums.CommissionOptEnum;
import com.howbuy.crm.hb.enums.CommissionStateEnum;
import com.howbuy.crm.hb.request.CommissionOptVo;
import com.howbuy.crm.hb.request.CommissionSplitInsertVo;
import com.howbuy.crm.hb.service.insur.*;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.insur.CmBxCommissionExt;
import com.howbuy.crm.hb.web.dto.insur.CommissionOptViewDto;
import com.howbuy.crm.organization.dto.OrgLayerInfo;
import com.howbuy.crm.organization.service.HbOrganizationService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: 销售业绩
 * @reason:
 * @Date: 2019/12/17 9:46
 */
@Controller
@RequestMapping("/insur")
@Slf4j
public class CmBxPrebookCommissionController  extends BaseController {

    @Autowired
    private CmBxPrebookinfoService cmBxPrebookinfoService;

    @Autowired
    private CmBxPrebookCommissionService cmBxPrebookCommissionService;
    
    @Autowired
    private CmBxPrebookBuyinfoService cmBxPrebookBuyinfoService;
    
    @Autowired
	private PageVisitLogService pageVisitLogService;

    @Autowired
    private HbOrganizationService coreOrganizationService;

    @Autowired
    private CommissionBusinessService commissionBusinessService;
    @Autowired
    private CommissionSettleBusinessService  commissionSettleBusinessService;
    
    @RequestMapping("/listinsurcommission.html")
    public String listinsurcommission(){
        return "insur/listinsurcommission";
    }

    
    
    /**
     * @description:(获取组织架构的 Map信息)
     * @param orgCodeList
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2024/10/23 11:13
     * @since JDK 1.8
     */
    private Map<String, OrgLayerInfo> getCenterOrgMap(List<String> orgCodeList){
        if(CollectionUtils.isEmpty(orgCodeList)){
            return Maps.newHashMap();
        }
        //组织架构所属中心
        return coreOrganizationService.getOrgLayerInfoByOrgCodeList(orgCodeList);
    }
    
    
    @RequestMapping("/listinsurcommissionByPageJson.do")
    @ResponseBody
    public Object listinsurcommissionByPageJson(HttpServletRequest request) throws Exception{
        // 设置查询参数
        Map<String, String> param = new ParamUtil(request).getParamMap();
        PageData<InsureCommissionPageDto> pageData = cmBxPrebookinfoService.listCmBxPrebookinfoCommByPage(param);
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<InsureCommissionPageDto> listdata = pageData.getListData();
        BigDecimal totgathercommin = new  BigDecimal(0);
        BigDecimal totgathercommout = new  BigDecimal(0);
        BigDecimal totrealcommission = new  BigDecimal(0);
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        Map<String, String> allOrgMap = consOrgCache.getAllOrgMap();
        ConstantCache constantCache = ConstantCache.getInstance();
        Map<String, String> upOrgMapCache = consOrgCache.getUpOrgMapCache();
        Map<String, String> allUserMap = consOrgCache.getAllUserMap();

        //列表中 组织机构 所属中心  map
        List<String> orgCodes = listdata.stream().filter(it->StringUtil.isNotNullStr(it.getOutletcode())).map(InsureCommissionPageDto::getOutletcode).collect(Collectors.toList());
        Map<String, OrgLayerInfo>  orgMap=getCenterOrgMap(orgCodes);

        for (InsureCommissionPageDto info : listdata) {
            String uporgcode = upOrgMapCache.get(info.getOrgcode());
            if("0".equals(uporgcode)){
                info.setUporgname(allOrgMap.get(info.getOrgcode()));
            }else{
                info.setUporgname(allOrgMap.get(uporgcode));
            }
            info.setOrgcode(allOrgMap.get(info.getOrgcode()));
            info.setConscode(allUserMap.get(info.getConscode()));
            info.setPayyears(constantCache.getVal("insurpayyears", info.getPayyears()));
            if(info.getGathercommin() != null){
            	totgathercommin = totgathercommin.add(info.getGathercommin());
            }
            // 根据投顾的所属部门号查询所属中心  2024年10月23日 修改逻辑，重构。 原逻辑性能问题。且orgcode为空，异常！
            if(orgMap.get(info.getOutletcode()) != null){
                info.setOutletcode(orgMap.get(info.getOutletcode()).getCenterOrgName());
            }

            if(info.getGathercommout() != null){
            	totgathercommout = totgathercommout.add(info.getGathercommout());
            }
            if(info.getRealcommission() != null){
            	totrealcommission = totrealcommission.add(info.getRealcommission());
            }
            if(StringUtil.isNotNullStr(info.getCaltime())){
            	info.setSkdt(DateTimeUtil.getWorkDay(info.getCaltime(), 5));
            	info.setSkdt(DateTimeUtil.getIO2O3O(info.getSkdt()));
            }
            if(StringUtil.isNotNullStr(info.getInsurstate())){
            	info.setInsurstateval(constantCache.getVal("insurstate", info.getInsurstate()));
            }
            if(StringUtil.isNotNullStr(info.getPaystate())){
            	info.setPaystateval(constantCache.getVal("insurpaystate", info.getPaystate()));
            }
            if(StringUtil.isNotNullStr(info.getVisitstate())){
            	info.setVisitstateval(constantCache.getVal("insurvisitstate", info.getVisitstate()));
            }
            if(StringUtil.isNotNullStr(info.getInsurrenewalstate())){
            	info.setInsurrenewalstateval(constantCache.getVal("insurrenewalstate", info.getInsurrenewalstate()));
            }
            if(StringUtil.isNotNullStr(info.getProdtype())){
                info.setProdtypeVal(constantCache.getVal("insurprodtype", info.getProdtype()));
            }
            //【A0191】佣金结算支持多汇率 :增加列： 应收佣金-内（外币）、应收佣金-外（外币）、实收佣金（外币）
            fillSettleInfo(info);

        }
        InsureCommissionPageDto info = new InsureCommissionPageDto();
        info.setGathercommin(totgathercommin);
        info.setGathercommout(totgathercommout);
        info.setRealcommission(totrealcommission);
        listdata.add(info);
        resultMap.put("rows", listdata);
        return resultMap;
    }


    /**
     * @description:(拆单明细查看)
     * @param
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2024/10/8 9:16
     * @since JDK 1.8
     */
    @RequestMapping("/getCommissionInfo.do")
    @ResponseBody
    public ReturnMessageDto<CmBxPrebookCommission> getCommissionInfo(String commissionId){
        if(StringUtil.isEmpty(commissionId)){
            return ReturnMessageDto.fail("佣金id不能为空");
        }
        CmBxPrebookCommission commissionInfo= commissionBusinessService.getCommission(new BigDecimal(commissionId));
        if(commissionInfo == null){
            log.info("根据commissionId:{} 获取佣金信息失败!",commissionId);
            return ReturnMessageDto.fail(String.format("根据commissionId:%s 获取佣金信息失败!",commissionId));
        }
        return ReturnMessageDto.ok("",commissionInfo);
    }



    /**
     * @description:(拆单明细查看)
     * @param
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2024/10/8 9:16
     * @since JDK 1.8
     */
    @RequestMapping("/validateCommissionOpt.do")
    @ResponseBody
    public ReturnMessageDto<CommissionOptViewDto> validateCommissionOpt(String sids,String commissionOptType){
        if(StringUtil.isEmpty(sids)){
            return ReturnMessageDto.fail("佣金id列表不能为空");
        }
        List<BigDecimal> commissionIdList =
                Arrays.asList(sids.split(SEPARATOR_COMMA)).stream().map(it -> new BigDecimal(it)).collect(Collectors.toList());;
        List<CmBxPrebookCommission> commissionInfoList= commissionBusinessService.getCommissionList(commissionIdList);
        if(CollectionUtils.isEmpty(commissionInfoList)){
            return ReturnMessageDto.fail("无可用佣金明细!");
        }

        CommissionOptEnum optTypeEnum = CommissionOptEnum.getEnum(commissionOptType);
        if(optTypeEnum == null){
            return ReturnMessageDto.fail("佣金结算操作类型错误!");
        }

        CommissionOptViewDto optVo = new CommissionOptViewDto();
        /**
         *佣金结算相关操作  数据类型：
         * 1- 对 佣金ID 操作
         * 2- 对 佣金明细拆单后的明细ID 操作
         */
        CommissionOptDataEnum optDataEnum=null;

        //提示：存在拆单明细数据，请单独结算！
        if(commissionInfoList.size() > 1){
            //不能包含：已拆单数据
            long splitCount=commissionInfoList.stream().filter(it -> YesOrNoEnum.YES.getCode().equals(it.getSplitFlag())).count();
            if(splitCount>0){
                return ReturnMessageDto.fail("存在拆单明细数据，请单独结算！");
            }
            //勾选多个佣金， 排除已拆单数据， 则说明：勾选多个佣金， 一定是  根据佣金明细 进行结算
            optDataEnum=CommissionOptDataEnum.OPT_COMMISSION;
        }else{
            //勾选一个佣金， 判断是否拆单，决定   根据佣金ID | 根据佣金拆单  进行结算
            CmBxPrebookCommission singleCommission = commissionInfoList.get(0);
            if(YesOrNoEnum.YES.getCode().equals(singleCommission.getSplitFlag())){
                //勾选一个佣金， 且已拆单， 则说明：勾选一个佣金， 一定是  根据佣金明细 进行结算
                optDataEnum=CommissionOptDataEnum.OPT_SPLIT;
            }else{
                //勾选一个佣金， 且未拆单， 则说明：勾选一个佣金， 一定是  根据佣金ID 进行结算
                optDataEnum=CommissionOptDataEnum.OPT_COMMISSION;
            }
        }

        //佣金明细的操作  校验 佣金明细 状态
        if(optDataEnum==CommissionOptDataEnum.OPT_COMMISSION){
            //校验 前置状态
            CommissionStateEnum preRequireStateEnum = optTypeEnum.getPreRequireStateEnum();
            long invalidStateCount = commissionInfoList.stream().filter(it -> !preRequireStateEnum.getCode().equals(it.getCommstate())).count();
            if(invalidStateCount>0){
                return ReturnMessageDto.fail(String.format("只有状态为：%s的数据才可%s，请重新勾选!",preRequireStateEnum.getDescription(),optTypeEnum.getDescription()));
            }
        }
        //拆单的操作  展示 拆单需要的 列表信息
        if(optDataEnum==CommissionOptDataEnum.OPT_SPLIT){
            //暂无校验
        }

        optVo.setOptDataType(optDataEnum.getCode());
        optVo.setCommissionOptType(optTypeEnum.getCode());
        optVo.setCommissionOptTypeDesc( optTypeEnum.getDescription());
        optVo.setCommissionInfoList(commissionInfoList);
        optVo.setCommissionIdList(commissionInfoList.stream().map(CmBxPrebookCommission::getId).collect(Collectors.toList()));

        //使用 returnList  ，前端判断 操作的 佣金信息
        ReturnMessageDto<CommissionOptViewDto> resp= ReturnMessageDto.ok();
        resp.setReturnObject(optVo);
        return resp;
    }



    /**
     * @description:(佣金拆单明细的 操作页面)
     * @param commissionId
     * @param commissionOptType
     * @return org.springframework.web.servlet.ModelAndView
     * @author: haoran.zhang
     * @date: 2024/10/21 10:12
     * @since JDK 1.8
     */
    @RequestMapping("/toSplitOptView.do")
    public ModelAndView toSplitOptView(String commissionId,String commissionOptType){
        //待处理的佣金 id 列表
//        List<BigDecimal> commissionIdList = Arrays.asList(commissionIds.split(SEPARATOR_COMMA)).stream().map(it -> new BigDecimal(it)).collect(Collectors.toList());

        Map<String, Object> model = Maps.newHashMap();


//        CommissionOptDataEnum optDataEnum=CommissionOptDataEnum.getEnum(optDataType);
        CommissionOptEnum optTypeEnum = CommissionOptEnum.getEnum(commissionOptType);

        //如果是操作 拆分数据
//        if(optDataEnum==CommissionOptDataEnum.OPT_SPLIT){
//            BigDecimal commissionId = commissionIdList.get(0);
            ReturnMessageDto<CommissionSplitViewDto> resp=commissionBusinessService. getSplitViewDto(new BigDecimal(commissionId));
            if(resp.isSuccess()){
                CommissionSplitViewDto splitViewDto = resp.getReturnObject();
                model.put("splitViewDto",splitViewDto);
            }
//        }

        model.put("commissionOptType",commissionOptType);
        model.put("commissionOptTypeDesc",optTypeEnum.getDescription());
//        model.put("optDataType",optDataType);
//        model.put("commissionIdList",commissionIdList);
        model.put("commissionId",commissionId);
        return  new ModelAndView("insur/splitOptView",model);
    }


    @RequestMapping("/executeOpt.do")
    @ResponseBody
    public ReturnMessageDto<String> executeOpt(CommissionOptVo commissionOptVo){
        commissionOptVo.setOperator(getLoginUserId());
        return commissionSettleBusinessService.executeOpt( commissionOptVo);
    }


    /**
     * @description:(拆单明细查看)
     * @param 	
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2024/10/8 9:16
     * @since JDK 1.8
     */
    @RequestMapping("/viewSplitInfo.do")
    public ModelAndView viewSplitInfo(String commissionId){
        ReturnMessageDto<CommissionSplitViewDto> resp=commissionBusinessService. getSplitViewDto(new BigDecimal(commissionId));
        Map<String, Object> model = Maps.newHashMap();

        model.put("viewDto",resp.getReturnObject());
        return  new ModelAndView("insur/viewSplitInfo",model);
    }

    /**
     * @description:(跳转至 拆单页面  )
     * @param
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2024/10/8 9:17
     * @since JDK 1.8
     */
    @RequestMapping("/splitCommission.do")
    public ModelAndView splitCommission(String commissionId){
        ReturnMessageDto<CommissionSplitViewDto>  resp=commissionBusinessService.prepareSplitViewDto(new BigDecimal(commissionId));
        Map<String, Object> model = Maps.newHashMap();
        model.put("viewDto",resp.getReturnObject());
        model.put("userId",getLoginUserId());

        return  new ModelAndView("insur/splitCommission",model);
    }


    /**
     * @description:(拆分明细 重新计算)
     * @param commissionId	
     * @param splitRatio	
     * @param splitSettleRate
     * @return com.howbuy.crm.base.ReturnMessageDto<com.howbuy.crm.hb.domain.insur.CommissionCalDto>
     * @author: haoran.zhang
     * @date: 2024/10/14 14:30
     * @since JDK 1.8
     */
    @RequestMapping("/getCalculateSplitInfoByRatio.do")
    @ResponseBody
    public ReturnMessageDto<CommissionCalDto> getCalculateSplitInfoByRatio(String commissionId,
                                            BigDecimal splitRatio,
                                            BigDecimal splitSettleRate){
        return commissionBusinessService.
                getCalculateSplitInfoByRatio(new BigDecimal(commissionId),
                splitRatio,
                splitSettleRate);
    }


    /**
     * @description:(删除拆单明细)
     * @param commissionId
     * @return com.howbuy.crm.base.ReturnMessageDto<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/12/11 16:59
     * @since JDK 1.8
     */
    @PostMapping("/deleteSplitInfo.do")
    @ResponseBody
    public ReturnMessageDto<String> deleteSplitInfo(String commissionId){
        try {
            return commissionBusinessService.deleteSplitInfo(new BigDecimal(commissionId),getLoginUserId());
        }catch (Exception e){
            log.error("佣金拆单保存失败",e);
            return ReturnMessageDto.fail("系统异常");
        }
    }




    @PostMapping("/saveSplitInfo.do")
    @ResponseBody
    public ReturnMessageDto<String> saveSplitInfo(@RequestBody CommissionSplitInsertVo insertVo){

        insertVo.setOperator(getLoginUserId());
        try {
            ReturnMessageDto<String> resp=commissionBusinessService.saveSplitInfo(insertVo);
            return resp;
        }catch (Exception e){
            log.error("佣金拆单保存失败",e);
            return ReturnMessageDto.fail("系统异常");
        }
    }


    /**
     * @description:(拆单 前置校验 )
     * @param insertVo
     * @return com.howbuy.crm.base.ReturnMessageDto<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/10/9 13:42
     * @since JDK 1.8
     */
    @PostMapping("/validateSplitInfo.do")
    @ResponseBody
    public ReturnMessageDto<String> validateSplitInfo(@RequestBody CommissionSplitInsertVo insertVo){
        insertVo.setOperator(getLoginUserId());
        try {
            ReturnMessageDto<String> resp=commissionBusinessService.validateSplitInfo(insertVo);
            //校验不通过 拒绝  返回success=false
            if(!resp.isSuccess()){
                return resp;
            }
            //二次确认的  提示信息 。
            ReturnMessageDto<String> alertResp= commissionBusinessService.getSplitAlertInfo(insertVo);
            //返回success=true . returnMsg 为 提示信息
            return  ReturnMessageDto.ok(alertResp.getReturnMsg(),null);
        }catch (Exception e){
            log.error("佣金拆单保存失败",e);
            return ReturnMessageDto.fail("系统异常");
        }
    }


    /**
     * @description:(佣金支持多汇率。外币的相关计算 展示 )
     * @param info
     * @return void
     * @author: haoran.zhang
     * @date: 2024/9/23 14:03
     * @since JDK 1.8
     */
    private void fillSettleInfo(InsureCommissionPageDto info){
         //【A0191】佣金结算支持多汇率 :
         // 增加列： 应收佣金-内（外币）、应收佣金-外（外币）、实收佣金（外币）

        BigDecimal settleRate = info.getSettleRate()==null?BigDecimal.ONE:info.getSettleRate();
//            应收佣金-内（外币） =应收佣金-内 * 结算汇率
        if(info.getGathercommin() != null ){
            info.setDisPlayGathercomminFc(info.getGathercommin().multiply(settleRate).setScale(2, BigDecimal.ROUND_HALF_UP));
        }

//            取值标记1:
//            如果【手工修改】有值，展示  【手工修改-应收佣金-外】
//            否则，展示【应收佣金-外】
//
//            取值标记2:
//            如果【手工修改】有值，展示  【手工修改-实收佣金】
//            否则，展示【实收佣金】

//            应收佣金-外（外币）
//            如果【手工修改】有值，展示  【手工修改-应收佣金-外（外币）】
//            否则，展示
//           【取值标记1  应收佣金-外】*汇率
        info.setDisPlayGathercommoutFc(
                //手工修改应收佣金-外(结算外币)  有值，使用修改的值
                info.getExpSettleGathercommout()!=null
                        ?info.getExpSettleGathercommout()
                        //否则，使用【取值标记1  应收佣金-外】*汇率
                        : (info.getGathercommout()==null?null:
                        info.getGathercommout().multiply(settleRate).setScale(2, BigDecimal.ROUND_HALF_UP)
                )
        );
//            如果【手工修改】有值，展示  【手工修改-实收佣金（外币）】
//            否则，展示
//           【取值标记2  实收佣金】* 汇率
        info.setDisPlayRealcommissionFc(
                //手工修改实收佣金(结算外币)  有值，使用修改的值
                info.getExpSettleRealcommission()!=null
                        ?info.getExpSettleRealcommission()
                        //否则，使用【取值标记2  实收佣金】* 汇率
                        :(info.getRealcommission()==null?null:
                        info.getRealcommission().multiply(settleRate).setScale(2, BigDecimal.ROUND_HALF_UP)
                )
        );
    }

    /**
     * @description:(佣金支持多汇率。外币的相关计算 展示 )
     * @param info
     * @return void
     * @author: haoran.zhang
     * @date: 2024/9/23 14:03
     * @since JDK 1.8
     */
    private void fillSettleInfo(CmBxCommissionExt info){
        //【A0191】佣金结算支持多汇率 :
        // 增加列： 应收佣金-内（外币）、应收佣金-外（外币）、实收佣金（外币）

        BigDecimal settleRate = info.getSettleRate()==null?BigDecimal.ONE:info.getSettleRate();
//            应收佣金-内（外币） =应收佣金-内 * 结算汇率
        if(info.getGathercommin() != null ){
            info.setDisPlayGathercomminFc(info.getGathercommin().multiply(settleRate).setScale(2, BigDecimal.ROUND_HALF_UP));
        }

//            取值标记1:
//            如果【手工修改】有值，展示  【手工修改-应收佣金-外】
//            否则，展示【应收佣金-外】
//
//            取值标记2:
//            如果【手工修改】有值，展示  【手工修改-实收佣金】
//            否则，展示【实收佣金】

//            应收佣金-外（外币）
//            如果【手工修改】有值，展示  【手工修改-应收佣金-外（外币）】
//            否则，展示
//           【取值标记1  应收佣金-外】*汇率
        info.setDisPlayGathercommoutFc(
                //手工修改应收佣金-外(结算外币)  有值，使用修改的值
                info.getExpSettleGathercommout()!=null
                        ?info.getExpSettleGathercommout()
                        //否则，使用【取值标记1  应收佣金-外】*汇率
                        : (info.getGathercommout()==null?null:
                        info.getGathercommout().multiply(settleRate).setScale(2, BigDecimal.ROUND_HALF_UP)
                )
        );
//            如果【手工修改】有值，展示  【手工修改-实收佣金（外币）】
//            否则，展示
//           【取值标记2  实收佣金】* 汇率
        info.setDisPlayRealcommissionFc(
                //手工修改实收佣金(结算外币)  有值，使用修改的值
                info.getExpSettleRealcommission()!=null
                        ?info.getExpSettleRealcommission()
                        //否则，使用【取值标记2  实收佣金】* 汇率
                        :(info.getRealcommission()==null?null:
                        info.getRealcommission().multiply(settleRate).setScale(2, BigDecimal.ROUND_HALF_UP)
                )
        );
    }
    
    /**
     * 导出操作
     * @param request
     * @param response
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/exportCommission.do")
    public void exportCommission(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
        Map<String, String> param = new ParamUtil(request).getParamMap();
        List<InsureCommissionPageDto> exportList = cmBxPrebookinfoService.listCmBxPrebookinfoCommByExp(param);
        LinkedHashMap<String, List<InsureCommissionPageDto>> sortList = exportList.stream().collect(Collectors.groupingBy(InsureCommissionPageDto::getInsurid, LinkedHashMap::new, Collectors.toList()));
        exportList.clear();
        // 根据保单号进行分组
        for (List<InsureCommissionPageDto> it : sortList.values()) {
            exportList.addAll(it);
        }
        //列表中 组织机构 所属中心  map
        List<String> orgCodes = exportList.stream().filter(it->StringUtil.isNotNullStr(it.getOutletcode())).map(InsureCommissionPageDto::getOutletcode).collect(Collectors.toList());
        Map<String, OrgLayerInfo>  orgMap=getCenterOrgMap(orgCodes);

        BigDecimal totgathercommin = new  BigDecimal(0);
        BigDecimal totgathercommout = new  BigDecimal(0);
        BigDecimal totrealcommission = new  BigDecimal(0);
        List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
		User user = (User) request.getSession().getAttribute("loginUser");
		String ip = getIpAddr(request);
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        Map<String, String> allOrgMap = consOrgCache.getAllOrgMap();
        ConstantCache constantCache = ConstantCache.getInstance();
        Map<String, String> upOrgMapCache = consOrgCache.getUpOrgMapCache();
        Map<String, String> allUserMap = consOrgCache.getAllUserMap();
        for (InsureCommissionPageDto info : exportList) {
            String uporgcode = upOrgMapCache.get(info.getOrgcode());
            if("0".equals(uporgcode)){
                info.setUporgname(allOrgMap.get(info.getOrgcode()));
            }else{
                info.setUporgname(allOrgMap.get(uporgcode));
            }
            info.setOrgcode(allOrgMap.get(info.getOrgcode()));
            info.setConscode(allUserMap.get(info.getConscode()));
            info.setPayyears(constantCache.getVal("insurpayyears", info.getPayyears()));
            if(info.getGathercommin() != null){
            	totgathercommin = totgathercommin.add(info.getGathercommin());
            }
            // 根据投顾的所属部门号查询所属中心  2024年10月23日 修改逻辑，重构。 原逻辑性能问题。且orgcode为空，异常！
            if(orgMap.get(info.getOutletcode()) != null){
                info.setOutletcode(orgMap.get(info.getOutletcode()).getCenterOrgName());
            }

            if(info.getGathercommout() != null){
            	totgathercommout = totgathercommout.add(info.getGathercommout());
            }
            if(info.getRealcommission() != null){
            	totrealcommission = totrealcommission.add(info.getRealcommission());
            }
            if(StringUtil.isNotNullStr(info.getCaltime())){
            	info.setSkdt(DateTimeUtil.getWorkDay(info.getCaltime(), 5));
            	info.setSkdt(DateTimeUtil.getIO2O3O(info.getSkdt()));
            }
            if(StringUtil.isNotNullStr(info.getInsurstate())){
            	info.setInsurstateval(constantCache.getVal("insurstate", info.getInsurstate()));
            }
            if(StringUtil.isNotNullStr(info.getPaystate())){
            	info.setPaystateval(constantCache.getVal("insurpaystate", info.getPaystate()));
            }
            if(StringUtil.isNotNullStr(info.getVisitstate())){
            	info.setVisitstateval(constantCache.getVal("insurvisitstate", info.getVisitstate()));
            }
            if(StringUtil.isNotNullStr(info.getInsurrenewalstate())){
            	info.setInsurrenewalstateval(constantCache.getVal("insurrenewalstate", info.getInsurrenewalstate()));
            }
            if(StringUtil.isNotNullStr(info.getProdtype())){
                info.setProdtypeVal(constantCache.getVal("insurprodtype", info.getProdtype()));
            }

            //【A0191】佣金结算支持多汇率 :增加列： 应收佣金-内（外币）、应收佣金-外（外币）、实收佣金（外币）
            fillSettleInfo(info);
            //2025年1月15日 变更 。如果 结算货币为空， 结算汇率不展示
            if(StringUtil.isEmpty(info.getSettleCurrency())){
                info.setSettleRate(null);
            }
            //翻译：
            info.setSettleCurrency(CurrencyEnum.getDescription(info.getSettleCurrency()));
            info.setCurrency(CurrencyEnum.getDescription(info.getCurrency()));

            //导出日志
            PageVisitLog pageVisitLog = new PageVisitLog();
            pageVisitLog.setConscustno(info.getConscustno());
            pageVisitLog.setUserId(user.getUserId());
            pageVisitLog.setVisitUrl(request.getRequestURI());
            pageVisitLog.setOperation("佣金结算导出");
            pageVisitLog.setVisitTime(new Date());
            pageVisitLog.setPreid(info.getId());
            pageVisitLog.setIp(ip);
            listlog.add(pageVisitLog);
        }
        InsureCommissionPageDto info = new InsureCommissionPageDto();
        info.setCustname("汇总");
        info.setGathercommin(totgathercommin);
        info.setGathercommout(totgathercommout);
        info.setRealcommission(totrealcommission);
        exportList.add(info);
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("公司佣金导出.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String[] columnName = {
                    "预约单号","投保人","受保人",
                    "所属投顾(预约时)","中心","所属区域(预约时)","所属部门(预约时)",
                    "产品名称","合作渠道","产品类型","保险公司",
                    "缴费年限","年缴保费","保障年限",
                    "保单号","冷静期截止日期","收款日期","签单日期","核保通过日期","佣金结算日期","应收日期","公司佣金率","币种",
                    "应收佣金-内","应收佣金-外","开票日期",
                    "实收佣金","结算日期",
                    //【A0191】佣金结算支持多汇率  增加字段
                    "结算外币","结算汇率","应收佣金-内（外币）","应收佣金-外（外币）","实收佣金（外币）",
                    "保单状态","缴费年份","缴费状态","回访状态",
                    "续期状态","续期日期","续期实缴保费",
                    "备忘"};

            String[] beanProperty = {
                    "id","custname","insurname",
                    "conscode","outletcode","uporgname","orgcode",
                    "fundname","channname","prodtypeVal","compname",
                    "payyears","yearamk","ensureyears",
                    "insurid","caltime","skdt","signdt","passdt","ratiodt","gatheringdt","comcommissionratio","currency",
                    "gathercommin","gathercommout","ticketdt",
                    "realcommission","accountdt",
                    //【A0191】佣金结算支持多汇率  增加字段
                    "settleCurrency","settleRate","disPlayGathercomminFc","disPlayGathercommoutFc","disPlayRealcommissionFc",
                    "insurstateval","yearno","paystateval","visitstateval",
                    "insurrenewalstateval","realpaydt","yearamk",
                    "settleRemark"};
            ExcelWriter.writeExcel(os, "公司佣金", 0, exportList, columnName, beanProperty);
            os.close(); // 关闭流
            for(PageVisitLog log : listlog){
            	pageVisitLogService.recordLog(log);
            }
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }

    }

    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    @ResponseBody
    @RequestMapping("/showbuycommission.do")
    public ModelAndView showbuycommission(HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
		String buyid = request.getParameter("buyid");
        Map<String,Object> map = new HashMap<String,Object>();
        Map<String,Object> param = new HashMap<String,Object>();
		param.put("buyid", buyid);
		List<CmBxPrebookCommission> list = cmBxPrebookCommissionService.listCmBxPrebookCommission(param);

        List<CmBxCommissionExt> extList = Lists.newArrayList();
		for(CmBxPrebookCommission commission : list){
            CmBxCommissionExt extInfo=new CmBxCommissionExt();
            BeanUtils.copyProperties(commission, extInfo);

            extInfo.setCommstate(ConstantCache.getInstance().getVal("insurfinstate", extInfo.getCommstate()));
			if(extInfo.getExpgathercommout() != null){
                extInfo.setGathercommout(extInfo.getExpgathercommout());
			}
			if(extInfo.getExprealcommission() != null){
                extInfo.setRealcommission(extInfo.getExprealcommission());
			}
			if(StringUtil.isNotNullStr(extInfo.getExpticketdt())){
                extInfo.setTicketdt(extInfo.getExpticketdt());
			}
			if(StringUtil.isNotNullStr(extInfo.getExpaccountdt())){
                extInfo.setAccountdt(extInfo.getExpaccountdt());
			}

            //赋值
            fillSettleInfo(extInfo);
            //获取 币种
            CmBxPrebookinfo  preInfo=null;
            if(extInfo.getBuyid()!=null){
                preInfo=getBxPreInfoByBuyId(extInfo.getBuyid());
            }
            log.info("根据buyId:{} 获取保险产品购买信息：{}",extInfo.getBuyid(), JSON.toJSONString(preInfo));
            if(preInfo != null){
                //翻译信息
                extInfo.setCurrency(preInfo.getCurrency());
            }

            //翻译
            extInfo.setSettleCurrencyDesc(CurrencyEnum.getDescription(extInfo.getSettleCurrency()));
            extInfo.setCurrencyDesc(CurrencyEnum.getDescription(extInfo.getCurrency()));
            extList.add(extInfo);
		}
        map.put("list", extList);
        return new ModelAndView("insur/showbuycommission", "map", map);
    }
    
    @ResponseBody
    @RequestMapping("/editCommission.do")
    public ModelAndView editCommission(HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
		String id = request.getParameter("id");
        Map<String,Object> map = new HashMap<String,Object>();
        Map<String,Object> param = new HashMap<String,Object>();
		param.put("id", id);
		CmBxPrebookCommission comm = cmBxPrebookCommissionService.getCmBxPrebookCommission(param);
		comm.setCommstate(ConstantCache.getInstance().getVal("insurfinstate", comm.getCommstate()));

        BigDecimal settleRate = comm.getSettleRate()==null?BigDecimal.ONE:comm.getSettleRate();

//        应收佣金-内（外币）
        BigDecimal  settleGathercommin = comm.getGathercommin()!=null
                ?comm.getGathercommin().multiply(settleRate).setScale(2, BigDecimal.ROUND_HALF_UP)
                :null;
        //应收佣金-内（外币） = 应收佣金-内  *  结算汇率。 不允许编辑
        map.put("settleGathercommin", settleGathercommin);

        //手工修改应收佣金-外
        BigDecimal  editGathercommout = comm.getExpgathercommout() !=null ? comm.getExpgathercommout() : comm.getGathercommout();
        comm.setExpgathercommout(editGathercommout);
        //手工修改 应收佣金-外(结算外币)
        BigDecimal  editSettleGathercommout=
                //手工修改应收佣金-外(结算外币)  有值，使用修改的值
                comm.getExpSettleGathercommout()!=null
                        ?comm.getExpSettleGathercommout()
                        //否则，使用【取值标记1  应收佣金-外】*汇率
                        : (editGathercommout==null?null:
                        editGathercommout.multiply(settleRate).setScale(2, BigDecimal.ROUND_HALF_UP)
                );
        comm.setExpSettleGathercommout(editSettleGathercommout);

        //手工修改实收佣金
        BigDecimal editRealcommission = comm.getExprealcommission() !=null ? comm.getExprealcommission() : comm.getRealcommission();
        comm.setExprealcommission(editRealcommission);

        //手工修改实收佣金(结算外币)
//            如果【手工修改】有值，展示  【手工修改-实收佣金（外币）】
//            否则，展示
//           【取值标记2  实收佣金】* 汇率
        BigDecimal editSettleRealcommission=
                //手工修改实收佣金(结算外币)  有值，使用修改的值
                comm.getExpSettleRealcommission()!=null
                        ?comm.getExpSettleRealcommission()
                        //否则，使用【取值标记2  实收佣金】* 汇率
                        :(editRealcommission==null?null:
                        editRealcommission.multiply(settleRate).setScale(2, BigDecimal.ROUND_HALF_UP)
                );
        comm.setExpSettleRealcommission(editSettleRealcommission);



		if(StringUtil.isNullStr(comm.getExpticketdt())){
			comm.setExpticketdt(comm.getTicketdt());
		}
		if(StringUtil.isNullStr(comm.getExpaccountdt())){
			comm.setExpaccountdt(comm.getAccountdt());
		}
        map.put("info", comm);
        map.put("splitFlag",YesOrNoEnum.YES.getCode().equals(comm.getSplitFlag()));

        //获取 币种
        CmBxPrebookinfo  preInfo=null;
        if(comm.getBuyid()!=null){
            preInfo=getBxPreInfoByBuyId(comm.getBuyid());
        }
        log.info("根据buyId:{} 获取保险产品购买信息：{}",comm.getBuyid(), JSON.toJSONString(preInfo));
        if(preInfo != null){
            map.put("currency", preInfo.getCurrency());
            map.put("currencyDesc", CurrencyEnum.getDescription(preInfo.getCurrency()));
        }
        return new ModelAndView("insur/editcommission", "map", map);
    }




    /**
     * @description:(根据buydId 逐步获取 创新产品预约信息)
     * @param buyId
     * @return com.howbuy.crm.hb.domain.insur.CmBxPrebookinfo
     * @author: haoran.zhang
     * @date: 2024/9/23 16:05
     * @since JDK 1.8
     */
    private CmBxPrebookinfo getBxPreInfoByBuyId(BigDecimal buyId){
        CmBxPrebookBuyinfo buyInfo= getBuyInfo( buyId);
        if(buyInfo == null){
            log.info("根据buyId:{} 获取保险产品购买信息失败!",buyId);
            return null;
        }
        if(buyInfo.getPreid() == null){
            log.info("buyId:{} ,保险产品预约ID为空!",buyId);
            return null;
        }
        CmBxPrebookinfo preInfo = getBxPreBookInfo(buyInfo.getPreid());
        if(preInfo == null){
            log.info("根据buyId:{} , preId :{}  获取保险产品预约信息失败!",buyId,buyInfo.getPreid());
            return null;
        }
        return preInfo;
    }



    /**
     * 获取 保险产品 购买信息
     * @param buyId
     * @return
     */
    private  CmBxPrebookBuyinfo  getBuyInfo(BigDecimal buyId){
        Map<String, Object> map = Maps.newHashMap();
        map.put("id", buyId);
        return cmBxPrebookBuyinfoService.getCmBxPrebookBuyinfo(map);
    }

    /**
     * @description:(获取保险产品 创新产品预约信息)
     * @param preId
     * @return com.howbuy.crm.hb.domain.insur.CmBxPrebookinfo
     * @author: haoran.zhang
     * @date: 2024/9/23 15:59
     * @since JDK 1.8
     */
    private CmBxPrebookinfo getBxPreBookInfo(BigDecimal preId){
        Map<String,Object> parampreinfo = Maps.newHashMap();
        parampreinfo.put("id", preId);
        return cmBxPrebookinfoService.getCmBxPrebookinfo(parampreinfo);
    }
    
    @RequestMapping("/saveUpdateCommission.do")
    @ResponseBody
    public ReturnMessageDto<String> saveUpdateCommission(HttpServletRequest request){
        String id = request.getParameter("id");
        String ticketdt = request.getParameter("ticketdt");
        String accountdt = request.getParameter("accountdt");
        String realcommission = request.getParameter("realcommission");
        String gathercommout = request.getParameter("gathercommout");
        String expcommissionratio = request.getParameter("expcommissionratio");
        String ticketproject = request.getParameter("ticketproject");
        String ticketmsg = request.getParameter("ticketmsg");
        String addr = request.getParameter("addr");
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        //查询预约
//        Map<String,Object> param = new HashMap<String,Object>();

        CmBxPrebookCommission updateCommission= new CmBxPrebookCommission();

        updateCommission.setId(new BigDecimal(id));
        updateCommission.setExpcreator(userlogin.getUserId());
        if(StringUtil.isNotNullStr(ticketdt)){
        	if(!ticketdt.matches("^[0-9]{8}$")){
                return  ReturnMessageDto.fail("开票日期格式不对！");
			}else{
                updateCommission.setExpticketdt(ticketdt);
			}
        }else{
            updateCommission.setExpticketdt("");
        }
        if(StringUtil.isNotNullStr(accountdt)){
        	if(!accountdt.matches("^[0-9]{8}$")){
                return  ReturnMessageDto.fail("结算日期格式不对！");
			}else{
                updateCommission.setExpaccountdt(accountdt);
			}
        }else{
            updateCommission.setExpaccountdt("");
        }
        if(StringUtil.isNotNullStr(realcommission)){
        	if(new BigDecimal(realcommission).compareTo(new BigDecimal("*************")) > 0){
                return  ReturnMessageDto.fail("实收佣金金额太大！");
			}else{
                updateCommission.setExprealcommission(new BigDecimal(realcommission));
			}
        }else{
            updateCommission.setExprealcommission(null);
        }
        if(StringUtil.isNotNullStr(gathercommout)){
        	if(new BigDecimal(gathercommout).compareTo(new BigDecimal("*************")) > 0){
                return  ReturnMessageDto.fail("应收佣金-外金额太大！");
			}else{
                updateCommission.setExpgathercommout(new BigDecimal(gathercommout));
			}
        }else{
            updateCommission.setExpgathercommout(null);
        }
        if(StringUtil.isNotNullStr(expcommissionratio)){
        	if(new BigDecimal(expcommissionratio).compareTo(new BigDecimal("1000000")) > 0 || new BigDecimal(expcommissionratio).compareTo(new BigDecimal("-1000000")) < 0){
        		return  ReturnMessageDto.fail("公司佣金率调整超出范围！");
			}else{
                updateCommission.setExpcommissionratio(new BigDecimal(expcommissionratio));
			}
        }else{
            updateCommission.setExpcommissionratio(null);
        }
        if(StringUtil.isNotNullStr(ticketproject)){
        	if(ticketproject.length() > 60){
                return  ReturnMessageDto.fail("开票项目字数不能超过60个字符！");
        	}else{
                updateCommission.setTicketproject(ticketproject);
        	}
        }else{
            updateCommission.setTicketproject(null);
        }
        if(StringUtil.isNotNullStr(ticketmsg)){
        	if(ticketmsg.length() > 60){
                return  ReturnMessageDto.fail("发票信息字数不能超过60个字符！");
        	}else{
                updateCommission.setTicketmsg(ticketmsg);
        	}
        }else{
            updateCommission.setTicketmsg(null);
        }
        if(StringUtil.isNotNullStr(addr)){
        	if(addr.length() > 100){
                return  ReturnMessageDto.fail("邮寄地址字数不能超过100个字符！");
        	}else{
                updateCommission.setAddr(addr);
        	}
        }else{
            updateCommission.setAddr(null);
        }
        String settleCurrency = request.getParameter("settleCurrency");
        String settleRate = request.getParameter("settleRate");

        //2025年2月7日 新增校验：结算外币”，“结算汇率”二者任一有值，则另一项也会变成必填项
        if(StringUtil.isNotNullStr(settleCurrency) && StringUtil.isNullStr(settleRate)){
            return ReturnMessageDto.fail("请填写结算汇率！");
        }
        if(StringUtil.isNullStr(settleCurrency) && StringUtil.isNotNullStr(settleRate)){
            return ReturnMessageDto.fail("请填写结算外币！");
        }
        updateCommission.setSettleCurrency(StringUtil.isNotNullStr(settleCurrency)?settleCurrency:null);
        if(StringUtil.isNotNullStr(settleRate)){
            updateCommission.setSettleRate(new BigDecimal(settleRate));
        }else{
            updateCommission.setSettleRate(null);
        }

        String expSettleGathercommout= request.getParameter("expSettleGathercommout");
        if(StringUtil.isNotNullStr(expSettleGathercommout)){
            updateCommission.setExpSettleGathercommout(new BigDecimal(expSettleGathercommout));
        }else{
            updateCommission.setExpSettleGathercommout(null);
        }

        String expSettleRealcommission= request.getParameter("expSettleRealcommission");
        if(StringUtil.isNotNullStr(expSettleRealcommission)){
            updateCommission.setExpSettleRealcommission(new BigDecimal(expSettleRealcommission));
        }else{
            updateCommission.setExpSettleRealcommission(null);
        }

        cmBxPrebookCommissionService.updateExpCommission(updateCommission);
        return ReturnMessageDto.ok();
    }


    /**
     * @description:(请在此添加描述)
     * @param commissionId	
     * @param settleRemark
     * @return com.howbuy.crm.base.ReturnMessageDto<java.lang.String>
     * @author: haoran.zhang
     * @date: 2024/9/24 9:16
     * @since JDK 1.8
     */
    @RequestMapping("/saveSettleRemark.do")
    @ResponseBody
    public ReturnMessageDto<String> saveSettleRemark (String commissionId,String settleRemark){
        try {
            cmBxPrebookCommissionService.updateSettleRemark(new BigDecimal(commissionId),settleRemark);
        }catch (Exception ex){
            log.error("保存结算备注异常：", ex);
            return ReturnMessageDto.fail("系统异常！");
        }
        return ReturnMessageDto.ok("保存成功！");
    }
    
    /**
     * 批量处理公司佣金率计算
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/batchCalculateGsCommAmk.do")
    @ResponseBody
    public String batchCalculateGsCommAmk(HttpServletRequest request) throws Exception{
    	String result="";
    	User user = (User) request.getSession().getAttribute("loginUser");
    	List<String> listpayid = new ArrayList<String>();
    	// 设置查询参数
        String ids = request.getParameter("paylistids");
        if(StringUtil.isNotNullStr(ids) && ids.contains(",")){
        	for(String id :ids.split(",")){
        		listpayid.add(id);
        	}
        }else{
        	listpayid.add(ids);
        }
        StringBuilder errMsg = new StringBuilder();
        cmBxPrebookBuyinfoService.batchCalculateGsCommAmk(listpayid, user.getUserId(), errMsg);
        if (!"".equals(errMsg.toString()) && !",".equals(errMsg.toString())) {
            result = errMsg.toString();
        } else {
            result = "success";
        }
        return result;
    }
}
