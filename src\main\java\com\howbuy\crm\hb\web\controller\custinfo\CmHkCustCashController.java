/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.web.controller.custinfo;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.howbuy.crm.base.CurrencyEnum;
import com.howbuy.crm.cashbalance.CmQueryHkCashBalanceService;
import com.howbuy.crm.cashbalance.request.CmQueryHkCashBalanceRequest;
import com.howbuy.crm.cashbalance.response.CmQueryHkCashBalanceResponse;
import com.howbuy.crm.hb.domain.hkconscust.HkConscustVO;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.hkconscust.HkConscustService;
import com.howbuy.crm.hb.service.outerservice.CrmAccountOuterService;
import com.howbuy.crm.hb.web.response.hkconscust.HwCashBalanceDTO;
import com.howbuy.crm.hb.web.response.hkconscust.RateDTO;
import com.howbuy.crm.hb.web.response.hkpayvoucher.HkPayVoucherDTO;
import com.howbuy.crm.payvoucher.CmQueryHkPayVoucherListFacade;
import com.howbuy.crm.payvoucher.request.HkPayVoucherListRequest;
import com.howbuy.crm.payvoucher.response.HkPayVoucherListResponse;
import com.howbuy.crm.util.AmtOrVolFormateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * @description: 香港客户现金信息
 * <AUTHOR>
 * @date 2025/2/13 11:03
 * @since JDK 1.8
 */

@Slf4j
@Controller
@RequestMapping(value = "/hkCustCash")
public class CmHkCustCashController {

    @Autowired
    private ConscustService conscustService;

    @Autowired
    private CrmAccountOuterService crmAccountOuterService;

    @Autowired
    private HkConscustService hkConscustService;

    @Autowired
    private CmQueryHkCashBalanceService cmQueryHkCashBalanceService;

    @Autowired
    private CmQueryHkPayVoucherListFacade cmQueryHkPayVoucherListFacade;

    /**
     * @api {GET} /hkCustCash/viewHwBalanceDetails viewHwBalanceDetails()
     * @apiVersion 1.0.0
     * @apiGroup CmHkCustCashController
     * @apiName viewHwBalanceDetails()
     * @apiDescription 香港现金余额明细弹窗
     * @apiParam (请求参数) {String} consCustNo
     * @apiParamExample 请求参数示例
     * consCustNo=AFKs66mk
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":false,"status":"METHOD_NOT_ALLOWED"}
     */
    @GetMapping("/viewHwBalanceDetails")
    public ModelAndView viewHwBalanceDetails(@RequestParam("consCustNo") String consCustNo) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject("consCustNo", consCustNo);

        modelAndView.setViewName("/custinfo/hkCashBalanceDtl");
        return modelAndView;
    }

    /**
     * @api {POST} /hkCustCash/queryHwBalanceDetails queryHwBalanceDetails()
     * @apiVersion 1.0.0
     * @apiGroup CmHkCustCashController
     * @apiName queryHwBalanceDetails()
     * @apiDescription 香港现金余额明细数据
     * @apiParam (请求参数) {String} consCustNo
     * @apiParamExample 请求参数示例
     * consCustNo=o7
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * {}
     */
    @ResponseBody
    @PostMapping("/queryHwBalanceDetails")
    public Map<String, Object> queryHwBalanceDetails(@RequestParam("consCustNo") String consCustNo) {
        Map<String, Object> resultMap = new HashMap<>();

        try {
            // 香港账号
            HkConscustVO hkConscustVO = crmAccountOuterService.queryHkCustInfoByCustNo(consCustNo);

            CmQueryHkCashBalanceRequest cmQueryHkCashBalanceRequest = new CmQueryHkCashBalanceRequest();
            cmQueryHkCashBalanceRequest.setHkCustNo(hkConscustVO.getHkTxAcctNo());
            // 查询香港账号[现金余额]，并返回换算成【人民币】金额
            cmQueryHkCashBalanceRequest.setDisCurrency(CurrencyEnum.RMB.getCode());
            CmQueryHkCashBalanceResponse rmbResponse = cmQueryHkCashBalanceService.execute(cmQueryHkCashBalanceRequest);
            //  查询香港账号[现金余额]，并返回换算成【美元】金额
            cmQueryHkCashBalanceRequest.setDisCurrency(CurrencyEnum.USD.getCode());
            CmQueryHkCashBalanceResponse usdResponse = cmQueryHkCashBalanceService.execute(cmQueryHkCashBalanceRequest);

            // 以 币种 为key，获取当前客户所有币种的现金余额
            Map<String, HwCashBalanceDTO> currencyAndCashDTOMap = new HashMap<>();
            // 1人民币 可以兑换多少其他币种map
            Map<String, RateDTO> rmbRateMap = new HashMap<>();
            // 1美元 可以兑换多少其他币种map
            Map<String, RateDTO> usdRateMap = new HashMap<>();

            if (Objects.nonNull(rmbResponse)
                    && CollectionUtils.isNotEmpty(rmbResponse.getQueryEbrokerCustBalanceDtlDTO())) {
                for (CmQueryHkCashBalanceResponse.QueryEbrokerCustBalanceDtlDTO dtlDTO : rmbResponse.getQueryEbrokerCustBalanceDtlDTO()) {
                    HwCashBalanceDTO hwCashBalanceDTO = new HwCashBalanceDTO();
                    hwCashBalanceDTO.setCurCode(dtlDTO.getCurCode());
                    hwCashBalanceDTO.setCurCodeDesc(CurrencyEnum.getDescription(dtlDTO.getCurCode()));
                    hwCashBalanceDTO.setCurBalance(dtlDTO.getCurBalance());
                    hwCashBalanceDTO.setCurBalanceStr(AmtOrVolFormateUtil.formatByPerciSion(dtlDTO.getCurBalance(), 2));

                    hwCashBalanceDTO.setRmbChangeBalance(dtlDTO.getChangeBalance());
                    hwCashBalanceDTO.setRmbChangeBalanceStr(AmtOrVolFormateUtil.formatByPerciSion(dtlDTO.getChangeBalance(), 2));

                    currencyAndCashDTOMap.put(dtlDTO.getCurCode(), hwCashBalanceDTO);

                    // 1人民币 可以兑换其他币种
                    if (!CurrencyEnum.RMB.getCode().equals(dtlDTO.getCurCode())) {
                        String dataDt = rmbResponse.getDataDt();
                        // dataDt 数据格式为yyyyMMdd 改为yyyy-MM-dd
                        String dataDtStr = formatDate(dataDt);

                        RateDTO rateDTO = new RateDTO();
                        rateDTO.setCurrencyRate(dtlDTO.getCurrencyRate());
                        rateDTO.setDataDt(dataDtStr);
                        rmbRateMap.put(dtlDTO.getCurCode(), rateDTO);
                    }
                }

                if (Objects.nonNull(usdResponse)
                        && CollectionUtils.isNotEmpty(usdResponse.getQueryEbrokerCustBalanceDtlDTO())) {
                    for (CmQueryHkCashBalanceResponse.QueryEbrokerCustBalanceDtlDTO dtlDTO : usdResponse.getQueryEbrokerCustBalanceDtlDTO()) {
                        HwCashBalanceDTO hwCashBalanceDTO = currencyAndCashDTOMap.get(dtlDTO.getCurCode());
                        hwCashBalanceDTO.setUsdChangeBalance(dtlDTO.getChangeBalance());
                        hwCashBalanceDTO.setUsdChangeBalanceStr(AmtOrVolFormateUtil.formatByPerciSion(dtlDTO.getChangeBalance(), 2));

                        // 1美元 可以兑换其他币种
                        if (!CurrencyEnum.USD.getCode().equals(dtlDTO.getCurCode())) {
                            String dataDt = usdResponse.getDataDt();
                            // dataDt 数据格式为yyyyMMdd 改为yyyy-MM-dd
                            String dataDtStr = formatDate(dataDt);

                            RateDTO rateDTO = new RateDTO();
                            rateDTO.setCurrencyRate(dtlDTO.getCurrencyRate());
                            rateDTO.setDataDt(dataDtStr);
                            usdRateMap.put(dtlDTO.getCurCode(), rateDTO);
                        }
                    }
                }
            }

            // 页面展示封装对象
            List<HwCashBalanceDTO> hwCashBalanceDTOS = new ArrayList<>();
            if (MapUtils.isNotEmpty(currencyAndCashDTOMap)) {
                hwCashBalanceDTOS = new ArrayList<>(currencyAndCashDTOMap.values());
                // hwCashBalanceDTOS根据CurCode升序排序
                hwCashBalanceDTOS.sort(Comparator.comparing(HwCashBalanceDTO::getCurCode,
                        Comparator.nullsLast(Comparator.naturalOrder())));

                // 将“总计”数据作为最后一行
                HwCashBalanceDTO hwCashBalanceDTO = createTotalRow(rmbResponse.getTotalBalance(), usdResponse.getTotalBalance());
                hwCashBalanceDTOS.add(hwCashBalanceDTO);
            }

            // 香港现金余额明细List
            resultMap.put("rows", hwCashBalanceDTOS);
            // 汇率换算
            resultMap.put("rateText", buildRateText(rmbRateMap, usdRateMap));
        } catch (Exception e) {
            log.error("查询香港现金余额明细 出现异常:" + Throwables.getStackTraceAsString(e));
        }

        return resultMap;
    }

//    private static StringBuilder buildRateText1(Map<String, RateDTO> rmbRateMap, Map<String, RateDTO> usdRateMap) {
//        // 展示的实际文案效果： *汇率换算：1人民币 ≈ 0.1364美元 (2025-02-12)，1美元 ≈ 7.3688人民币 (2025-02-12)
//        StringBuilder stringBuilder = new StringBuilder();
//        for (Map.Entry<String, RateDTO> rmbRateDTOEntry : rmbRateMap.entrySet()) {
//            String currency = rmbRateDTOEntry.getKey();
//            RateDTO rateDTO = rmbRateDTOEntry.getValue();
//            String description = CurrencyEnum.getDescription(currency);
//            stringBuilder.append("1人民币 ≈ ")
//                    .append(rateDTO.getCurrencyRate().toString())
//                    .append(description)
//                    .append(" (").append(rateDTO.getDataDt()).append(")，");
//        }
//        for (Map.Entry<String, RateDTO> usdRateDTOEntry : usdRateMap.entrySet()) {
//            String currency = usdRateDTOEntry.getKey();
//            RateDTO rateDTO = usdRateDTOEntry.getValue();
//            String description = CurrencyEnum.getDescription(currency);
//            stringBuilder.append("1美元 ≈ ")
//                    .append(rateDTO.getCurrencyRate().toString())
//                    .append(description)
//                    .append(" (").append(rateDTO.getDataDt()).append(")，");
//        }
//        if (stringBuilder.length() > 0) {
//            stringBuilder.insert(0, "*汇率换算：");
//            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
//        }
//        return stringBuilder;
//    }


    /**
     * @description: 香港现金余额明细 汇总数据
     * @param rmbBalance
     * @param usdBalance
     * @return com.howbuy.crm.hb.web.response.hkconscust.HwCashBalanceDTO
     * @author: jin.wang03
     * @date: 2025/2/12 17:03
     * @since JDK 1.8
     */
    private static HwCashBalanceDTO createTotalRow(BigDecimal rmbBalance, BigDecimal usdBalance) {
        HwCashBalanceDTO hwCashBalanceDTO = new HwCashBalanceDTO();
        hwCashBalanceDTO.setCurCodeDesc("总计");
        hwCashBalanceDTO.setCurBalanceStr("-");
        hwCashBalanceDTO.setRmbChangeBalance(rmbBalance);
        hwCashBalanceDTO.setRmbChangeBalanceStr(AmtOrVolFormateUtil.formatByPerciSion(rmbBalance, 2));
        hwCashBalanceDTO.setUsdChangeBalance(usdBalance);
        hwCashBalanceDTO.setUsdChangeBalanceStr(AmtOrVolFormateUtil.formatByPerciSion(usdBalance, 2));
        return hwCashBalanceDTO;
    }


    /**
     * @description: 将数据格式为yyyyMMdd 改为yyyy-MM-dd
     * @param dataDt
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/2/12 17:03
     * @since JDK 1.8
     */
    private String formatDate(String dataDt) {
        if (dataDt == null || dataDt.length() != 8) {
            return "";
        }
        return dataDt.substring(0, 4) + "-" + dataDt.substring(4, 6) + "-" + dataDt.substring(6, 8);
    }
    /**
     * @description: 将数据格式为hhMMss 改为hh:mm:ss
     * @param dataDt
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/2/12 17:03
     * @since JDK 1.8
     */
    private String formatTime(String dataDt) {
        if (dataDt == null || dataDt.length() != 6) {
            return "";
        }
        return dataDt.substring(0, 2) + ":" + dataDt.substring(2, 4) + ":" + dataDt.substring(4, 6);
    }

    /**
     * @description: 展示的实际文案效果： *汇率换算：1人民币 ≈ 0.1364美元 (2025-02-12)，1美元 ≈ 7.3688人民币 (2025-02-12)
     * @param rmbRateMap
     * @param usdRateMap
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/2/12 17:04
     * @since JDK 1.8
     */
    private String buildRateText(Map<String, RateDTO> rmbRateMap, Map<String, RateDTO> usdRateMap) {
        StringBuilder stringBuilder = new StringBuilder();

        appendRateText(stringBuilder, rmbRateMap, "1人民币 ≈ ");
        appendRateText(stringBuilder, usdRateMap, "1美元 ≈ ");

        if (stringBuilder.length() > 0) {
            stringBuilder.insert(0, "*汇率换算：");
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }

        return stringBuilder.toString();
    }

    /**
     * @description:(请在此添加描述)
     * @param stringBuilder
     * @param rateMap
     * @param prefix
     * @return void
     * @author: jin.wang03
     * @date: 2025/2/12 17:50
     * @since JDK 1.8
     */
    private void appendRateText(StringBuilder stringBuilder, Map<String, RateDTO> rateMap, String prefix) {
        for (Map.Entry<String, RateDTO> entry : rateMap.entrySet()) {
            String currency = entry.getKey();
            RateDTO rateDTO = entry.getValue();
            String description = CurrencyEnum.getDescription(currency);
            stringBuilder.append(prefix)
                    .append(rateDTO.getCurrencyRate().toString())
                    .append(description)
                    .append(" (").append(rateDTO.getDataDt()).append(")，");
        }
    }

    /**
     * @api {GET} /hkCustCash/viewCashDepositVoucherRecords viewCashDepositVoucherRecords()
     * @apiVersion 1.0.0
     * @apiGroup CmHkCustCashController
     * @apiName viewCashDepositVoucherRecords()
     * @apiDescription 香港客户现金存入凭证记录页
     * @apiParam (请求参数) {String} consCustNo
     * @apiParamExample 请求参数示例
     * consCustNo=pzJ
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":true,"status":"INSUFFICIENT_STORAGE"}
     */
    @GetMapping("/viewCashDepositVoucherRecords")
    public ModelAndView viewCashDepositVoucherRecords(@RequestParam("consCustNo") String consCustNo) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject("consCustNo", consCustNo);

        // 香港账号
        HkConscustVO hkConscustVO = crmAccountOuterService.queryHkCustInfoByCustNo(consCustNo);
        modelAndView.addObject("hkCustNo", hkConscustVO.getHkTxAcctNo());

        modelAndView.setViewName("/custinfo/hkCashDepositVoucherRecords");
        return modelAndView;
    }

    /**
     * @api {POST} /hkCustCash/payVoucherList payVoucherList()
     * @apiVersion 1.0.0
     * @apiGroup CmHkCustCashController
     * @apiName payVoucherList()
     * @apiDescription 现金存入凭证记录list
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * {}
     */
    @ResponseBody
    @PostMapping("/payVoucherList")
    public Map<String, Object> payVoucherList(HttpServletRequest request) {
        // 获取请求参数
        String hkCustNo = request.getParameter("hkCustNo");
        String voucherAuditStatus = request.getParameter("voucherAuditStatus");
        String appDateStart = request.getParameter("appDateStart");
        String appDateEnd = request.getParameter("appDateEnd");

        // 构建请求对象
        HkPayVoucherListRequest hkPayVoucherListRequest = buildPayVoucherListRequest(hkCustNo, voucherAuditStatus, appDateStart, appDateEnd);

        // 执行查询
        HkPayVoucherListResponse response = cmQueryHkPayVoucherListFacade.execute(hkPayVoucherListRequest);

        // 处理响应结果
        List<HkPayVoucherDTO> resultList = processPayVoucherListResponse(response);

        // 构建返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("rows", resultList);
        return resultMap;
    }

    /**
     * @description: 构建请求对象
     * @param hkCustNo
     * @param voucherAuditStatus
     * @param appDateStart
     * @param appDateEnd
     * @return com.howbuy.crm.payvoucher.request.HkPayVoucherListRequest
     * @author: jin.wang03
     * @date: 2025/2/19 10:51
     * @since JDK 1.8
     */
    private HkPayVoucherListRequest buildPayVoucherListRequest(String hkCustNo, String voucherAuditStatus, String appDateStart, String appDateEnd) {
        HkPayVoucherListRequest request = new HkPayVoucherListRequest();
        request.setHkCustNo(hkCustNo);
        if (StringUtils.isNotBlank(voucherAuditStatus)) {
            request.setAuditStatusList(Lists.newArrayList(voucherAuditStatus));
        }
        if (StringUtils.isNotBlank(appDateStart)) {
            request.setAppDateStart(appDateStart);
        }
        if (StringUtils.isNotBlank(appDateEnd)) {
            request.setAppDateEnd(appDateEnd);
        }
        return request;
    }

    /**
     * @description: 处理查询结果
     * @param response
     * @return java.util.List<com.howbuy.crm.hb.web.response.hkpayvoucher.HkPayVoucherDTO>
     * @author: jin.wang03
     * @date: 2025/2/19 10:51
     * @since JDK 1.8
     */
    private List<HkPayVoucherDTO> processPayVoucherListResponse(HkPayVoucherListResponse response) {
        List<HkPayVoucherDTO> resultList = new ArrayList<>();
        if (Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getPayVoucherList())) {
            for (HkPayVoucherListResponse.WebPayVoucherVO webPayVoucherVO : response.getPayVoucherList()) {
                // 转换
                HkPayVoucherDTO dto = convertToDTO(webPayVoucherVO);
                resultList.add(dto);
            }
            // resultList根据[appDateTime]倒序
            resultList.sort(Comparator.comparing(HkPayVoucherDTO::getAppDateTime, Comparator.nullsLast(Comparator.reverseOrder())));
        }
        return resultList;
    }

    /**
     * @description: 转换
     * @param webPayVoucherVO
     * @return com.howbuy.crm.hb.web.response.hkpayvoucher.HkPayVoucherDTO
     * @author: jin.wang03
     * @date: 2025/2/19 10:51
     * @since JDK 1.8
     */
    private HkPayVoucherDTO convertToDTO(HkPayVoucherListResponse.WebPayVoucherVO webPayVoucherVO) {
        HkPayVoucherDTO dto = new HkPayVoucherDTO();
        BeanUtils.copyProperties(webPayVoucherVO, dto);
        dto.setAppDateTime(formatDateTime(webPayVoucherVO.getAppDate(), webPayVoucherVO.getAppTime()));
        dto.setActualPayDt(formatDate(webPayVoucherVO.getActualPayDt()));
        dto.setRemitAmtStr(AmtOrVolFormateUtil.formatByPerciSion(dto.getRemitAmt(), 2));
        dto.setActualPayAmtStr(AmtOrVolFormateUtil.formatByPerciSion(dto.getActualPayAmt(), 2));
        return dto;
    }

    /**
     * @description: 格式化日期
     * @param date
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/2/19 10:51
     * @since JDK 1.8
     */
    private String formatDateTime(String date, String time) {
        return formatDate(date) + " " + formatTime(time);
    }

}