/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.listener;

import com.google.common.collect.Lists;
import com.howbuy.crm.hb.domain.prosale.preandtradeimport.CmPreAndTradeImportViewDto;
import lombok.Data;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/3/24 20:29
 * @since JDK 1.8
 */
@Data
public  class PreAndTradeExcelReadInfo {

    /**
     * 导入 数据列表
     */
    private List<CmPreAndTradeImportViewDto> dataList= Lists.newArrayList();

    /**
     * 解析后错误提示
     */
    private List<String> errorMsgList= Lists.newArrayList();

} 