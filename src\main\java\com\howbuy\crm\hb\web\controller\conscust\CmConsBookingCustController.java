package com.howbuy.crm.hb.web.controller.conscust;

import com.howbuy.crm.hb.domain.callout.CsCommunicateVisit;
import com.howbuy.crm.hb.domain.callout.CsVisitNewest;
import com.howbuy.crm.hb.domain.custinfo.CmConsBookingCust;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.persistence.common.CommonMapper;
import com.howbuy.crm.hb.service.custinfo.CmConsBookingCustService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import crm.howbuy.base.utils.DateTimeUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: Controller
 * <AUTHOR> @version 1.0
 * @created 
 */
@Controller
@RequestMapping("/conscust")
public class CmConsBookingCustController {
	public final static String BOOKING_STATUS_ZERO = "0";	// 未完成
	public final static String BOOKING_STATUS_ONE = "1";	// 已完成
	public final static String BOOKING_STATUS_TWO = "2";	// 已取消
	
	@Autowired
	private CmConsBookingCustService cmConsBookingCustService;
	
	@Autowired
	private ConscustService conscustService;
	
	@Autowired
	private CommonMapper commonMapper;
	
	/**
	 * 修改预约信息方法
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/toUpdate.do")
	public String toUpdate(HttpServletRequest request, HttpServletResponse response) throws IOException {
		String result;
		try {
			// 获取页面数据
			String consBookingId = request.getParameter("consBookingId");
			String visitType = request.getParameter("visitType");
			String bookingDt = request.getParameter("bookingDt");
			String bookingStartTime = request.getParameter("bookingStartTime");
			String bookingEndTime = request.getParameter("bookingEndTime");
			String content = request.getParameter("content");
			String bookingStatus = request.getParameter("bookingStatus");
			HttpSession session = request.getSession();
			String userId = (String)session.getAttribute("userId");
			
			// 修改预约记录信息
			CmConsBookingCust updateBookingCust = new CmConsBookingCust();
			updateBookingCust.setConsBookingId(consBookingId);
			updateBookingCust.setVisitType(visitType);
			updateBookingCust.setBookingDt(bookingDt);
			updateBookingCust.setBookingStartTime(bookingStartTime);
			updateBookingCust.setBookingEndTime(bookingEndTime);
			updateBookingCust.setContent(content.trim());
			updateBookingCust.setBookingStatus(bookingStatus);
			updateBookingCust.setModDt(DateTimeUtil.getCurDate());
			
			// 修改拜访记录信息
			CsCommunicateVisit csCommunicateVisit = new CsCommunicateVisit();
			csCommunicateVisit.setConsBookingId(consBookingId);
			csCommunicateVisit.setNextDt(bookingDt);
			csCommunicateVisit.setNextVisitType(visitType);
			csCommunicateVisit.setNextStartTime(bookingStartTime);
			csCommunicateVisit.setNextEndTime(bookingEndTime);
			csCommunicateVisit.setNextVisitContent(content.trim());
			csCommunicateVisit.setModifier(userId);
			csCommunicateVisit.setHisFlag("0");
			
			cmConsBookingCustService.updateCmConsBookingCustAll(updateBookingCust, csCommunicateVisit);
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 处理预约信息方法
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/toDeal.do")
	public String toDeal(HttpServletRequest request, HttpServletResponse response) throws IOException {
		String result;
		try {
			// 获取新增拜访数据
			String custNo = request.getParameter("custNo");
			String consBookingId = request.getParameter("consBookingId");
			String visitType = request.getParameter("visitType");	// 新增拜访类型
			String visitClassify = request.getParameter("visitclassify");
			String dealContent = request.getParameter("deal_content");
			
			// 获取新增预约数据
			String bookingType = request.getParameter("bookingType");	// 新增预约类型
			String bookingDt = request.getParameter("bookingDt");
			String bookingStartTime = request.getParameter("bookingStartTime");
			String bookingEndTime = request.getParameter("bookingEndTime");
			String deal_bookingContent = request.getParameter("deal_bookingContent");
			String newConsBookingId = commonMapper.getSeqValue("SEQ_PCUSTREC").toString();// 从序列中获取预约编号
			String appSerialNo = commonMapper.getSeqValue("SEQ_PCUSTREC").toString();// 从序列中获取交易流水号
			String id = commonMapper.getSeqValue("SEQ_CS_COMMUNICATE_VISIT_ID").toString();// 从序列中获取id
			HttpSession session = request.getSession();
			String userId = (String)session.getAttribute("userId");
			
			// 修改原来预约记录信息（根据consBookingId）
			CmConsBookingCust updateBookingCust = new CmConsBookingCust();
			updateBookingCust.setConsBookingId(consBookingId);
			updateBookingCust.setBookingStatus(BOOKING_STATUS_ONE);// 设置状态为已完成
			updateBookingCust.setVisitType(visitType);
			updateBookingCust.setModDt(DateTimeUtil.getCurDate());
			
			// 如果输入了预约记录信息，则新增一条预约信息到预约表
			CmConsBookingCust insertBookingCust = new CmConsBookingCust();
			insertBookingCust.setConsBookingId(newConsBookingId);
			insertBookingCust.setBookingCons(userId);	// 设置预约投顾
			insertBookingCust.setConsCustNo(custNo);	// 设置投顾客户
			insertBookingCust.setVisitType(bookingType);
			insertBookingCust.setBookingDt(bookingDt);
			insertBookingCust.setBookingStartTime(bookingStartTime);
			insertBookingCust.setBookingEndTime(bookingEndTime);
			insertBookingCust.setContent(deal_bookingContent);
			insertBookingCust.setBookingStatus(BOOKING_STATUS_ZERO);
			insertBookingCust.setCreDt(DateTimeUtil.getCurDate());
			insertBookingCust.setCreator(userId);
			
			// 新增一条拜访表记录信息
			CsCommunicateVisit insertCsCommunicateVisit = new CsCommunicateVisit();
			insertCsCommunicateVisit.setId(id);
			insertCsCommunicateVisit.setHisId(appSerialNo);
			insertCsCommunicateVisit.setHisFlag("0");
			insertCsCommunicateVisit.setModifyFlag("0");
			insertCsCommunicateVisit.setConsBookingId(newConsBookingId);
			insertCsCommunicateVisit.setConscustNo(custNo);
			insertCsCommunicateVisit.setVisitType(visitType);
			insertCsCommunicateVisit.setVisitClassify(visitClassify);
			insertCsCommunicateVisit.setCommContent(dealContent);
			insertCsCommunicateVisit.setNextDt(bookingDt);// 预约新增时间设置成下次拜访时间
			insertCsCommunicateVisit.setNextStartTime(bookingStartTime);
			insertCsCommunicateVisit.setNextEndTime(bookingEndTime);
			insertCsCommunicateVisit.setNextVisitContent(deal_bookingContent);
			insertCsCommunicateVisit.setNextVisitType(bookingType);
			insertCsCommunicateVisit.setCreator(userId);
			
			// 新增一条拜访最新记录表信息
			CsVisitNewest insertCsVisitNewest = new CsVisitNewest();
			insertCsVisitNewest.setId(id);
			insertCsVisitNewest.setHisId(appSerialNo);
			insertCsVisitNewest.setHisFlag("0");
			insertCsVisitNewest.setVisitType(visitType);
			insertCsVisitNewest.setVisitClassify(visitClassify);
			insertCsVisitNewest.setModifyFlag("0");
			insertCsVisitNewest.setConsBookingId(consBookingId);
			insertCsVisitNewest.setConscustNo(custNo);
			insertCsVisitNewest.setCommContent(dealContent);
			insertCsVisitNewest.setCreator(userId);
			
			// 如果预约新增中相关字段有填写，则往预约表增加一条记录
			if(StringUtils.isNotBlank(bookingType) && StringUtils.isNotBlank(bookingDt)){
				// 执行修改、新增预约表数据和添加拜访表、拜访最新表数据方法
				cmConsBookingCustService.insertDelUpdateCmConsBookingCustAll(updateBookingCust, insertBookingCust, insertCsCommunicateVisit, custNo, insertCsVisitNewest);
			}else{
				cmConsBookingCustService.insertDelUpdateCmConsBookingCustAll(updateBookingCust, null, insertCsCommunicateVisit, custNo, insertCsVisitNewest);
			}
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 添加预约信息方法
	 * @param request
	 * @param response
	 * @return String
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/toAdd.do")
	public String toAdd(HttpServletRequest request, HttpServletResponse response) throws IOException {
		String result;
		try {
			// 获取拜访预约数据
			String custNo = request.getParameter("custNo");
			String visitType = request.getParameter("visitType");	// 新增拜访类型
			String visitClassify = request.getParameter("visitclassify");
			String add_content = request.getParameter("add_content");
			String bookingType = request.getParameter("bookingType");	// 新增预约类型
			String bookingDt = request.getParameter("bookingDt");
			String bookingStartTime = request.getParameter("bookingStartTime");
			String bookingEndTime = request.getParameter("bookingEndTime");
			String add_bookingContent = request.getParameter("add_bookingContent");
			String newConsBookingId = commonMapper.getSeqValue("SEQ_PCUSTREC").toString();// 从序列中获取预约编号
			String appSerialNo = commonMapper.getSeqValue("SEQ_PCUSTREC").toString();// 从序列中获取交易流水号
			String id = commonMapper.getSeqValue("SEQ_CS_COMMUNICATE_VISIT_ID").toString();// 从序列中获取id
			HttpSession session = request.getSession();
			String userId = (String)session.getAttribute("userId");
			
			// 如果输入了预约记录信息，则新增一条预约信息到预约表
			CmConsBookingCust insertBookingCust = new CmConsBookingCust();
			insertBookingCust.setConsBookingId(newConsBookingId);
			insertBookingCust.setBookingCons(userId);	// 设置预约投顾
			insertBookingCust.setConsCustNo(custNo);	// 设置投顾客户
			insertBookingCust.setVisitType(bookingType);
			insertBookingCust.setBookingDt(bookingDt);
			insertBookingCust.setBookingStartTime(bookingStartTime);
			insertBookingCust.setBookingEndTime(bookingEndTime);
			insertBookingCust.setContent(add_bookingContent);
			insertBookingCust.setBookingStatus(BOOKING_STATUS_ZERO);
			insertBookingCust.setCreDt(DateTimeUtil.getCurDate());
			insertBookingCust.setCreator(userId);
			
			// 新增一条拜访表记录信息
			CsCommunicateVisit insertCsCommunicateVisit = new CsCommunicateVisit();
			insertCsCommunicateVisit.setId(id);
			insertCsCommunicateVisit.setHisId(appSerialNo);
			insertCsCommunicateVisit.setHisFlag("0");
			insertCsCommunicateVisit.setModifyFlag("0");
			insertCsCommunicateVisit.setConsBookingId(newConsBookingId);
			insertCsCommunicateVisit.setConscustNo(custNo);
			insertCsCommunicateVisit.setVisitType(visitType);
			insertCsCommunicateVisit.setVisitClassify(visitClassify);
			insertCsCommunicateVisit.setCommContent(add_content);
			insertCsCommunicateVisit.setNextDt(bookingDt);// 预约新增时间设置成下次拜访时间
			insertCsCommunicateVisit.setNextStartTime(bookingStartTime);
			insertCsCommunicateVisit.setNextEndTime(bookingEndTime);
			insertCsCommunicateVisit.setNextVisitContent(add_bookingContent);
			insertCsCommunicateVisit.setNextVisitType(bookingType);
			insertCsCommunicateVisit.setCreator(userId);
			
			// 新增一条拜访最新记录表信息
			CsVisitNewest insertCsVisitNewest = new CsVisitNewest();
			insertCsVisitNewest.setId(id);
			insertCsVisitNewest.setHisId(appSerialNo);
			insertCsVisitNewest.setHisFlag("0");
			insertCsVisitNewest.setModifyFlag("0");
			insertCsVisitNewest.setVisitType(visitType);
			insertCsVisitNewest.setVisitClassify(visitClassify);
			insertCsVisitNewest.setCommContent(add_content);
			insertCsVisitNewest.setConsBookingId(newConsBookingId);
			insertCsVisitNewest.setConscustNo(custNo);
			insertCsVisitNewest.setCreator(userId);
			
			// 如果预约新增中相关字段有填写，则往预约表增加一条记录（此操作中不涉及修改，所以在调用第一个通用方法是给赋null值）
			if(StringUtils.isNotBlank(bookingType) && StringUtils.isNotBlank(bookingDt)){
				// 执行新增预约表数据和添加拜访表、拜访最新表数据方法
				cmConsBookingCustService.insertDelUpdateCmConsBookingCustAll(null, insertBookingCust, insertCsCommunicateVisit, custNo, insertCsVisitNewest);
			}else{
				cmConsBookingCustService.insertDelUpdateCmConsBookingCustAll(null, null, insertCsCommunicateVisit, custNo, insertCsVisitNewest);
			}
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}
	
	/**
	 * 自动补全投顾客户方法
	 */
	@ResponseBody
	@RequestMapping("/autoCompleteConsCust.do")
	public Map<String, List<Conscust>> autoCompleteConsCust(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		String searchParam = request.getParameter("term");
		String flag=request.getParameter("flag");
		param.put("searchParam", searchParam);
		
		// 如果传入客户投顾不为空，则增加投顾条件限制
		if(StringUtils.isNotBlank(flag)){
			String consCode = request.getParameter("consCode");
			param.put("consCode", consCode);
		} else {
			// 否则使用默认客户所属投顾条件限制
			HttpSession session = request.getSession();
			String userId = (String)session.getAttribute("userId");
			param.put("consCode", userId);
		}
		
		List<Conscust> listConscust = conscustService.listCustInfoByCustTel(param);
		Map<String,List<Conscust>> result = new HashMap<String,List<Conscust>>();
		result.put("result", listConscust);
		return result;
	}
	
}