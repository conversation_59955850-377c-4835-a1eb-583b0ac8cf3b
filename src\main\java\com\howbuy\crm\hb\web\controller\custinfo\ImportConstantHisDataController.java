package com.howbuy.crm.hb.web.controller.custinfo;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.howbuy.crm.hb.service.custinfo.impl.ImportConstantHisService;
import crm.howbuy.base.utils.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 分配记录刷数据
 * @date 2023/11/7 14:20
 * @since JDK 1.8
 */
@Slf4j
@RequestMapping("/constanthis")
@Controller
public class ImportConstantHisDataController {

    @Autowired
    private ImportConstantHisService importConstantHisService;

    private static final Integer PAGE_SIZE = 500;
    private static final String IMPORT_HIS_DATA = "0";
    private static final String UPDATE_CONS_NULL = "1";
    private static final String IMPORT_RS_DATA = "2";
    private static final String IMPORT_20W_DATA = "3";
    private static final String MATCH_UPDATE_DATA = "4";
    private static final String BAK_DATA = "5";


    private static final String SUCCESS = "success";

    /**
     * @api {POST} /constanthis/refreshdata refreshData
     * @apiVersion 1.0.0
     * @apiGroup ImportConstantHisDataController
     * @apiName importData
     * @apiDescription 导入要刷的数据
     * @apiParam (请求参数) {Object} file 导入文件
     * @apiParamExample 请求参数示例
     * file=null
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * {}
     */
    @PostMapping("/refreshdata")
    @ResponseBody
    public String refreshData(ImportHisDataRequest request) {

        if(IMPORT_HIS_DATA.equals(request.getType())){
            return importHisData(request);
        }else if(UPDATE_CONS_NULL.equals(request.getType())){
            updateConsNullData();
        }else if(IMPORT_RS_DATA.equals(request.getType())){
            return importRsData(request);
        }else if(IMPORT_20W_DATA.equals(request.getType())){
            return import20wData(request);
        }else if(MATCH_UPDATE_DATA.equals(request.getType())){
            return matchAndUpdate(request);
        }else if(BAK_DATA.equals(request.getType())){
            bakData();
        }
        return SUCCESS;
    }
    /**
     * @description 备份数据
     * @param
     * @return void
     * @author: jianjian.yang
     * @date: 2023/12/4 14:33
     * @since JDK 1.8
     */
    public void bakData(){
        importConstantHisService.bakData();
    }

    /**
     * @description 刷分配记录
     * @param request
     * @return void
     * @author: jianjian.yang
     * @date: 2023/11/9 11:12
     * @since JDK 1.8
     */
    private String importHisData(ImportHisDataRequest request){

        MultipartFile multipartFile = request.getFile();
        try {
            List<ConstantHisExcelImport> constantHisExcelImportList = EasyExcelFactory.read(multipartFile.getInputStream()).head(ConstantHisExcelImport.class).sheet().headRowNumber(1).doReadSync();
            if(CollectionUtils.isNotEmpty(constantHisExcelImportList)){
                List<String> custNos = constantHisExcelImportList.stream().map(ConstantHisExcelImport::getConscustNo).collect(Collectors.toList());
                List<String> custNameErrorList = Lists.newArrayList();
                int i = 0;
                while (PAGE_SIZE * i < custNos.size()){
                    int start = i * PAGE_SIZE;
                    int end = custNos.size() > (i + 1) * PAGE_SIZE ?  (i + 1) * PAGE_SIZE : custNos.size();
                    i++;
                    List<ImportConstantHisService.CustInfo> custInfoList = importConstantHisService.listCustInfo(custNos.subList(start, end));
                    if(custInfoList.size() > 0) {
                        //先插入一批数据
                        importConstantHisService.batchInsertCustHis(custInfoList);
                    }
                }

                return String.format("姓名不匹配的：%s", Joiner.on(",").join(custNameErrorList));
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return SUCCESS;
    }

    public void updateConsNullData(){
        importConstantHisService.updateConsNullData();
    }

    /**
     * @description 匹配更新
     * @param request
     * @return void
     * @author: jianjian.yang
     * @date: 2023/11/9 19:59
     * @since JDK 1.8
     */
    private String matchAndUpdate(ImportHisDataRequest request){

        MultipartFile multipartFile = request.getFile();

        List<ConstantHisExcelImport> constantHisExcelImportList = null;
        try {
            constantHisExcelImportList = EasyExcelFactory.read(multipartFile.getInputStream()).head(ConstantHisExcelImport.class).sheet().headRowNumber(1).doReadSync();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        if(CollectionUtils.isNotEmpty(constantHisExcelImportList)){
            List<String> unMatchList = new ArrayList<>();
            List<String> repeatList = new ArrayList<>();
            int i = 0;
            while (PAGE_SIZE * i < constantHisExcelImportList.size()) {
                int start = i * PAGE_SIZE;
                int end = constantHisExcelImportList.size() > (i + 1) * PAGE_SIZE ? (i + 1) * PAGE_SIZE : constantHisExcelImportList.size();
                i++;
                List<ConstantHisExcelImport> importList = constantHisExcelImportList.subList(start, end);
                List<ImportConstantHisService.ConsMatchInfo> consMatchInfos = new ArrayList<>();
                importList.forEach(constantHisExcelImport -> {
                    ImportConstantHisService.ConsMatchInfo consMatchInfo = new ImportConstantHisService.ConsMatchInfo();
                    consMatchInfo.setCustNo(constantHisExcelImport.getConscustNo());
                    consMatchInfo.setAssignMonth(replaceDate(constantHisExcelImport.getAssignTime()));
                    consMatchInfo.setConsName(constantHisExcelImport.getConsName());
                    consMatchInfos.add(consMatchInfo);
                });
                List<ImportConstantHisService.ConsMatchBO> consMatchBOList = importConstantHisService.matchAssignRecord(consMatchInfos);
                List<ImportConstantHisService.ConsMatchBO> updateConsMatchBOList = new ArrayList<>(consMatchBOList.size());
                for (ImportConstantHisService.ConsMatchBO consMatchBO : consMatchBOList) {
                    if(consMatchBO.getNum() == 0){
                        unMatchList.add(consMatchBO.getCustNo());
                    }else if (consMatchBO.getNum() > 1) {
                        repeatList.add(consMatchBO.getCustNo());
                    } else {
                        updateConsMatchBOList.add(consMatchBO);
                    }
                }
                if(updateConsMatchBOList.size() > 0) {
                    importConstantHisService.updateHisLeads(updateConsMatchBOList);
                }
            }
            log.info("repeatList：{}, unMatchList : {}", repeatList == null ? null :repeatList, unMatchList == null ? null :unMatchList);
            return String.format("投顾重复的：%s %n无匹配结果的: %s", CollectionUtils.isNotEmpty(repeatList) ? Joiner.on(",").join(repeatList) : null, CollectionUtils.isNotEmpty(unMatchList) ? Joiner.on(",").join(unMatchList) : null);
        }
        return SUCCESS;
    }
    /**
     * @description 刷Rs库leads分配记录
     * @param request
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2023/11/9 11:12
     * @since JDK 1.8
     */
    public String importRsData(ImportHisDataRequest request) {
        MultipartFile multipartFile = request.getFile();
        try {
            List<RsHisExcelImport> constantHisExcelImportList = EasyExcelFactory.read(multipartFile.getInputStream()).head(RsHisExcelImport.class).sheet().headRowNumber(1).doReadSync();
            if(CollectionUtils.isNotEmpty(constantHisExcelImportList)){
                List<String> custNos = constantHisExcelImportList.stream().map(RsHisExcelImport::getConscustNo).collect(Collectors.toList());
                int i = 0;
                while (PAGE_SIZE * i < custNos.size()){
                    int start = i * PAGE_SIZE;
                    int end = custNos.size() > (i + 1) * PAGE_SIZE ? (i + 1) * PAGE_SIZE : custNos.size();
                    i++;
                    importConstantHisService.updateRsLeads(custNos.subList(start, end));
                }

            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return SUCCESS;
    }

    /**
     * @description 导入20w
     * @param importHisDataRequest
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2023/11/9 19:55
     * @since JDK 1.8
     */
    public String import20wData(ImportHisDataRequest importHisDataRequest){
        MultipartFile multipartFile = importHisDataRequest.getFile();
        try {
            List<ConstantHisExcelImport> constantHisExcelImportList = EasyExcelFactory.read(multipartFile.getInputStream()).head(ConstantHisExcelImport.class).sheet().headRowNumber(1).doReadSync();
            if(CollectionUtils.isNotEmpty(constantHisExcelImportList)){
                int i = 0;
                List<String> unMatchList = new ArrayList<>();
                List<String> repeatList = new ArrayList<>();
                while (PAGE_SIZE * i < constantHisExcelImportList.size()){
                    int start = i * PAGE_SIZE;
                    int end = constantHisExcelImportList.size() > (i + 1) * PAGE_SIZE ?  (i + 1) * PAGE_SIZE : constantHisExcelImportList.size();
                    i++;
                    List<ConstantHisExcelImport> importList = constantHisExcelImportList.subList(start, end);
                    List<ImportConstantHisService.ConsMatchInfo> consMatchInfos = new ArrayList<>();
                    importList.forEach(constantHisExcelImport -> {
                        ImportConstantHisService.ConsMatchInfo consMatchInfo = new ImportConstantHisService.ConsMatchInfo();
                        consMatchInfo.setCustNo(constantHisExcelImport.getConscustNo());
                        consMatchInfo.setAssignMonth(constantHisExcelImport.getAssignTime().replace("-", "").substring(0, 8));
                        consMatchInfo.setConsName(constantHisExcelImport.getConsName());
                        consMatchInfos.add(consMatchInfo);
                    });
                    List<ImportConstantHisService.ConsMatchBO> consMatchBOList = importConstantHisService.matchAssign20wRecord(consMatchInfos);
                    List<ImportConstantHisService.ConsMatchBO> updateConsMatchBOList = new ArrayList<>(consMatchBOList.size());
                    for(ImportConstantHisService.ConsMatchBO consMatchBO : consMatchBOList){
                        if(consMatchBO.getNum() == 0){
                            unMatchList.add(consMatchBO.getCustNo());
                        }else if(consMatchBO.getNum() > 1){
                            repeatList.add(consMatchBO.getCustNo());
                        }else {
                            updateConsMatchBOList.add(consMatchBO);
                        }
                    }
                    if(updateConsMatchBOList.size() > 0) {
                        importConstantHisService.updateHis20w(updateConsMatchBOList);
                    }
                }

                return String.format("匹配不上的:%s %n投顾重复的：%s",  CollectionUtils.isNotEmpty(repeatList) ? Joiner.on(",").join(unMatchList) : null,
                        CollectionUtils.isNotEmpty(unMatchList) ? Joiner.on(",").join(unMatchList) : null);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return SUCCESS;
    }

    /**
     * @description 替换日期
     * @param dateStr
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2023/12/4 21:12
     * @since JDK 1.8
     */
    private static String replaceDate(String dateStr){
        dateStr = dateStr.replace("年", "").replace("月", "");
        if(dateStr.length() == 5){
            dateStr = dateStr.substring(0, 4) + "0" + dateStr.substring(4);
        }
        return dateStr;
    }

    @Data
    static class ImportHisDataRequest{

        /**
         * 导入文件
         */
        private MultipartFile file;
        /**
         * 类型
         */
        private String type;
    }

    /**
     * @description 高端历史分配记录
     * @author: jianjian.yang
     * @date: 2023/11/7 19:21
     * @since JDK 1.8
     */
    @Data
    public static class ConstantHisExcelImport{

        @ExcelProperty(value = "分配时间", index = 0)
        private String assignTime;

        @ExcelProperty(value = "投顾客户号", index = 1)
        private String conscustNo;

        @ExcelProperty(value = "分配投顾", index = 2)
        private String consName;
    }

    /**
     * @description 历史RS库分配记录
     * @author: jianjian.yang
     * @date: 2023/11/7 19:21
     * @since JDK 1.8
     */
    @Data
    public static class RsHisExcelImport{

        @ExcelProperty(value = "投顾客户号", index = 0)
        private String conscustNo;
    }

}
