package com.howbuy.crm.hb.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Description: 异常处理
 * @reason: 对于未处理的异常打印日志，同时也可以返回一个特定的页面
 * @Date: 2020/6/3 10:43
 */
@Slf4j
public class CrmExceptionHandle implements HandlerExceptionResolver{
    @Override
    public ModelAndView resolveException(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) {

        log.error(e.getMessage(), e);
        //暂时不返回特定页面
        return null;
    }
}
