package com.howbuy.crm.hb.web.controller.custinfo;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.howbuy.crm.hb.domain.conscust.CmTransfLogEntity;
import com.howbuy.crm.hb.domain.conscust.CmTransfLogVo;
import com.howbuy.crm.hb.domain.custinfo.*;
import com.howbuy.crm.hb.domain.system.HbUserrole;
import com.howbuy.crm.hb.persistence.system.HbUserroleMapper;
import com.howbuy.crm.hb.service.custinfo.CmCustconstantService;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.howbuy.crm.hb.service.custinfo.CmTransfLogService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;

/**
 * @Description: Controller
 * @version 1.0
 */
@Slf4j
@Controller
@RequestMapping(value = "/cmTransfLog")
public class CmTransfLogController {
	@Autowired
	private CmTransfLogService cmTransfLogService;

	@Autowired
	private CmCustconstantService cmCustconstantService;
	@Autowired
	private HbUserroleMapper hbUserroleMapper;

	@RequestMapping("/listCmTransfLog.do")
	public ModelAndView listCmTransfLog(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		User user = (User) request.getSession().getAttribute("loginUser");
		request.setAttribute("userid", user.getUserId());
        modelAndView.setViewName("/custinfo/listCmTransfLog");
        return modelAndView;
	}
	
	/**
	 * 加载列表页面数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listCmTransfLogByPage.do")
	public Map<String, Object> listCmTransfLogByPage(HttpServletRequest request)	throws Exception {
		Map<String, String> param = new ParamUtil(request).getParamMap();
		String oldConsCode = request.getParameter("oldConsCode");
        String oldOrgCode = request.getParameter("oldOrgCode");
        if(StringUtils.isNotEmpty(oldConsCode) && !"null".equals(oldConsCode) && !"ALL".equals(oldConsCode)) {
			param.put("oldConsCode", oldConsCode);
		}else {
			param.put("oldTeamCode", oldOrgCode);
		}

		String appdtEnd = request.getParameter("appdtEnd");
		if(StringUtils.isNotBlank(appdtEnd)){
			appdtEnd = DateTimeUtil.getNextDayByStr(appdtEnd,"yyyyMMdd");
			param.put("appdtEnd", appdtEnd);
		}
		param.put("isdel", "1");

		String consCode = request.getParameter("consCode");
		String orgCode = request.getParameter("orgCode");

		if(StringUtils.isNotEmpty(consCode) && !"null".equals(consCode) && !"ALL".equals(consCode)) {
			param.put("consCode", consCode);
		}else {
			param.put("teamCode", orgCode);
		}

		PageData<CmTransfLog> pageData = cmTransfLogService.listCmTransfLogByPage(param);

		// 返回查询结果
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmTransfLog> listdata = pageData.getListData();
		if(CollectionUtils.isNotEmpty(listdata)){
			for(CmTransfLog cmTransfLog : listdata){
				//设置常量
				setCmTransfLogConstant(cmTransfLog);
			}
		}
		resultMap.put("rows", listdata);
		
		return resultMap;
	}
	
	
	/**
	 * 展示修改页面
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/viewCmTransfLogel", method = RequestMethod.POST)
	@ResponseBody
    public  Map<String, Object> viewCmTransfLog(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("errorMsg", "操作成功");
		resultMap.put("errorCode", "0000");
		String id = request.getParameter("id");
		if(StringUtils.isNotBlank(id)){
			Map<String,String> param = new HashMap<String,String> ();
			param.put("id", id);
			CmTransfLog cmTransfLog = cmTransfLogService.getCmTransfLog(param);
			if(cmTransfLog != null){
				if(StringUtils.isNotBlank(cmTransfLog.getAppconscode())){
					cmTransfLog.setApporgname(ConsOrgCache.getInstance().getAllOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(cmTransfLog.getAppconscode())));
					cmTransfLog.setAppconsname(ConsOrgCache.getInstance().getAllConsMap().get(cmTransfLog.getAppconscode()));
				}
				if("1".equals(cmTransfLog.getCuststatus())){
					cmTransfLog.setCuststatus("成交");
				}else{
					cmTransfLog.setCuststatus("潜在");
				}
			}
			resultMap.put("domain", cmTransfLog);
		}else{
			resultMap.put("errorMsg", "操作失败：id不能为空");
			resultMap.put("errorCode", "9999");
		}
      
        return resultMap;
    }

	//判断客户投顾是否为申请投顾
	@ResponseBody
	@RequestMapping("/checkconscode")
	public String checkconscode(HttpServletRequest request) throws Exception{
		String result = "";
		String conscustno = request.getParameter("editconscustno");
		String oldconscode = request.getParameter("oldconscode");
		if(StringUtil.isNotNullStr(conscustno)){

			Map<String, String> paramconstant = new HashMap();
			paramconstant.put("custno", conscustno);
			CmCustconstant constant = cmCustconstantService.getCmCustconstant(paramconstant);

			if(constant != null){
				if(oldconscode.equals(constant.getConscode())){
					result = "same";
				}else{
					result = "diffrent";
				}
			}
		}else{
			result = "paramError";
		}
		return result;
	}

	// 批量判断当前投顾是否等于原投顾
	@ResponseBody
	@RequestMapping("/batchCheckconscode")
	public Map<String, Object> batchCheckconscode(HttpServletRequest request) {
		String transLogIds = request.getParameter("transLogIds");
		Map<String, Object> resultMap = new HashMap<>();
		try {
			resultMap = cmTransfLogService.batchCheckconscode(transLogIds);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return resultMap;
	}

	/**
	 * 更新
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/updateCmTransfLog", method = RequestMethod.POST)
	@ResponseBody
    public Map<String, Object> updateCmTransfLog(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String id = request.getParameter("id");
		String operationid = request.getParameter("operationid");
		String optype = request.getParameter("optype");
		String opstatus = request.getParameter("opstatus");
		String transfcause = request.getParameter("transfcause");
		String memo = request.getParameter("memo");
		
        resultMap.put("errorMsg", "操作成功");
        resultMap.put("errorCode", "0000");
        User user = (User) request.getSession().getAttribute("loginUser");

        if (!StringUtils.isEmpty(id)) {
        	CmTransfLog cmTransfLog = new CmTransfLog();
        	cmTransfLog.setId(new BigDecimal(id));
        	cmTransfLog.setOpstatus(opstatus);
        	cmTransfLog.setOptype(optype);
        	cmTransfLog.setTransfcause(transfcause);
        	cmTransfLog.setMemo(memo);
        	cmTransfLog.setModifier(user.getUserId());
        	cmTransfLog.setUpdatetime(new Date());
        	cmTransfLogService.updatePartMsg(cmTransfLog,operationid);
        }else{
        	resultMap.put("errorMsg", "参数错误：id不能为空");
            resultMap.put("errorCode", "9999");
        }
        return resultMap;
    }

	//导出操作
	@RequestMapping("/exportTransf.do")
	public void exportTransf(HttpServletRequest request, HttpServletResponse response) throws Exception {
		// 设置查询参数
		String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
		if (StringUtils.isNotBlank(ids)) {
			String[] custs = ids.split(",");
			//传过来的客户号
			List<String> list = new ArrayList<String>();
			//将数组转list
			CollectionUtils.addAll(list, custs);
			// 检查打标的客户是否有在申请划转的客户中
			Map<String,String> paramsql = new HashMap<String,String>();
			String sqlins = com.howbuy.crm.hb.web.util.Util.getOracleSQLIn(list,999,"t.id");
			paramsql.put("sqlins", sqlins);

			List<CmTransfLog> exportList = cmTransfLogService.exportCmTransfLog(paramsql);

			if(CollectionUtils.isNotEmpty(exportList)){
				for(CmTransfLog cmTransfLog : exportList){
					//设置常量
					setCmTransfLogConstant(cmTransfLog);
				}
			}
			try {
				// 清空输出流
				response.reset();
				// 设置文件格式和名字
				response.setContentType("multipart/form-data");
				response.setHeader("Content-Disposition",
						"attachment;fileName=" + new String("重复客户划转.xls".getBytes("gb2312"), "ISO8859-1"));
				ServletOutputStream os = response.getOutputStream();


				String[] columnName = {"申请日期","申请中心" , "申请区域" ,"申请部门", "申请投顾", "客户姓名", "投顾客户号", "原投顾","原投顾是否分总", "原中心","原区域", "原部门", "潜在/成交客户", "申请原因",
						"不符合条件", "处理状态", "处理方式", "备注", "处理后客户成交状态"};

				String[] beanProperty = {"appdt", "appupcentername","appuporgname", "apporgname", "appconsname", "custname","conscustno","oldconsname","oldconsCodeIsFz","oldupcentername","olduporgname",
						"oldorgname","custstatus","transfcause","miscondition","opstatus","optype","memo","transstatus"};
				ExcelWriter.writeExcel(os, "客户", 0, exportList, columnName, beanProperty);
				os.close(); // 关闭流
			} catch (Exception e) {
				log.error("文件导出异常", e);
			}
		}
	}
	
	/**
     * 导出全部操作
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportAllTransf.do")
    public void exportAllTransf(HttpServletRequest request, HttpServletResponse response) throws Exception {
    	Map<String, String> param = new ParamUtil(request).getParamMap();
		String oldConsCode = request.getParameter("oldConsCode");
        String oldOrgCode = request.getParameter("oldOrgCode");
        if(StringUtils.isNotEmpty(oldConsCode) && !"null".equals(oldConsCode) && !"ALL".equals(oldConsCode)) {
			param.put("oldConsCode", oldConsCode);
		}else {
			param.put("oldTeamCode", oldOrgCode);
		}

		String appdtEnd = request.getParameter("appdtEnd");
		if(StringUtils.isNotBlank(appdtEnd)){
			appdtEnd = DateTimeUtil.getNextDayByStr(appdtEnd,"yyyyMMdd");
			param.put("appdtEnd", appdtEnd);
		}
		param.put("isdel", "1");

		String consCode = request.getParameter("consCode");
		String orgCode = request.getParameter("orgCode");

		if(StringUtils.isNotEmpty(consCode) && !"null".equals(consCode) && !"ALL".equals(consCode)) {
			param.put("consCode", consCode);
		}else {
			param.put("teamCode", orgCode);
		}

		PageData<CmTransfLog> pageData = cmTransfLogService.listCmTransfLogAllByPage(param);

		// 返回查询结果
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmTransfLog> listdata = pageData.getListData();
		if(CollectionUtils.isNotEmpty(listdata)){
			if(listdata.size() > 60000){
				response.setContentType("text/html;charset=utf-8");
	        	response.getWriter().write("导出数据量超过6万，请重新选择!");
			}else{
				for(CmTransfLog cmTransfLog : listdata){
					//设置常量
					setCmTransfLogConstant(cmTransfLog);
				}	
	        	com.alibaba.excel.ExcelWriter excelWriter = null;
	        	List<CmTransfLogVo> exportlistdata = new ArrayList<>();
	        	for(CmTransfLog log : listdata){
	        		CmTransfLogVo vo = new CmTransfLogVo();
	        		BeanUtils.copyProperties(log, vo);
	        		exportlistdata.add(vo);
	        	}
	        	try {
	        		// 设置导出内容
	                response.setContentType("application/vnd.ms-excel");
	                response.setCharacterEncoding("utf-8");
	
	                // 设置文件名称
	                String fileName = URLEncoder.encode("重复客户划转记录导出", "UTF-8");
	                response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
	                excelWriter = EasyExcel.write(response.getOutputStream(), CmTransfLogEntity.class).build();
	                WriteSheet writeSheet = EasyExcel.writerSheet("重复客户划转").build();
	                excelWriter.write(exportlistdata, writeSheet);
	        	} catch (Exception e) {
	                log.error("文件导出异常" + e.getMessage());
	            } finally {
	                // 关闭流操作
	                if (excelWriter != null) {
	                    excelWriter.finish();
	                }
	            }
			}
		
	        
        }
    
        
    }
    
    /**
     * 设置常量
     * @param cmTransfLog
     */
    private void setCmTransfLogConstant(CmTransfLog cmTransfLog){
    	ConsOrgCache orgcache = ConsOrgCache.getInstance();
		cmTransfLog.setOpstatus(StringUtils.isEmpty(cmTransfLog.getOpstatus()) ? "" : ConstantCache.getInstance().getVal("transfopstatus", cmTransfLog.getOpstatus()));
		cmTransfLog.setOptype(StringUtils.isEmpty(cmTransfLog.getOptype()) ? "" :  ConstantCache.getInstance().getVal("transfoptype", cmTransfLog.getOptype()));
		if(StringUtils.isNotBlank(cmTransfLog.getAppconscode())){
			cmTransfLog.setApporgname(orgcache.getAllOrgMap().get(orgcache.getCons2OutletMap().get(cmTransfLog.getAppconscode())));
			cmTransfLog.setAppconsname(orgcache.getAllConsMap().get(cmTransfLog.getAppconscode()));
			//appuporgname申请区域
			String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(cmTransfLog.getAppconscode()));
			if("0".equals(uporgcode)){
				cmTransfLog.setAppuporgname(cmTransfLog.getApporgname());
			}else{
				cmTransfLog.setAppuporgname(orgcache.getAllOrgMap().get(uporgcode));
			}
			// 设置申请中心
			String upCenterCode = orgcache.getAllOrgMap().get(orgcache.getUpCenterMapCache().get(orgcache.getCons2OutletMap().get(cmTransfLog.getAppconscode())));
			cmTransfLog.setAppupcentername(upCenterCode);
		}
		if(StringUtils.isNotBlank(cmTransfLog.getOldconscode())){
			cmTransfLog.setOldorgname(orgcache.getAllOrgMap().get(orgcache.getCons2OutletMap().get(cmTransfLog.getOldconscode())));
			cmTransfLog.setOldconsname(orgcache.getAllConsMap().get(cmTransfLog.getOldconscode()));
			//olduporgname 原区域
			String uporgcode = orgcache.getUpOrgMapCache().get(orgcache.getCons2OutletMap().get(cmTransfLog.getOldconscode()));
			if("0".equals(uporgcode)){
				cmTransfLog.setOlduporgname(cmTransfLog.getOldorgname());
			}else{
				cmTransfLog.setOlduporgname(orgcache.getAllOrgMap().get(uporgcode));
			}
			//列表添加字段“原投顾是否分总”，添加在“原投顾”后面，判断原投顾的角色是否包含“销售-区域总/分总”，包含则取“是”，否则取“否”
			HashMap<String, String> queryRoleMap = new HashMap<>();
			queryRoleMap.put("rolecode","ROLE_SIC_HEAD");
			queryRoleMap.put("usercode",cmTransfLog.getOldconscode());
			List<HbUserrole> hbUserroles = hbUserroleMapper.listHbUserrole(queryRoleMap);
			if(CollectionUtils.isNotEmpty(hbUserroles)){
				cmTransfLog.setOldconsCodeIsFz("是");
			}else{
				cmTransfLog.setOldconsCodeIsFz("否");
			}
			// 设置原中心
			String oldupCenterCode = orgcache.getAllOrgMap().get(orgcache.getUpCenterMapCache().get(orgcache.getCons2OutletMap().get(cmTransfLog.getOldconscode())));
			cmTransfLog.setOldupcentername(oldupCenterCode);
		}
		if("1".equals(cmTransfLog.getCuststatus())){
			cmTransfLog.setCuststatus("成交");
		}else{
			cmTransfLog.setCuststatus("潜在");
		}
    }
}
