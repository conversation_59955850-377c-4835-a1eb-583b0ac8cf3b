package com.howbuy.crm.hb.web.controller.doubletrade;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SendCallClient {
	
	@Value("${CALLCENTER_SERVER_IP}")
	private String callcenter_server_ip;
	
	@Value("${CALLCENTER_SERVER_PORT}")
	private String callcenter_server_port;

	/**
	 * 发送套接字
	 *
	 * @param workPlace
	 * @param call
	 * @throws Exception
	 */
	public void send(String workPlace, String call) throws Exception {
		DatagramSocket sendSocket = new DatagramSocket();
		log.info("========workPlace：" + workPlace);

		String serverIP = callcenter_server_ip;
		String serverPort = callcenter_server_port;
		InetAddress address = InetAddress.getByName(serverIP);
		log.info("========server ip：" + serverIP);
		log.info("========call data：" + call);
		DatagramPacket sendPacket = new DatagramPacket(call.getBytes(), call.getBytes().length, address, Integer.parseInt(serverPort));
		if (sendSocket != null && !sendSocket.isClosed()) {
			sendSocket.send(sendPacket);
		}
		sendSocket.close();

	}

	/**
	 * 软电话测试发送端
	 *
	 * @param args
	 * @throws Exception
	 */
	public static void main(String[] args) throws Exception {
		DatagramSocket sendSocket = new DatagramSocket();

		//软电话服务器IP
		InetAddress address = InetAddress.getByName("**************");

		//拨号信息（分机号;9或者90+手机号;唯一KEY）
		String msg = "6277;915021286907;62771399444581858";
		DatagramPacket sendPacket = new DatagramPacket(msg.getBytes(), msg.getBytes().length, address, Integer.parseInt("5494"));
		if (sendSocket != null && !sendSocket.isClosed()) {
			sendSocket.send(sendPacket);
		}

		sendSocket.close();
	}

}
