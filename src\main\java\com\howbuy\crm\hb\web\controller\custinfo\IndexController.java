package com.howbuy.crm.hb.web.controller.custinfo;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.ui.ModelMap;

import com.howbuy.crm.hb.domain.callout.CsCommunicateVisit;
import com.howbuy.crm.hb.domain.callout.CsVisitNewest;
import com.howbuy.crm.hb.domain.custinfo.CmConsBookingCust;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.custinfo.ProductShareBonus;
import com.howbuy.crm.hb.manager.BookingCustExportManager;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.CmConsBookingCustService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.custinfo.ProductShareBonusService;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.utils.ParamUtil;

import crm.howbuy.base.constants.ContextManager;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;

/**
 * @Description: Controller
 * <AUTHOR> @version 1.0
 * @created
 */
@Controller
@RequestMapping("/productDividend")
public class IndexController {
	@Autowired
	private ProductShareBonusService productShareBonusService;
	
	@Autowired
	private CmConsBookingCustService cmConsBookingCustService;
	@Autowired
    private CommonService commonService;
	@Autowired
	private ConscustService conscustService;
	
	public final static String BOOKING_STATUS_ZERO = "0";	// 未完成
	public final static String BOOKING_STATUS_ONE = "1";	// 已完成
	public final static String BOOKING_STATUS_TWO = "2";	// 已取消
	
	/**
	 * 跳转产品分红提醒（更多）页面
	 */
	@RequestMapping("/showProductShareBonusMore")
	public String showProductShareBonusMore(HttpServletRequest request,HttpServletResponse response,ModelMap model) throws UnsupportedEncodingException {
		Map<String,String> startAndEndMap=DateTimeUtil.getBonusMonthStartAndEnd();
		request.setAttribute("beginDate",startAndEndMap.get("beginDate"));
		request.setAttribute("endDate",startAndEndMap.get("endDate"));
		return "custinfo/listProductShareBonusMore";
	}
	
	/**
	 * 加载产品分红（更多）数据方法
	 * 
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/loadProductShareBonusMore_json.do")
	public Map<String,Object> loadProductShareBonusMore_json(HttpServletRequest request,HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String,String> param=new HashMap<String,String>();
		// 获取分页参数
		param=new ParamUtil(request).getParamMap();

		// 获取登陆用户信息
		HttpSession session=request.getSession();
		String userId=(String)session.getAttribute("userId");
		//param.put("consCode",userId);
		String curPage=request.getParameter("page");
		String jjdm=request.getParameter("jjdm");
		String jjjc=request.getParameter("jjjc");
		String beginDate=request.getParameter("beginDate");
		String endDate=request.getParameter("endDate");
		String searchFlag=request.getParameter("searchFlag");

		// 如果查询条件（基金代码）不为空，则增加基金代码查询参数
		if(StringUtil.isNotNullStr(jjdm)) {
			param.put("jjdm",jjdm);
		}
		else {
			param.put("jjdm",null);
		}

		// 如果查询条件（基金简称）不为空，则增加基金简称查询参数
		if(StringUtil.isNotNullStr(jjjc)) {
			param.put("jjjc",jjjc);
		}
		else {
			param.put("jjjc",null);
		}

		// 设置日期时间范围查询参数
		if(StringUtil.isNullStr(searchFlag)) {
			Map<String,String> startAndEndMap=DateTimeUtil.getBonusMonthStartAndEnd();
			param.put("beginDate",startAndEndMap.get("beginDate"));
			param.put("endDate",startAndEndMap.get("endDate"));
		}
		else {
			// 如果查询条件（起始日期）不为空，则增加起始日期查询参数
			if(StringUtil.isNotNullStr(beginDate)) {
				param.put("beginDate",beginDate);
			}
			else {
				param.put("beginDate",null);
			}

			// 如果查询条件（终止日期）不为空，则增加终止日期查询参数
			if(StringUtil.isNotNullStr(endDate)) {
				param.put("endDate",endDate);
			}
			else {
				param.put("endDate",null);
			}
		}
		String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        // 如果查询条件（所属部门）不为空，则增加所属部门查询参数
 		if(StringUtil.isNotNullStr(orgCode)) {
 			if(StringUtil.isNotNullStr(consCode)){
 				param.put("consCode", "'"+consCode+"'");
 			}else{
 				param.put("consCode",Util.getSubQueryByOrgCode(orgCode));
 			}
 		}
 		else {
 			// 获取默认登陆用户下属投顾信息
 			String outletcode=ConsOrgCache.getInstance().getCons2OutletMap().get(userId);
 			String tearmcode=ConsOrgCache.getInstance().getCons2TeamMap().get(userId);
 			String groupOrgCode=""; // 登陆用户所属的部门编码
 			String loginOrgCode=StringUtil.isNotNullStr(tearmcode)?tearmcode:outletcode; // 登陆用户所属的部门编码
 			String topgd=(String)request.getSession().getAttribute("topgddata");
 			if(StaticVar.DATARANGE_GD_ALL.equals(topgd)||StaticVar.DATARANGE_GD_ALL_NOWFP.equals(topgd)) {
 				groupOrgCode="0";
 			}
 			else if(StaticVar.DATARANGE_GD_OUTLET.equals(topgd)) {
 				groupOrgCode=outletcode;
 			}
 			else if(StaticVar.DATARANGE_GD_TEARM.equals(topgd)) {
 				groupOrgCode=tearmcode;
 			}
 			else {
 				groupOrgCode=loginOrgCode;
 			}
 			param.put("consCode",Util.getSubQueryByOrgCode(groupOrgCode));
 		}
 		// 将查询权限信息放入session中
 		request.getSession().setAttribute("deptConsCodeByShareBonus",param.get("consCode"));
		Map<String,Object> resultMap=new HashMap<String,Object>();
		PageData<ProductShareBonus> loadData=productShareBonusService.listProductShareBonusByPage(param);

		// 对产品分配日期进行格式处理，同时转义分配收益频率字段
		for(ProductShareBonus productShareBonus:loadData.getListData()) {
			if(StringUtil.isNotNullStr(productShareBonus.getFprq())) {
				productShareBonus.setFprq(DateTimeUtil.fmtDate(productShareBonus.getFprq(),"yyyyMMdd"));
			}
			if(StringUtil.isNotNullStr(productShareBonus.getSyfppl())&&ContextManager.SYFPPL_STATUS.containsKey(productShareBonus.getSyfppl())) {
				productShareBonus.setSyfppl(ContextManager.SYFPPL_STATUS.get(productShareBonus.getSyfppl()));
			}

		}
		resultMap.put("total",loadData.getPageBean().getTotalNum());
		resultMap.put("page",curPage);
		resultMap.put("rows",loadData.getListData());
		return resultMap;
	}
	
	/**
	 * 加载产品持有人数据方法
	 * 
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/loadProductHolding_json.do")
	public Map<String,Object> loadProductHolding_json(HttpServletRequest request,HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String,String> param=new HashMap<String,String>();
		// 获取分页参数
		param=new ParamUtil(request).getParamMap();

		// 获取登陆用户信息
		//HttpSession session=request.getSession();
		//String userId=(String)session.getAttribute("userId");
		//param.put("consCode",userId);
		String curPage=request.getParameter("page");
		String jjdm=request.getParameter("jjdm");
		String custName=request.getParameter("custName");
		
		// 获取查询权限信息
		String consCode=(String)request.getSession().getAttribute("deptConsCodeByShareBonus");
		param.put("consCodes",consCode);

		// 如果查询条件（基金代码）不为空，则增加基金代码查询参数
		if(StringUtil.isNotNullStr(jjdm)) {
			param.put("jjdm",jjdm);
		}
		else {
			param.put("jjdm",null);
		}

		// 如果查询条件（客户姓名）不为空，则增加客户姓名查询参数
		if(StringUtil.isNotNullStr(custName)) {
			param.put("custName",custName);
		}
		else {
			param.put("custName",null);
		}

		Map<String,Object> resultMap=new HashMap<String,Object>();
		PageData<ProductShareBonus> loadData=productShareBonusService.listProductHoldingByPage(param);

		// 对产品分配日期进行格式处理，同时转义分配收益频率字段
		for(ProductShareBonus productShareBonus:loadData.getListData()) {
			if(StringUtil.isNotNullStr(productShareBonus.getFprq())) {
				productShareBonus.setFprq(DateTimeUtil.fmtDate(productShareBonus.getFprq(),"yyyyMMdd"));
			}
			if(StringUtil.isNotNullStr(productShareBonus.getSyfppl())&&ContextManager.SYFPPL_STATUS.containsKey(productShareBonus.getSyfppl())) {
				productShareBonus.setSyfppl(ContextManager.SYFPPL_STATUS.get(productShareBonus.getSyfppl()));
			}

		}
		resultMap.put("total",loadData.getPageBean().getTotalNum());
		resultMap.put("page",curPage);
		resultMap.put("rows",loadData.getListData());
		return resultMap;
	}
	
	/**
	 * 跳转到产品持仓收益详细页面
	 * 
	 * @param request
	 * @param response
	 * @param model
	 * @return String
	 * @throws UnsupportedEncodingException
	 */
	@RequestMapping("/listProductHolding.do")
	public String listProductHolding(HttpServletRequest request,HttpServletResponse response,ModelMap model) throws UnsupportedEncodingException {
		String jjdm=request.getParameter("jjdm");
		request.setAttribute("jjdm",jjdm);
		return "custinfo/listProductHolding";
	}

	/**
	 * 跳转到我的预约客户（更多）页面
	 */
	@RequestMapping("/showBookingCustMore.do")
	public String showBookingCustMore(HttpServletRequest request,HttpServletResponse response,ModelMap model) throws UnsupportedEncodingException {
		Map<String,String> startAndEndWeekMap=DateTimeUtil.getLastWeekStartAndEnd();
		request.setAttribute("beginDate",startAndEndWeekMap.get("beginDate"));
		request.setAttribute("endDate",startAndEndWeekMap.get("endDate"));
		return "custinfo/listConsBookingCust";
	}
	
	
	/**
	 * 加载我的预约客户（更多）数据方法
	 * 
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/loadConsBookingCustMore_json.do")
	public Map<String,Object> loadConsBookingCustMore_json(HttpServletRequest request,HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String,String> param=new HashMap<String,String>();
		// 获取分页参数
		param=new ParamUtil(request).getParamMap();
		String curPage=request.getParameter("page");
		String beginDate=request.getParameter("beginDate");
		String endDate=request.getParameter("endDate");
		String bookStatus=request.getParameter("bookStatus");
		String orgCode=request.getParameter("orgCode");
		String consCode=request.getParameter("consCode");
		String creator=request.getParameter("creator");
		String searchFlag=request.getParameter("searchFlag");

		// 如果查询条件（起始日期）不为空，则增加起始日期查询参数
		if(StringUtil.isNotNullStr(beginDate)) {
			param.put("beginDate",beginDate);
		}
		else {
			param.put("beginDate",null);
		}

		// 如果查询条件（终止日期）不为空，则增加终止日期查询参数
		if(StringUtil.isNotNullStr(endDate)) {
			param.put("endDate",endDate);
		}
		else {
			param.put("endDate",null);
		}

		// 如果查询条件（预约状态）不为空，则增加预约状态查询参数
		if(StringUtil.isNotNullStr(bookStatus)) {
			param.put("bookStatus",bookStatus);
		}
		else {
			param.put("bookStatus",null);
		}

		// 获取登陆用户信息
		HttpSession session=request.getSession();
		String userId=(String)session.getAttribute("userId");
		param.put("loginUser",userId);

		// 如果查询条件（所属投顾）不为空，则增加所属投顾查询参数
		if(StringUtil.isNotNullStr(searchFlag)&&"true".equals(searchFlag)) {
			// 根据查询条件赋予用户权限
			if(StringUtil.isNullStr(consCode)&&StringUtil.isNullStr(creator)) {
				// 设置查询参数
				Map<String,String> loginParam=new HashMap<String,String>();
				loginParam.put("orgCode",orgCode);
				loginParam.put("loginUser",userId);
				String isExsit=cmConsBookingCustService.listDeptConsByLoginUser(loginParam);
				if(StringUtil.isNotNullStr(isExsit)) {
					param.put("isExsit","true");
				}
				else {
					param.put("isExsit",null);
				}
				param.put("creators",Util.getSubQueryByOrgCode(orgCode));
			}
		}
		else {
			Map<String,String> startAndEndWeekMap=DateTimeUtil.getLastWeekStartAndEnd();
			param.put("beginDate",startAndEndWeekMap.get("beginDate"));
			param.put("endDate",startAndEndWeekMap.get("endDate"));
			param.put("creator",userId);
		}

		PageData<CmConsBookingCust> custData=cmConsBookingCustService.listCmConsBookingCustByPage(param);
		// 对列表投顾字段进行转义
		for(CmConsBookingCust cmConsBookingCust:custData.getListData()) {
			if(StringUtil.isNotNullStr(cmConsBookingCust.getConsCode())&&ConsOrgCache.getInstance().getAllConsMap().containsKey(cmConsBookingCust.getConsCode())) {
				cmConsBookingCust.setConsName(ConsOrgCache.getInstance().getAllConsMap().get(cmConsBookingCust.getConsCode()));
			}
		}
		Map<String,Object> resultMap=new HashMap<String,Object>();
		resultMap.put("total",custData.getPageBean().getTotalNum());
		resultMap.put("page",curPage);
		resultMap.put("rows",custData.getListData());
		return resultMap;
	}
	
	
	/**
	 * 最近预约数据导出方法
	 * 
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/exportBookingCust.do")
	public Map<String,Object> exportBookingCust(HttpServletRequest request,HttpServletResponse response) throws Exception {
		Map<String,Object> resultMap=new HashMap<String,Object>();
		try {
			// 设置查询参数
			Map<String,String> param=new HashMap<String,String>();
			// 获取分页参数
			param=new ParamUtil(request).getParamMap();
			String beginDate=request.getParameter("beginDate");
			String endDate=request.getParameter("endDate");
			String bookStatus=request.getParameter("bookStatus");
			String orgCode=request.getParameter("orgCode");
			String consCode=request.getParameter("consCode");
			String creator=request.getParameter("creator");
			String searchFlag=request.getParameter("searchFlag");

			// 如果查询条件（起始日期）不为空，则增加起始日期查询参数
			if(StringUtil.isNotNullStr(beginDate)) {
				param.put("beginDate",beginDate);
			}
			else {
				param.put("beginDate",null);
			}

			// 如果查询条件（终止日期）不为空，则增加终止日期查询参数
			if(StringUtil.isNotNullStr(endDate)) {
				param.put("endDate",endDate);
			}
			else {
				param.put("endDate",null);
			}

			// 如果查询条件（预约状态）不为空，则增加预约状态查询参数
			if(StringUtil.isNotNullStr(bookStatus)) {
				param.put("bookStatus",bookStatus);
			}
			else {
				param.put("bookStatus",null);
			}

			// 获取登陆用户信息
			HttpSession session=request.getSession();
			String userId=(String)session.getAttribute("userId");
			param.put("loginUser",userId);

			// 如果查询条件（所属投顾）不为空，则增加所属投顾查询参数
			if(StringUtil.isNotNullStr(searchFlag)&&"true".equals(searchFlag)) {
				// 根据查询条件赋予用户权限
				if(StringUtil.isNullStr(consCode)&&StringUtil.isNullStr(creator)) {
					// 设置查询参数
					Map<String,String> loginParam=new HashMap<String,String>();
					loginParam.put("orgCode",orgCode);
					loginParam.put("loginUser",userId);
					String isExsit=cmConsBookingCustService.listDeptConsByLoginUser(loginParam);
					if(StringUtil.isNotNullStr(isExsit)) {
						param.put("isExsit","true");
					}
					else {
						param.put("isExsit",null);
					}
					param.put("creators",Util.getSubQueryByOrgCode(orgCode));
				}
				else {
					Map<String,String> startAndEndWeekMap=DateTimeUtil.getLastWeekStartAndEnd();
					param.put("beginDate",startAndEndWeekMap.get("beginDate"));
					param.put("endDate",startAndEndWeekMap.get("endDate"));
					param.put("creator",userId);
				}

				List<CmConsBookingCust> listBookingCust=cmConsBookingCustService.listCmConsBookingCustExport(param);
				if(listBookingCust!=null&&listBookingCust.size()>0) {
					// 对列表投顾字段进行转义
					for(CmConsBookingCust cmConsBookingCust:listBookingCust) {
						if(StringUtil.isNotNullStr(cmConsBookingCust.getConsCode())&&ConsOrgCache.getInstance().getAllConsMap().containsKey(cmConsBookingCust.getConsCode())) {
							cmConsBookingCust.setConsName(ConsOrgCache.getInstance().getAllConsMap().get(cmConsBookingCust.getConsCode()));
						}
					}
					BookingCustExportManager manager=new BookingCustExportManager();
					manager.exportBookingCustByList(response,listBookingCust);
					resultMap.put("msg","success");
				}else {
					resultMap.put("msg","noData");
					return resultMap;
				}
			}
		}
		catch(Exception e) {
			resultMap.put("msg","error");
			e.printStackTrace();
		}
		return resultMap;
	}
	
	
	
	/**
	 * 修改预约信息方法
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/toUpdateConsBookingCust.do")
	public String toUpdateConsBookingCust(HttpServletRequest request, HttpServletResponse response) throws IOException {
		String result;
		try {
			// 获取页面数据
			String consBookingId = request.getParameter("consBookingId");
			String visitType = request.getParameter("visitType");
			String bookingDt = request.getParameter("bookingDt");
			String bookingStartTime = request.getParameter("bookingStartTime");
			String bookingEndTime = request.getParameter("bookingEndTime");
			String content = request.getParameter("content");
			String bookingStatus = request.getParameter("bookingStatus");
			HttpSession session = request.getSession();
			String userId = (String)session.getAttribute("userId");
			
			// 修改预约记录信息
			CmConsBookingCust updateBookingCust = new CmConsBookingCust();
			updateBookingCust.setConsBookingId(consBookingId);
			updateBookingCust.setVisitType(visitType);
			updateBookingCust.setBookingDt(bookingDt);
			updateBookingCust.setBookingStartTime(bookingStartTime);
			updateBookingCust.setBookingEndTime(bookingEndTime);
			updateBookingCust.setContent(content.trim());
			updateBookingCust.setBookingStatus(bookingStatus);
			updateBookingCust.setModDt(DateTimeUtil.getCurDate());
			
			// 修改拜访记录信息
			CsCommunicateVisit csCommunicateVisit = new CsCommunicateVisit();
			csCommunicateVisit.setConsBookingId(consBookingId);
			csCommunicateVisit.setNextDt(bookingDt);
			csCommunicateVisit.setNextVisitType(visitType);
			csCommunicateVisit.setNextStartTime(bookingStartTime);
			csCommunicateVisit.setNextEndTime(bookingEndTime);
			csCommunicateVisit.setNextVisitContent(content.trim());
			csCommunicateVisit.setModifier(userId);
			csCommunicateVisit.setHisFlag("0");
			
			cmConsBookingCustService.updateCmConsBookingCustAll(updateBookingCust, csCommunicateVisit);
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}
	
	
	
	/**
	 * 处理预约信息方法
	 * @param request
	 * @param response
	 * @return
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/toDealConsBookingCust.do")
	public String toDealConsBookingCust(HttpServletRequest request, HttpServletResponse response) throws IOException {
		String result;
		try {
			// 获取新增拜访数据
			String custNo = request.getParameter("custNo");
			String consBookingId = request.getParameter("consBookingId");
			String visitType = request.getParameter("visitType");	// 新增拜访类型
			String visitClassify = request.getParameter("visitclassify");
			String dealContent = request.getParameter("deal_content");
			
			// 获取新增预约数据
			String bookingType = request.getParameter("bookingType");	// 新增预约类型
			String bookingDt = request.getParameter("bookingDt");
			String bookingStartTime = request.getParameter("bookingStartTime");
			String bookingEndTime = request.getParameter("bookingEndTime");
			String deal_bookingContent = request.getParameter("deal_bookingContent");
			String newConsBookingId = commonService.getSeqValue("SEQ_PCUSTREC");// 从序列中获取预约编号
			String appSerialNo = commonService.getSeqValue("SEQ_PCUSTREC");// 从序列中获取交易流水号
			String id = commonService.getSeqValue("SEQ_CS_COMMUNICATE_VISIT_ID");// 从序列中获取id
			HttpSession session = request.getSession();
			String userId = (String)session.getAttribute("userId");
			
			// 修改原来预约记录信息（根据consBookingId）
			CmConsBookingCust updateBookingCust = new CmConsBookingCust();
			updateBookingCust.setConsBookingId(consBookingId);
			updateBookingCust.setBookingStatus(BOOKING_STATUS_ONE);// 设置状态为已完成
			updateBookingCust.setVisitType(visitType);
			updateBookingCust.setModDt(DateTimeUtil.getCurDate());
			
			// 如果输入了预约记录信息，则新增一条预约信息到预约表
			CmConsBookingCust insertBookingCust = new CmConsBookingCust();
			insertBookingCust.setConsBookingId(newConsBookingId);
			insertBookingCust.setBookingCons(userId);	// 设置预约投顾
			insertBookingCust.setConsCustNo(custNo);	// 设置投顾客户
			insertBookingCust.setVisitType(bookingType);
			insertBookingCust.setBookingDt(bookingDt);
			insertBookingCust.setBookingStartTime(bookingStartTime);
			insertBookingCust.setBookingEndTime(bookingEndTime);
			insertBookingCust.setContent(deal_bookingContent);
			insertBookingCust.setBookingStatus(BOOKING_STATUS_ZERO);
			insertBookingCust.setCreDt(DateTimeUtil.getCurDate());
			insertBookingCust.setCreator(userId);
			
			// 新增一条拜访表记录信息
			CsCommunicateVisit insertCsCommunicateVisit = new CsCommunicateVisit();
			insertCsCommunicateVisit.setId(id);
			insertCsCommunicateVisit.setHisId(appSerialNo);
			insertCsCommunicateVisit.setHisFlag("0");
			insertCsCommunicateVisit.setModifyFlag("0");
			insertCsCommunicateVisit.setConsBookingId(newConsBookingId);
			insertCsCommunicateVisit.setConscustNo(custNo);
			insertCsCommunicateVisit.setVisitType(visitType);
			insertCsCommunicateVisit.setVisitClassify(visitClassify);
			insertCsCommunicateVisit.setCommContent(dealContent);
			insertCsCommunicateVisit.setNextDt(bookingDt);// 预约新增时间设置成下次拜访时间
			insertCsCommunicateVisit.setNextStartTime(bookingStartTime);
			insertCsCommunicateVisit.setNextEndTime(bookingEndTime);
			insertCsCommunicateVisit.setNextVisitContent(deal_bookingContent);
			insertCsCommunicateVisit.setNextVisitType(bookingType);
			insertCsCommunicateVisit.setCreator(userId);
			
			// 新增一条拜访最新记录表信息
			CsVisitNewest insertCsVisitNewest = new CsVisitNewest();
			insertCsVisitNewest.setId(id);
			insertCsVisitNewest.setHisId(appSerialNo);
			insertCsVisitNewest.setHisFlag("0");
			insertCsVisitNewest.setVisitType(visitType);
			insertCsVisitNewest.setVisitClassify(visitClassify);
			insertCsVisitNewest.setModifyFlag("0");
			insertCsVisitNewest.setConsBookingId(consBookingId);
			insertCsVisitNewest.setConscustNo(custNo);
			insertCsVisitNewest.setCommContent(dealContent);
			insertCsVisitNewest.setCreator(userId);
			
			// 如果预约新增中相关字段有填写，则往预约表增加一条记录
			if(StringUtil.isNotNullStr(bookingType) && StringUtil.isNotNullStr(bookingDt)){
				// 执行修改、新增预约表数据和添加拜访表、拜访最新表数据方法
				cmConsBookingCustService.insertDelUpdateCmConsBookingCustAll(updateBookingCust, insertBookingCust, insertCsCommunicateVisit, custNo, insertCsVisitNewest);
			}else{
				cmConsBookingCustService.insertDelUpdateCmConsBookingCustAll(updateBookingCust, null, insertCsCommunicateVisit, custNo, insertCsVisitNewest);
			}
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}
	
	
	
	/**
	 * 添加预约信息方法
	 * @param request
	 * @param response
	 * @return String
	 * @throws IOException
	 */
	@ResponseBody
	@RequestMapping("/toAddConsBookingCust.do")
	public String toAddConsBookingCust(HttpServletRequest request, HttpServletResponse response) throws IOException {
		String result;
		try {
			// 获取拜访预约数据
			String custNo = request.getParameter("custNo");
			String visitType = request.getParameter("visitType");	// 新增拜访类型
			String visitClassify = request.getParameter("visitclassify");
			String add_content = request.getParameter("add_content");
			String bookingType = request.getParameter("bookingType");	// 新增预约类型
			String bookingDt = request.getParameter("bookingDt");
			String bookingStartTime = request.getParameter("bookingStartTime");
			String bookingEndTime = request.getParameter("bookingEndTime");
			String add_bookingContent = request.getParameter("add_bookingContent");
			String newConsBookingId = commonService.getSeqValue("SEQ_PCUSTREC");// 从序列中获取预约编号
			String appSerialNo = commonService.getSeqValue("SEQ_PCUSTREC");// 从序列中获取交易流水号
			String id = commonService.getSeqValue("SEQ_CS_COMMUNICATE_VISIT_ID");// 从序列中获取id
			HttpSession session = request.getSession();
			String userId = (String)session.getAttribute("userId");
			
			// 如果输入了预约记录信息，则新增一条预约信息到预约表
			CmConsBookingCust insertBookingCust = new CmConsBookingCust();
			insertBookingCust.setConsBookingId(newConsBookingId);
			insertBookingCust.setBookingCons(userId);	// 设置预约投顾
			insertBookingCust.setConsCustNo(custNo);	// 设置投顾客户
			insertBookingCust.setVisitType(bookingType);
			insertBookingCust.setBookingDt(bookingDt);
			insertBookingCust.setBookingStartTime(bookingStartTime);
			insertBookingCust.setBookingEndTime(bookingEndTime);
			insertBookingCust.setContent(add_bookingContent);
			insertBookingCust.setBookingStatus(BOOKING_STATUS_ZERO);
			insertBookingCust.setCreDt(DateTimeUtil.getCurDate());
			insertBookingCust.setCreator(userId);
			
			// 新增一条拜访表记录信息
			CsCommunicateVisit insertCsCommunicateVisit = new CsCommunicateVisit();
			insertCsCommunicateVisit.setId(id);
			insertCsCommunicateVisit.setHisId(appSerialNo);
			insertCsCommunicateVisit.setHisFlag("0");
			insertCsCommunicateVisit.setModifyFlag("0");
			insertCsCommunicateVisit.setConsBookingId(newConsBookingId);
			insertCsCommunicateVisit.setConscustNo(custNo);
			insertCsCommunicateVisit.setVisitType(visitType);
			insertCsCommunicateVisit.setVisitClassify(visitClassify);
			insertCsCommunicateVisit.setCommContent(add_content);
			insertCsCommunicateVisit.setNextDt(bookingDt);// 预约新增时间设置成下次拜访时间
			insertCsCommunicateVisit.setNextStartTime(bookingStartTime);
			insertCsCommunicateVisit.setNextEndTime(bookingEndTime);
			insertCsCommunicateVisit.setNextVisitContent(add_bookingContent);
			insertCsCommunicateVisit.setNextVisitType(bookingType);
			insertCsCommunicateVisit.setCreator(userId);
			
			// 新增一条拜访最新记录表信息
			CsVisitNewest insertCsVisitNewest = new CsVisitNewest();
			insertCsVisitNewest.setId(id);
			insertCsVisitNewest.setHisId(appSerialNo);
			insertCsVisitNewest.setHisFlag("0");
			insertCsVisitNewest.setModifyFlag("0");
			insertCsVisitNewest.setVisitType(visitType);
			insertCsVisitNewest.setVisitClassify(visitClassify);
			insertCsVisitNewest.setCommContent(add_content);
			insertCsVisitNewest.setConsBookingId(newConsBookingId);
			insertCsVisitNewest.setConscustNo(custNo);
			insertCsVisitNewest.setCreator(userId);
			
			// 如果预约新增中相关字段有填写，则往预约表增加一条记录（此操作中不涉及修改，所以在调用第一个通用方法是给赋null值）
			if(StringUtil.isNotNullStr(bookingType) && StringUtil.isNotNullStr(bookingDt)){
				// 执行新增预约表数据和添加拜访表、拜访最新表数据方法
				cmConsBookingCustService.insertDelUpdateCmConsBookingCustAll(null, insertBookingCust, insertCsCommunicateVisit, custNo, insertCsVisitNewest);
			}else{
				cmConsBookingCustService.insertDelUpdateCmConsBookingCustAll(null, null, insertCsCommunicateVisit, custNo, insertCsVisitNewest);
			}
			result = "success";
		} catch (Exception e) {
			result = "error";
			e.printStackTrace();
		}
		return result;
	}

	/**
	 * 自动补全投顾客户方法
	 */
	@ResponseBody
	@RequestMapping("/autoCompleteConsCust.do")
	public Map<String, List<Conscust>> autoCompleteConsCust(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		String searchParam = request.getParameter("term");
		String flag=request.getParameter("flag");
		param.put("searchParam", searchParam);
		
		// 如果传入客户投顾不为空，则增加投顾条件限制
		if(StringUtil.isNotNullStr(flag)){
			String consCode = request.getParameter("consCode");
			param.put("consCode", consCode);
		} else {
			// 否则使用默认客户所属投顾条件限制
			HttpSession session = request.getSession();
			String userId = (String)session.getAttribute("userId");
			param.put("consCode", userId);
		}
		
		List<Conscust> listConscust = conscustService.listCustInfoByCustTel(param);
		Map<String,List<Conscust>> result = new HashMap<String,List<Conscust>>();
		result.put("result", listConscust);
		return result;
	}
}
