package com.howbuy.crm.hb.web.controller.fixed;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.howbuy.crm.hb.constants.CrmTradeServerPathConstant;
import com.howbuy.crm.hb.domain.fixed.CmFixedIntention;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.fixed.CmFixedIntentionService;
import com.howbuy.crm.hb.web.controller.counter.BaseCounterController;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prosale.request.GetInfoByParamRequest;
import com.howbuy.crm.prosale.response.GetPreTigFundCustResponse;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import com.howbuy.crm.trade.common.enums.counter.CounterBusiEnum;
import com.howbuy.crm.trade.common.enums.counter.CounterStateEnum;
import com.howbuy.crm.trade.common.response.BaseResponse;
import com.howbuy.crm.trade.model.counter.po.CmCounterOrder;
import com.howbuy.crm.trade.model.counter.request.SaveCmCounterOrderRequest;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplan.QueryHighFundInvPlanFacade;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplan.QueryHighFundInvPlanRequest;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplan.QueryHighFundInvPlanResponse;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplan.bean.HighFundInvPlanVo;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplandtl.QueryHighFundInvPlanDtlFacade;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplandtl.QueryHighFundInvPlanDtlRequest;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplandtl.QueryHighFundInvPlanDtlResponse;
import com.howbuy.tms.high.orders.facade.search.queryhighfundinvplandtl.bean.HighFundInvPlanDtlVo;
import com.howbuy.tms.high.orders.facade.search.queryinvplancustinfoforcrm.QueryInvPlanCustInfoForCrmFacade;
import com.howbuy.tms.high.orders.facade.search.queryinvplancustinfoforcrm.QueryInvPlanCustInfoForCrmRequest;
import com.howbuy.tms.high.orders.facade.search.queryinvplancustinfoforcrm.QueryInvPlanCustInfoForCrmResponse;
import com.howbuy.tms.high.orders.facade.search.queryinvplancustinfoforcrm.bean.BalanceVolDtlPlanBean;
import com.howbuy.tms.high.orders.facade.search.queryinvplancustinfoforcrm.bean.BankCardInfoBean;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
@Slf4j
@Controller
@RequestMapping(value = "/fixed")
public class CmFixedIntentionController extends BaseCounterController {
	
	private Logger logger= LoggerFactory.getLogger(CmFixedIntentionController.class);
	
	@Autowired
	private CmFixedIntentionService cmFixedIntentionService;
	
	@Autowired
    private CommonService commonService;
	
	@Autowired
    private QueryPreBookService queryPreBookService;
	
	@Autowired
	private ConscustService conscustService;
	
	@Autowired
    private QueryInvPlanCustInfoForCrmFacade queryInvPlanCustInfoForCrmFacade;
	
	@Autowired
	private QueryHighFundInvPlanFacade queryHighFundInvPlanFacade;
	
	@Autowired
	private QueryHighFundInvPlanDtlFacade queryHighFundInvPlanDtlFacade;
	



	@RequestMapping("/listFixedIntention.do")
	public ModelAndView listFixedIntention(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();        
        modelAndView.setViewName("/fixed/listFixedIntention");
        return modelAndView;
	}
	
	/**
	 * 加载列表页面数据
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listFixedIntentionByPage.do")
	public Map<String, Object> listFixedIntentionByPage(HttpServletRequest request)	throws Exception {
		Map<String, String> param = new ParamUtil(request).getParamMap();
		PageData<CmFixedIntention> pageData = cmFixedIntentionService.listCmFixedIntentionByPage(param);
		List<CmFixedIntention> list = pageData.getListData();
		for(CmFixedIntention info : list){
			info.setFixedstateval(ConstantCache.getInstance().getVal("fixedstate", info.getFixedstate()));
			info.setPlanstateval(ConstantCache.getInstance().getVal("fixedplanstate", info.getPlanstate()));
			info.setDiscountstateval(ConstantCache.getInstance().getVal("discountStates", info.getDiscountstate()));
			info.setDoublestateval(ConstantCache.getInstance().getVal("fixedoublestate", info.getDoublestate()));
            ConsOrgCache orgcache = ConsOrgCache.getInstance();
            String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
            if("0".equals(uporgcode)){
                info.setUporgname(orgcache.getAllOrgMap().get(info.getOrgcode()));
            }else{
                info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
            }
			info.setOutletName(ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
			info.setConscode(ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
		}
		// 返回查询结果
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<CmFixedIntention> listdata = pageData.getListData();
		resultMap.put("rows", listdata);
		
		return resultMap;
	}
	
	/**
     * 跳转到新增意向单页面方法
     * @return String
     */
    @ResponseBody
    @RequestMapping("/addFixedIntention.do")
    public ModelAndView addFixedIntention(HttpServletRequest request) throws Exception {
		Map<String,Object> returnMap= Maps.newHashMap();
		returnMap.put("selectedBusiType",CounterBusiEnum.AIP_ADD.getKey());
        return new ModelAndView("fixed/addFixedIntention",returnMap);
    }
	
	
	/**
     * 新增定投意向单
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveFixedIntention.do", method = RequestMethod.POST)
    public BaseResponse<String> saveFixedIntention(HttpServletRequest request) {
		String fundcode = request.getParameter("fundcode");
		String custno = request.getParameter("custno");
		String conscode = request.getParameter("conscode");
		String cpAcctNo = request.getParameter("cpAcctNo");
		String planrate = request.getParameter("planrate");
		String paytype = request.getParameter("paytype");
		String plantotalnum = request.getParameter("plantotalnum");
		String operatorNo =getLoginUserId(request);

		SaveCmCounterOrderRequest orderRequest=new SaveCmCounterOrderRequest();
		orderRequest.setBdId(request.getParameter("bdid"));
		orderRequest.setConscustNo(custno);
		orderRequest.setPCode(fundcode);

		orderRequest.setOperatorNo(operatorNo);


		String planAmount=request.getParameter("planAmount");
		String minAmt=request.getParameter("minAmt");
		String prodDiffer=request.getParameter("prodDiffer");

		//定义 forId
		String planId= commonService.getSeqValue("SEQ_PREBOOK");
		orderRequest.setForId(planId);

		if(StringUtil.isNotNullStr(planAmount)){
			orderRequest.setPlanAmount(new BigDecimal(planAmount));
		}
		if(StringUtil.isNotNullStr(minAmt) && !"无".equals(minAmt.trim())){
			orderRequest.setMinAmt(new BigDecimal(minAmt));
		}
		if(StringUtil.isNotNullStr(prodDiffer) && !"无".equals(prodDiffer.trim())  ){
			orderRequest.setProdDiffer(new BigDecimal(prodDiffer));
		}

		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		List<MultipartFile> files = multipartRequest.getFiles("filelist[]");
		//文件处理
		Map<String, List<String>>  fileMap=processCounterFile (files);
		orderRequest.setAddFileMap(fileMap);
		BaseResponse<String> counterResp= getPostEntityByJsonObject(CrmTradeServerPathConstant.INSERT_COUNTER_ORDER,
				orderRequest,
				new ParameterizedTypeReference<BaseResponse<String>>(){});

         //上传不成功 ：
		if(!counterResp.isSuccess()){
           return  counterResp;
		}

//		级差的判断在订单上传的校验中实现

		//插入 意向单数据：
		CmFixedIntention fixedinfo = new CmFixedIntention();
		fixedinfo.setId(new BigDecimal(planId));
		fixedinfo.setConscustno(custno);
		fixedinfo.setConscode(conscode);
		fixedinfo.setFundcode(fundcode);
		fixedinfo.setPlanamount(new BigDecimal(planAmount));
		fixedinfo.setPlanrate(planrate);
		fixedinfo.setCpacctno(cpAcctNo);
		fixedinfo.setPaytype(paytype);
		fixedinfo.setFixedstate("1");
		fixedinfo.setPlanstate("1");
		fixedinfo.setCreator(operatorNo);
		fixedinfo.setPlantotalnum(plantotalnum);
		cmFixedIntentionService.insertCmFixedIntention(fixedinfo);


		String fronAlertMsgPrefix="";

		//查询预约后提示语句
		GetInfoByParamRequest checkreq = new GetInfoByParamRequest();
		checkreq.setConscustno(custno);
		checkreq.setFundcode(fundcode);
		GetPreTigFundCustResponse tigresponse = queryPreBookService.getPreTigFundCust(checkreq);

		if (BaseConstantEnum.SUCCESS.getCode().equals(tigresponse.getReturnCode())) {
			fronAlertMsgPrefix=StringUtils.isNotBlank(tigresponse.getMsg())?tigresponse.getMsg():"新增成功！";
		} else {
			fronAlertMsgPrefix="预约提示" + tigresponse.getDescription();
		}
		String  frontMsg=fronAlertMsgPrefix+"<br><br><span style='color:red'>是否需要申请折扣？</span>";

		BaseResponse<String> baseReturn=BaseResponse.ok();
		baseReturn.setData(planId);
		baseReturn.setReturnMsg(frontMsg);


        return baseReturn;
    }
    

    private String formatStr(String num,int point){
    	if(StringUtil.isNull(num)){
    		num = "0.00";
    	}
    	return new BigDecimal(num).setScale(point, BigDecimal.ROUND_HALF_UP).toPlainString();
    }
	
	/**
	 * 展示修改页面
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/viewCmBxChannel", method = RequestMethod.POST)
	@ResponseBody
    public  Map<String, Object> viewCmBxChannel(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		resultMap.put("errorMsg", "操作成功");
		resultMap.put("errorCode", "0000");
		String id = request.getParameter("id");
		if(StringUtils.isNotBlank(id)){
			Map<String,Object> param = new HashMap<String,Object> (1);
			param.put("id", id);
			CmFixedIntention obj = cmFixedIntentionService.getCmFixedIntention(param);
			resultMap.put("domain", obj);
		}else{
			resultMap.put("errorMsg", "操作失败：id不能为空");
			resultMap.put("errorCode", "9999");
		}
      
        return resultMap;
    }
	
	@RequestMapping("/getFixedBankInfoList")
    @ResponseBody
    public void getFixedBankInfoList(HttpServletRequest request, HttpServletResponse response) {
        String custno = request.getParameter("custno");
        String fundcode = request.getParameter("fundcode");
        Map<String, String> param = new HashMap<>(1);
        param.put("conscustno",custno);
        Map<String,Object> hboneMap = conscustService.getHboneInfo(param);
        String hboneno = "";
        if(hboneMap != null){
        	hboneno = ObjectUtils.ObjectToString(hboneMap.get("HBONENO"));
        }
        QueryInvPlanCustInfoForCrmRequest req = new QueryInvPlanCustInfoForCrmRequest();
        req.setHbOneNo(hboneno);
        //中台必填字段，但是中台搜索条件用不到下面两个字段，所以写死下面两个字段的值
  		req.setDisCode(StaticVar.HB_DISCODE);
  		req.setOutletCode("W20170215");
  		req.setOperIp("127.0.0.1");
  		req.setTxChannel("1");
  		req.setDataTrack(DateTimeUtil.getCurDateTime()+"13");
        logger.info("根据一账通号获取中台定投银行账号传参:"+ JSON.toJSONString(req));
        QueryInvPlanCustInfoForCrmResponse res = new QueryInvPlanCustInfoForCrmResponse();
        res = queryInvPlanCustInfoForCrmFacade.execute(req);
        logger.info("根据一账通号获取中台定投银行账号回参:"+ JSON.toJSONString(res));
        String sb = "";
        if(res != null && res.getBankCardInfoBeanList() != null){
        	List<BankCardInfoBean> listbank = res.getBankCardInfoBeanList();
        	
            if (listbank != null && listbank.size() > 0) {
                sb += "[{\"id\": \"\", \"text\": \"请选择\"},";
                //如果传了产品代码，判断产品代码对应的是否只支持单卡的，如果是，只能推出单卡的银行卡号
                if(StringUtil.isNotNullStr(fundcode) && res.getBalanceVolDtlPlanBeanList() != null){
                	List<BalanceVolDtlPlanBean> listproduct = res.getBalanceVolDtlPlanBeanList();
                	for (BalanceVolDtlPlanBean productinfo : listproduct) {
                		if(fundcode.equals(productinfo.getFundCode())){
                			//单卡
                			if("2".equals(productinfo.getSupportCardType()) || "3".equals(productinfo.getSupportCardType())){
                				for (BankCardInfoBean bankinfo : listbank) {
                					if(productinfo.getCpAcctNoList().contains(bankinfo.getCpAcctNo())){
                						String text = bankinfo.getBankRegionName() + ' ' + bankinfo.getBankAcct();
                						sb += "{ \"id\": \"" + bankinfo.getCpAcctNo() + "\", \"text\": \"" + text + "\" },";
                					}
            	                } 
                			}else{
                				//支持多卡
                				for (BankCardInfoBean bankinfo : listbank) {
            	                    String text = bankinfo.getBankRegionName() + ' ' + bankinfo.getBankAcct();
            	                    sb += "{ \"id\": \"" + bankinfo.getCpAcctNo() + "\", \"text\": \"" + text + "\" },";
            	                }
                			}
                		}
                	}
                }else{
	                for (BankCardInfoBean bankinfo : listbank) {
	                    String text = bankinfo.getBankRegionName() + ' ' + bankinfo.getBankAcct();
	                    sb += "{ \"id\": \"" + bankinfo.getCpAcctNo() + "\", \"text\": \"" + text + "\" },";
	                }
                }
                sb = sb.substring(0, sb.lastIndexOf(","));
                sb += "]";
            }
        }

//        
//        String sb = "";
//        sb += "[{\"id\": \"\", \"text\": \"请选择\"},";
//
//        sb += "{ \"id\": \"" + "************" + "\", \"text\": \"" + "*************** 工商银行" + "\" },";
//        sb += "{ \"id\": \"" + "************" + "\", \"text\": \"" + "************* 建设银行" + "\" },";
//        sb = sb.substring(0, sb.lastIndexOf(","));
//        sb += "]";

        response.setContentType("text/html; charset=utf-8");
        PrintWriter pw = null;
        try {
            pw = response.getWriter();
            pw.print(sb.toString());
            pw.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (pw != null){
                pw.close();
            }
        }

    }
	
	@RequestMapping("/getFixedProductInfoList")
    @ResponseBody
    public void getFixedProductInfoList(HttpServletRequest request, HttpServletResponse response) {

		String custno = request.getParameter("custno");
        Map<String, String> param = new HashMap<>(1);
        param.put("conscustno",custno);
        Map<String,Object> hboneMap = conscustService.getHboneInfo(param);
        String hboneno = "";
        if(hboneMap != null){
        	hboneno = ObjectUtils.ObjectToString(hboneMap.get("HBONENO"));
        }
        QueryInvPlanCustInfoForCrmRequest req = new QueryInvPlanCustInfoForCrmRequest();
        req.setHbOneNo(hboneno);
        //中台必填字段，但是中台搜索条件用不到下面两个字段，所以写死下面两个字段的值
		req.setDisCode(StaticVar.HB_DISCODE);
		req.setOutletCode("W20170215");
		req.setOperIp("127.0.0.1");
		req.setTxChannel("1");
		req.setDataTrack(DateTimeUtil.getCurDateTime()+"12");
        logger.info("根据一账通号获取中台定投产品传参:"+ JSON.toJSONString(req));
        QueryInvPlanCustInfoForCrmResponse res = new QueryInvPlanCustInfoForCrmResponse();
        res = queryInvPlanCustInfoForCrmFacade.execute(req);
        logger.info("根据一账通号获取中台定投产品回参:"+ JSON.toJSONString(res));

	      String sb = "";
	      if(res != null && res.getBalanceVolDtlPlanBeanList() != null){
	      	List<BalanceVolDtlPlanBean> listproduct = res.getBalanceVolDtlPlanBeanList();
	          if (listproduct != null && listproduct.size() > 0) {
	              sb += "[{\"id\": \"\", \"text\": \"请选择\"},";
	              for (BalanceVolDtlPlanBean productinfo : listproduct) {
	                  String text = productinfo.getFundCode() + ' ' + productinfo.getFundAttr();
	                  String minAmt = "无";
	                  if(productinfo.getMinAddPurchaseAmt() != null){
	                	  minAmt = productinfo.getMinAddPurchaseAmt().toPlainString();
	                  }
	                  String prodDiffer = "无";
	                  if(productinfo.getProdDiffer() != null){
	                	  prodDiffer = String.valueOf(productinfo.getProdDiffer());
	                  }
	                  sb += "{ \"id\": \"" + productinfo.getFundCode()+"#"+minAmt+"#"+ prodDiffer + "\", \"text\": \"" + text + "\" },";
	              }
	              sb = sb.substring(0, sb.lastIndexOf(","));
	              sb += "]";
	          }
	      }
        
//        String sb = "";
//        sb += "[{\"id\": \"\", \"text\": \"请选择\"},";
//
//        sb += "{ \"id\": \"" + "S27871#10000#1000" + "\", \"text\": \"" + "S27871 新方程汇嘉对冲精选1号" + "\" },";
//        sb += "{ \"id\": \"" + "S27872#10000#1000" + "\", \"text\": \"" + "S27872 新方程汇嘉对冲精选2号" + "\" },";
//        sb = sb.substring(0, sb.lastIndexOf(","));
//        sb += "]";

        response.setContentType("text/html; charset=utf-8");
        PrintWriter pw = null;
        try {
            pw = response.getWriter();
            pw.print(sb.toString());
            pw.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (pw != null){
                pw.close();
            }
        }

    }
	
	/**
     * 导出操作
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportPlanInfo.do")
    public void exportPlanInfo(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
    	Map<String, String> param = new ParamUtil(request).getParamMap();
    	List<CmFixedIntention> list = cmFixedIntentionService.listCmFixedIntention(param);
    	for(CmFixedIntention info : list){
			info.setFixedstateval(ConstantCache.getInstance().getVal("fixedstate", info.getFixedstate()));
			info.setPlanstateval(ConstantCache.getInstance().getVal("fixedplanstate", info.getPlanstate()));
			info.setDiscountstateval(ConstantCache.getInstance().getVal("discountStates", info.getDiscountstate()));
			if(StringUtil.isNullStr(info.getDiscountstateval())){
				info.setDiscountstateval("未申请");
			}
			info.setDoublestateval(ConstantCache.getInstance().getVal("fixedoublestate", info.getDoublestate()));
            ConsOrgCache orgcache = ConsOrgCache.getInstance();
            String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
            if("0".equals(uporgcode)){
                info.setUporgname(orgcache.getAllOrgMap().get(info.getOrgcode()));
            }else{
                info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
            }
			info.setOutletName(ConsOrgCache.getInstance().getAllOrgMap().get(info.getOrgcode()));
			info.setConscode(ConsOrgCache.getInstance().getAllUserMap().get(info.getConscode()));
		}
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String("意向单信息导出.xls".getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String [] columnName = new String [] {"录入时间","一账通号","投顾客户号","客户姓名","预约时所属投顾","所属部门","所属区域","产品代码","产品名称","定投期数","定投金额","意向单状态","折扣状态","双录状态","定投状态","已定投期数"};

            String [] beanProperty = new String [] {"creattimeval","hboneno","conscustno","custname","conscode","outletName","uporgname","fundcode","fundname","plantotalnum","planamount","planstateval","discountstateval","doublestateval","fixedstateval","planhasnum"};


            ExcelWriter.writeExcel(os, "意向单信息", 0, list, columnName, beanProperty);
            os.close(); 
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }
    	
    }
    
    /**
     * 撤销意向单
     * @param request
    * <AUTHOR>
    * @date 2021/12/08
    */
    @RequestMapping("/cancelPlan.do")
    @ResponseBody
    public String cancelPlan(HttpServletRequest request){
        String planid = request.getParameter("id");

		CmCounterOrder order= getValidOrderByForId(planid,CounterBusiEnum.AIP_ADD);
		if(order==null){
          return  String.format("该意向单 intentionid :%s 未关联柜台订单",planid);
		}

         //作废 意向单
		Map<String,String> postParam = new HashMap<String,String>();
		postParam.put("id", order.getId());
		postParam.put("operatorNo", getLoginUserId(request));
		BaseResponse<String> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.INVALID_COUNTER_ORDER,
				postParam,
				new ParameterizedTypeReference<BaseResponse<String>>(){});
		//关联账户对应的busiId
		return  httpRsp.isSuccess() ? "success" : httpRsp.getReturnMsg();
    }
    
    /**
     * 详情
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/viewPlan.do")
    public ModelAndView viewPlan(HttpServletRequest request){
        Map<String,Object> map = new HashMap<String,Object>(8);
        String id = request.getParameter("id");
        CmFixedIntention info = new CmFixedIntention();
		if(StringUtils.isNotBlank(id)){
			Map<String,Object> param = new HashMap<String,Object> (1);
			param.put("planid", id);
			info = cmFixedIntentionService.getCmFixedIntention(param);
			ConstantCache constantCache = ConstantCache.getInstance();
			info.setFixedstateval(constantCache.getVal("fixedstate", info.getFixedstate()));
			info.setPlanstateval(constantCache.getVal("fixedplanstate", info.getPlanstate()));
			info.setPaytypeval(constantCache.getVal("fixedpaytype", info.getPaytype()));
			info.setPlanrate(constantCache.getVal("fixedPlanRate", info.getPlanrate()));
			if(StringUtil.isNotNullStr(info.getDiscountstate())){
				info.setDiscountstateval(constantCache.getVal("discountStates", info.getDiscountstate()));
			}else{
				info.setDiscountstateval("未申请");
			}
			info.setDoublestateval(constantCache.getVal("fixedoublestate", info.getDoublestate()));
            ConsOrgCache orgcache = ConsOrgCache.getInstance();
            String uporgcode = orgcache.getUpOrgMapCache().get(info.getOrgcode());
            if(StaticVar.HOWBUY_ORGCODE.equals(uporgcode)){
                info.setUporgname(orgcache.getAllOrgMap().get(info.getOrgcode()));
            }else{
                info.setUporgname(orgcache.getAllOrgMap().get(uporgcode));
            }
			info.setOutletName(orgcache.getAllOrgMap().get(info.getOrgcode()));
			info.setConscode(orgcache.getAllUserMap().get(info.getConscode()));
			info.setCreator(orgcache.getAllUserMap().get(info.getCreator())+" ("+info.getCreattimeval()+")");
			info.setDischecker(orgcache.getAllUserMap().get(info.getDischecker()));
			info.setDiscountstyle(constantCache.getVal("discountStyles", info.getDiscountstyle()));
			info.setDiscountWay(constantCache.getVal("discountWays", info.getDiscountWay()));
			info.setDiscountType(constantCache.getVal("discountTypes", info.getDiscountType()));
			if(info.getIsrefund() != null){
				if(StaticVar.DISCOUNT_ISREFUNDS_NO.equals(info.getIsrefund())){
					info.setIsrefund("否");
				}else{
					info.setIsrefund("是");
				}
			}
			// 查银行卡信息
			BankCardInfoBean bankinfo = new BankCardInfoBean();
			if(StringUtil.isNotNullStr(info.getCpacctno()) && StringUtil.isNotNullStr(info.getConscustno())){
				bankinfo = getBankInfo(info.getConscustno(),info.getCpacctno());
			}
			map.put("bankinfo", bankinfo);
			map.put("info", info);
			// 历史逻辑：查询资料管理审核通过人和通过时间
//			SELECT T1.CONSNAME || ' (' ||
//					TO_CHAR(T.CURCHECKDATE, 'yyyymmdd hh24:mi:ss') || ')' des
//			FROM CM_COUNTER_ORDER T
//			LEFT JOIN CM_CONSULTANT T1
//			ON T.CURCHECKER = T1.CONSCODE
//			WHERE T.BDID LIKE '0241%'
//			AND T.INTENTIONID = #{intentionid}
//			AND T.CURSTAT = '9'
			CmCounterOrder orderDto= getValidOrderByForId(id,CounterBusiEnum.AIP_ADD);
			if(orderDto!=null && CounterStateEnum.ORDER_PASS.getKey().equals(orderDto.getCurStat())){
				String constanName=ConsOrgCache.getInstance().getAllUserMap().get(orderDto.getCurChecker());
				map.put("checker", String.format("%s (%s)",constanName, DateUtil.getDateFormat(orderDto.getCurCheckdate(),"yyyy-MM-dd HH:mm:ss")));
			}else{
				map.put("checker","");
			}

		}
		return new ModelAndView("fixed/viewPlan","map",map);
    }
    
    private BankCardInfoBean getBankInfo(String custno,String cpAcctNo){
    	BankCardInfoBean info = new BankCardInfoBean();
    	Map<String, String> param = new HashMap<>(1);
        param.put("conscustno",custno);
        Map<String,Object> hboneMap = conscustService.getHboneInfo(param);
        String hboneno = "";
        if(hboneMap != null){
        	hboneno = ObjectUtils.ObjectToString(hboneMap.get("HBONENO"));
        }
        QueryInvPlanCustInfoForCrmRequest req = new QueryInvPlanCustInfoForCrmRequest();
        req.setHbOneNo(hboneno);
        //中台必填字段，但是中台搜索条件用不到下面两个字段，所以写死下面两个字段的值
  		req.setDisCode(StaticVar.HB_DISCODE);
  		req.setOutletCode("W20170215");
  		req.setOperIp("127.0.0.1");
  		req.setTxChannel("1");
  		req.setDataTrack(DateTimeUtil.getCurDateTime()+"14");
        logger.info("根据一账通号获取中台定投银行账号传参:"+ JSON.toJSONString(req));
        QueryInvPlanCustInfoForCrmResponse res = new QueryInvPlanCustInfoForCrmResponse();
        res = queryInvPlanCustInfoForCrmFacade.execute(req);
        logger.info("根据一账通号获取中台定投银行账号回参:"+ JSON.toJSONString(res));
        if(res != null && res.getBankCardInfoBeanList() != null){
        	List<BankCardInfoBean> listbank = res.getBankCardInfoBeanList();
            if (listbank != null && listbank.size() > 0) {
                for (BankCardInfoBean bankinfo : listbank) {
                    if(cpAcctNo.equals(bankinfo.getCpAcctNo())){
                    	info = bankinfo;
                    	break;
                    }
                }
                
            }
        }
        return info;
    }
    
    /**
     * 定投合约
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/viewFixedTreaty.do")
    public ModelAndView viewFixedTreaty(HttpServletRequest request){
        Map<String,Object> map = new HashMap<String,Object>(8);
        String id = request.getParameter("id");
        HighFundInvPlanVo vol = new HighFundInvPlanVo();
        CmFixedIntention info = new CmFixedIntention();
		if(StringUtils.isNotBlank(id)){
			Map<String,Object> param = new HashMap<String,Object> (1);
			param.put("planid", id);
			info = cmFixedIntentionService.getCmFixedIntention(param);
			QueryHighFundInvPlanRequest req =  new QueryHighFundInvPlanRequest();
			req.setPlanId(id);
			req.setHbOneNo(info.getHboneno());
			//中台必填字段，但是中台搜索条件用不到下面两个字段，所以写死下面两个字段的值
			req.setDisCode(StaticVar.HB_DISCODE);
			req.setOutletCode("W20170215");
			req.setOperIp("127.0.0.1");
			req.setTxChannel("1");
			req.setDataTrack(DateTimeUtil.getCurDateTime()+"12");
			QueryHighFundInvPlanResponse res = new QueryHighFundInvPlanResponse();
			logger.info("根据意向单号查询定投合约传参:"+ JSON.toJSONString(req));
			res = queryHighFundInvPlanFacade.execute(req);
			logger.info("根据意向单号查询定投合约出参:"+ JSON.toJSONString(res));
			if(res != null && res.getHighFundInvPlanVo() != null){
				vol = res.getHighFundInvPlanVo();
			}
		}
		map.put("info", vol);
		return new ModelAndView("fixed/viewFixedTreaty","map",map);
    }
    
    /**
     * 定投计划
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/viewFixedPlan.do")
    public ModelAndView viewFixedPlan(HttpServletRequest request){
        Map<String,Object> map = new HashMap<String,Object>(8);
        String id = request.getParameter("id");
        List<HighFundInvPlanDtlVo> list = new ArrayList<HighFundInvPlanDtlVo>();
        CmFixedIntention info = new CmFixedIntention();
		if(StringUtils.isNotBlank(id)){
			Map<String,Object> param = new HashMap<String,Object> (1);
			param.put("planid", id);
			info = cmFixedIntentionService.getCmFixedIntention(param);
			list = getListHighFundInvPlanDtl(info);
		}
		map.put("list", list);
		return new ModelAndView("fixed/viewFixedPlan","map",map);
    }
    
    private List<HighFundInvPlanDtlVo> getListHighFundInvPlanDtl(CmFixedIntention info){
    	List<HighFundInvPlanDtlVo> list = new ArrayList<HighFundInvPlanDtlVo>();
    	QueryHighFundInvPlanDtlRequest req =  new QueryHighFundInvPlanDtlRequest();
		req.setPlanId(info.getId().toPlainString());
		req.setHbOneNo(info.getHboneno());
		//中台必填字段，但是中台搜索条件用不到下面两个字段，所以写死下面两个字段的值
		req.setDisCode(StaticVar.HB_DISCODE);
		req.setOutletCode("W20170215");
		req.setOperIp("127.0.0.1");
		req.setTxChannel("1");
		req.setDataTrack(DateTimeUtil.getCurDateTime()+"12");
		logger.info("根据意向单号查询定投计划传参:"+ JSON.toJSONString(req));
		QueryHighFundInvPlanDtlResponse res = queryHighFundInvPlanDtlFacade.execute(req);
		logger.info("根据意向单号查询定投计划出参:"+ JSON.toJSONString(res));
		if(res != null && res.getHighFundInvPlanDtlVoList() != null){
			list = res.getHighFundInvPlanDtlVoList();
		}
		return list;
    }
    
    /**
     * 定投终止-->新增 页面
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/cancelFixedPlan.do")
    public ModelAndView cancelFixedPlan(HttpServletRequest request){
		Map<String,Object> returnMap= Maps.newHashMap();
		returnMap.put("selectedBusiType",CounterBusiEnum.AIP_TERMINATE.getKey());


		String id = request.getParameter("id");
		ConstantCache constantCache = ConstantCache.getInstance();

		//意向单 信息
		CmFixedIntention info = new CmFixedIntention();
		Map<String,Object> parampaln = new HashMap<String,Object> (1);
		parampaln.put("planid", id);
		info = cmFixedIntentionService.getCmFixedIntention(parampaln);
		info.setPaytypeval(constantCache.getVal("fixedpaytype", info.getPaytype()));
		info.setPlanrateval(constantCache.getVal("fixedPlanRate", info.getPlanrate()));

		returnMap.put("info",info);
		returnMap.put("custNo",info.getConscustno());
		returnMap.put("pCode",info.getFundcode());

		return new ModelAndView("fixed/cancelFixedPlan",returnMap);

    }
	
    /**
     * 终止定投意向单
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/cancelFixedIntention.do", method = RequestMethod.POST)
    public BaseResponse<String> cancelFixedIntention(HttpServletRequest request){

		String id = request.getParameter("id");// 意向单Id
		if(StringUtil.isEmpty(id)){
			return BaseResponse.fail("意向单Id不能为空！");
		}

		Map<String,Object> param = new HashMap<String,Object> (1);
		param.put("planid", id);
		CmFixedIntention fixedInfo = cmFixedIntentionService.getCmFixedIntention(param);
        if(fixedInfo==null){
			return BaseResponse.fail(String.format("意向单Id：%s 无法获取意向单信息！",id));
		}

        String operatorNo=getLoginUserId(request);

		SaveCmCounterOrderRequest orderRequest=new SaveCmCounterOrderRequest();
		orderRequest.setBdId(request.getParameter("bdid"));
		orderRequest.setConscustNo(fixedInfo.getConscustno());
		orderRequest.setPCode(fixedInfo.getFundcode());
		orderRequest.setForId(fixedInfo.getId().toPlainString());
		orderRequest.setOperatorNo(operatorNo);


		MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		List<MultipartFile> files = multipartRequest.getFiles("filelist[]");
		//文件处理
		Map<String, List<String>>  fileMap=processCounterFile (files);
		orderRequest.setAddFileMap(fileMap);
		BaseResponse<String> counterResp= getPostEntityByJsonObject(CrmTradeServerPathConstant.INSERT_COUNTER_ORDER,
				orderRequest,
				new ParameterizedTypeReference<BaseResponse<String>>(){});

		if(counterResp.isSuccess()){
			//查询定投计划里的日期
			List<HighFundInvPlanDtlVo> list = getListHighFundInvPlanDtl(fixedInfo);
			long count= list.stream().filter(vo->vo.getPaymentDate().equals(DateTimeUtil.getCurDate())).count();
			if(count>0){
				counterResp.setReturnMsg("当天为扣款日，定投终止将在下期定投计划生效！");
			}else{
				counterResp.setReturnMsg("上传成功！");
			}
		}
		return  counterResp;
    }

    /**
     * 生成定投计划
     * @param request
    * <AUTHOR>
    * @date 2022/08/04
    */
    @RequestMapping("/createFixedPlan.do")
    @ResponseBody
    public String createFixedPlan(HttpServletRequest request){
        String planid = request.getParameter("id");
        User userlogin = (User)request.getSession().getAttribute("loginUser");
        return cmFixedIntentionService.createFixedPlan(planid,userlogin.getUserId());
    }

}
