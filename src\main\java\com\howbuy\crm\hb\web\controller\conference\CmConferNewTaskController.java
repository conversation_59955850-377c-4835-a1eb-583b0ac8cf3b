package com.howbuy.crm.hb.web.controller.conference;

import com.howbuy.crm.hb.domain.conference.*;
import com.howbuy.crm.hb.service.conference.CmConferNewcustTaskDetailService;
import com.howbuy.crm.hb.service.conference.CmConferNewcustTaskService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.trade.common.response.BaseResponse;
import crm.howbuy.base.db.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * @description:(扫码参会新课分配任务表)
 * @author: xufanchao
 * @date: 2023/11/13 18:18
 * @since JDK 1.8
 */
@Slf4j
@Controller
@RequestMapping(value = "/cmconfertask")
public class CmConferNewTaskController {

	@Autowired
	private CmConferNewcustTaskService cmConferNewcustTaskService;

	@Autowired
	private CmConferNewcustTaskDetailService cmConferNewcustTaskDetailService;


	/**
	 * @api {GET} /cmconfertask/cmconfernewcusttask 获取会议新客户任务页面
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferNewTaskController
	 * @apiName cmconferNewCustTask()
	 * @apiDescription 获取会议新客户任务页面
	 * @apiSuccess (响应结果) {Object} view 页面属性
	 * @apiSuccessExample 响应结果示例
	 * {"view":{}}
	 */
	@GetMapping(value = "/cmconfernewcusttask")
	public ModelAndView cmconferNewCustTask(HttpServletRequest request) {
		User userlogin = (User)request.getSession().getAttribute("loginUser");
		String userId = userlogin.getUserName();
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.addObject("userId", userId);
		modelAndView.setViewName("/conference/cmconferNewCustTask");
		return modelAndView;
	}


	/**
	 * @api {POST} /cmconfertask/listcmconfernewtask 查询新客分配数据
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferNewTaskController
	 * @apiName listCmConferNewCustTask()
	 * @apiDescription 查询新客分配数据
	 * @apiParam (请求参数) {String} conferenceId 会议ID
	 * @apiParam (请求参数) {String} mobileDigest 手机号摘要
	 * @apiParam (请求参数) {String} applyer 申请人
	 * @apiParam (请求参数) {String} currentStatus 当前状态
	 * @apiParam (请求参数) {String} page 页数
	 * @apiParam (请求参数) {String} rows 行数
	 * @apiParamExample 请求参数示例
	 * applyer=fdKjKMYN2X&conferenceId=qwMjfznSEg&currentStatus=PLcM&mobileDigest=5D0lqG2&page=PQLiWsJfnB&rows=6D0n
	 * @apiSuccess (响应结果) {Array} listData
	 * @apiSuccess (响应结果) {String} listData.id 表单id
	 * @apiSuccess (响应结果) {String} listData.applyer 申请人
	 * @apiSuccess (响应结果) {String} listData.appDt 申请时间
	 * @apiSuccess (响应结果) {String} listData.currentStatus 当前状态
	 * @apiSuccess (响应结果) {String} listData.remark 备注
	 * @apiSuccess (响应结果) {Object} pageBean
	 * @apiSuccess (响应结果) {Number} pageBean.totalNum 总条数
	 * @apiSuccess (响应结果) {Number} pageBean.pageSize 每页条数
	 * @apiSuccess (响应结果) {Number} pageBean.curPage 当前页
	 * @apiSuccess (响应结果) {Number} pageBean.totalPage 总页数
	 * @apiSuccessExample 响应结果示例
	 * {"listData":[{"applyer":"B4Gm","currentStatus":"QOawS2","appDt":"Xcj2k","remark":"o","id":"S"}],"pageBean":{"curPage":3743,"totalNum":7307,"totalPage":3695,"pageSize":9382}}
	 */
	@PostMapping(value = "/listcmconfernewtask")
	@ResponseBody
	public PageResult<CmconferNewCustTaskDTO> listCmConferNewCustTask(CmconferNewCustTaskVO cmconferNewCustTaskVO) {
		return cmConferNewcustTaskService.listCmConferNewCustByPage(cmconferNewCustTaskVO);
	}


	/**
	 * @api {POST} /cmconfertask/applydistribute 申请进行客户分配接口
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferNewTaskController
	 * @apiName applydistribute()
	 * @apiDescription 申请进行客户分配接口
	 * @apiParam (请求体) {Array} requestBody
	 * @apiParam (请求体) {String} requestBody.id 主键
	 * @apiParam (请求体) {String} requestBody.conferenceId 会议主键(外键)
	 * @apiParam (请求体) {String} requestBody.conferenceName 会议名称
	 * @apiParam (请求体) {String} requestBody.mobile 手机号
	 * @apiParam (请求体) {String} requestBody.mobileMask
	 * @apiParam (请求体) {String} requestBody.mobileDigest
	 * @apiParam (请求体) {String} requestBody.mobileCipher
	 * @apiParam (请求体) {String} requestBody.credt 记录创建日期
	 * @apiParam (请求体) {String} requestBody.creator 创建人
	 * @apiParam (请求体) {String} requestBody.conscustno
	 * @apiParam (请求体) {String} requestBody.custname
	 * @apiParam (请求体) {String} requestBody.consname
	 * @apiParam (请求体) {String} requestBody.conscode
	 * @apiParam (请求体) {String} requestBody.isconspre 是否投顾预约参会
	 * @apiParam (请求体) {Number} requestBody.appointmentsnub 预约参会人数
	 * @apiParam (请求体) {String} requestBody.uporgname 所属区域
	 * @apiParam (请求体) {String} requestBody.orgname 所属部门
	 * @apiParam (请求体) {String} requestBody.custnamesr 客户姓名(输入)
	 * @apiParam (请求体) {String} requestBody.consnamesr 投顾姓名(输入)
	 * @apiParam (请求体) {Number} requestBody.meetingnumber 实际人数(输入)
	 * @apiParam (请求体) {String} requestBody.scanCustState 扫码参会时，手机号码判断客户信息状态：     0-不存在客户 1-存在唯一客户 2-存在多个客户
	 * @apiParam (请求体) {String} requestBody.custState 实时手机号码判断客户信息状态：     0-不存在客户 1-存在唯一客户 2-存在多个客户
	 * @apiParamExample 请求体示例
	 * [{"appointmentsnub":3876,"creator":"8XT4tuQ2","conferenceName":"2","consnamesr":"O2","isconspre":"5TTzn3Z25D","mobile":"6WmQl","meetingnumber":1180,"mobileCipher":"M","credt":"xdSBcv","uporgname":"fMzg3","conscustno":"MRvHt8akR","orgname":"BMgEaYC","conferenceId":"HM20W","mobileDigest":"Xm93KKDiEH","consname":"PhojNXN","custState":"2wLzsPr","id":"drelP3m","custnamesr":"ceFJkmj0s","custname":"q","conscode":"YyMZ5Iu","mobileMask":"GYfQXl52o8","scanCustState":"TQrhzP"}]
	 * @apiSuccess (响应结果) {Boolean} success 是否成功
	 * @apiSuccess (响应结果) {String} data  具体返回参数
	 * @apiSuccess (响应结果) {String} returnCode 返回码
	 * @apiSuccess (响应结果) {String} returnMsg 返回信息
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"yRow","returnMsg":"R","data":"gu","success":true}
	 */
	@PostMapping(value = "/applydistribute")
	@ResponseBody
	public BaseResponse<String> applydistribute(@RequestBody List<CmConferenceScan> cmConferenceScanList, HttpServletRequest request) {
		User userlogin = (User)request.getSession().getAttribute("loginUser");
		String userId = userlogin.getUserId();
		cmConferNewcustTaskService.applyDistrubute(cmConferenceScanList, userId);
		return BaseResponse.ok();
	}


	/**
	 * @api {POST} /cmconfertask/cancelcmconfernewcusttask 作废当前分配单
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferNewTaskController
	 * @apiName cancelCmConferNewCustTask()
	 * @apiDescription 作废当前分配单
	 * @apiParam (请求参数) {String} conferenceId 会议ID
	 * @apiParam (请求参数) {String} mobileDigest 手机号摘要
	 * @apiParam (请求参数) {String} applyer 申请人
	 * @apiParam (请求参数) {String} currentStatus 当前状态
	 * @apiParam (请求参数) {Number} page 第几页
	 * @apiParam (请求参数) {Number} rows 每页显示多少条记录
	 * @apiParamExample 请求参数示例
	 * applyer=kjhkzS&conferenceId=Geh0WTSy&currentStatus=1mhKzNX&mobileDigest=7Tp&page=1880&rows=9518
	 * @apiSuccess (响应结果) {Boolean} success 是否成功
	 * @apiSuccess (响应结果) {String} data  具体返回参数
	 * @apiSuccess (响应结果) {String} returnCode 返回码
	 * @apiSuccess (响应结果) {String} returnMsg 返回信息
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"Uk2tzkC3","returnMsg":"I6Bf","data":"0qa","success":true}
	 */
	@RequestMapping(value = "/cancelcmconfernewcusttask", method = RequestMethod.POST)
	@ResponseBody
	public BaseResponse<String> cancelCmConferNewCustTask(CmconferNewCustTaskVO cmconferNewCustTaskVO, HttpServletRequest request) {
		User userlogin = (User)request.getSession().getAttribute("loginUser");
		String userId = userlogin.getUserId();
		try {
			cmConferNewcustTaskService.updateCmConferNewCustTask(cmconferNewCustTaskVO, userId);
			return BaseResponse.ok();
		} catch (Exception e) {
			log.error("error in cancelcmconfernewcusttask", e);
			return BaseResponse.fail("请注意，作废操作失败");
		}
	}


	/**
	 * @api {POST} /cmconfertask/audit audit()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferNewTaskController
	 * @apiName audit()
	 * @apiDescription 审核接口
	 * @apiParam (请求参数) {String} id 主键id
	 * @apiParam (请求参数) {String} conferenceId 会议ID
	 * @apiParam (请求参数) {String} mobileDigest 手机号摘要
	 * @apiParam (请求参数) {String} applyer 申请人
	 * @apiParam (请求参数) {String} currentStatus 当前状态
	 * @apiParam (请求参数) {String} remark 审核意见
	 * @apiParam (请求参数) {Number} page 第几页
	 * @apiParam (请求参数) {Number} rows 每页显示多少条记录
	 * @apiParamExample 请求参数示例
	 * applyer=O1HVwtQ6&conferenceId=9mfjgPMx&currentStatus=EqM6a3&mobileDigest=yo0oJqsco&remark=guU0AKF&id=C&page=8695&rows=3599
	 * @apiSuccess (响应结果) {Boolean} success 是否成功
	 * @apiSuccess (响应结果) {String} data 具体返回参数
	 * @apiSuccess (响应结果) {String} returnCode 返回码
	 * @apiSuccess (响应结果) {String} returnMsg 返回信息
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"HvLp","returnMsg":"nvShnv","data":"x2","success":true}
	 */
	@RequestMapping(value = "/audit", method = RequestMethod.POST)
	@ResponseBody
	public BaseResponse<String> audit(CmconferNewCustTaskVO cmconferNewCustTaskVO, HttpServletRequest request) {
		User userlogin = (User)request.getSession().getAttribute("loginUser");
		String userId = userlogin.getUserId();
		try {
			cmConferNewcustTaskService.updateCmConferNewCustTask(cmconferNewCustTaskVO, userId);
			return BaseResponse.ok();
		} catch (Exception e) {
			log.error("error in cancelcmconfernewcusttask", e);
			return BaseResponse.fail("请注意，作废操作失败");
		}
	}


	/**
	 * @api {GET} /cmconfertask/toaudit 跳转到分配的处理页面
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferNewTaskController
	 * @apiName toAudit()
	 * @apiDescription 跳转到分配的处理页面
	 * @apiSuccess (响应结果) {Object} view 页面地址
	 * @apiSuccess {String} id 扫码参会数据的主键ID
	 * @apiSuccess {String} type 扫码参会操作的类型
	 * @apiSuccessExample 响应结果示例
	 * {"view":{}}
	 */
	@GetMapping("/toaudit")
	public ModelAndView toAudit(HttpServletRequest request) {
		// 扫码参会客户主键
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/conference/dealcmconferTaskDetail");
		modelAndView.addObject("id", request.getParameter("id"));
		modelAndView.addObject("type", request.getParameter("type"));
		// 获取当前分配单的审核意见
		String id = request.getParameter("id");
		modelAndView.addObject("remark", cmConferNewcustTaskService.getCmConferNewCustTaskRemark(id));
		return modelAndView;
	}

	/**
	 * @api {GET} /cmconfertask/listcmconfertaskdetail listCmconferTaskDetail()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferNewTaskController
	 * @apiName listCmconferTaskDetail()
	 * @apiDescription 获取审核和修改时的数据
	 * @apiSuccess (响应结果) {Array} response
	 * @apiSuccess (响应结果) {String} response.id 主键
	 * @apiSuccess (响应结果) {String} response.conferenceId 会议ID CM_CONFERENCE.ID
	 * @apiSuccess (响应结果) {String} response.conferenceName 会议名称
	 * @apiSuccess (响应结果) {String} response.mobile 参会手机号
	 * @apiSuccess (响应结果) {String} response.mobileDigest 手机号摘要
	 * @apiSuccess (响应结果) {String} response.signDt 签到时间
	 * @apiSuccess (响应结果) {String} response.custNamesr 客户姓名(输入)
	 * @apiSuccess (响应结果) {String} response.consNamesr 投顾姓名(输入)
	 * @apiSuccessExample 响应结果示例
	 * [{"conferenceName":"ybdAYYQO","conferenceId":"bYQ8ay5W","custNamesr":"AdGH","mobileDigest":"q","mobile":"v8oZ","signDt":"1KyOlW","id":"Ig0","consNamesr":"O38"}]
	 */
	@GetMapping("/listcmconfertaskdetail")
	@ResponseBody
	public List<CmconferNewCustTaskDetailDTO> listCmconferTaskDetail(HttpServletRequest request) {
		// 获取当前的列表的id
		String id = request.getParameter("id");
		// 获取所有的分配数据
		return cmConferNewcustTaskDetailService.listCmConferNewCust(id);
	}


	/**
	 * @api {POST} /cmconfertask/updatecusttaskdetail updateCustTaskDetail()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferNewTaskController
	 * @apiName updateCustTaskDetail()
	 * @apiDescription 修改操作
	 * @apiParam (请求体) {Array} requestBody
	 * @apiParam (请求体) {String} requestBody.id 主键
	 * @apiParam (请求体) {String} requestBody.taskId 申请单ID
	 * @apiParam (请求体) {String} requestBody.conferenceId 会议ID CM_CONFERENCE.ID
	 * @apiParam (请求体) {String} requestBody.conferenceName 会议名称
	 * @apiParam (请求体) {String} requestBody.mobile 参会手机号
	 * @apiParam (请求体) {String} requestBody.mobileDigest 手机号摘要
	 * @apiParam (请求体) {String} requestBody.signDt 签到时间
	 * @apiParam (请求体) {String} requestBody.custNamesr 客户姓名(输入)
	 * @apiParam (请求体) {String} requestBody.consNamesr 投顾姓名(输入)
	 * @apiParam (请求体) {String} requestBody.dealStatus 处理状态
	 * @apiParam (请求体) {String} requestBody.dealRemark 处理意见
	 * @apiParamExample 请求体示例
	 * [{"conferenceName":"YZ","conferenceId":"RXM","custNamesr":"t","mobileDigest":"egyvu28W","mobile":"OYfpI8","signDt":"rlC","id":"LBwnhac","dealStatus":"1I","taskId":"C3VF1yp4","dealRemark":"zS","consNamesr":"m6eCc"}]
	 * @apiSuccess (响应结果) {Boolean} success 是否成功
	 * @apiSuccess (响应结果) {String} data 具体返回参数
	 * @apiSuccess (响应结果) {String} returnCode 返回码
	 * @apiSuccess (响应结果) {String} returnMsg 返回信息
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"bQM0","returnMsg":"4kQTkC7MC","data":"yE4ZOw","success":false}
	 */
	@PostMapping("/updatecusttaskdetail")
	@ResponseBody
	public BaseResponse<String> updateCustTaskDetail(@RequestBody List<CmconferNewCustTaskDetailVO> cmconferNewCustTaskDetailVOS, HttpServletRequest request) {
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		String userId = userlogin.getUserId();
		try {
			cmConferNewcustTaskDetailService.updateCmConferNewCustTaskDetail(cmconferNewCustTaskDetailVOS, userId);
			return BaseResponse.ok();
		} catch (Exception e) {
			log.error("error in updatecusttaskdetail", e);
			return BaseResponse.fail("请注意，修改操作失败");
		}
	}

	/**
	 * @api {POST} /cmconfertask/passcusttaskdetail passCustTaskDetail()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferNewTaskController
	 * @apiName passCustTaskDetail()
	 * @apiDescription 处理申请分配单的详细数据
	 * @apiParam (请求体) {Array} requestBody
	 * @apiParam (请求体) {String} requestBody.id 主键
	 * @apiParam (请求体) {String} requestBody.taskId 申请单ID
	 * @apiParam (请求体) {String} requestBody.scanId 扫码表主键ID
	 * @apiParam (请求体) {String} requestBody.conferenceId 会议ID CM_CONFERENCE.ID
	 * @apiParam (请求体) {String} requestBody.conferenceName 会议名称
	 * @apiParam (请求体) {String} requestBody.mobile 参会手机号
	 * @apiParam (请求体) {String} requestBody.mobileDigest 手机号摘要
	 * @apiParam (请求体) {String} requestBody.signDt 签到时间
	 * @apiParam (请求体) {String} requestBody.custNamesr 客户姓名(输入)
	 * @apiParam (请求体) {String} requestBody.consNamesr 投顾姓名(输入)
	 * @apiParam (请求体) {String} requestBody.dealStatus 处理状态
	 * @apiParam (请求体) {String} requestBody.dealRemark 处理意见
	 * @apiParam (请求体) {String} requestBody.isMatch
	 * @apiParamExample 请求体示例
	 * [{"conferenceName":"0JlpT","scanId":"X","mobile":"FZ","dealStatus":"mW","isMatch":"1nbZmJU","conferenceId":"kwhpb0Tje0","custNamesr":"sqJiwY9","mobileDigest":"zbvnV","signDt":"LLA","id":"87V","taskId":"v","dealRemark":"9f9fHcfc","consNamesr":"Hm"}]
	 * @apiSuccess (响应结果) {Boolean} success 是否成功
	 * @apiSuccess (响应结果) {String} data 具体返回参数
	 * @apiSuccess (响应结果) {String} returnCode 返回码
	 * @apiSuccess (响应结果) {String} returnMsg 返回消息
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"leXWhvTYPp","returnMsg":"f1XkU2jFE","data":"r5e","success":true}
	 */
	@PostMapping("/passcusttaskdetail")
	@ResponseBody
	public BaseResponse<String> passCustTaskDetail(@RequestBody List<CmconferNewCustTaskDetailVO> cmconferNewCustTaskDetailVOS, HttpServletRequest request) {
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		String userId = userlogin.getUserId();
		try {
			cmConferNewcustTaskDetailService.dealCmConferNewCustTaskDetail(cmconferNewCustTaskDetailVOS, userId);
			return BaseResponse.ok();
		} catch (Exception e) {
			log.error("error in updatecusttaskdetail", e);
			return BaseResponse.fail("请注意，修改操作失败");
		}
	}

}