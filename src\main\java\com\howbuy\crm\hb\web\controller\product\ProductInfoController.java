package com.howbuy.crm.hb.web.controller.product;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.hb.domain.product.ProductManagerInfo;
import com.howbuy.crm.hb.domain.prosale.Productinfo;
import com.howbuy.crm.hb.service.product.ProductManagerService;
import com.howbuy.crm.hb.service.prosale.ProductinfoService;
import com.howbuy.crm.hb.web.util.ParamUtil;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConstantCache;

import crm.howbuy.base.constants.ContextManager;
import crm.howbuy.base.db.CommPageBean;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.DateTimeUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import java.util.*;

@Controller
@RequestMapping(value = "/proManager")
public class ProductInfoController {

	@Autowired
	private ProductManagerService managerService;
	
	@Autowired
    private ProductinfoService productinfoService;

	@Autowired
	private JjxxInfoService jjxxInfoService;

	@RequestMapping(value="/toProInfoListView.do")
	public ModelAndView toProductInfoList(HttpServletRequest request){
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/product/productInfoList");
		//查看详情权限
		String opDetail = null;
		//修改操作权限
		String opUp = null;
    	String  menucode = "080108";
    	List<String> userroles = (List<String>) request.getSession().getAttribute("loginRoles");
		for(String role : userroles){
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menucode);
			if(temp != null && temp.contains("1")){
				opDetail = "true";
			}
			if(temp != null && temp.contains("2")){
				opUp = "true";
			}
		}
		
		modelAndView.addObject("opDetail", opDetail);
		modelAndView.addObject("opUp", opUp);

		return modelAndView;
	}

	@RequestMapping(value="/proInfoList.do")
	@ResponseBody
	public Map<String,Object> productInfoList(HttpServletRequest request) throws Exception{
		Map<String,Object> resultMap = new HashMap<String, Object>();
		Map<String,String> paramMap = new HashMap<String, String>();
		paramMap = new ParamUtil(request).getParamMap();

		// 判断常量表中合规标识：true启用，false停用
		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
		boolean roleCPFlag = false;
		if (cacheMap != null && !cacheMap.isEmpty()) {
			roleCPFlag = JSON.toJSONString(cacheMap).contains("true");
		}

		// 判断登录人员的角色中是否包括“合规人员”角色
		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
		if (roleCPFlag || loginRoles.contains("ROLE_CP")) {
			paramMap.put("hascp", "true");
		}
		PageData<ProductManagerInfo> productInfoList = managerService.getProductInfoList(paramMap);
		List<ProductManagerInfo> listData = productInfoList.getListData();
		ConstantCache instance = ConstantCache.getInstance();
		LinkedHashMap<String, String> hbtypeMap = instance.getConstantKeyVal("hmcpxs");
		LinkedHashMap<String, String> saleStateMap = instance.getConstantKeyVal("xszts");
		LinkedHashMap<String, String> proStateMap = instance.getConstantKeyVal("cpzts");
		LinkedHashMap<String, String> preStateMap = instance.getConstantKeyVal("yyzts");
		for (ProductManagerInfo info : listData) {
			JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(info.getPcode(), false);
			info.setSfmsjg( jjxxInfo == null ? "" : jjxxInfo.getSfmsjg());
			String hbtype = info.getHbtype();
			String saleState = info.getSaleState();
			String proState = info.getProductState();
			String preState = info.getPrebookState();
			if(StringUtils.isNotBlank(hbtype)){
				info.setHbtype(hbtypeMap.get(hbtype));
			}

			if(StringUtils.isNotBlank(saleState)){
				info.setSaleState(saleStateMap.get(saleState));
			}

			if(StringUtils.isNotBlank(proState)){
				info.setProductState(proStateMap.get(proState));
			}

			if(StringUtils.isNotBlank(preState)){
				info.setPrebookState(preStateMap.get(preState));
			}
			
			// 转义发行方式字段
            if (StringUtils.isNotBlank(info.getPublishWay()) && ContextManager.FXFSS_TYPE.containsKey(info.getPublishWay())) {
            	info.setPublishWay(ContextManager.FXFSS_TYPE.get(info.getPublishWay()));
            } else {
            	info.setPublishWay("");
            }
		}
		CommPageBean pageBean = productInfoList.getPageBean();
		resultMap.put("rows", listData);
		resultMap.put("total", pageBean.getTotalNum());
		return resultMap;
	}

	@RequestMapping(value="/proInfoDetail.do")
	public ModelAndView proInfoDetail( String pcode){
		Map<String,String> paramMap = new HashMap<String, String>();
		paramMap.put("pcode", pcode);
		ProductManagerInfo proDetail = managerService.getProductInfoDetail(paramMap);

		ModelAndView model = new ModelAndView("product/productInfoDetail", "info", proDetail);
		return model;
	}

	@RequestMapping(value="/toProInfoEditor.do")
	public ModelAndView toProInfoEditor( String pcode){
		Map<String,String> paramMap = new HashMap<String, String>();
		paramMap.put("pcode", pcode);
		//ProductManagerInfo proDetail = managerService.getProductInfoDetail(paramMap);
		Productinfo productinfo = productinfoService.getProductinfoDetail(paramMap);

		ModelAndView model = new ModelAndView("product/productInfoDetailEditor", "info", productinfo);
		return model;
	}

	@ResponseBody
	@RequestMapping(value="/updProInfoEditor.do")
	public String updProInfoEditor(ProductManagerInfo info,HttpServletRequest request){
		HttpSession session = request.getSession();
		String userId=(String) session.getAttribute("userId");
		info.setModifier(userId);
		info.setModdt(DateTimeUtil.fmtDate(new Date(), "yyyyMMdd"));
		String result = managerService.updProInfoEditor(info);
		return result;
	}

}
