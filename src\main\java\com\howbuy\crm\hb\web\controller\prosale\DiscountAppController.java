package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.base.CurrencyEnum;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.base.YesOrNoEnum;
import com.howbuy.crm.base.discount.DisCountInterestLevelEnum;
import com.howbuy.crm.base.discount.DisCountInterestTypeEnum;
import com.howbuy.crm.base.trade.FeeRateMethodEnum;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.fixed.CmFixedIntention;
import com.howbuy.crm.hb.domain.prosale.Prebookproductinfo;
import com.howbuy.crm.hb.service.callout.VconscustService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.fixed.CmFixedIntentionService;
import com.howbuy.crm.hb.service.prosale.DiscountappService;
import com.howbuy.crm.hb.service.prosale.ManyCallPreInfoService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.service.system.HbUserroleService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.hb.web.vo.PreBookDealDisCountViewVo;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prebook.dto.CmDiscountApp;
import com.howbuy.crm.prebook.dto.CmPreBookProductInfo;
import com.howbuy.crm.prebook.request.DisCountInterestListRequest;
import com.howbuy.crm.prebook.request.DisCountInterestRequest;
import com.howbuy.crm.prebook.response.DisCountInterestListVO;
import com.howbuy.crm.prebook.response.DisCountInterestVO;
import com.howbuy.crm.prebook.service.PreDisCountExtendService;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.prebook.service.PrebookConfigService;
import com.howbuy.crm.prebook.service.PrebookDisCountService;
import com.howbuy.crm.prebook.vo.InsertDiscountAppVo;
import com.howbuy.crm.prebook.vo.PreBookDealDiscountVo;
import com.howbuy.crm.prebook.vo.UpdateDiscountAppVo;
import com.howbuy.crm.prosale.dto.CmDiscountLog;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import com.howbuy.crm.prosale.dto.Discountapp;
import com.howbuy.crm.prosale.request.GetPrebookInfoByIdRequest;
import com.howbuy.crm.prosale.response.GetPrebookByIdResponse;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import com.howbuy.crm.util.exception.BusinessException;
import com.howbuy.simu.dto.business.product.RmbhlzjjDto;
import com.howbuy.simu.service.business.product.ComprehensiveService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.CalculateUtil;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2019/8/23 14:50
 */
@Slf4j
@Controller
@RequestMapping("/prosale")
public class DiscountAppController  extends BaseController {

    public static final String AUTH_OPER_CODE = "99";
    @Autowired
    private ConscustService conscustService;
    @Autowired
    private ComprehensiveService comprehensiveService;

    @Autowired
    private QueryPreBookService queryPreBookService;

    @Autowired
    private DiscountappService discountappService;
    @Autowired
    private PrebookproductinfoService prebookproductinfoService;
    @Autowired
    private VconscustService vconscustService;
    @Autowired
    private HbUserroleService hbUserroleService;
    @Autowired
	private CmFixedIntentionService cmFixedIntentionService;
    @Autowired
    private JjxxInfoService jjxxInfoService;
    @Autowired
    private ManyCallPreInfoService manyCallPreInfoService;
    @Autowired
    private PrebookDisCountService prebookDisCountService;

    @Autowired
    private PrebookBasicInfoService prebookBasicInfoService;

    @Autowired
    private PrebookConfigService prebookConfigService;

    @Autowired
    private PreDisCountExtendService preDisCountExtendService;

    private static final String ROLE_HK_CP = "ROLE_HK_CP";

    /**
     * 默认的费率
     */
    private static final BigDecimal DEFAULT_FEE_RATE = new BigDecimal("0.01");



    /**
     * 填充折扣处理VO
     * @param preBookInfo 预约信息
     * @param menucode 菜单代码
     * @param optcode 操作代码
     * @param isEdit 是否是编辑模式
     * @return PreBookDealDisCountVo
     */
    private PreBookDealDisCountViewVo populateDiscountVo(CmPreBookProductInfo preBookInfo, String menucode, String optcode, boolean isEdit) {
        PreBookDealDisCountViewVo pageViewVo = new PreBookDealDisCountViewVo();

        ReturnMessageDto<PreBookDealDiscountVo>  discountVoResp =prebookDisCountService.getDealDiscountVo(preBookInfo.getId());
        PreBookDealDiscountVo discountVo = discountVoResp.getReturnObject();

        //保留代码 ：
        pageViewVo.setMenucode(menucode);
        pageViewVo.setOptcode(optcode);
        // 客户来源
        Conscust cust = conscustService.getConscust(preBookInfo.getConscustno());
        pageViewVo.setSource(cust.getNewsourcename());
        pageViewVo.setSubSource(cust.getNewsubsourcename());

        // 权限信息
        Boolean hasAuthOverDt = checkUserOperateAuth(AUTH_OPER_CODE, menucode);
        pageViewVo.setHasAuthOverDt(Boolean.TRUE.equals(hasAuthOverDt) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());

        // 员工关系
        ConstantCache constantCache = ConstantCache.getInstance();
        Map<String,String> staffRelationsMap = constantCache.getConstantKeyVal("staffrelations");
        pageViewVo.setStaffRelations(buildSelectMap(staffRelationsMap, null));

        //格式化代码
        BigDecimal subscribeAmt = discountVo.getSubscribeAmt();
        pageViewVo.setTotalamt(subscribeAmt==null?null:subscribeAmt.divide(BIG_DECIMAL_1W, 2, BigDecimal.ROUND_HALF_UP));
        pageViewVo.setCurrencytype(discountVo.getCurrency());
        pageViewVo.setCurrency(CurrencyEnum.getDescription(discountVo.getCurrency()));
        BigDecimal buyamt = discountVo.getBuyAmt();
        pageViewVo.setBuyamt(buyamt==null?null:buyamt.divide(BIG_DECIMAL_1W, 6, BigDecimal.ROUND_HALF_UP));

        // 基本信息
        pageViewVo.setPrebookid(discountVo.getPreId().toString());
        pageViewVo.setExpecttradedt(discountVo.getExpectTradeDt());
        pageViewVo.setSpecTradeType(discountVo.getSpecTradeType());
        
        // 产品信息
        pageViewVo.setPname(discountVo.getProdName());
        // 分次call信息
        pageViewVo.setIsfccl(discountVo.getIsFccl());
        
        // 客户信息
        pageViewVo.setConscustname(discountVo.getConsCustName());
        pageViewVo.setConscustno(discountVo.getConsCustNo());
        
        // 金额信息
        //exchangerate.setScale(4, BigDecimal.ROUND_HALF_UP).toPlainString());
        pageViewVo.setExchangerate(discountVo.getExchangeRate());
        // 外部订单信息
        pageViewVo.setDealno(discountVo.getDealNo());
        // 交易次数
        pageViewVo.setTradeCount(discountVo.getTradeCount());
        // 折扣权限
        /**
         * 是否允许申请折扣判断
         * success() true-标识 可以申请折扣 。false-不允许申请折扣
         * returnMsg标识 提示信息
         */
        pageViewVo.setCanDiscountResp(discountVo.getCanDiscountResp());
        // 手续费相关
        pageViewVo.setFeeRateMethodDesc(FeeRateMethodEnum.getDescription(discountVo.getFeeRateMethod()));
        pageViewVo.setFeeRateMethod(discountVo.getFeeRateMethod());
        pageViewVo.setFeeUsedType(discountVo.getFeeUsedType());
        
        pageViewVo.setCalculateAppAmt(discountVo.getCalculateAppAmt());
        pageViewVo.setFeeAmt(discountVo.getFeeAmt());

        JjxxInfo  jjxxInfo=jjxxInfoService.getJjxxByJjdm(preBookInfo.getPcode());
        pageViewVo.setFeeRate(getFeeRateFormatter(jjxxInfo, discountVo.getFeeRate()));
        // 当费率为0，算出的费用是空，而页面可选好买返回的方式计算，需要用默认的0.01作为费率，不然无法计算
        pageViewVo.setFeeAmt(getDefaultFeeAmt(pageViewVo.getCalculateAppAmt(), pageViewVo.getFeeAmt(), pageViewVo.getFeeRate()));


        ReturnMessageDto<String> canDiscountResp = discountVo.getCanDiscountResp();
        canDiscountResp =  canDiscountResp==null ? ReturnMessageDto.fail(""):canDiscountResp;
        pageViewVo.setCanApply(canDiscountResp.isSuccess());
        pageViewVo.setApplyAlertMsg(canDiscountResp.getReturnMsg());


        PreBookDealDiscountVo.DisCountInnerVo innerVo= discountVo.getDiscountInfo();
        DisCountInterestVO disCountInterestVO = discountVo.getInterestVo();
        
        // 如果是编辑模式，填充折扣相关信息
        if (isEdit) {
            if(innerVo != null) {
                fillDiscountToView(pageViewVo, innerVo);
            }
            if(disCountInterestVO != null){
                fillInterestToView(pageViewVo, disCountInterestVO);
            }
        }else {
            // 填充客户权益信息
            fillInterestInfo(preBookInfo.getId(), pageViewVo);
        }


        pageViewVo.setArchType(preBookInfo.getArchType());

        return pageViewVo;
    }

    /**
     * @description 当费率为0，算出的费用是空，而页面可选好买返回的方式计算，需要用默认的0.01作为费率，不然无法计算
     * @param calculateAppAmt  计算金额
     * @param feeAmt 费用
     * @param feeRate	费率
     * @return java.math.BigDecimal
     * @author: jianjian.yang
     * @date: 2025/6/30 15:30
     * @since JDK 1.8
     */
    private BigDecimal getDefaultFeeAmt(BigDecimal calculateAppAmt, BigDecimal feeAmt, BigDecimal feeRate){
        log.info("getDefaultFeeAmt calculateAppAmt:{},feeAmt:{},feeRate:{}", calculateAppAmt, feeAmt, feeRate);
        if(feeRate != null
                && feeRate.compareTo(BigDecimal.ZERO) == 0
                && (feeAmt == null || feeAmt.compareTo(BigDecimal.ZERO) == 0)
                && calculateAppAmt != null){
            return calculateAppAmt.multiply(DEFAULT_FEE_RATE);
        }
        return feeAmt;
    }

    /**
     * @description 填充客户权益信息
     * @param preId	预约id
     * @param pageViewVo  页面VO
     * @return void
     * @author: jianjian.yang
     * @date: 2025/6/24 16:21
     * @since JDK 1.8
     */
    private void fillInterestInfo(BigDecimal preId, PreBookDealDisCountViewVo pageViewVo){
        DisCountInterestRequest disCountInterestRequest = new DisCountInterestRequest();
        disCountInterestRequest.setPreId(preId);
        ReturnMessageDto<DisCountInterestVO> returnMessageDto = preDisCountExtendService.queryDiscountInterest(disCountInterestRequest);
        if(returnMessageDto.isSuccess() && returnMessageDto.getReturnObject() != null) {
            DisCountInterestVO disCountInterestVO = returnMessageDto.getReturnObject();
            fillInterestToView(pageViewVo, disCountInterestVO);
        }
    }

    /**
     * @description 填充客户权益信息
     * @param pageViewVo
     * @param disCountInterestVO
     * @return void
     * @author: jianjian.yang
     * @date: 2025/6/26 9:49
     * @since JDK 1.8
     */
    private void fillInterestToView(PreBookDealDisCountViewVo pageViewVo, DisCountInterestVO disCountInterestVO) {
        pageViewVo.setInterestComment(disCountInterestVO.getInterestComment());
        pageViewVo.setInterestType(disCountInterestVO.getInterestType());
        pageViewVo.setInterestLevel(disCountInterestVO.getInterestLevel());
        pageViewVo.setInterestRate(disCountInterestVO.getInterestRate());
    }

    /**
     * @description:(转义为 页面对象)
     * @param vo  页面对象
     * @param innerVo 折扣的内部对象
     * @return com.howbuy.crm.hb.web.vo.PreBookDealDisCountViewVo.DisCountInnerViewVo
     * @author: haoran.zhang
     * @date: 2025/4/15 16:51
     * @since JDK 1.8
     */
    private static void fillDiscountToView(
            PreBookDealDisCountViewVo vo ,
            PreBookDealDiscountVo.DisCountInnerVo innerVo) {
        vo.setDiscountWay(innerVo.getDiscountWay());
        vo.setDiscountStyle(innerVo.getDiscountStyle());
        vo.setDiscountType(innerVo.getDiscountType());
        vo.setIsRefund(innerVo.getIsrefund());
        vo.setDiscountRate(innerVo.getDiscountRate());
        vo.setBeforeTaxAmt(innerVo.getBeforeTaxAmt());
        vo.setAfterTaxAmt(innerVo.getAfterTaxAmt());
        vo.setDiscountReason(innerVo.getDiscountReason());
        vo.setStaffRelation(innerVo.getStaffrelation());
        vo.setCalculateRate(innerVo.getCalculateRate());
        vo.setDiscountUseType(innerVo.getDiscountUseType());
        vo.setDiscountAmt(innerVo.getDiscountAmt());
    }

    @RequestMapping("/applyDiscount.do")
    public ModelAndView applyDiscount(HttpServletRequest request, Map map) {
        String id = request.getParameter("id");
        String menucode = request.getParameter("menuCode");
        String optcode = request.getParameter("optcode");
        if (StringUtil.isEmpty(id)) {
            throw new BusinessException("预约ID不能为空");
        }
        BigDecimal preId = new BigDecimal(id);
        CmPreBookProductInfo preBookInfo = prebookBasicInfoService.getPreBookById(preId);
        if (preBookInfo == null) {
            throw new BusinessException("预约信息不存在");
        }

        PreBookDealDisCountViewVo vo = populateDiscountVo(preBookInfo, menucode, optcode, false);
        
        return new ModelAndView("prosale/applyDiscount", transformToMap(vo));
    }


    /**
     * @description:(页面对象 转换为 Map)
     * @param vo
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * @author: haoran.zhang
     * @date: 2025/6/17 19:00
     * @since JDK 1.8
     */
   private Map<String, Object> transformToMap(PreBookDealDisCountViewVo vo) {
    Map<String, Object> map = new HashMap<>();

    map.put("menucode", vo.getMenucode());
    map.put("optcode", vo.getOptcode());
    map.put("source", vo.getSource());
    map.put("subSource", vo.getSubSource());
    map.put("hasAuthOverDt", vo.getHasAuthOverDt());
    map.put("staffRelations", vo.getStaffRelations());
    map.put("totalamt", vo.getTotalamt());
    map.put("currencytype", vo.getCurrencytype());
    map.put("currency", vo.getCurrency());
    map.put("buyamt", vo.getBuyamt());
    map.put("prebookid", vo.getPrebookid());
    map.put("expecttradedt", vo.getExpecttradedt());
    map.put("specTradeType", vo.getSpecTradeType());
    map.put("pname", vo.getPname());
    map.put("isfccl", vo.getIsfccl());
    map.put("conscustname", vo.getConscustname());
    map.put("conscustno", vo.getConscustno());
    map.put("exchangerate", vo.getExchangerate());
    map.put("dealno", vo.getDealno());
    map.put("tradeCount", vo.getTradeCount());
    map.put("canDiscountResp", vo.getCanDiscountResp());
    map.put("feeRateMethodDesc", vo.getFeeRateMethodDesc());
    map.put("feeRateMethod", vo.getFeeRateMethod());
    map.put("feeUsedType", vo.getFeeUsedType());
    map.put("calculateAppAmt", vo.getCalculateAppAmt());
    map.put("feeAmt", vo.getFeeAmt());
    map.put("feeRate", vo.getFeeRate());
    map.put("canApply", vo.isCanApply());
    map.put("applyAlertMsg", vo.getApplyAlertMsg());

    // 如果包含 discountInfo 内部对象，也可以嵌套处理
   map.put("discountWay", vo.getDiscountWay());
   map.put("discountStyle", vo.getDiscountStyle());
   map.put("discountType", vo.getDiscountType());
   map.put("isRefund", vo.getIsRefund());
   map.put("discountRate", vo.getDiscountRate());
   map.put("beforeTaxAmt", vo.getBeforeTaxAmt());
   map.put("afterTaxAmt", vo.getAfterTaxAmt());
   map.put("discountReason", vo.getDiscountReason());
   map.put("staffRelation", vo.getStaffRelation());
   map.put("calculateRate", vo.getCalculateRate());
   map.put("discountUseType", vo.getDiscountUseType());
   map.put("discountAmt", vo.getDiscountAmt());
   map.put("interestRate", vo.getInterestRate());
   map.put("interestLevel", vo.getInterestLevel());
   map.put("interestType", vo.getInterestType());
   map.put("interestComment", vo.getInterestComment());

   //预约的架构分类。 0-海外（中台）预约    1-高端（中台）代销  2-直销的预约
   map.put("archType",vo.getArchType());

    return map;
}


    @RequestMapping("/editDiscount.do")
    public String editDiscount(HttpServletRequest request, Map map) {
        String id = request.getParameter("id");
        map.put("endAudit", request.getParameter("endAudit"));

        if (StringUtil.isEmpty(id)) {
            throw new BusinessException("预约ID不能为空");
        }
        BigDecimal preId = new BigDecimal(id);
        CmPreBookProductInfo preBookInfo = prebookBasicInfoService.getPreBookById(preId);
        if (preBookInfo == null) {
            throw new BusinessException("预约信息不存在");
        }

        String menucode = request.getParameter("menuCode");
        String optcode = request.getParameter("optcode");
        
        PreBookDealDisCountViewVo vo = populateDiscountVo(preBookInfo, menucode, optcode, true);
        map.putAll(transformToMap(vo));
        
        return "prosale/editDiscount";
    }

    /**
     * 撤销折扣
     * @param request
    * <AUTHOR>
    * @date 2019/9/3
    */
    @RequestMapping("/cancelDiscount.do")
    @ResponseBody
    public String cancelDiscount(HttpServletRequest request){
        String preBookId = request.getParameter("id");
        String type = request.getParameter("optype");
        User userlogin = (User)request.getSession().getAttribute("loginUser");
        return discountappService.cancelDiscount(preBookId,userlogin.getUserId(), type);
    }

    /**
     * @api {GET} /prosale/saveDiscountApply.do saveDiscountApply()
     * @apiVersion 1.0.0
     * @apiGroup DiscountAppController
     * @apiName saveDiscountApply()
     * @apiDescription 保存折扣信息
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"o0Uy7GRB","returnMsg":"H2WFnlZ","returnObject":"AkLvvbP","returnList":["eXDy"]}
     */
    @ResponseBody
    @RequestMapping("/saveDiscountApply.do")
    public ReturnMessageDto<String> saveDiscountApply(HttpServletRequest request) {
        String prebookid = request.getParameter("id");
        String calculateAppAmt=request.getParameter("calculateAppAmt");
        String discountType = request.getParameter("discountType");
        String discountRate = request.getParameter("discountRate");
        String discountReason = request.getParameter("discountReason");
        String afterTaxAmt = request.getParameter("afterTaxAmt");
        String beforeTaxAmt = request.getParameter("beforeTaxAmt");
        String discountWay = request.getParameter("discountWay");
        String discountStyle = request.getParameter("discountStyle");
        String isRefund = request.getParameter("isRefund");
        String tradeCount = request.getParameter("tradeCount");
        String endAudit = request.getParameter("endAudit");
        String staffRelation = request.getParameter("staffRelation");

        String interestType = request.getParameter("interestType");
        String interestLevel = request.getParameter("interestLevel");
        String interestRate = request.getParameter("interestRate");

        if (StringUtil.isNullStr(discountRate) || StringUtil.isNullStr(afterTaxAmt) || StringUtil.isNullStr(beforeTaxAmt) || StringUtil.isNullStr(tradeCount)) {
            return ReturnMessageDto.fail("参数出现异常，添加失败！");
        }
        String conscustno = "";
        CmPrebookproductinfo info = null;
        List<String> userroles = new ArrayList<>();
        if (StringUtil.isNotNullStr(prebookid)) {
            GetPrebookInfoByIdRequest getPrebookInfoByIdRequest = new GetPrebookInfoByIdRequest();
            getPrebookInfoByIdRequest.setId(new BigDecimal(prebookid));
            GetPrebookByIdResponse preResponse = queryPreBookService.getPrebookById(getPrebookInfoByIdRequest);
            if(preResponse.isSuccessful()){
                info = preResponse.getPreinfo();
            }
        }
        if(null==info){
            //查询是否是定投管理的
        	Map<String,Object> paramfixed = new HashMap<String,Object>(1);
        	paramfixed.put("planid", prebookid);
        	CmFixedIntention fixedinfo = cmFixedIntentionService.getCmFixedIntention(paramfixed);
        	if(fixedinfo != null){
        		conscustno = fixedinfo.getConscustno();
        	}
        }else{
        	conscustno = info.getConscustno();
        }
        if(StringUtil.isNull(conscustno)){
            return ReturnMessageDto.fail("参数出现异常，添加失败！");
        }
        //获取所属投顾的角色
        String conscode = vconscustService.getConscodeFromConscustno(conscustno);
        userroles = hbUserroleService.getAllRoleCodeByConsCode(conscode);
        User userlogin = (User) request.getSession().getAttribute("loginUser");

        //正常折扣、活动竞赛，新增或修改后折扣状态为终审通过
        String defineState=StaticVar.DISCOUNT_NSTATES_HAS_APPLY;
        if(StaticVar.DISCOUNT_TYPE_NORMAL.equals(discountType) && !userroles.contains(StaticVar.ROLE_SIC_TEMP)){
            defineState=StaticVar.DISCOUNT_NSTATES_ZS_PASS;
        }

        //查询是否存在有效 折扣
        CmDiscountApp existApp=prebookDisCountService.getDisCountByPreId(Long.valueOf(prebookid));
        if (existApp!=null) {
            UpdateDiscountAppVo updateVo=new UpdateDiscountAppVo();
            updateVo.setPrebookid(Long.valueOf(prebookid));
            updateVo.setCalculateAppAmt(new BigDecimal(calculateAppAmt));
            updateVo.setDiscountType(discountType);
            updateVo.setDiscountRate(new BigDecimal(discountRate));
            updateVo.setDiscountReason(discountReason);
            updateVo.setAfterTaxAmt(new BigDecimal(afterTaxAmt));
            updateVo.setBeforeTaxAmt(new BigDecimal(beforeTaxAmt));
            updateVo.setBefTaxAmtAdjust(updateVo.getBeforeTaxAmt());
            updateVo.setDiscountWay(discountWay);
            updateVo.setStaffrelation(staffRelation);
            updateVo.setDiscountStyle(discountStyle);
            //正常折扣、活动竞赛，新增或修改后折扣状态为终审通过
            updateVo.setDiscountState(defineState);
            updateVo.setTradecount(Long.valueOf(tradeCount));
            updateVo.setOperator(userlogin.getUserId());
            updateVo.setEndAudit(endAudit);
            return prebookDisCountService.updateDisCount(updateVo);
        } else {
            InsertDiscountAppVo insertVo = new InsertDiscountAppVo();

            insertVo.setPrebookid(Long.valueOf(prebookid));
            insertVo.setCalculateAppAmt(new BigDecimal(calculateAppAmt));
            insertVo.setDiscountType(discountType);
            insertVo.setDiscountRate(new BigDecimal(discountRate));
            insertVo.setDiscountReason(discountReason);
            insertVo.setAfterTaxAmt(new BigDecimal(afterTaxAmt));
            insertVo.setBeforeTaxAmt(new BigDecimal(beforeTaxAmt));
            insertVo.setBefTaxAmtAdjust(insertVo.getBeforeTaxAmt());
            insertVo.setDiscountWay(discountWay);
            insertVo.setStaffrelation(staffRelation);
            insertVo.setDiscountStyle(discountStyle);
            insertVo.setIsrefund(isRefund);
            insertVo.setDiscountState(defineState);
            insertVo.setTradecount(Long.valueOf(tradeCount));
            insertVo.setCreator(userlogin.getUserId());


            if(StringUtils.isNotBlank(interestRate)) {
                DisCountInterestVO disCountInterestVO = new DisCountInterestVO();
                disCountInterestVO.setInterestType(interestType);
                disCountInterestVO.setInterestLevel(interestLevel);
                disCountInterestVO.setInterestRate(new BigDecimal(interestRate));
                insertVo.setInterestVo(disCountInterestVO);
            }
            //插入折扣申请表
            return prebookDisCountService.insertDisCount(insertVo);
        }
    }

    @RequestMapping("/auditDiscountList.do")
    public String auditDiscountList(){
        return "prosale/auditDiscountList";
    }

    @SuppressWarnings("unchecked")
	@RequestMapping("/auditDiscountListJson.do")
    @ResponseBody
    public Object auditDiscountListJson(HttpServletRequest request) throws Exception{
        // 设置查询参数
        Map<String, String> param;
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();
        String custname = request.getParameter("custname");
        String conscustno = request.getParameter("conscustno");
        String hboneno = request.getParameter("hboneno");
        String preid = request.getParameter("preid");
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        String discountstate = request.getParameter("discountstate");
        String prebookStates = request.getParameter("prebookStates");
        String tradestate = request.getParameter("tradestate");
        String discountType = request.getParameter("discountType");
        String expectTradeBegDt = request.getParameter("expectTradeBegDt");
        String expectTradeEndDt = request.getParameter("expectTradeEndDt");
        String payStates = request.getParameter("payStates");
        String realpayamtbegdt = request.getParameter("realpayamtbegdt");
        String realpayamtenddt = request.getParameter("realpayamtenddt");
        String discountstyle = request.getParameter("discountstyle");
        //查询条件（打款状态）不为空，增增加客户名参数
        param.put("payStates", StringUtil.getStr(payStates));
        //查询条件（预计交易开始日期）不为空，增增加客户名参数
        param.put("realpayamtbegdt", StringUtil.getStr(realpayamtbegdt));
        //查询条件（预计交易结束日期）不为空，增增加客户名参数
        param.put("realpayamtenddt", StringUtil.getStr(realpayamtenddt));
        //查询条件（折扣形式）不为空，增增加客户名参数
        param.put("discountstyle", StringUtil.getStr(discountstyle));
        //查询条件（客户名）不为空，增增加客户名参数
        param.put("custname", StringUtil.getStr(custname));
        //查询条件（投顾客户号）不为空，则增加投顾客户号参数
        param.put("conscustno", StringUtil.getStr(conscustno));
        //查询条件（一账通号）不为空，则增加一账通号参数
        param.put("hboneno", StringUtil.getStr(hboneno));
        param.put("preid", StringUtil.getStr(preid));
        //如果查询条件（折扣状态）不为空，则增加折扣状态查询参数
        if(StringUtil.isNotNullStr(discountstate)){
            param.put("discountstate", discountstate);
        }else{
            param.put("discountstate", "all");
        }

        if (StringUtil.isNotNullStr(consCode)) {
            param.put("conscode", consCode);
        } else {
            param.put("orgcode", orgCode);
        }

        //如果查询条件（预计交易日期开始）不为空，则增加预计交易日期开始查询参数
        if(StringUtil.isNotNullStr(expectTradeBegDt)){
            param.put("expecttradebegdt", expectTradeBegDt);
        }
        //如果查询条件（预计交易日期截止）不为空，则增加预计交易日期截止查询参数
        if(StringUtil.isNotNullStr(expectTradeEndDt)){
            param.put("expecttradeenddt", expectTradeEndDt);
        }
        //如果查询条件（预约状态）不为空，则增加预约状态查询参数
        if(StringUtil.isNotNullStr(prebookStates)){
            param.put("prebookStates", prebookStates);
        }
        //如果查询条件（交易确认状态）不为空，则增加交易确认状态查询参数
        if(StringUtil.isNotNullStr(tradestate)){
            param.put("tradeStates", tradestate);
        }
        //如果查询条件（折扣状态）不为空，则增加折扣状态查询参数
        if(StringUtil.isNotNullStr(discountstate)){
            param.put("discountstate", discountstate);
        }
        //如果查询条件（折扣类型）不为空，则增加折扣类型查询参数
        if(StringUtil.isNotNullStr(discountType)){
            param.put("discountType", discountType);
        }
        // 判断常量表中合规标识：true启用，false停用
 		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
 		boolean roleCpFlag = false;
 		if (cacheMap != null && !cacheMap.isEmpty()) {
 			roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
 		}
 		// 判断登录人员的角色中是否包括"合规人员"角色
 		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
// 		if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
//            param.put("hascp", "true");
//        }
        // 通过Session获取产品广度信息
        String topcpdata = (String) request.getSession().getAttribute("topcpdata");
        param.put("topCpData", topcpdata);
        PageData<Prebookproductinfo> pageData = prebookproductinfoService.listPrebookproductinfoByPage(param);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<Prebookproductinfo> listdata= pageData.getListData();
        Map<BigDecimal, String> interestMap = getInterestMap(listdata);
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        ConstantCache constantCache = ConstantCache.getInstance();
        for(Prebookproductinfo info : listdata){
            String fccl =getFcclFlagByProdCode(info.getFundcode());
            info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));
            // 分次CALL产品查询认缴金额
            info.setFccl(fccl);
            if(StaticVar.FCCL_YES.equals(fccl)){
                // 分次call产品，需要展示【手续费标准】
                info.setFeeRateMethod(manyCallPreInfoService.getFeeRateMethodName(info.getId()));
            }
            info.setBuyamt((info.getDealno() != null ? info.getZtbuyamt() : info.getBuyamt()).divide(new BigDecimal(10000)));
            info.setPrebookstateval(constantCache.getConstantKeyVal("prebookStates").get(info.getPrebookstate()));
            info.setPaystateval(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));
            info.setOrderstateval(constantCache.getConstantKeyVal("dealorderStatus").get(info.getOrderstate()));
            info.setPretypeval(constantCache.getConstantKeyVal("pretype").get(info.getPretype()));
            info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
            info.setDiscountWayVal(constantCache.getConstantKeyVal("discountWays").get(info.getDiscountWay()));
            info.setDiscountTypeVal(constantCache.getConstantKeyVal("discountTypes").get(info.getDiscountType()));
            info.setTradestateval(constantCache.getConstantKeyVal("tradestate").get(info.getTradestate()));
            info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));
            info.setConsname(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
            info.setOutletName(consOrgCache.getOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(info.getCreator())));
            String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(info.getCreator()));
            if("0".equals(uporgcode)){
                info.setUporgname(info.getOutletName());
            }else{
                info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
            }
            info.setDiscountstyle(constantCache.getConstantKeyVal("discountStyles").get(info.getDiscountstyle()));
            info.setStaffRelation(constantCache.getConstantKeyVal("staffrelations").get(info.getStaffRelation()));
            info.setFundtype(constantCache.getConstantKeyVal("jjxxhmcpx").get(info.getHmcpx()));
            //折扣客户权益描述
            info.setInterestComment(interestMap.get(info.getId()));
        }
        resultMap.put("rows", listdata);
        return resultMap;
    }

    private Map<BigDecimal, String> getInterestMap(List<Prebookproductinfo> listdata){
        if(CollectionUtils.isEmpty(listdata)){
            return new HashMap<>();
        }
        List<BigDecimal> preIdList = listdata.stream().map(Prebookproductinfo::getId).collect(Collectors.toList());
        DisCountInterestListRequest disCountInterestListRequest = new DisCountInterestListRequest();
        disCountInterestListRequest.setPreIdList(preIdList);
        ReturnMessageDto<DisCountInterestListVO> returnMessageDto = preDisCountExtendService.queryDiscountInterestCommentList(disCountInterestListRequest);
        DisCountInterestListVO disCountInterestListVO = returnMessageDto.getReturnObject();
        return disCountInterestListVO.getDiscountIdMap();
    }
    
    /**
     * 导出操作     
     * @param request
     * @param response
     * @throws Exception
     */
    @RequestMapping("/exportDiscountApp.do")
    public void exportDiscountApp(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置查询参数
    	String ids = ObjectUtils.replaceNull(request.getParameter("ids"));
    	if (StringUtils.isNotBlank(ids)) {
            String[] temp = ids.split(",");
            List<String> list = new ArrayList<String>();
            //将数组转list
            CollectionUtils.addAll(list, temp);
            Map<String,Object> param = new HashMap<String,Object>(1);
            param.put("ids", list);
            List<Prebookproductinfo> exportList = discountappService.exportDiscountApp(param);			
            if(CollectionUtils.isNotEmpty(exportList)){
            	ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
            	ConstantCache constantCache = ConstantCache.getInstance();
            	for(Prebookproductinfo info : exportList){
                    //原逻辑： 中台打款状态等于到账确认后取中台手续费
                    //原逻辑问题： 只考虑 高端中台。未考虑海中台 。
                    //新逻辑： 直接取 预约的fee .
                    //       原因：【高端|海外】中台订单同步时，预约到账确认，会同步 【高端|海外】中台费用 。
                	BigDecimal fee = info.getFee()==null?BigDecimal.ZERO:info.getFee();
                	info.setRealfee(fee.setScale(2, RoundingMode.HALF_UP));
                	info.setDiscountRate(info.getDiscountRate() == null ? null : info.getDiscountRate().setScale(2, RoundingMode.HALF_UP));


                    info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));

                    // 是否分次call产品：1-是 0-否
                    String isfccall =getFcclFlagByProdCode(info.getFundcode());
                    info.setFccl(isfccall);

                    //原逻辑： 高端中台  考虑内外扣金额： DECODE(CZO.FEECALMODE,'0',(CZO.APPAMT - NVL(CZO.FEE, 0)), CZO.APPAMT)
                    //原逻辑问题： 只考虑 高端中台。未考虑海中台 。
                    //新逻辑： 直接取 预约的购买金额 .
                    //       原因：【高端|海外】中台订单同步时，已考虑内外扣，并将净金额同步至预约购买金额 。
                    info.setBuyamt(info.getBuyamt()==null?null:info.getBuyamt().divide(BIG_DECIMAL_1W, 2, RoundingMode.HALF_UP));
                    info.setTotalamt(info.getTotalamt() == null ? null : info.getTotalamt().divide(BIG_DECIMAL_1W, 2, RoundingMode.HALF_UP));
                    info.setRealpayamt(info.getRealpayamt() == null ? null : info.getRealpayamt().divide(BIG_DECIMAL_1W, 2, RoundingMode.HALF_UP));
                    info.setPrebookstateval(constantCache.getConstantKeyVal("prebookStates").get(info.getPrebookstate()));
                    info.setPaystateval(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));
                    info.setOrderstateval(constantCache.getConstantKeyVal("dealorderStatus").get(info.getOrderstate()));
                    info.setPretypeval(constantCache.getConstantKeyVal("pretype").get(info.getPretype()));
                    info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
                    info.setDiscountWayVal(constantCache.getConstantKeyVal("discountWays").get(info.getDiscountWay()));
                    info.setDiscountTypeVal(constantCache.getConstantKeyVal("discountTypes").get(info.getDiscountType()));
                    info.setTradestateval(constantCache.getConstantKeyVal("tradestate").get(info.getTradestate()));
                    info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));
                    info.setConsname(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
                    info.setOutletName(consOrgCache.getOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(info.getCreator())));
                    String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(info.getCreator()));
                    if("0".equals(uporgcode)){
                        info.setUporgname(info.getOutletName());
                    }else{
                        info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
                    }
                    info.setDiscountstyle(constantCache.getConstantKeyVal("discountStyles").get(info.getDiscountstyle()));
                    info.setStaffRelation(constantCache.getConstantKeyVal("staffrelations").get(info.getStaffRelation()));
                    info.setFundtype(constantCache.getConstantKeyVal("jjxxhmcpx").get(info.getHmcpx()));
                }
            }
            try {
                // 清空输出流
                response.reset();
                // 设置文件格式和名字
                response.setContentType("multipart/form-data");
                response.setHeader("Content-Disposition",
                        "attachment;fileName=" + new String("折扣审核.xls".getBytes("gb2312"), "ISO8859-1"));
                ServletOutputStream os = response.getOutputStream();
                String [] columnName = new String [] {"预计交易日期", "录入时间", "投顾客户号", "客户姓名","预约时所属投顾", "所属部门", "所属区域", "交易类型", "产品类型", "产品名称","预约状态", "认缴金额(万)", "购买金额(万)", "实际打款金额(万)", "实际打款日期", "实际打款手续费", "打款状态", "折扣方式","折扣形式","折扣率", "税前折扣金额", "折扣类型", "员工关系","折扣理由", "折扣状态", "审核人意见", "交易确认状态"};
                String [] beanProperty = new String [] {"expecttradedt", "credt", "conscustno", "conscustname","consname", "outletName", "uporgname", "tradeTypeVal","fundtype", "fundname", "prebookstateval", "totalamt", "buyamt", "realpayamt","realpayamtdt", "realfee", "paystateval","discountWayVal", "discountstyle","discountRate", "beforetaxamt", "discountTypeVal", "staffRelation","discountReason","discountstateval", "remarks2", "tradestateval"};
                ExcelWriter.writeExcel(os, "折扣审核", 0, exportList, columnName, beanProperty);
                os.close(); // 关闭流
            } catch (Exception e) {
                log.error("折扣文件导出异常", e);
            }
    	}
    }
    

    /**
     * @description:(根据产品代码判断是否分次call。 1-是 0-否)
     * @param prodCode
     * @return java.lang.String
     * @author: haoran.zhang
     * @date: 2025/5/23 15:44
     * @since JDK 1.8
     */
    private String getFcclFlagByProdCode(String prodCode){
        // 是否分次call产品：1-是 0-否
        String isfccall = YesOrNoEnum.NO.getCode();
        ReturnMessageDto<String>  fcclFlagResp=prebookConfigService.getFcclFlag(prodCode);
        if(fcclFlagResp.isSuccess() &&  YesOrNoEnum.YES.getCode().equals(fcclFlagResp.getReturnObject())){
            isfccall = YesOrNoEnum.YES.getCode();
        }
        return isfccall;
    }


    /**
     * 审核页面:
     * @param request
     * @param map 返回数据
    * <AUTHOR>
    * @date 2019/9/5
    */
    @RequestMapping("/auditDiscount.do")
    public String auditDiscount(HttpServletRequest request, Map<String,Object> map) throws Exception {
        String id = request.getParameter("id");
        map.put("prebookid",id);
        CmPrebookproductinfo info;
        if(StringUtil.isNullStr(id)){
            throw new Exception("预约ID为空");
        }
        GetPrebookInfoByIdRequest getPrebookInfoByIdRequest = new GetPrebookInfoByIdRequest();
        getPrebookInfoByIdRequest.setId(new BigDecimal(id));
        GetPrebookByIdResponse preResponse = queryPreBookService.getPrebookById(getPrebookInfoByIdRequest);
        info = preResponse.getPreinfo();
        JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(info.getPcode(), false);
        if (jjxxInfo== null) {
            throw new Exception("产品信息为空!");
        }
        map.put("pname",jjxxInfo.getJjjc());

        // 是否分次call产品：1-是 0-否
        String isfccall =getFcclFlagByProdCode(info.getPcode());
        map.put("isfccl", isfccall);

        if(StaticVar.FCCL_YES.equals(isfccall)){
            // 分次CALL产品查询认缴金额
            map.put("totalamt", info.getTotalamt() == null ? BigDecimal.ZERO : info.getTotalamt().divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP).toPlainString());
            // 分次call产品，需要展示【手续费标准】
            map.put("feeRateMethod", manyCallPreInfoService.getFeeRateMethodName(info.getId()));
        }
        map.put("consName",ConsOrgCache.getInstance().getAllConsMap().get(info.getCreator()));
        map.put("conscustname", info.getConscustname());
        map.put("currencyVal", ConstantCache.getInstance().getVal("currencys", info.getCurrency()));
        Discountapp discountapp = info.getDiscountapp();
        map.put("afterTaxAmt",discountapp.getAfterTaxAmt().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        map.put("beforeTaxAmt",discountapp.getBeforeTaxAmt().setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
        map.put("befTaxAmtAdjust",discountapp.getBefTaxAmtAdjust() == null ? BigDecimal.ZERO : discountapp.getBefTaxAmtAdjust().setScale(4, BigDecimal.ROUND_HALF_UP).toPlainString());
        //申请折扣和MGM时取实际打款金额
        if(info.getRealpayamt() != null){
            map.put("buyamt", info.getRealpayamt().divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP).toPlainString());
        }else{//申请折扣和MGM时取预约金额
            map.put("buyamt", info.getBuyamt().divide(new BigDecimal(10000), 2, BigDecimal.ROUND_HALF_UP).toPlainString());
        }
        map.put("currency",info.getCurrency());
        if(!CurrencyEnum.RMB.getCode().equals(info.getCurrency())) {
            BigDecimal exchangerate = BigDecimal.ONE;
            if (info.getBuyamtRmb() != null) {
                exchangerate = info.getBuyamtRmb().divide(info.getBuyamt(),4,BigDecimal.ROUND_UP);
            } else {
                RmbhlzjjDto rmbhlzjjDto = comprehensiveService.getRmbhlzjj(info.getRatedt() == null ? null : DateTimeUtil.strToDate(info.getRatedt()), info.getCurrency());
                if (rmbhlzjjDto != null) {
                    exchangerate = new BigDecimal(rmbhlzjjDto.getZjj());
                }
            }
            map.put("beforeTaxAmtRmb",exchangerate.multiply(discountapp.getBeforeTaxAmt()).setScale(2,BigDecimal.ROUND_HALF_UP));
            map.put("afterTaxAmtRmb",exchangerate.multiply(discountapp.getAfterTaxAmt()).setScale(2,BigDecimal.ROUND_HALF_UP));
        }
        //客户来源
        Conscust cust = conscustService.getConscust(info.getConscustno());
        map.put("source", cust.getNewsourcename());
        map.put("subSource", cust.getNewsubsourcename());

        //交易次数
        int tradecount = discountappService.getTradeCountByCustno(info.getConscustno());
        map.put("tradeCount", tradecount + 1);
        map.put("discountState", discountapp.getDiscountState());
        map.put("isRefund", discountapp.getIsrefund());
        map.put("discountType", discountapp.getDiscountType());
        map.put("discountRate", discountapp.getDiscountRate().setScale(4, BigDecimal.ROUND_HALF_UP).toPlainString());
        map.put("discountWay", discountapp.getDiscountWay());
        map.put("discountStyle", discountapp.getDiscountStyle());
        map.put("discountReason", discountapp.getDiscountReason());
        map.put("remarks", discountapp.getRemarks());
        map.put("staffRelation", discountapp.getStaffRelation());
        BigDecimal amt = discountappService.getTradeAmtByCustno(info.getConscustno());
        map.put("amt",amt);

        return "prosale/auditDiscount";
    }


    /**
     * 处理审核:
     * @param request
    * <AUTHOR>
    * @date 2019/9/5
    */
    @ResponseBody
    @RequestMapping("/auditDiscountDeal.do")
    public String auditDiscountDeal(HttpServletRequest request) {
        String prebookid = request.getParameter("prebookid");
        String state = request.getParameter("state");
        String remarks = request.getParameter("remarks");
        String befTaxAmtAdjust = request.getParameter("befTaxAmtAdjust");
        String result;
        Map<String,Object> param = new HashMap<>(1);
        param.put("prebookid", prebookid);
        Discountapp discountapp = discountappService.getDiscountapp(param);
        if(discountapp != null){
            User userlogin = (User)request.getSession().getAttribute("loginUser");
            boolean canzspass = state.equals(StaticVar.DISCOUNT_NSTATES_CS_PASS) && (StaticVar.DISCOUNT_TYPE_DADAN.equals(discountapp.getDiscountType())  || StaticVar.DISCOUNT_TYPE_ACTIVITY.equals(discountapp.getDiscountType()));
            //活动竞赛和大单定制初审通过直接改状态为终审通过
            if(canzspass){
                state = StaticVar.DISCOUNT_NSTATES_ZS_PASS;
            }
            discountapp.setDiscountState(state);
            discountapp.setRemarks(remarks);
            if(StringUtil.isNotNullStr(befTaxAmtAdjust)){
            	discountapp.setBefTaxAmtAdjust(new BigDecimal(befTaxAmtAdjust));
            }
            discountapp.setChecker(userlogin.getUserId());
            discountapp.setCheckdt(DateTimeUtil.PatternDate(new Date(), "yyyyMMdd"));
            discountappService.updateDiscountapp(discountapp);
            //插入日志
            insertAuditLog(state,userlogin.getUserId(),discountapp.getId());
            result = "success";
        }else {
            result = "paramError";
        }
        return result;
    }

    @ResponseBody
    @RequestMapping("/calculate_tax.do")
    public Double calculateTax(HttpServletRequest request) {
        String amt = request.getParameter("amt");
        Double result = 0d;
        result = CalculateUtil.calculateDiscountTax(new Double(amt));
        result = (new Double(amt)) - result;
        return result;
    }

    private void insertAuditLog(String state,String userId,BigDecimal discountId){
        CmDiscountLog cmDiscountLog = new CmDiscountLog();
        cmDiscountLog.setOptMan(userId);
        cmDiscountLog.setDiscountId(discountId);
        if(state.equals(StaticVar.DISCOUNT_NSTATES_CS_PASS)) {
            cmDiscountLog.setOptType(StaticVar.DISCOUNT_OPT_FIRST_AUDIT_PASS);
        }
        if(state.equals(StaticVar.DISCOUNT_NSTATES_CS_NOPASS)) {
            cmDiscountLog.setOptType(StaticVar.DISCOUNT_OPT_FIRST_AUDIT_REJECT);
        }
        if(state.equals(StaticVar.DISCOUNT_NSTATES_ZS_PASS)) {
            cmDiscountLog.setOptType(StaticVar.DISCOUNT_OPT_END_AUDIT_PASS);
        }
        if(state.equals(StaticVar.DISCOUNT_NSTATES_ZS_NOPASS)) {
            cmDiscountLog.setOptType(StaticVar.DISCOUNT_OPT_END_AUDIT_REJECT);
        }
        discountappService.insertLog(cmDiscountLog);
    }

    /**
     * @description 费率格式化
     * @param jjxxInfo
     * @param feeRate
     * @return java.math.BigDecimal
     * @author: jianjian.yang
     * @date: 2023/9/19 10:30
     * @since JDK 1.8
     */
    public BigDecimal getFeeRateFormatter(JjxxInfo jjxxInfo, BigDecimal feeRate){
        //海外产品费率用原始值
        if(jjxxInfo != null && YesOrNoEnum.YES.getCode().equals(jjxxInfo.getSfxg())){
            return feeRate;
        }else {
            return feeRate == null ? null : feeRate.setScale(4, BigDecimal.ROUND_HALF_UP);
        }
    }

}
