package com.howbuy.crm.hb.web.controller.custinfo;

import com.google.common.collect.Lists;
import com.howbuy.acc.common.utils.DigestUtil;
import com.howbuy.crm.account.client.enums.DealStatusEnum;
import com.howbuy.crm.account.client.request.custinfo.*;
import com.howbuy.crm.account.client.response.PageVO;
import com.howbuy.crm.account.client.response.Response;
import com.howbuy.crm.account.client.response.custinfo.AbnormalCustInfoVO;
import com.howbuy.crm.account.client.response.custinfo.AbnormalRelatedDetailVO;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.hb.service.custinfo.HboneCancelFlushUnbindService;
import com.howbuy.crm.hb.service.custinfo.MergeConscustService;
import com.howbuy.crm.hb.service.outerservice.CrmHboneAbnormalOuterService;
import com.howbuy.crm.hb.web.dto.custinfo.AbnormalCustInfoDto;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Description: 一账通异常客户 Controller
 */
@Slf4j
@Controller
@RequestMapping(value = "/hboneabnormalcust")
public class HboneAbnormalCustController extends AbstractAbnormalCustController {

	@Autowired
	private CrmHboneAbnormalOuterService abnormalOuterService;



	@Autowired
	private MergeConscustService mergeConscustService;

	@Autowired
	private HboneCancelFlushUnbindService flushUnbindService;


	/**
	 * @api {GET} /hboneabnormalcust/queryhboneabnormalpage hkAbnormal()
	 * @apiVersion 1.0.0
	 * @apiGroup HboneAbnormalCustController
	 * @apiName hkAbnormal()
	 * @apiDescription 跳转到一账通异常信息处理页面
	 * @apiSuccess (响应结果) {Object} view
	 * @apiSuccess (响应结果) {Object} model
	 * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
	 * @apiSuccess (响应结果) {Boolean} cleared
	 * @apiSuccessExample 响应结果示例
	 * {"view":{},"model":{},"cleared":true,"status":"REQUEST_HEADER_FIELDS_TOO_LARGE"}
	 */
	@RequestMapping("/queryhboneabnormalpage")
	public ModelAndView hkAbnormal(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/custinfo/hboneAbnormalCust");
		return modelAndView;
	}


	/**
	 * @api {GET} /hboneabnormalcust/listhkabnomalcust.do listHkAbnomalCust()
	 * @apiVersion 1.0.0
	 * @apiGroup HboneAbnormalCustController
	 * @apiName listHkAbnomalCust()
	 * @apiParam (请求参数) {String} custName
	 * @apiParam (请求参数) {String} hkTxAcctNo
	 * @apiParam (请求参数) {String} hboneNo
	 * @apiParam (请求参数) {String} custNo
	 * @apiParam (请求参数) {String} mobileDigest
	 * @apiParam (请求参数) {String} idNoDigest
	 * @apiParam (请求参数) {String} abnormalSource
	 * @apiParam (请求参数) {String} dealStatus
	 * @apiParam (请求参数) {String} createBginDdate
	 * @apiParam (请求参数) {String} createEndDate
	 * @apiParam (请求参数) {Number} page
	 * @apiParam (请求参数) {Number} rows
	 * @apiParamExample 请求参数示例
	 * hkTxAcctNo=5G0ZcYO3v&custNo=GJIt9b&abnormalSource=Eu4NtuE6Nl&mobileDigest=apR6uUIAZR&idNoDigest=m&dealStatus=iqLO8&createBginDdate=IRZax&page=6029&custName=B3JF3I5w&rows=6406&createEndDate=hS&hboneNo=rbJnK5rQ6n
	 * @apiSuccess (响应结果) {Number} page
	 * @apiSuccess (响应结果) {Number} size
	 * @apiSuccess (响应结果) {Number} total
	 * @apiSuccess (响应结果) {Array} rows
	 * @apiSuccess (响应结果) {String} rows.id 异常客户数据ID
	 * @apiSuccess (响应结果) {String} rows.messageClientId 消息通知的clientId
	 * @apiSuccess (响应结果) {String} rows.hkTxAcctNo 香港客户号
	 * @apiSuccess (响应结果) {String} rows.custName 客户姓名
	 * @apiSuccess (响应结果) {String} rows.investType 投资者类型
	 * @apiSuccess (响应结果) {String} rows.mobileAreaCode 手机地区码
	 * @apiSuccess (响应结果) {String} rows.mobileDigest 手机号摘要
	 * @apiSuccess (响应结果) {String} rows.mobileMask 手机号掩码
	 * @apiSuccess (响应结果) {String} rows.mobileCipher 手机号密文
	 * @apiSuccess (响应结果) {String} rows.idSignAreaCode 证件地区码
	 * @apiSuccess (响应结果) {String} rows.idType 证件类型
	 * @apiSuccess (响应结果) {String} rows.idNoDigest 证件号码摘要
	 * @apiSuccess (响应结果) {String} rows.idNoMask 证件号码掩码
	 * @apiSuccess (响应结果) {String} rows.idNoCipher 证件号码密文
	 * @apiSuccess (响应结果) {String} rows.hboneNo 一账通号
	 * @apiSuccess (响应结果) {String} rows.abnormalSource 异常来源：1-香港注册2-香港开户7-好买开户 [暂时同于  7-分销开户]3-香港客户信息同步4-香港客户号/一账通绑定5-一账通实名
	 * @apiSuccess (响应结果) {String} rows.abnormalSceneType 异常描述，按异常类别汇总：1-匹配到多个投顾客户号【香港注册、香港开户】2-匹配到的投顾客户号已被占用 【香港注册】3-手机号相同，证件类型/证件号不匹配 【香港开户】4-证件相同，但手机号不匹配【香港开户】5-CRM重复客户预警【香港客户信息同步】6-同时绑定香港客户号时，投顾客户号/香港客户号被占用 【好买开户、一账通实名】7-香港开户证件与一账通证件不一致 【香港开户】8-两边绑定的投顾客户号不一致【香港客户号/一账通绑定】
	 * @apiSuccess (响应结果) {String} rows.operateChannel 操作通道 1-MQ  2-菜单页面
	 * @apiSuccess (响应结果) {String} rows.creator 创建人
	 * @apiSuccess (响应结果) {Number} rows.createTimestamp 创建时间
	 * @apiSuccess (响应结果) {String} rows.modifier 修改人
	 * @apiSuccess (响应结果) {Number} rows.modifyTimestamp 修改时间
	 * @apiSuccess (响应结果) {String} rows.recStat 记录有效状态（1-正常  0-删除）
	 * @apiSuccess (响应结果) {String} rows.dealStatus 处理状态：0-未处理 1-已处理 2-无需处理
	 * @apiSuccess (响应结果) {String} rows.dealOperator 处理人
	 * @apiSuccess (响应结果) {String} rows.dealRemark 处理意见
	 * @apiSuccess (响应结果) {Object} rows.hkSideInfo 香港账户侧 客户信息
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.hkTxAcctNo
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.hboneNo
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.custName
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.investType
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.custStatus
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.custStatusDesc
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.isRealName
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.openAcct
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.idNoDigest
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.idSignAreaCode
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.idType
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.idNoMask
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.idTypeDesc
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.mobileAreaCode
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.mobileDigest
	 * @apiSuccess (响应结果) {String} rows.hkSideInfo.mobileMask
	 * @apiSuccess (响应结果) {Object} rows.hboneSideInfo 一账通账户侧 客户信息
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.hkTxAcctNo
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.hboneNo
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.custName
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.investType
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.custStatus
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.custStatusDesc
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.isRealName
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.openAcct
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.idNoDigest
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.idSignAreaCode
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.idType
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.idNoMask
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.idTypeDesc
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.mobileAreaCode
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.mobileDigest
	 * @apiSuccess (响应结果) {String} rows.hboneSideInfo.mobileMask
	 * @apiSuccess (响应结果) {String} rows.relatedCustNo crm已关联的 客户号
	 * @apiSuccess (响应结果) {Number} rows.dealTimestamp 处理时间
	 * @apiSuccess (响应结果) {Array} rows.relatedList 关联客户列表
	 * @apiSuccess (响应结果) {String} rows.relatedList.id 异常客户待关联id
	 * @apiSuccess (响应结果) {String} rows.relatedList.abnormalId 异常客户数据ID
	 * @apiSuccess (响应结果) {String} rows.relatedList.custNo 客户号
	 * @apiSuccess (响应结果) {String} rows.relatedList.custName 客户名称
	 * @apiSuccess (响应结果) {String} rows.relatedList.investType 投资者类型
	 * @apiSuccess (响应结果) {String} rows.relatedList.mobileAreaCode 手机地区码
	 * @apiSuccess (响应结果) {String} rows.relatedList.mobileDigest 手机号摘要
	 * @apiSuccess (响应结果) {String} rows.relatedList.mobileMask 手机号掩码
	 * @apiSuccess (响应结果) {String} rows.relatedList.mobileCipher 手机号密文
	 * @apiSuccess (响应结果) {String} rows.relatedList.idSignAreaCode 证件地区码
	 * @apiSuccess (响应结果) {String} rows.relatedList.idType 证件类型
	 * @apiSuccess (响应结果) {String} rows.relatedList.idNoDigest 证件号码摘要
	 * @apiSuccess (响应结果) {String} rows.relatedList.idNoMask 证件号码掩码
	 * @apiSuccess (响应结果) {String} rows.relatedList.idNoCipher 证件号码密文
	 * @apiSuccess (响应结果) {String} rows.relatedList.hboneNo 一账通号
	 * @apiSuccess (响应结果) {String} rows.relatedList.hkTxAcctNo 香港客户号
	 * @apiSuccess (响应结果) {String} rows.relatedList.consCode 客户所属投顾
	 * @apiSuccess (响应结果) {String} rows.relatedList.creator 创建人
	 * @apiSuccess (响应结果) {Number} rows.relatedList.createTimestamp 创建时间
	 * @apiSuccess (响应结果) {String} rows.relatedList.modifier 修改人
	 * @apiSuccess (响应结果) {Number} rows.relatedList.modifyTimestamp 修改时间
	 * @apiSuccess (响应结果) {String} rows.relatedList.idTypeDesc 证件类型描述
	 * @apiSuccess (响应结果) {String} rows.relatedList.createDt 投顾客户号】的【创建日期】      yyyy-MM-dd
	 * @apiSuccess (响应结果) {Object} rows.relatedList.hkSideInfo 香港账户侧 客户信息
	 * @apiSuccess (响应结果) {Object} rows.relatedList.hboneSideInfo 一账通账户侧 客户信息
	 * @apiSuccess (响应结果) {String} rows.relatedList.hasTradeOrPre 是否有预约或交易 1-是 0-否
	 * @apiSuccess (响应结果) {Boolean} rows.relatedList.hasPreBook 是否有预约
	 * @apiSuccess (响应结果) {Boolean} rows.relatedList.hasTrade 是否有交易
	 * @apiSuccess (响应结果) {String} rows.relatedList.orgName 所属部门
	 * @apiSuccess (响应结果) {String} rows.relatedList.consName 所属投顾
	 * @apiSuccess (响应结果) {String} rows.relatedList.dealStatus 主表      处理状态：0-未处理 1-已处理 2-无需处理
	 * @apiSuccess (响应结果) {String} rows.idTypeDesc 证件类型描述
	 * @apiSuccess (响应结果) {String} rows.abnormalSceneDesc 异常详细描述
	 * @apiSuccessExample 响应结果示例
	 * {"total":5767,"size":1559,"page":5589,"rows":[{"operateChannel":"A7tp","modifier":"O","hboneSideInfo":{"investType":"pdW4","idType":"xXbWiQTS","idNoDigest":"w","custName":"SLXw","idNoMask":"xYKmq","hkTxAcctNo":"DS1sa1R","mobileAreaCode":"lFNK15P","idSignAreaCode":"rmGktWhoGf","mobileDigest":"Lmi5ZL","idTypeDesc":"shqgpryR","isRealName":"WYv","custStatusDesc":"lbQRCS","hboneNo":"ExGfcbm","custStatus":"Rvt","openAcct":"9Q4pQIy","mobileMask":"cuCe"},"createTimestamp":1824947061255,"modifyTimestamp":2857754596164,"mobileAreaCode":"PHEzDmhq","abnormalSceneDesc":"Foy4","mobileDigest":"pNeytEyhRT","id":"Xmer2NpgE","dealRemark":"C","hboneNo":"iw7sjSMzo","investType":"bOtX5N","creator":"ejPriMUPeq","idType":"8MkyMav2mG","messageClientId":"Y","dealOperator":"fLAjLLm0rO","dealTimestamp":1065427662860,"idNoDigest":"Od","dealStatus":"f","custName":"9IneE6","mobileCipher":"aOk","idNoMask":"R2lN0R3","hkTxAcctNo":"phrUuuj","idSignAreaCode":"C8mmHr","abnormalSource":"8VkSorY8gj","abnormalSceneType":"Z","relatedList":[{"modifier":"okaQ","consName":"2","createTimestamp":2458195807398,"modifyTimestamp":3510512445567,"mobileAreaCode":"f7zK","abnormalId":"0yA","mobileDigest":"cjI8tnVQ","id":"twqYr","hboneNo":"zDTUR","investType":"QA8n","custNo":"4iOApK","creator":"QO","hasTrade":false,"idType":"ctUn","orgName":"KJ9N7pLln7","hasPreBook":true,"idNoDigest":"wx91","dealStatus":"h4fE","custName":"dxVHI","mobileCipher":"OMu5j7t","idNoMask":"U","createDt":"itMw","consCode":"ekYpojR","hkTxAcctNo":"LPjQS","idSignAreaCode":"2gbkNRqNTN","hasTradeOrPre":"A","idTypeDesc":"EE2kVI","idNoCipher":"KibSx4","mobileMask":"Ls331MetcW"}],"idTypeDesc":"FnWR8os","idNoCipher":"fVb92BEjZa","relatedCustNo":"zCgOS","recStat":"xg","hkSideInfo":{"investType":"G","idType":"EQJDLMyKZp","idNoDigest":"kzyxl","custName":"4KmAMlc","idNoMask":"Lrs","hkTxAcctNo":"hr6wCOe","mobileAreaCode":"3riEm","idSignAreaCode":"TQ0rjeNO6d","mobileDigest":"0wB","idTypeDesc":"tOkiwiE6Iv","isRealName":"pft57hy","custStatusDesc":"Nb2LOyedD","hboneNo":"2Esl","custStatus":"YmutO9","openAcct":"cqQ","mobileMask":"qBQvOY"},"mobileMask":"3TUTXGWJg"}]}
	 */
	@ResponseBody
	@RequestMapping("/listhkabnomalcust.do")
	public PageVO<AbnormalCustInfoDto> listHkAbnomalCust(AbnormalCustInfoRequest request) {

		//digest 处理
		if(StringUtil.isNotNullStr(request.getMobileDigest())){
			request.setMobileDigest(DigestUtil.digest(request.getMobileDigest()));
		}
		if(StringUtil.isNotNullStr(request.getIdNoDigest())){
			request.setIdNoDigest(DigestUtil.digest(request.getIdNoDigest()));
		}

		PageVO<AbnormalCustInfoVO> pageResult= abnormalOuterService. queryHboneAbnormalPage( request);

		PageVO<AbnormalCustInfoDto>  returnPage=new PageVO<>();
		returnPage.setTotal(pageResult.getTotal());
		returnPage.setSize(pageResult.getSize());
		returnPage.setPage(pageResult.getPage());
		List<AbnormalCustInfoDto> rowList=Lists.newArrayList();


		List<AbnormalCustInfoVO>  pageList= pageResult.getRows();
		for(AbnormalCustInfoVO  custInfo : pageList){

			//主表 转译
			AbnormalCustInfoDto  dto=new AbnormalCustInfoDto();
			BeanUtils.copyProperties(custInfo,dto);
            //明细列表
			dto.setRelatedList(convertRelatedCustList(custInfo));
			rowList.add(dto);
		}
		returnPage.setRows(rowList);
		return returnPage;
	}


	/**
	 * @api {GET} /hboneabnormalcust/noneeddealabnormal.do noNeedDealAbnormal()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName noNeedDealAbnormal()
	 * @apiDescription 标记异常：无需处理
	 * @apiParam (请求参数) {String} id 异常客户信息id
	 * @apiParamExample 请求参数示例
	 * id=cfnjz7H3
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"e","data":"M2CnJMHHmk","description":"ZGmlQalwB"}
	 */
	@ResponseBody
	@RequestMapping("/noneeddealabnormal.do")
	public Response<String> noNeedDealAbnormal(String id) {
		DealAbnormalRequest dealAbnormalRequest = new DealAbnormalRequest();
		dealAbnormalRequest.setId(id);
		dealAbnormalRequest.setOperator(getLoginUserId());
		dealAbnormalRequest.setRemark(null);
		dealAbnormalRequest.setDealStatus(DealStatusEnum.NO_NEED_DEAL.getCode());
		return abnormalOuterService.dealAbnormal(dealAbnormalRequest);
	}


	/**
	 * @api {GET} /hboneabnormalcust/associateabnormalhbone.do associateAbnormalHbone()
	 * @apiVersion 1.0.0
	 * @apiGroup HboneAbnormalCustController
	 * @apiName associateAbnormalHbone()
	 * @apiDescription 关联异常主表一账通并更新信息
	 * @apiParam (请求参数) {String} detailId 异常信息明细id
	 * @apiParamExample 请求参数示例
	 * detailId=0ErzMONc
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"L5B","data":"Ot5rGk7C","description":"x6"}
	 */
	@ResponseBody
	@RequestMapping("/associateabnormalhbone.do")
	public Response<String> associateAbnormalHbone(String detailId){
		return abnormalOuterService.associateAbnormalHbone(detailId,getLoginUserId());
	}


	/**
	 * @api {GET} /hboneabnormalcust/validatbeforemerge.do validateBeforeMerge()
	 * @apiVersion 1.0.0
	 * @apiGroup HboneAbnormalCustController
	 * @apiName validateBeforeMerge()
	 * @apiParam (请求参数) {String} detailIds
	 * @apiParamExample 请求参数示例
	 * detailIds=Uc
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {Array} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"q7BCZqPZS","data":["13v2U"],"description":"runmbk"}
	 */
	@ResponseBody
	@RequestMapping("/validatbeforemerge.do")
	public Response<List<String>> validateBeforeMerge(String  detailIds){
		List<String> detailIdList=Lists.newArrayList(detailIds.split(","));
		if(CollectionUtils.isEmpty(detailIdList) || detailIdList.size()<2){
			return Response.fail("请至少选择2条数据再合并");
		}
//		判断②：在当前主表数据下，选中的明细数据量是否≤3
//		若否，则弹窗提示：最多选择3条数据进行合并
		if(detailIdList.size()>3){
			return Response.fail("最多选择3条数据进行合并");
		}
		List<AbnormalRelatedDetailVO>  detailList=abnormalOuterService.queryHboneAbnormalDetailList(detailIdList);
		if(CollectionUtils.isEmpty(detailList)){
			return Response.fail("查询异常");
		}
		//判断 明细列表的  abnormalId 是否一致
		List<String> mainIdList=detailList.stream().map(AbnormalRelatedDetailVO::getAbnormalId).distinct().collect(Collectors.toList());
		if(mainIdList.size()>1){
			return Response.fail("请在同一条一账通客户数据下，选择不同投顾客户再进行合并！\n" +
					"注：不可以跨明细列表合并客户。");
		}
		List<String> custNoList=detailList.stream().map(AbnormalRelatedDetailVO::getCustNo).distinct().collect(Collectors.toList());

		//合并客户相关校验  前置判断
		ReturnMessageDto<String> mergeResp=mergeConscustService.validateCustNoList(custNoList);
		if(!mergeResp.isSuccess()){
			return Response.fail(mergeResp.getReturnMsg());
		}

		return Response.ok(custNoList);

	}



	/**
	 * @api {GET} /hboneabnormalcust/batchnoneeddealabnormal.do batchNoNeedDealAbnormal()
	 * @apiVersion 1.0.0
	 * @apiGroup HkAbnormalCustController
	 * @apiName batchNoNeedDealAbnormal()
	 * @apiDescription 批量设置异常信息为：无需处理
	 * @apiParam (请求参数) {String} ids id列表
	 * @apiParamExample 请求参数示例
	 * ids=kcq8VUywjM
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"XjsRZP","data":"e3gn","description":"tx9RcnmCCv"}
	 */
	@ResponseBody
	@RequestMapping("/batchnoneeddealabnormal.do")
	public Response<String> batchNoNeedDealAbnormal(String  ids ) {
		List<String> idList=Lists.newArrayList(ids.split(","));
		BatchDealAbnormalRequest dealAbnormalRequest = new BatchDealAbnormalRequest();
		dealAbnormalRequest.setIdList(idList);
		dealAbnormalRequest.setOperator(getLoginUserId());
		dealAbnormalRequest.setRemark(null);
		dealAbnormalRequest.setDealStatus(DealStatusEnum.NO_NEED_DEAL.getCode());
		return abnormalOuterService.batchDealAbnormal(dealAbnormalRequest);
	}


	/**
	 * @api {GET} /hboneabnormalcust/flushHboneCancel.do flushHboneCancel()
	 * @apiVersion 1.0.0
	 * @apiGroup HboneAbnormalCustController
	 * @apiName flushHboneCancel()
	 * @apiDescription 已销户的一账通自动解绑
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"equTU1v","data":"GAAr","description":"hczd26a"}
	 */
	@ResponseBody
	@RequestMapping("/flushHboneCancel.do")
	public Response<String> flushHboneCancel(HttpServletRequest request){
		flushUnbindService.executeUnbindCust();
		return Response.ok();
	}


	/**
	 * @api {GET} /hboneabnormalcust/flushCancelByHboneNo.do flushCancelByHboneNo()
	 * @apiVersion 1.0.0
	 * @apiGroup HboneAbnormalCustController
	 * @apiName flushCancelByHboneNo()
	 * @apiDescription 根据一账通号处理 已销户的一账通自动解绑
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"n8xJGISL","data":"ZHa9ed1v","description":"Pyk47cp"}
	 */
	@ResponseBody
	@RequestMapping("/flushCancelByHboneNo.do")
	public Response<String> flushCancelByHboneNo(HttpServletRequest request){
		String hboneNo = request.getParameter("hboneNo");
		if(StringUtil.isEmpty(hboneNo)){
			return  Response.fail("hboneNo不能为空");
		}
		 flushUnbindService.executeUnbindByHboneNo(hboneNo);
		return Response.ok();
	}


	/**
	 * @api {POST} /hboneabnormalcust/createcustinfobyhbone.do createCustInfoByHk()
	 * @apiVersion 1.0.0
	 * @apiGroup HboneAbnormalCustController
	 * @apiName createCustInfoByHk()
	 * @apiDescription 一账通异常客户页，按钮：新增客户
	 * @apiParam (请求参数) {String} id
	 * @apiParamExample 请求参数示例
	 * id=AEWhzovN
	 * @apiSuccess (响应结果) {String} code
	 * @apiSuccess (响应结果) {String} description
	 * @apiSuccess (响应结果) {String} data
	 * @apiSuccessExample 响应结果示例
	 * {"code":"9E","data":"2x","description":"bDNcvrXw"}
	 */
	@ResponseBody
	@PostMapping("/createcustinfobyhbone.do")
	public Response<String> createCustInfoByHk(String id) {
		HboneAbnormalCreateConsCustRequest dealAbnormalRequest = new HboneAbnormalCreateConsCustRequest();
		dealAbnormalRequest.setId(id);
		dealAbnormalRequest.setOperator(getLoginUserId());
		return abnormalOuterService.createCustInfoByHbone(dealAbnormalRequest);
	}


}
