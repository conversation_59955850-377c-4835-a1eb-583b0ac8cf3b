/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.hb.web.controller.prebook;

import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.http.model.HzFundAmtLockConfDTO;
import com.howbuy.crm.prebook.service.PrebookConfigService;
import com.howbuy.crm.prebook.vo.CmPreBookVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (预约配置相关的controller)
 * @date 2024/2/29 10:58
 * @since JDK 1.8
 */

@Slf4j
@Controller
@RequestMapping(value = "/prebookconfig")
public class PrebookConfigController {

    @Autowired
    private PrebookConfigService prebookConfigService;

    /**
     * @api {POST} /prebookconfig/isExistValidHzFundAmtLockCfg.do isExistValidHzFundAmtLockCfg()
     * @apiVersion 1.0.0
     * @apiGroup PrebookConfigController
     * @apiName isExistValidHzFundAmtLockCfg()
     * @apiDescription 根据预约id查询是否存在好臻金额锁定及修改的配置
     * @apiParam (请求参数) {Number} id
     * @apiParam (请求参数) {String} pcode
     * @apiParam (请求参数) {Array} tradeTypeList
     * @apiParam (请求参数) {String} conscustno
     * @apiParam (请求参数) {Array} preBookStateList
     * @apiParam (请求参数) {Array} payStateList
     * @apiParam (请求参数) {Array} tradeStateList
     * @apiParam (请求参数) {String} isdxflag
     * @apiParam (请求参数) {String} sfxg
     * @apiParam (请求参数) {Array} onlineSignFlagList
     * @apiParam (请求参数) {Number} page
     * @apiParam (请求参数) {Number} rows
     * @apiParamExample 请求参数示例
     * conscustno=u0wVUS&pcode=wXQjY7IcTP&tradeTypeList=gQINKB&payStateList=cwX&sfxg=xW&preBookStateList=OvRNr&tradeStateList=jNvCE&id=5356.3627237101&page=1920&rows=5961&isdxflag=LR5mx&onlineSignFlagList=Q16tIj86a
     * @apiSuccess (响应结果) {String} returnCode
     * @apiSuccess (响应结果) {String} returnMsg
     * @apiSuccess (响应结果) {String} returnObject
     * @apiSuccess (响应结果) {Array} returnList
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"Po","returnMsg":"UedWblZn4","returnObject":"NE","returnList":["XqZr"]}
     */
    @ResponseBody
    @PostMapping("/isExistValidHzFundAmtLockCfg.do")
    public ReturnMessageDto<String> isExistValidHzFundAmtLockCfg(CmPreBookVo preBookVo) {
        ReturnMessageDto<List<HzFundAmtLockConfDTO>> result = prebookConfigService.getFundAmtLockConfListByPreId(preBookVo.getId());
        if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getReturnObject())) {
            return ReturnMessageDto.ok("存在有效的金额锁定配置");
        }
        return ReturnMessageDto.fail("不存在有效的金额锁定配置");
    }


}