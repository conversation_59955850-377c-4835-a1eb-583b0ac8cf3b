package com.howbuy.crm.hb.web.controller.conference;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.howbuy.cms.dto.base.SmYxsCoursePlan;
import com.howbuy.cms.dto.base.request.SmYxsCourseQueryInfo;
import com.howbuy.cms.service.base.SmYxsCoursePlanService;
import com.howbuy.common.page.Page;
import com.howbuy.common.page.Pagination;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.base.YesOrNoEnum;
import com.howbuy.crm.hb.constants.MenuButtonConstant;
import com.howbuy.crm.hb.domain.conference.CmConference;
import com.howbuy.crm.hb.domain.conference.CmConferenceFile;
import com.howbuy.crm.hb.domain.conference.SaveConferenceFileVO;
import com.howbuy.crm.hb.enums.ConferenceTypeEnum;
import com.howbuy.crm.hb.service.conference.CmConferenceFileService;
import com.howbuy.crm.hb.service.conference.CmConferenceService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.conference.CmConferenceExportDto;
import com.howbuy.crm.hb.web.util.QRCodeUtil;
import com.howbuy.crm.hbconstant.service.HbConstantService;
import com.howbuy.crm.http.service.WebChatService;
import com.howbuy.crm.nt.base.enums.ConferThemeTypeEnum;
import com.howbuy.crm.nt.base.response.NtReturnMessageDto;
import com.howbuy.crm.nt.conference.dto.CmConferenceDisplayDto;
import com.howbuy.crm.nt.conference.dto.CmConferenceTheme;
import com.howbuy.crm.nt.conference.request.CmConferenceUpdateVo;
import com.howbuy.crm.nt.conference.request.CmConferenceVo;
import com.howbuy.crm.nt.conference.service.CmConferenceBusinessService;
import com.howbuy.crm.nt.param.vo.ParamAuditVo;
import com.howbuy.crm.nt.param.vo.ParamDeleteVo;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.util.FileUtil;
import com.howbuy.crm.util.exception.BusinessException;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageResult;
import crm.howbuy.base.utils.DateUtil;
import crm.howbuy.base.utils.ParamUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:(路演会议 controller	)
 * @param
 * @return
 * @author: haoran.zhang
 * @date: 2023/11/13 20:03
 * @since JDK 1.8
 */
@Slf4j
@Controller
@RequestMapping(value = "/conference")
public class CmConferenceController  extends BaseController {

	private static Logger LOG = LoggerFactory.getLogger(CmConferenceController.class);
	
	@Autowired
	private CmConferenceService cmConferenceService;
	@Autowired
	private CmConferenceFileService cmConferenceFileService;
	@Autowired
	private WebChatService webChatService;

	@Autowired
	private SmYxsCoursePlanService smYxsCoursePlanService;

	@Autowired
	private CmConferenceBusinessService cmConferenceBusinessService;
	@Autowired
	private HbConstantService hbConstantService;

	/**
	 * 会议类型-6-至臻年会
	 */
	private static final String CONFERENCETYPE_ZZ = "6";


	/**
	 * 理财九章 的 会议类型  typeCode=newconferenceOptType
	 */
	public static final String LCJZ_CONFERENCE_TYPE = "newconferenceOptType";



	@Value("${FILE_VISIT_PATH}")
	private String crmFilePath;

	@Value("${HZ_APPLET_QR_CODE_PATH}")
	private String hzAppletQrCodePath;

	/**
	 * 路演会议 路径
	 */
	private static final String CONFERENCE_DIR_PATH = "conference/";

	@RequestMapping(value="/conferenceList.do")
	public ModelAndView conferenceList(){
		ModelAndView mav = new ModelAndView("/conference/conferenceList");

		boolean generateSelf=checkUserOperateAuth(MenuButtonConstant.OPT_GENERATE_SELF_QRCODE,MenuButtonConstant.MENU_CONFERENCE);
		boolean generateAll=checkUserOperateAuth(MenuButtonConstant.OPT_GENERATE_ALL_QRCODE,MenuButtonConstant.MENU_CONFERENCE);
		//11-生成所有二维码  12-生成自己创建二维码
		mav.addObject("generateAll",generateAll);
		mav.addObject("generateSelf",generateSelf);
		mav.addObject("userId",getLoginUserId());
		return mav;
	}


	/**
	 * @api {GET} /conference/viewconference.do viewConference()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceController
	 * @apiName viewConference()
	 * @apiDescription 跳转至查看路演会议详情页面
	 * @apiParam (请求参数) {String} conferenceId 路演会议id
	 * @apiParamExample 请求参数示例
	 * conferenceId=8BD9n4RJ
	 * @apiSuccess (响应结果) {Object} view
	 * @apiSuccess (响应结果) {Object} model
	 * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
	 * @apiSuccess (响应结果) {Boolean} cleared
	 * @apiSuccessExample 响应结果示例
	 * {"view":{},"model":{},"cleared":true,"status":"INSUFFICIENT_STORAGE"}
	 */
	@RequestMapping(value="/viewconference.do")
	public ModelAndView viewConference(String conferenceId){
     	ModelAndView mav = new ModelAndView("/conference/viewConference");
		CmConferenceDisplayDto dto=cmConferenceBusinessService.selectDisplayDtoByConferenceId(conferenceId);


		//翻译字典Map
		Map<String,String> userMap = ConsOrgCache.getInstance().getAllUserMap();
		Map<String,String> orgMap = ConsOrgCache.getInstance().getOrgMap();
		Map<String,String> provCityMap = ConstantCache.getInstance().getProvCityMap();
		translateConferenceDesc(dto,userMap,orgMap,provCityMap);


		//是否理财九章
		boolean lcjzFlag=YesOrNoEnum.YES.getCode().equals(dto.getIslcjz());
		//理财九章-特殊属性：  课程相关处理
		String courseName="";
		if(StringUtil.isNotNullStr(dto.getCourseid()) && lcjzFlag ){
			SmYxsCoursePlan   courseDto=smYxsCoursePlanService.getSmYxsCoursePlanById(Integer.valueOf(dto.getCourseid()));
			courseName=courseDto==null?"":courseDto.getCourseName();
		}
		mav.addObject("lcjzFlag",lcjzFlag);
		mav.addObject("courseName",courseName);

		//类型是否为：至臻年会
		List<String> typeCodeList=dto.getTypeCodeList();
		boolean zzFlag=false;
		if(CollectionUtils.isNotEmpty(typeCodeList) && typeCodeList.contains(CONFERENCETYPE_ZZ)){
			zzFlag=true;
		}
		mav.addObject("zzFlag",zzFlag);


		//会议属性 code 列表
		List<String> themeCodeList= Lists.newArrayList();
		//会议属性详情描述
		String themeDetailDesc="";
		Map<String, List<CmConferenceTheme>> themeMap=dto.getThemeMap();
		if(MapUtils.isNotEmpty(themeMap)){
			themeCodeList.addAll(themeMap.keySet());
            //按 会议属性类型分组 的详情描述
			List<String> descList=Lists.newArrayList();
			themeMap.forEach((key,value)->{
				descList.add(joinSingleThemeTypeDesc(value));
			});
			themeDetailDesc=Joiner.on(";").join(descList);
		}


		mav.addObject("dto",dto);
		mav.addObject("themeCodeList",themeCodeList);
		mav.addObject("themeDetailDesc",themeDetailDesc);
		return mav;
	}


	/**
	 * @api {GET} /conference/toauditview.do toAuditView()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceController
	 * @apiName toAuditView()
	 * @apiDescription 跳转至 审核页面
	 * @apiParam (请求参数) {String} conferenceId 扫码参会id
	 * @apiParamExample 请求参数示例
	 * conferenceId=LNsOS4
	 * @apiSuccess (响应结果) {Object} view
	 * @apiSuccess (响应结果) {Object} model
	 * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
	 * @apiSuccess (响应结果) {Boolean} cleared
	 * @apiSuccessExample 响应结果示例
	 * {"view":{},"model":{},"cleared":true,"status":"REQUEST_TIMEOUT"}
	 */
	@RequestMapping(value="/toauditview.do")
	public ModelAndView toAuditView(String conferenceId){
		ModelAndView mav = new ModelAndView("/conference/auditConference");

		NtReturnMessageDto<CmConferenceDisplayDto> auditResp=cmConferenceBusinessService.selectAuditDtoByConferenceId(conferenceId);
        //服务端校验不通过
		if(!auditResp.isSuccess()){
			throw  new BusinessException(auditResp.getReturnMsg());
		}

		CmConferenceDisplayDto dto=auditResp.getReturnObject();

		//翻译字典Map
		Map<String,String> userMap = ConsOrgCache.getInstance().getAllUserMap();
		Map<String,String> orgMap = ConsOrgCache.getInstance().getOrgMap();
		Map<String,String> provCityMap = ConstantCache.getInstance().getProvCityMap();
		translateConferenceDesc(dto,userMap,orgMap,provCityMap);


		//是否理财九章
		boolean lcjzFlag=YesOrNoEnum.YES.getCode().equals(dto.getIslcjz());
		//理财九章-特殊属性：  课程相关处理
		String courseName="";
		if(StringUtil.isNotNullStr(dto.getCourseid()) && lcjzFlag ){
			SmYxsCoursePlan   courseDto=smYxsCoursePlanService.getSmYxsCoursePlanById(Integer.valueOf(dto.getCourseid()));
			courseName=courseDto==null?"":courseDto.getCourseName();
		}
		mav.addObject("lcjzFlag",lcjzFlag);
		mav.addObject("courseName",courseName);

		//类型是否为：至臻年会
		List<String> typeCodeList=dto.getTypeCodeList();
		boolean zzFlag=false;
		if(CollectionUtils.isNotEmpty(typeCodeList) && typeCodeList.contains(CONFERENCETYPE_ZZ)){
			zzFlag=true;
		}
		mav.addObject("zzFlag",zzFlag);


		//会议属性 code 列表
		List<String> themeCodeList= Lists.newArrayList();
		//会议属性详情描述
		String themeDetailDesc="";
		Map<String, List<CmConferenceTheme>> themeMap=dto.getThemeMap();
		if(MapUtils.isNotEmpty(themeMap)){
			themeCodeList.addAll(themeMap.keySet());
			//按 会议属性类型分组 的详情描述
			List<String> descList=Lists.newArrayList();
			themeMap.forEach((key,value)->{
				descList.add(joinSingleThemeTypeDesc(value));
			});
			themeDetailDesc=Joiner.on(";").join(descList);
		}


		mav.addObject("dto",dto);
		mav.addObject("themeCodeList",themeCodeList);
		mav.addObject("themeDetailDesc",themeDetailDesc);
		return mav;
	}

	/**
	 * 单个主题类型下的列表。 ，分割  拼接 翻译信息字符串
	 * @return
	 */
	private String joinSingleThemeTypeDesc(List<CmConferenceTheme>  themeList){
		if(CollectionUtils.isNotEmpty(themeList)){
			return Joiner.on(",").join(themeList.stream().map(CmConferenceTheme::getThemeDesc).filter(Objects::nonNull).collect(Collectors.toList()));
		}
		return "";
	}

	/**
	 * @api {GET} /conference/downloadXcxQRcode downloadXcxQRcode()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceController
	 * @apiName downloadXcxQRcode()
	 * @apiDescription 生成小程序二维码
	 * @apiSuccess (响应结果) {Object} response
	 * @apiSuccessExample 响应结果示例
	 * null
	 */
	@RequestMapping("/downloadXcxQRcode")
	public void downloadXcxQRcode(HttpServletRequest request, HttpServletResponse response) {
		OutputStream os = null;
		try {
			//会议ID
			String conferenceId = request.getParameter("conferenceId");
			CmConference conference=cmConferenceService.queryCmConferenceInfo(conferenceId);

			Map<String,String> paramMap= new HashMap<>(1);
			paramMap.put("conferenceId",conferenceId);
			//调用TD获取小程序短连接
			String shortUrlForXcx = webChatService.getGeneratedUrl(hzAppletQrCodePath, paramMap);

			String outputName=String.format("会议：%s-小程序二维码.png",conference.getConferencename());
			response.setContentType("application/force-download");
			response.setHeader("Content-disposition", "attachment;filename=" + new String(outputName.getBytes("gb2312"), "ISO8859-1"));
			os = response.getOutputStream();



			String textDesc=String.format("会议：%s",conference.getConferencename());
			BufferedImage image = QRCodeUtil.createQr(shortUrlForXcx,textDesc);

			boolean crateQRCode = ImageIO.write(image, "PNG", os);


		} catch (Exception e) {
			LOG.error(e.getMessage(), e);
		} finally {
			if (os != null) {
				try {
					os.close();
				} catch (IOException e) {
					LOG.error(e.getMessage(), e);
				}
			}
		}
	}

	/**
	 * 生成二维码图片，并写入输出流中
	 * @param text 写入二维码的文本
	 * @param width 二维码宽度
	 * @param height 二维码高度
	 * @param os 输出流
	 * @throws WriterException
	 * @throws IOException
	 */
	private static void generateQRCodeImage(String text, int width, int height, OutputStream os) throws WriterException, IOException {
		QRCodeWriter qrCodeWriter = new QRCodeWriter();
		BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height);
		MatrixToImageWriter.writeToStream(bitMatrix, "PNG", os);
	}


	
	@RequestMapping(value="/addConferenceView.do")
	public String addConferenceView(){
		return "/conference/addConferenceView";
	}

	@RequestMapping(value="/addlcjzconferenceview.do")
	public String addLcjzConferenceView(HttpServletRequest request){
		return "/conference/addLcjzConferenceView";
	}

	/**
	 * 跳转选择课程的弹框
	 * @return
	 */
	@RequestMapping(value="/selectcourse.do")
	public String selectcourse(){
		return "/conference/selectCourse";
	}

	@ResponseBody
	@RequestMapping("/querycourse")
	public Map<String, Object> querycourse(HttpServletRequest request) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		// 获取分页参数
		String cityCode = request.getParameter("cityCode");
		String courseId = request.getParameter("courseId");
		String courseName = request.getParameter("courseName");
		String startDate = request.getParameter("startDate");
		String endDate = request.getParameter("endDate");
		String courseOnlineStatus = request.getParameter("courseOnlineStatus");
		String joinCourse = request.getParameter("joinCourse");
		String pageno = request.getParameter("page");
		String rows = request.getParameter("rows");
		Page page = new Page();
		page.setPerPage(Integer.parseInt(rows));
		page.setPage(Integer.parseInt(pageno));

		SmYxsCourseQueryInfo smYxsCourseQueryInfo = new SmYxsCourseQueryInfo();
		smYxsCourseQueryInfo.setCityCode(cityCode);
		smYxsCourseQueryInfo.setCourseId(courseId);
		smYxsCourseQueryInfo.setCourseName(courseName);
		smYxsCourseQueryInfo.setStartDate(startDate);
		smYxsCourseQueryInfo.setEndDate(endDate);
		smYxsCourseQueryInfo.setCourseOnlineStatus(courseOnlineStatus);
		smYxsCourseQueryInfo.setJoinCourse(ConferenceTypeEnum.getValueByKey(joinCourse));
		Pagination<SmYxsCoursePlan> coursePlanListByPage = smYxsCoursePlanService.getCoursePlanListByPage(page, smYxsCourseQueryInfo);
		log.info("查询课程列表结果：{}", JSON.toJSON(coursePlanListByPage.getResultList()));
		resultMap.put("total", coursePlanListByPage.getPage().getTotal());
		resultMap.put("rows", coursePlanListByPage.getResultList());
		return resultMap;
	}

	/**
	 * 跳转路演会议地图选址弹窗
	 * @return
	 */
	@RequestMapping(value="/selectConferenceAddressByMap.do")
	public String selectConferenceAddressByMap(){
		return "/conference/selectConferenceAddressByMap";
	}

	@RequestMapping(value="/addZzConferenceView.do")
	public String addZzConferenceView(){
		return "/conference/addZzConferenceView";
	}
	
	@RequestMapping(value="/updateConferenceView.do")
	public String updateConferenceView(HttpServletRequest request) throws Exception{
		String conferenceid = request.getParameter("conferenceid");
		CmConference cmConference = cmConferenceService.queryCmConferenceInfo(conferenceid);
		
		String value = "";
		SmYxsCourseQueryInfo smYxsCourseQueryInfo = new SmYxsCourseQueryInfo();
		if(cmConference !=null ){
			if(StringUtil.isNotNullStr(cmConference.getPcodes())){
				cmConference.setPcodes(cmConference.getPcodes()+",");
				value += "1,";
			}
			if(StringUtil.isNotNullStr(cmConference.getCompanys())){
				cmConference.setCompanys(cmConference.getCompanys()+",");
				value += "2,";
			}
			if(StringUtil.isNotNullStr(cmConference.getLines())){
				cmConference.setLines(cmConference.getLines()+",");
				cmConference.setLinenames(cmConference.getLinenames()+",");
				value += "3,";
			}
			
			if(StringUtil.isNotNullStr(cmConference.getConferencedt())){
				
				String defaultdate = DateUtil.getDateFormat(DateUtil.getFormatTimeStr(cmConference.getConferencedt(), null),null);
				request.setAttribute("defaultdate",defaultdate);
			}
			
			if(StringUtil.isNotNullStr(cmConference.getCutoffdt())){
				
				String defaultcutoffdate = DateUtil.getDateFormat(DateUtil.getFormatTimeStr(cmConference.getCutoffdt(), null),null);
				request.setAttribute("defaultcutoffdate",defaultcutoffdate);
			}

			if (StringUtil.isNotNullStr(cmConference.getCourseId())) {
				smYxsCourseQueryInfo.setCourseId(cmConference.getCourseId());
			}
		}
		Page page = new Page();
		Pagination<SmYxsCoursePlan> coursePlanListByPage = smYxsCoursePlanService.getCoursePlanListByPage(page, smYxsCourseQueryInfo);
		// 获取课程名称
		String courseName = coursePlanListByPage.getResultList().get(0).getCourseName();
		request.setAttribute("defaultvalue",value);
		request.setAttribute("courseName",courseName);
		request.setAttribute("cmConference",cmConference);

		//是否为： 理财九章
		List<String> typeCodeList;
		if (!Objects.isNull(cmConference) && StringUtil.isNotNullStr(cmConference.getConferencetype())) {
			typeCodeList= ParamUtil.getParamList(cmConference.getConferencetype(), Lists.newArrayList());
			//获取 翻译信息 字典表配置的： 理财九章的  会议类型
			Map<String,String>  lcjzTypeMap= hbConstantService.getHbConstantMap(LCJZ_CONFERENCE_TYPE);

			boolean  lcjzMark=false;
			//如果有交集 代表为：理财九章类型
			List<String> filterList=typeCodeList.stream().filter(lcjzTypeMap::containsKey).collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(filterList)){
				lcjzMark=true;
			}
			request.setAttribute("lcjzMark",lcjzMark);
		}

		return "/conference/updateConferenceView";
	}

	@ResponseBody
	@RequestMapping("/comboxconferOrg.do")
	public Map<String, Object> queryConferenceOrg() throws Exception {

		Map<String, String> coursePlanCity = smYxsCoursePlanService.getCoursePlanCity("1", "");
		Map<String, Object> resultMap = new HashMap<>();

		try {
			List<Map<String, String>> cityList = new ArrayList<>();
			for (Map.Entry<String, String> entry : coursePlanCity.entrySet()) {
				Map<String, String> city = new HashMap<>();
				city.put("citycode", entry.getValue());
				city.put("cityname", entry.getKey());
				cityList.add(city);
			}
			resultMap.put("rowsData", cityList);
			resultMap.put("resultcode", 1);
		} catch (Exception e) {
			log.error("查询上线城市出错", e);
			resultMap.put("resultcode", 0);
		}
		return resultMap;
	}
	
	@RequestMapping(value="/updateZzConferenceView.do")
	public String updateZzConferenceView(HttpServletRequest request) throws Exception{
		String conferenceid = request.getParameter("conferenceid");
		CmConference cmConference = cmConferenceService.queryCmConferenceInfo(conferenceid);
		
		String value = "";
		
		if(cmConference !=null ){
			if(StringUtil.isNotNullStr(cmConference.getPcodes())){
				cmConference.setPcodes(cmConference.getPcodes()+",");
				value += "1,";
			}
			if(StringUtil.isNotNullStr(cmConference.getCompanys())){
				cmConference.setCompanys(cmConference.getCompanys()+",");
				value += "2,";
			}
			if(StringUtil.isNotNullStr(cmConference.getLines())){
				cmConference.setLines(cmConference.getLines()+",");
				cmConference.setLinenames(cmConference.getLinenames()+",");
				value += "3,";
			}
			
			if(StringUtil.isNotNullStr(cmConference.getConferencedt())){
				
				String defaultdate = DateUtil.getDateFormat(DateUtil.getFormatTimeStr(cmConference.getConferencedt(), null),null);
				request.setAttribute("defaultdate",defaultdate);
			}
			
			if(StringUtil.isNotNullStr(cmConference.getCutoffdt())){
				
				String defaultcutoffdate = DateUtil.getDateFormat(DateUtil.getFormatTimeStr(cmConference.getCutoffdt(), null),null);
				request.setAttribute("defaultcutoffdate",defaultcutoffdate);
			}
		}
		request.setAttribute("defaultvalue",value);
		request.setAttribute("cmConference",cmConference);
		return "/conference/updateZzConferenceView";
	}


	/**
	 * @api {GET} /conference/queryConferenceList.do queryConferenceList()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceController
	 * @apiName queryConferenceList()
	 * @apiDescription 分页查询路演会议列表
	 * @apiParam (请求参数) {String} conferenceid
	 * @apiParam (请求参数) {String} conferencename
	 * @apiParam (请求参数) {String} conferencenameLike
	 * @apiParam (请求参数) {String} conferencetype
	 * @apiParam (请求参数) {Array} conferencetypeList
	 * @apiParam (请求参数) {String} provcode
	 * @apiParam (请求参数) {String} citycode
	 * @apiParam (请求参数) {Number} maxnumber
	 * @apiParam (请求参数) {String} creatdt
	 * @apiParam (请求参数) {String} creater
	 * @apiParam (请求参数) {String} modifydt
	 * @apiParam (请求参数) {String} modifier
	 * @apiParam (请求参数) {String} cutoffdt
	 * @apiParam (请求参数) {String} fileauditstatus
	 * @apiParam (请求参数) {String} courseid
	 * @apiParam (请求参数) {String} islcjz
	 * @apiParam (请求参数) {String} conferencedt
	 * @apiParam (请求参数) {String} conferenceBeginDt
	 * @apiParam (请求参数) {String} conferenceEndDt
	 * @apiParam (请求参数) {String} authConsCode
	 * @apiParam (请求参数) {String} authOthertearm
	 * @apiParam (请求参数) {String} authTeamcode
	 * @apiParam (请求参数) {Array} authOutletcodeList
	 * @apiParam (请求参数) {String} notCanSeeConsNullFlag
	 * @apiParam (请求参数) {Number} page
	 * @apiParam (请求参数) {Number} rows
	 * @apiParamExample 请求参数示例
	 * authTeamcode=bMpT6yF&islcjz=uM2EYk&modifier=GoxC2&conferencedt=ne7C8baI&authConsCode=rgGi&citycode=oTT&maxnumber=3785.2633704757054&conferenceid=yw7tm&conferencename=k&notCanSeeConsNullFlag=WpENVVi5V9&conferenceBeginDt=bxi7FYo&creatdt=494d&modifydt=DGsU&fileauditstatus=8&conferencetype=W&conferenceEndDt=Bar&conferencetypeList=V2zT1x&conferencenameLike=l7skLL3D&authOthertearm=if&rows=8888&authOutletcodeList=&cutoffdt=cJE6e4miK&provcode=FIj0pyA&creater=Uf9O8ewii&page=4135&courseid=a0qF
	 * @apiSuccess (响应结果) {Array} rows
	 * @apiSuccess (响应结果) {Object} rows.themeMap
	 * @apiSuccess (响应结果) {String} rows.conferenceTypeDesc
	 * @apiSuccess (响应结果) {Array} rows.typeCodeList
	 * @apiSuccess (响应结果) {String} rows.createrName
	 * @apiSuccess (响应结果) {String} rows.orgName
	 * @apiSuccess (响应结果) {String} rows.cityName
	 * @apiSuccess (响应结果) {String} rows.provinceName
	 * @apiSuccess (响应结果) {String} rows.audtAdvice
	 * @apiSuccess (响应结果) {String} rows.conferenceid
	 * @apiSuccess (响应结果) {String} rows.conferencename
	 * @apiSuccess (响应结果) {String} rows.conferencedt
	 * @apiSuccess (响应结果) {String} rows.conferencetype
	 * @apiSuccess (响应结果) {String} rows.orgcode
	 * @apiSuccess (响应结果) {String} rows.provcode
	 * @apiSuccess (响应结果) {String} rows.citycode
	 * @apiSuccess (响应结果) {Number} rows.maxnumber
	 * @apiSuccess (响应结果) {String} rows.creatdt
	 * @apiSuccess (响应结果) {String} rows.creater
	 * @apiSuccess (响应结果) {String} rows.modifydt
	 * @apiSuccess (响应结果) {String} rows.modifier
	 * @apiSuccess (响应结果) {String} rows.cutoffdt
	 * @apiSuccess (响应结果) {String} rows.meetingcontents
	 * @apiSuccess (响应结果) {String} rows.uncommitteddt
	 * @apiSuccess (响应结果) {String} rows.noiddt
	 * @apiSuccess (响应结果) {String} rows.noroutingdt
	 * @apiSuccess (响应结果) {String} rows.fileauditstatus
	 * @apiSuccess (响应结果) {String} rows.audittext
	 * @apiSuccess (响应结果) {String} rows.address
	 * @apiSuccess (响应结果) {String} rows.lat
	 * @apiSuccess (响应结果) {String} rows.lng
	 * @apiSuccess (响应结果) {String} rows.courseid
	 * @apiSuccess (响应结果) {String} rows.islcjz
	 * @apiSuccess (响应结果) {String} rows.remark
	 * @apiSuccess (响应结果) {Number} rows.createTimestamp
	 * @apiSuccess (响应结果) {Number} rows.modifyTimestamp
	 * @apiSuccess (响应结果) {String} rows.auditor
	 * @apiSuccess (响应结果) {Number} rows.auditTimestamp
	 * @apiSuccess (响应结果) {String} rows.id
	 * @apiSuccess (响应结果) {String} rows.auditStatus
	 * @apiSuccess (响应结果) {String} rows.lastAuditStatus
	 * @apiSuccess (响应结果) {String} rows.recStat
	 * @apiSuccess (响应结果) {Number} total
	 * @apiSuccessExample 响应结果示例
	 * {"total":8745,"rows":[{"audittext":"a9Ns","lastAuditStatus":"gsBWdixIEm","islcjz":"9TRbt","modifier":"b1uy9swSN","remark":"99V","themeMap":{},"conferencedt":"LoUm","createTimestamp":************,"modifyTimestamp":*************,"cityName":"LxZ5XxirNc","citycode":"WD8r7a","maxnumber":9011.************,"conferenceid":"GbSYnRRM","conferencename":"pZqK","typeCodeList":["t"],"creatdt":"lfgfdj","audtAdvice":"bspys","modifydt":"xgzp5sk","id":"wGX0rbFL","lat":"M8N58","fileauditstatus":"AiXSrWeN","orgName":"ava2i","conferencetype":"Yg9VNZOu","address":"ukGLf","meetingcontents":"WqMIRIZoS","lng":"KtB","auditTimestamp":*************,"uncommitteddt":"vqildyxZ","orgcode":"s","auditor":"5MEOSZNF","noroutingdt":"iYIV6aW","conferenceTypeDesc":"tk","createrName":"uAQ3","noiddt":"xZEVe4GBc","cutoffdt":"hU9JV6eo","provcode":"cIJdAV","creater":"TRS7YR","auditStatus":"lLK","provinceName":"8Cejk9HIL","courseid":"VS6X1pZ7","recStat":"aC6YbT"}]}
	 */
	@ResponseBody
	@RequestMapping("/queryConferenceList.do")
	public PageResult<CmConferenceDisplayDto> queryConferenceList(CmConferenceVo searchVo,HttpServletRequest request){
		//如果orgCode!=0-HOWBUY . 根据权限 查找 列表
		fillAuthVo(request, searchVo);
		//只查询有效性数据
		searchVo.setRecStat(YesOrNoEnum.YES.getCode());
		PageResult<CmConferenceDisplayDto> cmConferencePage=cmConferenceBusinessService.selectPageByVo(searchVo);
		log.info("查询路演管理列表，参数：{},返回条数：{}", JSONObject.toJSONString(searchVo),cmConferencePage.getRows().size());

		//翻译字典Map
		Map<String,String> userMap = ConsOrgCache.getInstance().getAllUserMap();
		Map<String,String> orgMap = ConsOrgCache.getInstance().getOrgMap();
		Map<String,String> provCityMap = ConstantCache.getInstance().getProvCityMap();

		cmConferencePage.getRows().forEach(displayDto->{
			translateConferenceDesc(displayDto,userMap,orgMap,provCityMap);
		});
		return cmConferencePage;
	}





	/**
	 * @description:(路演翻译 )
	 * @param displayDto
	 * @param userMap
	 * @param orgMap
	 * @param provCityMap
	 */
	private void translateConferenceDesc(CmConferenceDisplayDto displayDto,
							   Map<String,String> userMap,
							   Map<String,String> orgMap,
							   Map<String,String> provCityMap){
		displayDto.setCreaterName(userMap.get(displayDto.getCreater()));
		displayDto.setOrgName(orgMap.get(displayDto.getOrgcode()));
		displayDto.setCityName(provCityMap.get(displayDto.getCitycode()));
		displayDto.setProvinceName(provCityMap.get(displayDto.getProvcode()));
	}



	/**
	 * @description:(翻译信息 )
	 * @param conferenceList
	 * @return void
	 * @author: haoran.zhang
	 * @date: 2023/11/13 16:35
	 * @since JDK 1.8
	 */
	private void translateConference(List<CmConferenceDisplayDto> conferenceList){
		if(CollectionUtils.isEmpty(conferenceList)){
			return;
		}
		Map<String,String> userMap = ConsOrgCache.getInstance().getAllUserMap();
		Map<String,String> orgMap = ConsOrgCache.getInstance().getOrgMap();
		Map<String,String> provCityMap = ConstantCache.getInstance().getProvCityMap();
		for(CmConferenceDisplayDto v:conferenceList){
			v.setCreaterName(userMap.get(v.getCreater()));
			v.setOrgName(orgMap.get(v.getOrgcode()));
			v.setCityName(provCityMap.get(v.getCitycode()));
			v.setProvinceName(provCityMap.get(v.getProvcode()));
		}
	}

	/**
	 * @api {POST} /conference/uploadFile.do uploadFile()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceController
	 * @apiName uploadFile()
	 * @apiDescription 路演会议-上传文件
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"7O88","returnMsg":"7n6t","returnObject":"jzFGBp5","returnList":["xdKy"]}
	 */
	@ResponseBody
	@RequestMapping(value="/uploadFile.do",method= RequestMethod.POST)
	public ReturnMessageDto<String> uploadFile(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap(3);
		String conferenceId = request.getParameter("conferenceId");
		try {
			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
			Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

			if(fileMap.size()!=1){
				return  ReturnMessageDto.fail("只能上传一个文件");
			}
			//获取 fileMap 第一个文件
			MultipartFile file = fileMap.entrySet().iterator().next().getValue();
			String originalFileName  = file.getOriginalFilename();
			String suffixName  = FileUtil.getSuffixName(originalFileName);
			//删除原文件：

			SaveConferenceFileVO saveVo = new SaveConferenceFileVO();
			saveVo.setConferenceId(conferenceId);
			saveVo.setFilesSuffix(suffixName);
			saveVo.setFileBytes(file.getBytes());
			saveVo.setOperator(getLoginUserId());
			return cmConferenceFileService.saveFile(saveVo);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return ReturnMessageDto.fail("系统异常！");
		}
	}

	@RequestMapping("/viewUpload")
	@ResponseBody
	public Object viewUpload(String conferenceId){
		CmConferenceFile cmConferenceFile = cmConferenceFileService.getFileByConferenceId(conferenceId);
		cmConferenceFile.setFileName(cmConferenceFile.getFileName());
		Map result = new HashMap(2);
		result.put("fileName", cmConferenceFile.getFileName());
		result.put("filePath",String.join("",
				crmFilePath,FileUtil.SEPARATOR_SLASH, CONFERENCE_DIR_PATH,
				cmConferenceFile.getRelativeFilePath(),
				FileUtil.SEPARATOR_SLASH,
				cmConferenceFile.getFileName()));
		return result;
	}

	@RequestMapping("/passOne")
	@ResponseBody
	public String passOne(String id,HttpServletRequest request){
		//更新主表
		CmConference cmConference = new CmConference();
		cmConference.setConferenceid(id);
		cmConference.setFileAuditStatus(StaticVar.CONFERENCE_AUDIT_PASS_ONE);
		cmConference.setModifier(getLoginUserId());
		cmConferenceService.updateCmConference(cmConference);
		return "success";
	}

	@RequestMapping("/passEnd")
	@ResponseBody
	public String passEnd(String id,HttpServletRequest request){
		//更新主表
		CmConference cmConference = new CmConference();
		cmConference.setConferenceid(id);
		cmConference.setFileAuditStatus(StaticVar.CONFERENCE_AUDIT_PASS_END);
		cmConference.setModifier(getLoginUserId());
		cmConferenceService.updateCmConference(cmConference);
		return "success";
	}

	@RequestMapping("/reject")
	@ResponseBody
	public String reject(String id,HttpServletRequest request){
		//更新主表
		CmConference cmConference = new CmConference();
		cmConference.setConferenceid(id);
		cmConference.setFileAuditStatus(StaticVar.CONFERENCE_AUDIT_REJECT);
		cmConference.setModifier(getLoginUserId());
		cmConference.setAuditText(request.getParameter("auditText"));
		cmConferenceService.updateCmConference(cmConference);
		return "success";
	}

	/**
	 * @api {GET} /conference/saveConference.do saveConference()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceController
	 * @apiName saveConference()
	 * @apiDescription 保存路演会议
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"dO6ynN1Ze","returnMsg":"ePw9","returnObject":"qtXdUcrC","returnList":["8"]}
	 */
	@ResponseBody
	@RequestMapping("/saveConference.do")
	public NtReturnMessageDto<String> saveConference(HttpServletRequest request) {
		String courseId = request.getParameter("courseId");
		String [] conferencetype = request.getParameterValues("conferencetype[]");
		String conferencedt = request.getParameter("conferencedt");
		String cutoffdt = request.getParameter("cutoffdt");
		String conferencename = request.getParameter("conferencename");
		String orgcode = request.getParameter("orgCode");
		String provcode = request.getParameter("provCode");
		String citycode = request.getParameter("cityCode");
		String maxnumber = request.getParameter("maxnumber");
		String pcodes = request.getParameter("pcodes");
		String companys = request.getParameter("companys");
		String productlines = request.getParameter("productlines");
		String meetingcontents = request.getParameter("meetingcontents");
		String uncommitteddt = request.getParameter("uncommitteddt");
		String noiddt = request.getParameter("noiddt");
		String noroutingdt = request.getParameter("noroutingdt");
		String islcjz = request.getParameter("islcjz");

		String address = request.getParameter("address");
		String lat = request.getParameter("lat");
		String lng = request.getParameter("lng");

		CmConferenceUpdateVo cmConference = new CmConferenceUpdateVo();
		cmConference.setOperator(getLoginUserId());
		cmConference.setHoldAuth(
				checkUserOperateAuth(MenuButtonConstant.OPT_CONFERENCE_WITH_AUTH,
						MenuButtonConstant.MENU_CONFERENCE)
		                     );


		cmConference.setConferencedt(DateUtil.StrToNewStringDate(conferencedt));
		cmConference.setConferencename(conferencename);
		cmConference.setCourseid(courseId);
		cmConference.setIslcjz(islcjz);
		StringBuilder sb = new StringBuilder();				
		for (int i=0;i<conferencetype.length;i++){
			sb.append(conferencetype[i]);
			sb.append(",");
		}
		cmConference.setConferencetype(sb.substring(0,sb.length()-1).toString());
		cmConference.setOrgcode(orgcode);
		cmConference.setProvcode(provcode);
		cmConference.setCitycode(citycode);
		cmConference.setCutoffdt(DateUtil.StrToNewStringDate(cutoffdt));
		cmConference.setMaxnumber(new BigDecimal(maxnumber));
		cmConference.setMeetingcontents(meetingcontents);

		if(StringUtils.isNotBlank(uncommitteddt)){
			cmConference.setUncommitteddt(uncommitteddt);
		}
		if(StringUtils.isNotBlank(noiddt)){
			cmConference.setNoiddt(noiddt);
		}
		if(StringUtils.isNotBlank(noroutingdt)){
			cmConference.setNoroutingdt(noroutingdt);
		}

		cmConference.setAddress(address);
		cmConference.setLat(lat);
		cmConference.setLng(lng);

		//主题
		cmConference.setSingleProduct(pcodes);
		cmConference.setManager(companys);
		cmConference.setProductLine(productlines);
//		cmConferenceService.insertCmConference(cmConference,pcodes,companys,productlines);
		return cmConferenceBusinessService.insertFlow(cmConference);
	}

	/**
	 * @api {GET} /conference/updateConference.do updateConference()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceController
	 * @apiName updateConference()
	 * @apiDescription 更新路演会议
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"O","returnMsg":"3aC4kTjag","returnObject":"JAQqX0A","returnList":["VO3Hr"]}
	 */
	@ResponseBody
	@RequestMapping("/updateConference.do")
	public NtReturnMessageDto<String>  updateConference(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		String courseId = request.getParameter("courseId");
		String conferenceid = request.getParameter("conferenceid");
		String[] conferencetype = request.getParameterValues("conferencetype[]");
		String conferencedt = request.getParameter("conferencedt");
		String cutoffdt = request.getParameter("cutoffdt");
		String conferencename = request.getParameter("conferencename");
		String orgcode = request.getParameter("orgCode");
		String provcode = request.getParameter("provCode");
		String citycode = request.getParameter("cityCode");
		String maxnumber = request.getParameter("maxnumber");
		String pcodes = request.getParameter("pcodes");
		String companys = request.getParameter("companys");
		String productlines = request.getParameter("productlines");
		String meetingcontents = request.getParameter("meetingcontents");

		String uncommitteddt = request.getParameter("uncommitteddt");
		String noiddt = request.getParameter("noiddt");
		String noroutingdt = request.getParameter("noroutingdt");

		String address = request.getParameter("address");
		String lat = request.getParameter("lat");
		String lng = request.getParameter("lng");

		if(provcode.length() < 2){
			int prov = Integer.parseInt(provcode);
			if(prov < 10){
				provcode = "0"+prov;
			}
			
		}
		CmConferenceUpdateVo cmConference=new CmConferenceUpdateVo();
		cmConference.setOperator(getLoginUserId());
		cmConference.setHoldAuth(
				checkUserOperateAuth(MenuButtonConstant.OPT_CONFERENCE_WITH_AUTH,
						MenuButtonConstant.MENU_CONFERENCE)
		);

		cmConference.setConferenceid(conferenceid);
		cmConference.setConferencedt(DateUtil.StrToNewStringDate(conferencedt));
		cmConference.setCutoffdt(DateUtil.StrToNewStringDate(cutoffdt));
		cmConference.setConferencename(conferencename);
		cmConference.setCourseid(courseId);
		StringBuilder sb = new StringBuilder();				
		for (int i=0;i<conferencetype.length;i++){
			sb.append(conferencetype[i]);
			sb.append(",");
		}
		cmConference.setConferencetype(sb.substring(0,sb.length()-1).toString());
		cmConference.setOrgcode(orgcode);
		cmConference.setProvcode(provcode);
		cmConference.setCitycode(citycode);
		cmConference.setMaxnumber(new BigDecimal(maxnumber));
		cmConference.setMeetingcontents(meetingcontents);
		cmConference.setUncommitteddt(uncommitteddt);
		cmConference.setNoiddt(noiddt);
		cmConference.setNoroutingdt(noroutingdt);

		cmConference.setAddress(address);
		cmConference.setLat(lat);
		cmConference.setLng(lng);

		//主题
		cmConference.setSingleProduct(pcodes);
		cmConference.setManager(companys);
		cmConference.setProductLine(productlines);
		return  cmConferenceBusinessService.updateFlow(cmConference);
	}

	/**
	 * @api {GET} /conference/validatebeforedelete.do validateBeforeDelete()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceController
	 * @apiName validateBeforeDelete()
	 * @apiDescription 删除会议的前置校验
	 * @apiParam (请求参数) {String} conferenceId 会议ID
	 * @apiParamExample 请求参数示例
	 * conferenceId=BPaiFr3T0
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"gItiGcjB","returnMsg":"Hg","returnObject":"kuxgD6DfH","returnList":["ZMVOk6uwWB"]}
	 */
	@ResponseBody
	@RequestMapping("/validatebeforedelete.do")
	public NtReturnMessageDto<String>  validateBeforeDelete(String conferenceId){
		return  cmConferenceBusinessService.validateBeforeDelete(conferenceId);
	}


	/**
	 * @api {GET} /conference/deleteConferenceInfo.do deleteConferenceInfo()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceController
	 * @apiName deleteConferenceInfo()
	 * @apiDescription 删除路演会议
	 * @apiParam (请求参数) {String} conferenceId
	 * @apiParamExample 请求参数示例
	 * conferenceId=hCZyXkv
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"X0HFkf711","returnMsg":"YI6xT","returnObject":"KYo5Lli","returnList":["1bSMl"]}
	 */
	@ResponseBody
	@RequestMapping("/deleteConferenceInfo.do")
	public NtReturnMessageDto<String> deleteConferenceInfo(String conferenceId){
		ParamDeleteVo paramDeleteVo = new ParamDeleteVo();
		paramDeleteVo.setOperator(getLoginUserId());
		//删除操作： 默认直接 审核通过
		paramDeleteVo.setHoldAuth(true);
		paramDeleteVo.setParamId(conferenceId);
		return  cmConferenceBusinessService.deleteFlow(paramDeleteVo);
	}


	/**
	 * @api {GET} /conference/saveaudit.do saveAudit()
	 * @apiVersion 1.0.0
	 * @apiGroup CmConferenceController
	 * @apiName saveAudit()
	 * @apiDescription 路演会议审核操作
	 * @apiParam (请求参数) {String} paramId
	 * @apiParam (请求参数) {String} auditPass YES,NO
	 * @apiParam (请求参数) {String} auditor
	 * @apiParam (请求参数) {String} auditAdvice
	 * @apiParamExample 请求参数示例
	 * auditPass=YES&auditAdvice=IXGL&auditor=UE1I&paramId=9Dp1Xtat
	 * @apiSuccess (响应结果) {String} returnCode
	 * @apiSuccess (响应结果) {String} returnMsg
	 * @apiSuccess (响应结果) {String} returnObject
	 * @apiSuccess (响应结果) {Array} returnList
	 * @apiSuccessExample 响应结果示例
	 * {"returnCode":"DMg6","returnMsg":"uPaJlLdP1J","returnObject":"KmTSZz","returnList":["WQsBXwLXe"]}
	 */
	@ResponseBody
	@RequestMapping("/saveaudit.do")
	public NtReturnMessageDto<String>  saveAudit(ParamAuditVo auditVo){
//		ParamAuditVo auditVo=new ParamAuditVo();
        auditVo.setAuditor(getLoginUserId());
		return  cmConferenceBusinessService.auditFlow(auditVo);
	}


	@RequestMapping("/exportConference.do")
	public void exportConference(CmConferenceVo searchVo,
								 HttpServletRequest request,
								HttpServletResponse response){

		//如果orgCode!=0-HOWBUY . 根据权限 查找 列表
		fillAuthVo(request, searchVo);
		//只查询有效性数据
		searchVo.setRecStat(YesOrNoEnum.YES.getCode());

		//TODO: 导出  递归分页查询
		searchVo.setPage(1);
		searchVo.setRows(50000);

		PageResult<CmConferenceDisplayDto> cmConferencePage=cmConferenceBusinessService.selectPageByVo(searchVo);
		log.info("查询路演管理列表，参数：{},返回条数：{}", JSONObject.toJSONString(searchVo),cmConferencePage.getRows().size());

		//完整导出 列表
		List<CmConferenceExportDto> exportList=Lists.newArrayList();
		//翻译字典Map
		Map<String,String> userMap = ConsOrgCache.getInstance().getAllUserMap();
		Map<String,String> orgMap = ConsOrgCache.getInstance().getOrgMap();
		Map<String,String> provCityMap = ConstantCache.getInstance().getProvCityMap();
		cmConferencePage.getRows().forEach(displayDto->{
			CmConferenceExportDto exportDto = new CmConferenceExportDto();
			//翻译
			translateConferenceDesc(displayDto,userMap,orgMap,provCityMap);
			BeanUtils.copyProperties(displayDto,exportDto);
			//主题赋值
			if(exportDto.getThemeMap()!=null){
				exportDto.setSingleProductThemeDesc(joinSingleThemeTypeDesc(exportDto.getThemeMap().get(ConferThemeTypeEnum.SINGLE_PRODUCT.getCode())));
				exportDto.setManagerThemeDesc(joinSingleThemeTypeDesc(exportDto.getThemeMap().get(ConferThemeTypeEnum.MANAGER.getCode())));
				exportDto.setProductLineThemeDesc(joinSingleThemeTypeDesc(exportDto.getThemeMap().get(ConferThemeTypeEnum.PRODUCT_LINE.getCode())));
			}
			exportList.add(exportDto);
		});

		
		try {
			// 清空输出流
			response.reset();
			// 设置文件格式和名字
			response.setContentType("multipart/form-data");
			response.setHeader("Content-Disposition",
					"attachment;fileName=" + new String("会议记录.xls".getBytes("gb2312"), "ISO8859-1"));
			ServletOutputStream os = response.getOutputStream();
			
			String [] columnName = { 
			"会议id","会议日期","举办部门","会议名称","会议类型","省份","城市","人数上限",
			"单产品","管理人","产品线","创建人"
			};
			
			String [] beanProperty = { 
			"conferenceid","conferencedt","orgName","conferencename","conferenceTypeDesc","provinceName",
			"cityName","maxnumber","singleProductThemeDesc","managerThemeDesc","productLineThemeDesc","createrName"
			};
			ExcelWriter.writeExcel(os, "会议记录", 0, exportList, columnName, beanProperty);
			os.close(); // 关闭流
		} catch (Exception e) {
			LOG.error("文件导出异常", e);
		}

	}
}
