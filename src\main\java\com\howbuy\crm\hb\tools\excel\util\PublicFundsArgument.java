/**   
* @Title: PublicFundsArgument.java 
* @Package com.hb.crm.web.util.excel.util 
* @Description: TODO(用一句话描述该文件做什么) 
* <AUTHOR>
* @date 2016年5月18日 下午4:58:51 
* @version V1.0   
*/ 
 package com.howbuy.crm.hb.tools.excel.util;

import java.util.LinkedHashMap;
import java.util.Map;

/**
* @ClassName: PublicFundsArgument
* @Description: 主要应用在 公墓报表中 数据导入进行代码转换
* <AUTHOR>
* @date 2016年5月18日 下午4:58:51
*
*/

public class PublicFundsArgument {

   /**
   * @Fields IndependentaAccounting_STATUS : 独立核算状态   0-否， 1-是
   */
   public static final Map<String, String> IA_STATUS = new LinkedHashMap<String, String>();
   static {
       IA_STATUS.put("0", "否");
       IA_STATUS.put("1", "是");
   }

   /**
   * @Fields FUNDS_TYPE_STATUS :基金类型至应用于导入数据（主要是用在公募基金报表菜单中）
   * 储蓄罐--CXG  货币型--7  QDII--9  公募专户--ZH 股票型--1 混合型--3 结构型--t 债券型--5
   */
   public static final Map<String, String> FUNDS_TYPE_STATUS = new LinkedHashMap<String, String>();
   static {
       FUNDS_TYPE_STATUS.put("CXG", "储蓄罐");
       FUNDS_TYPE_STATUS.put("7", "货币型");
       FUNDS_TYPE_STATUS.put("9", "QDII");
       FUNDS_TYPE_STATUS.put("ZH", "公募专户");
       FUNDS_TYPE_STATUS.put("1", "股票型");
       FUNDS_TYPE_STATUS.put("3", "混合型");
       FUNDS_TYPE_STATUS.put("t", "结构型");
       FUNDS_TYPE_STATUS.put("5", "债券型");
       FUNDS_TYPE_STATUS.put("2", "债券型");
       FUNDS_TYPE_STATUS.put("4", "另类投资");
   }

   /**
   * @Fields FUNDS_TYPE_STATUS :保险类型至应用于导入数据（主要是用在公募基金报表菜单中）
   * 封闭式--1 开放式--2 活期保险1--3 活期保险2--4 定期保险1--5 定期保险2--6
   */
   public static final Map<String, String> INSURANCE_TYPE_STATUS = new LinkedHashMap<String, String>();
   static {
       INSURANCE_TYPE_STATUS.put("1", "封闭式");
       INSURANCE_TYPE_STATUS.put("2", "开放式");
       INSURANCE_TYPE_STATUS.put("3", "活期保险1");
       INSURANCE_TYPE_STATUS.put("4", "活期保险2");
       INSURANCE_TYPE_STATUS.put("5", "定期保险1");
       INSURANCE_TYPE_STATUS.put("6", "定期保险2");
   }

   /**
   * @Fields FUNDS_TYPE_STATUS :收入日期判断
   * 前一日--1 当日--2
   */
   public static final Map<String, String> INCOME_DATE_STATUS = new LinkedHashMap<String, String>();
   static {
       INCOME_DATE_STATUS.put("1", "前一日");
       INCOME_DATE_STATUS.put("2", "当日");
   }

}
 