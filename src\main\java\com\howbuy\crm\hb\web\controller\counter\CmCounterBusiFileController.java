package com.howbuy.crm.hb.web.controller.counter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.TypeReference;
import com.howbuy.crm.hb.constants.CrmTradeServerPathConstant;
import com.howbuy.crm.trade.common.constant.ExceptionCodes;
import com.howbuy.crm.trade.common.response.BaseResponse;
import com.howbuy.crm.trade.model.counter.po.CmCounterBusiFile;

/**
 * @Description: Controller
 * @version 1.0
 */
@Controller
@RequestMapping(value = "/counter")
public class CmCounterBusiFileController  extends BaseCounterController {

	
	@RequestMapping(value = "/queryCounterBusiFileList", method = RequestMethod.POST)
    @ResponseBody
    public Map<String, Object> queryCounterBusiFileList(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String id = request.getParameter("id");
        
        // 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("ftid", id);
    	postParam.put("stat", "1");
		BaseResponse<List<CmCounterBusiFile>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_BUSI_FILE_LIST, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterBusiFile>>>(){});
		if (httpRsp.isSuccess()) {
			List<CmCounterBusiFile> list = httpRsp.getData();
			resultMap.put("rows", list);
		}

        return resultMap;
    }
	
}