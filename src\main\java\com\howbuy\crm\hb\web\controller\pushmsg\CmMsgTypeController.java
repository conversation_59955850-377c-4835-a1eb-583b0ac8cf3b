package com.howbuy.crm.hb.web.controller.pushmsg;

import com.howbuy.crm.hb.domain.pushmsg.CmMsgType;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.pushmsg.CmMsgTypeService;

import crm.howbuy.base.constants.StaticVar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2021/1/13 11:13
 */
@Controller
@RequestMapping("/msgType")
public class CmMsgTypeController {

    @Autowired
    private CmMsgTypeService cmMsgTypeService;
    @Autowired
    private CommonService commonService;

    @RequestMapping("/listMsgType")
    public String listMsgType(Map map){
        List<CmMsgType> list = cmMsgTypeService.listAllSubType();
        List<CmMsgType> masterList = cmMsgTypeService.listMasterType(false);
        map.put("list", list);
        map.put("masterList", getComboList(masterList, false));
        return "/pushmsg/listMsgType";
    }

    @RequestMapping("/listMsgType_json")
    @ResponseBody
    public Object listMsgType_json(String dataType){
        if(StaticVar.MSG_DATA_TYPE_MASTER.equals(dataType)) {
            List<CmMsgType> masterList = cmMsgTypeService.listMasterType(false);
            return getComboList(masterList, false);
        }else {
            List<CmMsgType> list = cmMsgTypeService.listAllSubType();
            return list;
        }
    }

    @RequestMapping("/insert")
    @ResponseBody
    public Object insert(HttpServletRequest request){
        String userId = (String)request.getSession().getAttribute("userId");
        CmMsgType cmMsgType = new CmMsgType();
        String dataType = request.getParameter("dataType");
        String msgTypeName = request.getParameter("msgTypeName");
        String isPopup = request.getParameter("isPopup");
        cmMsgType.setDataType(dataType);
        cmMsgType.setMsgTypeName(msgTypeName);
        cmMsgType.setIsPopup("null".equals(isPopup) ? null : isPopup);
        if(StaticVar.MSG_DATA_TYPE_SUB.equals(cmMsgType.getDataType())){
            cmMsgType.setMasterId(request.getParameter("masterId"));
            cmMsgType.setMasterName(request.getParameter("masterName"));
        }
        cmMsgType.setId(commonService.getSeqValue("SEQ_CM_MSG_MANAGE"));
        cmMsgType.setCreator(userId);
        int count = cmMsgTypeService.queryRepeatCount(msgTypeName, dataType, null);
        if(count > 0){
            return "repeat";
        }
        cmMsgTypeService.insert(cmMsgType);
        return "success";
    }

    @RequestMapping("/update")
    @ResponseBody
    public Object update(HttpServletRequest request){
        String userId = (String)request.getSession().getAttribute("userId");
        CmMsgType cmMsgType = new CmMsgType();
        String msgTypeName = request.getParameter("msgTypeName");
        String id = request.getParameter("id");
        if(msgTypeName != null) {
            int count = cmMsgTypeService.queryRepeatCount(msgTypeName, StaticVar.MSG_DATA_TYPE_SUB, id);
            if (count > 0) {
                return "repeat";
            }
        }
        cmMsgType.setId(id);
        cmMsgType.setMsgTypeName(msgTypeName);
        cmMsgType.setMasterId(request.getParameter("masterId"));
        cmMsgType.setMasterName(request.getParameter("masterName"));
        cmMsgType.setIsEnable(request.getParameter("isEnable"));
        cmMsgType.setUpdateMan(userId);
        cmMsgType.setIsPopup(request.getParameter("isPopup"));
        cmMsgTypeService.update(cmMsgType);
        return "success";
    }

    @RequestMapping("/useCount")
    @ResponseBody
    public Object useCount(String id){
        return cmMsgTypeService.useCount(id);
    }

    @RequestMapping("/getSubTypes")
    @ResponseBody
    public Object getSubTypes(String masterId, String hasAll){
        List<CmMsgType> subList = cmMsgTypeService.listSubTypeByMasterId(masterId);
        return getComboList(subList, hasAll != null ? false : true);
    }

    public List<Map> getComboList(List<CmMsgType> masterList, boolean hasAll){
        List<Map> list = new ArrayList<>(masterList.size());
        if(hasAll){
            Map map = new HashMap(3);
            map.put("id", "");
            map.put("text", "全部");
            list.add(map);
        }
        for(CmMsgType cmMsgType : masterList){
            Map map = new HashMap(3);
            map.put("id", cmMsgType.getId());
            map.put("text", cmMsgType.getMsgTypeName());
            list.add(map);
        }
        return list;
    }
}
