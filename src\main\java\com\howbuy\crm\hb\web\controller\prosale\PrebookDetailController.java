package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.base.*;
import com.howbuy.crm.hb.domain.custinfo.CmCustconstant;
import com.howbuy.crm.hb.service.custinfo.CmCustconstantService;
import com.howbuy.crm.hb.service.prosale.ManyCallPreInfoService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.prebook.dto.CmPreBookProductInfo;
import com.howbuy.crm.prebook.dto.CmPrebookBankInfo;
import com.howbuy.crm.prebook.dto.CmPrebookExtra;
import com.howbuy.crm.prebook.dto.PreRelatedOrderDto;
import com.howbuy.crm.prebook.service.CmPrebookExtraService;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.prebook.service.PrebookBusinessService;
import com.howbuy.crm.prebook.service.PrebookConfigService;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import com.howbuy.crm.prosale.dto.Discountapp;
import com.howbuy.crm.prosale.request.GetPrebookInfoByIdRequest;
import com.howbuy.crm.prosale.response.GetPrebookByIdResponse;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import com.howbuy.simu.dto.business.product.RmbhlzjjDto;
import com.howbuy.simu.service.business.product.ComprehensiveService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2019/7/24 10:09
 */
@Slf4j
@Controller
@RequestMapping("/prosale")
public class PrebookDetailController {

    @Autowired
    private PrebookproductinfoService prebookproductinfoService;
    
    @Autowired
    private QueryPreBookService queryPreBookService;
    
    @Autowired
    private CmCustconstantService cmCustconstantService;

	@Autowired
	private ComprehensiveService comprehensiveService;
	
	@Autowired
	private PageVisitLogService pageVisitLogService;

	@Autowired
	private PrebookBasicInfoService prebookBasicInfoService;

	@Autowired
	private PrebookBusinessService prebookBusinessService;

	@Autowired
	private JjxxInfoService jjxxInfoService;


	@Autowired
	private ManyCallPreInfoService manyCallPreInfoService;

	@Autowired
	private CmPrebookExtraService cmPrebookExtraService;

	@Autowired
	private PrebookConfigService prebookConfigService;

    @RequestMapping("/prebookDetail.do")
    public ModelAndView detail(HttpServletRequest request){
        String id = request.getParameter("id");
        Map<String,Object> map = new HashMap<>();
        GetPrebookInfoByIdRequest req = new GetPrebookInfoByIdRequest();
        req.setId(new BigDecimal(id));
        GetPrebookByIdResponse res = queryPreBookService.getPrebookById(req);
		if (res == null || res.getPreinfo() == null) {
			log.info("prebookDetail,map:{}", JSON.toJSONString(map));
			return new ModelAndView("prosale/prebookDetail","map",map);
		}

		String userId = (String) request.getSession().getAttribute("userId");
		String ip = getIpAddr(request);
		CmPrebookproductinfo pinfo = res.getPreinfo();
		PageVisitLog pageVisitLog = new PageVisitLog();
		pageVisitLog.setConscustno(pinfo.getConscustno());
		pageVisitLog.setUserId(userId);
		pageVisitLog.setVisitUrl(request.getRequestURI());
		pageVisitLog.setOperation("预约详情");
		pageVisitLog.setVisitTime(new Date());
		pageVisitLog.setPreid(pinfo.getId());
		pageVisitLog.setIp(ip);
		pageVisitLogService.recordLog(pageVisitLog);
		Map<String, Object> param = new HashMap<>();
		param.put("opttype", StaticVar.PREBOOK_LOG_TRADECONFIRM);
		param.put("preid", id);
		//交易确认人
		map.put("tradecheckman", prebookproductinfoService.getCheckManAndTime(param));
		if (StaticVar.REPEATBUY_YES.equals(pinfo.getSpectradetype())) {
			map.put("prebookcheckman", "sys");
			map.put("paycheckman", "sys");
		} else {
			param.put("opttype", StaticVar.PREBOOK_LOG_PRECOMFIRM);
			//预约确认人
			map.put("prebookcheckman", prebookproductinfoService.getCheckManAndTime(param));
			param.put("opttype", StaticVar.PREBOOK_LOG_PAYCHECK);
			//打款确认人
			map.put("paycheckman", prebookproductinfoService.getCheckManAndTime(param));
		}
		//撤销状态的预约显示撤销人和时间，否则为空
		map.put("cancelman", "");
		if (StaticVar.PREBOOK_STATES_HAS_CANCEL.equals(pinfo.getPrebookstate())) {
			List<String> listcancels = prebookproductinfoService.getCancelManAndTime(param);
			if (listcancels != null && listcancels.size() > 0) {
				map.put("cancelman", listcancels.get(0));
			}
		}
		map.put("preid", id);

		JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(pinfo.getPcode(), false);
		map.put("pname", "");
		if (jjxx != null) {
			map.put("pname", jjxx.getJjjc());
		}
		// 是否分次call产品：1-是 0-否
		String isfccall = YesOrNoEnum.NO.getCode();
		ReturnMessageDto<String>  fcclFlagResp=prebookConfigService.getFcclFlag(pinfo.getPcode());
		if(fcclFlagResp.isSuccess() &&  YesOrNoEnum.YES.getCode().equals(fcclFlagResp.getReturnObject())){
			isfccall = YesOrNoEnum.YES.getCode();
		}

		map.put("isfccall", isfccall);
		//创建人
		String displayCreator = ConsOrgCache.getInstance().getAllUserMap().get(pinfo.getRealcreator());
		String displayCretime = null;
		try {
			displayCretime = String.format("%s:%s:%s",
					pinfo.getCretime().substring(0, 2), pinfo.getCretime().substring(2, 4), pinfo.getCretime().substring(4, 6));
		} catch (Exception e) {
			//忽略赋值问题
		}
		map.put("realcreatorInfo", String.format("%s（%s %s）",
				displayCreator == null ? "" : displayCreator,
				pinfo.getCredt(), displayCretime == null ? "" : displayCretime));//手续费
		map.put("ztfee", null);
		//处理中台费用
		dealZtFee(pinfo, map);
		ConstantCache constantCache = ConstantCache.getInstance();
		map.put("bankacct", null);
		if (StringUtil.isNotNullStr(pinfo.getBankacct())) {
			Map<String, String> paramconstant = new HashMap<String, String>();
			paramconstant.put("custno", pinfo.getConscustno());
			CmCustconstant constant = cmCustconstantService.getCmCustconstant(paramconstant);
			map.put("bankacct", ObjectUtils.getEncryptValue(request, StaticVar.ENCRYPT_BANKNO, pinfo.getBankacct(), constant.getConscode(), null));
		}
		map.put("bankcodename", pinfo.getBankname());
		boolean dxflag = queryPreBookService.isDxflag(pinfo.getConscustno(), pinfo.getPcode());
		//当产品=代销且无关联中台订单时，显示空
		if (dxflag && StringUtils.isBlank(pinfo.getDealno())) {
			map.put("bankacct", null);
			map.put("bankcodename", null);
		}

		map.put("conscustname", pinfo.getConscustname());
		map.put("currency", constantCache.getVal("currencys", pinfo.getCurrency()));
		map.put("tradetype", pinfo.getTradeType());

		//处理金额份额
		dealAmtVol(pinfo, map);
		map.put("expectpayamtdt", pinfo.getExpectpayamtdt());//预计打款日期
		map.put("expecttradedt", pinfo.getExpecttradedt());//预计交易日期
		map.put("realpayamtdt", pinfo.getRealpayamtdt());//实际打款日期

		map.put("fee", pinfo.getFee());//手续费
		map.put("precode", pinfo.getPcode());
		Discountapp discountapp = pinfo.getDiscountapp();
		//处理折扣信息
		dealDiscountInfo(discountapp, pinfo.getInterestComment(), map, constantCache, pinfo);

		//中台订单号
		// 海外中台的订单号
		PreRelatedOrderDto outerInfo =prebookBusinessService.getOuterOrderInfo(new BigDecimal(id));
		map.put("dealno", outerInfo==null?"":outerInfo.getDealNo());
		//默认没有
		map.put("hassno", "0");
		if (StringUtil.isNotNullStr(pinfo.getSno())) {
			map.put("hassno", "1");
			map.put("tzqsr", pinfo.getTzqsr());
			map.put("yjdqr", pinfo.getYjdqr());
		}

		//香港产品 认购单信息
		CmPreBookProductInfo prebookInfo = prebookBasicInfoService.getPreBookById(new BigDecimal(id));
		map.put("preBookInfo", prebookInfo);
		map.put("tradetype", prebookInfo.getTradeType());
		//货币
		map.put("currencyDesc", CurrencyEnum.getDescription(prebookInfo.getCurrency()));


		if (StaticVar.FCCL_YES.equals(isfccall)) {
			// 分次call产品的手续费标准
			map.put("feeRateMethod", manyCallPreInfoService.getFeeRateMethodName(pinfo.getId()));
		}

		//香港产品
		if (PreBookArchTypeEnum.HW.getCode().equals(prebookInfo.getArchType())) {
			//是否线上签约
			PreSignFlagEnum signFlagEnum = PreSignFlagEnum.getEnum(prebookInfo.getOnlineSignFlag());
			//支付方式
			List<String> paymentList = PaymentModeEnum.getPaymentEnumListByValue(prebookInfo.getPaymentMode())
					.stream().map(PaymentModeEnum::getDesc).collect(Collectors.toList());

			//赎回方向
			List<String> redeemList = RedeemDirectEnum.getRedeemEnumListByValue(prebookInfo.getRedeemDirection())
					.stream().map(RedeemDirectEnum::getDesc).collect(Collectors.toList());

			map.put("paymentDesc", String.join(",", paymentList));
			map.put("redeemDesc", String.join(",", redeemList));
			map.put("divModeDesc", DivModeEnum.getName(prebookInfo.getDivMode()));
			map.put("onlineSignFlagDesc", PreSignFlagEnum.getDescription(prebookInfo.getOnlineSignFlag()));
			//卡信息列表
			List<CmPrebookBankInfo> hkBankList = prebookBasicInfoService.getHkBankAcctInfoList(new BigDecimal(id));
			map.put("hkBankList", hkBankList);
		}

		//预约额外属性 取值
		CmPrebookExtra extra= cmPrebookExtraService.selectExtraByPreId(prebookInfo.getId());
		map.put("confirmPayRemark", extra==null?null:extra.getConfirmPayRemark());

		log.info("prebookDetail,map:{}", JSON.toJSONString(map));
        return new ModelAndView("prosale/prebookDetail","map",map);
    }

	/**
     * 处理中台费用
     * @param pinfo
     * @param map
     */
    private void dealZtFee(CmPrebookproductinfo pinfo,Map<String,Object> map){
    	if(pinfo.getPaystate().equals(StaticVar.PAY_STATES_HAS_CONFIRM)) {
        	map.put("ztfee","0.00");
        	if(StringUtil.isNotNullStr(pinfo.getZtfee())){
        		map.put("ztfee",pinfo.getZtfee());
        	}
        }
    }
    
    /**
     * 处理金额份额
     * @param pinfo
     * @param map
     */
    private void dealAmtVol(CmPrebookproductinfo pinfo,Map<String,Object> map){
    	if(!StaticVar.PREBOOK_TRADE_TYPE_SALE.equals(pinfo.getTradeType())){
        	map.put("buyamt", pinfo.getBuyamt() == null ? null : pinfo.getBuyamt().divide(new BigDecimal(10000)));
			map.put("realpayamt", pinfo.getRealpayamt() == null ? null : pinfo.getRealpayamt().divide(new BigDecimal(10000)));//实际购买金额

			// 申请金额
			BigDecimal appAmt = pinfo.getAppAmt();
			map.put("appAmt", appAmt == null ? null : appAmt.divide(new BigDecimal(10000)));
		} else {
			map.put("sellvol", pinfo.getSellvol());//赎回份额
			map.put("sellamt", pinfo.getSellAmt());//赎回金额
			//赎回方式赋值
			map.put("redeemMode", RedeemModeEnum.getDescription(pinfo.getRedeemMode()) );
		}

		// 认缴金额
		map.put("totalAmt","--");
		if (StaticVar.YES.equals(map.get("isfccall"))) {
			BigDecimal totalAmt = pinfo.getTotalamt();
			map.put("totalAmt", (totalAmt == null ? BigDecimal.ZERO :
					totalAmt.divide(new BigDecimal(10000))).setScale(6, BigDecimal.ROUND_DOWN).toPlainString() + "万元");
		}
    }
    
    /**
     * 处理折扣信息
     * @param discountapp
     * @param map
     * @param constantCache
     * @param pinfo
     */
    private void dealDiscountInfo(Discountapp discountapp, String interestComment,Map<String,Object> map,ConstantCache constantCache,CmPrebookproductinfo pinfo){
    	if(discountapp != null){
        	String staffRelation = null;
            //当折扣类型 = 员工及其亲属福利 时，显示申请折扣时录入的信息；当折扣类型 != 员工及其亲属福利 时，显示空
            if("6".equals(discountapp.getDiscountType())){
            	staffRelation = discountapp.getStaffRelation();
            }
            map.put("staffRelation",  constantCache.getVal("staffrelations", staffRelation));//员工关系
            map.put("discountType", constantCache.getVal("discountTypes", discountapp.getDiscountType()));//折扣类型
            map.put("discountWay",  constantCache.getVal("discountWays", discountapp.getDiscountWay()));//折扣方式
            map.put("discountStyle", constantCache.getVal("discountStyles", discountapp.getDiscountStyle()));//折扣形式
            map.put("isRefund",  constantCache.getVal("isRefunds", discountapp.getIsrefund()));//是否报销
            map.put("discountRate",  discountapp.getDiscountRate());//折扣率
            map.put("discountTaxType",  constantCache.getVal("taxTypes", discountapp.getDiscountTaxType()));//折扣税类型
            map.put("discountReason",  discountapp.getDiscountReason());//折扣理由
            map.put("afterTaxAmt",   discountapp.getAfterTaxAmt());//税后折扣金额
            map.put("batchflag", discountapp.getBatchflag());
            map.put("afterTaxAmtRMB",  Util.ObjectToBigDecimal(discountapp.getAfterTaxAmt()).multiply(getExchangerate(pinfo)));//税后折扣金额
            map.put("beforeTaxAmt",   discountapp.getBeforeTaxAmt());//税前折扣金额
            map.put("befTaxAmtAdjust",   discountapp.getBefTaxAmtAdjust());//税前折扣金额（调整）
            map.put("disremarks", discountapp.getRemarks());//备注
			map.put("discountStateVal",  constantCache.getConstantKeyVal("discountStates").get(discountapp.getDiscountState()));
			map.put("disChecker",  ConsOrgCache.getInstance().getAllUserMap().get(discountapp.getChecker()));
			map.put("interestComment",  interestComment);
        }else{
        	map.put("staffRelation",null);//员工关系
        	map.put("discountType", null);//折扣类型
            map.put("discountWay",  null);//折扣方式
            map.put("discountStyle", null);//折扣形式
            map.put("isRefund",  null);//是否报销
            map.put("discountRate",  null);//折扣率
            map.put("discountTaxType",  null);//折扣税类型
            map.put("discountReason",  null);//折扣理由
            map.put("afterTaxAmt",   null);//税后折扣金额
            map.put("batchflag", null);
            map.put("afterTaxAmtRMB",  null);//税后折扣金额
            map.put("beforeTaxAmt",   null);//税前折扣金额
            map.put("befTaxAmtAdjust",   null);//税前折扣金额（调整）
            map.put("disremarks", null);//备注
			map.put("discountStateVal",  null);
			map.put("disChecker",  null);
			map.put("interestComment",  null);
        }
    }
    
    /**
     * 获取汇率
     * @param pinfo
     * @return
     */
    private BigDecimal getExchangerate(CmPrebookproductinfo pinfo){
    	BigDecimal exchangerate = new BigDecimal("1.0");
        if(pinfo.getBuyamtRmb() != null  && !"156".equals(pinfo.getCurrency())){
            exchangerate = pinfo.getBuyamtRmb().divide(pinfo.getBuyamt(),4,BigDecimal.ROUND_UP);
        }else{
            RmbhlzjjDto rmbhlzjjDto = comprehensiveService.getRmbhlzjj(pinfo.getRatedt() == null ? null : DateTimeUtil.strToDate(pinfo.getRatedt()),pinfo.getCurrency());
			if (rmbhlzjjDto != null) {
				exchangerate = new BigDecimal(rmbhlzjjDto.getZjj());
			}
        }
        return exchangerate;
    }
    
    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
}
