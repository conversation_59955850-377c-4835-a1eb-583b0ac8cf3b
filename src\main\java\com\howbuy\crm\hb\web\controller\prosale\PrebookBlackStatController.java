package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.fastjson.JSON;
import com.howbuy.crm.base.YesOrNoEnum;
import com.howbuy.crm.hb.domain.prosale.Prebookproductinfo;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prebook.service.PrebookBusinessService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description: TODO
 * @reason:
 * @Date: 2019/8/5 10:09
 */
@Slf4j
@Controller
@RequestMapping(value = "/prosale")
public class PrebookBlackStatController {

    @Autowired
    private PrebookproductinfoService prebookproductinfoService;
	@Autowired
	private JjxxInfoService jjxxInfoService;

	@Autowired
	private PrebookBusinessService prebookBusinessService;

    @RequestMapping("/blackPrebookList.do")
    public ModelAndView blackPrebookList(HttpServletRequest request){
        Map<String,Object> map = new HashMap<>();
        return new ModelAndView("prosale/listBlackPrebook","map",map);
    }


	/**
	 * 设置是否可线上下单
	 * @param listdata
	 */
	private void setOnlineOrder(List<Prebookproductinfo> listdata){
		//预约Id列表
		List<BigDecimal> preIdList = listdata.stream().map(Prebookproductinfo::getId).collect(Collectors.toList());
		Map<String,String> onlineInfoMap = prebookBusinessService.getOnLineOrderInfoMap(preIdList);
		for(Prebookproductinfo info : listdata){
			// [是否可线上下单]
			String inlineOrder = onlineInfoMap.get(info.getId().toString());
			info.setIslineorder(YesOrNoEnum.YES.getCode().equals(inlineOrder)?YesOrNoEnum.YES.getDescription():YesOrNoEnum.NO.getDescription());
		}
	}
    
    @ResponseBody
	@RequestMapping("/listBlackPrebookByPage_json.do")
	public Map<String, Object> listBlackPrebookByPage_json(HttpServletRequest request, HttpServletResponse response) throws Exception {
		// 设置查询参数
		Map<String, String> param = new HashMap<String, String>();
		// 获取分页参数
		param = new ParamUtil(request).getParamMap();
		String custname = request.getParameter("custname");
		String startdt = request.getParameter("startdt");
		String enddt = request.getParameter("enddt");
		String pcode = request.getParameter("pcode");
		String prebookStates = request.getParameter("prebookStates");
		String payStates = request.getParameter("payStates");
		String orgCode = request.getParameter("orgCode");
		String consCode = request.getParameter("consCode");
		String tradetype = request.getParameter("tradeTypes");
		String selecttype = request.getParameter("selecttype");
		
		//查询条件（客户名）不为空，增增加客户名参数	
		if(StringUtil.isNotNullStr(custname)){
			param.put("custname", custname);
		}else{
			param.put("custname", null);
		}

		//查询条件（客户名）不为空，增增加客户名参数
		if(StringUtil.isNotNullStr(tradetype)){
			param.put("tradeTypes", tradetype);
		}else{
			param.put("tradeTypes", null);
		}
		
		//如果查询条件（录入日期）开始时间不为空，则增加录入日期查询参数
		if(StringUtil.isNotNullStr(startdt)){
			param.put("startdt", startdt);
		}else{
			param.put("startdt", null);
		}
		
		if(StringUtil.isNotNullStr(enddt)){
			param.put("enddt", enddt);
		}else{
			param.put("enddt", null);
		}
		
		//如果查询条件（产品代码）不为空，则增加产品代码查询参数
		if(StringUtil.isNotNullStr(pcode)){
			param.put("pcode", pcode);
		}else{
			param.put("pcode", null);
		}
		
		//如果查询条件（打款状态）不为空，则增加打款状态查询参数
		if(StringUtil.isNotNullStr(payStates)){
			param.put("payStates", payStates);
		}else{
			param.put("payStates", null);
		}
		//如果查询条件（预约状态）不为空，则增加预约状态查询参数
		if(StringUtil.isNotNullStr(prebookStates)){
			param.put("prebookStates", prebookStates);
		}else{
			param.put("prebookStates", null);
		}
		//1：查询重复的；0：查询全部
		if(StringUtil.isNotNullStr(selecttype)){
			param.put("selecttype", selecttype);
		}else{
			param.put("selecttype", null);
		}
		
		if(StringUtil.isNotNullStr(consCode)){
			param.put("creator", consCode);
		}else{
			if(!"0".equals(orgCode)){
				param.put("creators", Util.getSubQueryByOrgCode(orgCode));
			}
		}
		
		// 判断常量表中合规标识：true启用，false停用
		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
		boolean roleCPFlag = false;
		if (cacheMap != null && !cacheMap.isEmpty()) {
			roleCPFlag = JSON.toJSONString(cacheMap).contains("true");
		}

		// 判断登录人员的角色中是否包括“合规人员”角色
		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
		if (roleCPFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
			param.put("hascp", "true");
		}
		PageData<Prebookproductinfo> pageData = prebookproductinfoService.listPrebookproductinfoBlackPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<Prebookproductinfo> listdata= pageData.getListData();
		List<Prebookproductinfo> list = new ArrayList<Prebookproductinfo>();
		for(Prebookproductinfo model : listdata){
			model.setTradeTypeVal(ConstantCache.getInstance().getConstantKeyVal("tradeTypes").get(model.getTradeType()));
			JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(model.getFundcode(), false);
			if (jjxx1 != null) {
				model.setFundname(jjxx1.getJjjc());
			}
			model.setPaymenttype(ConstantCache.getInstance().getConstantKeyVal("paymenttype").get(model.getPaymenttype()));
			//赎回至：直销直接显示“银行卡”，代销，存在中台订单，显示中台的赎回方式，不存在显示空
        	String saleto = "";
        	if(StaticVar.PREBOOK_TRADE_TYPE_SALE.equals(model.getTradeType())){
	        	if("1".equals(model.getIszx())){
	        		saleto = "银行卡";
	        	}else{
	        		if(StringUtil.isNotNullStr(model.getDealno())){
	        			saleto = ConstantCache.getInstance().getVal("redeemdirection", model.getRedeemdirection());
	        		}
	        	}
        	}
        	model.setRedeemdirection(saleto);
			model.setAppdate(model.getAppdate());
			model.setApptime(model.getApptime());
			model.setPrebookstateval(ConstantCache.getInstance().getConstantKeyVal("prebookStates").get(model.getPrebookstate()));
			model.setPaystateval(ConstantCache.getInstance().getConstantKeyVal("payStates").get(model.getPaystate()));
			model.setOrderstateval(ConstantCache.getInstance().getConstantKeyVal("dealorderStatus").get(model.getOrderstate()));
			model.setPretypeval(ConstantCache.getInstance().getConstantKeyVal("pretype").get(model.getPretype()));
			if(model.getDiscountstate() != null){
				model.setDiscountstateval(ConstantCache.getInstance().getConstantKeyVal("discountStates").get(model.getDiscountstate()));
			}else{
				model.setDiscountstate(StaticVar.DISCOUNT_STATES_NOT_APPLY);
				model.setDiscountstateval(ConstantCache.getInstance().getConstantKeyVal("discountStates").get(model.getDiscountstate()));
			}
			model.setTradestateval(ConstantCache.getInstance().getConstantKeyVal("tradestate").get(model.getTradestate()));
			
			//设置投顾code为原始的投顾（creator）
			model.setConsname(ConsOrgCache.getInstance().getAllConsMap().get(model.getCreator()));
			model.setOutletName(ConsOrgCache.getInstance().getOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(model.getCreator())));
			model.setCalmdatetime(model.getCalmdatetime());
			model.setZczmstate(prebookproductinfoService.getZczmstate(model.getHboneno(), model.getFundcode(), model.getConscustno()));
			BigDecimal fee = null;
        	//直销取CRM手续费
        	if("1".equals(model.getIszx())){
        		fee = model.getFee();
				if(fee == null){
					fee = new BigDecimal(0);
				}
        	}else{
        		//中台打款状态等于到账确认后取中台手续费
        		if("1".equals(model.getZtPayConfirm())){
					fee = model.getRealfee();
					if(fee == null){
						fee = new BigDecimal(0);
					}
				}else {
					//中台打款状态不等于到账确认且预约打款状态等于到账确认或已打款，显示CRM手续费
					if(StaticVar.PAY_STATES_HAS_CONFIRM.equals(model.getPaystate()) || StaticVar.PAY_STATES_HAS_PAY.equals(model.getPaystate())){
						fee = model.getFee();
						if(fee == null){
							fee = new BigDecimal(0);
						}
					}
				}
        	}
        	model.setRealfee(fee);
			
			list.add(model);
		}


		// [是否可线上下单]
		setOnlineOrder(listdata);

		resultMap.put("rows", list);
		return resultMap;
	}
    
    @ResponseBody
    @RequestMapping("/changeBlackPrebook.do")
    public String changeBlackPrebook(HttpServletRequest request,
                                   HttpServletResponse response) {
        String id = request.getParameter("id");
        String opt = request.getParameter("opt");
        String result = "";
        User userlogin = (User) request.getSession().getAttribute("loginUser");
        prebookproductinfoService.changeBlackPrebook(id,opt,userlogin.getUserId());
        result = "1";
        return result;
    }
	@ResponseBody
    @RequestMapping("/procRuleBlackPrebook.do")
    public String procRuleBlackPrebook(HttpServletRequest request,
                                   HttpServletResponse response) {
        String conscustno = request.getParameter("conscustno").trim();
        String fundcode = request.getParameter("fundcode").trim();
        String tradetype = request.getParameter("tradetype").trim();
        String result = "1";
		try {
			prebookproductinfoService.procRuleBlackPrebook(conscustno, fundcode, tradetype);
		} catch (Exception e) {
			log.error("处理黑名单预约单异常", e);
			result = "0";
		}
		
        return result;
    }
}
