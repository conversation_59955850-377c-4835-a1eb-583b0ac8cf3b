package com.howbuy.crm.hb.web.controller.custinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.crm.base.CurrencyEnum;
import com.howbuy.crm.base.YesNoEnum;
import com.howbuy.crm.base.YesOrNoEnum;
import com.howbuy.crm.cashbalance.CmQueryHkCashBalanceService;
import com.howbuy.crm.cashbalance.request.CmQueryHkCashBalanceRequest;
import com.howbuy.crm.cashbalance.response.CmQueryHkCashBalanceResponse;
import com.howbuy.crm.conscust.dto.ConscustInfoDomain;
import com.howbuy.crm.conscust.request.QueryConscustInfoRequest;
import com.howbuy.crm.conscust.response.QueryConscustInfoResponse;
import com.howbuy.crm.conscust.service.QueryConscustInfoService;
import com.howbuy.crm.hb.constants.CommonConstant;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.custinfo.CrmBalanceBean;
import com.howbuy.crm.hb.domain.custinfo.CrmDealOrderBean;
import com.howbuy.crm.hb.domain.custinfo.SubCrmBalanceBean;
import com.howbuy.crm.hb.domain.hkconscust.HkConscustVO;
import com.howbuy.crm.hb.domain.insur.BxBalanceDetail;
import com.howbuy.crm.hb.domain.prosale.*;
import com.howbuy.crm.hb.enums.ChartTypeEnum;
import com.howbuy.crm.hb.enums.HmcpxEnum;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.insur.CmBxPrebookBuyinfoService;
import com.howbuy.crm.hb.service.outerservice.CrmAccountOuterService;
import com.howbuy.crm.hb.service.prosale.CustprivatefundtradeService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.service.prosale.ProductinfoService;
import com.howbuy.crm.hb.service.prosale.ZxPrivateTradeFundService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.custinfo.*;
import com.howbuy.crm.hb.web.support.custinfo.HighDealOrderSupport;
import com.howbuy.crm.hb.web.support.custinfo.SequentialDataDTO;
import com.howbuy.crm.hb.web.support.custinfo.SequentialResponse;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.hb.web.vo.SequentialVO;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.CmCustSourceCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.cache.JgjjCache;
import com.howbuy.crm.page.core.domain.PageCmJjxx1Jgflb;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.prebook.service.HkBalanceFactorService;
import com.howbuy.crm.prosale.dto.Custprivatefundtrade;
import com.howbuy.crm.prosale.request.BalanceFactorListRequest;
import com.howbuy.crm.prosale.response.BalanceFactorListResponse;
import com.howbuy.crm.util.MainLogUtils;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.simu.dto.base.product.SmcccpJbxxDto;
import com.howbuy.simu.dto.business.product.RmbhlzjjDto;
import com.howbuy.simu.service.base.product.SmccProductJbxxService;
import com.howbuy.simu.service.business.product.ComprehensiveService;
import com.howbuy.tms.high.batch.facade.query.queryhighfundarrivalproof.QueryHighFundArrivalProofFacade;
import com.howbuy.tms.high.batch.facade.query.queryhighfundarrivalproof.QueryHighFundArrivalProofRequest;
import com.howbuy.tms.high.batch.facade.query.queryhighfundarrivalproof.QueryHighFundArrivalProofResponse;
import com.howbuy.tms.high.batch.facade.query.queryhighfundarrivalproof.bean.QueryHighFundArrivalProofCondition;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmFacade;
import com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmRequest;
import com.howbuy.tms.high.orders.facade.search.querybalancevoldtlforcrm.QueryBalanceVolDtlForCrmResponse;
import com.howbuy.tms.high.orders.facade.search.querycustfunddiv.QueryCustFundDivResponse;
import com.howbuy.tms.high.orders.facade.search.querydealorder.QueryDealOrderFacade;
import com.howbuy.tms.high.orders.facade.search.querydealorder.QueryDealOrderRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorder.QueryDealOrderResponse;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListFacade;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListRequest;
import com.howbuy.tms.high.orders.facade.search.querydealorderlist.QueryDealOrderListResponse;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolFacade;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolRequest;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.QueryCustRepurchaseProtocolResposne;
import com.howbuy.tms.high.orders.facade.search.queryrepurchaseprotocol.bean.CustRepurchaseProtocolBean;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.enums.CrmCustInvestTypeEnum;
import crm.howbuy.base.enums.DisChannelCodeEnum;
import crm.howbuy.base.enums.DisCodeEnum;
import crm.howbuy.base.utils.*;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.Border;
import jxl.format.BorderLineStyle;
import jxl.format.Colour;
import jxl.format.VerticalAlignment;
import jxl.format.*;
import jxl.write.*;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @reason:
 * @Date: 2020/4/8 10:41
 */
@Slf4j
@Controller
@RequestMapping("/fundTradeNew")
public class HighDealOrderNewController extends BaseController {

    @Autowired
    private QueryConscustInfoService queryConscustInfoService;
    @Autowired
    private QueryDealOrderListFacade queryDealOrderListFacade;
    @Autowired
    private QueryCustRepurchaseProtocolFacade queryCustRepurchaseProtocolFacade;
    @Autowired
    private ConscustService conscustService;
    @Autowired
    private ProductinfoService productinfoService;
    @Autowired
    private CmBxPrebookBuyinfoService cmBxPrebookBuyinfoService;
    @Autowired
    private PrebookproductinfoService prebookproductinfoService;
    @Autowired
    private DecryptSingleFacade decryptSingleFacade;

    @Autowired
    private QueryBalanceVolDtlForCrmFacade queryBalanceVolDtlForCrmFacade;
    @Autowired
    private QueryDealOrderFacade queryDealOrderFacade;

    @Autowired
    private JjxxInfoService jjxxInfoService;

    @Autowired
    private ZxPrivateTradeFundService zxPrivateTradeFundService;

    @Autowired
    private CustprivatefundtradeService custprivatefundtradeService;
    @Autowired
    private QueryHighFundArrivalProofFacade queryHighFundArrivalProofFacade;
    @Autowired
    private HighProductService highProductService;
    @Autowired
    private SmccProductJbxxService smccProductJbxxService;
    @Autowired
    private ComprehensiveService comprehensiveService;
    @Autowired
    private HkBalanceFactorService hkBalanceFactorService;

    @Autowired
    private CrmAccountOuterService crmAccountOuterService;

    @Autowired
    private CmQueryHkCashBalanceService cmQueryHkCashBalanceService;


    private static final String PE0053 = "PE0053";
    @Autowired
    private HighDealOrderSupport highDealOrderSupport;

    /**
     * NA产品标识
     */
    private static final String NA_PRODUCT_SIGN = "10201";
    /**
     * 人民币
     */
    private static final String CURRENCY_RMB = "156";
    /**
     * 券商类型的固收
     */
    private static final String STANDARD_FIXED_INCOME_FLAG_QS = "3";
    /**
     * 无权限的人能看的时序
     */
    public static final String AUTH_START_DAY = "********";
    private static final String AUTH_START_DAY_PATTERN = "2022-01-01";
    //特殊权限产品
    private static final String SPECIAL_AUTH_PRODUCT = "H04026";

    /**
     * 已清仓
     */
    private static final String CLEARED_BALANCE_TYPE = "0";

    /**
     * 高端交易情况:
     *
     * <AUTHOR>
     * @date 2020/4/8
     */
    @RequestMapping("/highDealOrderList.do")
    public String highDealOrderList(HttpServletRequest request) {
        request.setAttribute("conscustno", request.getParameter("conscustno"));
        request.setAttribute("pubcustno", request.getParameter("pubcustno"));

        request.setAttribute("cpTopDataCG", StaticVar.CP_TOP_DATA_CG);
        request.setAttribute("authStartDay", AUTH_START_DAY_PATTERN);
        request.setAttribute("isHaveAuth", true);
        request.setAttribute("isHavePageAuth", highDealOrderSupport.isHavePageAuth(request));
        return "/custinfo/highDealOrderList";

    }


    /**
     * 高端交易情况:
     *
     * <AUTHOR>
     * @date 2020/4/8
     */
    @RequestMapping("/zxHighTradeList.do")
    public String zxHighTradeList(HttpServletRequest request) {
        request.setAttribute("conscustno", request.getParameter("conscustno"));
        request.setAttribute("pubcustno", request.getParameter("pubcustno"));
        return "/custinfo/zxHighTradeList";

    }

    /**
     * 高端产品持仓:
     *TODO:  待删除
     * <AUTHOR>
     * @date 2020/4/8
     */
    @ResponseBody
    @RequestMapping("/queryAcctBalanceList.do")
    public Map<String, Object> queryAcctBalanceList(String conscustno, HttpServletRequest request) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>(8);
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(conscustno);
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();
        Map<String, Object> resultmap = highDealOrderSupport.getBalanceListbean(CrmCustInvestTypeEnum.getEnum(conscust.getInvsttype()), conscust.getHboneno(), true, null);
        List<QueryAcctBalanceResponse.BalanceBean> listbean = (List<QueryAcctBalanceResponse.BalanceBean>) resultmap.get("list");
        List<CrmBalanceBean> listcrmbean = new LinkedList();
        if (listbean != null && listbean.size() > 0) {
            // 新增参数
            BigDecimal profit = new BigDecimal("0.00");
            BigDecimal value = new BigDecimal("0.00");
            BigDecimal profitUs = new BigDecimal("0.00");
            BigDecimal valueUs = new BigDecimal("0.00");
            int rmbCount = 0;
            int usCount = 0;

            // 判断常量表中合规标识：true启用，false停用
            LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
            boolean roleCpFlag = false;
            if (cacheMap != null && !cacheMap.isEmpty()) {
                roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
            }

            // 判断登录人员的角色中是否包括“合规人员”角色
            List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
            boolean isRoleCp = false;
            if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
                isRoleCp = true;
            }

            Map<String, PageCmJjxx1Jgflb> jgjjBeanMap = JgjjCache.getInstance().getJgjjBeanMap();
            List<String> fundList = new ArrayList<String>();
            for (QueryAcctBalanceResponse.BalanceBean instance : listbean) {
                if (isRoleCp && jgjjBeanMap.containsKey(instance.getProductCode()) && ("21".equals(jgjjBeanMap.get(instance.getProductCode()).getEjfl()) || "31".equals(jgjjBeanMap.get(instance.getProductCode()).getEjfl()) || "41".equals(jgjjBeanMap.get(instance.getProductCode()).getEjfl()) || "61".equals(jgjjBeanMap.get(instance.getProductCode()).getEjfl()))) {
                    continue;
                }
                BigDecimal floatProfit = instance.getCurrentAssetCurrency();
                BigDecimal marketCap = instance.getCurrencyMarketValue();
                if (floatProfit == null) {
                    floatProfit = new BigDecimal("0.00");
                    instance.setCurrentAssetCurrency(floatProfit);
                }
                if (marketCap == null) {
                    marketCap = new BigDecimal("0.00");
                    instance.setCurrencyMarketValue(marketCap);
                }
                if (instance.getBalanceVol() == null) {
                    instance.setBalanceVol(new BigDecimal("0.00"));
                }
                if (instance.getNav() == null) {
                    instance.setNav(new BigDecimal("0.00"));
                }
                if (instance.getAccumRealizedIncome() == null) {
                    instance.setAccumRealizedIncome(new BigDecimal("0.00"));
                }
                // 新增3,5.0 合计项
                if ("156".equals(instance.getCurrency())) {
                    // 市值合计
                    value = value.add(marketCap);
                    if (!fundList.contains(instance.getProductCode()) && "1".equals(instance.getIncomeCalStat())) {
                        // 持仓收益合计（按产品维度去重且只统计计算完成的）
                        profit = profit.add(floatProfit);
                    }
                    rmbCount++;
                } else {
                    // 市值合计
                    valueUs = valueUs.add(marketCap);
                    if (!fundList.contains(instance.getProductCode()) && "1".equals(instance.getIncomeCalStat())) {
                        // 持仓收益合计（按产品维度去重且只统计计算完成的）
                        profitUs = profitUs.add(floatProfit);
                    }
                    usCount++;
                }
                if (StringUtil.isNotNullStr(instance.getCurrency())) {
                    instance.setCurrency(ConstantCache.getInstance().getConstantKeyVal("currencys").get(instance.getCurrency()));
                }
                if (StringUtil.isNotNullStr(instance.getScaleType())) {
                    JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(instance.getProductCode(), false);
                    if (jjxxInfo != null) {
                        if ("1".equals(jjxxInfo.getSfmsjg())) { // 代销
                            instance.setScaleType("代销");
                        } else if ("3".equals(jjxxInfo.getSfmsjg())) { // 直转代
                            instance.setScaleType("直转代");
                        } else { // 直销
                            instance.setScaleType("直销");
                        }
                    }
                }
                if (StringUtil.isNotNullStr(instance.getProductType())) {
                    if ("7".equals(instance.getProductType())) {
                        instance.setProductType("专户");
                    } else if ("11".equals(instance.getProductType())) {
                        instance.setProductType("私募");
                    } else {
                        instance.setProductType("其他");
                    }
                }
                CrmBalanceBean crmbalance = new CrmBalanceBean();
                try {
                    BeanUtils.copyProperties(instance, crmbalance);
                } catch (Exception e) {
                }
                JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(instance.getProductCode(), false);
                if (jjxxInfo != null) {
                    crmbalance.setCompany(jjxxInfo.getGljc());
                    crmbalance.setHmcpx(jjxxInfo.getHmcpx());
                    crmbalance.setDuedt(jjxxInfo.getDuedt());
                }
                if (crmbalance.getCashCollection() == null) {
                    crmbalance.setCashCollection(new BigDecimal("0.00"));
                }
                listcrmbean.add(crmbalance);

                // 将产品编码放入List中用于判重
                fundList.add(instance.getProductCode());
            }

            CrmBalanceBean newcrmbal = null;
            // 合并后的User存在map中
            Map<String, CrmBalanceBean> map = new HashMap<String, CrmBalanceBean>(8);
            for (CrmBalanceBean crmbalance : listcrmbean) {
                List<SubCrmBalanceBean> listcrmbalance = new ArrayList();
                SubCrmBalanceBean subCrmBalanceBean = new SubCrmBalanceBean();
                newcrmbal = map.get(crmbalance.getProductCode());
                BeanUtils.copyProperties(crmbalance, subCrmBalanceBean);
                if (newcrmbal != null) {
                    // 持仓合并
                    newcrmbal.setBalanceVol(newcrmbal.getBalanceVol().add(crmbalance.getBalanceVol()));
                    // 当前市值合并
                    newcrmbal.setCurrencyMarketValue(newcrmbal.getCurrencyMarketValue().add(crmbalance.getCurrencyMarketValue()));
                    //累计应收管理费合并
                    if (newcrmbal.getReceivManageFee() == null) {
                        //累计应收管理费合并
                        newcrmbal.setReceivManageFee(BigDecimal.ZERO.add(crmbalance.getReceivManageFee() == null ? BigDecimal.ZERO : crmbalance.getReceivManageFee()));
                    } else {
                        //累计应收管理费合并
                        newcrmbal.setReceivManageFee(newcrmbal.getReceivManageFee().add(crmbalance.getReceivManageFee() == null ? BigDecimal.ZERO : crmbalance.getReceivManageFee()));
                    }

                    if (newcrmbal.getReceivPreformFee() == null) {
                        //累计应收管理费合并
                        newcrmbal.setReceivPreformFee(BigDecimal.ZERO.add(crmbalance.getReceivPreformFee() == null ? BigDecimal.ZERO : crmbalance.getReceivPreformFee()));
                    } else {
                        //累计应收管理费合并
                        newcrmbal.setReceivPreformFee(newcrmbal.getReceivPreformFee().add(crmbalance.getReceivPreformFee() == null ? BigDecimal.ZERO : crmbalance.getReceivPreformFee()));
                    }

                    if (newcrmbal.getMarketValueExFee() == null) {
                        //累计应收管理费合并
                        newcrmbal.setMarketValueExFee(BigDecimal.ZERO.add(crmbalance.getMarketValueExFee() == null ? BigDecimal.ZERO : crmbalance.getMarketValueExFee()));
                    } else {
                        //累计应收管理费合并
                        newcrmbal.setMarketValueExFee(newcrmbal.getMarketValueExFee().add(crmbalance.getMarketValueExFee() == null ? BigDecimal.ZERO : crmbalance.getMarketValueExFee()));
                    }

                    if (newcrmbal.getCurrencyMarketValueExFee() == null) {
                        //累计应收管理费合并
                        newcrmbal.setCurrencyMarketValueExFee(BigDecimal.ZERO.add(crmbalance.getCurrencyMarketValueExFee() == null ? BigDecimal.ZERO : crmbalance.getCurrencyMarketValueExFee()));
                    } else {
                        //累计应收管理费合并
                        newcrmbal.setCurrencyMarketValueExFee(newcrmbal.getCurrencyMarketValueExFee().add(crmbalance.getCurrencyMarketValueExFee() == null ? BigDecimal.ZERO : crmbalance.getCurrencyMarketValueExFee()));
                    }

                    if (!"2".equals(crmbalance.getHmcpx())) {
                        // 持仓收益
                        newcrmbal.setCurrentAssetCurrency(newcrmbal.getCurrentAssetCurrency() == null ? BigDecimal.ZERO : newcrmbal.getCurrentAssetCurrency().add(crmbalance.getCurrentAssetCurrency() == null ? BigDecimal.ZERO : crmbalance.getCurrentAssetCurrency()));
                        // 已实现收益
                        newcrmbal.setAccumRealizedIncome(newcrmbal.getAccumRealizedIncome() == null ? BigDecimal.ZERO : newcrmbal.getAccumRealizedIncome().add(crmbalance.getAccumRealizedIncome() == null ? BigDecimal.ZERO : crmbalance.getAccumRealizedIncome()));
                    }

                    if ("0".equals(crmbalance.getIncomeCalStat())) {
                        newcrmbal.setIncomeCalStat(crmbalance.getIncomeCalStat());
                    }
                    newcrmbal.getSubCrmBalanceBeanList().add(subCrmBalanceBean);
                } else {
                    listcrmbalance.add(subCrmBalanceBean);
                    crmbalance.setSubCrmBalanceBeanList(listcrmbalance);
                    map.put(crmbalance.getProductCode(), crmbalance);
                }
            }

            listcrmbean = mapTransitionList(map);
            resultMap.put("total", listcrmbean.size());
            resultMap.put("rows", listcrmbean);

            // 新增 3.5.0 合计项
            resultMap.put("totalValue", value);
            resultMap.put("totalProfit", profit);
            resultMap.put("totalValueUS", valueUs);
            resultMap.put("totalProfitUS", profitUs);
            resultMap.put("rmbCount", rmbCount);
            resultMap.put("usCount", usCount);
            resultMap.put("record", listcrmbean.size());
        }
        return resultMap;
    }


    /**
     * null处理  默认返回BigDecimal.ZERO
     *
     * @param bigDecimal
     * @return
     */
    private BigDecimal dealNullValue(BigDecimal bigDecimal) {
        return bigDecimal == null ? BigDecimal.ZERO : bigDecimal;
    }


    /**
     * @description:(请在此添加描述)
     * @param jjxxInfo
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2025/2/18 15:52
     * @since JDK 1.8
     */
    private String getSaleTypeByJjxx(JjxxInfo jjxxInfo) {
        String sfmsjg = jjxxInfo.getSfmsjg();
        String sfxg = jjxxInfo.getSfxg();

        if (StaticVar.YES.equals(sfxg)) {
            return "代销";
        }

        if ("1".equals(sfmsjg)) {
            // 代销
            return "代销";
        } else if ("3".equals(sfmsjg)) {
            // 直转代
            return "直转代";
        } else { // 直销
            return "直销";
        }
    }

    private String getProductType(String code) {
        if ("7".equals(code)) {
            return "专户";
        } else if ("11".equals(code)) {
            return "私募";
        } else {
            return "其他";
        }
    }

    //阳关私募 --hmcpx not in ['2','5']
    //listBean大于1条时，生成合并信息
    private CrmYgsmProdDto buildYgsmDto(JjxxInfo jjxxInfo, List<CRMBalanceBean> listbean, String custNo) {
        CrmYgsmProdDto returnDto;
        List<CrmYgsmProdDto> list = Lists.newArrayList();
        String prodCode = jjxxInfo.getJjdm();
        listbean.forEach(bean -> {
            CrmYgsmProdDto detailDto = new CrmYgsmProdDto();
            fillProdInfo(jjxxInfo, detailDto);
            fillCalculateInfo(bean, detailDto);
            //千禧年产品使用正常的产品代码
            if (Objects.equals(StaticVar.YES, bean.getQianXiFlag())) {
                detailDto.setQianXiFlag(bean.getQianXiFlag());
                detailDto.incrUnPaidInAmt(bean.getUnPaidInAmt());
                detailDto.incrCurrencyUnPaidInAmt(bean.getCurrencyUnPaidInAmt());
                detailDto.incrPaidInAmt(bean.getPaidInAmt());
            }
            //填充平衡因子信息
            fillBalanceFactorInfo(bean, detailDto);
            //明细暂定使用subProdCode
            detailDto.setProductCode(bean.getSubProductCode());

            detailDto.incrCurrentAccumIncome(bean.getCurrentAccumIncome());
            detailDto.incrAccumRealizedIncome(bean.getAccumRealizedIncome());
            detailDto.incrBalanceVol(bean.getBalanceVol());

            detailDto.incrCurrencyMarketValue(bean.getCurrencyMarketValue());

            detailDto.incrCurrencyMarketValueExFee(bean.getCurrencyMarketValueExFee());
            detailDto.incrBalanceAmtExFeeRmb(bean.getBalanceAmtExFeeRmb());
            detailDto.incrBalanceAmtExFee(bean.getBalanceAmtExFee());
            detailDto.incrCurrentIncome(bean.getCurrentIncome());
            detailDto.incrCurrentAsset(bean.getCurrentAsset());
            detailDto.setStageEstablishFlag(bean.getStageEstablishFlag());
            detailDto.setSxz(bean.getSxz());

            //持仓收益率
            detailDto.incrCurrentYields(bean.getYieldRate());
            detailDto.incrReceivManageFee(bean.getReceivManageFee());
            detailDto.incrReceivPreformFee(bean.getReceivPreformFee());
            //累计收益率
            detailDto.incrAccumYieldRate(bean.getAccumYieldRate());
            detailDto.incrBalanceCost(bean.getBalanceCostCurrency());
            detailDto.incrAccumCostRmb(bean.getAccumCostRmb());
            detailDto.incrAccumCost(bean.getAccumCost());
            detailDto.incrBalanceFloatIncome(bean.getBalanceFloatIncome());
            detailDto.incrBalanceFloatIncomeRmb(bean.getBalanceFloatIncomeRmb());
            // 持仓浮盈亏比率
            detailDto.incrBalanceFloatIncomeRate(bean.getBalanceFloatIncomeRate());
            detailDto.incrDailyAssetCurrency(bean.getDailyAssetCurrency());
            // 最新收益率
            detailDto.incrDayAssetRate(bean.getDayAssetRate());
            // 最新资产增长率
            detailDto.incrDayIncomeGrowthRate(bean.getDayIncomeGrowthRate());

            detailDto.incrUnitBalanceCostExFee(bean.getUnitBalanceCostExFee());
            //当前收益率=[当前收益/持仓成本]*100    当前币种
//           if(bean.getBalanceCostCurrency()!=null && bean.getCurrentAssetCurrency()!=null
//                         && bean.getBalanceCostCurrency().compareTo(BigDecimal.ZERO)>0){
//               //detailDto.setCurrentYields(bean.getCurrentAssetCurrency().divide(bean.getBalanceCostCurrency(),4,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)));
//               detailDto.setCurrentYields(processYieldRate(bean.getBalanceCostCurrency(),bean.getCurrentAssetCurrency()).multiply(BigDecimal.valueOf(100)));
//           }
            detailDto.setDivMode(bean.getDivMode());

            detailDto.setAssetUpdateDate(bean.getAssetUpdateDate());
            detailDto.setLatestIncomeDt(bean.getLatestIncomeDt());

            list.add(detailDto);
        });
        /* 当为分期成立产品[StageEstablishFlag=1-是] 或者 有N条持仓明细记录 . 一定合并展示 */
        boolean stageFlag = listbean.stream().filter(bean -> "1".equals(bean.getStageEstablishFlag())).count() > 0; //包含[StageEstablishFlag=1]为 true
        if (stageFlag || list.size() > 1) {
            // 合并展示字段：  “持仓份额”、“参考市值”、“持仓收益”、“累计收益”、“已实现收益”
            returnDto = new CrmYgsmProdDto();
            returnDto.setStageEstablishFlag(listbean.get(0).getStageEstablishFlag());
            returnDto.getSubList().addAll(list);
            //group取值： 产品信息
            fillProdInfo(jjxxInfo, returnDto);
            //汇总使用prodCode
            returnDto.setProductCode(prodCode);
            //group取值：get[0]
            fillCalculateInfo(listbean.get(0), returnDto);
            //汇总属性 balance
            incrYgsmBalanceValue(returnDto);

            List<String> fundCodes = list.stream().filter(crmYgsmProdDto -> StringUtil.isNotNullStr(crmYgsmProdDto.getProductCode()))
                    .map(CrmYgsmProdDto::getProductCode)
                    .collect(Collectors.toList());
            BigDecimal dailyAssetLatest = highDealOrderSupport.highStagesFundDailyAssetLatest(fundCodes, custNo);
            returnDto.setDailyAssetCurrency(dailyAssetLatest);
            returnDto.setDailyAssetCurrencyStr(CommonUtil.formatBigDecimalQfw(dailyAssetLatest));

            //汇总是否 计算完成
            returnDto.setCalculateFinish(returnDto.getSubList().stream().filter(d -> !d.isCalculateFinish()).count() == 0);

        } else {
            returnDto = list.get(0);
            returnDto.setProductCode(prodCode);
        }
        //汇总取值
        return returnDto;

    }
//    private BigDecimal processYieldRate(BigDecimal balanceCost, BigDecimal currentAsset) {
//        BigDecimal yieldRate = null;
//        if(balanceCost == null || BigDecimal.ZERO.compareTo(balanceCost) == 0){
//            yieldRate = BigDecimal.ZERO;
//        }else{
//            // 收益为负
//            if(currentAsset != null && BigDecimal.ZERO.compareTo(currentAsset) > 0){
//                BigDecimal yieldRate5 = currentAsset.divide(balanceCost, 5, BigDecimal.ROUND_DOWN);
//                BigDecimal yieldRate4 = currentAsset.divide(balanceCost, 4, BigDecimal.ROUND_DOWN);
//                if(yieldRate5.compareTo(yieldRate4) < 0){
//                    yieldRate = yieldRate5.setScale(4, BigDecimal.ROUND_UP);
//                }else {
//                    yieldRate = yieldRate5.setScale(4, BigDecimal.ROUND_DOWN);
//                }
//            }else{
//                // 收益为正
//                yieldRate = (currentAsset==null?BigDecimal.ZERO:currentAsset).divide(balanceCost, 4, BigDecimal.ROUND_DOWN);
//            }
//        }
//        return yieldRate;
//    }

    /**
     * 填充平衡因子信息
     *
     * @param bean
     * @param detailDto
     */
    private void fillBalanceFactorInfo(CRMBalanceBean bean, CrmYgsmProdDto detailDto) {
        detailDto.setBalanceFactor(bean.getBalanceFactor());
        detailDto.setConvertFinish(bean.getConvertFinish());
        detailDto.setBalanceFactorDate(bean.getBalanceFactorDate());
    }

    /**
     * 汇总方法。
     * 包含明细  只统计明细列表。不包含明细，统计自身
     *
     * @param resultDto
     */
    private void incrYgsmBalanceValue(CrmYgsmProdDto resultDto) {
        if (CollectionUtils.isEmpty(resultDto.getSubList())) {
            return;
        }

//       合并展示字段：  “持仓份额”、“参考市值”、“持仓收益”、“累计收益”、“已实现收益”
        //包含明细  只统计明细列表。不包含明细，统计自身
        final List<CrmYgsmProdDto> usedList = resultDto.getSubList();
        usedList.forEach(sub -> {
            resultDto.incrBalanceVol(sub.getBalanceVol());//持仓份额
            resultDto.incrCurrencyMarketValue(sub.getCurrencyMarketValue());//参考市值
            resultDto.incrCurrencyMarketValue(sub.getCurrencyUnPaidInAmt());//参考市值加上待投金额
            resultDto.incrCurrentIncome(sub.getCurrentIncome());//持仓收益
            resultDto.incrCurrentAsset(sub.getCurrentAsset());//持仓收益（二期）
            resultDto.incrCurrentAccumIncome(sub.getCurrentAccumIncome());//累计收益
            resultDto.incrAccumRealizedIncome(sub.getAccumRealizedIncome());//累计已实现收益
            resultDto.incrAccumCost(sub.getAccumCost());//累计成本
            resultDto.incrBalanceCost(sub.getBalanceCost());//持仓总成本
            resultDto.incrBalanceFloatIncome(sub.getBalanceFloatIncome());//持仓浮盈亏
            resultDto.incrDailyAssetCurrency(sub.getDailyAssetCurrency());//最新收益
            //NA产品费后市值
            resultDto.incrCurrencyMarketValueExFee(sub.getCurrencyMarketValueExFee());
            resultDto.maxLatestIncomeDt(sub.getLatestIncomeDt());
            //可以判断NA产品的标识
            resultDto.setSxz(sub.getSxz());

            if (Objects.equals(StaticVar.YES, sub.getQianXiFlag())) {
                resultDto.incrUnPaidInAmt(sub.getUnPaidInAmt());
                resultDto.incrCurrencyUnPaidInAmt(sub.getCurrencyUnPaidInAmt());
            }
        });
    }


    //固定收益-- hmcpx=2
    private CrmGdsyProdDto buildGdsyDto(JjxxInfo jjxxInfo, List<CRMBalanceBean> listbean) {
        List<CrmGdsyProdDto> list = Lists.newArrayList();
        CrmGdsyProdDto returnDto;

        listbean.forEach(bean -> {
            CrmGdsyProdDto detailDto = new CrmGdsyProdDto();
            fillProdInfo(jjxxInfo, detailDto);
            fillCalculateInfo(bean, detailDto);
            detailDto.setProductCode(bean.getSubProductCode());
            detailDto.incrAccumRealizedIncome(bean.getAccumRealizedIncome());//已实现收益
            detailDto.setStandardFixedIncomeFlag(bean.getStandardFixedIncomeFlag());// 设置固收类型

//            累计收益
//            a、当固收二级类型（standardFixedIncomeFlag）=“0股权固收”或“1正常固收”时，取持仓接口中的累计收益（股权固收新算法）原币
//            b、当固收二级类型=2-现金管理或 3-券商集合理财 或4-纯债产品时，取持仓接口中的累计收益字段

            //固收类且固收类型等于“0股权固收”或“1正常固收”）-->累计收益（股权新算法）人民币 .否则：累计收益（人民币）
            if ("0".equals(bean.getStandardFixedIncomeFlag()) || "1".equals(bean.getStandardFixedIncomeFlag())) { //standardFixedIncomeFlag:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品
                detailDto.setCurrentYields(null);
            }
            //持仓收益率
            detailDto.incrCurrentYields(bean.getYieldRate());
            detailDto.incrBalanceVol(bean.getBalanceVol());
            detailDto.incrCurrencyMarketValue(bean.getCurrencyMarketValue());
            detailDto.incrCurrentAssetCurrency(bean.getCurrentAssetCurrency());
            detailDto.incrCurrentIncome(bean.getCurrentIncome());
            detailDto.incrCurrentAccumIncome(bean.getCurrentAccumIncome());
            detailDto.incrTotalCashCollection(bean.getTotalCashCollection());
            detailDto.incrAccumCollection(bean.getAccumCollection());
            // 累计收益率
            detailDto.incrAccumYieldRate(bean.getAccumYieldRate());
            detailDto.incrBalanceCost(bean.getBalanceCostCurrency());
            detailDto.incrAccumCostRmb(bean.getAccumCostRmb());
            detailDto.incrAccumCost(bean.getAccumCost());
            detailDto.incrBalanceFloatIncome(bean.getBalanceFloatIncome());
            detailDto.incrBalanceFloatIncomeRmb(bean.getBalanceFloatIncomeRmb());
            // 持仓浮盈亏比率
            detailDto.incrBalanceFloatIncomeRate(bean.getBalanceFloatIncomeRate());
            detailDto.incrDailyAssetCurrency(bean.getDailyAssetCurrency());
            // 最新收益率
            detailDto.incrDayAssetRate(bean.getDayAssetRate());
            // 最新资产增长率
            detailDto.incrDayIncomeGrowthRate(bean.getDayIncomeGrowthRate());
            detailDto.incrBalanceIncomeNew(bean.getBalanceIncomeNew());
            detailDto.incrAccumIncomeNew(bean.getAccumCostNew());

            detailDto.setBenchmark(bean.getBenchmark());
            detailDto.setValueDate(bean.getValueDate());
            detailDto.setDueDate(bean.getDueDate());
            detailDto.setDivMode(bean.getDivMode());
            detailDto.incrUnitBalanceCostExFee(bean.getUnitBalanceCostExFee());
            detailDto.setAssetUpdateDate(bean.getAssetUpdateDate());
            detailDto.setNavDisclosureType(bean.getNavDisclosureType());
            detailDto.setLatestIncomeDt(bean.getLatestIncomeDt());
            list.add(detailDto);

        });

        if (list.size() == 1) {
            returnDto = list.get(0);
            returnDto.setProductCode(jjxxInfo.getJjdm());
        } else {
            returnDto = new CrmGdsyProdDto();
            returnDto.getSubList().addAll(list);
            //group取值： 产品信息
            fillProdInfo(jjxxInfo, returnDto);
            //group取值：get[0]
            fillCalculateInfo(listbean.get(0), returnDto);
            returnDto.setBenchmark(listbean.get(0).getBenchmark());
            returnDto.setValueDate(listbean.get(0).getValueDate());
            returnDto.setDueDate(listbean.get(0).getDueDate());

            //汇总是否 计算完成
            int unCalculateSize = returnDto.getSubList().stream().filter(d -> !d.isCalculateFinish()).collect(Collectors.toList()).size();
            returnDto.setCalculateFinish(unCalculateSize > 0 ? false : true);

            //汇总属性 balance
            incrGdsyBalanceValue(returnDto);
        }
        return returnDto;
    }

    /**
     * 汇总方法。
     *
     * @param resultDto
     */
    private void incrGdsyBalanceValue(CrmGdsyProdDto resultDto) {
        if (CollectionUtils.isEmpty(resultDto.getSubList())) {
            return;
        }

        //包含明细  只统计明细列表。
        final List<CrmGdsyProdDto> usedList = resultDto.getSubList();
        usedList.forEach(sub -> {
            resultDto.incrCurrentAccumIncome(sub.getCurrentAccumIncome());
            resultDto.incrAccumRealizedIncome(sub.getAccumRealizedIncome());
            resultDto.incrBalanceVol(sub.getBalanceVol());
            resultDto.incrCurrencyMarketValue(sub.getCurrencyMarketValue());
            resultDto.incrCurrentAssetCurrency(sub.getCurrentAssetCurrency());
            resultDto.incrCurrentIncome(sub.getCurrentIncome());
            resultDto.incrAccumCost(sub.getAccumCost());//累计成本
            resultDto.incrBalanceCost(sub.getBalanceCost());//持仓总成本
            resultDto.incrBalanceFloatIncome(sub.getBalanceFloatIncome());//持仓浮盈亏
            resultDto.incrDailyAssetCurrency(sub.getDailyAssetCurrency());//最新收益
        });
    }

    /**
     * 回款进度 =已回款/投资成本
     *
     * @param currencyCashCollection 已回款
     * @param currencyNetBuyAmount   投资成本
     * @return
     */
    private BigDecimal getBackYields(BigDecimal currencyCashCollection, BigDecimal currencyNetBuyAmount) {
//回款进度 =已回款/投资成本
        if (currencyCashCollection != null && currencyNetBuyAmount != null
                && currencyNetBuyAmount.compareTo(BigDecimal.ZERO) > 0) {
            return currencyCashCollection
                    .divide(currencyNetBuyAmount, 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
        }
        return null;
    }

    /**
     * 私募股权 的 产品期限说明
     * 如果产品是清盘[crisisFlag=1-是]中，返回空；否则 返回字段:fundCXQXStr
     *
     * @param crisisFlag  清盘中标识 0-否 1-是
     * @param fundCXQXStr 中台的产品期限说明
     * @return
     */
    private String getProdQxsm(String crisisFlag, String fundCXQXStr) {
        return "1".equals(crisisFlag) ? null : fundCXQXStr;
    }


    //私募股权 --hmcpx=5
    private CrmSmgqProdDto buildSmgqDto(JjxxInfo jjxxInfo, List<CRMBalanceBean> listbean) {
        List<CrmSmgqProdDto> list = Lists.newArrayList();
        CrmSmgqProdDto returnDto;
        listbean.forEach(bean -> {
            CrmSmgqProdDto detailDto = new CrmSmgqProdDto();
            fillProdInfo(jjxxInfo, detailDto);
            fillCalculateInfo(bean, detailDto);
            detailDto.setProductCode(bean.getSubProductCode());

            //持仓份额
            detailDto.incrBalanceVol(bean.getBalanceVol());
            //当前投资成本 -现有持仓份额部分对应购入本金金额-  净购买金额(投资成本)(当前币种)
            detailDto.incrCurrencyNetBuyAmount(bean.getCurrencyNetBuyAmount());
            //已发生回款  项目回款金额总和  私募股权回款(当前币种)
            detailDto.incrCurrencyCashCollection(bean.getCurrencyCashCollection());
            //累计总回款
            detailDto.incrAccumCollection(bean.getAccumCollection());
            //持仓收益
            detailDto.incrCurrentIncome(bean.getCurrentIncome());
            //投资成本
            detailDto.incrAccumCostNew(bean.getAccumCostNew());
            //认缴金额
            detailDto.incrPaidInAmt(bean.getPaidInAmt());
            //投资期限
            detailDto.setInvestmentHorizon(bean.getInvestmentHorizon());
            //产品期限说明
            detailDto.setProdQxsm(getProdQxsm(bean.getCrisisFlag(), bean.getFundCXQXStr()));
            //累计收益
            detailDto.incrCurrentAccumIncome(bean.getCurrentAccumIncome());

            //回款进度 =已回款/投资成本
            detailDto.setBackYields(getBackYields(bean.getCurrencyCashCollection(), bean.getCurrencyNetBuyAmount()));
            //累计成本
            detailDto.incrAccumCostRmb(bean.getAccumCostRmb());
            detailDto.incrAccumCost(bean.getAccumCost());
            detailDto.setAssetUpdateDate(bean.getAssetUpdateDate());
            detailDto.incrBalanceIncomeNew(bean.getBalanceIncomeNew());
            detailDto.incrAccumIncomeNew(bean.getAccumIncomeNew());
            detailDto.incrUnitBalanceCostExFee(bean.getUnitBalanceCostExFee());
            list.add(detailDto);
        });

        if (list.size() == 1) {
            returnDto = list.get(0);
            returnDto.setProductCode(jjxxInfo.getJjdm());
        } else { //合计行
            returnDto = new CrmSmgqProdDto();
            returnDto.getSubList().addAll(list);
            //group取值： 产品信息
            fillProdInfo(jjxxInfo, returnDto);
            returnDto.setProductCode(jjxxInfo.getJjdm());
            //group取值：get[0]
            fillCalculateInfo(listbean.get(0), returnDto);
            returnDto.setInvestmentHorizon(listbean.get(0).getInvestmentHorizon());
            //产品期限说明
            returnDto.setProdQxsm(getProdQxsm(listbean.get(0).getCrisisFlag(), listbean.get(0).getFundCXQXStr()));
//            1、合计行中的“认缴金额”，直接展示为空白，无需展示“--”；
            returnDto.setPaidInAmt(null);
//            2、合计行中的持仓收益和累计收益，直接合计明细值，不需要“计算中”的逻辑判断；
            //固定： 计算完成
            returnDto.setCalculateFinish(true);
            //汇总属性 balance
            incrSmgqBalanceValue(returnDto);
            //回款进度 =已回款/投资成本
            returnDto.setBackYields(getBackYields(returnDto.getCurrencyCashCollection(), returnDto.getCurrencyNetBuyAmount()));
        }
        return returnDto;
    }


    /**
     * 汇总方法。
     *
     * @param resultDto
     */
    private void incrSmgqBalanceValue(CrmSmgqProdDto resultDto){
        if (CollectionUtils.isEmpty(resultDto.getSubList())) {
            return;
        }

        //包含明细  只统计明细列表
        final List<CrmSmgqProdDto> usedList = resultDto.getSubList();
        usedList.forEach(sub -> {
            resultDto.incrBalanceVol(sub.getBalanceVol());
            resultDto.incrCurrentAccumIncome(sub.getCurrentAccumIncome());
            resultDto.incrCurrentIncome(sub.getCurrentIncome());
            resultDto.incrCurrencyCashCollection(sub.getCurrencyCashCollection());
            resultDto.incrCurrencyNetBuyAmount(sub.getCurrencyNetBuyAmount());
            resultDto.incrPaidInAmt(sub.getPaidInAmt());
            resultDto.incrAccumCostRmb(sub.getAccumCostRmb());
            resultDto.incrAccumCost(sub.getAccumCost());
            resultDto.incrBalanceIncomeNew(sub.getBalanceIncomeNew());
            resultDto.incrAccumIncomeNew(sub.getAccumIncomeNew());
        });
    }

    /**
     * 全局统计
     *
     * @param pageDto
     * @param beans
     * @param hmcpxEnum 好买产品线Enum
     * @param xgFlag 是否香港产品
     */
    private void fillTotalPageCount(CrmHighBalancePageDto pageDto, List<CRMBalanceBean> beans, HmcpxEnum hmcpxEnum, boolean xgFlag) {
        //汇总统计
        beans.forEach(bean -> {
            // 市值合计 持仓明细中，接口字段“市值（人民币）”的合计值
            pageDto.incrTotalValue(bean.getMarketValue());
            pageDto.incrDetailCount();

            //NA产品标识
            if(NA_PRODUCT_SIGN.equals(bean.getSxz())){
                pageDto.setHaveNaProduct(true);
            }

            //币种不为人民币时提示币种
            String currency = bean.getCurrency();
            RmbhlzjjDto rmbhlzjjDto = null;
            if(!CURRENCY_RMB.equals(bean.getCurrency())){
                String currencyName = ConstantCache.getInstance().getConstantKeyVal("currencys").get(bean.getCurrency());
                rmbhlzjjDto = comprehensiveService.getRmbhlzjj(null, currency);
                String currencyDesc = String.format("1%s=%s人民币", currencyName, rmbhlzjjDto.getZjj());
                if(pageDto.getCurrencyDescList() == null){
                    pageDto.setCurrencyDescList(Sets.newHashSet());
                }
                pageDto.getCurrencyDescList().add(currencyDesc);
                pageDto.getCurrencyRateList().add(pageDto.new CurrencyRate(currencyName, rmbhlzjjDto.getZjj()));
            }

            if (HmcpxEnum.YGSM.equals(hmcpxEnum)) { //阳光私募 tab  汇总(人民币)
                CrmYgsmProdDto sumYgsmDto = xgFlag ? pageDto.getSumYgsmXgDto() : pageDto.getSumYgsmDto();
//                   D、新增合计行（人民币），合计字段“参考市值”、“持仓收益”、“累计收益”、“已实现收益”，按接口中对应的人民币金额字段合计；
                sumYgsmDto.incrCurrentAccumIncome(bean.getCurrentAccumIncomeRmb());//累计收益
                sumYgsmDto.incrAccumRealizedIncome(bean.getAccumRealizedIncomeRmb());//累计已实现收益
                sumYgsmDto.incrBalanceVol();//持仓份额
                sumYgsmDto.incrCurrencyMarketValue(bean.getMarketValue());//参考市值
                //费后市值
                sumYgsmDto.incrCurrencyMarketValueExFee(calAmtRmb(bean.getCurrencyMarketValueExFee(), rmbhlzjjDto));
                sumYgsmDto.incrCurrentIncome(bean.getCurrentIncomeRmb());//持仓收益
                sumYgsmDto.incrCurrentAsset(bean.getCurrentAsset());//持仓收益

                // 持仓二期改造
                sumYgsmDto.incrBalanceCost(bean.getBalanceCost());//持仓总成本
                sumYgsmDto.incrAccumCostRmb(bean.getAccumCostRmb());//累计成本（汇总）
                sumYgsmDto.incrAccumCost(bean.getAccumCostRmb());//累计成本
                sumYgsmDto.incrBalanceFloatIncome(bean.getBalanceFloatIncomeRmb());//持仓浮盈亏
                sumYgsmDto.incrBalanceFloatIncomeRmb(bean.getBalanceFloatIncomeRmb());//持仓浮盈亏
                sumYgsmDto.incrDailyAssetCurrency(bean.getDailyAsset());//最新收益

                //最新收益日期
                sumYgsmDto.maxLatestIncomeDt(bean.getLatestIncomeDt());
                //收益计算日期
                sumYgsmDto.maxAssetUpdateDate(bean.getAssetUpdateDate());

                // 拼接分类汇总的基金代码
                sumYgsmDto.appendProductCodeStr(bean.getProductCode());

                //sumYgsmDto.incrUnitBalanceCostExFee(bean.getUnitBalanceCostExFeeRmb());

                //全局汇总
                if (bean.getProductSubType() != null && ((!"2".equals(bean.getProductSubType())  && !"5".equals(bean.getProductSubType())) || ("2".equals(bean.getProductSubType()) && "3".equals(bean.getStandardFixedIncomeFlag())))) {
                    pageDto.incrTotalProfit(bean.getCurrentIncomeRmb());// 持仓收益合计-阳光私募和固收类取接口中“持仓收益（人民币）”
                    pageDto.incrTotalAccuProfit(bean.getCurrentAccumIncomeRmb());// 累计收益合计-阳光私募类取接口中“累计收益（人民币）”
                }


                //千禧年产品属于阳光私募,计算千禧年产品代投金额总数，封装千禧年产品用于界面展示
                if (Objects.equals(StaticVar.YES, bean.getQianXiFlag())) {

                    if (!pageDto.isHavQianXiFlag()) {
                        pageDto.setHavQianXiFlag(true);
                    }
                    //阳光私募合计+待投金额
                    sumYgsmDto.incrCurrencyUnPaidInAmt(bean.getUnPaidInAmt());
                    sumYgsmDto.incrCurrencyMarketValue(bean.getUnPaidInAmt());
                    //界面底部合计人民币
                    pageDto.incrCurrencyUnPaidInAmt(bean.getUnPaidInAmt());
                    pageDto.getListShowProductName().add(bean.getProductName());
                }

            } else if (HmcpxEnum.GDSY.equals(hmcpxEnum)) { //固定收益 tab  汇总(人民币)
                CrmGdsyProdDto crmGdsyProdDto = STANDARD_FIXED_INCOME_FLAG_QS.equals(bean.getStandardFixedIncomeFlag()) ? pageDto.getSumGdsyDto() : pageDto.getSumGdsyOtherDto();
//                   G、新增合计行（人民币），合计字段“参考市值”、“持仓收益”、“累计收益”、“已实现收益”，按接口中对应的人民币金额字段合计。注意：不同固收类型取不同的累计收益字段；
                crmGdsyProdDto.incrAccumRealizedIncome(bean.getAccumRealizedIncomeRmb());//累计已实现收益
                crmGdsyProdDto.incrCurrencyMarketValue(bean.getMarketValue());//参考市值


                // 持仓二期改造
                crmGdsyProdDto.incrBalanceCost(bean.getBalanceCost());//持仓总成本
                crmGdsyProdDto.incrAccumCostRmb(bean.getAccumCostRmb());//累计成本
                crmGdsyProdDto.incrAccumCost(bean.getAccumCostRmb());//累计成本
                crmGdsyProdDto.incrCurrentIncome(bean.getCurrentIncomeRmb());//持仓收益
                crmGdsyProdDto.incrBalanceIncomeNew(bean.getBalanceIncomeNewRmb());
                crmGdsyProdDto.incrAccumIncomeNew(bean.getAccumCostRmbNew());

                // 拼接分类汇总的基金代码
                crmGdsyProdDto.appendProductCodeStr(bean.getProductCode());
                crmGdsyProdDto.incrCurrentAccumIncome(bean.getCurrentAccumIncome());//合计行-累计收益
                crmGdsyProdDto.incrDailyAssetCurrency(bean.getDailyAsset());//最新收益

                crmGdsyProdDto.incrAccumCollection(bean.getAccumCollectionRmb());
                crmGdsyProdDto.incrCurrentAssetCurrency(bean.getCurrentAssetCurrency());
                //收益计算日期
                crmGdsyProdDto.maxAssetUpdateDate(bean.getAssetUpdateDate());

                //crmGdsyProdDto.incrUnitBalanceCostExFee(bean.getUnitBalanceCostExFeeRmb());

                //全局汇总
                if (bean.getProductSubType() != null && ((!"2".equals(bean.getProductSubType())  && !"5".equals(bean.getProductSubType())) || ("2".equals(bean.getProductSubType()) && "3".equals(bean.getStandardFixedIncomeFlag())))) {
                    pageDto.incrTotalProfit(bean.getCurrentIncomeRmb());// 持仓收益合计-阳光私募和固收类取接口中“持仓收益（人民币）”
                    pageDto.incrTotalAccuProfit(bean.getCurrentAccumIncomeRmb());// 累计收益合计-阳光私募类取接口中“累计收益（人民币）”
                }
            } else if (HmcpxEnum.SMGQ.equals(hmcpxEnum)) {//私募股权 tab  汇总(人民币)
//                   H、新增合计行（人民币），合计字段“当前投资成本”、“已发生回款”、“累计收益”、“持仓收益”，按接口中对应的人民币金额字段合计。
//                   注意：不同的产品取不同的收益字段，既合计行要等于列表明细的人民汇总；
                pageDto.getSumSmgqDto().incrCurrencyNetBuyAmount(bean.getNetBuyAmount());//合计行-投资成本
                pageDto.getSumSmgqDto().incrCurrencyCashCollection(bean.getCashCollection());//合计行-私募股权回款

                // 持仓二期改造
                pageDto.getSumSmgqDto().incrTotalCashCollection(bean.getTotalCashCollection());//累计总回款
                pageDto.getSumSmgqDto().incrAccumCollection(bean.getAccumCollectionRmb());//累计总回款（持仓二期）
                pageDto.getSumSmgqDto().incrAccumCostNew(bean.getAccumCostRmbNew());//累计成本（持仓二期）
                pageDto.getSumSmgqDto().incrBalanceVol(bean.getBalanceVol());//持仓份额（持仓二期）
                pageDto.getSumSmgqDto().incrAccumCostRmb(bean.getAccumCostRmb());//合计行-累计收益(持仓二期)
                pageDto.getSumSmgqDto().incrAccumCost(bean.getAccumCostRmb());//合计行-累计收益(持仓二期)
                pageDto.getSumSmgqDto().incrBalanceIncomeNew(bean.getBalanceIncomeNewRmb());
                pageDto.getSumSmgqDto().incrAccumIncomeNew(bean.getAccumCostRmbNew());

                // 拼接分类汇总的基金代码
                pageDto.getSumSmgqDto().appendProductCodeStr(bean.getProductCode());
                pageDto.getSumSmgqDto().incrCurrentIncome(bean.getCurrentIncome());//合计行-持仓收益
                pageDto.getSumSmgqDto().incrCurrentAccumIncome(bean.getCurrentAccumIncome());//合计行-累计收益
                //收益计算日期
                pageDto.getSumSmgqDto().maxAssetUpdateDate(bean.getAssetUpdateDate());

                if (bean.getProductSubType() != null && ((!"2".equals(bean.getProductSubType())  && !"5".equals(bean.getProductSubType())) || ("2".equals(bean.getProductSubType()) && "3".equals(bean.getStandardFixedIncomeFlag())))) {
                    pageDto.incrTotalProfit(bean.getCurrentIncomeRmb());// 持仓收益合计-阳光私募和固收类取接口中“持仓收益（人民币）”
                    pageDto.incrTotalAccuProfit(bean.getCurrentAccumIncomeRmb());// 累计收益合计-阳光私募类取接口中“累计收益（人民币）”
                }

                //全局汇总
                pageDto.incrTotalStockRecovery(bean.getAccumCollectionRmb());//累计已回款-totalStockRecovery：只取股权类的“累计总回款（人民币）”合计值

            }
        });
    }

    /**
     * @description 换算人民币
     * @param amt
     * @param rmbhlzjjDto
     * @return java.math.BigDecimal
     * @author: jianjian.yang
     * @date: 2023/8/30 14:15
     * @since JDK 1.8
     */
    private BigDecimal calAmtRmb(BigDecimal amt, RmbhlzjjDto rmbhlzjjDto){
        if(amt == null){
            return null;
        }
        if(rmbhlzjjDto != null){
            return amt.multiply(BigDecimal.valueOf(rmbhlzjjDto.getZjj())).setScale(2, BigDecimal.ROUND_HALF_UP);
        }
        return amt;
    }

    /**
     * 根据外部[中台]
     *
     * @param hboneNo  一账通账号
     * @param custNo   客户号
     * @param listbean 明细列表
     * @param isRoleCp
     * @return
     */
    private CrmHighBalancePageDto constructPageDto(String hboneNo,
                                                   String custNo,
                                                   List<QueryAcctBalanceResponse.BalanceBean> listbean,
                                                   boolean isRoleCp,
                                                   CrmCustInvestTypeEnum investTypeEnum,
                                                   HttpServletRequest request) {
        CrmHighBalancePageDto pageBean = new CrmHighBalancePageDto(hboneNo, custNo);
        if (CollectionUtils.isEmpty(listbean)) {
            return pageBean;
        }

        // 调用分红方式查询接口，拼接分红方式字段
        List<QueryCustFundDivResponse.CustFundDivBean> listFundDivBean = highDealOrderSupport.getCustFundDivList(hboneNo, investTypeEnum);

        // 通过Session获取产品广度信息
        HttpSession session = request.getSession();
        String topcpdata = (String) session.getAttribute("topcpdata");

        // 获取基金分红方式Map
        Map<String, String> fundDivModeMap = CollectionUtils.isNotEmpty(listFundDivBean) ?
                listFundDivBean.stream().collect(Collectors.toMap(QueryCustFundDivResponse.CustFundDivBean::getFundCode, QueryCustFundDivResponse.CustFundDivBean::getDivMode))
                : new HashMap<>();

        // 用CRM的Bean追加字段信息
        Map<String, PageCmJjxx1Jgflb> jgjjBeanMap = JgjjCache.getInstance().getJgjjBeanMap();
        List<CRMBalanceBean> listCRMBean = listbean.stream().filter(tmsBalanceBean -> {
            if (tmsBalanceBean.getProductCode() != null) {
                JjxxInfo jjxxByJjdm = jjxxInfoService.getJjxxByJjdm(tmsBalanceBean.getProductCode(), false);
                return HighDealOrderSupport.stayToDisplay(jjxxByJjdm, topcpdata, isRoleCp, jgjjBeanMap);
            } else {
                return false;
            }
        }).map(tmsBalanceBean -> {
            CRMBalanceBean crmBalanceBean = new CRMBalanceBean();
            BeanUtils.copyProperties(tmsBalanceBean, crmBalanceBean);
            String fundCode = crmBalanceBean.getSubProductCode() != null ? crmBalanceBean.getSubProductCode() : crmBalanceBean.getProductCode();
            //最新收益日期
            crmBalanceBean.setLatestIncomeDt(highDealOrderSupport.highSingFundLatest(fundCode, custNo));
            crmBalanceBean.setDivMode(fundDivModeMap.get(tmsBalanceBean.getProductCode()));
            return crmBalanceBean;
        }).collect(Collectors.toList());

        //bean 做filter , 并且按照基金代码汇总称为Map.
        Map<String, List<CRMBalanceBean>> beanMap = listCRMBean.stream().collect(Collectors.groupingBy(CRMBalanceBean::getProductCode));
        //构建页面展示的每一行记录
        beanMap.forEach((productCode, beanList) -> {
                    //获取产品信息
                    JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(productCode, false);
                    if (jjxxInfo == null) {
                        jjxxInfo = new JjxxInfo();
                        jjxxInfo.setJjdm(productCode);
                    }
                    jjxxInfo.setJjjc(beanList.get(0).getProductName());//使用接口中的产品名称，不使用jjxx表中简称

                    String hmcpx = beanList.get(0).getProductSubType();
                    boolean xgFlag = YesNoEnum.Y.getCode().equals(beanList.get(0).getHkSaleFlag());
                    HmcpxEnum hmcpxEnum = HmcpxEnum.getHmcpxEnum(hmcpx);
                    //包含特殊权限产品
                    if(SPECIAL_AUTH_PRODUCT.equals(productCode)){
                        pageBean.setContainSpecialAuthProduct(true);
                    }

                    //全局汇总统计
                    fillTotalPageCount(pageBean, beanList, hmcpxEnum, xgFlag);

                    //分类到不同的tab下
                    switch (hmcpxEnum) {
                        case GDSY:
                            if(STANDARD_FIXED_INCOME_FLAG_QS.equals(beanList.get(0).getStandardFixedIncomeFlag())) {
                                pageBean.getListGs().add(buildGdsyDto(jjxxInfo, beanList));
                            }else {
                                pageBean.getListGsOther().add(buildGdsyDto(jjxxInfo, beanList));
                            }
                            return;
                        case SMGQ:
                            pageBean.getListGq().add(buildSmgqDto(jjxxInfo, beanList));
                            return;
                        case YGSM:
                            if(xgFlag){
                                pageBean.getListYgsmXg().add(buildYgsmDto(jjxxInfo, beanList, custNo));
                            }else {
                                pageBean.getListYgsm().add(buildYgsmDto(jjxxInfo, beanList, custNo));
                            }
                            return;
                        default:
                    }
                }
        );


        //追加 合计行：
//        阳关私募 国内   CrmYgsmProdDto
        if (CollectionUtils.isNotEmpty(pageBean.getListYgsm())) {
            pageBean.getSumYgsmDto().setProductName("合计");
            pageBean.getSumYgsmDto().setCurrencyStr("人民币");
            pageBean.getSumYgsmDto().setCalculateFinish(true);//不关心计算
            setYgsmLatest(pageBean.getListYgsm(), pageBean.getSumYgsmDto(), custNo);
            pageBean.getListYgsm().add(pageBean.getSumYgsmDto());
        }
//        阳关私募 香港   CrmYgsmProdDto
        if (CollectionUtils.isNotEmpty(pageBean.getListYgsmXg())) {
            pageBean.getSumYgsmXgDto().setProductName("合计");
            pageBean.getSumYgsmXgDto().setCurrencyStr("人民币");
            pageBean.getSumYgsmXgDto().setCalculateFinish(true);//不关心计算
            setYgsmLatest(pageBean.getListYgsmXg(), pageBean.getSumYgsmXgDto(), custNo);
            pageBean.getListYgsmXg().add(pageBean.getSumYgsmXgDto());
        }
        //阳光私募总市值
        pageBean.setTotalYgsmMarketValue(calYgsmTotalMarketValue(pageBean));
        pageBean.setTotalYgsmMarketValueStr(CommonUtil.formatBigDecimalQfw(pageBean.getTotalYgsmMarketValue()));
//        固定收益   CrmGdsyProdDto
        if (CollectionUtils.isNotEmpty(pageBean.getListGs())) {
            pageBean.getSumGdsyDto().setProductName("合计");
            pageBean.getSumGdsyDto().setCurrencyStr("人民币");
            pageBean.getSumGdsyDto().setCalculateFinish(true);//不关心计算
            setGdsyLatest(pageBean.getListGs(), pageBean.getSumGdsyDto(), custNo);
            pageBean.getListGs().add(pageBean.getSumGdsyDto());
        }
//        固定收益   CrmGdsyProdDto
        if (CollectionUtils.isNotEmpty(pageBean.getListGsOther())) {
            pageBean.getSumGdsyOtherDto().setProductName("合计");
            pageBean.getSumGdsyOtherDto().setCurrencyStr("人民币");
            pageBean.getSumGdsyOtherDto().setCalculateFinish(true);//不关心计算
            setGdsyLatest(pageBean.getListGsOther(), pageBean.getSumGdsyOtherDto(), custNo);
            pageBean.getListGsOther().add(pageBean.getSumGdsyOtherDto());
        }
        //固定收益总市值
        pageBean.setTotalGdsyMarketValue(calGdsyTotalMarketValue(pageBean));
        pageBean.setTotalGdsyMarketValueStr(CommonUtil.formatBigDecimalQfw(pageBean.getTotalGdsyMarketValue()));
//        私募股权   CrmSmgqProdDto
        if (CollectionUtils.isNotEmpty(pageBean.getListGq())) {
            pageBean.getSumSmgqDto().setProductName("合计");
            pageBean.getSumSmgqDto().setCurrencyStr("人民币");
            pageBean.getSumSmgqDto().setCalculateFinish(true);//不关心计算
            setSmgqLatest(pageBean.getListGq(), pageBean.getSumSmgqDto(), custNo);
            pageBean.getListGq().add(pageBean.getSumSmgqDto());
        }
        pageBean.setTotalSmgqMarketValue(pageBean.getSumSmgqDto().getCurrencyNetBuyAmount());
        pageBean.setTotalSmgqMarketValueStr(CommonUtil.formatBigDecimalQfw(pageBean.getTotalSmgqMarketValue()));
        return pageBean;
    }

    /**
     * @description 阳光私募合计最新收益
     * @param list
     * @param crmYgsmProdDtoSum
     * @param custNo
     * @return void
     * @author: jianjian.yang
     * @date: 2023/8/30 20:30
     * @since JDK 1.8
     */
    private void setYgsmLatest(List<CrmYgsmProdDto> list, CrmYgsmProdDto crmYgsmProdDtoSum, String custNo){
        List<String> fundCodes = Lists.newArrayList();
        list.forEach(crmYgsmProdDto -> {
            if(CollectionUtils.isNotEmpty(crmYgsmProdDto.getSubList())){
                fundCodes.addAll(crmYgsmProdDto.getSubList().stream().map(CrmYgsmProdDto::getProductCode).collect(Collectors.toList()));
            }else if(StringUtil.isNotNullStr(crmYgsmProdDto.getProductCode())){
                fundCodes.add(crmYgsmProdDto.getProductCode());
            }
        });
        SequentialDataDTO sequentialDataDTO = highDealOrderSupport.highClassfyAssetLatest(fundCodes, custNo);
        if(sequentialDataDTO != null) {
            crmYgsmProdDtoSum.setDailyAssetCurrency(sequentialDataDTO.getDailyAsset());
            crmYgsmProdDtoSum.setDailyAssetCurrencyStr(CommonUtil.formatBigDecimalQfw(sequentialDataDTO.getDailyAsset()));
        }else {
            crmYgsmProdDtoSum.setDailyAssetCurrency(null);
            crmYgsmProdDtoSum.setDailyAssetCurrencyStr(StringUtils.EMPTY);
        }
    }

    /**
     * @description 固定收益合计最新收益
     * @param list
     * @param crmGdsyProdDtoSum
     * @param custNo
     * @return void
     * @author: jianjian.yang
     * @date: 2023/8/30 20:30
     * @since JDK 1.8
     */
    private void setGdsyLatest(List<CrmGdsyProdDto> list, CrmGdsyProdDto crmGdsyProdDtoSum, String custNo){
        List<String> fundCodes = Lists.newArrayList();
        list.forEach(crmGdsyProdDto -> {
            if(CollectionUtils.isNotEmpty(crmGdsyProdDto.getSubList())){
                fundCodes.addAll(crmGdsyProdDto.getSubList().stream().map(CrmGdsyProdDto::getProductCode).collect(Collectors.toList()));
            }else if(StringUtil.isNotNullStr(crmGdsyProdDto.getProductCode())){
                fundCodes.add(crmGdsyProdDto.getProductCode());
            }
        });
        SequentialDataDTO sequentialDataDTO = highDealOrderSupport.highClassfyAssetLatest(fundCodes, custNo);
        if(sequentialDataDTO != null) {
            crmGdsyProdDtoSum.setDailyAssetCurrency(sequentialDataDTO.getDailyAsset());
            crmGdsyProdDtoSum.setDailyAssetCurrencyStr(CommonUtil.formatBigDecimalQfw(sequentialDataDTO.getDailyAsset()));
        }else {
            crmGdsyProdDtoSum.setDailyAssetCurrency(null);
            crmGdsyProdDtoSum.setDailyAssetCurrencyStr(StringUtils.EMPTY);
        }
    }

    /**
     * @description 私募股权合计最新收益
     * @param list
     * @param crmSmgqProdDtoSum
     * @param custNo
     * @return void
     * @author: jianjian.yang
     * @date: 2023/8/30 20:30
     * @since JDK 1.8
     */
    private void setSmgqLatest(List<CrmSmgqProdDto> list, CrmSmgqProdDto crmSmgqProdDtoSum, String custNo){
        List<String> fundCodes = Lists.newArrayList();
        list.forEach(crmSmgqProdDto -> {
            if(CollectionUtils.isNotEmpty(crmSmgqProdDto.getSubList())){
                fundCodes.addAll(crmSmgqProdDto.getSubList().stream().map(CrmSmgqProdDto::getProductCode).collect(Collectors.toList()));
            }else if(StringUtil.isNotNullStr(crmSmgqProdDto.getProductCode())){
                fundCodes.add(crmSmgqProdDto.getProductCode());
            }
        });
        SequentialDataDTO sequentialDataDTO = highDealOrderSupport.highClassfyAssetLatest(fundCodes, custNo);
        if(sequentialDataDTO != null) {
            crmSmgqProdDtoSum.setBalanceIncomeNew(sequentialDataDTO.getCurrentAsset());
            crmSmgqProdDtoSum.setAccumIncomeNew(sequentialDataDTO.getAccumIncome());
            crmSmgqProdDtoSum.setBalanceIncomeNewStr(CommonUtil.formatBigDecimalQfw(sequentialDataDTO.getCurrentAsset()));
            crmSmgqProdDtoSum.setAccumIncomeNewStr(CommonUtil.formatBigDecimalQfw(sequentialDataDTO.getAccumIncome()));
        }else {
            crmSmgqProdDtoSum.setBalanceIncomeNew(null);
            crmSmgqProdDtoSum.setBalanceIncomeNewStr(StringUtils.EMPTY);
            crmSmgqProdDtoSum.setAccumIncomeNew(null);
            crmSmgqProdDtoSum.setAccumIncomeNewStr(StringUtils.EMPTY);
        }
    }

    /**
     * @description 算阳光私募总市值
     * @param pageBean
     * @return java.math.BigDecimal
     * @author: jianjian.yang
     * @date: 2023/7/6 18:55
     * @since JDK 1.8
     */
    private BigDecimal calYgsmTotalMarketValue(CrmHighBalancePageDto pageBean){
        //阳光私募总市值
        BigDecimal ygsmTotalValue = BigDecimal.ZERO;
        if(pageBean.getSumYgsmDto().getCurrencyMarketValue() != null){
            ygsmTotalValue = ygsmTotalValue.add(pageBean.getSumYgsmDto().getCurrencyMarketValue());
        }
        if(pageBean.getSumYgsmXgDto().getCurrencyMarketValue() != null){
            ygsmTotalValue = ygsmTotalValue.add(pageBean.getSumYgsmXgDto().getCurrencyMarketValue());
        }
        return ygsmTotalValue;
    }

    /**
     * @description 算阳光私募总市值
     * @param pageBean
     * @return java.math.BigDecimal
     * @author: jianjian.yang
     * @date: 2023/7/6 18:55
     * @since JDK 1.8
     */
    private BigDecimal calGdsyTotalMarketValue(CrmHighBalancePageDto pageBean){
        //阳光私募总市值
        BigDecimal ygsmTotalValue = BigDecimal.ZERO;
        if(pageBean.getSumGdsyDto().getCurrencyMarketValue() != null){
            ygsmTotalValue = ygsmTotalValue.add(pageBean.getSumGdsyDto().getCurrencyMarketValue());
        }
        if(pageBean.getSumGdsyOtherDto().getCurrencyMarketValue() != null){
            ygsmTotalValue = ygsmTotalValue.add(pageBean.getSumGdsyOtherDto().getCurrencyMarketValue());
        }
        return ygsmTotalValue;
    }

    /**
     * 根据产品代码，填充基金相关信息
     *
     * @param jjxxInfo
     * @param dto
     * @return
     */
    private <T extends CrmBalanceProdDto> void fillProdInfo(JjxxInfo jjxxInfo, T dto) {
        if (jjxxInfo != null) {
            dto.setCompany(jjxxInfo.getGljc());
            dto.setHmcpx(jjxxInfo.getHmcpx());
            dto.setDuedt(jjxxInfo.getDuedt());
            dto.setProductName(jjxxInfo.getJjjc());
            dto.setScaleType(getSaleTypeByJjxx(jjxxInfo));
        }
    }

    /**
     * 根据持仓数据，填充 净值、产品持仓计算信息
     * @param bean
     * @param dto
     * @return
     */
    private <T extends CrmBalanceProdDto> void fillCalculateInfo(CRMBalanceBean bean, T dto) {
        dto.setCurrency(bean.getCurrency());
        dto.setCurrencyStr(ConstantCache.getInstance().getConstantKeyVal("currencys").get(bean.getCurrency()));
        dto.setIncomeDt(bean.getIncomeDt());
        dto.setNav(bean.getNav());
        dto.setNavStr(CommonUtil.formatBigDecimalQfw(bean.getNav()));
        dto.setNavDt(bean.getNavDt());
        dto.setProductType(getProductType(bean.getProductType()));
        dto.setCalculateFinish(isCalculateFinishByStat(bean.getIncomeCalStat())); //1-计算完成 true
    }

    /**
     * 根据计算状态 0-计算中；1-计算完成 返回 是否计算完成
     * true标识计算完成
     *
     * @param incomeCalStat
     * @return
     */
    private boolean isCalculateFinishByStat(String incomeCalStat) {
        return "1".equals(incomeCalStat);
    }


    /**
     * 高端产品持仓:
     *
     * <AUTHOR>
     * @modifier :haoran.zhang  2021-7-22 10:56:25
     * @date 2020/4/8
     */
    @ResponseBody
    @RequestMapping("/queryNewAcctBalanceList.do")
    public CrmHighBalancePageDto queryNewAcctBalanceList(String conscustno, String balanceType, HttpServletRequest request) throws Exception {
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(conscustno);
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();

        Map<String, Object> resultmap = highDealOrderSupport.getBalanceListbean(CrmCustInvestTypeEnum.getEnum(conscust.getInvsttype()),
                conscust.getHboneno(), true, balanceType);

        // new logic begin
        boolean isRoleCp = isCompositeRoleCp(request);
        List<QueryAcctBalanceResponse.BalanceBean> listbean = (List<QueryAcctBalanceResponse.BalanceBean>) resultmap.get("list");

        CrmHighBalancePageDto pageDto = constructPageDto(conscust.getHboneno(), conscustno, listbean,
                isRoleCp, CrmCustInvestTypeEnum.getEnum(conscust.getInvsttype()), request);

        // 总市值
        BigDecimal totalMarketValue = new BigDecimal("0.00");
        // 在途资产
        BigDecimal totalUnconfirmedAmt = new BigDecimal("0.00");
        if (StringUtil.isNotNullStr(resultmap.get("totalMarketValue"))) {
            totalMarketValue = new BigDecimal(resultmap.get("totalMarketValue").toString());
        }
        if (StringUtil.isNotNullStr(resultmap.get("totalUnconfirmedAmt"))) {
            totalUnconfirmedAmt = new BigDecimal(resultmap.get("totalUnconfirmedAmt").toString());
        }
        pageDto.setTotalMarketValue(totalMarketValue);
        pageDto.setTotalUnconfirmedAmt(totalUnconfirmedAmt);
//        pageDto.setTotalMarketValue(new BigDecimal("********.77"));
//        pageDto.setTotalUnconfirmedAmt(new BigDecimal("0.00"));


        // 香港账号[现金余额]
        HkConscustVO hkConscustVO = crmAccountOuterService.queryHkCustInfoByCustNo(conscustno);
        boolean isOpenedHkAcct = Objects.nonNull(hkConscustVO) && hkConscustVO.isOpHkAcct();
        // !!! 持仓记录范围 != 已清仓，才去查[现金余额]
        // 客户好买香港账户已成功开户
        if (!CLEARED_BALANCE_TYPE.equals(balanceType) && isOpenedHkAcct) {
            CmQueryHkCashBalanceRequest cmQueryHkCashBalanceRequest = new CmQueryHkCashBalanceRequest();
            cmQueryHkCashBalanceRequest.setHkCustNo(hkConscustVO.getHkTxAcctNo());
            cmQueryHkCashBalanceRequest.setDisCurrency(CurrencyEnum.RMB.getCode());
            CmQueryHkCashBalanceResponse response = cmQueryHkCashBalanceService.execute(cmQueryHkCashBalanceRequest);

            // 现金余额
            BigDecimal hkCashBalanceAmt = new BigDecimal("0.00");
            if (Objects.nonNull(response) && Objects.nonNull(response.getTotalBalance())) {
                hkCashBalanceAmt = response.getTotalBalance();
            }
            // 现金余额
            pageDto.setHkCashBalanceAmt(hkCashBalanceAmt);
        }

        // 是否展示：“查看余额明细”按钮、“现金存入凭证记录”按钮、“现金余额”字段、指标说明icon
        // 持仓记录范围 != 已清仓 && 客户好买香港账户已成功开户 && 产品广度 ！= 常规产品
        String topCpData = (String) request.getSession().getAttribute("topcpdata");
        log.info("consCustNo: {}, isOpenedHkAcct:{}, topCpData:{}", conscustno, isOpenedHkAcct, topCpData);
        boolean showHwCashFlag = !CLEARED_BALANCE_TYPE.equals(balanceType) && isOpenedHkAcct
                && !StaticVar.CP_TOP_DATA_CG.equals(topCpData);
        pageDto.setShowHwCashFlag(showHwCashFlag);

        // 客户总资产
        pageDto.setTotalAssetAmt(pageDto.getHkCashBalanceAmt().add(pageDto.getTotalMarketValue()));

        return pageDto;
    }


    /**
     * 高端产品持仓:
     *
     * <AUTHOR>
     * @date 2020/4/8
     */
    @ResponseBody
    @RequestMapping("/queryBalanceVolDtlForCrmFacade.do")
    public Map<String, Object> queryBalanceVolDtlForCrmFacade(String conscustno, String productcode, HttpServletRequest request) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>(8);
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(conscustno);
        log.info("QueryConscustInfoRequest：" + JSON.toJSONString(queryRequest));
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        log.info("QueryConscustInfoResponse：" + JSON.toJSONString(queryResponse));
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();

        QueryBalanceVolDtlForCrmRequest req = new QueryBalanceVolDtlForCrmRequest();
        req.setHbOneNo(conscust.getHboneno());
        req.setFundCode(productcode);
        req.setOutletCode("A20150120");
        req.setOperIp(getIpAddr(request));
        req.setTxChannel("1");
        req.setDataTrack("crmsale");
        req.setDisCodeList(jjxxInfoService.getHbFullDisCodeList());
        QueryBalanceVolDtlForCrmResponse rep = queryBalanceVolDtlForCrmFacade.execute(req);
        log.info("QueryBalanceVolDtlForCrm, request:{} ,response:{}", JSON.toJSONString(req), JSON.toJSONString(rep));
        resultMap.put("list", rep.getBalanceVolDtlList());
        return resultMap;
    }

    public static Map<String, Object> mapTransitionMap(Map map) {
        Map<String, Object> resultMap = new HashMap<String, Object>(8);
        List<CrmBalanceBean> listYgsm = new LinkedList<CrmBalanceBean>();
        List<CrmBalanceBean> listGq = new LinkedList<CrmBalanceBean>();
        List<CrmBalanceBean> listGs = new LinkedList<CrmBalanceBean>();
        // 获得map的Iterator
        Iterator iter = map.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry<String, CrmBalanceBean> entry = (Map.Entry) iter.next();
            CrmBalanceBean tempCrmBalanceBean = (CrmBalanceBean) entry.getValue();
            if (StringUtils.isNotBlank(tempCrmBalanceBean.getHmcpx())) {
                String subProductCode = tempCrmBalanceBean.getHmcpx();
                log.info("=============subProductCode: " + subProductCode);
                if ("2".equals(subProductCode)) {
                    listGs.add(tempCrmBalanceBean);
                } else if ("5".equals(subProductCode)) {
                    listGq.add(tempCrmBalanceBean);
                } else {
                    listYgsm.add(tempCrmBalanceBean);
                }
            } else {
                listYgsm.add(tempCrmBalanceBean);
            }
        }

        if (listYgsm.size() > 0) {

            //合计字段“参考市值”、“持仓收益”、“累计收益”、“已实现收益”，按接口中对应的人民币金额字段合计；
            CrmBalanceBean allygsm = new CrmBalanceBean();
            BigDecimal currencyMarketValue = BigDecimal.ZERO;//参考市值
            BigDecimal currentAssetCurrency = BigDecimal.ZERO;//持仓收益
            BigDecimal accumIncome = BigDecimal.ZERO;//累计收益
            BigDecimal accumRealizedIncome = BigDecimal.ZERO;//已实现收益
            for (CrmBalanceBean bean : listYgsm) {
                currencyMarketValue = currencyMarketValue.add(bean.getMarketValue() != null ? bean.getMarketValue() : BigDecimal.ZERO);
                currentAssetCurrency = currentAssetCurrency.add(bean.getCurrentAsset() != null ? bean.getCurrentAsset() : BigDecimal.ZERO);
                accumIncome = accumIncome.add(bean.getAccumIncomeRmb() != null ? bean.getAccumIncomeRmb() : BigDecimal.ZERO);
                accumRealizedIncome = accumRealizedIncome.add(bean.getAccumRealizedIncomeRmb() != null ? bean.getAccumRealizedIncomeRmb() : BigDecimal.ZERO);
            }
            allygsm.setCurrencyMarketValue(currencyMarketValue);
            allygsm.setCurrentAssetCurrency(currentAssetCurrency);
            allygsm.setAccumRealizedIncome(accumRealizedIncome);
            allygsm.setAccumIncome(accumIncome);
            allygsm.setProductName("合计(人民币)");
            listYgsm.add(allygsm);
        }

        if (listGs.size() > 0) {

            //G、新增合计行（人民币），合计字段“参考市值”、“持仓收益”、“累计收益”、“已实现收益”，按接口中对应的人民币金额字段合计。注意：不同固收类型取不同的累计收益字段；
            CrmBalanceBean allygsm = new CrmBalanceBean();
            BigDecimal currencyMarketValue = BigDecimal.ZERO;//参考市值
            BigDecimal currentAssetCurrency = BigDecimal.ZERO;//持仓收益
            BigDecimal accumIncome = BigDecimal.ZERO;//累计收益
            BigDecimal accumRealizedIncome = BigDecimal.ZERO;//已实现收益
            for (CrmBalanceBean bean : listGs) {
                currencyMarketValue = currencyMarketValue.add(bean.getMarketValue() != null ? bean.getMarketValue() : BigDecimal.ZERO);
                currentAssetCurrency = currentAssetCurrency.add(bean.getCurrentAsset() != null ? bean.getCurrentAsset() : BigDecimal.ZERO);
                accumIncome = accumIncome.add(bean.getAccumIncomeNewRmb() != null ? bean.getAccumIncomeNewRmb() : BigDecimal.ZERO);
                accumRealizedIncome = accumRealizedIncome.add(bean.getAccumRealizedIncomeRmb() != null ? bean.getAccumRealizedIncomeRmb() : BigDecimal.ZERO);
            }
            allygsm.setCurrencyMarketValue(currencyMarketValue);
            allygsm.setCurrentAssetCurrency(currentAssetCurrency);
            allygsm.setAccumRealizedIncome(accumRealizedIncome);
            allygsm.setAccumIncomeNew(accumIncome);
            allygsm.setProductName("合计(人民币)");

            listGs.add(allygsm);
        }

        if (listGq.size() > 0) {

            //H、新增合计行（人民币），合计字段“当前投资成本”、“已发生回款”、“累计收益”、“持仓收益”，按接口中对应的人民币金额字段合计。注意：不同的产品取不同的收益字段，既合计行要等于列表明细的人民汇总；
            CrmBalanceBean allygsm = new CrmBalanceBean();
            BigDecimal currencyMarketValue = BigDecimal.ZERO;//当前投资成本
            BigDecimal currentAssetCurrency = BigDecimal.ZERO;//已发生回款
            BigDecimal accumIncome = BigDecimal.ZERO;//累计收益
            BigDecimal accumRealizedIncome = BigDecimal.ZERO;//已实现收益
            for (CrmBalanceBean bean : listGq) {
                currencyMarketValue = currencyMarketValue.add(bean.getNetBuyAmount() != null ? bean.getNetBuyAmount() : BigDecimal.ZERO);
                currentAssetCurrency = currentAssetCurrency.add(bean.getCashCollection() != null ? bean.getCashCollection() : BigDecimal.ZERO);
                accumIncome = accumIncome.add(bean.getAccumIncomeRmb() != null ? bean.getAccumIncomeRmb() : BigDecimal.ZERO);
                accumRealizedIncome = accumRealizedIncome.add(bean.getAccumRealizedIncomeRmb() != null ? bean.getAccumRealizedIncomeRmb() : BigDecimal.ZERO);
            }
            allygsm.setCurrencyNetBuyAmount(currencyMarketValue);
            allygsm.setCurrencyCashCollection(currentAssetCurrency);
            allygsm.setAccumRealizedIncome(accumRealizedIncome);
            allygsm.setAccumIncome(accumIncome);
            allygsm.setProductName("合计(人民币)");
            listGq.add(allygsm);
        }
        resultMap.put("listYgsm", listYgsm);
        resultMap.put("listGq", listGq);
        resultMap.put("listGs", listGs);

        return resultMap;
    }

    public static List<CrmBalanceBean> mapTransitionList(Map map) {
        List<CrmBalanceBean> listAll = new ArrayList<CrmBalanceBean>();
        List<CrmBalanceBean> listYgsm = new LinkedList<CrmBalanceBean>();
        List<CrmBalanceBean> listGq = new LinkedList<CrmBalanceBean>();
        List<CrmBalanceBean> listGs = new LinkedList<CrmBalanceBean>();
        // 获得map的Iterator
        Iterator iter = map.entrySet().iterator();
        while (iter.hasNext()) {
            Map.Entry<String, CrmBalanceBean> entry = (Map.Entry) iter.next();
            CrmBalanceBean tempCrmBalanceBean = (CrmBalanceBean) entry.getValue();
            if (StringUtils.isNotBlank(tempCrmBalanceBean.getHmcpx())) {
                String subProductCode = tempCrmBalanceBean.getHmcpx();
                log.info("=============subProductCode: " + subProductCode);
                if ("2".equals(subProductCode)) {
                    listGs.add(tempCrmBalanceBean);
                } else if ("5".equals(subProductCode)) {
                    listGq.add(tempCrmBalanceBean);
                } else {
                    listYgsm.add(tempCrmBalanceBean);
                }
            } else {
                listYgsm.add(tempCrmBalanceBean);
            }
        }
        if (listYgsm != null && listYgsm.size() > 0) {
            listAll.addAll(listYgsm);
        }
        if (listGq != null && listGq.size() > 0) {
            listAll.addAll(listGq);
        }
        if (listGs != null && listGs.size() > 0) {
            listAll.addAll(listGs);
        }
        return listAll;
    }

    /**
     * 获取资金到账证明文件pdf文件地址
     *
     * @param dealNo 订单号 NOT NULL
     * @return
     */
    private String getHighFundArrivalPdfFilePath(String dealNo) {
        QueryHighFundArrivalProofRequest request = new QueryHighFundArrivalProofRequest();
        QueryHighFundArrivalProofCondition condition = new QueryHighFundArrivalProofCondition();
        condition.setDealNo(dealNo);
        request.setCondition(condition);
//        disCode  outletCode operIp txChannel
        request.setDisCode(DisCodeEnum.HOWBUY.getCode());//无业务含义
        request.setOutletCode("W20170215");
        request.setOperIp("127.0.0.1");
        request.setTxChannel("1");
        QueryHighFundArrivalProofResponse response = queryHighFundArrivalProofFacade.execute(request);
        log.info("查询资金到账证明,request:{},response:{}", JSONObject.toJSONString(request), JSONObject.toJSONString(response));
        if (response != null && CollectionUtils.isNotEmpty(response.getHighFundArrivalProofBeans())) {
            return response.getHighFundArrivalProofBeans().get(0).getPdfPath();
        }
        return null;
    }

    /**
     * 获取[有限合伙产品]列表
     *
     * @return
     */
    private List<String> getLimitedCooperativeCodeList() {
        List<String> prodCodeList = highProductService.getIsLimitedCooperativeProducts();
        log.info("中台获取有限合伙产品列表：{}", JSONObject.toJSONString(prodCodeList));
        return prodCodeList == null ? Lists.newArrayList() : prodCodeList;
    }


    /**
     * 高端产品交易记录:
     *
     * <AUTHOR>
     * @date 2020/4/8
     */
    @Deprecated
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/queryAcctBalanceDealList.do")
    public Map<String, Object> queryAcctBalanceDealList(HttpServletRequest request) throws Exception {
        String conscustno = StringUtil.replaceNullStr(request.getParameter("conscustno"));
        String fundcode = StringUtil.replaceNullStr(request.getParameter("fundcode"));
        String busicode = StringUtil.replaceNullStr(request.getParameter("busicode"));
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        QueryConscustInfoRequest queryRequest = new QueryConscustInfoRequest();
        queryRequest.setConscustno(conscustno);
        log.info("QueryConscustInfoRequest：" + JSON.toJSONString(queryRequest));
        QueryConscustInfoResponse queryResponse = queryConscustInfoService.queryConscustInfo(queryRequest);
        log.info("QueryConscustInfoResponse：" + JSON.toJSONString(queryResponse));
        ConscustInfoDomain conscust = queryResponse.getConscustinfo();

        List<QueryDealOrderListResponse.DealOrderBean> dealOrderlist = this.queryDealOrderList(request, CrmCustInvestTypeEnum.getEnum(conscust.getInvsttype()), conscust.getHboneno());

//        获取[有限合伙产品]列表
        List<String> limitedCoopProdCodesList = getLimitedCooperativeCodeList();
        List<String> orderStatusList = Lists.newArrayList("1", "2", "3");//订单状态=[1-申请成功  2-部分确认 3-确认成功]
        List<String> buyBusiCodeList = Lists.newArrayList("1120", "1122"); //mbusiCode=1120-认购 1122-申购

        List<CrmDealOrderBean> crmdealOrderlist = new ArrayList<>();
        if (dealOrderlist != null && dealOrderlist.size() > 0) {

            // 判断常量表中合规标识：true启用，false停用
            LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
            boolean roleCpFlag = false;
            if (cacheMap != null && !cacheMap.isEmpty()) {
                roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
            }

            // 判断登录人员的角色中是否包括“合规人员”角色
            List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
            boolean isRoleCp = false;
            if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
                isRoleCp = true;
            }
            Map<String, PageCmJjxx1Jgflb> jgjjBeanMap = JgjjCache.getInstance().getJgjjBeanMap();

            // 通过Session获取产品广度信息
            HttpSession session = request.getSession();
            String topcpdata = (String) session.getAttribute("topcpdata");
            for (QueryDealOrderListResponse.DealOrderBean dealorder : dealOrderlist) {
                if (isRoleCp && jgjjBeanMap.containsKey(dealorder.getProductCode()) && ("21".equals(jgjjBeanMap.get(dealorder.getProductCode()).getEjfl()) || "31".equals(jgjjBeanMap.get(dealorder.getProductCode()).getEjfl()) || "41".equals(jgjjBeanMap.get(dealorder.getProductCode()).getEjfl()) || "61".equals(jgjjBeanMap.get(dealorder.getProductCode()).getEjfl()))) {
                    continue;
                }
                if(StringUtil.isNotNullStr(fundcode) && !fundcode.equals(dealorder.getProductCode()) /*&& !fundcode.equals(dealorder.getMjjdm())*/){
                	continue;
            	}
                JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(dealorder.getProductCode(), false);

                if (jjxxInfo == null) {
                    log.error("持仓记录数据，基金代码：{} 不存在！", dealorder.getProductCode());
                    continue;
                }

                // 获取产品分类字段信息
                String cpfl = jjxxInfo.getCpfl();

                // 获取是否香港字段信息
                String sfxg = jjxxInfo.getSfxg();

                // 获取是否标准固收信息
                String sfbzgs = jjxxInfo.getSfbzgs();
                if ("32".equals(topcpdata) && jjxxInfo != null) {
                    // 产品过滤条件：只显示满足 （产品分类 = 1-固定收益、2-公募基金、4-私募基金、5-券商集合理财、10-特殊私募）
                    // 且 是否香港 = 否 的产品 且 固收类型（是否标准固收） != （0-股权固收、1-正常固收）
                    if (!("1".equals(cpfl) || "2".equals(cpfl) || "4".equals(cpfl) || "5".equals(cpfl) || "10".equals(cpfl)) || "1".equals(sfxg) || "0".equals(sfbzgs) || "1".equals(sfbzgs)) {
                        continue;
                    }
                }

                //if(StringUtil.isNotNullStr(busicode) && (!busicode.equals(dealorder.getmBusiCode()) || (StaticVar.MBUSICODE_SMGQHK.equals(busicode) && ))){
                //选择了私募股权回款查询，需要
                if (StringUtil.isNotNullStr(busicode) && StaticVar.MBUSICODE_SMGQHK.equals(busicode) && !(jjxxInfo != null && StaticVar.HMCPX_PEVC.equals(jjxxInfo.getHmcpx()) && StaticVar.MBUSICODE_FH.equals(dealorder.getmBusiCode()))) {
                    continue;
                }
                //选择的非分红和私募股权回款的，去除不相符的交易记录
                if (StringUtil.isNotNullStr(busicode) && !StaticVar.MBUSICODE_SMGQHK.equals(busicode) && !busicode.equals(dealorder.getmBusiCode())) {
                    continue;
                }
                //选择的分红，但是符合私募股权回款的也要去除
                if (StringUtil.isNotNullStr(busicode) && StaticVar.MBUSICODE_FH.equals(busicode) && jjxxInfo != null && StaticVar.HMCPX_PEVC.equals(jjxxInfo.getHmcpx()) && StaticVar.MBUSICODE_FH.equals(dealorder.getmBusiCode())) {
                    continue;
                }
                //是否为【有限合伙产品】
                boolean limitedCoopProd = limitedCoopProdCodesList.contains(dealorder.getProductCode());


                //产品属于【有限合伙产品】， mbusiCode=1120-认购  1122-申购
                // 且 订单状态=[1-申请成功  2-部分确认 3-确认成功]，付款=4-成功 ,支付方式=01-自划款   的，
                // 才查询 到账证明
                boolean needQueryPdfpath = false;
                if (limitedCoopProd &&
                        dealorder.getmBusiCode() != null && buyBusiCodeList.contains(dealorder.getmBusiCode()) &&
                        dealorder.getOrderStatus() != null && orderStatusList.contains(dealorder.getOrderStatus()) &&
                        "4".equals(dealorder.getPayStatus()) &&
                        "01".equals(dealorder.getPaymentType())
                ) {
                    needQueryPdfpath = true;
                }


                if (StringUtil.isNotNullStr(dealorder.getPayStatus())) {
                    dealorder.setPayStatus(ConstantCache.getInstance().getConstantKeyVal("dealpayStatus").get(dealorder.getPayStatus()));
                }

                if (StringUtil.isNotNullStr(dealorder.getOrderStatus())) {
                    dealorder.setOrderStatus(ConstantCache.getInstance().getConstantKeyVal("dealorderStatus").get(dealorder.getOrderStatus()));
                }

                if (StringUtil.isNotNullStr(dealorder.getBankCode()) && !"null".equals(dealorder.getBankCode())) {
                    dealorder.setBankCode(ConstantCache.getInstance().getConstantKeyVal("bankType").get(dealorder.getBankCode()));
                } else {
                    dealorder.setBankCode("");
                }

                if (StringUtil.isNullStr(dealorder.getBankAcct()) || "null".equals(dealorder.getBankAcct())) {
                    dealorder.setBankAcct("");
                } else {
                    dealorder.setBankAcct(dealorder.getBankAcct().replace(" ", ""));
                }


                if (StringUtil.isNotNullStr(dealorder.getmBusiCode())) {
                    if (jjxxInfo != null && StaticVar.HMCPX_PEVC.equals(jjxxInfo.getHmcpx()) && StaticVar.MBUSICODE_FH.equals(dealorder.getmBusiCode())) {
                        dealorder.setmBusiCode(ConstantCache.getInstance().getConstantKeyVal("mBusiCode").get(StaticVar.MBUSICODE_SMGQHK));
                    } else {
                        dealorder.setmBusiCode(ConstantCache.getInstance().getConstantKeyVal("mBusiCode").get(dealorder.getmBusiCode()));
                    }
                }
                if (dealorder.getAckAmt() == null) {
                    dealorder.setAckAmt(new BigDecimal("0.00"));
                }

                if (dealorder.getAppAmt() == null) {
                    dealorder.setAppAmt(new BigDecimal("0.00"));
                }

                if (dealorder.getAckVol() == null) {
                    dealorder.setAckVol(new BigDecimal("0.00"));
                }
                if (dealorder.getNav() == null) {
                    dealorder.setNav(new BigDecimal("0.00"));
                }
                if (dealorder.getFee() == null) {
                    dealorder.setFee(new BigDecimal("0.00"));
                }
                CrmDealOrderBean crmdealorder = new CrmDealOrderBean();
                try {
                    BeanUtils.copyProperties(dealorder, crmdealorder);
                } catch (Exception e) {
                }
                if (jjxxInfo != null) {
                    crmdealorder.setClrq(jjxxInfo.getClrq());
                }
                //查询对应预约的手续费
                Map<String, String> paraminfo = new HashMap<String, String>(1);
                paraminfo.put("dealno", dealorder.getDealNo());
                Map<String, Object> orderinfo = prebookproductinfoService.getPreFeeBydealno(paraminfo);
                if (orderinfo != null && orderinfo.get("FEE") != null) {
                    crmdealorder.setPrefee(new BigDecimal(StringUtil.replaceNullStr(orderinfo.get("FEE"))));
                }

                crmdealorder.setLimitedCoopProd(limitedCoopProd);

                //到账证明pdf文件地址
                if (needQueryPdfpath) {
                    String arrivalProofPdfFilePath = getHighFundArrivalPdfFilePath(dealorder.getDealNo());
                    crmdealorder.setArrivalProofPdfFilePath(arrivalProofPdfFilePath);
                }

                // 设置业务类型（内容包括：“常规”、“好臻”、“海外”三种类型）
                handleBusiTypeName(dealorder.getDisCode(), dealorder.getHkSaleFlag(), crmdealorder);

                crmdealOrderlist.add(crmdealorder);
            }

        }
        StringBuilder sb = new StringBuilder();
        //选择交易类型的情况
        if (StringUtil.isNotNullStr(busicode)) {
            if (crmdealOrderlist.size() > 0) {
                sb.append("该交易类型共" + crmdealOrderlist.size() + "条记录，");
                BigDecimal totalAppAmt = new BigDecimal("0.00");
                BigDecimal totalAppFee = new BigDecimal("0.00");
                BigDecimal totalAckAmt = new BigDecimal("0.00");
                BigDecimal totalAckFee = new BigDecimal("0.00");
                BigDecimal totalAckVol = new BigDecimal("0.00");
                for (CrmDealOrderBean bean : crmdealOrderlist) {
                    totalAppAmt = totalAppAmt.add(bean.getAppAmt());
                    if (bean.getPrefee() != null) {
                        totalAppFee = totalAppFee.add(bean.getPrefee());
                    }
                    totalAckAmt = totalAckAmt.add(bean.getAckAmt());
                    totalAckFee = totalAckFee.add(bean.getFee());
                    totalAckVol = totalAckVol.add(bean.getAckVol());
                }
                sb.append("累计申请金额：" + totalAppAmt.toPlainString() + "，申请手续费：" + totalAppFee.toPlainString() + "，确认金额：" + totalAckAmt.toPlainString() + "，确认手续费：" + totalAckFee.toPlainString() + "，确认份额：" + totalAckVol.toPlainString());
            } else {
                //没有查询到数据的情况
                sb.append("该交易类型共0条记录，累计申请金额：0.00，申请手续费：0.00，确认金额：0.00，确认手续费：0.00，确认份额：0.00");
            }
        } else {
            //选择全部的情况
            sb.append("可根据筛选的交易类型，汇总显示对应交易情况数据!");
        }
        resultMap.put("resultStr", sb.toString());
        resultMap.put("total", crmdealOrderlist.size());
        resultMap.put("rows", crmdealOrderlist);
        return resultMap;
    }

    /**
     * 设置业务类型名称
     * <p>
     * 取值逻辑如下
     * 好臻：分销渠道=好臻分销
     * 海外：分销渠道=好买分销，且好买香港代销=是
     * 常规：除好臻及海外之外的场景，统一等于常规
     *
     * @param disCode      分销代码
     * @param hkSaleFlag   好买香港代销标识 0-否; 1-是
     * @param crmdealorder
     */
    private void handleBusiTypeName(String disCode, String hkSaleFlag, CrmDealOrderBean crmdealorder) {
        String busiTypeName;
        if (DisChannelCodeEnum.HZ.getCode().equals(disCode)) {
            busiTypeName = "好臻";
        } else if (DisChannelCodeEnum.HOWBUY.getCode().equals(disCode) && "1".equals(hkSaleFlag)) {
            busiTypeName = "海外";
        } else {
            busiTypeName = "常规";
        }
        crmdealorder.setBusiTypeName(busiTypeName);
    }


    /**
     * 查询中台-客户复购意向
     *
     * @param request
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/queryCustRepurchaseProtocolFacade.do")
    public Map<String, Object> queryCustRepurchaseProtocolFacade(HttpServletRequest request, @RequestParam(value = "page", defaultValue = "1") Integer pageNo
            , @RequestParam(value = "rows", defaultValue = "10") Integer pageSize) throws Exception {
        // 设置查询参数
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("rows", 0);
        resultMap.put("total", 0);
        String pubcustno = request.getParameter("pubcustno");

        // 判断参数是否为空
        if (StringUtils.isBlank(pubcustno)) {
            return resultMap;
        }

        try {
            QueryCustRepurchaseProtocolResposne dubboRsp = queryCustRepurchaseProtocol(pubcustno, pageNo, pageSize);
            String code = dubboRsp.getReturnCode();
            if ("Z0000000".equals(code)) {
                List<CustRepurchaseProtocolBean> resultList = dubboRsp.getCustRepurchaseProtocolList();
                List<CustRepurchaseProtocolBeanLocal> localList = new ArrayList();

                // 判断常量表中合规标识：true启用，false停用
                LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
                boolean roleCpFlag = false;
                if (cacheMap != null && !cacheMap.isEmpty()) {
                    roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
                }

                // 判断登录人员的角色中是否包括“合规人员”角色
                List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
                boolean isRoleCp = false;
                if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
                    isRoleCp = true;
                }
                Map<String, PageCmJjxx1Jgflb> jgjjBeanMap = JgjjCache.getInstance().getJgjjBeanMap();
                for (CustRepurchaseProtocolBean resultInfo : resultList) {
                    if (isRoleCp && jgjjBeanMap.containsKey(resultInfo.getFundCode()) && ("21".equals(jgjjBeanMap.get(resultInfo.getFundCode()).getEjfl()) || "31".equals(jgjjBeanMap.get(resultInfo.getFundCode()).getEjfl()) || "41".equals(jgjjBeanMap.get(resultInfo.getFundCode()).getEjfl()) || "61".equals(jgjjBeanMap.get(resultInfo.getFundCode()).getEjfl()))) {
                        continue;
                    }

                    CustRepurchaseProtocolBeanLocal custRepurchaseProtocolBeanLocal = new CustRepurchaseProtocolBeanLocal();
                    BeanUtils.copyProperties(resultInfo, custRepurchaseProtocolBeanLocal);
                    JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(resultInfo.getFundCode(), false);

                    // 设置页面基金编码和基金名称
                    if (StringUtils.isNotBlank(resultInfo.getFundCode()) && jjxxInfo != null) {
                        custRepurchaseProtocolBeanLocal.setFundName(jjxxInfo.getJjjc());
                    }
                    localList.add(custRepurchaseProtocolBeanLocal);
                }
                resultMap.put("rows", localList);
                resultMap.put("total", localList.size());
            }
        } catch (Exception e) {
            log.error("中台接口调用异常queryCustRepurchaseProtocolFacade.execute(dubboREQ)：" + e.getMessage(), e);
        }
        return resultMap;
    }

    /**
     * 打开时序图弹出框
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/openChartDialog.do")
    public Object openChartDialog(HttpServletRequest request) {
        String chartType = request.getParameter("chartType");
        String chartName = ChartTypeEnum.getEnum(chartType).getDescription();
        String chartUnit = ChartTypeEnum.getEnum(chartType).getUnit();
        return "<div style='margin-left: 10px;font-size:14px;color:red;'>温磐提示:时间序列数据目前正在内测中，请暂时不要转发，谢谢<div style='display: none'>【" + chartName + "|" + chartUnit + "】</div></div>";
    }

    /**
     * 查询高端单基金日序数据
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/highSingFund.do")
    public SequentialVO highSingFund(HttpServletRequest request) {
        String chartType = request.getParameter("chartType");
        String fundCode = request.getParameter("fundCode");
        String conscustno = request.getParameter("conscustno");
        String startDay = request.getParameter("startDay");
        String endDay = request.getParameter("endDay");
        String isNAProduct = request.getParameter("isNAProduct");
        String currencyRate = request.getParameter("currencyRate");
        String isNAProductTrue = "true";
        boolean isNAProductBool = isNAProductTrue.equals(isNAProduct);

        String res = highDealOrderSupport.highSingFund(fundCode, conscustno, startDay, endDay);

        SequentialResponse sequentialResponse = JSON.parseObject(res, SequentialResponse.class);
        if((isNAProductBool && ChartTypeEnum.BALANCE_AMT.getCode().equals(chartType))
                || ChartTypeEnum.BALANCE_FACTOR.getCode().equals(chartType) ){
            ConscustInfoDomain conscustInfoDomain = highDealOrderSupport.queryConscustInfo(conscustno);
            setBalanceFactor(sequentialResponse.getAssets(), fundCode, conscustInfoDomain.getHboneno());
        }
        return getSequentialVoByResponse(sequentialResponse, chartType, false, isNAProductBool, currencyRate);
    }

    /**
     * @description 设平衡因子
     * @param dataList
     * @param fundCode
     * @param hboneNo
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/5 19:46
     * @since JDK 1.8
     */
    private void setBalanceFactor(List<SequentialDataDTO> dataList, String fundCode, String hboneNo){
        if(CollectionUtils.isNotEmpty(dataList)) {
//            String startDate = dataList.get(0).getIncomeDt();
//            String endDate = dataList.get(dataList.size() - 1).getIncomeDt();
            BalanceFactorListRequest request = new BalanceFactorListRequest();
//            request.setFundCode(fundCode);
//            request.setStartDt(startDate);
//            request.setEndDt(endDate);
            request.setHboneNo(hboneNo);
            BalanceFactorListRequest.FundNavDtVO fundNavDtVO = new BalanceFactorListRequest.FundNavDtVO();
            fundNavDtVO.setFundCode(fundCode);
            List<String> incomeDtList = dataList.stream().map(SequentialDataDTO::getIncomeDt).collect(Collectors.toList());
            fundNavDtVO.setNavDtList(incomeDtList);
            request.setFundNavDtVOList(Lists.newArrayList(fundNavDtVO));


            BalanceFactorListResponse balanceFactorListResponse = hkBalanceFactorService.queryBalanceFactorList(request);
            if(balanceFactorListResponse.isSuccessful()){
                Map<String, BigDecimal> balanceFactorMap = balanceFactorListResponse.getBalanceFactorDomains().stream().collect(Collectors.toMap(BalanceFactorListResponse.BalanceFactorDomain::getNavDt, BalanceFactorListResponse.BalanceFactorDomain::getBalanceFactor));
                dataList.forEach(sequentialDataDTO -> {
                    BigDecimal balanceFactor = balanceFactorMap.get(sequentialDataDTO.getIncomeDt());
                    if(balanceFactor != null){
                        sequentialDataDTO.setBalanceFactor(balanceFactor);
                    }
                });
            }
        }
    }

    /**
     * @description 把返回的数据处理成页面需要的
     * @param sequentialResponse
     * @param chartType
     * @param isSummary
     * @param isNAProduct
     * @param currencyRate
     * @return com.howbuy.crm.hb.web.vo.SequentialVO
     * @author: jianjian.yang
     * @date: 2023/7/5 17:30
     * @since JDK 1.8
     */
    private SequentialVO getSequentialVoByResponse(SequentialResponse sequentialResponse, String chartType, boolean isSummary, boolean isNAProduct, String currencyRate){
        SequentialVO sequentialVO = new SequentialVO();
        List<List<String>> showDataList = Lists.newArrayList();
        List<List<String>> showDataCurrencyList = Lists.newArrayList();
        if (HighDealOrderSupport.ZT_SUCCESS_CODE.equals(sequentialResponse.getResCode())) {
            List<SequentialDataDTO> dataList = sequentialResponse.getAssets();
            if(CollectionUtils.isNotEmpty(dataList)) {
                List<BigDecimal> chartValueList = Lists.newArrayList();
                List<String> chartDateList = Lists.newArrayList();
                //图表类型对应需要的字段映射
                SequentialMappingDTO sequentialMappingDTO = mappingTitleAndColumn(chartType, isSummary, isNAProduct, StringUtil.isNullStr(currencyRate) ? null : new BigDecimal(currencyRate));
                dataList.forEach(sequentialDataDTO -> {
                    List<String> sequentialDataList = Lists.newArrayList();
                    List<String> sequentialDataCurrencyList = Lists.newArrayList();
                    //表格及toolTip默认展示数据
                    sequentialMappingDTO.getShowColumnFunctionList().forEach(columnFunction -> {
                        //执行预先设定的函数
                        Object dtoResult = columnFunction.apply(sequentialDataDTO);
                        //空值处理，“”占位
                        if(dtoResult == null){
                            dtoResult = StringUtils.EMPTY;
                        }
                        if(dtoResult instanceof BigDecimal) {
                            sequentialDataList.add(CommonUtil.formatBigDecimalQfw((BigDecimal)dtoResult));
                        }else {
                            sequentialDataList.add((String)dtoResult);
                        }
                    });
                    //表格及toolTip展示汇率处理数据
                    if(currencyRate != null) {
                        sequentialMappingDTO.getShowCurrencyColumnFunctionList().forEach(columnFunction -> {
                            Object dtoResult = columnFunction.apply(sequentialDataDTO);
                            //空值处理，“”占位
                            if (dtoResult == null) {
                                dtoResult = StringUtils.EMPTY;
                            }
                            if(dtoResult instanceof BigDecimal) {
                                sequentialDataCurrencyList.add(CommonUtil.formatBigDecimalQfw((BigDecimal)dtoResult));
                            }else {
                                sequentialDataCurrencyList.add((String)dtoResult);
                            }
                        });
                    }
                    showDataList.add(sequentialDataList);
                    showDataCurrencyList.add(sequentialDataCurrencyList);
                    //图横坐标（时间）数据
                    Function<SequentialDataDTO, Object> chartDateFunction = sequentialMappingDTO.getValueColumnFunctionList().get(0);
                    //图纵坐标数据
                    Function<SequentialDataDTO, Object> chartValueFunction = sequentialMappingDTO.getValueColumnFunctionList().get(1);
                    chartDateList.add((String) chartDateFunction.apply(sequentialDataDTO));
                    chartValueList.add((BigDecimal) chartValueFunction.apply(sequentialDataDTO));
                });
                sequentialVO.setTitleList(sequentialMappingDTO.titleList);
                sequentialVO.setShowDataList(showDataList);
                sequentialVO.setShowDataCurrencyList(showDataCurrencyList);
                sequentialVO.setChartDateList(chartDateList);
                sequentialVO.setChartValueList(chartValueList);
            }
        }
        log.info("sequentialVO:{}", JSON.toJSONString(sequentialVO));
        return sequentialVO;
    }

    @RequestMapping("/highSingFundExport")
    public Object highSingFundExport(HttpServletRequest request, HttpServletResponse response){
        SequentialVO sequentialVO = highSingFund(request);
        return exportSequential(request, response, sequentialVO);
    }

    @RequestMapping("/highStagesFundExport")
    public Object highStagesFundExport(HttpServletRequest request, HttpServletResponse response){
        SequentialVO sequentialVO = highStagesFund(request);
        return exportSequential(request, response, sequentialVO);
    }

    @RequestMapping("/highClassfyAssetExport")
    public Object highClassfyAssetExport(HttpServletRequest request, HttpServletResponse response){
        SequentialVO sequentialVO = highClassfyAsset(request);
        return exportSequential(request, response, sequentialVO);
    }

    @RequestMapping("/highUserAssetExport")
    public Object highUserAssetExport(HttpServletRequest request, HttpServletResponse response){
        SequentialVO sequentialVO = highUserAsset(request);
        return exportSequential(request, response, sequentialVO);
    }

    /**
     * @description 导出时序表格
     * @param request
     * @param response
     * @param sequentialVO
     * @return java.lang.Object
     * @author: jianjian.yang
     * @date: 2023/7/11 15:54
     * @since JDK 1.8
     */
    private Object exportSequential(HttpServletRequest request, HttpServletResponse response, SequentialVO sequentialVO){
        if(CollectionUtils.isEmpty(sequentialVO.getShowDataList())){
            return "dataError";
        }
        String valueUnit = request.getParameter("valueUnit");
        String showCurrencyValue = request.getParameter("showCurrencyValue");
        String excelName = "时序数据导出.xls";
        ServletOutputStream os = null;
        try {
            // 清空输出流
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((excelName).getBytes(), "iso8859-1"));
            os = response.getOutputStream();
            // 建立excel文件
            WritableWorkbook wbook = Workbook.createWorkbook(os);
            WritableSheet wsheet = wbook.createSheet("sheet", 1);

            WritableFont fontCss = new WritableFont(WritableFont.ARIAL, 12,	WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
            WritableCellFormat formatTitle = new WritableCellFormat(fontCss);
            wsheet.mergeCells(0, 0, sequentialVO.getTitleList().size() - 1, 0);
            formatTitle.setBackground(Colour.WHITE);
            formatTitle.setBorder(Border.ALL, BorderLineStyle.THIN);
            formatTitle.setAlignment(Alignment.RIGHT);
            wsheet.addCell(new Label(0, 0, "单位：" + valueUnit, formatTitle));
            fontCss = new jxl.write.WritableFont(WritableFont.ARIAL, 9,	WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
            formatTitle = new WritableCellFormat(fontCss);
            formatTitle.setAlignment(Alignment.CENTRE);
            formatTitle.setBorder(Border.ALL, BorderLineStyle.THIN);
            WritableCellFormat topTitle = new WritableCellFormat(fontCss);
            topTitle.setBorder(Border.ALL, BorderLineStyle.THIN);
            topTitle.setAlignment(Alignment.CENTRE);
            topTitle.setVerticalAlignment(VerticalAlignment.CENTRE);

            // 对合并后的单元格进行样式设置，包括垂直居中显示
            WritableFont contentFont = new WritableFont(WritableFont.ARIAL,	9, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
            WritableCellFormat formatContent = new WritableCellFormat(contentFont);
            formatContent.setBackground(Colour.WHITE);
            formatContent.setBorder(Border.ALL, BorderLineStyle.THIN);
            formatContent.setVerticalAlignment(VerticalAlignment.CENTRE);

            // 设置标题：列（从0开始），行（从1开始），显示内容
            int column = 0;
            for(String title : sequentialVO.getTitleList()){
                wsheet.setColumnView(column, 25);
                wsheet.addCell(new Label(column, 1, title, formatTitle));
                column++;
            }
            int row = 2;
            List<List<String>> showDataList = sequentialVO.getShowDataList();
            if(YesOrNoEnum.YES.equals(showCurrencyValue)){
                showDataList = sequentialVO.getShowDataCurrencyList();
            }
            for(List<String> dataList : showDataList){
                int columnData = 0;
                for(String data : dataList) {
                    wsheet.addCell(new Label(columnData, row, data, formatContent));
                    columnData++;
                }
                row++;
            }
            // 主体内容生成结束
            wbook.write(); // 写入文件
            if(wbook!=null){
                wbook.close();
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
            return "fail";
        }finally {
            if(os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return "success";
    }

    /**
     * 查询高端分期成立基金日序数据
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/highStagesFund.do")
    public SequentialVO highStagesFund(HttpServletRequest request) {
        String chartType = request.getParameter("chartType");
        String fundCode = request.getParameter("fundCode");
        String conscustno = request.getParameter("conscustno");
        String startDay = request.getParameter("startDay");
        String endDay = request.getParameter("endDay");

        String res = highDealOrderSupport.highStagesFund(fundCode, conscustno, startDay, endDay);

        String currencyRate = request.getParameter("currencyRate");
        SequentialResponse sequentialResponse = JSON.parseObject(res, SequentialResponse.class);
        return getSequentialVoByResponse(sequentialResponse, chartType, true, false, currencyRate);
    }

    /**
     * @description 图表类型对应需要的字段映射
     * @param chartType
     * @param isSummary
     * @param isNAProduct
     * @param currencyRate
     * @return com.howbuy.crm.hb.web.controller.custinfo.HighDealOrderNewController.SequentialMappingDTO
     * @author: jianjian.yang
     * @date: 2023/7/5 17:33
     * @since JDK 1.8
     */
    private SequentialMappingDTO mappingTitleAndColumn(String chartType, boolean isSummary, boolean isNAProduct, BigDecimal currencyRate){
        SequentialMappingDTO sequentialMappingDTO = new SequentialMappingDTO();
        List<String> titleList = Lists.newArrayList();
        List<Function<SequentialDataDTO, Object>> showColumnFunctionList = Lists.newArrayList();
        List<Function<SequentialDataDTO, Object>> valueColumnFunctionList = Lists.newArrayList();
        List<Function<SequentialDataDTO, Object>> showCurrencyColumnFunctionList = Lists.newArrayList();
        sequentialMappingDTO.setTitleList(titleList);
        sequentialMappingDTO.setShowColumnFunctionList(showColumnFunctionList);
        sequentialMappingDTO.setShowCurrencyColumnFunctionList(showCurrencyColumnFunctionList);
        sequentialMappingDTO.setValueColumnFunctionList(valueColumnFunctionList);

        titleList.add("日期");
        showColumnFunctionList.add(sequentialDataDTO -> DateUtil.getDateFormat(DateUtil.getString2Date(sequentialDataDTO.getIncomeDt(), com.howbuy.common.utils.DateUtil.SHORT_DATE_PATTERN), DateTimeUtil.DATE_PATTERN));
        if(currencyRate != null) {
            showCurrencyColumnFunctionList.add(sequentialDataDTO -> DateUtil.getDateFormat(DateUtil.getString2Date(sequentialDataDTO.getIncomeDt(), com.howbuy.common.utils.DateUtil.SHORT_DATE_PATTERN), DateTimeUtil.DATE_PATTERN));
        }
        valueColumnFunctionList.add(sequentialDataDTO -> DateUtil.getDateFormat(DateUtil.getString2Date(sequentialDataDTO.getIncomeDt(), com.howbuy.common.utils.DateUtil.SHORT_DATE_PATTERN), DateTimeUtil.DATE_PATTERN));

        ChartTypeEnum chartTypeEnum= ChartTypeEnum.getEnum(chartType);
        switch (chartTypeEnum){
            case BALANCE_VOL:
                mappingBalanceVol(sequentialMappingDTO, currencyRate);
                break;
            case BALANCE_AMT:
                mappingBalanceAmt(sequentialMappingDTO, isSummary, isNAProduct, currencyRate);
                break;
            case DAILY_ASSET:
                mappingDailyAsset(sequentialMappingDTO, isSummary, currencyRate);
                break;
            case CURRENT_ASSET:
                mappingCurrentAsset(sequentialMappingDTO, isSummary, currencyRate);
                break;
            case BALANCE_INCOME_NEW:
                mappingBalanceIncomeNew(sequentialMappingDTO, isSummary, currencyRate);
                break;
            case ACCUM_INCOME:
                mappingAccumIncome(sequentialMappingDTO, isSummary, currencyRate);
                break;
            case ACCUM_INCOME_NEW:
                mappingAccumIncomeNew(sequentialMappingDTO, currencyRate);
                break;
            case ACCUM_REALIZED_INCOME:
                mappingAccumRealizedIncome(sequentialMappingDTO, currencyRate);
                break;
            case RECEIV_MANAGE_FEE:
                mappingReceivManageFee(sequentialMappingDTO, currencyRate);
                break;
            case RECEIV_PREFORM_FEE:
                mappingReceivPreformFee(sequentialMappingDTO, currencyRate);
                break;
            case BALANCE_FACTOR:
                mappingBalanceFactor(sequentialMappingDTO, currencyRate);
                break;
            case NA_BALANCE_AMT:
                mappingNABalanceAmt(sequentialMappingDTO, isSummary, currencyRate);
                break;
            case BALANCE_PAIDINAMT_NEW:
                mappingAccumPaidinAmount(sequentialMappingDTO, currencyRate);
                break;
            case BALANCE_UNPAIDINAMT_NEW:
                mappingInvestedAmount(sequentialMappingDTO, currencyRate);
                break;
            case BALANCE_FLOAT_INCOME:
                mappingBalanceFloatIncome(sequentialMappingDTO, isSummary, currencyRate);
                break;
            case BALANCE_COST:
                mappingBalanceCost(sequentialMappingDTO, currencyRate);
                break;
            case DAY_INCOME_GROWTH_RATE:
                mappingDayIncomeGrowthRate(sequentialMappingDTO);
                break;
            case ACCUM_COST:
                mappingAccumCost(sequentialMappingDTO, currencyRate);
                break;
            case ACCUM_COLLECTION:
                mappingAccumCollection(sequentialMappingDTO, currencyRate);
                break;
            case ACCUM_COST_NEW:
                mappingAccumCostNew(sequentialMappingDTO, currencyRate);
                break;
            case UNIT_BALANCE_COST_EX_FEE:
                mappingUnitBalanceCost(sequentialMappingDTO, currencyRate);
             default:
        }
        return sequentialMappingDTO;
    }

    /**
     * @description 持仓份额
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:53
     * @since JDK 1.8
     */
    private void mappingBalanceVol(SequentialMappingDTO sequentialMappingDTO,
                         BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("持仓份额");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getBalanceVol);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getBalanceVol);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getBalanceVol().multiply(currencyRate));
        }
    }

    /**
     * @description 已实现收益
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:53
     * @since JDK 1.8
     */
    private void mappingAccumRealizedIncome(SequentialMappingDTO sequentialMappingDTO,
                                   BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("已实现收益");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getAccumRealizedIncome);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getAccumRealizedIncome);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getAccumRealizedIncome().multiply(currencyRate));
        }
    }

    /**
     * @description 持仓总成本
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:53
     * @since JDK 1.8
     */
    private void mappingBalanceCost(SequentialMappingDTO sequentialMappingDTO,
                                   BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("持仓总成本");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getBalanceCost);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getBalanceCost);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getBalanceCost().multiply(currencyRate));
        }
    }

    /**
     * @description 累计应收管理费
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:53
     * @since JDK 1.8
     */
    private void mappingReceivManageFee(SequentialMappingDTO sequentialMappingDTO,
                                   BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("累计应收管理费");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getReceivManageFee);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getReceivManageFee);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getReceivManageFee().multiply(currencyRate));
        }
    }

    /**
     * @description 累计应收业绩报酬
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:53
     * @since JDK 1.8
     */
    private void mappingReceivPreformFee(SequentialMappingDTO sequentialMappingDTO,
                                   BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("累计应收业绩报酬");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getReceivPreformFee);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getReceivPreformFee);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getReceivPreformFee().multiply(currencyRate));
        }
    }

    /**
     * @description 平衡因子
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:54
     * @since JDK 1.8
     */
    private void mappingBalanceFactor(SequentialMappingDTO sequentialMappingDTO,
                                      BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("平衡因子");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getBalanceFactor);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getBalanceFactor);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> Objects.equals(sequentialDataDTO.getBalanceFactor(), null) ? BigDecimal.ZERO : sequentialDataDTO.getBalanceFactor().multiply(currencyRate) );
        }
    }

    /**
     * @description 总实缴金额
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:54
     * @since JDK 1.8
     */
    private void mappingAccumPaidinAmount(SequentialMappingDTO sequentialMappingDTO,
                                   BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("总实缴金额");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getAccumPaidinAmount);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getAccumPaidinAmount);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getAccumPaidinAmount().multiply(currencyRate));
        }
    }

    /**
     * @description 待投金额
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:54
     * @since JDK 1.8
     */
    private void mappingInvestedAmount(SequentialMappingDTO sequentialMappingDTO,
                                   BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("待投金额");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getInvestedAmount);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getInvestedAmount);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getInvestedAmount().multiply(currencyRate));
        }
    }

    /**
     * @description 最新资产增长率
     * @param sequentialMappingDTO
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:54
     * @since JDK 1.8
     */
    private void mappingDayIncomeGrowthRate(SequentialMappingDTO sequentialMappingDTO){
        sequentialMappingDTO.getTitleList().add("最新资产增长率");
        sequentialMappingDTO.getShowColumnFunctionList().add(sequentialDataDTO -> showRate(sequentialDataDTO.getDayIncomeGrowthRate()));
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getDayIncomeGrowthRate);
        sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(SequentialDataDTO::getDayIncomeGrowthRate);
    }

    /**
     * @description 累计成本
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:54
     * @since JDK 1.8
     */
    private void mappingAccumCost(SequentialMappingDTO sequentialMappingDTO,
                                       BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("累计成本");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getAccumCost);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getAccumCost);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getAccumCost().multiply(currencyRate));
        }
    }

    /**
     * @description 累计总回款
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:54
     * @since JDK 1.8
     */
    private void mappingAccumCollection(SequentialMappingDTO sequentialMappingDTO,
                                       BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("累计总回款");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getAccumCollection);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getAccumCollection);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getAccumCollection().multiply(currencyRate));
        }
    }

    /**
     * @description 初始投资成本
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:55
     * @since JDK 1.8
     */
    private void mappingAccumCostNew(SequentialMappingDTO sequentialMappingDTO,
                                       BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("初始投资成本");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getAccumCostNew);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getAccumCostNew);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getAccumCostNew().multiply(currencyRate));
        }
    }

    /**
     * @description 单位持仓成本
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/8/16 19:39
     * @since JDK 1.8
     */
    private void mappingUnitBalanceCost(SequentialMappingDTO sequentialMappingDTO,
                                     BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("单位持仓成本");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getUnitBalanceCostExFee);
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getUnitBalanceCostExFee);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getUnitBalanceCostExFee().multiply(currencyRate));
        }
    }

    /**
     * @description 最新收益
     * @param sequentialMappingDTO
     * @param isSummary
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:55
     * @since JDK 1.8
     */
    private void mappingDailyAsset(SequentialMappingDTO sequentialMappingDTO,
                                   boolean isSummary,
                         BigDecimal currencyRate){
        if(isSummary){
            sequentialMappingDTO.getTitleList().add("最新收益");
            sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getDailyAsset);
            if(currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getDailyAsset().multiply(currencyRate));
            }
        }else {
            sequentialMappingDTO.getTitleList().addAll(Arrays.asList("最新收益","最新收益率","单位净值"));
            sequentialMappingDTO.getShowColumnFunctionList().addAll(Arrays.asList(SequentialDataDTO::getDailyAsset,
                    sequentialDataDTO -> showRate(Objects.equals(sequentialDataDTO.getDayAssetRate(), null) ? BigDecimal.ZERO : sequentialDataDTO.getDayAssetRate()),
                    SequentialDataDTO::getNav));
            if(currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().addAll(Arrays.asList(sequentialDataDTO -> sequentialDataDTO.getDailyAsset().multiply(currencyRate),
                        sequentialDataDTO -> showRate(Objects.equals(sequentialDataDTO.getDayAssetRate(), null) ? BigDecimal.ZERO : sequentialDataDTO.getDayAssetRate()),
                        SequentialDataDTO::getNav));
            }
        }
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getDailyAsset);
    }

    /**
     * @description 持仓收益
     * @param sequentialMappingDTO
     * @param isSummary
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:55
     * @since JDK 1.8
     */
    private void mappingCurrentAsset(SequentialMappingDTO sequentialMappingDTO,
                                     boolean isSummary,
                                     BigDecimal currencyRate){
        if(isSummary){
            sequentialMappingDTO.getTitleList().add("持仓收益");
            sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getCurrentAsset);
            if(currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getCurrentAsset().multiply(currencyRate));
            }
        }else {
            sequentialMappingDTO.getTitleList().addAll(Arrays.asList("持仓收益","持仓收益率","单位净值"));
            sequentialMappingDTO.getShowColumnFunctionList().addAll(Arrays.asList(SequentialDataDTO::getCurrentAsset,
                    sequentialDataDTO -> showRate(sequentialDataDTO.getCurrentIncomeRate()),
                    SequentialDataDTO::getNav));
            if(currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().addAll(Arrays.asList(sequentialDataDTO -> sequentialDataDTO.getCurrentAsset().multiply(currencyRate),
                        sequentialDataDTO -> showRate(sequentialDataDTO.getCurrentIncomeRate()),
                        SequentialDataDTO::getNav));
            }
        }
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getCurrentAsset);
    }

    /**
     * @description 持仓收益_新
     * @param sequentialMappingDTO
     * @param isSummary
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:55
     * @since JDK 1.8
     */
    private void mappingBalanceIncomeNew(SequentialMappingDTO sequentialMappingDTO,
                                         boolean isSummary,
                                     BigDecimal currencyRate){
        if(isSummary){
            sequentialMappingDTO.getTitleList().add("持仓收益_新");
            sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getCurrentAsset);
            if(currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getCurrentAsset().multiply(currencyRate));
            }
            sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getCurrentAsset);
        }else {
            sequentialMappingDTO.getTitleList().add("持仓收益_新");
            sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getBalanceIncomeNew);
            if (currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getBalanceIncomeNew().multiply(currencyRate));
            }
            sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getBalanceIncomeNew);
        }
    }

    /**
     * @description 累计收益
     * @param sequentialMappingDTO
     * @param isSummary
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:55
     * @since JDK 1.8
     */
    private void mappingAccumIncome(SequentialMappingDTO sequentialMappingDTO,
                                    boolean isSummary,
                                    BigDecimal currencyRate){
        if(isSummary){
            sequentialMappingDTO.getTitleList().add("累计收益");
            sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getAccumIncome);
            if(currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getAccumIncome().multiply(currencyRate));
            }
        }else {
            sequentialMappingDTO.getTitleList().addAll(Arrays.asList("累计收益","累计收益率","单位净值"));
            sequentialMappingDTO.getShowColumnFunctionList().addAll(Arrays.asList(SequentialDataDTO::getAccumIncome,
                    sequentialDataDTO -> showRate(sequentialDataDTO.getTotalIncomeRate()),
                    SequentialDataDTO::getNav));
            if(currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().addAll(Arrays.asList(sequentialDataDTO -> sequentialDataDTO.getAccumIncome().multiply(currencyRate),
                        sequentialDataDTO -> showRate(sequentialDataDTO.getTotalIncomeRate()),
                        SequentialDataDTO::getNav));
            }
        }
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getAccumIncome);
    }

    /**
     * @description 累计收益
     * @param sequentialMappingDTO
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:55
     * @since JDK 1.8
     */
    private void mappingAccumIncomeNew(SequentialMappingDTO sequentialMappingDTO,
                                    BigDecimal currencyRate){
        sequentialMappingDTO.getTitleList().add("累计收益");
        sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getAccumIncomeNew);
        if(currencyRate != null) {
            sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getAccumIncomeNew().multiply(currencyRate));
        }
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getAccumIncomeNew);
    }

    /**
     * @description NA费前市值
     * @param sequentialMappingDTO
     * @param isSummary
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:55
     * @since JDK 1.8
     */
    private void mappingNABalanceAmt(SequentialMappingDTO sequentialMappingDTO,
                                     boolean isSummary,
                                     BigDecimal currencyRate){
        if(isSummary){
            sequentialMappingDTO.getTitleList().add("NA费前市值");
            sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getBalanceAmt);
            if(currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getBalanceAmt().multiply(currencyRate));
            }
        }else {
            sequentialMappingDTO.getTitleList().addAll(Arrays.asList("持仓份额", "NA费前市值","单位净值"));
            sequentialMappingDTO.getShowColumnFunctionList().addAll(Arrays.asList(SequentialDataDTO::getBalanceVol, SequentialDataDTO::getBalanceAmt, SequentialDataDTO::getNav));
            if(currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().addAll(Arrays.asList(SequentialDataDTO::getBalanceVol,
                        sequentialDataDTO -> sequentialDataDTO.getBalanceAmt().multiply(currencyRate),
                        SequentialDataDTO::getNav));
            }
        }
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getBalanceAmt);
    }

    /**
     * @description 持仓浮盈亏
     * @param sequentialMappingDTO
     * @param isSummary
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/6 14:55
     * @since JDK 1.8
     */
    private void mappingBalanceFloatIncome(SequentialMappingDTO sequentialMappingDTO,
                                           boolean isSummary,
                                     BigDecimal currencyRate){
        if(isSummary){
            sequentialMappingDTO.getTitleList().add("持仓浮盈亏");
            sequentialMappingDTO.getShowColumnFunctionList().add(SequentialDataDTO::getBalanceFloatIncome);
            if(currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().add(sequentialDataDTO -> sequentialDataDTO.getAccumIncome().multiply(currencyRate));
            }
        }else {
            sequentialMappingDTO.getTitleList().addAll(Arrays.asList("持仓浮盈亏", "持仓浮盈亏率", "单位净值"));
            sequentialMappingDTO.getShowColumnFunctionList().addAll(Arrays.asList(SequentialDataDTO::getBalanceFloatIncome,
                    sequentialDataDTO -> showRate(sequentialDataDTO.getBalanceFloatIncomeRate()),
                    SequentialDataDTO::getNav));
            if(currencyRate != null) {
                sequentialMappingDTO.getShowCurrencyColumnFunctionList().addAll(Arrays.asList(sequentialDataDTO -> sequentialDataDTO.getBalanceFloatIncome().multiply(currencyRate),
                        sequentialDataDTO -> showRate(sequentialDataDTO.getBalanceFloatIncomeRate()),
                        SequentialDataDTO::getNav));
            }
        }
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getAccumIncome);
    }

    /**
     * @description 对应持仓市值类型
     * @param sequentialMappingDTO
     * @param isSummary
     * @param isNAProduct
     * @param currencyRate
     * @return void
     * @author: jianjian.yang
     * @date: 2023/7/5 19:53
     * @since JDK 1.8
     */
    private void mappingBalanceAmt(SequentialMappingDTO sequentialMappingDTO,
                     boolean isSummary,
                     boolean isNAProduct,
                     BigDecimal currencyRate){
        if(isSummary){
            sequentialMappingDTO.getTitleList().add("参考市值");
            sequentialMappingDTO.getShowColumnFunctionList().add(sequentialDataDTO -> CommonUtil.formatBigDecimalQfw(sequentialDataDTO.getBalanceAmtExFee()));
        }else {
            if(isNAProduct){
                sequentialMappingDTO.getTitleList().addAll(Arrays.asList("持仓份额","NA费后市值","净值","累计应收管理费","累计应收业绩报酬","平衡因子"));
                sequentialMappingDTO.getShowColumnFunctionList().addAll(Arrays.asList(SequentialDataDTO::getBalanceVol,
                        SequentialDataDTO::getBalanceAmtExFee,
                        SequentialDataDTO::getNav,
                        SequentialDataDTO::getReceivManageFee,
                        SequentialDataDTO::getReceivPreformFee,
                        SequentialDataDTO::getBalanceFactor));
                if(currencyRate != null) {
                    sequentialMappingDTO.getShowCurrencyColumnFunctionList().addAll(Arrays.asList(sequentialDataDTO -> multiplyNotNull(sequentialDataDTO.getBalanceVol(), currencyRate),
                            sequentialDataDTO -> multiplyNotNull(sequentialDataDTO.getBalanceAmtExFee(), currencyRate),
                            SequentialDataDTO::getNav,
                            sequentialDataDTO -> multiplyNotNull(sequentialDataDTO.getReceivManageFee(), currencyRate),
                            sequentialDataDTO -> multiplyNotNull(sequentialDataDTO.getReceivPreformFee(), currencyRate),
                            sequentialDataDTO -> multiplyNotNull(sequentialDataDTO.getBalanceFactor(),currencyRate)));
                }
            }else {
                sequentialMappingDTO.getTitleList().addAll(Arrays.asList("持仓份额","参考市值","净值"));
                sequentialMappingDTO.getShowColumnFunctionList().addAll(Arrays.asList(SequentialDataDTO::getBalanceVol,
                        SequentialDataDTO::getBalanceAmtExFee,
                        SequentialDataDTO::getNav));
                if(currencyRate != null) {
                    sequentialMappingDTO.getShowCurrencyColumnFunctionList().addAll(Arrays.asList(sequentialDataDTO -> multiplyNotNull(sequentialDataDTO.getBalanceVol(), currencyRate),
                            sequentialDataDTO -> multiplyNotNull(sequentialDataDTO.getBalanceAmtExFee(), currencyRate),
                            SequentialDataDTO::getNav));
                }
            }
        }
        sequentialMappingDTO.getValueColumnFunctionList().add(SequentialDataDTO::getBalanceAmtExFee);
    }

    /**
     * @description 两数相乘，考虑空
     * @param num
     * @param rate
     * @return java.math.BigDecimal
     * @author: jianjian.yang
     * @date: 2023/8/31 17:25
     * @since JDK 1.8
     */
    private BigDecimal multiplyNotNull(BigDecimal num, BigDecimal rate){
        if(num == null || rate == null){
            return BigDecimal.ZERO;
        }
        return num.multiply(rate);
    }

    /**
     * @description 比率显示加%
     * @param rate
     * @return java.lang.String
     * @author: jianjian.yang
     * @date: 2023/7/6 15:34
     * @since JDK 1.8
     */
    private String showRate(BigDecimal rate){
        return CommonUtil.formatBigDecimalYieldRate(rate) + "%";
    }


    @ToString
    public static class SequentialMappingDTO{

        private List<String> titleList;

        private List<Function<SequentialDataDTO, Object>> showColumnFunctionList;

        private List<Function<SequentialDataDTO, Object>> showCurrencyColumnFunctionList;

        private List<Function<SequentialDataDTO, Object>> valueColumnFunctionList;

        public List<String> getTitleList() {
            return titleList;
        }

        public void setTitleList(List<String> titleList) {
            this.titleList = titleList;
        }

        public List<Function<SequentialDataDTO, Object>> getShowColumnFunctionList() {
            return showColumnFunctionList;
        }

        public void setShowColumnFunctionList(List<Function<SequentialDataDTO, Object>> showColumnFunctionList) {
            this.showColumnFunctionList = showColumnFunctionList;
        }

        public List<Function<SequentialDataDTO, Object>> getValueColumnFunctionList() {
            return valueColumnFunctionList;
        }

        public void setValueColumnFunctionList(List<Function<SequentialDataDTO, Object>> valueColumnFunctionList) {
            this.valueColumnFunctionList = valueColumnFunctionList;
        }

        public List<Function<SequentialDataDTO, Object>> getShowCurrencyColumnFunctionList() {
            return showCurrencyColumnFunctionList;
        }

        public void setShowCurrencyColumnFunctionList(List<Function<SequentialDataDTO, Object>> showCurrencyColumnFunctionList) {
            this.showCurrencyColumnFunctionList = showCurrencyColumnFunctionList;
        }
    }

    /**
     * @description:(请在此添加描述)
            把方法拆出来，以前三个方法共用了此逻辑
     * @return com.howbuy.crm.hb.domain.prosale.HighDealOrderChart
     * @author: yu.zhang
     * @date: 2023/2/24 18:11
     * @since JDK 1.8
     */
    public HighDealOrderChart getFmtValue(String chartType, JSONObject jl) {
        HighDealOrderChart chartData = new HighDealOrderChart();

        ChartTypeEnum chartTypeEnum = ChartTypeEnum.getEnum(chartType);

        String keyCode = chartTypeEnum.getCode();
        chartData.setChartDate(jl.getString("incomeDt"));

        BigDecimal chartValue = jl.getBigDecimal(keyCode);
        chartData.setChartValue(chartValue);

        // 格式化显示数字
        String fmtValue = "";
        String fmtRateValue = "";
        if (chartTypeEnum != null) {
            if (chartTypeEnum.getDescription().endsWith("率")) {
                fmtValue = CommonUtil.formatBigDecimalYieldRate(chartValue) + "%";
            } else {
                if ("持仓收益".equals(chartTypeEnum.getDescription())) {
                    // 追加显示持仓收益率字段
                    BigDecimal rateValue = jl.getBigDecimal("currentIncomeRate");
                    fmtValue = CommonUtil.formatBigDecimalQfw(chartValue);
                    fmtRateValue = CommonUtil.formatBigDecimalYieldRate(rateValue) + "%";
                } else if ("累计收益".equals(chartTypeEnum.getDescription())) {
                    // 追加显示累计收益率字段
                    BigDecimal rateValue = jl.getBigDecimal("totalIncomeRate");
                    fmtValue = CommonUtil.formatBigDecimalQfw(chartValue);
                    fmtRateValue = CommonUtil.formatBigDecimalYieldRate(rateValue) + "%";
                } else if ("持仓浮盈亏".equals(chartTypeEnum.getDescription())) {
                    // 追加显示持仓浮盈亏比率字段
                    BigDecimal rateValue = jl.getBigDecimal("balanceFloatIncomeRate");
                    fmtValue = CommonUtil.formatBigDecimalQfw(chartValue);
                    fmtRateValue = CommonUtil.formatBigDecimalYieldRate(rateValue) + "%";
                } else if ("最新收益".equals(chartTypeEnum.getDescription())) {
                    // 追加显示最新收益率字段
                    BigDecimal rateValue = jl.getBigDecimal("dayAssetRate");
                    fmtValue = CommonUtil.formatBigDecimalQfw(chartValue);
                    fmtRateValue = CommonUtil.formatBigDecimalYieldRate(rateValue) + "%";
                } else {
                    fmtValue = CommonUtil.formatBigDecimalQfw(chartValue);
                }
            }
        }

        chartData.setChartValueStr(fmtValue);
        chartData.setRateValueStr(fmtRateValue);
        return chartData;
    }

    /**
     * 查询高端分类合计日序数据
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/highClassfyAsset.do")
    public SequentialVO highClassfyAsset(HttpServletRequest request) {
        String chartType = request.getParameter("chartType");
        String fundCode = request.getParameter("fundCode");
        String conscustno = request.getParameter("conscustno");
        String startDay = request.getParameter("startDay");
        String endDay = request.getParameter("endDay");
        String currencyRate = request.getParameter("currencyRate");

        String res = highDealOrderSupport.highClassfyAsset(fundCode, conscustno, startDay, endDay);

        SequentialResponse sequentialResponse = JSON.parseObject(res, SequentialResponse.class);
        return getSequentialVoByResponse(sequentialResponse, chartType, true, false, currencyRate);
    }

    /**
     * 查询高端用户合计日序数据
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/highUserAsset.do")
    public SequentialVO highUserAsset(HttpServletRequest request) {
        String chartType = request.getParameter("chartType");
        String fundCode = request.getParameter("fundCode");
        String conscustno = request.getParameter("conscustno");
        String startDay = request.getParameter("startDay");
        String endDay = request.getParameter("endDay");
        String currencyRate = request.getParameter("currencyRate");

        String res = highDealOrderSupport.highUserAsset(fundCode, conscustno, startDay, endDay);

        SequentialResponse sequentialResponse = JSON.parseObject(res, SequentialResponse.class);
        return getSequentialVoByResponse(sequentialResponse, chartType, true, false, currencyRate);
    }

    @RequestMapping("/listPriFundDetail.do")
    public String listPriFundDetail(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws UnsupportedEncodingException {
        String pCode = request.getParameter("pCode");
        String type = request.getParameter("type");
        request.setAttribute("pCode", pCode);
        request.setAttribute("type", type);
        List<Jjjlinfo> jjjlList = new ArrayList<>();
        String res = null;
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("fundCode", pCode);
        try {
            long startTime = System.currentTimeMillis();
            res = HttpUtils.get("http://data.howbuy.com/cgi/simu/v634/simuproductmanager.json", paramMap);
            long endTime = System.currentTimeMillis();
            MainLogUtils.httpCallOut("http://data.howbuy.com/cgi/simu/v634/simuproductmanager.json", CommonConstant.HTTP_SUCCESS_CODE, endTime - startTime);
            log.info("http://data.howbuy.com/cgi/simu/v634/simuproductmanager.json传参" + JSON.toJSON(paramMap) + "回参：" + res);
        } catch (IOException e) {
            e.printStackTrace();
            log.error("http://data.howbuy.com/cgi/simu/v634/simuproductmanager.json异常！", e);
        }
        if (StringUtils.isNotBlank(res)) {
            JSONObject jsonObject = JSON.parseObject(res);
            if ("0000".equals(jsonObject.getString("code"))) {
                JSONObject bodyjson = jsonObject.getJSONObject("body");
                JSONArray jsonArray = (JSONArray) bodyjson.get("managerList");
                if (jsonArray != null) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jl = jsonArray.getJSONObject(i);
                        Jjjlinfo jjjl = new Jjjlinfo();
                        jjjl.setZhpf(jl.getString("zhpf"));
                        jjjl.setBjjrq(jl.getString("bjjrq"));
                        jjjl.setRqhb(jl.getString("rqhb"));
                        String rydm = jsonArray.getJSONObject(i).getString("rydm");
                        if (StringUtils.isNotBlank(rydm)) {
                            Map<String, String> paramRydmMap = new HashMap<String, String>();
                            paramRydmMap.put("rydm", rydm);
                            try {
                                long startTime = System.currentTimeMillis();
                                String resry = HttpUtils.get("http://data.howbuy.com/cgi/simu/v736/manager.json", paramRydmMap);
                                long endTime = System.currentTimeMillis();
                                MainLogUtils.httpCallOut("http://data.howbuy.com/cgi/simu/v736/manager.json", CommonConstant.HTTP_SUCCESS_CODE, endTime - startTime);
                                log.info("http://data.howbuy.com/cgi/simu/v736/manager.json传参" + JSON.toJSON(paramRydmMap) + "回参：" + resry);
                                if (StringUtils.isNotBlank(resry)) {
                                    JSONObject jsonRyObject = JSON.parseObject(resry);
                                    if ("0000".equals(jsonRyObject.getString("code"))) {
                                        JSONObject rybodyjson = jsonRyObject.getJSONObject("body");
                                        jjjl.setManagerName(rybodyjson.getString("managerName"));
                                        jjjl.setSummary(rybodyjson.getString("summary"));
                                        jjjl.setCompanyName(rybodyjson.getString("companyName"));
                                        jjjl.setJjjlly(rybodyjson.getString("jjjlly"));
                                        jjjl.setSclx(rybodyjson.getString("sclx"));
                                        jjjl.setCysj(rybodyjson.getString("cysj"));
                                        jjjl.setRsmnx(rybodyjson.getString("rsmnx"));
                                        jjjl.setGljjsl(rybodyjson.getString("gljjsl"));
                                        jjjl.setCxjjsl(rybodyjson.getString("cxjjsl"));
                                        jjjl.setCypjhb(rybodyjson.getString("cypjhb"));
                                        jjjl.setHbjn(rybodyjson.getString("hbjn"));
                                    }
                                }
                            } catch (IOException e) {
                                e.printStackTrace();
                                log.error("http://data.howbuy.com/cgi/simu/v736/manager.json异常！", e);
                            }
                        }
                        jjjlList.add(jjjl);
                    }
                }
            }
        }
        request.setAttribute("jjjlList", jjjlList);
        return "/custinfo/priFundDetail";
    }

    /**
     * 加载私募基金详情数据方法
     *
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping("/listPriFundDetail_json.do")
    public Map<String, Object> listPriFundDetail_json(HttpServletRequest request) throws Exception {
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>(1);
        String pCode = request.getParameter("pCode");
        // 如果查询条件（产品编号）不为空，则增加产品编号查询参数
        if (StringUtil.isNotNullStr(pCode)) {
            param.put("pcode", pCode);
        } else {
            param.put("pcode", null);
        }

        Map<String, Object> resultMap = new HashMap<String, Object>(1);
        Productinfo productinfo = productinfoService.getProductinfoDetail(param);
        resultMap.put("productinfo", productinfo);
        //查基金公司相关信息
        Jjgsinfo jjgs = new Jjgsinfo();
        if (StringUtils.isNotBlank(productinfo.getGlrm())) {
            String res = null;
            Map<String, String> paramMap = new HashMap<String, String>();
            paramMap.put("gsdm", productinfo.getGlrm());
            try {
                long startTime = System.currentTimeMillis();
                res = HttpUtils.get("http://data.howbuy.com/cgi/simu/v736/company.json", paramMap);
                long endTime = System.currentTimeMillis();
                MainLogUtils.httpCallOut("http://data.howbuy.com/cgi/simu/v736/company.json", CommonConstant.HTTP_SUCCESS_CODE, endTime - startTime);
                log.info("http://data.howbuy.com/cgi/simu/v736/company.json传参" + JSON.toJSON(paramMap) + "回参：" + res);
            } catch (IOException e) {
                e.printStackTrace();
                log.error("http://data.howbuy.com/cgi/simu/v736/company.json异常！", e);
            }
            if (StringUtils.isNotBlank(res)) {
                JSONObject jsonObject = JSON.parseObject(res);
                if ("0000".equals(jsonObject.getString("code"))) {
                    JSONObject bodyjson = jsonObject.getJSONObject("body");
                    jjgs.setGsqc(bodyjson.getString("gsqc"));
                    jjgs.setGsjc(bodyjson.getString("gsjc"));
                    jjgs.setSummary(bodyjson.getString("summary"));
                    JSONObject jbxxjson = bodyjson.getJSONObject("jbxx");
                    jjgs.setClrq(jbxxjson.getString("clrq"));
                    jjgs.setFrdb(jbxxjson.getString("frdb"));
                    jjgs.setZczb(jbxxjson.getString("zczb"));
                    jjgs.setQxjl(jbxxjson.getString("qxjl"));
                    jjgs.setGljj(jbxxjson.getString("gljj"));
                    jjgs.setCxjj(jbxxjson.getString("cxjj"));
                    jjgs.setBahm(jbxxjson.getString("bahm"));
                    jjgs.setGlgm(jbxxjson.getString("glgm"));
                }
            }
        }
        resultMap.put("jjgs", jjgs);

        return resultMap;
    }

    /**
     * 客户交易详细
     *
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
    @Deprecated
    @RequestMapping("/detailTradeNew.do")
    public String detailTradeNew(HttpServletRequest request) throws UnsupportedEncodingException {

        String custno = request.getParameter("consCustNo");
        String bankName = java.net.URLDecoder.decode(request.getParameter("bankName"), "UTF-8");
        String bankAcct = java.net.URLDecoder.decode(request.getParameter("bankAcct"), "UTF-8");
        String mBusiCode = java.net.URLDecoder.decode(request.getParameter("mBusiCode"), "UTF-8");
        String ackVol = request.getParameter("ackVol");
        String ackAmt = request.getParameter("ackAmt");
        String taTradeDt = request.getParameter("taTradeDt");
        String productAttr = java.net.URLDecoder.decode(request.getParameter("productAttr"), "UTF-8");
        String nav = request.getParameter("nav");
        String fee = request.getParameter("fee");
        String clrq = request.getParameter("clrq");
        String busiTypeName = java.net.URLDecoder.decode(request.getParameter("busiTypeName"), "UTF-8");
        Map<String, String> param = new HashMap<>(1);
        Map<String, Object> resultMap = new HashMap<>(16);
        resultMap.put("bankName", bankName);
        resultMap.put("bankAcct", bankAcct);
        resultMap.put("mBusiCode", mBusiCode);

        if (StringUtil.isNotNullStr(ackVol)) {
            resultMap.put("ackVol", new BigDecimal(ackVol));
        }
        if (StringUtil.isNotNullStr(ackAmt)) {
            resultMap.put("ackAmt", new BigDecimal(ackAmt));
        }
        if (StringUtil.isNotNullStr(nav)) {
            BigDecimal tradenav = new BigDecimal(nav);
            resultMap.put("nav", tradenav);
        }
        if (StringUtil.isNotNullStr(fee)) {
            BigDecimal tradefee = new BigDecimal(fee);
            resultMap.put("fee", tradefee);
        }
        resultMap.put("taTradeDt", taTradeDt);
        resultMap.put("productAttr", productAttr);
        resultMap.put("clrq", clrq);
        resultMap.put("busiTypeName", busiTypeName);
        param.put("conscustno", custno);
        Conscust conscust = conscustService.getConscust(custno);
        if (conscust != null) {
            conscust.setIdtype(ConstantCache.getInstance().getVal("idtype", conscust.getIdtype()));
            if (StringUtil.isNotNullStr(conscust.getIdnoCipher())) {
                conscust.setIdno(decryptSingleFacade.decrypt(conscust.getIdnoCipher()).getCodecText());
            } else {
                conscust.setIdno("");
            }
            conscust.setGender(ConstantCache.getInstance().getVal("sex", conscust.getGender()));
            conscust.setProvcode(ConstantCache.getInstance().getProvCityMap().get(conscust.getProvcode()));
            conscust.setCitycode(ConstantCache.getInstance().getProvCityMap().get(conscust.getCitycode()));
            param.put("tradedt", taTradeDt);
            Map<String, String> conmgrmap = conscustService.getConsInfoByCustNoAndTddt(param);
            if (conmgrmap != null) {
                conscust.setConscode(Util.ObjectToString(conmgrmap.get("CONSCODE")));
                resultMap.put("seniormgrcode", Util.ObjectToString(conmgrmap.get("SENIORMGRCODE")));
                resultMap.put("consname", ConsOrgCache.getInstance().getAllConsMap().get(Util.ObjectToString(conmgrmap.get("CONSCODE"))));
            }
            if (StringUtils.isNotBlank(conscust.getNewsourceno())) {
                resultMap.put("source", conscust.getNewsourcename());
                resultMap.put("subsource", conscust.getNewsubsourcename());
                resultMap.put("subsourcetype", conscust.getNewsubsourcetypename());
            } else {
                Map<String, String> sourceNameMap = CmCustSourceCache.getInstance().getSourceNameMap();
                resultMap.put("source", sourceNameMap.get(conscust.getSource()));
                resultMap.put("subsource", sourceNameMap.get(conscust.getSubsource()));
                resultMap.put("subsourcetype", sourceNameMap.get(conscust.getSubsourcetype()));
            }
            conscust.setMobile(conscust.getMobileMask());
            if (StringUtil.isNotNullStr(conscust.getIdnoCipher())) {
                conscust.setIdno(decryptSingleFacade.decrypt(conscust.getIdnoCipher()).getCodecText());
            }
            resultMap.put("conscust", conscust);
        }
        request.setAttribute("conscustno", request.getParameter("consCustNo"));
        request.setAttribute("map", resultMap);
        return "/custinfo/detailFundTradeNew";
    }

    @RequestMapping("/bxBalanceDetail")
    @ResponseBody
    public Object bxBalanceDetail(String conscustno) {
        List<BxBalanceDetail> bxBalanceDetails = cmBxPrebookBuyinfoService.listBxBalance(conscustno);
        ConstantCache constantCache = ConstantCache.getInstance();
        for (BxBalanceDetail bxBalanceDetail : bxBalanceDetails) {
            bxBalanceDetail.setCurrency(constantCache.getVal("currencys", bxBalanceDetail.getCurrency()));
            bxBalanceDetail.setProdtype(constantCache.getVal("insurprodtype", bxBalanceDetail.getProdtype()));
            bxBalanceDetail.setExpirestat(constantCache.getVal("insurexpirestat", bxBalanceDetail.getExpirestat()));
        }
        return bxBalanceDetails;
    }

    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    @Data
    public class CRMBalanceBean {
        private String disCode;
        private List<String> disCodeList;
        private String productCode;
        private String subProductCode;
        private String productName;
        private String productType;
        private String productSubType;
        private BigDecimal balanceVol;
        private BigDecimal unconfirmedVol;
        private BigDecimal unconfirmedAmt;
        private String currency;
        private BigDecimal nav;
        private String navDt;
        private String navDivFlag = "0";
        private BigDecimal marketValue;
        private BigDecimal currencyMarketValue;
        private String scaleType;
        private String hkSaleFlag;
        private String StageEstablishFlag;
        private String fractionateCallFlag;
        private String fundCXQXStr;
        private BigDecimal netBuyAmount;
        private BigDecimal currencyNetBuyAmount;
        private BigDecimal cashCollection;
        private BigDecimal currencyCashCollection;
        private BigDecimal paidInAmt;
        private String incomeDt;
        private String incomeCalStat;
        private BigDecimal currentAsset;
        private BigDecimal currentAssetCurrency;
        private BigDecimal dailyAsset;
        private BigDecimal dailyAssetCurrency;
        private BigDecimal accumIncome;
        private BigDecimal accumIncomeRmb;
        private BigDecimal accumRealizedIncome;
        private BigDecimal accumRealizedIncomeRmb;
        private BigDecimal balanceCost;
        private BigDecimal balanceCostCurrency;
        private String rePurchaseFlag;
        private String benchmark;
        private String benchmarkType;
        private String valueDate;
        private String dueDate;
        private String standardFixedIncomeFlag;
        private String investmentHorizon;
        private String cooperation;
        private String crisisFlag;
        private BigDecimal yieldIncome;
        private BigDecimal yieldRate;
        private String yieldIncomeDt;
        private String hwSaleFlag;
        private String regDt;
        private String oneStepType;
        private String twoStepType;
        private String secondStepType;
        private String productSaleType;
        private String naProductFeeType;
        private BigDecimal receivManageFee;
        private BigDecimal receivPreformFee;
        private BigDecimal currencyMarketValueExFee;
        private BigDecimal marketValueExFee;
        private BigDecimal balanceIncomeNew;
        private BigDecimal balanceIncomeNewRmb;
        private BigDecimal accumIncomeNew;
        private BigDecimal accumIncomeNewRmb;
        private BigDecimal balanceFactor;
        private String convertFinish;
        private String balanceFactorDate;

        /**************持仓2.0 crm页面新增字段start****************/
        // 累计收益率
        private BigDecimal accumYieldRate;
        // 累计成本
        private BigDecimal accumCost;
        // 累计成本(人民币)
        private BigDecimal accumCostRmb;
        // 持仓浮盈亏
        private BigDecimal balanceFloatIncome;
        // 持仓浮盈亏(人民币)
        private BigDecimal balanceFloatIncomeRmb;
        // 持仓浮盈亏比率
        private BigDecimal balanceFloatIncomeRate;
        // 最新收益率
        private BigDecimal dayAssetRate;
        // 最新资产增长率
        private BigDecimal dayIncomeGrowthRate;
        // 持仓总回款
        private BigDecimal totalCashCollection;
        // 投资成本
        private BigDecimal accumCostNew;
        // 投资成本(人民币)
        private BigDecimal accumCostRmbNew;
        // 持仓市值
        private BigDecimal balanceAmt;
        // 持仓市值(人民币)
        private BigDecimal balanceAmtRmb;
        // 累计总回款
        private BigDecimal accumCollection;
        // 累计总回款(人民币)
        private BigDecimal accumCollectionRmb;
        //NA费后参考市值(人民币)
        private BigDecimal balanceAmtExFeeRmb;
        //NA费后参考市值(人民币)
        private BigDecimal balanceAmtExFee;
        private String cpqxsm;
        private String sxz;
        //持仓收益(当前币种）
        private BigDecimal currentIncome;
        //持仓收益(人民币）
        private BigDecimal currentIncomeRmb;
        //当前累计收益(当前币种）
        private BigDecimal currentAccumIncome;
        //当前累计收益(人民币）
        private BigDecimal currentAccumIncomeRmb;
        /**************持仓2.0 crm页面新增字段end****************/
        // 分红方式. 0-红利再投，1-现金红利，9-不分红
        private String divMode;

        /**
         * 待投金额（当前币种）
         */
        private BigDecimal currencyUnPaidInAmt;
        /**
         * 待投金额（人民币）
         */
        private BigDecimal unPaidInAmt;
        /**
         * 4.是否为千禧年产品 0-否、1-是qianXiFlag
         */
        private String qianXiFlag;
        /****************持仓2.2 新增**********************/
        /**
         * 收益计算日期
         */
        private String assetUpdateDate;
        /**
         * 单位持仓成本去费
         */
        private BigDecimal unitBalanceCostExFee;
        /**
         * 单位持仓成本去费(人民币)
         */
        private BigDecimal unitBalanceCostExFeeRmb;
        /**
         * 净值披露方式(1-净值,2-份额收益)
         */
        private String navDisclosureType;

        /**
         * 最新收益日期
         */
        private String latestIncomeDt;
        /****************持仓2.2 end**********************/
    }

    private List<QueryDealOrderListResponse.DealOrderBean> queryDealOrderList(HttpServletRequest request, CrmCustInvestTypeEnum investTypeEnum, String hboneno) {
        QueryDealOrderListRequest req = new QueryDealOrderListRequest();
        req.setOutletCode("A20150120");
        req.setOperIp(getIpAddr(request));
        req.setTxChannel("1");
        req.setDataTrack("crmsale");
        req.setPageSize(500);
        if (StringUtil.isNullStr(hboneno)) {
            return new ArrayList();
        }
        req.setHbOneNo(hboneno);
        List<String> fullDisCodeList = DisCodeUtil.getFullBusiDisCodeList(investTypeEnum).stream().map(DisCodeEnum::getCode).collect(Collectors.toList());
        req.setDisCodeList(fullDisCodeList);
        log.info("queryDealOrderListFacade.execute入参" + JSON.toJSON(req));
        List<QueryDealOrderListResponse.DealOrderBean> dealOrderlist = queryDealOrderListFacade.execute(req).getDealOrderList();
        log.info("queryDealOrderListFacade.execute返回" + JSON.toJSON(dealOrderlist));
        return dealOrderlist;
    }

    private QueryDealOrderResponse queryDealOrderFacade(HttpServletRequest request, String custno, String hboneno, String dealNo) {
        QueryDealOrderRequest req = new QueryDealOrderRequest();
        req.setDealNo(dealNo);
        req.setOutletCode("A20150120");
        req.setOperIp(getIpAddr(request));
        req.setTxChannel("1");
        req.setDataTrack("crmsale");
        req.setPageSize(500);
        if (StringUtil.isNullStr(hboneno)) {
            return null;
        }
        req.setHbOneNo(hboneno);
        req.setDisCode(conscustService.getDiscodeByConsCustno(custno));
        log.info("queryDealOrderFacade.execute入参" + JSON.toJSON(req));
        QueryDealOrderResponse response = queryDealOrderFacade.execute(req);
        log.info("queryDealOrderFacade.execute返回" + JSON.toJSON(response));
        return response;
    }

    private QueryCustRepurchaseProtocolResposne queryCustRepurchaseProtocol(String pubcustno, int pageNo, int pageSize) {
        // 调用中台接口
        QueryCustRepurchaseProtocolRequest dubboReq = new QueryCustRepurchaseProtocolRequest();
        dubboReq.setTxAcctNo(pubcustno);
        dubboReq.setDisCode(conscustService.getDiscodeByPubCustno(pubcustno));
        dubboReq.setOutletCode("A20140301");
        dubboReq.setOperIp("**************");
        dubboReq.setTxChannel("5");
        dubboReq.setPageNo(pageNo);
        dubboReq.setPageSize(pageSize);

        // 打印请求数据
        log.info(dubboReq.getClass().getSimpleName() + "|" + JSON.toJSON(dubboReq));
        QueryCustRepurchaseProtocolResposne dubboRsp = queryCustRepurchaseProtocolFacade.execute(dubboReq);
        // 打印响应数据
        log.info(dubboRsp.getClass().getSimpleName() + "|" + JSON.toJSON(dubboRsp));
        return dubboRsp;
    }


    /**
     * 客户复购意向本地实体类
     *
     * <AUTHOR>
     */
    class CustRepurchaseProtocolBeanLocal implements Serializable {
        private static final long serialVersionUID = 24847912626699338L;

        private String txAcctNo;

        private String repurchaseType;

        private String fundCode;

        private String fundName;

        private String repurchaseProtocolNo;

        private BigDecimal repurchaseVol;

        private String endModifyDt;

        private String canModify;

        private String productRepurchaseFlag;

        private BigDecimal balanceVol;

        private Date updateDtm;

        public String getTxAcctNo() {
            return txAcctNo;
        }

        public void setTxAcctNo(String txAcctNo) {
            this.txAcctNo = txAcctNo;
        }

        public String getRepurchaseType() {
            return repurchaseType;
        }

        public void setRepurchaseType(String repurchaseType) {
            this.repurchaseType = repurchaseType;
        }

        public String getFundCode() {
            return fundCode;
        }

        public void setFundCode(String fundCode) {
            this.fundCode = fundCode;
        }

        public String getFundName() {
            return fundName;
        }

        public void setFundName(String fundName) {
            this.fundName = fundName;
        }

        public String getRepurchaseProtocolNo() {
            return repurchaseProtocolNo;
        }

        public void setRepurchaseProtocolNo(String repurchaseProtocolNo) {
            this.repurchaseProtocolNo = repurchaseProtocolNo;
        }

        public BigDecimal getRepurchaseVol() {
            return repurchaseVol;
        }

        public void setRepurchaseVol(BigDecimal repurchaseVol) {
            this.repurchaseVol = repurchaseVol;
        }

        public String getEndModifyDt() {
            return endModifyDt;
        }

        public void setEndModifyDt(String endModifyDt) {
            this.endModifyDt = endModifyDt;
        }

        public String getCanModify() {
            return canModify;
        }

        public void setCanModify(String canModify) {
            this.canModify = canModify;
        }

        public String getProductRepurchaseFlag() {
            return productRepurchaseFlag;
        }

        public void setProductRepurchaseFlag(String productRepurchaseFlag) {
            this.productRepurchaseFlag = productRepurchaseFlag;
        }

        public BigDecimal getBalanceVol() {
            return balanceVol;
        }

        public void setBalanceVol(BigDecimal balanceVol) {
            this.balanceVol = balanceVol;
        }

        public Date getUpdateDtm() {
            return updateDtm;
        }

        public void setUpdateDtm(Date updateDtm) {
            this.updateDtm = updateDtm;
        }
    }

    /**
     * 直销高端产品持仓:
     *
     * <AUTHOR>
     * @date 2022/4/26
     */
    @ResponseBody
    @RequestMapping("/queryZxAcctBalanceList.do")
    public Map<String, Object> queryZxAcctBalanceList(String conscustno, HttpServletRequest request) throws Exception {
        ZxPrivateTradeFundVo vo = new ZxPrivateTradeFundVo();
        vo.setConscustno(conscustno);
        PageData<ZxPrivateTradeFund> pageData = zxPrivateTradeFundService.listZxPrivateTradeFundByPage(vo);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<ZxPrivateTradeFund> listdata = pageData.getListData();
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        for (ZxPrivateTradeFund info : listdata) {
            JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
            info.setConsname(consOrgCache.getAllUserMap().get(info.getConscode()));
            if (jjxx1 != null) {
                info.setFundname(jjxx1.getJjjc());
                if (StringUtil.isNotNullStr(jjxx1.getHbzl())) {
                    info.setCurrency(ConstantCache.getInstance().getVal("currencys", jjxx1.getHbzl()));
                }
            }
            if (info.getBalancevol() != null && info.getNav() != null) {
                info.setBalanceamt(info.getBalancevol().multiply(info.getNav()));
                if (info.getBalancefactor() != null) {
                    info.setBalanceamt(info.getBalanceamt().add(info.getBalancefactor()));
                }
            }
        }
        resultMap.put("rows", listdata);
        return resultMap;
    }


    @RequestMapping(value="/updateZxAcctBalanceView.do")
    public String updateZxAcctBalanceView(HttpServletRequest request) throws Exception{
        ZxPrivateTradeFundVo vo = new ZxPrivateTradeFundVo();
        String custno = request.getParameter("custno");
        String fundcode = request.getParameter("fundcode");
        vo.setConscustno(custno);
        vo.setFundcode(fundcode);

        PageData<ZxPrivateTradeFund> pageData = zxPrivateTradeFundService.listZxPrivateTradeFundByPage(vo);
        ZxPrivateTradeFund zxPrivateTradeFund = pageData.getListData().get(0);
        request.setAttribute("zxPrivateTradeFund",zxPrivateTradeFund);
        return "/custinfo/updateZxAcctBalance";
    }

    @ResponseBody
    @RequestMapping("/updateZxAcctBalance.do")
    public String updateZxAcctBalance(HttpServletRequest request, HttpServletResponse response) throws Exception {
        User user = (User)request.getSession().getAttribute("loginUser");

        ZxPrivateTradeFundVo vo = new ZxPrivateTradeFundVo();
        vo.setFundcode(request.getParameter("fundcode"));
        vo.setConscustno(request.getParameter("custno"));
        vo.setBalancevol(new BigDecimal(request.getParameter("balancevol")));
        vo.setModifier(user.getUserId());
        vo.setModdt(DateUtil.getDateFormat(new Date(),"yyyyMMdd"));
        String result = "";
        try {
            zxPrivateTradeFundService.updateZxPrivateTradeFund(vo);
            result = "success";
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return result;
    }

    /**
     * 直销高端产品交易:
     *
     * <AUTHOR>
     * @date 2022/4/26
     */
    @ResponseBody
    @RequestMapping("/queryZxAcctBalanceTradeList.do")
    public Map<String, Object> queryZxAcctBalanceTradeList(HttpServletRequest request) throws Exception {
        ZxPrivateTradeFundVo vo = new ZxPrivateTradeFundVo();
        String conscustno = request.getParameter("conscustno");
        String fundcode = request.getParameter("fundcode");
        String busicode = request.getParameter("busicode");
        if (StringUtil.isNotNullStr(conscustno)) {
            vo.setConscustno(conscustno);
        }
        if (StringUtil.isNotNullStr(fundcode)) {
            vo.setFundcode(fundcode);
        }
        if (StringUtil.isNotNullStr(busicode)) {
            vo.setBusicode(busicode);
        }
        PageData<Custprivatefundtrade> pageData = custprivatefundtradeService.listZxPrivateTradeByPage(vo);
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<Custprivatefundtrade> listdata = pageData.getListData();
        for (Custprivatefundtrade info : listdata) {
            JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
            if (jjxx1 != null) {
                info.setFundName(jjxx1.getJjjc());
            }
            info.setBusivar(ConstantCache.getInstance().getVal("zxBusiCode", info.getBusicode()));

            // 根据基金代码，调用私募接口拼接字段：母基金代码、是否分期成立
            String[] fundcodes = new String[]{info.getFundcode()};
            List<SmcccpJbxxDto> listDto = smccProductJbxxService.getSmccFromCacheByCodes(fundcodes);
            if (CollectionUtils.isNotEmpty(listDto)) {
                String mjjdm = listDto.get(0).getMjjdm();
                String sffqcl = listDto.get(0).getSffqcl();
                info.setMjjdm(mjjdm);
                info.setSffqcl(sffqcl);
            }
        }
        resultMap.put("rows", listdata);
        return resultMap;
    }


}
