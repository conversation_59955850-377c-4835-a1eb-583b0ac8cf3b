/**   
 * @Title: PageResult.java 
 * @Package com.hb.crm.web.util.excel.util 
 * @Description: TODO(用一句话描述该文件做什么) 
 * <AUTHOR>
 * @date 2016年4月29日 下午4:22:13 
 * @version V1.0   
 */
package com.howbuy.crm.hb.tools.excel.util;

import java.util.List;

/**
 * @ClassName: PageResult
 * @Description: 页面返回结果类
 * <AUTHOR>
 * @date 2016年4月29日 下午4:22:13
 * 
 */
public class PageResult {

	/**
	 * @Fields status : 请求是否成功
	 */
	private boolean status;
	/**
	 * @Fields message : 提示信息
	 */
	private String message;

	/**
	 * @Fields total : 分页时的记录总数
	 */
	private Integer total;

	/**
	 * @Fields rows : 分页时的记录
	 */
	private List<?> rows;

	/**
	 * @Title: getTotal <BR>
	 * @Description: please write your description <BR>
	 * @return: Integer <BR>
	 */

	public Integer getTotal() {
		return total;
	}

	/**
	 * @Title: setTotal <BR>
	 * @Description: please write your description <BR>
	 * @return: Integer <BR>
	 */

	public void setTotal(Integer total) {
		this.total = total;
	}

	/**
	 * @Title: getRows <BR>
	 * @Description: please write your description <BR>
	 * @return: List<?> <BR>
	 */

	public List<?> getRows() {
		return rows;
	}

	/**
	 * @Title: setRows <BR>
	 * @Description: please write your description <BR>
	 * @return: List<?> <BR>
	 */

	public void setRows(List<?> rows) {
		this.rows = rows;
	}

	/**
	 * @Title: isStatus <BR>
	 * @Description: please write your description <BR>
	 * @return: boolean <BR>
	 */

	public boolean isStatus() {
		return status;
	}

	/**
	 * @Title: setStatus <BR>
	 * @Description: please write your description <BR>
	 * @return: boolean <BR>
	 */

	public void setStatus(boolean status) {
		this.status = status;
	}

	/**
	 * @Title: getMessage <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public String getMessage() {
		return message;
	}

	/**
	 * @Title: setMessage <BR>
	 * @Description: please write your description <BR>
	 * @return: String <BR>
	 */

	public void setMessage(String message) {
		this.message = message;
	}

}
