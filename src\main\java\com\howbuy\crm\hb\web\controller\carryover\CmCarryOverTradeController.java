//package com.howbuy.crm.hb.web.controller.carryover;
//
//import com.alibaba.dubbo.common.utils.CollectionUtils;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.howbuy.crm.hb.domain.carryover.CmCarryOverTradeInfo;
//import com.howbuy.crm.hb.domain.custinfo.Conscust;
//import com.howbuy.crm.hb.service.carryover.CmCarryOverTradeService;
//import com.howbuy.crm.hb.service.common.CommonService;
//import com.howbuy.crm.hb.service.custinfo.ConscustService;
//import com.howbuy.crm.hb.service.prosale.CustprivatefundService;
//import com.howbuy.crm.hb.service.prosale.CustprivatefundtradeService;
//import com.howbuy.crm.jjxx.dto.JjxxInfo;
//import com.howbuy.crm.jjxx.service.JjxxInfoService;
//import com.howbuy.crm.page.cache.ConstantCache;
//import com.howbuy.crm.page.framework.domain.User;
//import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
//import com.howbuy.crm.page.framework.utils.ExcelUtils;
//import com.howbuy.crm.page.framework.utils.ParamUtil;
//import com.howbuy.crm.prosale.dto.Custprivatefund;
//import com.howbuy.crm.prosale.dto.Custprivatefundtrade;
//import com.howbuy.simu.dto.business.product.SmjzAndHbDto;
//import com.howbuy.simu.service.business.product.SmjzAndHbService;
//import crm.howbuy.base.db.PageData;
//import crm.howbuy.base.utils.DateTimeUtil;
//import crm.howbuy.base.utils.StringUtil;
//import jxl.Sheet;
//import jxl.Workbook;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestMethod;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.multipart.MultipartFile;
//import org.springframework.web.multipart.MultipartHttpServletRequest;
//import org.springframework.web.servlet.ModelAndView;
//
//import javax.servlet.ServletOutputStream;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//import java.io.InputStream;
//import java.math.BigDecimal;
//import java.util.*;
//
// NA产品转份额管理【所有功能标记删除】
//
///**
// * @Author: yu.zhang
// * @Date: create on 2020-09-18 13:48
// * @Description:
// */
//@Controller
//@Slf4j
//@RequestMapping(value = "/carryovertrade")
//@Deprecated
//public class CmCarryOverTradeController {
//
//
//    private static final String RETURN_RESULT = "success";
//
//    @Autowired
//    private CmCarryOverTradeService cmCarryOverTradeService;
//
//    @Autowired
//    private CustprivatefundService custprivatefundService;
//
//    @Autowired
//    private CustprivatefundtradeService custprivatefundtradeService;
//
//    @Autowired
//    private SmjzAndHbService smjzAndHbService;
//
//    @Autowired
//    private CommonService commonService;
//
//    @Autowired
//    private ConscustService conscustService;
//
//    @Autowired
//    private JjxxInfoService jjxxInfoService;
//
//    @RequestMapping(value="/carryovertradelist.do")
//    public String conferenceList(){
//        return "/carryover/carryovertradelist";
//    }
//
//    /**
//	 * 加载列表页面数据
//	 * @param request
//	 * @param
//	 * @return Map<String, Object>
//	 * @throws Exception
//	 */
//	@ResponseBody
//	@RequestMapping("/listCarryovertradeByPage.do")
//	public Map<String, Object> listCarryovertradeByPage(HttpServletRequest request)	throws Exception {
//		// 获取查询参数
//		Map<String, String> param = new ParamUtil(request).getParamMap();
//		param.put("fundcode",request.getParameter("fundcode"));
//		param.put("status",request.getParameter("status"));
//		param.put("custname",request.getParameter("custname"));
//
//
//		PageData<CmCarryOverTradeInfo> pageData = cmCarryOverTradeService.listCmCarryOverTradeInfoByPage(param);
//
//		ConstantCache constantCache= ConstantCache.getInstance();
//		for (CmCarryOverTradeInfo info : pageData.getListData()) {
//            JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
//			info.setFundname(jjxxInfo == null ? null : jjxxInfo.getJjjc());
//			info.setStatusStr(constantCache.getVal("carryoverstatus", info.getStatus()));
//		}
//
//		// 返回查询结果
//		Map<String, Object> resultMap = new HashMap<String, Object>(16);
//		resultMap.put("total", pageData.getPageBean().getTotalNum());
//		List<CmCarryOverTradeInfo> listdata = pageData.getListData();
//		resultMap.put("rows", listdata);
//
//		return resultMap;
//
//	}
//
//
//    /**
//     * dealCarryOverTrade
//     * @param request
//     * @return java.util.Map<java.lang.String,java.lang.Object>
//     * @Author: yu.zhang on 2020/9/24 10:02
//     */
//    @ResponseBody
//    @RequestMapping("/dealCarryOverTrade")
//    public Map<String, Object> dealCarryOverTrade(HttpServletRequest request) throws Exception {
//
//        Map<String, Object> param = new HashMap<String, Object>(16);
//        Map<String, Object> resultMap = new HashMap<>(16);
//
//        User user = (User) request.getSession().getAttribute("loginUser");
//        //处理是批量处理
//        String[] carryid = request.getParameterValues("carryid[]");
//        if(carryid != null && carryid.length > 0){
//            List<String> carryidList = new ArrayList<String>();
//            Collections.addAll(carryidList, carryid);
//            param.put("carryidList",carryidList);
//
//            //校验
//            List<CmCarryOverTradeInfo> carryoverlist = cmCarryOverTradeService.queryCarryOverTradeList(param);
//
//            boolean checkstatus = false;
//            boolean checkfund = false;
//            String hostfundcode = "";
//
//            BigDecimal jjjz = null;
//            if(carryoverlist != null && carryoverlist.size() > 0){
//
//                String comparefundcode = carryoverlist.get(0).getFundcode();
//                String comparetradedt = carryoverlist.get(0).getTradedt();
//
//                for(CmCarryOverTradeInfo carryovertrade:carryoverlist){
//                    if("2".equals(carryovertrade.getStatus())){
//                        checkstatus = true;
//                        break;
//                    }
//
//                    if(!comparefundcode.equals(carryovertrade.getFundcode()) || !comparetradedt.equals(carryovertrade.getTradedt())){
//                        checkfund = true;
//                        break;
//                    }
//                }
//
//                //如选择的数据存在已处理状态，则提示：“只能选择待处理的数据，请重新选择”
//                if(checkstatus){
//                    resultMap.put("code","error");
//                    resultMap.put("msg","只能选择待处理的数据，请重新选择");
//                    return resultMap;
//                }
//
//                //如选择的数据不是同一个产品，则提示：“只能选择同一个产品进行处理，请重新选择”
//                if(checkfund){
//                    resultMap.put("code","error");
//                    resultMap.put("msg","只能选择同一日期同一个产品进行处理，请重新选择");
//                    return resultMap;
//                }
//
//                //校验客户强赎子份额是否大于持仓份额，如大于，则提示：“客户【XXX】【xxx】份额不足，请重新选择”
//                List<String> custnames = cmCarryOverTradeService.querySubshareCustList(param);
//                if(custnames != null && custnames.size() > 0){
//                	StringBuilder sb = new StringBuilder();
//                	sb.append("客户");
//                    for(String custname:custnames){
//                    	sb.append("【"+custname+"】");
//                    }
//                    sb.append("份额不足，请重新选择");
//                    resultMap.put("code","error");
//                    resultMap.put("msg",sb.toString());
//                    return resultMap;
//                }
//
//                //d.校验是否可取到DB净值，如取不到，则提示：“没有获取到DB净值，请重新选择”；
//                log.info("查询净值接口参数：jjdm:{} startDate:{} endDate:{}",comparefundcode,null, DateTimeUtil.getCurDate());
//                List<SmjzAndHbDto> smjzAndHbDtos = smjzAndHbService.getByJjdm(comparefundcode,null,DateTimeUtil.getCurDate());
//                log.info("--------------查询净值接口返回：{}",smjzAndHbDtos == null ? null : JSON.toJSONString(smjzAndHbDtos));
//
//                if (CollectionUtils.isNotEmpty(smjzAndHbDtos)) {
//                    SmjzAndHbDto smjzAndHbDto = smjzAndHbDtos.get(0);
//                    jjjz = BigDecimal.valueOf(smjzAndHbDtos.get(smjzAndHbDtos.size()-1).getJjjz()).setScale(4,BigDecimal.ROUND_DOWN);
//                    if (smjzAndHbDto == null || smjzAndHbDto.getJjjz() == null) {
//                        resultMap.put("saveFlag", "error");
//                        resultMap.put("msg", "没有获取到DB净值，请重新选择");
//                        return resultMap;
//                    }
//                } else {
//                    resultMap.put("saveFlag", "error");
//                    resultMap.put("msg", "没有获取到DB净值，请重新选择");
//                    return resultMap;
//                }
//
//                //校验子代码是否可取到对应的主代码，如取不到，则提示：“取不到对应的主代码，请重新选择”
//                JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(comparefundcode, false);
//                if (jjxx != null) {
//                    if(comparefundcode.equals(jjxx.getMjjdm())){
//                        resultMap.put("code","error");
//                        resultMap.put("msg","取不到对应的主代码，请重新选择");
//                        return resultMap;
//                    }
//
//                    hostfundcode = jjxx.getMjjdm();
//                    param.put("hostfundcode",hostfundcode);
//                }else{
//                    resultMap.put("code","error");
//                    resultMap.put("msg","取不到对应的主代码，请重新选择");
//                    return resultMap;
//                }
//
//                //校验子代码对应的费用记录中，是否存在交易日期相同且类型=实收的费用记录，如存在，则提示：“客户【xxx】存在与交易日期相同的实收费用记录，请重新选择”
//                List<String> subcounts= cmCarryOverTradeService.querySubNafeeCount(param);
//                if (subcounts != null && subcounts.size() > 0) {
//                    StringBuilder stringBuilder = new StringBuilder();
//                    stringBuilder.append("客户:");
//                    for (String custname : subcounts) {
//                        stringBuilder.append("【").append(custname).append("】");
//                    }
//                    stringBuilder.append("存在与交易日期相同的实收费用记录，请重新选择");
//
//                    resultMap.put("code", "error");
//                    resultMap.put("msg", stringBuilder.toString());
//                    return resultMap;
//                }
//
//                //校验主代码对应的费用记录中，是否存在交易日期相同且类型=应收的费用记录，如存在，则提示：“客户【xxx】存在与交易日期相同的应收费用记录，请重新选择”
//                List<String> hostcounts= cmCarryOverTradeService.queryHostNafeeCount(param);
//                if (hostcounts != null && hostcounts.size() > 0) {
//                    StringBuilder stringBuilder = new StringBuilder();
//                    stringBuilder.append("客户:");
//                    for (String custname : hostcounts) {
//                        stringBuilder.append("【").append(custname).append("】");
//                    }
//                    stringBuilder.append("存在与交易日期相同的实收费用记录，请重新选择");
//
//                    resultMap.put("code", "error");
//                    resultMap.put("msg", stringBuilder.toString());
//                    return resultMap;
//                }
//            }
//
//            try{
//                //校验通过，对carryoverlist数据进行处理
//                Map<String,String> resultmap = cmCarryOverTradeService.dealCarryOverTrade(carryoverlist,hostfundcode,jjjz,user.getUserId());
//                String result = resultmap.get("code");
//
//                if(RETURN_RESULT.equals(result)){
//                    List<String> carrylist = new ArrayList<>(2);
//                    carrylist.add(resultmap.get("subid"));
//                    carrylist.add(resultmap.get("fid"));
//                    cmCarryOverTradeService.updateNaManageFee(carrylist);
//                    resultMap.put("code","success");
//                    resultMap.put("msg","保存完成");
//                }else{
//                    resultMap.put("code","error");
//                    resultMap.put("msg","数据异常，请联系开发人员处理");
//                }
//            }catch (Exception ex){
//                log.info("cmCarryOverTradeService.dealCarryOverTrade error:"+ex.getMessage(),ex);
//                resultMap.put("code","error");
//                resultMap.put("msg","系统异常，请联系开发人员处理");
//            }
//        }else{
//            resultMap.put("code","error");
//            resultMap.put("msg","请勾选数据后再点击此功能");
//        }
//
//        return resultMap;
//    }
//
//    /**
//     * cancelDealCarryOverTrade
//     * @param request
//     * @return java.lang.String
//     * @Author: yu.zhang on 2020/9/24 10:02
//     */
//    @ResponseBody
//    @RequestMapping("/cancelDealCarryOverTrade")
//    public String cancelDealCarryOverTrade(HttpServletRequest request) throws Exception {
//
//        String result = "success";
//
//        User user = (User) request.getSession().getAttribute("loginUser");
//        //处理是批量处理
//        String carryid = request.getParameter("carryid");
//        CmCarryOverTradeInfo carryovertradeinfo = cmCarryOverTradeService.getCmCarryOverTradeById(carryid);
//
//        if(carryovertradeinfo != null){
//            String comparefundcode = carryovertradeinfo.getFundcode();
//            String hostfundcode = "";
//            JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(comparefundcode, false);
//
//            if (jjxx != null) {
//                if(comparefundcode.equals(jjxx.getMjjdm())){
//                    result = "error";
//                    return result;
//                }
//                hostfundcode = jjxx.getMjjdm();
//            }else{
//                result = "error";
//                return result;
//            }
//
//            try{
//                //校验通过，对carryoverlist数据进行处理
//                Map<String, Object> resultmap = cmCarryOverTradeService.cancelDealCarryOverTrade(carryovertradeinfo,user.getUserId(),hostfundcode);
//                result = resultmap.get("code").toString();
//
//                if(RETURN_RESULT.equals(result)){
//                    List<String> carrylist = (List<String>) resultmap.get("carrylist");
//                    cmCarryOverTradeService.updateNaManageFee(carrylist);
//                }
//            }catch (Exception ex){
//                log.info("cmCarryOverTradeService.cancelDealCarryOverTrade error:"+ex.getMessage(),ex);
//                result = "error";
//            }
//        }
//
//        return result;
//    }
//
//    /**
//     * viewUpdateCarryOverTrade
//     * @param request
//     * @return java.lang.String
//     * @Author: yu.zhang on 2020/9/24 10:02
//     */
//    @RequestMapping("/viewUpdateCarryOverTrade")
//    public String viewUpdateCarryOverTrade(HttpServletRequest request){
//
//        String carryid = request.getParameter("carryid");
//
//        CmCarryOverTradeInfo carryovertradeinfo = cmCarryOverTradeService.getCmCarryOverTradeById(carryid);
//
//        if(carryovertradeinfo != null){
//
//            if(StringUtils.isNoneBlank(carryovertradeinfo.getFundcode())){
//                log.info("查询净值接口参数：jjdm:{} startDate:{} endDate:{}",carryovertradeinfo.getFundcode(),null,DateTimeUtil.getCurDate());
//                List<SmjzAndHbDto> smjzAndHbDtos = smjzAndHbService.getByJjdm(carryovertradeinfo.getFundcode(),null,DateTimeUtil.getCurDate());
//                log.info("查询净值接口返回：{}",smjzAndHbDtos == null ? null : JSON.toJSONString(smjzAndHbDtos));
//                if(CollectionUtils.isNotEmpty(smjzAndHbDtos)){
//
//                    Double jjjz = smjzAndHbDtos.get(0).getJjjz();
//
//                    request.setAttribute("jjjz",jjjz);
//                    request.setAttribute("sz",carryovertradeinfo.getSubshare().multiply(BigDecimal.valueOf(jjjz)).setScale(4,BigDecimal.ROUND_DOWN));
//                }
//            }
//
//            JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(carryovertradeinfo.getFundcode(), false);
//            if(jjxx != null){
//                carryovertradeinfo.setFundname(jjxx.getJjjc());
//            }
//        }
//        request.setAttribute("carryovertradeinfo",carryovertradeinfo);
//        return "/carryover/upcarryovertrade";
//    }
//
//    /**
//     * updateCarryOverTrade
//     * @param request
//     * @return java.lang.String
//     * @Author: yu.zhang on 2020/9/24 10:03
//     */
//    @ResponseBody
//    @RequestMapping("/updateCarryOverTrade")
//    public String updateCarryOverTrade(HttpServletRequest request) throws Exception {
//
//        String result = "success";
//
//        User user = (User) request.getSession().getAttribute("loginUser");
//        //处理是批量处理
//        String carryid = request.getParameter("upcarryid");
//        String uptradedt = request.getParameter("uptradedt");
//        String upcarryovershare = request.getParameter("upcarryovershare");
//        String isdel = request.getParameter("isdel");
//
//        CmCarryOverTradeInfo carryovertradeinfo = cmCarryOverTradeService.getCmCarryOverTradeById(carryid);
//        boolean checkrepeat = false;
//        if(StringUtils.isBlank(isdel)){
//            Map<String,Object> param = new HashMap<String,Object>(16);
//            param.put("conscustno", carryovertradeinfo.getConscustno());
//            param.put("tradedt", uptradedt);
//            param.put("fundcode", carryovertradeinfo.getFundcode());
//            param.put("isdel", "1");
//            CmCarryOverTradeInfo repeatInfo = cmCarryOverTradeService.getCmCarryOverTrade(param);
//            if(repeatInfo != null && !carryid.equals(repeatInfo.getCarryid())){
//                checkrepeat = true;
//            }
//        }
//
//        if(checkrepeat){
//            result = "repeat";
//        }else{
//            if(StringUtils.isNotBlank(uptradedt)){
//                carryovertradeinfo.setTradedt(uptradedt);
//            }
//            if(StringUtils.isNotBlank(upcarryovershare)){
//                carryovertradeinfo.setCarryovershare(new BigDecimal(upcarryovershare));
//            }
//            if(StringUtils.isNotBlank(isdel)){
//                carryovertradeinfo.setIsdel(isdel);
//            }
//
//            carryovertradeinfo.setModifier(user.getUserId());
//            carryovertradeinfo.setModifydt(DateTimeUtil.getCurDate());
//            cmCarryOverTradeService.updateCmCarryOverTrade(carryovertradeinfo);
//        }
//
//        return result;
//    }
//
//    /**
//     * viewAddCarryOverTrade
//     * @param
//     * @return java.lang.String
//     * @Author: yu.zhang on 2020/9/24 10:03
//     */
//    @RequestMapping("/viewAddCarryOverTrade.do")
//    public String viewAddCarryOverTrade(){
//    	return "/carryover/addCarryOverTrade";
//    }
//
//
//    /**
//     * listCustprivatefundByFundcode
//     * @param request
//     * @return java.util.Map<java.lang.String,java.lang.Object>
//     * @Author: yu.zhang on 2020/9/24 10:03
//     */
//	@ResponseBody
//	@RequestMapping("/listCustprivatefundByFundcode.do")
//	public Map<String, Object> listCustprivatefundByFundcode(HttpServletRequest request)	throws Exception {
//		// 获取查询参数
//		Map<String, Object> param = new HashMap<String,Object>(16);
//		param.put("fundcode",request.getParameter("fundcode"));
//
//		List<Custprivatefund> list = custprivatefundService.listCustprivatefundByConditon(param);
//		List<Custprivatefund> returnList = new ArrayList<Custprivatefund> ();
//
//		if(CollectionUtils.isNotEmpty(list)){
//			Double amount = 0d;
//			for (Custprivatefund info : list) {
//				if(info.getBalancevol() == null || info.getBalancevol().compareTo(BigDecimal.ZERO) <= 0){
//					continue;
//				}
//				info.setBalancevol(info.getBalancevol() == null ? BigDecimal.ZERO : info.getBalancevol());
//				info.setTradedt( null );
//				//查询该产品最近的净值
//				if(StringUtils.isNoneBlank(info.getFundcode())){
//					log.info("查询净值接口参数：jjdm:{} startDate:{} endDate:{}",info.getFundcode(),null,DateTimeUtil.getCurDate());
//					List<SmjzAndHbDto> smjzAndHbDtos = smjzAndHbService.getByJjdm(info.getFundcode(),null,DateTimeUtil.getCurDate());
//					log.info("查询净值接口返回：{}",smjzAndHbDtos == null ? null : JSON.toJSONString(smjzAndHbDtos));
//					if(CollectionUtils.isNotEmpty(smjzAndHbDtos)){
//						amount = smjzAndHbDtos.get(0).getJjjz();
//					}
//				}
//				info.setNav(new BigDecimal(amount));
//				returnList.add(info);
//			}
//		}
//
//		// 返回查询结果
//		Map<String, Object> resultMap = new HashMap<String, Object>(16);
//		resultMap.put("rows", returnList);
//
//		return resultMap;
//
//	}
//
//	/**
//	 * saveBatchCmCarryOverTrade
//	 * @param request
//	 * @param response
//	 * @return java.lang.String
//	 * @Author: yu.zhang on 2020/9/24 10:03
//	 */
//	@ResponseBody
//	@RequestMapping("/saveBatchCmCarryOverTrade.do")
//	public String saveBatchCmCarryOverTrade(HttpServletRequest request,
//										 HttpServletResponse response) throws Exception {
//		String result = "success";
//		String insertData = request.getParameter("insertData");
//		User user = (User) request.getSession().getAttribute("loginUser");
//
//		JSONArray jsonUpdateArray = JSONArray.parseArray(insertData);
//		String conscustno = null;
//		String fundcode = null;
//		String tradedt = null;
//		BigDecimal carryovershare = null;
//		BigDecimal subshare = null;
//		String id = null;
//		List<CmCarryOverTradeInfo> listCmCarryOverTradeInfo = new ArrayList<CmCarryOverTradeInfo>();
//		CmCarryOverTradeInfo cmCarryOverTradeInfo = null;
//		Map<String,Object> param = new HashMap<String,Object>(16);
//		if(jsonUpdateArray != null){
//			for(Object obj : jsonUpdateArray){
//				JSONObject entity = (JSONObject) obj;
//				conscustno = entity.getString("custno");
//				tradedt = entity.getString("tradedt").replace("-", "");
//				fundcode = entity.getString("fundcode");
//				carryovershare = StringUtils.isBlank(entity.getString("carryovershare")) ? BigDecimal.ZERO : new BigDecimal(entity.getString("carryovershare"));
//				subshare = StringUtils.isBlank(entity.getString("balancevol")) ? BigDecimal.ZERO : new BigDecimal(entity.getString("balancevol"));
//				param.put("conscustno", StringUtils.isNotBlank(conscustno) ? conscustno : null);
//				param.put("tradedt", StringUtils.isNotBlank(tradedt) ? tradedt : null);
//				param.put("fundcode", StringUtils.isNotBlank(fundcode) ? fundcode : null);
//				param.put("isdel", "1");
//				CmCarryOverTradeInfo info = cmCarryOverTradeService.getCmCarryOverTrade(param);
//				if(info != null){
//					result = "新增的数据与已存在的数据重复，不允许新增!";
//					return result;
//				}
//
//				id = commonService.getSeqValue("CARRY_OVER_TRADE_SEQ");
//				cmCarryOverTradeInfo = new CmCarryOverTradeInfo();
//				cmCarryOverTradeInfo.setCarryid(id);
//				cmCarryOverTradeInfo.setConscustno(conscustno);
//				cmCarryOverTradeInfo.setTradedt(tradedt);
//				cmCarryOverTradeInfo.setFundcode(fundcode);
//				cmCarryOverTradeInfo.setCarryovershare(carryovershare);
//				cmCarryOverTradeInfo.setSubshare(subshare);
//				cmCarryOverTradeInfo.setStatus("1");
//				cmCarryOverTradeInfo.setIsdel("1");
//				cmCarryOverTradeInfo.setCreatdt(new Date());
//				cmCarryOverTradeInfo.setCreator(user.getUserId());
//				listCmCarryOverTradeInfo.add(cmCarryOverTradeInfo);
//			}
//
//			if(CollectionUtils.isNotEmpty(listCmCarryOverTradeInfo)){
//				cmCarryOverTradeService.batchInsertCmCarryOverTrade(listCmCarryOverTradeInfo);
//			}
//		}
//
//		return result;
//	}
//
//	/**
//	 * importCmCarryOverTrade
//	 * @param request
//	 * @return java.util.Map<java.lang.String,java.lang.Object>
//	 * @Author: yu.zhang on 2020/9/24 10:03
//	 */
//	@ResponseBody
//	@RequestMapping(value="/importCmCarryOverTrade.do",method=RequestMethod.POST)
//	public Map<String, Object> importCmCarryOverTrade(HttpServletRequest request){
//		Map<String, Object> resultMap = new HashMap<String, Object>(16);
//
//		User user = (User)request.getSession().getAttribute("loginUser");
//
//		InputStream input = null;
//		Workbook workBook = null;
//		String uploadFlag = "success";
//
//		try {
//			// 转型为MultipartHttpRequest：
//			MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
//			// 获得文件：
//			MultipartFile file = multipartRequest.getFile("file");
//			// 获得输入流：
//			input = file.getInputStream();
//
//			workBook = Workbook.getWorkbook(input);
//
//			String[] colPropertity = {"conscustno","fundcode","tradedt","subshare","carryovershare"};
//
//			Sheet sheet = workBook.getSheet(0);
//
//			List<CmCarryOverTradeInfo> postList = ExcelUtils.getListByReadShell(sheet, 1, 0, 5, colPropertity,CmCarryOverTradeInfo.class);
//			StringBuilder errorMsg = new StringBuilder();
//			if (null == postList || postList.isEmpty()) {
//				errorMsg.append("没有上传记录");
//				uploadFlag = "error";
//			} else {
//				int line = 2;
//				Map<String,String> notRepeatStr = new HashMap<String,String>(16);
//				for (CmCarryOverTradeInfo info : postList) {
//					String checkstr = checkCmCarryOverTrade(info,notRepeatStr);
//					if (StringUtil.isNotNullStr(checkstr)) {
//						errorMsg.append( "第 " + line + " 行错误是：" + checkstr+"</br>");
//						uploadFlag = "error";
//					}
//					line++;
//				}
//
//				if(RETURN_RESULT.equals(uploadFlag)){
//					for(CmCarryOverTradeInfo info : postList){
//						info.setCarryid(commonService.getSeqValue("CARRY_OVER_TRADE_SEQ"));
//						info.setStatus("1");
//						info.setIsdel("1");
//						info.setCreatdt(new Date());
//						info.setCreator(user.getUserId());
//					}
//					cmCarryOverTradeService.batchInsertCmCarryOverTrade(postList);
//				}
//			}
//			resultMap.put("uploadFlag", uploadFlag);
//			resultMap.put("errorMsg", errorMsg.toString());
//
//	     } catch (Exception e) {
//	            e.printStackTrace();
//	            resultMap.put("uploadFlag", "error");
//	            resultMap.put("errorMsg", "请检查模板是否正确");
//	     }finally{
//	    	try {
//	    		if(input != null){
//	    			input.close();
//	    		}
//			} catch (IOException e) {
//				e.printStackTrace();
//			}
//	     }
//
//		return resultMap;
//	}
//
//	/**
//	 * checkCmCarryOverTrade
//	 * @param info
//	 * @param notRepeatStr
//	 * @return java.lang.String
//	 * @Author: yu.zhang on 2020/9/24 10:03
//	 */
//	private String checkCmCarryOverTrade(CmCarryOverTradeInfo info,Map<String,String> notRepeatStr){
//    	StringBuilder sb = new StringBuilder();
//    	if(StringUtils.isBlank(info.getConscustno())){
//    		sb.append("，投顾客户号必填");
//    	}else{
//    		Map<String,String> param = new HashMap<String,String>();
//    		param.put("conscustno", info.getConscustno());
//    		param.put("conscuststatus", "0");
//    		List<Conscust> listConscust = conscustService.listConscustByMap(param);
//    		if(CollectionUtils.isEmpty(listConscust)){
//    			sb.append("，投顾客户号" + info.getConscustno() + "不存在");
//    		}
//    	}
//
//    	if(StringUtils.isBlank(info.getFundcode())){
//    		sb.append("，基金代码必填");
//    	}else{
//    		if(info.getFundcode().length() > 8){
//    			sb.append("，基金代码过长");
//    		}else{
//                JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
//    			if(jjxxInfo == null){
//    				sb.append("，产品代码" + info.getFundcode() + "不存在");
//    			}
//    		}
//    	}
//
//    	if(StringUtils.isBlank(info.getTradedt())){
//    		sb.append("，交易日期必填");
//    	}else{
//    		if(info.getTradedt().length() != 8){
//    			sb.append("，交易日期格式不对");
//    		}else{
//    			try{
//    				DateTimeUtil.strToDate(info.getTradedt());
//    			}catch(Exception e){
//    				sb.append("，交易日期格式不对");
//    			}
//    		}
//    	}
//
//
//    	if(info.getSubshare() == null){
//    		sb.append("，强赎子份额必填");
//    	}else if(info.getSubshare().compareTo(BigDecimal.ZERO) <= 0){
//    		sb.append("，强赎子份额必须大于0");
//    	}else{
//    		info.setSubshare(info.getSubshare().setScale(2, BigDecimal.ROUND_HALF_UP));
//    	}
//
//    	if(info.getCarryovershare() == null){
//    		sb.append("，结转主份额必填");
//    	}else if(info.getCarryovershare().compareTo(BigDecimal.ZERO) <= 0){
//    		sb.append("，结转主份额必须大于0");
//    	}else{
//    		info.setCarryovershare(info.getCarryovershare().setScale(2, BigDecimal.ROUND_HALF_UP));
//    	}
//
//
//    	String tempStr = info.getConscustno() + info.getFundcode() + info.getTradedt();
//    	boolean isRepart = false;
//    	if(notRepeatStr.containsKey(tempStr)){
//    		sb.append("，文件中存在重复数据");
//    		isRepart = true;
//    	}else{
//    		notRepeatStr.put(tempStr, tempStr);
//    	}
//
//    	if(!isRepart){
//    		Map<String,Object> param = new HashMap<String,Object>(16);
//    		param.put("conscustno", info.getConscustno());
//			param.put("tradedt", info.getTradedt());
//			param.put("fundcode", info.getFundcode());
//			param.put("isdel", "1");
//			CmCarryOverTradeInfo cmCarryOverTradeInfo = cmCarryOverTradeService.getCmCarryOverTrade(param);
//			if(cmCarryOverTradeInfo != null){
//				sb.append("，数据库和文件存在重复数据");
//			}
//    	}
//
//    	if(StringUtil.isNotNullStr(sb.toString())){
//    		return sb.toString().replaceFirst("，", "");
//    	}else{
//    		return sb.toString();
//    	}
//    }
//
//    /**
//     * exportCarryOverTrade
//     * @param request
//     * @param response
//     * @return void
//     * @Author: yu.zhang on 2020/9/24 10:03
//     */
//    @RequestMapping("/exportCarryovertrade.do")
//    public void exportCarryOverTrade(HttpServletRequest request, HttpServletResponse response) throws Exception {
//        // 设置查询参数
//    	Map<String, String> param = new ParamUtil(request).getParamMap();
//		param.put("fundcode",request.getParameter("fundcode"));
//		param.put("status",request.getParameter("status"));
//		param.put("custname",request.getParameter("custname"));
//
//		List<CmCarryOverTradeInfo> list = cmCarryOverTradeService.listCmCarryOverTradeInfo(param);
//
//		ConstantCache constantCache= ConstantCache.getInstance();
//		int i = 1;
//		for (CmCarryOverTradeInfo info : list) {
//			info.setCarryid(Integer.toString(i));
//            JjxxInfo jjxxInfo = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
//			info.setFundname(jjxxInfo == null ? null : jjxxInfo.getJjjc());
//			info.setStatusStr(constantCache.getVal("carryoverstatus", info.getStatus()));
//			i++;
//		}
//
//        try {
//            // 清空输出流
//            response.reset();
//            // 设置文件格式和名字
//            response.setContentType("multipart/form-data");
//            response.setHeader("Content-Disposition",
//                    "attachment;fileName=" + new String("结转份额信息导出.xls".getBytes("gb2312"), "ISO8859-1"));
//            ServletOutputStream os = response.getOutputStream();
//
//            String[] columnName = {"序号","客户姓名","产品代码","产品名称","持仓份额","交易日期","强赎子份额","结转主份额","状态"};
//
//            String[] beanProperty = {"carryid","custname","fundcode","fundname","subshare","tradedt","subshare","carryovershare","statusStr"};
//            ExcelWriter.writeExcel(os, "结转份额信息", 0, list, columnName, beanProperty);
//            os.close(); // 关闭流
//        } catch (Exception e) {
//            log.error("文件导出异常", e);
//        }
//
//    }
//
//
//    /**
//     * 跳转到关联记录页面方法
//     *
//     * @return String
//     */
//    @RequestMapping("/viewRelateRecord.do")
//    public ModelAndView viewRelateRecord(HttpServletRequest request,HttpServletResponse response) throws Exception {
//    	List<Custprivatefundtrade> list = new ArrayList<Custprivatefundtrade>();
//        String carryid = request.getParameter("carryid");
//        Map<String, Object> map = new HashMap<String, Object>();
//        Map<String,Object> param = new HashMap<String,Object>();
//        param.put("carryid", carryid);
//        CmCarryOverTradeInfo info = cmCarryOverTradeService.getCmCarryOverTrade(param);
//        Custprivatefundtrade custprivatefundtrade = null;
//        String appserialno = null;
//        if(info != null){
//        	ConstantCache constantCache= ConstantCache.getInstance();
//
//        	appserialno = info.getRedemptionappserialno();
//            if(StringUtils.isNotBlank(appserialno)){
//            	param.clear();
//            	param.put("appserialno", appserialno);
//            	custprivatefundtrade = custprivatefundtradeService.getCustprivatefundtrade(param);
//            	if(custprivatefundtrade != null){
//            		custprivatefundtrade.setBusivar(constantCache.getVal("busiCode",custprivatefundtrade.getBusicode()));
//            		list.add(custprivatefundtrade);
//            	}
//
//            }
//
//            appserialno = info.getBuyappserialno();
//            if(StringUtils.isNotBlank(appserialno)){
//            	param.clear();
//            	param.put("appserialno", appserialno);
//            	custprivatefundtrade = custprivatefundtradeService.getCustprivatefundtrade(param);
//            	if(custprivatefundtrade != null){
//            		custprivatefundtrade.setBusivar(constantCache.getVal("busiCode",custprivatefundtrade.getBusicode()));
//            		list.add(custprivatefundtrade);
//            	}
//            }
//        }
//
//        map.put("list", list);
//        String url = "/carryover/relaterecord";
//        return new ModelAndView(url, "map", map);
//    }
//}
