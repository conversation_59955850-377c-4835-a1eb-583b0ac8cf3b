package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.base.*;
import com.howbuy.crm.base.hwdealorder.HwDealOrderStatusEnum;
import com.howbuy.crm.hb.constants.MenuCodeConstants;
import com.howbuy.crm.hb.domain.custinfo.CmCustconstant;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.hkconscust.HkConscustVO;
import com.howbuy.crm.hb.domain.prosale.Prebookproductinfo;
import com.howbuy.crm.hb.domain.prosale.Productinfo;
import com.howbuy.crm.hb.service.common.CommonService;
import com.howbuy.crm.hb.service.custinfo.CmCustconstantService;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.outerservice.CrmAccountOuterService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.service.prosale.ProductinfoService;
import com.howbuy.crm.hb.service.system.HbMenuService;
import com.howbuy.crm.hb.tools.ContextManager;
import com.howbuy.crm.hb.web.controller.counter.BaseCounterController;
import com.howbuy.crm.hb.web.service.prebook.PrebookCommonService;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prebook.dto.CmHwOrderInfo;
import com.howbuy.crm.prebook.dto.CmPreBookProductInfo;
import com.howbuy.crm.prebook.request.PrebookTradeDealRequest;
import com.howbuy.crm.prebook.service.HkOrderQueryService;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.prebook.service.PrebookBusinessService;
import com.howbuy.crm.prebook.service.PrebookTradeDealService;
import com.howbuy.crm.prebook.vo.CmPreBookInsertVo;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import com.howbuy.crm.prosale.dto.PreBookValidateResultDto;
import com.howbuy.crm.prosale.dto.PrebookOptLog;
import com.howbuy.crm.prosale.dto.SpecPrebookInfo;
import com.howbuy.crm.prosale.request.GetInfoByParamRequest;
import com.howbuy.crm.prosale.response.GetPreBalanceVolResponse;
import com.howbuy.crm.prosale.response.GetPreTigFundCustResponse;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import com.howbuy.crm.trade.common.enums.counter.CounterStateEnum;
import com.howbuy.crm.trade.model.counter.po.CmCounterOrder;
import com.howbuy.simu.dto.business.product.RmbhlzjjDto;
import com.howbuy.simu.dto.business.product.SmjzAndHbDto;
import com.howbuy.simu.service.business.product.ComprehensiveService;
import com.howbuy.simu.service.business.product.SmjzAndHbService;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.dubbo.model.BaseConstantEnum;
import crm.howbuy.base.utils.DateTimeUtil;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static crm.howbuy.base.constants.StaticVar.*;

/**
 * <AUTHOR>
 * @Description: 
 * @reason:
 * @Date: 2019/8/2 17:08
 */
@Slf4j
@Controller
@RequestMapping("/prosale")
public class PrebookProductInfoController  extends BaseCounterController {


    @Autowired
    private PrebookproductinfoService prebookproductinfoService;

    @Autowired
    private QueryAcctBalanceFacade queryAcctBalanceFacade;

    
    @Autowired
    private QueryPreBookService queryPreBookService;
    
    @Autowired
    private ComprehensiveService comprehensiveService;
    
    @Autowired
    private CommonService commonService;
    
    @Autowired
    private SmjzAndHbService smjzAndHbService;
    
    @Autowired
    private CmCustconstantService cmCustconstantService;
    @Autowired
    private ConscustService custService;
    
    @Autowired
    private ProductinfoService productinfoService;

    @Autowired
    private JjxxInfoService jjxxInfoService;
    @Autowired
    private PrebookCommonService prebookCommonService;
    @Autowired
    private HbMenuService hbMenuService;

    @Autowired
    private PrebookBusinessService prebookBusinessService;

    /**
     * 产品预约菜单的menuCode
     */
    private static final String PREBOOK_MENU_CODE = "B030103";

    @Autowired
    private PrebookTradeDealService prebookTradeDealService ;


    @Autowired
    private PrebookBasicInfoService prebookBasicInfoService;


    @Autowired
    private CrmAccountOuterService crmAccountOuterService;

    @Autowired
    private HkOrderQueryService hkOrderQueryService;

    /**
     *
     * 产品预约列表:
     * @param
    * <AUTHOR>
    * @date 2019/8/2
    */
    @RequestMapping("/listPrebookproductinfo.do")
    public String listPrebookproductinfo(HttpServletRequest request, Map map) {
        //常规产品广度
        map.put("cpTopDataCG", StaticVar.CP_TOP_DATA_CG);
        //
        HttpSession session=request.getSession();
        String topcpdata = (String) session.getAttribute("topcpdata");
        map.put("topcpdata", topcpdata);

        // 是否是[香港运营]角色
        map.put("isXgOperator", prebookCommonService.isXgOperator(request, PREBOOK_MENU_CODE));
        return "prosale/listPrebookproductinfo";
    }


    /**
     * 产品预约列表数据:
     * @param request
    * <AUTHOR>
    * @date 2019/8/5
    */
    @SuppressWarnings("unchecked")
	@ResponseBody
    @RequestMapping("/listPrebookproductinfo_json.do")
    public Map<String, Object> listPrebookproductinfo_json(HttpServletRequest request) throws Exception {
        long startTimestamp = System.currentTimeMillis();
        log.info("listPrebookproductinfo_json.do|预约查询开始 ");

        // 设置查询参数
        Map<String, String> param = new ParamUtil(request).getParamMap();
        String custname = request.getParameter("custname");
        String credt = request.getParameter("credt");
        String fundcode = request.getParameter("fundcode");
        String tradeTypes = request.getParameter("tradeTypes");
        String prebookStates = request.getParameter("prebookStates");
        String payStates = request.getParameter("payStates");
        String tradestate = request.getParameter("tradestate");
        String pretype = request.getParameter("pretype");
        String isrepeatbuy = request.getParameter("isrepeatbuy");
        String expecttradebegdt = request.getParameter("expecttradebegdt");
        String expecttradeenddt = request.getParameter("expecttradeenddt");
        //投顾号
        User user = (User) request.getSession().getAttribute("loginUser");
        param.put("conscode",user.getUserId());
        // 设置查询条件
        param.put("pretype", StringUtil.getStr(pretype));

        // 设置查询条件（客户名）
        param.put("custname", StringUtil.getStr(custname));
        
        // 设置查询条件（录入日期）
        param.put("credt", StringUtil.getStr(credt));
        
        // 设置查询条件（产品代码）
        param.put("pcode", StringUtil.getStr(fundcode));
        
        // 设置查询条件（交易类型）
        param.put("tradeTypes", StringUtil.getStr(tradeTypes));
      
        // 设置查询条件（预约状态）
        param.put("prebookStates", StringUtil.getStr(prebookStates));
       
        // 设置查询条件（打款状态）
        param.put("payStates", StringUtil.getStr(payStates));
     
        // 设置查询条件（交易确认状态）
        param.put("tradeStates", StringUtil.getStr(tradestate));

        // 设置查询条件（是否复购）
        param.put("isrepeatbuy", StringUtil.getStr(isrepeatbuy));
        // 设置查询条件（预约类型）
        param.put("pretype", StringUtil.getStr(pretype));
       
        // 设置查询条件（预计交易开始日期）
        param.put("expecttradebegdt", StringUtil.getStr(expecttradebegdt));

        // 设置查询条件（预计交易结束日期）
        param.put("expecttradeenddt", StringUtil.getStr(expecttradeenddt));

        //查询非分次call
        //2025年5月7日修改： 原分次call产品，都为好臻。 折扣申请只能在[分次call查询页面]操作。 海外分次call，允许在该页面直接 操作。 所以查询不加条件， 按钮处 加入条件 ： 如果archType=='0'，则允许操作。
//        param.put("disfccl","0");

        param.put("finMatched",StringUtil.getStr(request.getParameter("finMatched")));

        //海外产品
        param.put("cpfx",request.getParameter("cpfx"));
        param.put("legaldocStat",request.getParameter("legaldocStat"));
        param.put("legalDocUploadMethod",request.getParameter("legalDocUploadMethod"));

        param.put("submitStatus",request.getParameter("submitStatus"));
        param.put("hkTxAcctNo",request.getParameter("hkTxAcctNo"));
        
        // 判断常量表中合规标识：true启用，false停用
  		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
  		boolean roleCPFlag = false;
  		if (cacheMap != null && !cacheMap.isEmpty()) {
  			roleCPFlag = JSON.toJSONString(cacheMap).contains("true");
  		}

  		// 判断登录人员的角色中是否包括“合规人员”角色
  		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
  		if (roleCPFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
            param.put("hascp", "true");
        }

        // 通过Session获取产品广度信息
        String topcpdata = (String) request.getSession().getAttribute("topcpdata");
        param.put("topCpData", topcpdata);

        long timestamp1 = System.currentTimeMillis();
        log.info("listPrebookproductinfo_json.do|构建查询条件:{},耗时：{}",JSON.toJSONString(param), timestamp1 - startTimestamp);

        PageData<Prebookproductinfo> pageData = prebookproductinfoService.listPrebookproductinfoByPage(param);
        long timestamp2 = System.currentTimeMillis();
        log.info("listPrebookproductinfo_json.do|查询耗时：{}", timestamp2 - timestamp1);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<Prebookproductinfo> listdata = pageData.getListData();
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        ConstantCache constantCache = ConstantCache.getInstance();

        //预约Id列表
        List<BigDecimal> preIdList = listdata.stream().map(Prebookproductinfo::getId).collect(Collectors.toList());
        Map<String,String> onlineInfoMap = prebookBusinessService.getOnLineOrderInfoMap(preIdList);

        for (Prebookproductinfo info : listdata) {
            info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));
            if(StringUtil.isNotNullStr(consOrgCache.getAllUserMap().get(info.getPrebookcheckman()))){
            	info.setPrebookcheckman(consOrgCache.getAllUserMap().get(info.getPrebookcheckman()));
            }
            info.setPaymenttype(info.getPaymenttype());
            info.setExpectpayamtdt(info.getExpectpayamtdt());
            info.setRealpayamt(info.getRealpayamt());
            info.setSellvol(info.getSellvol());
            info.setPrebookstateval(constantCache.getConstantKeyVal("prebookStates").get(info.getPrebookstate()));
            info.setPaystateval(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));

            if (StaticVar.YES.equals(info.getSfxg())) {
                ReturnMessageDto<CmHwOrderInfo>  hwOrderResp= hkOrderQueryService.getHwDealOrderByPreId(info.getId());
                CmHwOrderInfo cmHwDealOrder =  hwOrderResp.getReturnObject();
                if (Objects.nonNull(cmHwDealOrder)) {
                    // 香港产品[下单渠道]：取预约关联的海外中台订单上的【交易渠道】
                    info.setTxchannelval(TradeChannelEnum.getName(cmHwDealOrder.getTradeChannel()));
                    // 香港产品[下单状态]：取预约关联的海外中台订单上的【订单状态】，枚举对齐代销
                    info.setOrderstateval(HwDealOrderStatusEnum.getDescription(cmHwDealOrder.getOrderStatus()));
                    // 香港产品[下单时间]：取预约关联的海外中台订单上的【创建时间】
                    info.setOrdertime(DateUtil.date2String(cmHwDealOrder.getCreateTimestamp(), DateUtil.DEFAULT_DATESFM));
                }
                // 香港产品[资产证明状态]：取预约关联的客户的【资产证明有效期】与当前时间进行比较  若【资产证明有效期】≥【当前服务器时间】，则展示“有效”
                HkConscustVO hkConscustVO = crmAccountOuterService.queryHkCustInfoByCustNo(info.getConscustno());
                if (Objects.nonNull(hkConscustVO) && StringUtils.isNotBlank(hkConscustVO.getAssetCertExpiredDate())) {
                    String nowDate = DateUtil.date2String(new Date(), DateUtil.SHORT_DATEPATTERN);
                    if (nowDate.compareTo(hkConscustVO.getAssetCertExpiredDate()) <= 0) {
                        info.setZczmstate("有效");
                    } else {
                        info.setZczmstate("无效");
                    }
                }
            } else {
                // [下单渠道]
                info.setTxchannelval(constantCache.getConstantKeyVal("tradeChans").get(info.getTxchannel()));
                // [下单状态]
                info.setOrderstateval(constantCache.getConstantKeyVal("dealorderStatus").get(info.getOrderstate()));
                // [资产证明状态]
                info.setZczmstate(prebookproductinfoService.getZczmstate(info.getHboneno(), info.getFundcode(), info.getConscustno()));
            }
            // [是否可线上下单]
            String inlineOrder = onlineInfoMap.get(info.getId().toString());
            info.setIslineorder(YesOrNoEnum.YES.getCode().equals(inlineOrder)?YesOrNoEnum.YES.getDescription():YesOrNoEnum.NO.getDescription());



            if (info.getDiscountstate() != null) {
                info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
            } else {
                info.setDiscountstate("1");
                info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
            }
            info.setPretypeval(constantCache.getConstantKeyVal("pretype").get(info.getPretype()));
            info.setTradestateval(constantCache.getConstantKeyVal("tradestate").get(info.getTradestate()));
            if (null != info.getSourcetype()) {
                info.setSourcetypeVal(constantCache.getConstantKeyVal("custrestype").get(info.getSourcetype()));
            }
            info.setFirstsourceVal(constantCache.getConstantKeyVal("firstrestype").get(info.getFirstsource()));
            info.setRemarks(Util.ObjectToString(info.getRemarks()));
            info.setConsname(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
            info.setLegaldocStatVal(constantCache.getConstantKeyVal("legaldocstat").get(info.getLegaldocStat()));
        }
        long timestamp3 = System.currentTimeMillis();
        log.info("listPrebookproductinfo_json.do|处理数据耗时：{}", timestamp3 - timestamp2);

        resultMap.put("rows", listdata);
        log.info("listPrebookproductinfo_json.do|预约查询结束，耗时：{}", System.currentTimeMillis() - startTimestamp);
        return resultMap;
    }


    /**
     * 撤销预约:
     * @param request
    * <AUTHOR>
    * @date 2019/8/6
    */
    @RequestMapping("/cancelPrebook.do")
    @ResponseBody
    public ReturnMessageDto<String> cancelPrebook(HttpServletRequest request){
    	String result="";
        String id = request.getParameter("id");
        String menuCode = request.getParameter("menuCode");
        String authPara = request.getParameter("authPara");
        User userlogin = (User) request.getSession().getAttribute("loginUser");

        CmPreBookProductInfo prebookInfo= prebookBasicInfoService.getPreBookById(new BigDecimal(id));
        if(prebookInfo == null) {
            return ReturnMessageDto.fail("预约单不存在");
        }
        /**
         * 1、预约购买/追加-撤销：
         * 若预约中 产品类型 = 直销产品 且 打款状态 = 到账确认 且 材料审核状态 = OP初审通过/审核通过，不允许投顾撤销，
         * 点击撤销则弹窗提示：“当前状态不允许撤销，若需撤销请联系smop进行处理！”；
         * 2、赎回-撤销：
         * 若预约中 产品类型 = 直销产品 且 材料审核状态 = OP初审通过/审核通过，不允许投顾撤销，
         * 点击撤销则弹窗提示：“当前状态不允许撤销，若需撤销请联系smop进行处理！”；
         * 3. 添加权限配置：“特殊撤销（直销）”；若配置了“特殊撤销（直销）”，则针对直销产品预约/赎回撤销不进行上述控制；
         */
        //add by zhangshuai 20211222 begin
        HashMap<String, String> pmap = new HashMap<>();
        pmap.put("usercode",userlogin.getUserId());
        pmap.put("menu_code",menuCode);
        pmap.put("auth_para",authPara);
        String hasOpenRange = custService.getHasOpenRange(pmap);
        //没配 特殊撤销权限
        if("FASLE".equals(hasOpenRange)){
            //获取产品信息
            String sfmsjg = "";
            JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(prebookInfo.getPcode(), false);
            if (jjxx1 != null) {
                // 2 直销
                sfmsjg = jjxx1.getSfmsjg();
            }
            //获取材料信息
            boolean flag = false;
            CmCounterOrder orderInfo=getValidOrderByForId(prebookInfo.getId().toPlainString());
            //有材料状态是OP初审通过/审核通过
            if(orderInfo!=null &&
                    (	CounterStateEnum.DONE_OPCS.getKey().equals(orderInfo.getCurStat()) ||
                            CounterStateEnum.ORDER_PASS.getKey().equals(orderInfo.getCurStat()) )
            ){
                flag = true;
            }
            //购买和追加
            if(PreBookTradeTypeEnum.getBuyCodeList().contains(prebookInfo.getTradeType())){
                // 2 直销  4到账确认
                if("2".equals(sfmsjg)&& PAY_STATES_HAS_CONFIRM.equals(prebookInfo.getPaystate())&&flag){
                    return ReturnMessageDto.fail("当前状态不允许撤销，若需撤销请联系smop进行处理！") ;
                }
            }
            //赎回
            else if(PREBOOK_TRADE_TYPE_SALE.equals(prebookInfo.getTradeType())){
                // 2 直销
                if("2".equals(sfmsjg)&& flag){
                    return ReturnMessageDto.fail( "当前状态不允许撤销，若需撤销请联系smop进行处理！") ;
                }
            }

        }

        PrebookTradeDealRequest cancelRequest=new PrebookTradeDealRequest();
        cancelRequest.setPreId(new BigDecimal(id));
        cancelRequest.setOperator(getLoginUserId());
        return prebookTradeDealService.executeCancel(cancelRequest);
    }



    /**
     * 预约-根据客户号校验客户
     * @param custNo
     * @param channelType
     * @return  ReturnMessageDto<Boolean>  Boolean标识是否需要弹窗二次确认
     */
    @ResponseBody
    @RequestMapping("/validateCustForPreBook.do")
    public ReturnMessageDto<Boolean> validateCustForPreBook(String custNo,String channelType){

//        ReturnMessageDto<PreBookValidateResultDto>  validateDto=queryPreBookService.getValidateResultByCustNo(custNo);

        //        仅判断 [实名  入会  掌机登录  字段完整]
        List<PreBookValidateTypeEnum> defineValidateTypeList= Lists.newArrayList(
                PreBookValidateTypeEnum.REAL_NAME,
                PreBookValidateTypeEnum.INVEST_CERTIFIED,PreBookValidateTypeEnum.CUST_ATTRIBUTE
        );
        ReturnMessageDto<PreBookValidateResultDto> validateDto=
                queryPreBookService.getValidateResultByChannel(custNo, PreBookChannelEnum.getEnum(channelType),defineValidateTypeList);


        if(!validateDto.isSuccess()){
            //校验失败 直接返回
           return  ReturnMessageDto.fail(validateDto.getReturnMsg());
        }

        //校验详细结果
        PreBookValidateResultDto resultDto=validateDto.getReturnObject();
        //校验规则 全部校验通过
        if(resultDto.isValidateSuccess()){
            //不 需要二次弹窗确认
           return  ReturnMessageDto.ok("",false);
        }

        //校验失败-特殊场景。只有 入会的判断不合格
        //校验失败的  rule=规则code
        List<String> faileRuleCodeList=
                resultDto.getRuleDto().getTypeEnumList()
                .stream()
                .filter(ruleCode-> !resultDto.isValidateSuccess(ruleCode))
                .collect(Collectors.toList());
        //只有 [入会] 判断未通过
        if(faileRuleCodeList.size()==1 &&
                faileRuleCodeList.get(0).equals(PreBookValidateTypeEnum.INVEST_CERTIFIED.getCode())){
            //需要弹窗确认
            return  ReturnMessageDto.ok(validateDto.getReturnMsg(),true);
        }

        //校验失败-正常场景
        if(!resultDto.isValidateSuccess()){
            //不 需要二次弹窗确认
            return  ReturnMessageDto.fail(validateDto.getReturnMsg(),false);
        }
        return  ReturnMessageDto.ok("",false);
    }


    
    @RequestMapping("/repeatbuy.do")
    public ModelAndView repeatbuy(HttpServletRequest request){
        String id = request.getParameter("id");
        Map<String,Object> map = new HashMap<>();
        String menucode = request.getParameter("menuCode");
        map.put("menucode", menucode);
        String optcode = request.getParameter("optcode");
        map.put("optcode", optcode);
        CmPrebookproductinfo pinfo = prebookproductinfoService.selectPrebookproductinfoById(id);
        ConstantCache constantCache = ConstantCache.getInstance();
        map.put("preid", id);
        JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(pinfo.getPcode(), false);
        map.put("pname",jjxx.getJjjc());
        map.put("conscustname", pinfo.getConscustname());
        map.put("pretype", constantCache.getConstantKeyVal("pretype").get(pinfo.getPretype()));
        map.put("custno", pinfo.getConscustno());
        map.put("currency", ConstantCache.getInstance().getVal("currencys", pinfo.getCurrency()));
        map.put("optdt", DateTimeUtil.getCurDate());//预计打款日期
        map.put("realbuyman", pinfo.getRealbuyman());
        //查询余额
        GetInfoByParamRequest checkreq = new GetInfoByParamRequest();
		checkreq.setConscustno(pinfo.getConscustno());
		checkreq.setFundcode(pinfo.getPcode());
		GetPreBalanceVolResponse balanceVolResponse = queryPreBookService.getPreBalanceVol(checkreq);
		if(balanceVolResponse.isSuccessful()){
			map.put("sellvol", balanceVolResponse.getVol());
		}else{
			map.put("sellvol", new BigDecimal(0.0));
		}

		try{
            BigDecimal balancecost = this.getBalanceCost(request,pinfo.getConscustno(),pinfo.getPcode());

            //如取到的成本为0 ，则持仓成本=持仓份额*DB最新净值（净值取不到默认1）
            if(balancecost.compareTo(new BigDecimal("0")) < 1){
                BigDecimal sellvol = (BigDecimal) map.get("sellvol");
                Double amount = null;
                //查询该产品最近的净值和净值日期
                log.info("查询净值接口参数：jjdm:{} startDate:{} endDate:{}",pinfo.getPcode(),null,DateTimeUtil.getCurDate());
                List<SmjzAndHbDto> smjzAndHbDtos = smjzAndHbService.getByJjdm(pinfo.getPcode(),null,DateTimeUtil.getCurDate());
                log.info("--------------查询净值接口返回：{}",smjzAndHbDtos == null ? null : JSON.toJSONString(smjzAndHbDtos));

                if (CollectionUtils.isNotEmpty(smjzAndHbDtos)) {
                	SmjzAndHbDto smjzAndHbDto = smjzAndHbDtos.get(0);
                    amount = smjzAndHbDto.getJjjz();
                } else {
                    amount = null;
                }

                if(amount == null){
                    amount = 1.0D;
                }

                BigDecimal balance = sellvol.multiply(BigDecimal.valueOf(amount)).divide(new BigDecimal(10000)).setScale(2,BigDecimal.ROUND_DOWN);
                map.put("balancecost", balance);
            }else{
                map.put("balancecost", balancecost);
            }
        }catch (Exception e){
            log.error("smjzService异常："+e.getMessage(),e);
        }

        return new ModelAndView("prosale/addRepeatBuy","map",map);
    }

    /**
     *
     * @param request 返回产品持仓成本
     * @param conscustno
     * @param fundcode
     * @return
     */
    public BigDecimal getBalanceCost(HttpServletRequest request, String conscustno,String fundcode) {

        BigDecimal vol = new BigDecimal(0.0);

        //TODO YU.ZHANG
        //获取客户信息，拿到一账通
        Conscust conscust =null;
        if(StringUtil.isNotNullStr(conscustno)){
            conscust = custService.getConscust(conscustno);
        }
        QueryAcctBalanceRequest req = new QueryAcctBalanceRequest();
        req.setOutletCode("A20150120");
        req.setOperIp(getIpAddr(request));
        req.setTxChannel("1");
        req.setDataTrack("crmsale");
        if (conscust != null) {
            req.setHbOneNo(conscust.getHboneno());
            req.setDisCode(custService.getDiscodeByConsCustno(conscust.getConscustno()));
            log.info("queryAcctBalanceFacade request:"+JSON.toJSONString(req));

            QueryAcctBalanceResponse balanceresponse = queryAcctBalanceFacade.execute(req);
            if("Z0000000".equals(balanceresponse.getReturnCode())){
                List<QueryAcctBalanceResponse.BalanceBean> listbean = balanceresponse.getBalanceList();
                log.info("queryAcctBalanceFacade response:"+JSON.toJSONString(listbean));
                if(listbean != null && listbean.size()>0){
                    BALCOST:
                	for (QueryAcctBalanceResponse.BalanceBean bean : listbean) {
                        if (fundcode.equals(bean.getProductCode())) {
                            if (bean.getBalanceCost() != null ) {
                                vol = vol.add(bean.getBalanceCost());
                                break BALCOST;
                            }
                        }
                    }
                    vol = vol.divide(new BigDecimal(10000));
                }
            }
        }

        return vol;
    }

    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    public String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    @ResponseBody
	@RequestMapping("/saveRepeatPrebookProductInfo.do")
    public Map<String, Object> saveRepeatPrebookProductInfo(HttpServletRequest request) throws Exception {
    	Map<String, Object> resultMap = new HashMap<String, Object>();
		User userlogin = (User) request.getSession().getAttribute("loginUser");
		
		String preid = request.getParameter("id");
		String sellvol = request.getParameter("sellvol");
		String repeatsellvolstr = request.getParameter("repeatsellvol");
        String repeatsellamt = request.getParameter("repeatsellamt");
		String pretrddt = request.getParameter("pretrddt");
		String repeatall = request.getParameter("repeatall");

		BigDecimal repeatsellvol = new BigDecimal(repeatsellvolstr).setScale(2,BigDecimal.ROUND_HALF_UP);
        CmPrebookproductinfo info = prebookproductinfoService.selectPrebookproductinfoById(preid);
		
		//判断是否已经有相关的交易未确认的预约
		Map<String,Object> paramhascount = new HashMap<String,Object>();
		paramhascount.put("conscustno", info.getConscustno());
		paramhascount.put("pcode", info.getPcode());
		paramhascount.put("spectradetype", StaticVar.REPEATBUY_YES);
		paramhascount.put("tradestate", StaticVar.TRADE_STATE_NO);
		int hasrepeatcount = prebookproductinfoService.getHasbuyByPcodeAndConscode(paramhascount);
		if(hasrepeatcount > 0){
			resultMap.put("saveFlag","error");
			resultMap.put("errorMsg", "预约已存在，请撤销后重新预约");
			return resultMap;
		}

		GetInfoByParamRequest checkreq = new GetInfoByParamRequest();
		//校验是否可以预约
		checkreq.setConscustno(info.getConscustno());
		checkreq.setFundcode(info.getPcode());
		//复购不需要判断判断是否为追加还是购买，直接传追加的参数。
		checkreq.setTradetype(StaticVar.PREBOOK_TRADE_TYPE_APPEND);
		checkreq.setSellVol(sellvol);
        checkreq.setExpectTradedt(pretrddt);
        checkreq.setAppAmt(new BigDecimal(repeatsellamt).multiply(new BigDecimal(10000)).setScale(2, BigDecimal.ROUND_HALF_UP));//同下buyAmt


        CmPreBookInsertVo insertVo= new CmPreBookInsertVo();
        String id = commonService.getSeqValue("SEQ_PREBOOK");
        info.setId(new BigDecimal(id));
        info.setTradeType(PREBOOK_TRADE_TYPE_BUY);
        RmbhlzjjDto rmbhlzjjDto = comprehensiveService.getRmbhlzjj(null,info.getCurrency());
        Double zjj = 1.0;
        if (rmbhlzjjDto != null ) {
            info.setRatedt(rmbhlzjjDto.getJzrq());
            zjj = rmbhlzjjDto.getZjj();
        }
        BigDecimal buyamt = null;
        BigDecimal buyamtRmb = null;
        BigDecimal realpayamt = null;
        BigDecimal realpayamtRmb = null;
        realpayamt = buyamt = new BigDecimal(repeatsellamt).multiply(new BigDecimal(10000)).setScale(2, BigDecimal.ROUND_HALF_UP);
        realpayamtRmb = buyamtRmb = buyamt.multiply(new BigDecimal(zjj)).setScale(4, BigDecimal.ROUND_HALF_UP);
        /*}*/

        info.setBuyamt(buyamt);
        info.setBuyamtRmb(buyamtRmb);
        info.setRealpayamt(realpayamt);
        info.setRealpayamtRmb(realpayamtRmb);
        info.setRealpayamtdt(DateTimeUtil.getCurDate());
        info.setExpectpayamtdt(DateTimeUtil.getCurDate());
        info.setExpecttradedt(pretrddt);
        info.setPrebookcheckdt(DateTimeUtil.getCurDate());
        info.setPayapplerdt(DateTimeUtil.getCurDate());
        info.setRealcreator(userlogin.getUserId());
        Map<String, String> paramcons = new HashMap<String,String>();
        paramcons.put("custno", info.getConscustno());
        CmCustconstant cons = cmCustconstantService.getCmCustconstant(paramcons);
        info.setCreator(cons.getConscode());
        info.setCredt(DateTimeUtil.getCurDate());
        info.setPaystate(StaticVar.PAY_STATES_HAS_CONFIRM);
        info.setPrebookstate(StaticVar.PREBOOK_STATES_HAS_CONFIRM);
        info.setTradestate(StaticVar.TRADE_STATE_NO);
        info.setRecstat(StaticVar.CONSCUST_STATUS_NORMAL);
        info.setPretype(StaticVar.PAPER_ORDER);
        info.setSpectradetype(StaticVar.REPEATBUY_YES);
        info.setCretime(DateTimeUtil.PatternDate(new Date(),"HHmmss"));
        info.setTradeverifydt("");
        info.setTradeverifyman("");
        info.setSno(null);
        info.setSnoflag("");

        Conscust conscust = custService.getConscust(info.getConscustno());
        info.setRestype(conscust.getRestype());

        //复购信息
        SpecPrebookInfo specprebookinfo = new SpecPrebookInfo();
        specprebookinfo.setPreid(new BigDecimal(id));
        specprebookinfo.setSpecflag(StaticVar.REPEATBUY_YES);
        specprebookinfo.setIsrepeatall(repeatall);
        specprebookinfo.setHoldvol(new BigDecimal(sellvol));
        specprebookinfo.setRepeatvol(repeatsellvol);
        //日志
        PrebookOptLog log = new PrebookOptLog();
        String logid = commonService.getSeqValue("SEQ_PREBOOK");
        log.setId(logid);
        log.setPreid(id);
        log.setOpttype(StaticVar.PREBOOK_LOG_PRECOMFIRM);
        log.setDes("预约确认操作（复购）");
        log.setCreator(userlogin.getUserId());
        //复购 无业务， 准备删除
//	        InsertPrebookInfoRequest prereq = new InsertPrebookInfoRequest();
//			prereq.setCmPrebookproductinfo(info);
//			prereq.setSpecPrebookInfo(specprebookinfo);
//			prereq.setPrebookOptLog(log);
//			BaseResponse rep = preBookService.insertPrebookInfo(prereq);
        BaseResponse rep= new BaseResponse();
        rep.sysError();
        rep.setDescription("复购 无业务， 不再支持！");
        //复购 无业务， 准备删除
        //暂不支持 复购
        if(BaseConstantEnum.SUCCESS.getCode().equals(rep.getReturnCode())){
            //查询预约后提示语句
            GetPreTigFundCustResponse tigresponse = queryPreBookService.getPreTigFundCust(checkreq);

            if(BaseConstantEnum.SUCCESS.getCode().equals(tigresponse.getReturnCode())){
                //落预约表
                String tigmsg = tigresponse.getMsg();
                if(StringUtil.isNotNullStr(tigmsg)){
                    resultMap.put("saveFlag","success");
                    resultMap.put("errorMsg", tigresponse.getMsg());
                }else{
                    resultMap.put("saveFlag","success");
                }
            }else{
                resultMap.put("saveFlag","error");
                resultMap.put("errorMsg", "预约提示"+tigresponse.getDescription());
            }
        }else{
            resultMap.put("saveFlag","error");
            resultMap.put("errorMsg", "预约新增"+rep.getDescription());
        }

        return resultMap;
    }

    
    
    /**
     * 已退款预约列表:
     * @param
    * <AUTHOR>
    * @date 2019/8/2
    */
    @RequestMapping("/listRefundPre.do")
    public String listRefundPre() {
        return "prosale/listRefundPre";
    }
    
    
    private Map<String,String> getParamMap(HttpServletRequest request) throws Exception {
    	// 设置查询参数
		Map<String,String> param = new ParamUtil(request).getParamMap();
		String custname = request.getParameter("custname");
		String fundcode = request.getParameter("fundcode");
		String expecttradebegdt = request.getParameter("expecttradebegdt");
		String expecttradeenddt = request.getParameter("expecttradeenddt");
		String conscustno = request.getParameter("conscustno");
		String refundbegdt = request.getParameter("refundbegdt");
		String refundenddt = request.getParameter("refundenddt");
		String realpayamtbegdt = request.getParameter("realpayamtbegdt");
		String realpayamtenddt = request.getParameter("realpayamtenddt");
		String prebookStates = request.getParameter("prebookStates");
		String payStates = request.getParameter("payStates");
		String orgcode = request.getParameter("orgcode");
		String conscode = request.getParameter("conscode");

		// 设置查询条件（客户名）
		if (StringUtils.isNotBlank(custname)) {
			param.put("custname", custname);
		} else {
			param.put("custname", null);
		}
		
		// 设置查询条件（基金代码）
		if (StringUtils.isNotBlank(fundcode)) {
			param.put("pcode", fundcode);
		} else {
			param.put("pcode", null);
		}
		
		// 设置查询条件（预计交易开始日期）
		if (StringUtils.isNotBlank(expecttradebegdt)) {
			param.put("expecttradebegdt", expecttradebegdt);
		} else {
			param.put("expecttradebegdt", null);
		}

		// 设置查询条件（预计交易结束日期）
		if (StringUtils.isNotBlank(expecttradeenddt)) {
			param.put("expecttradeenddt", expecttradeenddt);
		} else {
			param.put("expecttradeenddt", null);
		}
		
		// 设置查询条件（投顾客户号）
		if (StringUtils.isNotBlank(conscustno)) {
			param.put("conscustno", conscustno);
		} else {
			param.put("conscustno", null);
		}
				
			
		// 设置查询条件（退款开始日期）
		if (StringUtils.isNotBlank(refundbegdt)) {
			param.put("refundbegdt", refundbegdt);
		} else {
			param.put("refundbegdt", null);
		}

		// 设置查询条件（退款结束日期）
		if (StringUtils.isNotBlank(refundenddt)) {
			param.put("refundenddt", refundenddt);
		} else {
			param.put("refundenddt", null);
		}
		
		// 设置查询条件（实际打款开始日期）
		if (StringUtils.isNotBlank(realpayamtbegdt)) {
			param.put("realpayamtbegdt", realpayamtbegdt);
		} else {
			param.put("realpayamtbegdt", null);
		}

		// 设置查询条件（实际打款结束日期）
		if (StringUtils.isNotBlank(realpayamtenddt)) {
			param.put("realpayamtenddt", realpayamtenddt);
		} else {
			param.put("realpayamtenddt", null);
		}
				
		// 设置查询条件（预约状态）
		if (StringUtils.isNotBlank(prebookStates)) {
			param.put("prebookStates", prebookStates);
		} else {
			param.put("prebookStates", null);
		}		
				
		// 设置查询条件（支付状态）
		if (StringUtils.isNotBlank(payStates)) {
			param.put("payStates", payStates);
		} else {
			param.put("payStates", null);
		}		
				
		// 设置查询条件（投顾编码）
		if (StringUtils.isNotBlank(conscode)) {
			param.put("conscode", conscode);
		} else {
			param.put("orgcode", orgcode);
		}
		
		return param;
    }
    
    /**
	 * 加载页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/listRefundPreByPage_json.do")
	public Map<String, Object> listRefundPreByPage_json(HttpServletRequest request) throws Exception {
		// 设置查询参数
		Map<String,String> param = getParamMap(request);
		param.put("tradeTypesStr", "('1','2')");//购买和追加
		PageData<Prebookproductinfo> pageData = prebookproductinfoService.listPrebookproductinfoByPage(param);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("total", pageData.getPageBean().getTotalNum());
		List<Prebookproductinfo> listdata = pageData.getListData();
		ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
		ConstantCache constantCache = ConstantCache.getInstance();
		for (Prebookproductinfo info : listdata) {
			info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));
			info.setCurrency(constantCache.getConstantKeyVal("currencys").get(info.getCurrency()));
			
			String sfmsjg = ""; // 产品类型：1为代销，2为直销，3为直转代
			String fccl = ""; // 是否分次call产品：1-是 0-否
			// 设置页面基金编码和基金名称
			if (StringUtils.isNotBlank(info.getFundcode())) {
                JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
				if (jjxx1 != null) {
					info.setFundname(jjxx1.getJjjc());
					sfmsjg = jjxx1.getSfmsjg();
					info.setSfmsjg(sfmsjg);
					info.setSfdzcd(jjxx1.getSfdzcd());
					fccl = jjxx1.getFccl();
				}
			}

			// 分次call产品取认缴预约的认缴金额，非分次call产品显示“--”
			if("1".equals(fccl)){
				info.setFccl("1");
			}
			if(StaticVar.REPEATBUY_YES.equals(info.getSpectradetype())){
				info.setPrebookcheckman("sys");
			}else if(StringUtil.isNotNullStr(consOrgCache.getAllUserMap().get(info.getPrebookcheckman()))){
				info.setPrebookcheckman(consOrgCache.getAllUserMap().get(info.getPrebookcheckman()));
			}
			info.setExpectpayamtdt(info.getExpectpayamtdt());
			info.setRealpayamt(info.getRealpayamt());
			info.setSellvol(info.getSellvol());
			info.setPrebookstateval(constantCache.getConstantKeyVal("prebookStates").get(info.getPrebookstate()));
			info.setPaystateval(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));
			info.setOrderstateval(constantCache.getConstantKeyVal("dealorderStatus").get(info.getOrderstate()));
			if (info.getDiscountstate() != null) {
				info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
			} else {
				info.setDiscountstate("1");
				info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
			}
			info.setHmcpx(constantCache.getConstantKeyVal("jjxxhmcpx").get(info.getHmcpx()));
			info.setPretypeval(constantCache.getConstantKeyVal("pretype").get(info.getPretype()));
			info.setTradestateval(constantCache.getConstantKeyVal("tradestate").get(info.getTradestate()));
			info.setRemarks(Util.ObjectToString(info.getRemarks()));
			info.setConsname(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
			info.setOutletName(consOrgCache.getOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(info.getCreator())));
			info.setDiscountTypeVal(constantCache.getConstantKeyVal("discountTypes").get(info.getDiscountType()));
		}
		resultMap.put("rows", listdata);
		return resultMap;
	}
	
	
	
	//导出已退款预约
	@SuppressWarnings("unchecked")
	@ResponseBody
    @RequestMapping("/exportRefundPre.do")
    public Map<String, Object> exportRefundPre(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		// 设置查询参数
		Map<String,String> param = getParamMap(request);
		param.put("tradeTypesStr", "('1','2')");//购买和追加
		param.put("page", "1");
		param.put("rows", "5000");
		param.put("sort", "credt");
		param.put("order", "desc");

		PageData<Prebookproductinfo> pageData = prebookproductinfoService.listPrebookproductinfoByPage(param);
		List<Prebookproductinfo> listdata = pageData.getListData();
		if(CollectionUtils.isNotEmpty(listdata)){
			ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
			ConstantCache constantCache = ConstantCache.getInstance();
			for(Prebookproductinfo info : listdata){
				info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));
				info.setCurrency(constantCache.getConstantKeyVal("currencys").get(info.getCurrency()));
				
				String sfmsjg = ""; // 产品类型：1为代销，2为直销，3为直转代
				String fccl = ""; // 是否分次call产品：1-是 0-否
				// 设置页面基金编码和基金名称
                JjxxInfo jjxx1 = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
				if (jjxx1 != null) {
					info.setFundname(jjxx1.getJjjc());
					sfmsjg = jjxx1.getSfmsjg();
					info.setSfmsjg(sfmsjg);
					info.setSfdzcd(jjxx1.getSfdzcd());
					fccl = jjxx1.getFccl();
				}

				// 分次call产品取认缴预约的认缴金额，非分次call产品显示“--”
				if("1".equals(fccl)){
					info.setFccl("1");
				}
				if(StaticVar.REPEATBUY_YES.equals(info.getSpectradetype())){
					info.setPrebookcheckman("sys");
				}else if(StringUtil.isNotNullStr(consOrgCache.getAllUserMap().get(info.getPrebookcheckman()))){
					info.setPrebookcheckman(consOrgCache.getAllUserMap().get(info.getPrebookcheckman()));
				}
				info.setExpectpayamtdt(info.getExpectpayamtdt());
				info.setRealpayamt(info.getRealpayamt());
				info.setSellvol(info.getSellvol());
				info.setPrebookstateval(constantCache.getConstantKeyVal("prebookStates").get(info.getPrebookstate()));
				info.setPaystateval(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));
				info.setOrderstateval(constantCache.getConstantKeyVal("dealorderStatus").get(info.getOrderstate()));
				if (info.getDiscountstate() != null) {
					info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
				} else {
					info.setDiscountstate("1");
					info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
				}
				info.setHmcpx(constantCache.getConstantKeyVal("jjxxhmcpx").get(info.getHmcpx()));
				info.setPretypeval(constantCache.getConstantKeyVal("pretype").get(info.getPretype()));
				info.setTradestateval(constantCache.getConstantKeyVal("tradestate").get(info.getTradestate()));
				info.setRemarks(Util.ObjectToString(info.getRemarks()));
				info.setConsname(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
				info.setOutletName(consOrgCache.getOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(info.getCreator())));
				String fundtype = "";
				if("0".equals(info.getSfhwjj())){
					fundtype = "海外";
				}else if("1".equals(info.getSfhwjj())){
					fundtype = info.getHmcpx();
				}
				info.setFundtype(fundtype);
				
				String totalamtval = null;
				if("1".equals(info.getFccl()) &&  info.getTotalamt() != null){
					totalamtval = info.getTotalamt().divide(new BigDecimal(10000),2,BigDecimal.ROUND_HALF_DOWN).toString();
				}else{
					totalamtval = "--";
				}
				info.setTotalamtval(totalamtval);
				
				info.setRealpayamt(info.getRealpayamt() == null ? info.getRealpayamt() : info.getRealpayamt().divide(new BigDecimal(10000),2,BigDecimal.ROUND_HALF_DOWN) );
				info.setRealfee(info.getRealfee() == null ? info.getRealfee() : info.getRealfee().setScale(2,BigDecimal.ROUND_HALF_DOWN));
				info.setBeforetaxamt(info.getBeforetaxamt() == null ? info.getBeforetaxamt() : info.getBeforetaxamt().divide(new BigDecimal(10000),2,BigDecimal.ROUND_HALF_DOWN));
				info.setZjj(info.getZjj() == null ? info.getZjj() : info.getZjj().setScale(4,BigDecimal.ROUND_HALF_DOWN) );
				String discountstyle = info.getDiscountstyle();
				if("1".equals(discountstyle)){
					discountstyle = "货币基金";
				}else if("2".equals(discountstyle)){
					discountstyle = "现金";
				}
				info.setDiscountstyle(discountstyle);
				info.setDiscountTypeVal(constantCache.getConstantKeyVal("discountTypes").get(info.getDiscountType()));
			}
		}
				
        try {
            // 清空输出流
            response.reset();
            // 设置文件格式和名字
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + new String((hbMenuService.getMenuName(MenuCodeConstants.REFUND_PRE_MENU_CODE) + ".xls").getBytes("gb2312"), "ISO8859-1"));
            ServletOutputStream os = response.getOutputStream();

            String[] columnName = {"投顾客户号","预约id","预计交易日期","录入时间","客户姓名","所属部门","所属投顾","交易类型","产品代码","产品名称","产品类型","币种","预约类型","预约确认人","预约状态","认缴金额(万)","打款状态","实际打款日期","实际打款金额(万)","实际打款手续费","折扣类型","税前折扣金额(万)","汇率","支付方式","交易确认状态"};

            String[] beanProperty = {"conscustno","id","expecttradedt","credt","conscustname","outletName","consname","tradeTypeVal","fundcode","fundname","fundtype","currency","pretypeval","prebookcheckman","prebookstateval","totalamtval","paystateval","realpayamtdt","realpayamt", "realfee","discountTypeVal","beforetaxamt","zjj","paymenttype","tradestateval"};
            ExcelWriter.writeExcel(os, "已预约退款信息", 0, listdata, columnName, beanProperty);
            os.close(); // 关闭流
        } catch (Exception e) {
            log.error("文件导出异常", e);
        }
        resultMap.put("msg", "success");
		return resultMap;
    }
	
	
	
	/**
     * 跳转到listProductInfoDetail页面方法
     *
     * @return String
     */
    @RequestMapping("/listProductInfoDetail.do")
    public ModelAndView listProductInfoDetail(HttpServletRequest request) {
    	ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/prosale/listProductInfoDetail");
		//查看详情权限
		String opDetail = null;
    	String  menucode = "020148";
    	List<String> userroles = (List<String>) request.getSession().getAttribute("loginRoles");
		for(String role : userroles){
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menucode);
			if(temp != null && temp.contains("1")){
				opDetail = "true";
			}
		}
		
		modelAndView.addObject("opDetail", opDetail);
        return modelAndView;
    }

    
    /**
     * 加载私募产品（上线/下线）页面数据方法
     *
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
	@ResponseBody
    @RequestMapping("/listProductInfoDetail_json.do")
    public Map<String, Object> listProductInfoDetail_json(HttpServletRequest request, HttpServletResponse response, ModelMap model) throws Exception {
        // 设置查询参数
        Map<String, String> param = new HashMap<String, String>();
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();

        // 获取页面查询参数
        String curPage = request.getParameter("page");
        String saleState = request.getParameter("saleState");
        String pName = request.getParameter("pName");
        String hbType = request.getParameter("hbType");
        String productState = request.getParameter("productState");
        String fundReview = request.getParameter("fundReview");
        String fundReviewChan = request.getParameter("fundReviewChan");
        String salType = request.getParameter("salType");
       
        // 如果查询条件（销售状态）不为空，则增加销售状态查询参数
        param.put("saleState", StringUtil.getStr(saleState));
        
        // 如果查询条件（产品名称）不为空，则增加产品名称查询参数
        param.put("pName", StringUtil.getStr(pName));
        
        // 如果查询条件（好买产品线）不为空，则增加好买产品线查询参数
        param.put("hbType", StringUtil.getStr(hbType));
        
        //如果查询条件销售类型不为空，则增加销售类型查询参数
        param.put("salType", StringUtil.getStr(salType));
       
        // 如果查询条件（产品状态）不为空，则增加产品状态查询参数
        param.put("productState", StringUtil.getStr(productState));
       
        param.put("fundReview", StringUtil.getStr(fundReview));
        param.put("fundReviewChan", StringUtil.getStr(fundReviewChan));
     
        // 判断常量表中合规标识：true启用，false停用
 		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
 		boolean roleCPFlag = false;
 		if (cacheMap != null && !cacheMap.isEmpty()) {
 			roleCPFlag = JSON.toJSONString(cacheMap).contains("true");
 		}

 		// 判断登录人员的角色中是否包括“合规人员”角色
 		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
 		if (roleCPFlag || loginRoles.contains("ROLE_CP")) {
			param.put("hascp", "true");
		}

 		// 根据Session获取产品广度信息
        HttpSession session = request.getSession();
        String topcpdata = (String) session.getAttribute("topcpdata");
        param.put("topcpdata", topcpdata);

        Map<String, Object> resultMap = new HashMap<String, Object>();
        PageData<Productinfo> custData = productinfoService.listProductinfoByPage(param);
        for (Productinfo productinfo : custData.getListData()) {
            // 转义好买产品线字段
            if (StringUtil.isNotNullStr(productinfo.getHbtype()) && ContextManager.HMCPXS_TYPE.containsKey(productinfo.getHbtype())) {
                productinfo.setHbtype(ContextManager.HMCPXS_TYPE.get(productinfo.getHbtype()));
            } else {
                productinfo.setHbtype("");
            }

            // 转义预约情况字段
            if (StringUtil.isNotNullStr(productinfo.getPrebookstate()) && ContextManager.CPYYZTS_STATUS.containsKey(productinfo.getPrebookstate())) {
                productinfo.setPrebookstate(ContextManager.CPYYZTS_STATUS.get(productinfo.getPrebookstate()));
            } else {
                productinfo.setPrebookstate("");
            }

            // 转义评级字段
            if (StringUtil.isNotNullStr(productinfo.getPj())) {
                productinfo.setPj(ConstantCache.getInstance().getVal("fundReviewPJ", Util.ObjectToString(productinfo.getPj())));;
            } else {
                productinfo.setPj("");
            }
            
            // 发行方式字段
            if (StringUtil.isNotNullStr(productinfo.getPublishWay()) && ContextManager.FXFSS_TYPE.containsKey(productinfo.getHbtype())) {
                productinfo.setPublishWay(ContextManager.FXFSS_TYPE.get(productinfo.getPublishWay()));
            } else {
                productinfo.setPublishWay("");
            }

        }

        resultMap.put("total", custData.getPageBean().getTotalNum());
        resultMap.put("page", curPage);
        resultMap.put("rows", custData.getListData());
        return resultMap;
    }
	
}
