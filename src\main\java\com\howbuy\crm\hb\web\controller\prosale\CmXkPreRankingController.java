package com.howbuy.crm.hb.web.controller.prosale;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.base.PreOccupyTypeEnum;
import com.howbuy.crm.base.ReturnMessageDto;
import com.howbuy.crm.base.YesNoEnum;
import com.howbuy.crm.hb.domain.prosale.CmSaleCalPercentageInfo;
import com.howbuy.crm.hb.domain.prosale.CmXkPreRankingDto;
import com.howbuy.crm.hb.enums.PreCalculateTypeEnum;
import com.howbuy.crm.hb.service.prosale.CmCustTransferDetailService;
import com.howbuy.crm.hb.service.prosale.CmXkCalendarWarningService;
import com.howbuy.crm.hb.service.prosale.PreControlHbService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.web.controller.BaseController;
import com.howbuy.crm.hb.web.dto.prosale.CmPreControlCalPageDto;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prosale.dto.CategoryProcessInfo;
import com.howbuy.crm.prosale.dto.CmPreCalendar;
import com.howbuy.crm.prosale.dto.CmSaleControlComplexCalInfo;
import com.howbuy.crm.prosale.dto.CmSaleProcessDisplayInfo;
import com.howbuy.crm.prosale.service.PreCalendarService;
import com.howbuy.crm.prosale.service.PreDepositMatchService;
import com.howbuy.crm.prosale.service.PreSaleCalculateService;
import com.howbuy.crm.util.ParamFormatUtil;

import crm.howbuy.base.constants.StaticVar;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 销控报表Controller
 * <AUTHOR> @version 1.0
 * @created 
 */
@Slf4j
@Controller
@RequestMapping(value = "/prosale")
public class CmXkPreRankingController  extends BaseController {
	
	@Autowired
	private PrebookproductinfoService prebookproductinfoService;
	
	@Autowired
	private CmCustTransferDetailService cmCustTransferDetailService;
	
	@Autowired
	private PreCalendarService preCalendarService;
	@Autowired
	private PreSaleCalculateService preSaleCalculateService;
	
	@Autowired
	private PreControlHbService preControlHbService;

	@Autowired
    private CmXkCalendarWarningService cmXkCalendarWarningService;

	@Autowired
	private PreDepositMatchService preDepositMatchService;


	/**
	 * 跳转到预约排位页面方法
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listCmXkPreRanking.do")
	public ModelAndView listCmPreRanking(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/prosale/listCmXkPreRanking");
		return modelAndView;
	}
	
	
	/**
	 * 加载预约排位页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/listCmXkPreRanking_json.do")
	public Map<String, Object> listCmXkPreRankingJson(HttpServletRequest request) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(3);
		
		Map<String, String> param = new ParamUtil(request).getParamMap();
		//获取预约排位数据
		List<CmXkPreRankingDto> listdata = listCmXkPreRanking(param);
		resultMap.put("rows", listdata);
		return resultMap;
	}
	
	/**
	 * 获取预约排位数据
	 * @param param
	 * @return
	 */
	private List<CmXkPreRankingDto> listCmXkPreRanking(Map<String, String> param){
		List<CmXkPreRankingDto> retList = new ArrayList<CmXkPreRankingDto>(1000);
		
		List<CmXkPreRankingDto> listdata = prebookproductinfoService.listCmXkPreRanking(param);
		if(CollectionUtils.isNotEmpty(listdata)){
			//已手工标记到账1、未到账但人工预留额度2、资金与预约已关联0、销控名额3、已资金匹配4
			List<CmXkPreRankingDto> cmXkPreRanking1 = new ArrayList<>(1000);
			List<CmXkPreRankingDto> cmXkPreRanking2 = new ArrayList<>(1000);
			List<CmXkPreRankingDto> cmXkPreRanking0 = new ArrayList<>(1000);
			List<CmXkPreRankingDto> cmXkPreRanking3 = new ArrayList<>(1000);
			List<CmXkPreRankingDto> cmXkPreRanking4 = new ArrayList<>(1000);
			List<CmXkPreRankingDto> cmXkPreRankingNull = new ArrayList<>(1000);
			Map<String,Object> params = new HashMap<String,Object>(1);
			//等位情况
			int dwBalance = 0;
			int dwCount = 0;
			ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
			String orghowbuy="0";
			for(CmXkPreRankingDto cmXkPreRankingDto : listdata){
				//等位情况-金额
				dwBalance = handleDw(cmXkPreRankingDto,dwBalance,"0");
				//等位情况-人数
				dwCount = handleDw(cmXkPreRankingDto,dwCount,"1");
				
				boolean matchedDeposit = ParamFormatUtil.getParamList(cmXkPreRankingDto.getOccupyType(),Lists.newArrayList()).contains(PreOccupyTypeEnum.DEPOSIT.getCode());
				cmXkPreRankingDto.setMatchedDeposit(matchedDeposit);
			    
				//当前预约单已关联自划款总金额+按客户姓名查未关联的没有一账通且对应账户类型的自划款到账总金额
				//资金备注、资金余额
				params.clear();
				params.put("preid", cmXkPreRankingDto.getId());
				//当前预约单已关联自划款总金额

				String summaryInfo = "";
				BigDecimal occurBalance = BigDecimal.ZERO;
				//按客户姓名查未关联的没有一账通且对应账户类型的自划款到账总金额
				List<com.howbuy.crm.prosale.dto.CmCustTransferDetail>  availList=
						preDepositMatchService.selectAvailDepositListForMatch(cmXkPreRankingDto.getHboneNo(),cmXkPreRankingDto.getCustname(),cmXkPreRankingDto.getPcode());
				//不明的资金 查找 匹配关系条数
				List<String> bmIdList=availList.stream()
						.filter(detail-> StringUtil.isEmpty(detail.getHboneNo()))
						.map(com.howbuy.crm.prosale.dto.CmCustTransferDetail::getDepositSid)
						.collect(Collectors.toList());
				if(CollectionUtils.isNotEmpty(bmIdList)){
					Map<String,Integer>  relationCountMap=preControlHbService.selectMatchRelationCount(bmIdList);
					availList=availList.stream()
							//排除掉： 不明资金已有匹配关系
							.filter(detail-> !(relationCountMap.containsKey(detail.getDepositSid()) && relationCountMap.get(detail.getDepositSid())>0 ) )
							.collect(Collectors.toList());
				}
				for(com.howbuy.crm.prosale.dto.CmCustTransferDetail detail : availList){
					if(StringUtils.isNotBlank(detail.getSummaryInfo())){
						summaryInfo += detail.getSummaryInfo() + "/";
					}
					if(detail.getDepositAvailBalance() != null){
						occurBalance = occurBalance.add(detail.getDepositAvailBalance());
					}
				}

				cmXkPreRankingDto.setSummaryInfo(StringUtils.isBlank(summaryInfo) ? "" : summaryInfo.substring(0, summaryInfo.length() - 1));
				cmXkPreRankingDto.setOccurBalance(occurBalance);

				//额度占用标记
				String occupyType = cmXkPreRankingDto.getOccupyType();
				String retOccupyType = "";
				//设置所属投顾相关
				cmXkPreRankingDto.setConsname(consOrgCache.getAllUserMap().get((Util.ObjectToString(cmXkPreRankingDto.getCreator()))));
				cmXkPreRankingDto.setOutletName(consOrgCache.getOrgMap().get(consOrgCache.getCons2OutletMap().get(cmXkPreRankingDto.getCreator())));
				String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(cmXkPreRankingDto.getCreator()));
				if(orghowbuy.equals(uporgcode)){
					cmXkPreRankingDto.setUporgname(cmXkPreRankingDto.getOutletName());
				}else{
					cmXkPreRankingDto.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
				}
				if(StringUtils.isNotBlank(occupyType) ){
					String[] occupyTypeArr = occupyType.split(",");
					for(int i=occupyTypeArr.length-1; i>=0 ; i--){
						retOccupyType += PreOccupyTypeEnum.getDescription(occupyTypeArr[i])+",";
						if(i == 0){
							cmXkPreRankingDto.setOccupyType( StringUtils.isBlank(retOccupyType) ? "" : retOccupyType.substring(0, retOccupyType.length() -1));
							
						    if(PreOccupyTypeEnum.DEPOSIT.getCode().equals(occupyTypeArr[i])){
								cmXkPreRanking0.add(cmXkPreRankingDto);
							}else if(PreOccupyTypeEnum.MARK_DEPOSIT.getCode().equals(occupyTypeArr[i])){
								cmXkPreRanking1.add(cmXkPreRankingDto);
							}else if(PreOccupyTypeEnum.MANNUAL_RESERVE.getCode().equals(occupyTypeArr[i])){
								cmXkPreRanking2.add(cmXkPreRankingDto);
							}else if(PreOccupyTypeEnum.SEND_QUOTA.getCode().equals(occupyTypeArr[i])){
								cmXkPreRanking3.add(cmXkPreRankingDto);
							}
						}
					}
				}else{
					//已资金匹配
					if("1".equals(cmXkPreRankingDto.getFinMatched())){
						cmXkPreRanking4.add(cmXkPreRankingDto);
					}else{
						cmXkPreRankingNull.add(cmXkPreRankingDto);
					}
				}
			}
			
			
			//按类型排序：先按额度占用标记分类排序，具体为：已手工标记到账、未到账但人工预留额度、资金与预约已关联、已资金匹配、空
			if(CollectionUtils.isNotEmpty(cmXkPreRanking1)){
				retList.addAll(cmXkPreRanking1);
			}
			if(CollectionUtils.isNotEmpty(cmXkPreRanking2)){
				retList.addAll(cmXkPreRanking2);
			}
			if(CollectionUtils.isNotEmpty(cmXkPreRanking0)){
				retList.addAll(cmXkPreRanking0);
			}
			if(CollectionUtils.isNotEmpty(cmXkPreRanking3)){
				retList.addAll(cmXkPreRanking3);
			}
			if(CollectionUtils.isNotEmpty(cmXkPreRanking4)){
				retList.addAll(cmXkPreRanking4);
			}
			if(CollectionUtils.isNotEmpty(cmXkPreRankingNull)){
				retList.addAll(cmXkPreRankingNull);
			}
			
			//遍历赋值预约排位、拼接已资金匹配
			if(CollectionUtils.isNotEmpty(retList)){
				int count = 0;
				for(CmXkPreRankingDto cmXkPreRankingDto : retList){
					count++;
					cmXkPreRankingDto.setYypw(count);
					cmXkPreRankingDto.setCredt(cmXkPreRankingDto.getCredt() + " " + cmXkPreRankingDto.getCretime());
					//已资金匹配
					if(YesNoEnum.Y.getCode().equals(cmXkPreRankingDto.getFinMatched()) ){
						cmXkPreRankingDto.setOccupyType(
								String.join("",
										StringUtils.isBlank(cmXkPreRankingDto.getOccupyType()) ?  "" :  cmXkPreRankingDto.getOccupyType()+",",
										"已资金匹配")
								 );
					}
				}
			}
		}
		
		return retList;
	}
	
	/**
	 * 处理等位情况
	 * @param cmXkPreRanking
	 * @param dwqk
	 */
	private int handleDw(CmXkPreRankingDto cmXkPreRanking,int dwqk,String type ){
		//等位情况-金额：1、当预约为正常预约时，等位情况都为空。正常预约：已到账确认、已手工标记到账、未到账但人工预留额度预约、资金与预约已关联、已发送名额通知;2、非以上预约，按预约录入时间排序，取顺序号
		//等位情况-人数：1、当预约为正常预约或交易类型 = 追加时，等位情况都为空。正常预约：已到账确认、已手工标记到账、未到账但人工预留额度预约、资金与预约已关联、已发送名额通知;2、非以上预约，按预约录入时间排序，取顺序号
		if( !"1".equals(cmXkPreRanking.getFinMatched()) && StringUtils.isBlank(cmXkPreRanking.getOccupyType())){
			//等位情况-金额
			if("0".equals(type)){
				dwqk++;
				cmXkPreRanking.setDwBalance(dwqk);
			//等位情况-人数	
			}else if("1".equals(type)){
				//2:追加
				if(!"2".equals(cmXkPreRanking.getTradeType())){
					dwqk++;
					cmXkPreRanking.setDwCount(dwqk);
				}
			}
		}
		return dwqk;
	}
	/**
	 * 导出预约排位列表数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping("/preRankingExport.do")
	public Map<String, Object> orderExport(HttpServletRequest request, HttpServletResponse response) throws Exception {
		Map<String, Object> resultMap = new HashMap<String, Object>(2);
		
		Map<String, String> param = new ParamUtil(request).getParamMap();
		List<CmXkPreRankingDto> exportList = listCmXkPreRanking(param);
		
		// 判断导出List是否为空
		if (exportList != null) {
			try {
				// 清空输出流
				response.reset();
				// 设置文件格式和名字
				response.setContentType("multipart/form-data");
				response.setHeader("Content-Disposition", "attachment;fileName=" + new String("预约排位表.xls".getBytes("gb2312"), "ISO8859-1"));
				ServletOutputStream os = response.getOutputStream();
				String [] columnName = { "录入时间","预约交易日期", "产品代码","产品名称","客户姓名","所属投顾","所属区域","所属部门","预约金额","身份证","额度占用标记","预约排位","等位情况-金额","等位情况-人数","资金余额", "资金备注"};
				String [] beanProperty = { "credt","expecttradedt","pcode","pname","custname","consname","uporgname","outletName","buyAmt","idnoMask","occupyType","yypw","dwBalance","dwCount","occurBalance", "summaryInfo"};

				ExcelWriter.writeExcel(os, "预约排位", 0, exportList, columnName, beanProperty);
				// 关闭流
				os.close(); 
			} catch (Exception e) {
				log.error("文件导出异常", e);
			}
			resultMap.put("msg", "success");
		} else {
			resultMap.put("msg", "noData");
		}
		return resultMap;
	}



	/**
	 * 本期销售统计表页面
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listprecontrolcal.do")
	public ModelAndView lisPreControlCal(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/prosale/listPreControlCal");
		return modelAndView;
	}


	/**
	 * 本期销售统计表数据
	 * @param calendarId
	 * @return ReturnMessageDto
	 */
	@RequestMapping("/listprecontrolcal_json.do")
	@ResponseBody
	public ReturnMessageDto<CmPreControlCalPageDto> listPreControlCalJson(String calendarId) {
		CmPreCalendar calendar=preCalendarService.getCalendarById(calendarId);
		if(calendar==null){
           return  ReturnMessageDto.fail(String.format("日历Id：%s 不存在",calendarId));
		}
		CmPreControlCalPageDto returnDto=new CmPreControlCalPageDto();
		returnDto.setCalendarId(calendar.getCalendarId());
		returnDto.setCurLimit(calendar.getCurrentLimit());
		returnDto.setCurNumLmit(calendar.getCurrentNum());

		//统计
		CmSaleControlComplexCalInfo calInfo=preSaleCalculateService.getComplexCalInfo(calendarId);
        log.info("根据日历Id:{}，汇总统计信息，返回：{}",calendarId, JSONObject.toJSONString(calInfo));
		fillCalculate(calInfo,returnDto);
		return  ReturnMessageDto.ok("",returnDto);
	}


	/**
	 * 统计信息 追加到 页面展示属性中
	 * @param calInfo
	 * @param returnDto
	 * @return
	 */
	private void fillCalculate(CmSaleControlComplexCalInfo calInfo,CmPreControlCalPageDto returnDto){
		//未占位的
		if(calInfo.getUnOccupyCalInfo()!=null){
			returnDto.setUnOccupySumBalance(calInfo.getUnOccupyCalInfo().getSumBalance())
					 .setUnOccupySumNum(calInfo.getUnOccupyCalInfo().getCustNoCount());
		}
		//累计已销售
		returnDto.setProsaledBalance(calInfo.getCurAccumulateCalInfo().getSumBalance());
		returnDto.setProsaledSum(calInfo.getCurAccumulateCalInfo().getCustNoCount());

		//已到账
		returnDto.setArrivalSumBalance(calInfo.getArrivalCalInfo().getSumBalance());
		returnDto.setArrivalSumNum(calInfo.getArrivalCalInfo().getCustNoCount());

		//不明
		returnDto.setBmDepositBalance(calInfo.getBmDepositBalance());
		returnDto.setBmDepositIdList(calInfo.getBmDepositIdList());
		returnDto.setBmDepositNum(calInfo.getBmDepositIdList().size());

		calInfo.getOccupyTypeMap().forEach((occupyType,categoryCalInfo)->{
           PreOccupyTypeEnum typeEnum=PreOccupyTypeEnum.getEnum(occupyType);
			switch (typeEnum) {
				case DEPOSIT:
					returnDto.setDepositMatchSumBalance(categoryCalInfo.getSumBalance()).setDepositMatchSumNum(categoryCalInfo.getCustNoCount());
					return;
				case MARK_DEPOSIT:
					returnDto.setMarkDepositSumBalance(categoryCalInfo.getSumBalance()).setMarkDepositSumNum(categoryCalInfo.getCustNoCount());
					return;
				case MANNUAL_RESERVE:
					returnDto.setReservedSumBalance(categoryCalInfo.getSumBalance()).setReservedSumNum(categoryCalInfo.getCustNoCount());
					return;
				case SEND_QUOTA:
					returnDto.setSendQuotaSumBalance(categoryCalInfo.getSumBalance()).setSendQuotaSumNum(categoryCalInfo.getCustNoCount());
					return;
				default:
			}
		});
	}

	
	/**
	 * 跳转到产品预约打款进度页面方法
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listProPrePayLimit.do")
	public ModelAndView listProPrePayLimit(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/prosale/listProPrePayLimit");
		return modelAndView;
	}


	/**
	 * 加载产品预约打款进度页面数据方法
	 * @param request
	 * @return Map<String, Object>
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping("/listProPrePayLimit_json.do")
	public Map<String, Object> listProPrePayLimitJson(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>(3);
		String calendarId = request.getParameter("calendarId");
		//进度条数据
		CmSaleProcessDisplayInfo displayInfo
				=preSaleCalculateService.getCmSaleProcessInfoByCalendarId(calendarId,getLoginUserId(request));
        //有权限的进度数据列表
		List<CategoryProcessInfo> authList=
		displayInfo.getDataList()
				.stream()
				.filter(CategoryProcessInfo::isHasAuth)
				.collect(Collectors.toList());

		List<CmSaleCalPercentageInfo> percentList=Lists.newArrayList();
		int index=1;
		for(CategoryProcessInfo processInfo :authList){
			CmSaleCalPercentageInfo percentageInfo=new CmSaleCalPercentageInfo();
			percentageInfo.setSortNumber(index);
			percentageInfo.setPcode(displayInfo.getProdCode());
			percentageInfo.setPname(displayInfo.getProdName());
			percentageInfo.setCalculateBalance(processInfo.getCalculateBalance());
			percentageInfo.setBenchMarkBalance(processInfo.getBenchMarkBalance());
			percentageInfo.setPercentNum(processInfo.getCalculatePercent());
			PreCalculateTypeEnum typeEnum=PreCalculateTypeEnum.getEnum(processInfo.getTypeEnum().getCode());
			if(typeEnum!=null){
				percentageInfo.setTypeEnum(typeEnum);
				percentageInfo.setDescription(typeEnum.getDescription());
			}

			percentList.add(percentageInfo);
			index += 1;
		}

		resultMap.put("rows", percentList);
		return resultMap;
	}
}