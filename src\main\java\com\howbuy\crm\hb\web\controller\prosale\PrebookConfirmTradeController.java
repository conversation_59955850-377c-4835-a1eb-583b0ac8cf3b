package com.howbuy.crm.hb.web.controller.prosale;


import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.howbuy.common.page.Page;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.crm.base.*;
import com.howbuy.crm.base.hwdealorder.HwSubmitStatusEnum;
import com.howbuy.crm.hb.domain.custinfo.Conscust;
import com.howbuy.crm.hb.domain.prosale.CmProductParamSetup;
import com.howbuy.crm.hb.domain.prosale.Prebookproductinfo;
import com.howbuy.crm.hb.manager.ProsaleManager;
import com.howbuy.crm.hb.service.custinfo.ConscustService;
import com.howbuy.crm.hb.service.prosale.PrebookproductinfoService;
import com.howbuy.crm.hb.service.prosale.ProductParamSetupService;
import com.howbuy.crm.hb.web.dto.ComboboxItem;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.jjxx.dto.JjxxInfo;
import com.howbuy.crm.jjxx.service.JjxxInfoService;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.cache.ConstantCache;
import com.howbuy.crm.page.core.domain.PageVisitLog;
import com.howbuy.crm.page.core.service.PageVisitLogService;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.utils.ParamUtil;
import com.howbuy.crm.prebook.dto.CmPreBookProductInfo;
import com.howbuy.crm.prebook.dto.CmPrebookBankInfo;
import com.howbuy.crm.prebook.dto.ShouldFeeResultDto;
import com.howbuy.crm.prebook.request.PrebookTradeDealRequest;
import com.howbuy.crm.prebook.service.PrebookBasicInfoService;
import com.howbuy.crm.prebook.service.PrebookBusinessService;
import com.howbuy.crm.prebook.service.PrebookPayService;
import com.howbuy.crm.prebook.service.PrebookTradeDealService;
import com.howbuy.crm.prebook.vo.PreBookConfirmTradeVo;
import com.howbuy.crm.prebook.vo.PreBookUpdateVo;
import com.howbuy.crm.prosale.dto.CmPrebookExtend;
import com.howbuy.crm.prosale.dto.CmPrebookproductinfo;
import com.howbuy.crm.prosale.dto.Discountapp;
import com.howbuy.crm.prosale.request.GetPrebookInfoByIdRequest;
import com.howbuy.crm.prosale.response.GetPrebookByIdResponse;
import com.howbuy.crm.prosale.service.QueryPreBookService;
import com.howbuy.crm.syncfund.dto.FdbXtsylMid;
import com.howbuy.crm.syncfund.service.SyncFundAttrService;
import com.howbuy.crm.util.exception.BusinessException;
import com.howbuy.simu.dto.base.product.SmjzDto;
import com.howbuy.simu.dto.business.product.RmbhlzjjDto;
import com.howbuy.simu.service.base.product.SmjzService;
import crm.howbuy.base.constants.StaticVar;
import crm.howbuy.base.db.PageData;
import crm.howbuy.base.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description:
 * @reason:
 * @Date: 2019/8/14 14:58
 */
@Controller
@RequestMapping("/prosale")
public class PrebookConfirmTradeController {

    private static Logger logger = LoggerFactory.getLogger(PrebookConfirmTradeController.class);
    @Autowired
    private PrebookproductinfoService prebookproductinfoService;
    @Autowired
    private SmjzService smjzService;
    @Autowired
    private ConscustService conscustService;
    @Autowired
    private ProductParamSetupService productParamSetupService;
    @Autowired
    private QueryPreBookService queryPreBookService;

    @Autowired
    private PageVisitLogService pageVisitLogService;

    @Autowired
    private PrebookBasicInfoService prebookBasicInfoService;


    @Autowired
    private PrebookBusinessService prebookBusinessService;

    @Autowired
    private PrebookTradeDealService prebookTradeDealService;

    @Autowired
    private JjxxInfoService jjxxInfoService;

    @Autowired
    private SyncFundAttrService syncFundAttrService;

    @Autowired
    private PrebookPayService prebookPayService;

    @RequestMapping("/confirmTradeList.do")
    public ModelAndView listConfirmTrade() {
        return new ModelAndView("prosale/listConfirmTrade");
    }

    @SuppressWarnings("unchecked")
	@RequestMapping("/confirmTradeListJson.do")
    @ResponseBody
    public Object listConfirmTradeJson(HttpServletRequest request)throws Exception{
        Map<String, Object> resultMap = new HashMap<String, Object>(2);
        Map<String, String> param;
        // 获取分页参数
        param = new ParamUtil(request).getParamMap();
        String custname = request.getParameter("custname");
        String conscustno = request.getParameter("conscustno");
        String hboneno = request.getParameter("hboneno");
        String credt = request.getParameter("credt");
        String pcode = request.getParameter("pcode");
        String tradeTypes = request.getParameter("tradeTypes");
        String prebookStates = request.getParameter("prebookStates");
        String payStates = request.getParameter("payStates");
        String tradestate = request.getParameter("tradestate");
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        String isrepeatbuy = request.getParameter("isrepeatbuy");
        String tradeStartDate = request.getParameter("tradeStartDate");
        String tradeEndDate = request.getParameter("tradeEndDate");
        String cpfx = request.getParameter("cpfx");
        //查询条件（客户名）不为空，增增加客户名参数
        if(StringUtil.isNotNullStr(custname)){
            param.put("custname", custname);
        }

        //查询条件（投顾客户号）不为空，则增加投顾客户号参数
        if(StringUtil.isNotNullStr(conscustno)){
            param.put("conscustno", conscustno);
        }

        //查询条件（一账通号）不为空，则增加一账通号参数
        if(StringUtil.isNotNullStr(hboneno)){
            param.put("hboneno", hboneno);
        }

        //如果查询条件（录入日期）不为空，则增加录入日期查询参数
        if(StringUtil.isNotNullStr(credt)){
            param.put("credt", credt);
        }

        //如果查询条件（预计交易日期开始）不为空，则增加预计交易日期开始查询参数
        if(StringUtil.isNotNullStr(tradeStartDate)){
            param.put("expecttradebegdt", tradeStartDate);
        }
        //如果查询条件（预计交易日期截止）不为空，则增加预计交易日期截止查询参数
        if(StringUtil.isNotNullStr(tradeEndDate)){
            param.put("expecttradeenddt", tradeEndDate);
        }

        //如果查询条件（产品代码）不为空，则增加产品代码查询参数
        if(StringUtil.isNotNullStr(pcode)){
            param.put("pcode", pcode);
        }
        //如果查询条件（交易类型）不为空，则增加交易类型查询参数
        if(StringUtil.isNotNullStr(tradeTypes)){
            param.put("tradeTypes", tradeTypes);
        }

        //如果查询条件（预约状态）不为空，则增加预约状态查询参数
        if(StringUtil.isNotNullStr(prebookStates)){
            param.put("prebookStates", prebookStates);
        }
        //如果查询条件（交易确认状态）不为空，则增加交易确认状态查询参数
        if(StringUtil.isNotNullStr(tradestate)){
            param.put("tradeStates", tradestate);
        }
        //如果查询条件（打款状态）不为空，则增加打款状态查询参数
        if(StringUtil.isNotNullStr(payStates)){
            param.put("payStates", payStates);
        }
        //获取销售机构(XSJG) productDistribution
        if(StringUtil.isNotNullStr(cpfx)){
            param.put("cpfx", cpfx);
        }

        if(StringUtil.isNotNullStr(isrepeatbuy)){
            param.put("isrepeatbuy", isrepeatbuy);
        }
        if (StringUtil.isNotNullStr(consCode)) {
            param.put("conscode", consCode);
        } else {
            param.put("orgcode", orgCode);
        }

        // 判断常量表中合规标识：true启用，false停用
  		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
  		boolean roleCpFlag = false;
  		if (cacheMap != null && !cacheMap.isEmpty()) {
  			roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
  		}

  		// 判断登录人员的角色中是否包括“合规人员”角色
  		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
  		if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
            param.put("hascp", "true");
        }
        List<String> gdfxcplist = (List<String>) request.getSession().getAttribute("gdfxcplist");
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(gdfxcplist) && gdfxcplist.size() == 1 && gdfxcplist.contains("4")) {
            param.put("sfxg", "1");
        }
        param.put("submitStatus",request.getParameter("submitStatus"));
        param.put("hkTxAcctNo",request.getParameter("hkTxAcctNo"));
        // 通过Session获取产品广度信息
        String topcpdata = (String) request.getSession().getAttribute("topcpdata");
        param.put("topCpData", topcpdata);
        PageData<Prebookproductinfo> pageData = prebookproductinfoService.listPrebookproductinfoByPage(param);
        resultMap.put("total", pageData.getPageBean().getTotalNum());
        List<Prebookproductinfo> listdata= pageData.getListData();
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        ConstantCache constantCache = ConstantCache.getInstance();
        for(Prebookproductinfo info : listdata){
            JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
            info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));
            if(jjxx != null){
                // 分次CALL产品查询认缴金额
                if(StaticVar.FCCL_YES.equals(jjxx.getFccl())){
                    info.setFccl(StaticVar.FCCL_YES);
                }
            }
            info.setBuyamt(info.getDealno() != null ? info.getZtbuyamt() : info.getBuyamt());
            info.setPrebookstateval(constantCache.getConstantKeyVal("prebookStates").get(info.getPrebookstate()));
            info.setPaystateval(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));
            info.setOrderstateval(constantCache.getConstantKeyVal("dealorderStatus").get(info.getOrderstate()));
            info.setPretypeval(constantCache.getConstantKeyVal("pretype").get(info.getPretype()));
            info.setPaymenttype(constantCache.getConstantKeyVal("paymenttype").get(info.getPaymenttype()));
            if (info.getDiscountstate() != null) {
                info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
            } else {
                info.setDiscountstate("1");
                info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
            }
            if(StringUtil.isNotNullStr(consOrgCache.getAllConsMap().get(info.getPrebookcheckman()))){
            	info.setPrebookcheckman(consOrgCache.getAllConsMap().get(info.getPrebookcheckman()));
            }
            info.setTradestateval(constantCache.getConstantKeyVal("tradestate").get(info.getTradestate()));
            info.setRemarks(Util.ObjectToString(info.getRemarks()));
            info.setNotes(Util.ObjectToString(info.getNotes()));
            info.setConsname(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
            info.setOutletName(consOrgCache.getOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(info.getCreator())));
            String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(info.getCreator()));
            if("0".equals(uporgcode)){
                info.setUporgname(info.getOutletName());
            }else{
                info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
            }
            boolean dxflag = queryPreBookService.isDxflag(info.getConscustno(),info.getFundcode());
            info.setIszx(dxflag ? "0" : "1");
        }
        resultMap.put("rows", listdata);
        return resultMap;
    }


    /**
     * 根据汇率日期和币种获取DB汇率:
     * @param currency
     * <AUTHOR>
     * @date 2019/8/8
     */
    @RequestMapping("/getDbRateByCurAndDt2")
    @ResponseBody
    public Object getDbRate(String ratedt,String currency){
        Map<String,Object> result = new HashMap<>(1);
        result.put("zjj",null);
        if(StringUtils.isNotBlank(ratedt) && StringUtils.isNotBlank(currency)){
        	RmbhlzjjDto rmbhlzjjDto = prebookproductinfoService.getCurLastZjj(currency,ratedt);
            if(rmbhlzjjDto != null){
                result.put("zjj",rmbhlzjjDto.getZjj());
            }
        }

        return result;
    }

    @RequestMapping("/confirmTrade.do")
    @ResponseBody
    public Object confirmTrade(HttpServletRequest request) {
        Map<String, Object> map = new HashMap<String, Object>(8);
        String id = request.getParameter("id");
        GetPrebookInfoByIdRequest req = new GetPrebookInfoByIdRequest();
        req.setId(new BigDecimal(id));
        GetPrebookByIdResponse res = queryPreBookService.getPrebookById(req);
        if (res == null || res.getPreinfo() == null) {
            throw new BusinessException("", "未查询到预约信息");
        }

        CmPrebookproductinfo info = res.getPreinfo();
        // 增加是否海外产品标识
        map.put("sfxg", info.getSfxg());
        map.put("conscustno", info.getConscustno());
        map.put("fundCode", info.getPcode());
        // 交易类型 0:购买 1: 追加 2:赎回
        map.put("tradeType", info.getTradeType());
        boolean flag = true;
        JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(info.getPcode(), false);
        Double caljjjz = null;
        map.put("nav", null);
        if (jjxx == null) {
            flag = false;
            map.put("errorInfo", "基金数据库不存在该基金，不允许进行交易确认，请联系基金数据库维护");
        } else {
            map.put("errorInfo", null);
            map.put("pname", jjxx.getJjjc());
            String isfccall = jjxx.getFccl();
            map.put("isfccall", isfccall == null ? "0" : isfccall);
            if ("1".equals(isfccall)) {
                map.put("totalAmt", info.getTotalamt());
            } else {
                map.put("totalAmt", "--");
            }
            String hmcpx = jjxx.getHmcpx();
            SmjzDto smjzAndHbDto =getCalculateJjjz(hmcpx, info.getPcode(), info.getExpecttradedt());
            if (smjzAndHbDto!=null) {
                caljjjz = smjzAndHbDto.getJjjz();
                map.put("nav", caljjjz);
            } else {
                flag = false;
                map.put("errorInfo", "无法从基金数据库获取基金净值，不允许进行交易确认操作，请检查基金净值情况");
            }
            //处理汇率相关的信息 。赋值： exchangerate 、 exchangeratedt 、 zjj
            dealRmbzjj(info, map, jjxx.getHbzl());
        }

        Conscust cust = conscustService.getConscust(info.getConscustno());
        int isInner = 0;
        CmProductParamSetup cmProductParamSetup = productParamSetupService.selectParamSetupByPCode(info.getPcode());
        if (cmProductParamSetup != null && cmProductParamSetup.getInternalDeduction() != null) {
            isInner = cmProductParamSetup.getInternalDeduction().intValue();
        }
        //是否内扣
        map.put("isInner", isInner);
        // 获取卡信息列表
        List<CmPrebookBankInfo> hkBankAcctInfoList = prebookBasicInfoService.getHkBankAcctInfoList(new BigDecimal(id));
        map.put("hkBankList", hkBankAcctInfoList);

        map.put("id", id);
        map.put("conscustname", info.getConscustname());
        ConstantCache constantCache = ConstantCache.getInstance();
        map.put("currencyname", ConstantCache.getInstance().getVal("currencys", info.getCurrency()));
        map.put("currency", info.getCurrency());
        map.put("deadline", info.getDeadline());
        map.put("tradetype", info.getTradeType());
        map.put("email", info.getEmail());
        // 获取分红方式
        map.put("divMode", DivModeEnum.getName(info.getDivMode()));

        //赎回货币
        map.put("currencyDesc", CurrencyEnum.getDescription(info.getCurrency()));

        //默认情况下不存在查询固收产品批次号
        map.put("hasfdbxtsyl", "0");
        DecimalFormat df = new DecimalFormat("#0.00");
        //查询应打款手续费金额
        BigDecimal shouldFee =null;
        ReturnMessageDto<ShouldFeeResultDto> shouldFeeResp= prebookPayService.calculateShouldFee(info.getId());
        if(shouldFeeResp != null && shouldFeeResp.isSuccess()){
            shouldFee = shouldFeeResp.getReturnObject().getShouldFee();
        }
        map.put("shouldfee", shouldFee==null?"": df.format(shouldFee.doubleValue()));
        //处理其他信息
        dealOtherInfo(info, map, id, jjxx, caljjjz);
        //预计打款日期
        map.put("expectpayamtdt", info.getExpectpayamtdt());
        //预计交易日期
        map.put("expecttradedt", info.getExpecttradedt());
        //实际打款日期
        map.put("realpayamtdt", info.getRealpayamtdt());
        map.put("fee", info.getFee());
        map.put("appfee", info.getFee());
        Discountapp discountapp = info.getDiscountapp();
        if (discountapp != null) {
            map.put("tradeCount", String.valueOf(discountapp.getTradecount()));
            //折扣类型
            map.put("discountType", constantCache.getVal("discountTypes", discountapp.getDiscountType()));
            //折扣方式
            map.put("discountWay", constantCache.getVal("discountWays", discountapp.getDiscountWay()));
            //折扣形式
            map.put("discountStyle", constantCache.getVal("discountStyles", discountapp.getDiscountStyle()));
            //是否报销
            map.put("isRefund", constantCache.getVal("isRefunds", discountapp.getIsrefund()));
            //折扣率
            map.put("discountRate", discountapp.getDiscountRate().toPlainString());
            //折扣税类型
            map.put("discountTaxType", constantCache.getVal("taxTypes", discountapp.getDiscountTaxType()));
            //折扣理由
            map.put("discountReason", discountapp.getDiscountReason());
            //税后折扣金额
            map.put("afterTaxAmt", discountapp.getAfterTaxAmt());
            //税前折扣金额
            map.put("beforeTaxAmt", discountapp.getBeforeTaxAmt());
            //备注
            map.put("disremarks", discountapp.getRemarks());
            map.put("batchflag", discountapp.getBatchflag());
            //折扣审核状态
            map.put("discountStateDesc", constantCache.getVal("discountStates", discountapp.getDiscountState()));
        } else {
            map.put("tradeCount", null);
            //折扣类型
            map.put("discountType", null);
            //折扣方式
            map.put("discountWay", null);
            //折扣形式
            map.put("discountStyle", null);
            //是否报销
            map.put("isRefund", null);
            //折扣率
            map.put("discountRate", null);
            //折扣税类型
            map.put("discountTaxType", null);
            //折扣理由
            map.put("discountReason", null);
            //税后折扣金额
            map.put("afterTaxAmt", null);
            //税前折扣金额
            map.put("beforeTaxAmt", null);
            //备注
            map.put("disremarks", null);
            map.put("batchflag", null);
            //折扣审核状态
            map.put("discountStateDesc", null);
        }
        map.put("source", cust.getNewsourcename());
        map.put("subSource", cust.getNewsubsourcename());
        map.put("isJoinClub", "1".equals(cust.getIsjoinclub()) ? "是" : "否");
        map.put("flag", flag);
        map.put("redeemModeDesc", RedeemModeEnum.getDescription(info.getRedeemMode()));
        map.put("redeemMode", info.getRedeemMode());

        map.put("curDate", DateUtil.formatNowDate(DateUtil.SHORT_DATE_PATTERN));

        return new ModelAndView("prosale/confirmTrade", "map", map);
    }

    /**
     * 处理汇率相关的信息
     * @param info
     * @param map
     * @param hbzl
     */
    private void dealRmbzjj(CmPrebookproductinfo info,Map<String,Object> map,String hbzl){

        RmbhlzjjDto rmbhlzjjDto = prebookproductinfoService.getCurLastZjj(info.getCurrency() != null ? info.getCurrency() : hbzl,info.getExpecttradedt());
        if(rmbhlzjjDto != null){
            map.put("exchangerate",rmbhlzjjDto.getZjj());
            map.put("exchangeratedt",rmbhlzjjDto.getJzrq());
            map.put("zjj",rmbhlzjjDto.getZjj());
        }else {
        	if(StaticVar.CURRENCY_RMB.equals(info.getCurrency())){
        		map.put("zjj",1);
        	}else{
        		map.put("zjj",null);
        	}
            map.put("exchangerate",1.0);
            map.put("exchangeratedt",StringUtil.getCurrYMD());
        }
    }


    /**
     * 处理其他信息
     * @param info
     * @param map
     * @param id
     * @param jjxx
     * @param caljjjz
     */
    private void dealOtherInfo(CmPrebookproductinfo info,Map<String,Object> map,String id,JjxxInfo jjxx,Double caljjjz){
    	List<ComboboxItem> xtsylList = new ArrayList<>();
		xtsylList.add(new ComboboxItem("","请选择"));

        map.put("sno", "");
		map.put("tzqsr", "");
		map.put("yjdqr", "");
    	if(!StaticVar.PREBOOK_TRADE_TYPE_SALE.equals(info.getTradeType())){
    		//确认金额
    		map.put("ackAmt","0.00");
            if(info.getRealpayamt() != null){
                map.put("ackAmt",info.getRealpayamt().setScale(2,BigDecimal.ROUND_HALF_UP));
            }
            //确认份额
            map.put("ackVol","0");
            if(caljjjz != null && caljjjz != 0 && info.getRealpayamt() != null){
                //如果是内扣
            	if("1".equals(map.get("isInner").toString())){
                    map.put("ackVol",info.getRealpayamt().add(info.getFee()).divide(new BigDecimal(caljjjz),6,RoundingMode.DOWN));
                }else{
                    map.put("ackVol",info.getRealpayamt().divide(new BigDecimal(caljjjz),6,RoundingMode.DOWN));
                }
            }
        	CmPrebookExtend cmPrebookExtend = prebookproductinfoService.getCmPrebookExtend(id);
        	map.put("bankcodename", "");
            map.put("bankacct", "");
        	if (cmPrebookExtend != null){
                map.put("bankcodename", ConstantCache.getInstance().getVal("bankType", cmPrebookExtend.getBankcode()));
                map.put("bankacct", cmPrebookExtend.getBankacct());
            }
            if(info.getBuyamt() != null){
                map.put("buyamt", info.getBuyamt().divide(new BigDecimal(10000)).toPlainString());
            }
            if(info.getRealpayamt() != null){
            	//实际购买金额
                map.put("realpayamt", info.getRealpayamt().divide(new BigDecimal(10000)).toPlainString());
            }

            //非复购的购买追加的固收产品查询产品的关联批次
            if(jjxx != null && StaticVar.HMCPX_GD.equals(jjxx.getHmcpx()) && !"1".equals(info.getSpectradetype())){
            	map.put("hasfdbxtsyl", "1");
            	Map<String,String> param = new HashMap<String,String>(1);
            	param.put("fundcode", info.getPcode());
                List<FdbXtsylMid>  xtsylMidList=syncFundAttrService.selectXtsylList(info.getPcode());
            	if(CollectionUtils.isNotEmpty(xtsylMidList)){
            		for(FdbXtsylMid xtsylMid : xtsylMidList){
            			xtsylList.add(new ComboboxItem(xtsylMid.getSno().toPlainString(),
                                String.join("--",xtsylMid.getTzqsr(),
                                        StringUtil.replaceNullStr(xtsylMid.getYjdqr()))
                                          )
                                      );
            		}
            	}
                //根据 crm 预约的预计交易日期， 获取 默认的 批次号
                FdbXtsylMid matchedXtsyl = syncFundAttrService.selectMatchedXtsyl(info.getPcode(),info.getExpecttradedt());
                if(matchedXtsyl!=null){
                    map.put("sno", matchedXtsyl.getSno());
                    map.put("tzqsr", matchedXtsyl.getTzqsr());
                    map.put("yjdqr", matchedXtsyl.getYjdqr());
                }
            }
        }else{
            // 赎回份额
            map.put("sellvol", info.getSellvol() != null ? info.getSellvol().setScale(6, BigDecimal.ROUND_HALF_UP).toPlainString() : null);
            // 赎回金额
            map.put("sellamt", info.getSellAmt() == null ? null : info.getSellAmt().setScale(6, BigDecimal.ROUND_HALF_UP).toPlainString());
            //确认份额
            map.put("ackVol", info.getSellvol() == null ? BigDecimal.ZERO : info.getSellvol().setScale(6, BigDecimal.ROUND_DOWN));
            //确认金额
            if (StringUtil.isNotNullStr(map.get("sfxg")) && map.get("sfxg").equals(YesNoEnum.Y.getCode())) {
                map.put("ackAmt", info.getSellAmt() == null ? null : info.getSellAmt().setScale(6, BigDecimal.ROUND_HALF_UP).toPlainString());
            } else {
                map.put("ackAmt", caljjjz == null ? "0.00" : info.getSellvol().multiply(new BigDecimal(String.valueOf(caljjjz))).setScale(2, BigDecimal.ROUND_DOWN));
            }

        }
        map.put("xtsylList", xtsylList);
    }

    /**
     * 处理交易
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/confirmTradeDeal.do")
    public ReturnMessageDto<String> confirmTradeDeal(HttpServletRequest request){

        User userlogin = (User)request.getSession().getAttribute("loginUser");
        String id = request.getParameter("id");
        String expectTradeDt = request.getParameter("expecttradedt");
        String nav = request.getParameter("nav");
        String ackAmt = request.getParameter("ackAmt");
        String ackVol = request.getParameter("ackVol");
        String realAckVol = request.getParameter("realAckVol");
        String hasfdbxtsyl = request.getParameter("hasfdbxtsyl");
        String sno = request.getParameter("sno");
        String fee = request.getParameter("fee");
        String rate = request.getParameter("rate");
        String ratedt = request.getParameter("ratedt");

        boolean realoptflag = false;
        //页面手工调整确认份额(调低)/原始份额ackVol[(实际打款-业绩因子+手续费)/基金净值]
        BigDecimal realoptRate=null;


        BigDecimal preId =  new BigDecimal(id);
        CmPreBookProductInfo pinfo= prebookBasicInfoService.getPreBookById(preId);
        if(pinfo==null){
            return  ReturnMessageDto.fail("预约信息不存在!");
        }
        if(PreBookTradeStateEnum.CONFIRMED.getCode().equals(pinfo.getTradestate())){
            return ReturnMessageDto.fail("该预约已交易确认，不能再次交易确认!");
        }
        PreBookUpdateVo preBookUpdateVo = new PreBookUpdateVo();
        PreBookConfirmTradeVo confirmVo =new PreBookConfirmTradeVo();

            preBookUpdateVo.setExpectTradeDt(expectTradeDt);
            if("1".equals(hasfdbxtsyl)){
                //TODO : 购买追加的固收产品 使用  品的关联批次
                confirmVo.setSno(new BigDecimal(sno));
            }
            if(PreBookArchTypeEnum.ZX.getCode().equals(pinfo.getArchType())) {
                confirmVo.setAckAmt(new BigDecimal(ackAmt));

                confirmVo.setFundTradeDt(expectTradeDt);
                confirmVo.setFundAckDt(expectTradeDt);
                confirmVo.setNav(new BigDecimal(nav));

                BigDecimal usedFee = Util.ObjectToBigDecimalNull(fee);
                //预约上的 费用
                BigDecimal preBookFee = pinfo.getFee() == null ? BigDecimal.ZERO : pinfo.getFee();

                //赎回 费用 =  预约的手续费 + 管理费 +业绩报酬
                if (StaticVar.PREBOOK_TRADE_TYPE_SALE.equals(pinfo.getTradeType())) {
                    //赎回
                    confirmVo.setFee(preBookFee);
                    confirmVo.setAckVol(pinfo.getSellvol());
                    confirmVo.setExpectAckVol(pinfo.getSellvol());
                }else{
                    confirmVo.setFee(usedFee==null? preBookFee:usedFee);
                    confirmVo.setAckVol(new BigDecimal(realAckVol));
                    confirmVo.setExpectAckVol(new BigDecimal(ackVol));
                }
                if(StringUtil.isNotNullStr(rate)){
                    confirmVo.setRate(new BigDecimal(rate));
                }
                confirmVo.setRateDt(ratedt);
            }
            //调接口交易确认
             //替换接口
             PrebookTradeDealRequest confirmReq= new PrebookTradeDealRequest();
             confirmReq.setOperator(userlogin.getUserId());
             confirmReq.setPreId(preId);
             confirmReq.setConfirmTradeVo(confirmVo);
             confirmReq.setPreBookUpdateVo(preBookUpdateVo);
             return prebookTradeDealService.executeConfirmTrade(confirmReq);
    }




    /**
     * 根据预约id和预计交易日期查询基金净值:
     * @param request
    * <AUTHOR>
    * @date 2019/8/20
    */
    @ResponseBody
    @RequestMapping("/calconfirmTradenav.do")
    public String calconfirmTradenav(HttpServletRequest request) {
        String id = request.getParameter("id");
        String expecttradedt = request.getParameter("expecttradedt");
        Map<String,String> paraminfo = new HashMap<String,String>(1);
        paraminfo.put("id", id);
        CmPrebookproductinfo pinfo = prebookproductinfoService.selectPrebookproductinfoById(id);
        JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(pinfo.getPcode(), false);
        boolean flag = true;
        String errorInfo = "";
        Double amount = null;
        if(jjxx == null){
            flag = false;
            errorInfo = "基金数据库不存在该基金，不允许进行交易确认，请联系基金数据库维护";
        }else{
        	List<SmjzDto> smjzAndHbDtos = getsmjzAndHbDtos(jjxx.getHmcpx(),pinfo.getPcode(),expecttradedt);
            if(CollectionUtils.isNotEmpty(smjzAndHbDtos)){
                amount = smjzAndHbDtos.get(0).getJjjz();
            }
        }
        if(amount == null){
            //无法从基金数据库获取基金净值,不允许进行交易录入操作
            flag = false;
            errorInfo = "无法从基金数据库获取基金净值，不允许进行交易确认操作，请检查基金净值情况";
            amount = 0D;
        }

        String exchangerate = "1.0";
        String exchangeratedt = "";
        RmbhlzjjDto rmbhlzjjDto = prebookproductinfoService.getCurLastZjj(pinfo.getCurrency(), expecttradedt);
        if(rmbhlzjjDto != null){
            exchangerate = rmbhlzjjDto.getZjj().toString();
            exchangeratedt = rmbhlzjjDto.getJzrq();
        }
        String result = "{\"flag\":\""+String.valueOf(flag)+"\",\"nav\":\""+new BigDecimal(amount).setScale(8,BigDecimal.ROUND_HALF_UP)+"\",\"errorInfo\":\""+errorInfo+"\",\"exchangerate\":\""+exchangerate+"\",\"exchangeratedt\":\""+exchangeratedt+"\"}";
        return result;
    }

    /**
     * 导出数据
     * @param request
     * @param response
     * @return
     */
    @SuppressWarnings("unchecked")
    @ResponseBody
    @RequestMapping("/exportConfirmTradeReport.do")
    public String exportConfirmTradeReport(HttpServletRequest request,
                                           HttpServletResponse response) {
        Map<String,Object> param = new HashMap<>(8);
        String result = "";
        String custname = request.getParameter("query_custname");
        String credt = request.getParameter("query_credt");
        String pcode = request.getParameter("query_pcode");
        String tradeTypes = request.getParameter("query_tradeTypes");
        String prebookStates = request.getParameter("query_prebookStates");
        String payStates = request.getParameter("query_payStates");
        String tradestate = request.getParameter("query_tradestate");
        String orgCode = request.getParameter("query_orgCode");
        String consCode = request.getParameter("query_consCode");
        String isrepeatbuy = request.getParameter("isrepeatbuy");
        String tradeStartDate = request.getParameter("query_expecttradebegdt");
        String tradeEndDate = request.getParameter("query_expecttradeenddt");
        String onlineSignFlag = request.getParameter("onlineSignFlag");
        String signFlag = request.getParameter("signFlag");
        String signFlagBegDt = request.getParameter("signFlagBegDt");
        String signFlagEndDt = request.getParameter("signFlagEndDt");

        //查询条件（客户名）不为空，增增加客户名参数
        if(StringUtil.isNotNullStr(custname)){
            param.put("custname", custname);
        }

        //如果查询条件（录入日期）不为空，则增加录入日期查询参数
        if(StringUtil.isNotNullStr(credt)){
            param.put("credt", credt);
        }

        //如果查询条件（预计交易日期开始）不为空，则增加预计交易日期开始查询参数
        if(StringUtil.isNotNullStr(tradeStartDate)){
            param.put("expecttradebegdt", tradeStartDate);
        }
        //如果查询条件（预计交易日期截止）不为空，则增加预计交易日期截止查询参数
        if(StringUtil.isNotNullStr(tradeEndDate)){
            param.put("expecttradeenddt", tradeEndDate);
        }

        //如果查询条件（产品代码）不为空，则增加产品代码查询参数
        if(StringUtil.isNotNullStr(pcode)){
            param.put("pcode", pcode);
        }
        //如果查询条件（交易类型）不为空，则增加交易类型查询参数
        if(StringUtil.isNotNullStr(tradeTypes)){
            param.put("tradeTypes", tradeTypes);
        }

        //如果查询条件（预约状态）不为空，则增加预约状态查询参数
        if(StringUtil.isNotNullStr(prebookStates)){
            param.put("prebookStates", prebookStates);
        }
        //如果查询条件（交易确认状态）不为空，则增加交易确认状态查询参数
        if(StringUtil.isNotNullStr(tradestate)){
            param.put("tradeStates", tradestate);
        }
        //如果查询条件（打款状态）不为空，则增加打款状态查询参数
        if(StringUtil.isNotNullStr(payStates)){
            param.put("payStates", payStates);
        }

        if(StringUtil.isNotNullStr(isrepeatbuy)){
            param.put("isrepeatbuy", isrepeatbuy);
        }
        if (StringUtil.isNotNullStr(consCode)) {
            if(!"ALL".equals(consCode)){
                param.put("conscode", consCode);
            }
        } else {
            param.put("orgcode", orgCode);
        }
        // 如果查询条件（是否线上签约）不为空，则增加是否线上签约
        if (StringUtil.isNotNullStr(onlineSignFlag)) {
            param.put("onlineSignFlag", onlineSignFlag);
        }
        // 如果查询条件（签署状态)不为空，则增加签署状态
        if (StringUtil.isNotNullStr(signFlag)) {
            param.put("signFlag", signFlag);
        }
        // 如果查询条件（签约时间)不为空，则增加签约时间
        param.put("signFlagBegDt", signFlagBegDt);
        param.put("signFlagEndDt", signFlagEndDt);

        param.put("submitStatus",request.getParameter("submitStatus"));
        param.put("hkTxAcctNo",request.getParameter("query_hkTxAcctNo"));


        // 判断常量表中合规标识：true启用，false停用
  		LinkedHashMap<String, String> cacheMap = ConstantCache.getInstance().getConstantKeyVal("roleCPFlag");
  		boolean roleCpFlag = false;
  		if (cacheMap != null && !cacheMap.isEmpty()) {
  			roleCpFlag = JSON.toJSONString(cacheMap).contains("true");
  		}

  		// 判断登录人员的角色中是否包括“合规人员”角色
  		List<String> loginRoles = (List<String>) request.getSession().getAttribute("loginRoles");
  		if (roleCpFlag || loginRoles.contains(StaticVar.ROLE_CP)) {
            param.put("hascp", "true");
        }
        String topcpdata = (String) request.getSession().getAttribute("topcpdata");
  		param.put("topCpData", topcpdata);
        List<Prebookproductinfo> listData = prebookproductinfoService.listPrebookproductinfoByExport(param);
        Map<String, Object> resultMap = new HashMap<String, Object>(16);
        ConstantCache constantCache = ConstantCache.getInstance();
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        List<PageVisitLog> listlog = new ArrayList<PageVisitLog>();
		User user = (User) request.getSession().getAttribute("loginUser");
		String ip = getIpAddr(request);
        for(Prebookproductinfo info : listData){
        	//记录访问日志
            PageVisitLog pageVisitLog = new PageVisitLog();
            pageVisitLog.setConscustno(info.getConscustno());
            pageVisitLog.setUserId(user.getUserId());
            pageVisitLog.setVisitUrl(request.getRequestURI());
            pageVisitLog.setOperation("交易确认导出");
            pageVisitLog.setVisitTime(new Date());
            pageVisitLog.setPreid(info.getId());
            pageVisitLog.setIp(ip);
            listlog.add(pageVisitLog);
            JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(info.getFundcode(), false);
            info.setTradeTypeVal(constantCache.getConstantKeyVal("tradeTypes").get(info.getTradeType()));
            if(jjxx != null){
                // 分次CALL产品查询认缴金额
                if(StaticVar.FCCL_YES.equals(jjxx.getFccl())){
                    info.setFccl(StaticVar.FCCL_YES);
                }
            }
            info.setBuyamt(info.getDealno() != null ? info.getZtbuyamt() : info.getBuyamt());
            info.setPrebookstateval(constantCache.getConstantKeyVal("prebookStates").get(info.getPrebookstate()));
            info.setPaystateval(constantCache.getConstantKeyVal("payStates").get(info.getPaystate()));
            info.setOrderstateval(constantCache.getConstantKeyVal("dealorderStatus").get(info.getOrderstate()));
            info.setPretypeval(constantCache.getConstantKeyVal("pretype").get(info.getPretype()));
            info.setPaymenttype(constantCache.getConstantKeyVal("paymenttype").get(info.getPaymenttype()));
            if (info.getDiscountstate() != null) {
                info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
            } else {
                info.setDiscountstate("1");
                info.setDiscountstateval(constantCache.getConstantKeyVal("discountStates").get(info.getDiscountstate()));
            }
            info.setPrebookcheckman(ConsOrgCache.getInstance().getAllConsMap().get(info.getPrebookcheckman()));
            info.setTradestateval(constantCache.getConstantKeyVal("tradestate").get(info.getTradestate()));
            info.setRemarks(Util.ObjectToString(info.getRemarks()));
            info.setNotes(Util.ObjectToString(info.getNotes()));
            info.setConsname(ConsOrgCache.getInstance().getAllUserMap().get((Util.ObjectToString(info.getCreator()))));
            info.setOutletName(ConsOrgCache.getInstance().getOrgMap().get(ConsOrgCache.getInstance().getCons2OutletMap().get(info.getCreator())));

            info.setSubmitStatus(HwSubmitStatusEnum.getDescription(info.getSubmitStatus()));

            String uporgcode = consOrgCache.getUpOrgMapCache().get(consOrgCache.getCons2OutletMap().get(info.getCreator()));
            if("0".equals(uporgcode)){
                info.setUporgname(info.getOutletName());
            }else{
                info.setUporgname(consOrgCache.getAllOrgMap().get(uporgcode));
            }
        }
        resultMap.put("custname", custname);
        resultMap.put("credt", credt);
        resultMap.put("expecttradebegdt", tradeStartDate);
        resultMap.put("expecttradeenddt", tradeEndDate);
        String pname = "";
        if(StringUtil.isNotNullStr(pcode)){
            JjxxInfo jjxx = jjxxInfoService.getJjxxByJjdm(pcode, false);
            pname = jjxx.getJjjc();
        }
        resultMap.put("pname", pname);
        resultMap.put("tradeTypes", ConstantCache.getInstance().getConstantKeyVal("tradeTypes").get(tradeTypes));
        resultMap.put("prebookStates", ConstantCache.getInstance().getConstantKeyVal("prebookStates").get(prebookStates));
        resultMap.put("payStates", ConstantCache.getInstance().getConstantKeyVal("payStates").get(payStates));
        resultMap.put("tradestate", ConstantCache.getInstance().getConstantKeyVal("tradestate").get(tradestate));
        resultMap.put("orgCode", ConsOrgCache.getInstance().getAllOrgMap().get(orgCode));
        resultMap.put("consCode",ConsOrgCache.getInstance().getAllConsMap().get(consCode));
        if(StringUtil.isNotNullStr(isrepeatbuy)){
            if("1".equals(isrepeatbuy)){
                isrepeatbuy = "是";
            }else{
                isrepeatbuy = "否";
            }
        }else {
            isrepeatbuy = "全部";
        }
        resultMap.put("isrepeatbuy", isrepeatbuy);
        resultMap.put("rows", listData);
        resultMap.put("downName", "交易确认.xls");
        try{
            ProsaleManager export = new ProsaleManager();
            export.exportConfirmTradeList(response, resultMap);
            for(PageVisitLog log : listlog){
            	pageVisitLogService.recordLog(log);
            }
            result = "success";
        }catch(Exception ex){
            result = "error";
            ex.printStackTrace();
        }
        return result;
    }
    
    /**
     * 获取客户的IP
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    
    private List<SmjzDto> getsmjzAndHbDtos(String hmcpx,String fundCode,String tradeDt){
        List<SmjzDto> pFundHisNav;
        //调用DB接口获取净值
        if (StaticVar.HMCPX_GD.equals(hmcpx) || StaticVar.HMCPX_PEVC.equals(hmcpx)) {
            logger.info("--------------查询净值接口参数：jjdm:{} startDate:{} endDate:{}", fundCode, null, tradeDt);
            Page page = new Page(1, 1);
            pFundHisNav = smjzService.getPFundHisNav(fundCode, null, tradeDt, page);
        }else {
            logger.info("--------------查询净值接口参数：jjdm:{} startDate:{} endDate:{}",fundCode,tradeDt,tradeDt);
            pFundHisNav =smjzService.getPFundHisNav(fundCode,tradeDt,tradeDt);
        }
        logger.info("--------------查询净值接口返回：{}",pFundHisNav == null ? null : JSON.toJSONString(pFundHisNav));
        return pFundHisNav;
    }


    /**
     * @description:(获取 净值)
     * @param hmcpx
     * @param fundCode
     * @param tradeDt
     * @return com.howbuy.simu.dto.base.product.SmjzDto
     * @author: haoran.zhang
     * @date: 2024/11/22 13:26
     * @since JDK 1.8
     */
    private SmjzDto getCalculateJjjz(String hmcpx,String fundCode,String tradeDt){
        List<SmjzDto> smjzAndHbDtos = getsmjzAndHbDtos(hmcpx, fundCode, tradeDt);
        if(CollectionUtils.isNotEmpty(smjzAndHbDtos)){
            return smjzAndHbDtos.get(0);
        }
        return null;
    }
}
