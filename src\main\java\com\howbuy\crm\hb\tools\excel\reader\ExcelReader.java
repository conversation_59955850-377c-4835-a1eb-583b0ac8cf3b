/**   
 * @Title: ExcelReader.java 
 * @Package com.hb.crm.web.util.excel.reader 
 * @Description: excel文件读取的的公用类 
 * <AUTHOR>
 * @date 2016年4月28日 下午5:27:38 
 * @version V1.0   
 */

package com.howbuy.crm.hb.tools.excel.reader;

import com.howbuy.crm.hb.tools.excel.bean.BatchImpBean;
import com.howbuy.crm.hb.tools.excel.bean.BatchImpFile;
import com.howbuy.crm.hb.tools.excel.bean.ExcelDataResult;
import com.howbuy.crm.hb.tools.excel.bean.RegexUtils;
import com.howbuy.crm.hb.tools.excel.util.DBStringUtils;
import jxl.*;
import jxl.read.biff.BiffException;
import org.apache.commons.beanutils.PropertyUtils;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName: ExcelReader
 * @Description: excel进行文件读取
 * <AUTHOR>
 * @date 2016年4月28日 下午5:27:38
 * 
 */
public class ExcelReader {

	/**
	 * @Title: readExcel
	 * @Description: 根据文件流和配置信息进行文件的读取
	 * @param inputStream
	 * @param batchImpBean
	 * @throws BiffException
	 * @throws IOException
	 * @throws ClassNotFoundException
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 * @throws NoSuchMethodException
	 * @throws InvocationTargetException
	 */

	public static ExcelDataResult readExcel(InputStream inputStream,
											BatchImpBean batchImpBean) throws BiffException, IOException,
			ClassNotFoundException, InstantiationException,
			IllegalAccessException, InvocationTargetException,
			NoSuchMethodException {

		ExcelDataResult excelDataResult = new ExcelDataResult();
		
		List<Map<String,String>> errorMapLists = new ArrayList<Map<String,String>>();
		List<Object> lists = new ArrayList<Object>();
		boolean errorFlag = true;

		String objType = batchImpBean.getBeanClassName();
		BatchImpFile[] batchImpFiles = batchImpBean.getArrays();
		int[] columnIndes = batchImpBean.getColumnIndex();

		Workbook workbook = null;
		workbook = Workbook.getWorkbook(inputStream);

		// 取得第一个Sheet页
		Sheet sheet = workbook.getSheet(0);
		int i = 1;
		int j = 0;
		Cell cell = null;
		String value;
		Class<?> cl = Class.forName(objType);
		 int  Columns = sheet.getColumns();
		
		// 文件从第二行开始 (表头的目录不需要，从1开始)
		for (; i < sheet.getRows(); i++) {
			// 初始化bean
			Map<String,String> errorMap = new HashMap<String,String>();
			Object obj = cl.newInstance();
			errorFlag = true;
			j = 0;
			for (; j < columnIndes.length; j++) {
				if(columnIndes[j]>=Columns){
					value ="";
				}else{
					cell = sheet.getCell(columnIndes[j], i);
					value = getValue(cell);
				}
				BatchImpFile batchImpFile = batchImpFiles[j];
				Map<String, Object> map = checkValue(value, batchImpFile);
				
				if (!(Boolean) map.get("type")) {
					excelDataResult.setCheakExcelFlag(false);
					errorFlag=false;
					errorMap.put(batchImpFile.getBeanPorperties(), value);
					errorMap.put("opt_detail", (errorMap.get("opt_detail")==null?(String) map.get("message"):(errorMap.get("opt_detail")+ (String) map.get("message"))));
				}else{
					errorMap.put(batchImpFile.getBeanPorperties(), value);
					PropertyUtils.setProperty(
							obj,
							batchImpFile.getBeanPorperties(),
							ExcelDataType.dataFormatType(value,
									batchImpFile));
				}
			}
			if(!errorFlag){
				errorMapLists.add(errorMap);	
			}
			lists.add(obj);
		}
		excelDataResult.setLists(lists);
		excelDataResult.setErrorMapLists(errorMapLists);
		return excelDataResult;
	}

	/**
	 * @Title: getValue
	 * @Description: 取Cell数据
	 * @param cell
	 * @param
	 * @return
	 */
	public static String getValue(Cell cell) {
		// 取得cell的类型
		String cellType = cell.getType().toString();

		if (cellType.equals(CellType.LABEL.toString())) {
			LabelCell labelCell = (LabelCell) cell;
			String value = labelCell.getString();
			return value;
		} else if (cellType.equals(CellType.DATE.toString())) {
			DateCell dateCell = (DateCell) cell;
			Date date = dateCell.getDate();
			SimpleDateFormat dateFormatType = new SimpleDateFormat("yyyyMMdd");
			dateFormatType.format(date);
			return dateFormatType.format(date);
		}else if (cellType.equals(CellType.EMPTY.toString())) {
			return "";
		} else {
			String value = cell.getContents();
			return value;
		}
	}

	/**
	 * @Title: checkValue
	 * @Description: 校验数据的正确性 是否必填 正则校验 并进行转化
	 * @param value
	 * @param
	 * @param
	 * @param batchImpFile
	 * @return
	 */
	public static Map<String, Object> checkValue(String value, BatchImpFile batchImpFile) {
		
		StringBuffer stringBuffer = new StringBuffer();
		Map<String, Object> map = new HashMap<String, Object>();
		boolean type = true;
		if (batchImpFile.isRequire()) {
			if (value.length() == 0) {
				type = false;
				stringBuffer.append(batchImpFile.getBeanChineseName()+"为必填项；");
			}
		}
		
		if (batchImpFile.getColumnLength()>0){
			if(DBStringUtils.toCharLength(value)>batchImpFile.getColumnLength()){
				type = false;
				stringBuffer.append(batchImpFile.getBeanChineseName()+"字段长度过长，不能超过"+batchImpFile.getColumnLength()+";");
			}
		}
		
		if(batchImpFile.getCheckStatus()){
			if (batchImpFile.getRegex()!=null) {
				if (!RegexUtils.startCheck(batchImpFile.getRegex(), value)) {
					type = false;
					stringBuffer.append(batchImpFile.getErrorMessage());
				}
			}else{
				type = false;
				stringBuffer.append(batchImpFile.getBeanChineseName()+"未配置正则表达式;");
			}
		}
		
		map.put("type", type);
		map.put("message", stringBuffer.toString());

		return map;
	}
}
