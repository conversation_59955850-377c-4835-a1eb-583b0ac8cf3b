package com.howbuy.crm.hb.web.controller.counter;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.common.collect.Lists;
import com.howbuy.crm.trade.model.counter.dto.CounterFilemodelStream;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import com.howbuy.crm.hb.constants.CrmTradeServerPathConstant;
import com.howbuy.crm.hb.web.util.ObjectUtils;
import com.howbuy.crm.page.cache.AuthCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.trade.common.enums.counter.CounterBusiEnum;
import com.howbuy.crm.trade.common.response.BaseResponse;
import com.howbuy.crm.trade.common.response.PageResult;
import com.howbuy.crm.trade.model.counter.dto.CmCounterBusiFileDto;
import com.howbuy.crm.trade.model.counter.dto.CmCounterBusinessDto;
import com.howbuy.crm.trade.model.counter.dto.CmCounterBusinessMsgDto;
import com.howbuy.crm.trade.model.counter.po.CmCounterBusiFile;
import com.howbuy.crm.trade.model.counter.po.CmCounterBusiness;
import com.howbuy.crm.trade.model.counter.po.CmCounterFilemodel;
import com.howbuy.crm.trade.model.counter.po.CmCounterFiletype;
import com.howbuy.crm.trade.model.counter.request.SaveCounterConfigFileRequest;

/**
 * @Description: Controller
 * @version 1.0
 */
@Slf4j
@Controller
@RequestMapping(value = "/counter")
public class CmCounterBusinessController extends BaseCounterController{
	
	 @Resource(name="taskExecutor")
	 private ThreadPoolTaskExecutor taskExecutor;

	/**
	 * 跳转到资料类型配置列表页面
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listCounterBusiness.do")
	public ModelAndView listCmCounterOrder(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		modelAndView.setViewName("/counter/listCounterBusiness");
		Map<String, String> map = validate( request ,null,"B020602");
		if(map != null){
			modelAndView.addObject("opStatus", map.get("status"));
		}
		return modelAndView;
	}
	
	/**
	 * 加载列表页面数据
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping("/listCounterBusinessByPage.do")
	public PageResult<CmCounterBusinessDto> listCounterBusinessByPage(HttpServletRequest request)	throws Exception {
		PageResult<CmCounterBusinessDto> pageResult = new PageResult<CmCounterBusinessDto> ();
		String busiProId = request.getParameter("busiProId");
		String busiId = request.getParameter("busiId");
		String custType = request.getParameter("custType");
		String productType = request.getParameter("productType");
		String isBusiFile = request.getParameter("isBusiFile");
		String page = request.getParameter("page");
    	String rows = request.getParameter("rows");
		
		// 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("busiProId", busiProId);
    	postParam.put("busiId", busiId );
    	postParam.put("custType", custType );
    	if(StringUtils.isNotBlank(productType)){
    		//查询是否进入OP为空
    		if("2".equals(productType)){
    			postParam.put("productTypeIsNull", "1");
    			postParam.put("productType", null);
    		}else{
    			postParam.put("productType", productType);
    		}
    	}else{
    		postParam.put("productType", null );
    	}
    	// 是否已配置文件  1:已配置文件，0：未配置文件
    	postParam.put("isBusiFile", isBusiFile );
    	postParam.put("page", StringUtils.isBlank(page) ? "1": page );
    	postParam.put("rows", StringUtils.isBlank(rows) ? "10" : rows);
    	BaseResponse<PageResult<CmCounterBusinessDto>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_BUSINESS_BY_PAGE, postParam,new ParameterizedTypeReference<BaseResponse<PageResult<CmCounterBusinessDto>>>(){});
		if (httpRsp.isSuccess()) {
			pageResult = httpRsp.getData();
		}
		return pageResult;
	}
	
	
	/**
     * 跳转到vieworupcounterbusiness页面方法
     *
     * @return String
     */
    @RequestMapping("/vieworupcounterbusiness")
    public ModelAndView vieworupcounterbusiness(HttpServletRequest request,HttpServletResponse response) throws Exception {
        String id = request.getParameter("id");
        Map<String, Object> map = new HashMap<String, Object>();
        if (StringUtils.isNotBlank(id)) {
        	CmCounterBusiness domain = null;
        	Map<String,String> postParam = new HashMap<String,String>();
        	postParam.put("id", id);
			BaseResponse<CmCounterBusinessMsgDto> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.GET_COUNTER_BUSINESS_MSG, postParam,new ParameterizedTypeReference<BaseResponse<CmCounterBusinessMsgDto>>(){});
			if (httpRsp.isSuccess()) {
				domain = httpRsp.getData();
			}
        	
            map.put("domain", domain);
            map.put("id", id);
            map.put("type", request.getParameter("type"));
        }
        String url = "/counter/vieworupcounterbusiness";
        return new ModelAndView(url, "map", map);
    }
    
    
    @ResponseBody
	@RequestMapping("/saveCounterBusiness")
	public Map<String, Object> saveCounterBusiness(HttpServletRequest request, HttpServletResponse response) throws Exception {
    	Map<String,Object> map = new HashMap<String,Object> ();
    	String id = request.getParameter("id");
    	if(StringUtils.isEmpty(id)){
    		map.put("msg", "更新id不能为空");
    		return map;
    	}
    	User user = getLoginUser();
		Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("id", id);
    	postParam.put("operatorNo", user.getUserId());
    	postParam.put("busiReq", request.getParameter("busiReq"));
		BaseResponse<String> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.UPDATE_CM_COUNTER_BUSINESS, postParam,new ParameterizedTypeReference<BaseResponse<String>>(){});
		if (httpRsp.isSuccess()) {
			map.put("msg", "success");
		}

    	return map;
    }
    
    
    
    /**
	 * 验证是否有操作权限
	 * @param request
	 * @param response
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/validatePermission", method = RequestMethod.POST)
    public Map<String, String> validatePermission(HttpServletRequest request) {
		//判断是否有权限
		String opercode = request.getParameter("opercode");
		String menucode = request.getParameter("menucode");
		return validate( request , opercode, menucode);
    }
    
    
	private  Map<String, String> validate(HttpServletRequest request ,String opercode,String menucode) {
		Map<String, String> resultMap = new HashMap<String, String>();
		resultMap.put("status", "false");
		//判断是否有权限
		List<String> userroles = (List<String>)request.getSession().getAttribute("loginRoles");
		for(String role : userroles){
			List<String> temp = AuthCache.getInstance().getOperListStr(role, menucode);
			if(StringUtils.isNotBlank(opercode)){
				if(temp != null && temp.contains(opercode)){
					resultMap.put("status", "true");
					break;
				}
			}else{
				if(temp != null && (temp.contains("3") || temp.contains("4")) ){
					resultMap.put("status", "true");
					break;
				}
			}
		}
		return resultMap;
    }
    
    
	/**
	 * 跳转到配置文件页面
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/toConfigFile.do")
	public ModelAndView toConfigFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String id = request.getParameter("id");
		String busiId = request.getParameter("busiId");
		Map<String, Object> map = new HashMap<String, Object>();
		if (StringUtils.isNotBlank(id)) {
			// 设置页面显示数据
			map.put("bdid", id);
			map.put("busiId", busiId);
			map.put("busiName", CounterBusiEnum.getDesc(busiId));
			
			// 设置默认参数
	    	Map<String,String> postParam = new HashMap<String,String>();
	    	postParam.put("bdid", id);
	    	postParam.put("stat", "1");



			BaseResponse<List<CmCounterBusiFileDto>> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_BUSI_FILE_AND_FILE_TYPE_LIST, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterBusiFileDto>>>(){});

			//已配置的文件类型
			List<CmCounterBusiFileDto> listCmCounterBusiFileDto = httpRsp.getData();

			postParam.clear();
	    	postParam.put("stat", "1");
	    	BaseResponse<List<CmCounterFiletype>> rsp = getPostEntityByMap(CrmTradeServerPathConstant.QUERY_COUNTER_FILE_TYPE_LIST, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterFiletype>>>(){});

			//所有的配置类型
			List<CmCounterFiletype> listCmCounterFiletype = rsp.getData();

			//赋值：已配置的文件类型
			map.put("configList", listCmCounterBusiFileDto);
			//未配置的文件类型
	    	List<CmCounterFiletype> noConfigList = new ArrayList<CmCounterFiletype>(8);
			CmCounterFiletype filetype = null;
			for(CmCounterFiletype cmCounterFiletype : listCmCounterFiletype){
                //默认未配置
				boolean isInclude = false;
				for(CmCounterBusiFileDto cmCounterBusiFileDto : listCmCounterBusiFileDto){
					filetype = cmCounterBusiFileDto.getCmCounterFiletype();
					if( filetype != null && cmCounterFiletype.getId().equals(filetype.getId()) ){
						isInclude = true;
						break;
					}
				}
				
				if(!isInclude){
					noConfigList.add(cmCounterFiletype);
				}
			}
			//赋值：未配置的文件类型
			map.put("noConfigList", noConfigList);

		}
		return new ModelAndView("/counter/configFile", "map", map);
	}
	
	
	
	/**
	 * 保存配置文件信息
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/saveConfigFile.do")
	public String saveConfigFile(HttpServletRequest request) {
		String result = "";
		User user = getLoginUser();
		String bdid = ObjectUtils.replaceNull(request.getParameter("bdid"));
		String allFids = ObjectUtils.replaceNull(request.getParameter("allFids"));
		String addFids = ObjectUtils.replaceNull(request.getParameter("addFids"));
		if (StringUtils.isNotBlank(bdid)) {
			Map<String,Object> postParam = new HashMap<String,Object>();
	    	postParam.put("id", bdid);
	    	postParam.put("operatorNo", user.getUserId());
	    	/*if(StringUtils.isNotBlank(allFids)){
	    		postParam.put("allFtidList", allFids);
	    	}
	    	if(StringUtils.isNotBlank(addCounterBusiFiles)){
	    		postParam.put("addCounterBusiFiles", addCounterBusiFiles);
	    	}*/
			//BaseResponse<String> httpRsp = getPostEntity(CrmTradeServerPathConstant.SAVE_COUNTER_CONFIG_FILE, postParam,new TypeReference<BaseResponse<String>>(){});
	    	SaveCounterConfigFileRequest saveCounterConfigFileRequest = new SaveCounterConfigFileRequest();
	    	saveCounterConfigFileRequest.setId(bdid);
	    	saveCounterConfigFileRequest.setOperatorNo( user.getUserId());
	    	if(StringUtils.isNotBlank(allFids)){
	    		saveCounterConfigFileRequest.setAllFtidList(Lists.newArrayList(allFids.split(",")));
	    	}
	    	List<CmCounterBusiFile> addCounterBusiFiles = null;
	    	// 增加新配置的文件
			if (StringUtils.isNotBlank(addFids)) {
				addCounterBusiFiles = new ArrayList<CmCounterBusiFile>();
				String fids = addFids.replaceFirst("\\|", "");
				String[] resultArray = fids.split("\\|");
				for (String resultSingle : resultArray) {
					String ftid = resultSingle.split("`")[0];
					String isRequire = resultSingle.split("`")[1];
					String isTip = resultSingle.split("`")[2];
					String des = "";
					if(resultSingle.split("`").length == 4) {
						des = resultSingle.split("`")[3];
					}
					
					CmCounterBusiFile addBusiFile = new CmCounterBusiFile();
					addBusiFile.setBdid(bdid);
					addBusiFile.setFtid(ftid);
					addBusiFile.setIsRequire(isRequire);
					addBusiFile.setIsTip(isTip);
					// 记录状态（1正常、0已删除）
					addBusiFile.setStat("1");
					addBusiFile.setDes(des);
					addBusiFile.setCreator(user.getUserId());
					addCounterBusiFiles.add(addBusiFile);
				}
			}
			saveCounterConfigFileRequest.setAddCounterBusiFiles(addCounterBusiFiles);

	    	BaseResponse<String> httpRsp = getPostEntityByJsonObject(CrmTradeServerPathConstant.SAVE_COUNTER_CONFIG_FILE, saveCounterConfigFileRequest,new ParameterizedTypeReference<BaseResponse<String>>(){});
			if (httpRsp.isSuccess()) {
				result = "success";
			}
		} else {
			result = "paramError";
		}
		return result;
	}
	
	
	
	/**
	 * 启用或禁用
	 * @param request
	 * @return Map<String, Object>
	 * @throws Exception 
	 */
	@RequestMapping(value = "/updateCounterBusinessStat", method = RequestMethod.POST)
    public @ResponseBody
    Map<String, Object> updateCounterBusinessStat(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        String id = request.getParameter("id");
        String stat = request.getParameter("stat");
        User user = getLoginUser();
        resultMap.put("errorMsg", "操作失败");

        if (StringUtils.isNotEmpty(id)) {
    		Map<String,String> postParam = new HashMap<String,String>();
        	postParam.put("id", id);
        	postParam.put("operatorNo", user.getUserId());
        	postParam.put("stat", stat);
			BaseResponse<String> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.UPDATE_CM_COUNTER_BUSINESS, postParam,new ParameterizedTypeReference<BaseResponse<String>>(){});
			if (httpRsp.isSuccess()) {
				resultMap.put("errorMsg", "操作成功");
			}
        }
        return resultMap;
    }
	
	
	/**
	 * 跳转到柜台订单主列表页面
	 * @param request
	 * @return ModelAndView
	 */
	@RequestMapping("/listCounterBusinessDetail")
	public ModelAndView listCounterBusinessDetail(HttpServletRequest request) {
		ModelAndView modelAndView = new ModelAndView();
		
		String id = request.getParameter("id");	
		String busiArea = request.getParameter("busiArea");

		// 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("id", id);
		BaseResponse<CmCounterBusinessMsgDto> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.GET_COUNTER_BUSINESS_MSG, postParam,new ParameterizedTypeReference<BaseResponse<CmCounterBusinessMsgDto>>(){});
		if (httpRsp.isSuccess()) {
			CmCounterBusinessMsgDto cmCounterBusinessMsgDto = httpRsp.getData();
			modelAndView.addObject("list", cmCounterBusinessMsgDto.getListBusiFileDto());
			modelAndView.addObject("busiProId",cmCounterBusinessMsgDto.getBusiProId());
		}
		modelAndView.addObject("id", id);
		//操作权限
		modelAndView.addObject("busiArea", busiArea);
		modelAndView.addObject("opStatus", request.getParameter("opStatus"));
		modelAndView.setViewName("/counter/listCounterBusinessDetail");
		return modelAndView;
	}
	
	/**
	 * 移动：上移下移
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/moveStep.do")
	public String moveStep(HttpServletRequest request) {
		String bdid = request.getParameter("id");
		String id = request.getParameter("busiFileId");
		String stepType = request.getParameter("type");
		User user = getLoginUser();

		// 设置默认参数
    	Map<String,String> postParam = new HashMap<String,String>();
    	postParam.put("id", id);
    	postParam.put("bdid", bdid);
    	postParam.put("stepType", stepType);
    	postParam.put("operatorNo", user.getUserId());
		BaseResponse<String> httpRsp = getPostEntityByMap(CrmTradeServerPathConstant.MOVE_COUNTER_BUSI_FILE_STEP, postParam,new ParameterizedTypeReference<BaseResponse<String>>(){});
		if (httpRsp.isSuccess()) {
			return "success";
		}
        return "error";
	}
	

	
	@ResponseBody
	@RequestMapping(value = "/updateCmCounterBusiFile", method = RequestMethod.POST)
    public Map<String, Object> updateCmCounterBusiFile(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("errorMsg", "修改失败");
        String id = request.getParameter("id");
        User user = getLoginUser();
        
        if (StringUtils.isNotEmpty(id)) {
        	// 设置默认参数
        	Map<String,String> postParam = new HashMap<String,String>();
        	postParam.put("id", id);
        	postParam.put("operatorNo", user.getUserId());
        	postParam.put("des", request.getParameter("des"));
        	postParam.put("isRequire", request.getParameter("isRequire"));
        	postParam.put("isTip", request.getParameter("isTip"));
			BaseResponse<String> httpRsp = getPostEntityByMap( CrmTradeServerPathConstant.UPDATE_CM_COUNTER_BUSI_FILE, postParam,new ParameterizedTypeReference<BaseResponse<String>>(){});
			if (httpRsp.isSuccess()) {
				resultMap.put("errorMsg", "修改成功");
			}
        }

        return resultMap;
    }
	
	
	@ResponseBody
	@RequestMapping(value = "/delCmCounterBusiFile", method = RequestMethod.POST)
    public Map<String, Object> delCmCounterBusiFile(HttpServletRequest request) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        resultMap.put("errorMsg", "删除删除");
        String id = request.getParameter("id");
        User user = getLoginUser();
        
        if (StringUtils.isNotEmpty(id)) {
        	// 设置默认参数
        	Map<String,String> postParam = new HashMap<String,String>();
        	postParam.put("id", id);
        	postParam.put("operatorNo", user.getUserId());
        	postParam.put("stat", "0");//删除
			BaseResponse<String> httpRsp = getPostEntityByMap( CrmTradeServerPathConstant.UPDATE_CM_COUNTER_BUSI_FILE, postParam,new ParameterizedTypeReference<BaseResponse<String>>(){});
			if (httpRsp.isSuccess()) {
				resultMap.put("errorMsg", "删除成功");
			}
        }

        return resultMap;
    }
	

	/**
	 * 通过业务类型和文件关系查询模板列表
	 * @param busiFileId
	 * @return
	 */
	private List<CmCounterFilemodel> listCmCounterFilemodelByBusifileid(String busiFileId){
		List<CmCounterFilemodel> listFile = null;
		if(StringUtils.isNoneBlank(busiFileId)){
			// 设置默认参数
        	Map<String,String> postParam = new HashMap<String,String>();
        	postParam.put("busiFileId", busiFileId);
			BaseResponse<List<CmCounterFilemodel>> httpRsp = getPostEntityByMap( CrmTradeServerPathConstant.LIST_CM_COUNTER_FILE_MODEL_BY_BUSI_FILE_ID, postParam,new ParameterizedTypeReference<BaseResponse<List<CmCounterFilemodel>>>(){});
			if (httpRsp.isSuccess()) {
				listFile = httpRsp.getData();
			}
		}
		
		return listFile;
	}
	
	/**
	 * 验证下载的文件是否存在
	 * @param request
	 * @return String
	 */
	@RequestMapping(value = "/validateBusiDownload", method = RequestMethod.POST)
	@ResponseBody
    public Map<String, Object> validateBusiDownload(HttpServletRequest request) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultMap.put("status", "false");
		List<CmCounterFilemodel> listFile = listCmCounterFilemodelByBusifileid(request.getParameter("busiFileId"));
		if(CollectionUtils.isNotEmpty(listFile)){
			resultMap.put("status", "true");
		}
        return resultMap;
    }
	
	
	/**
	 * 模板打包下载
	 * @param request
	 * @return String
	 */
	@ResponseBody
	@RequestMapping("/counterBusiDownload.do")
	public void counterBusiDownload(HttpServletRequest request, HttpServletResponse response) throws Exception {
		String id= request.getParameter("id");
		String busiFileId= request.getParameter("busiFileId");
		if (StringUtils.isNotBlank(id) && (StringUtils.isNotBlank(busiFileId))) {
			List<CmCounterFilemodel> listFile = listCmCounterFilemodelByBusifileid(busiFileId);
			
			log.info("模板附件开始打包下载！");
			if (CollectionUtils.isNotEmpty(listFile)) {
				response.setContentType("application/force-download");
				response.setHeader("Content-disposition", "attachment;filename=" + new String("资料附件.zip".getBytes("gb2312"), "ISO8859-1"));
				OutputStream fos = response.getOutputStream();
				final ZipOutputStream zipOutputStream = new ZipOutputStream(fos);
				final CountDownLatch countDownLatch = new CountDownLatch(listFile.size());
				// 打印订单关联到的附件个数
				log.info("已找到附件文件个数:{} ", listFile.size() );
				// 打包下载附件
				for (int j = 0; j < listFile.size(); j++) {
					CmCounterFilemodel filemodel = listFile.get(j);
					final String fileName = filemodel.getModelName();
					final String filemodelid = filemodel.getId();
					taskExecutor.execute(new Runnable() {
						@Override
						public void run() {
							ByteArrayInputStream bis = null;
							try {
								Map<String, String> param = new HashMap<String, String>();
								param.put("id", filemodelid);
								CounterFilemodelStream fileStreamInfo;
								BaseResponse<CounterFilemodelStream> httpRsp =
										getPostEntityByMap(CrmTradeServerPathConstant.GET_CM_COUNTER_MODEL_FILE_STREAM,
												param,
												new ParameterizedTypeReference<BaseResponse<CounterFilemodelStream>>() {
												});
								if (httpRsp.isSuccess()) {
									fileStreamInfo = httpRsp.getData();
								}else{
									fileStreamInfo=new CounterFilemodelStream();
								}
								byte[] bytes= fileStreamInfo.getFilebyte();
								bis = new ByteArrayInputStream(bytes);
								byte[] buf = new byte[8096];
								int size = 0;
								synchronized (zipOutputStream) {
									ZipEntry ze = new ZipEntry(fileName);
									zipOutputStream.putNextEntry(ze);
									while ((size = bis.read(buf)) != -1) {
										zipOutputStream.write(buf, 0, size);
										zipOutputStream.flush();
									}
								}
							} catch (Exception e) {
								log.error("模板附件下载异常:{}" + e.getMessage(),e);
							} finally {
								if (bis != null) {
									try {
										bis.close();
									} catch (IOException e) {
										log.error("bis数据流出现异常:{}" + e.getMessage(),e);
									}
								}
							}
							countDownLatch.countDown();
						}
					});
				}

				countDownLatch.await();
				if (zipOutputStream != null) {
					zipOutputStream.close();
				}
				log.info("模板附件下载成功！");
			}
		}
	}

}
