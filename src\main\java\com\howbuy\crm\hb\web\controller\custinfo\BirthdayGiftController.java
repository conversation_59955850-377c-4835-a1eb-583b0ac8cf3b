package com.howbuy.crm.hb.web.controller.custinfo;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.auth.facade.decrypt.DecryptSingleFacade;
import com.howbuy.auth.facade.response.CodecSingleResponse;
import com.howbuy.common.page.Page;
import com.howbuy.common.utils.DateUtil;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.conscust.service.ConscustAcctInfoService;
import com.howbuy.crm.hb.constants.HbConstant;
import com.howbuy.crm.hb.constants.MenuCodeConstants;
import com.howbuy.crm.hb.domain.birthdaygift.SmGiftGisVO;
import com.howbuy.crm.hb.domain.birthdaygift.SmGiftInfoVO;
import com.howbuy.crm.hb.domain.birthdaygift.SmGiftModifyRecordVO;
import com.howbuy.crm.hb.domain.conscust.CustConsAndMgrCodeDTO;
import com.howbuy.crm.hb.domain.custinfo.CmBirthdayGiftInfo;
import com.howbuy.crm.hb.domain.qywechat.CmWechatCustRelationInfo;
import com.howbuy.crm.hb.domain.qywechat.HboneNoAndConsCodeVo;
import com.howbuy.crm.hb.domain.system.UserSdDTO;
import com.howbuy.crm.hb.enums.GiftOperationSourceEnum;
import com.howbuy.crm.hb.outersevice.CmWechatOuterService;
import com.howbuy.crm.hb.request.birthdaygift.ModifyGiftRequest;
import com.howbuy.crm.hb.service.conscust.BirthdayGiftService;
import com.howbuy.crm.hb.service.custinfo.CustconstantService;
import com.howbuy.crm.hb.web.task.*;
import com.howbuy.crm.hb.web.util.Util;
import com.howbuy.crm.page.cache.ConsOrgCache;
import com.howbuy.crm.page.framework.domain.User;
import com.howbuy.crm.page.framework.excel.write.ExcelWriter;
import com.howbuy.crm.util.UserAuthUtil;
import com.howbuy.crm.util.UserSdUtil;
import com.howbuy.member.dto.xlw.SmGiftInfoDTO;
import com.howbuy.member.dto.xlw.SmGiftInfoUpdateDTO;
import com.howbuy.member.service.xlw.MemberUserBirthdayService;
import com.howbuy.persistence.web.SVIPBirthdayGiftInfo;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.web.dto.xlw.*;
import com.howbuy.web.dto.xlw.ems.EmsBaseResponseDTO;
import com.howbuy.web.dto.xlw.ems.EmsWbGisDetailDTO;
import com.howbuy.web.dto.xlw.ems.EmsWbGisInfoDTO;
import com.howbuy.web.service.business.SVIPBirthdayGiftMessageService;
import com.howbuy.web.service.xlw.SmUserBirthdayEmsDataService;
import com.howbuy.web.service.xlw.SmUserBirthdayInfoLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping("/conscust")
public class BirthdayGiftController {
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    @Autowired
    private SVIPBirthdayGiftMessageService sbgmService;

    @Autowired
    private SmUserBirthdayInfoLogService smUserBirthdayInfoLogService;

    @Autowired
    private MemberUserBirthdayService memberUserBirthdayService;

    @Autowired
    private SmUserBirthdayEmsDataService smUserBirthdayEmsDataService;

    @Autowired
    private CustconstantService custconstantService;

    @Autowired
    private DecryptSingleFacade decryptSingleFacade;

    @Autowired
    private ConscustAcctInfoService conscustAcctInfoService;

    @Autowired
    private BirthdayGiftService birthdayGiftService;

    @Autowired
    private CmWechatOuterService cmWechatOuterService;

    /**
     * 修改礼品 操作权限code
     */
    private static final String MODIFY_GIFT_OP_CODE = "2";

    /**
     * 查看修改礼品记录 操作权限code
     */
    private static final String VIEW_MODIFY_GIFT_OP_CODE = "3";

    /**
     * 查看物流信息 操作权限code
     */
    private static final String VIEW_LOGISTICS_OP_CODE = "4";

    /**
     * 分页查询生日礼品记录
     */
    private static final int PAGE_SIZE = 500;

    /**
     * @api {GET} /conscust/listBirthdayGift index()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftController
     * @apiName index()
     * @apiDescription 生日礼品记录页面
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":true,"status":"PARTIAL_CONTENT"}
     */
    @RequestMapping("/listBirthdayGift")
    public ModelAndView index(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("/custinfo/listBirthdayGift");

        // 给操作按钮赋权
        authOperationButtons(request, modelAndView);
        return modelAndView;
    }


    /**
     * @api {GET} /conscust/logisticsView logisticsView()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftController
     * @apiName logisticsView()
     * @apiDescription 查询物流信息
     * @apiParam (请求参数) {String} id
     * @apiParamExample 请求参数示例
     * id=1xsF9
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":true,"status":"CONTINUE"}
     */
    @GetMapping("/logisticsView")
    public ModelAndView logisticsView(@RequestParam("waybillNo") String waybillNo) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject("waybillNo", waybillNo);
        modelAndView.setViewName("/birthdayGift/birthdayGiftLogistics");
        return modelAndView;
    }

    /**
     * @api {POST} /conscust/queryLogisticsList queryLogisticsList()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftController
     * @apiName queryLogisticsList()
     * @apiDescription 查询生日礼品的物流情况
     * @apiParam (请求参数) {String} id
     * @apiParamExample 请求参数示例
     * id=8uAYg3
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * {}
     */
    @PostMapping("/queryLogisticsList")
    @ResponseBody
    public Map<String, Object> queryLogisticsList(@RequestParam("waybillNo") String waybillNo) {
        List<SmGiftGisVO> smGiftGisVOS = getSmGiftGisVOS(waybillNo);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("rows", smGiftGisVOS);
        return resultMap;
    }

    /**
     * @param waybillNo 生日礼物记录id
     * @return java.util.List<com.howbuy.crm.hb.domain.birthdaygift.SmGiftGisVO>
     * @description: 查询物流信息
     * @author: jin.wang03
     * @date: 2024/9/23 15:55
     * @since JDK 1.8
     */
    private List<SmGiftGisVO> getSmGiftGisVOS(String waybillNo) {
        List<SmGiftGisVO> smGiftGisVOS = new ArrayList<>();

        QueryUserBirthdayInfoDTO request = new QueryUserBirthdayInfoDTO();
        request.setWaybillNo(waybillNo);
        log.info("queryLogisticsList request: " + JSON.toJSONString(request));
        EmsBaseResponseDTO<EmsWbGisInfoDTO> emsBaseResponseDTO = smUserBirthdayEmsDataService.queryEmsWaybillTrackInfoList(request);
        log.info("queryLogisticsList emsBaseResponseDTO: " + JSON.toJSONString(emsBaseResponseDTO));

        if (Objects.nonNull(emsBaseResponseDTO)
                && Objects.nonNull(emsBaseResponseDTO.getRetBody())
                && CollectionUtils.isNotEmpty(emsBaseResponseDTO.getRetBody().getWbGisDtoList())) {
            for (EmsWbGisDetailDTO emsWbGisDetailDTO : emsBaseResponseDTO.getRetBody().getWbGisDtoList()) {
                SmGiftGisVO smGiftGisVO = new SmGiftGisVO();
                smGiftGisVO.setStatus(emsWbGisDetailDTO.getStatus());
                smGiftGisVO.setStatusName(emsWbGisDetailDTO.getStatusName());
                smGiftGisVO.setStatusDesc(emsWbGisDetailDTO.getStatusDesc());
                smGiftGisVO.setTimeDesc(emsWbGisDetailDTO.getOperateTimeStr());
                smGiftGisVOS.add(smGiftGisVO);
            }
        }

        return smGiftGisVOS;
    }

    /**
     * @api {GET} /conscust/modifyGiftView modifyGiftView()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftController
     * @apiName modifyGiftView()
     * @apiDescription 修改礼品页面
     * @apiParam (请求参数) {String} id
     * @apiParamExample 请求参数示例
     * id=KDsqOUgK
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":true,"status":"ACCEPTED"}
     */
    @GetMapping("/modifyGiftView")
    public ModelAndView modifyGiftView(@RequestParam("id") String id) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject("id", id);
        // 礼品列表
        List<SmGiftInfoVO> smGiftInfoVOS = getSmGiftInfoVOS();
        modelAndView.addObject("giftList", smGiftInfoVOS);

        modelAndView.setViewName("/birthdayGift/modifyGift");
        return modelAndView;
    }

    /**
     * @param
     * @return java.util.List<com.howbuy.crm.hb.domain.birthdaygift.SmGiftInfoVO>
     * @description: 查询礼品列表
     * @author: jin.wang03
     * @date: 2024/9/23 15:17
     * @since JDK 1.8
     */
    private List<SmGiftInfoVO> getSmGiftInfoVOS() {
        // 查询礼品列表
        List<SmGiftInfoDTO> memberUserBirthday = memberUserBirthdayService.getMemberUserBirthday();
        List<SmGiftInfoVO> smGiftInfoVOS = new ArrayList<>(memberUserBirthday.size());
        for (SmGiftInfoDTO smGiftInfoDTO : memberUserBirthday) {
            SmGiftInfoVO smGiftInfoVO = new SmGiftInfoVO();
            BeanUtils.copyProperties(smGiftInfoDTO, smGiftInfoVO);
            smGiftInfoVOS.add(smGiftInfoVO);
        }

        return smGiftInfoVOS;
    }


    /**
     * @api {POST} /conscust/toModifyGift toModifyGift()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftController
     * @apiName toModifyGift()
     * @apiDescription 修改生日礼品
     * @apiParam (请求体) {String} giftInfoId 礼品记录id
     * @apiParam (请求体) {String} giftCode 礼物种类id
     * @apiParamExample 请求体示例
     * {"giftInfoId":"IDGZbppLTT","giftCode":"6joUCnqyJ"}
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * {}
     */
    @ResponseBody
    @PostMapping("/toModifyGift")
    public Map<String, Object> toModifyGift(HttpServletRequest request,
                                            @RequestBody ModifyGiftRequest modifyGiftRequest) {
        User user = (User) request.getSession().getAttribute("loginUser");
        // 调非交易的接口：修改礼品
        return modifyGift(modifyGiftRequest, user);
    }


    /**
     * @param modifyGiftRequest
     * @param user
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * @description: 修改礼品
     * @author: jin.wang03
     * @date: 2024/9/24 10:59
     * @since JDK 1.8
     */
    private Map<String, Object> modifyGift(ModifyGiftRequest modifyGiftRequest, User user) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("returnCode", "0000");
        resultMap.put("returnMsg", "处理成功");

        SmGiftInfoUpdateDTO smGiftInfoUpdateDTO = new SmGiftInfoUpdateDTO();
        smGiftInfoUpdateDTO.setInfoId(Long.valueOf(modifyGiftRequest.getGiftInfoId()));
        smGiftInfoUpdateDTO.setAfterGiftCode(modifyGiftRequest.getGiftCode());
        smGiftInfoUpdateDTO.setOperationId(user.getUserId());
        smGiftInfoUpdateDTO.setOperationName(user.getUserName());
        smGiftInfoUpdateDTO.setOperationSource("3");
        log.info("修改生日礼品,接口入参: " + JSON.toJSONString(smGiftInfoUpdateDTO));
        try {
            memberUserBirthdayService.updateMemberUserBirthdayInfo(smGiftInfoUpdateDTO);
        } catch (Exception e) {
            log.error("修改生日礼品，出现异常！", e);

            resultMap.put("returnCode", "9999");
            resultMap.put("returnMsg", "修改礼品失败！");
        }

        return resultMap;
    }

    /**
     * @api {GET} /conscust/showGiftModifyRecords showGiftModifyRecords()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftController
     * @apiName showGiftModifyRecords()
     * @apiDescription 展示 礼品修改记录页面
     * @apiParam (请求参数) {String} id
     * @apiParamExample 请求参数示例
     * id=LwM
     * @apiSuccess (响应结果) {Object} view
     * @apiSuccess (响应结果) {Object} model
     * @apiSuccess (响应结果) {String} status CONTINUE,SWITCHING_PROTOCOLS,PROCESSING,CHECKPOINT,OK,CREATED,ACCEPTED,NON_AUTHORITATIVE_INFORMATION,NO_CONTENT,RESET_CONTENT,PARTIAL_CONTENT,MULTI_STATUS,ALREADY_REPORTED,IM_USED,MULTIPLE_CHOICES,MOVED_PERMANENTLY,FOUND,MOVED_TEMPORARILY,SEE_OTHER,NOT_MODIFIED,USE_PROXY,TEMPORARY_REDIRECT,PERMANENT_REDIRECT,BAD_REQUEST,UNAUTHORIZED,PAYMENT_REQUIRED,FORBIDDEN,NOT_FOUND,METHOD_NOT_ALLOWED,NOT_ACCEPTABLE,PROXY_AUTHENTICATION_REQUIRED,REQUEST_TIMEOUT,CONFLICT,GONE,LENGTH_REQUIRED,PRECONDITION_FAILED,PAYLOAD_TOO_LARGE,REQUEST_ENTITY_TOO_LARGE,URI_TOO_LONG,REQUEST_URI_TOO_LONG,UNSUPPORTED_MEDIA_TYPE,REQUESTED_RANGE_NOT_SATISFIABLE,EXPECTATION_FAILED,I_AM_A_TEAPOT,INSUFFICIENT_SPACE_ON_RESOURCE,METHOD_FAILURE,DESTINATION_LOCKED,UNPROCESSABLE_ENTITY,LOCKED,FAILED_DEPENDENCY,UPGRADE_REQUIRED,PRECONDITION_REQUIRED,TOO_MANY_REQUESTS,REQUEST_HEADER_FIELDS_TOO_LARGE,UNAVAILABLE_FOR_LEGAL_REASONS,INTERNAL_SERVER_ERROR,NOT_IMPLEMENTED,BAD_GATEWAY,SERVICE_UNAVAILABLE,GATEWAY_TIMEOUT,HTTP_VERSION_NOT_SUPPORTED,VARIANT_ALSO_NEGOTIATES,INSUFFICIENT_STORAGE,LOOP_DETECTED,BANDWIDTH_LIMIT_EXCEEDED,NOT_EXTENDED,NETWORK_AUTHENTICATION_REQUIRED
     * @apiSuccess (响应结果) {Boolean} cleared
     * @apiSuccessExample 响应结果示例
     * {"view":{},"model":{},"cleared":false,"status":"UNAUTHORIZED"}
     */
    @GetMapping("/showGiftModifyRecords")
    public ModelAndView showGiftModifyRecords(@RequestParam("id") String id) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject("id", id);

        List<SmGiftModifyRecordVO> smGiftInfoVOS = getGiftModifyRecords(id);
        modelAndView.addObject("giftList", smGiftInfoVOS);

        modelAndView.setViewName("/birthdayGift/giftModifyRecords");
        return modelAndView;
    }

    /**
     * @api {POST} /conscust/queryGiftModifyRecords queryGiftModifyRecords()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftController
     * @apiName queryGiftModifyRecords()
     * @apiDescription 查询 生日礼品修改记录
     * @apiParam (请求参数) {String} id
     * @apiParamExample 请求参数示例
     * id=A567E1pN7e
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * {}
     */
    @ResponseBody
    @PostMapping("/queryGiftModifyRecords")
    public Map<String, Object> queryGiftModifyRecords(@RequestParam("id") String id) {
        List<SmGiftModifyRecordVO> giftModifyRecords = getGiftModifyRecords(id);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("rows", giftModifyRecords);
        return resultMap;
    }


    /**
     * @param id
     * @return java.util.List<com.howbuy.crm.hb.domain.birthdaygift.SmGiftModifyRecordVO>
     * @description: 查询 礼品修改记录
     * @author: jin.wang03
     * @date: 2024/9/24 15:14
     * @since JDK 1.8
     */
    private List<SmGiftModifyRecordVO> getGiftModifyRecords(String id) {
        List<SmGiftModifyRecordVO> resultList = new ArrayList<>();

        // 查询礼品列表
        List<SmGiftInfoVO> smGiftInfoVOS = getSmGiftInfoVOS();
        Map<String, String> giftCodeAndNameMap = new HashMap<>();
        for (SmGiftInfoVO smGiftInfoVO : smGiftInfoVOS) {
            giftCodeAndNameMap.put(smGiftInfoVO.getGiftCode(), smGiftInfoVO.getName());
        }

        List<UserBirthdayInfoLogDTO> userBirthdayInfoLogDTOS = null;
        try {
            Long infoId = Long.valueOf(id);
            userBirthdayInfoLogDTOS = smUserBirthdayInfoLogService.queryUserBirthdayInfoLogList(infoId);
            log.info("根据生日礼品记录id: {}, 查询生日礼品修改记录结果: {}", id, JSON.toJSONString(userBirthdayInfoLogDTOS));
        } catch (Exception e) {
            log.error("根据生日礼品记录id: {}, 查询生日礼品修改记录出现异常！", id, e);
        }
        if (CollectionUtils.isNotEmpty(userBirthdayInfoLogDTOS)) {
            // userBirthdayInfoLogDTOS 根据createTime排序，日期近的放在前面
            userBirthdayInfoLogDTOS.sort(Comparator.comparing(UserBirthdayInfoLogDTO::getCreateTime).reversed());
            for (UserBirthdayInfoLogDTO userBirthdayInfoLogDTO : userBirthdayInfoLogDTOS) {
                SmGiftModifyRecordVO smGiftModifyRecordVO = new SmGiftModifyRecordVO();
                BeanUtils.copyProperties(userBirthdayInfoLogDTO, smGiftModifyRecordVO);

                // 翻译礼品名称
                smGiftModifyRecordVO.setAfterGiftName(giftCodeAndNameMap.get(userBirthdayInfoLogDTO.getAfterGiftCode()));
                // 翻译操作时间
                String createTimeDesc = DateUtil.date2String(smGiftModifyRecordVO.getCreateTime(), DateUtil.DEFAULT_DATESFM);
                smGiftModifyRecordVO.setCreateTimeDesc(createTimeDesc);
                // 翻译操作类型
                String operationSourceDesc = GiftOperationSourceEnum.getCrmDescription(smGiftModifyRecordVO.getOperationSource());
                smGiftModifyRecordVO.setOperationSourceDesc(operationSourceDesc);

                resultList.add(smGiftModifyRecordVO);
            }
        }

        return resultList;
    }

    /**
     * @api {POST} /conscust/decryptGiftMobile decryptGiftMobile()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftController
     * @apiName decryptGiftMobile()
     * @apiDescription 解密 生日礼物的手机号
     * @apiParam (请求参数) {String} phoneEncrypt
     * @apiParamExample 请求参数示例
     * phoneEncrypt=JVCV
     * @apiSuccess (响应结果) {String} response
     * @apiSuccessExample 响应结果示例
     * "e"
     */
    @ResponseBody
    @PostMapping("/decryptGiftMobile")
    public String decryptGiftMobile(@RequestParam String phoneEncrypt) {
        // 解密出明文
        if (StringUtils.isNotBlank(phoneEncrypt)) {
            CodecSingleResponse codecSingleResponse = decryptSingleFacade.decrypt(phoneEncrypt);
            if (codecSingleResponse != null && codecSingleResponse.getCodecText() != null) {
                return codecSingleResponse.getCodecText();
            }
        }
        return "";
    }


    /**
     * @api {GET} /conscust/queryBirthdayGiftByDate queryBirthdayGiftByDate()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftController
     * @apiName queryBirthdayGiftByDate()
     * @apiDescription 查询客户生日礼物方法（按推送日期区间去查）
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * {}
     */
    @ResponseBody
    @RequestMapping("/queryBirthdayGiftByDate")
    public Map<String, Object> queryBirthdayGiftByDate(HttpServletRequest request) {
        List<CmBirthdayGiftInfo> resultList = getCmBirthdayGiftInfos(request);

        // 增加对生日礼物总人数、挑选礼物人数及未挑选生日礼物人数统计
        int totalNum, choicedNum, unChoiceNum;
        totalNum = resultList.size();
        List<CmBirthdayGiftInfo> unChoiceNumList = resultList.stream().filter(obj -> StringUtils.isBlank(obj.getChoiceTime())).collect(Collectors.toList());
        unChoiceNum = unChoiceNumList.size();
        choicedNum = totalNum - unChoiceNum;

        // 返回结果数据List
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("totalNum", totalNum);
        resultMap.put("choicedNum", choicedNum);
        resultMap.put("unChoiceNum", unChoiceNum);
        resultMap.put("rows", resultList);


        return resultMap;
    }

    /**
     * @param request
     * @return java.util.List<com.howbuy.crm.hb.domain.custinfo.CmBirthdayGiftInfo>
     * @description: 根据页面筛选条件，查询数据，并对部分字段进行翻译
     * @author: jin.wang03
     * @date: 2024/9/26 14:51
     * @since JDK 1.8
     */
    private List<CmBirthdayGiftInfo> getCmBirthdayGiftInfos(HttpServletRequest request) {
        // 设置查询参数
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");
        String choiceGift = request.getParameter("choiceGift");
        String bindWechat = request.getParameter("bindWechat");

        // 调用外部接口查询数据
        List<CmBirthdayGiftInfo> sbgiList = queryBirthdayInfoByDate(request);

        // 对接口数据进行条件过滤
        List<CmBirthdayGiftInfo> buildList = birthdayGiftService.processBirthdayGiftInfoByDate(sbgiList, orgCode, consCode, choiceGift);

        // 拼装微信绑定相关数据
        return getBindWechatList(buildList, bindWechat);
    }

    /**
     * @param request
     * @param modelAndView
     * @return void
     * @description: 给操作按钮赋权
     * @author: jin.wang03
     * @date: 2024/9/25 14:44
     * @since JDK 1.8
     */
    private static void authOperationButtons(HttpServletRequest request, ModelAndView modelAndView) {
        // 按钮【修改礼物】的操作权限
        Boolean modifyGiftAuth = UserAuthUtil.hasOperAuth(request, MenuCodeConstants.BIRTHDAY_GIFT_MENU_CODE, MODIFY_GIFT_OP_CODE);
        modelAndView.addObject("modifyGiftAuth", modifyGiftAuth);
        // 按钮【查看修改】的操作权限
        Boolean viewModifyGiftAuth = UserAuthUtil.hasOperAuth(request, MenuCodeConstants.BIRTHDAY_GIFT_MENU_CODE, VIEW_MODIFY_GIFT_OP_CODE);
        modelAndView.addObject("viewModifyGiftAuth", viewModifyGiftAuth);
        // 按钮【查看物流】的操作权限
        Boolean viewLogisticsAuth = UserAuthUtil.hasOperAuth(request, MenuCodeConstants.BIRTHDAY_GIFT_MENU_CODE, VIEW_LOGISTICS_OP_CODE);
        modelAndView.addObject("viewLogisticsAuth", viewLogisticsAuth);
    }

    /**
     * 调用外部接口查询数据（按推送日期区间去查）
     *
     * @return
     */
    public List<CmBirthdayGiftInfo> queryBirthdayInfoByDate(HttpServletRequest request) {
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String birthMonth = request.getParameter("birthMonth");
        String custName = request.getParameter("custName");
        String consCustNo = request.getParameter("consCustNo");
        // 发货状态
        String shippingStatus = request.getParameter("shippingStatus");
        // 快递单号
        String waybillNo = request.getParameter("waybillNo");
        // 礼品种类
        String giftTypeName = request.getParameter("giftTypeName");
        // 一账通号
        String hbOneNo = request.getParameter("hbOneNo");
        // 寄送方式
        String deliveryMethod = request.getParameter("deliveryMethod");
        // 快递公司
        String logisticsCompany = request.getParameter("logisticsCompany");
        // 选择礼物时间start
        String chooseStartTime = request.getParameter("chooseStartTime");
        // 选择礼物时间end
        String chooseEndTime = request.getParameter("chooseEndTime");

        SVIPBirthdayGiftInfoRequest giftInfoRequest = new SVIPBirthdayGiftInfoRequest();
        giftInfoRequest.setPushStartTime(startTime);
        giftInfoRequest.setPushEndTime(endTime);

        if (StringUtils.isNotBlank(birthMonth)) {
            giftInfoRequest.setBirthday(birthMonth);
        }
        if (StringUtils.isNotBlank(custName)) {
            giftInfoRequest.setCustName(custName);
        }
        if (StringUtils.isNotBlank(consCustNo)) {
            giftInfoRequest.setConsCustNo(consCustNo);
        }
        if (StringUtils.isNotBlank(shippingStatus)) {
            giftInfoRequest.setShippingStatus(shippingStatus);
        }
        if (StringUtils.isNotBlank(waybillNo)) {
            giftInfoRequest.setWaybillNo(waybillNo);
        }
        if (StringUtils.isNotBlank(giftTypeName)) {
            giftInfoRequest.setGiftType(giftTypeName);
        }

        // 一账通号
        if (StringUtils.isNotBlank(hbOneNo)) {
            giftInfoRequest.setHboneno(hbOneNo);
        }

        // 寄送方式(1:公司寄送,2:人工寄送)
        if (StringUtils.isNotBlank(deliveryMethod)) {
            giftInfoRequest.setDeliveryMethod(deliveryMethod);
        }

        // 快递公司
        if (StringUtils.isNotBlank(logisticsCompany)) {
            giftInfoRequest.setLogisticsCompany(logisticsCompany);
        }

        // 选择礼物时间
        if (StringUtils.isNotBlank(chooseStartTime)) {
            giftInfoRequest.setChooseStartTime(chooseStartTime);
        }
        if (StringUtils.isNotBlank(chooseEndTime)) {
            giftInfoRequest.setChooseEndTime(chooseEndTime);
        }


        Page page = new Page();
        // 页码
        page.setPage(1);
        // 每页显示记录数
        page.setPerPage(PAGE_SIZE);

        // 打印请求参数和返回数据
        log.info("sbgmService.queryBirthdayInfoDTOPage(dubboREQ)，" +
                "giftInfoRequest：{}, page：{}, perPage：{}", JSON.toJSON(giftInfoRequest), page.getPage(), page.getPerPage());
        SVIPBirthdayGiftInfoDTOPage giftInfoDTOPage = sbgmService.queryBirthdayInfoDTOPage(giftInfoRequest, page);
        log.info("sbgmService.queryBirthdayInfoDTOPage，dubboRSP：{}", JSON.toJSON(giftInfoDTOPage));
        if (Objects.isNull(giftInfoDTOPage)) {
            return new ArrayList<>();
        }

        if (Objects.nonNull(giftInfoDTOPage.getPage()) && giftInfoDTOPage.getPage().getTotal() > PAGE_SIZE) {
            ReentrantLockListDto<SVIPBirthdayGiftInfoDTO> reentrantLockListDto = new ReentrantLockListDto<>();
            List<QueryBirthdayInfoTask> queryBirthdayInfoTaskList = new ArrayList<>();
            // 分页查询
            int totalPage = giftInfoDTOPage.getPage().getTotal() / PAGE_SIZE;
            if (giftInfoDTOPage.getPage().getTotal() % PAGE_SIZE != 0) {
                totalPage++;
            }
            for (int i = 2; i <= totalPage; i++) {
                queryBirthdayInfoTaskList.add(new QueryBirthdayInfoTask(i, PAGE_SIZE, sbgmService, reentrantLockListDto, giftInfoRequest));
            }
            howBuyRunTaskUil.runTask(queryBirthdayInfoTaskList);
            giftInfoDTOPage.getResultList().addAll(reentrantLockListDto.getList());
        }
        // 获取当前登录用户的数据深度
        UserSdDTO userSdDTO = UserSdUtil.getUserSdDTO(request);
        // 对生日礼品记录，进行数据转换
        return getCmBirthdayGiftInfos(giftInfoDTOPage.getResultList(), userSdDTO);
    }

    /**
     * @param userSdDTO
     * @param sbgiList
     * @return java.util.List<com.howbuy.crm.hb.domain.custinfo.CmBirthdayGiftInfo>
     * @description:(请在此添加描述)
     * @author: jin.wang03
     * @date: 2024/9/25 17:15
     * @since JDK 1.8
     */
    private List<CmBirthdayGiftInfo> getCmBirthdayGiftInfos(List<SVIPBirthdayGiftInfoDTO> sbgiList, UserSdDTO userSdDTO) {
        if (CollectionUtils.isEmpty(sbgiList)) {
            return new ArrayList<>();
        }
        ConsOrgCache consOrgCache = ConsOrgCache.getInstance();
        Map<String, String> user2OutletMap = consOrgCache.getUser2OutletMap();
        Map<String, String> allUserMap = consOrgCache.getAllUserMap();
        Map<String, String> allOrgMap = consOrgCache.getAllOrgMap();
        Map<String, String> upOrgMapCache = consOrgCache.getUpOrgMapCache();
        Map<String, String> upCenterMapCache = consOrgCache.getUpCenterMapCache();

        Map<String, CustConsAndMgrCodeDTO> consAndMgrCodeDtoMap = getConsCodeMap(sbgiList);

        Map<String, CmWechatCustRelationInfo> custBindRelationVoMap = queryBindRelationMap(sbgiList,consAndMgrCodeDtoMap);


        // 将查询接口数据进行转换
        List<CmBirthdayGiftInfo> queryList = new ArrayList<>(sbgiList.size());
        for (SVIPBirthdayGiftInfoDTO svipBirthdayGiftInfo : sbgiList) {
            CmBirthdayGiftInfo cmBirthdayGiftInfo = new CmBirthdayGiftInfo();
            BeanUtils.copyProperties(svipBirthdayGiftInfo, cmBirthdayGiftInfo);
            // 生日礼品记录的id是Long类型的，改成String类型，防止前端因数字太多导致精度丢失
            cmBirthdayGiftInfo.setIdString(svipBirthdayGiftInfo.getId().toString());

            queryList.add(cmBirthdayGiftInfo);
            // 如果该客户未找到所属的投顾，则跳过后续处理
            CustConsAndMgrCodeDTO custConsAndMgrCodeDTO = consAndMgrCodeDtoMap.get(svipBirthdayGiftInfo.getConsCustNo());
            if (Objects.isNull(custConsAndMgrCodeDTO)) {
                continue;
            }
            String consCode = Util.ObjectToString(custConsAndMgrCodeDTO.getConsCode());
            cmBirthdayGiftInfo.setConscode(consCode);

            // 判断当前登录用户 能否浏览 该条数据的明文信息（当前登录用户的数据深度 和 该客户记录所属的投顾做比较）
            String seniorMgrCode = custConsAndMgrCodeDTO.getSeniorMgrCode();
            Boolean displayPlaintext = UserSdUtil.isDisplayPlaintext(userSdDTO, consCode, seniorMgrCode);
            cmBirthdayGiftInfo.setMobileViewFlag(displayPlaintext);
            // 如果当前登录用户的数据深度，不满足查看这个客户的详细地址信息，则将客户地址信息加密展示
            if (!userSdDTO.getCanSeeAddressRole() && !displayPlaintext) {
                cmBirthdayGiftInfo.setDeliveryAddr(HbConstant.ADDR_MASK);
            }

            // 列表所属投顾、所属中心、所属区域、所属部门通过投顾客户号取CRM自己的数据
            // 获取投顾所属组织架构编码
            String orgCode = user2OutletMap.get(consCode);

            // 获取投顾姓名
            cmBirthdayGiftInfo.setConsName(allUserMap.get(consCode));

            // 获取投顾所属部门
            cmBirthdayGiftInfo.setU1Name(allOrgMap.get(orgCode));

            // 获取投顾所属区域
            String addConsUpOrgCode = upOrgMapCache.get(orgCode);
            if ("0".equals(addConsUpOrgCode)) {
                cmBirthdayGiftInfo.setU2Name(cmBirthdayGiftInfo.getU1Name());
            } else {
                cmBirthdayGiftInfo.setU2Name(allOrgMap.get(addConsUpOrgCode));
            }

            // 获取投顾所属中心
            String addCenterName = allOrgMap.get(upCenterMapCache.get(orgCode));
            cmBirthdayGiftInfo.setU3Name(addCenterName);
            // 添加企微并绑定
            // 一账通与投顾号任意为空,直接返回未绑定
            if (StringUtils.isBlank(svipBirthdayGiftInfo.getHboneno()) || StringUtils.isBlank(consCode)) {
                cmBirthdayGiftInfo.setAddWeChatAndBind(YesOrNoEnum.NO.getCode());
                cmBirthdayGiftInfo.setAddWeChatAndBindStr("否");
            }
            String key = svipBirthdayGiftInfo.getHboneno() + HbConstant.UN_LINE_CHAR + consCode;
            CmWechatCustRelationInfo cmWechatCustRelationInfo = custBindRelationVoMap.get(key);
            if (cmWechatCustRelationInfo != null && (StringUtils.isNotBlank(cmWechatCustRelationInfo.getGnUnionId()) || StringUtils.isNotBlank(cmWechatCustRelationInfo.getHwUnionId()))) {
                cmBirthdayGiftInfo.setAddWeChatAndBind(YesOrNoEnum.YES.getCode());
                cmBirthdayGiftInfo.setAddWeChatAndBindStr("是");
            } else {
                cmBirthdayGiftInfo.setAddWeChatAndBind(YesOrNoEnum.NO.getCode());
                cmBirthdayGiftInfo.setAddWeChatAndBindStr("否");
            }

        }
        return queryList;
    }

    /**
     * 根据一账通分批查询绑定企微信息
     */
    private Map<String, CmWechatCustRelationInfo> queryBindRelationMap(List<SVIPBirthdayGiftInfoDTO> sbgiList, Map<String, CustConsAndMgrCodeDTO> consAndMgrCodeDtoMap) {
        // 并行过滤出有投顾号以及一账通的数据
        List<HboneNoAndConsCodeVo> queryList = sbgiList.parallelStream().filter(dto -> StringUtils.isNotBlank(dto.getConsCustNo())).filter(dto -> {
                    CustConsAndMgrCodeDTO custConsAndMgrCodeDTO = consAndMgrCodeDtoMap.get(dto.getConsCustNo());
                    return custConsAndMgrCodeDTO != null && StringUtils.isNotBlank(custConsAndMgrCodeDTO.getConsCode());
                }).map(dto -> { CustConsAndMgrCodeDTO custConsAndMgrCodeDTO = consAndMgrCodeDtoMap.get(dto.getConsCustNo());
            HboneNoAndConsCodeVo hboneNoAndConsCodeVo = new HboneNoAndConsCodeVo();
            hboneNoAndConsCodeVo.setHboneNo(dto.getHboneno());
            hboneNoAndConsCodeVo.setConscode(custConsAndMgrCodeDTO.getConsCode());
            return hboneNoAndConsCodeVo; }).collect(Collectors.toList());

        List<List<HboneNoAndConsCodeVo>> partitionHbOneNoList = Lists.partition(queryList, 500);
        Map<String, CmWechatCustRelationInfo> cmCustBindRelationMap = new ConcurrentHashMap<>(sbgiList.size());
        List<QueryBindRelationTask> queryBindRelationTaskList = partitionHbOneNoList.stream().map(subList -> new QueryBindRelationTask(subList, cmWechatOuterService, cmCustBindRelationMap)).collect(Collectors.toList());
        howBuyRunTaskUil.runTask(queryBindRelationTaskList);
        return cmCustBindRelationMap;
    }

    /**
     * @param sbgiList
     * @return java.util.Map<java.lang.String, com.howbuy.crm.hb.domain.conscust.Custconstant>
     * @description:(请在此添加描述)
     * @author: jin.wang03
     * @date: 2024/9/25 17:34
     * @since JDK 1.8
     */
    private Map<String, CustConsAndMgrCodeDTO> getConsCodeMap(List<SVIPBirthdayGiftInfoDTO> sbgiList) {

        // 转换成投顾客户号
        List<String> custNoList = sbgiList.stream()
                .map(SVIPBirthdayGiftInfoDTO::getConsCustNo)
                .filter(StringUtils::isNotBlank)
                // 去重
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(custNoList)) {
            return new HashMap<>();
        }
        Map<String, CustConsAndMgrCodeDTO> resultMap = new ConcurrentHashMap<>(custNoList.size());
        List<List<String>> partitionCustNoList = Lists.partition(custNoList, 100);
        List<QueryCustconstantTask> taskList = partitionCustNoList.stream().map(subList -> new QueryCustconstantTask(resultMap, subList, custconstantService)).collect(Collectors.toList());
        howBuyRunTaskUil.runTask(taskList);
        return resultMap;
    }


    /**
     * @api {GET} /conscust/exportData.do exportUser()
     * @apiVersion 1.0.0
     * @apiGroup BirthdayGiftController
     * @apiName exportUser()
     * @apiDescription 导出数据
     * @apiSuccess (响应结果) {Object} response
     * @apiSuccessExample 响应结果示例
     * null
     */
    @RequestMapping(value = "/exportData.do", method = {RequestMethod.GET, RequestMethod.POST})
    public void exportUser(HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<CmBirthdayGiftInfo> resultList = getCmBirthdayGiftInfos(request);
        if (CollectionUtils.isEmpty(resultList)) {
            log.error("生日礼品记录 导出列表数据为空！");
            return;
        }

        // 清空输出流
        response.reset();

        // 设置文件格式和名字
        response.setContentType("multipart/form-data");
        response.setHeader("Content-Disposition", "attachment;fileName=" + new String("生日礼品记录.xls".getBytes("gb2312"), "ISO8859-1"));
        ServletOutputStream os = response.getOutputStream();
        String[] columnName = {"序号", "客户姓名", "投顾客户号", "一账通号", "所属投顾", "所属中心", "所属区域", "所属部门", "客户生日时间",
                "选择礼品时间", "选择礼品种类", "寄送方式", "发货状态", "快递单号", "快递公司", "高端总市值", "添加企微且绑定"};

        String[] beanProperty = {"id", "custName", "consCustNo", "hboneno", "consName", "u3Name", "u2Name", "u1Name", "birthday",
                "choiceTime", "giftType", "deliveryMethodName", "shippingStatusName", "waybillNo", "logisticsCompany", "premiumAmount", "addWeChatAndBindStr"};
        ExcelWriter.writeExcel(os, "生日礼品记录", 0, resultList, columnName, beanProperty);

        // 关闭流
        os.close();
    }


    /**
     * @param hboneNo
     * @return java.lang.String
     * @description:(获取 unionId)
     * @author: haoran.zhang
     * @date: 2025/1/3 8:59
     * @since JDK 1.8
     */
    private String getUnionId(String hboneNo) {
        if (StringUtil.isEmpty(hboneNo)) {
            return null;
        }
        return conscustAcctInfoService.getUnionIdByHboneNo(hboneNo);
    }

    /**
     * 按照是否添加企微且绑定过滤
     *
     * @param buildList
     * @param bindWechat
     * @return
     */
    public List<CmBirthdayGiftInfo> getBindWechatList(List<CmBirthdayGiftInfo> buildList, String bindWechat) {
        // 设置导出序号值
        long id = 1;
        List<CmBirthdayGiftInfo> resultList = new ArrayList<>();
        for (CmBirthdayGiftInfo giftInfo : buildList) {
            if (StringUtils.isNotBlank(bindWechat)) {
                // 按照是否添加企微且绑定进行过滤
                if (!bindWechat.equals(giftInfo.getAddWeChatAndBind())) {
                    continue;
                }
            }
            giftInfo.setId(id++);
            if (giftInfo.getPremiumAmount() != null) {
                giftInfo.setPremiumAmount(giftInfo.getPremiumAmount().setScale(2, BigDecimal.ROUND_HALF_UP));
            }

            if (StringUtils.isNotEmpty(giftInfo.getGiftType())) {
                giftInfo.setGiftType(StringEscapeUtils.unescapeHtml(giftInfo.getGiftType()));
            }
            resultList.add(giftInfo);
        }

        return resultList;
    }

    /**
     * 查询客户生日礼物方法
     *
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/queryBirthdayGift")
    public Map<String, Object> queryBirthdayGift(HttpServletRequest request) {
        // 设置查询参数
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String birthMonth = request.getParameter("birthMonth");
        String custName = request.getParameter("custName");
        String orgCode = request.getParameter("orgCode");
        String consCode = request.getParameter("consCode");

        // 调用外部接口查询数据
        List<SVIPBirthdayGiftInfo> sbgiList = queryBirthdayInfo(startTime, endTime);

        // 返回结果数据List
        Map<String, Object> resultMap = new HashMap<>();
        List<SVIPBirthdayGiftInfo> resultList = birthdayGiftService.processBirthdayGiftInfo(sbgiList, orgCode, consCode, birthMonth, custName);
        resultMap.put("rows", resultList);
        return resultMap;
    }

    /**
     * 调用外部接口查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public List<SVIPBirthdayGiftInfo> queryBirthdayInfo(String startTime, String endTime) {
        SVIPBirthdayGiftInfo sbgInfo = new SVIPBirthdayGiftInfo();

        // 打印请求参数和返回数据
        log.info("sbgmService.queryBirthdayInfoListByChoiceTime(dubboREQ)，" +
                "sbgInfo：{}, startTime：{}, endTime：{}", JSON.toJSON(sbgInfo), startTime, endTime);

        // 测试时间范围：
        // startTime = "2021-01-01 00:00:00";
        // endTime = "2021-12-31 23:59:59";
        List<SVIPBirthdayGiftInfo> sbgiList = sbgmService.queryBirthdayInfoListByChoiceTime(sbgInfo, startTime, endTime);
        log.info("sbgmService.queryBirthdayInfoListByChoiceTime，dubboRSP：{}", JSON.toJSON(sbgiList));

        return sbgiList;
    }

}
